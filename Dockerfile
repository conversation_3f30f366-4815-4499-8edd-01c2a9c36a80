# syntax=docker/dockerfile:1
FROM gradle:8.9.0-jdk21-alpine AS builder

ARG jar_name

# 构建基础插件（≈120s）
COPY --chown=gradle:gradle settings.gradle.kts gradle.properties build.gradle.kts lombok.config /home/<USER>/
COPY --chown=gradle:gradle buildSrc /home/<USER>/buildSrc/
RUN mkdir -p app/bootstrap app/api app/biz app/infra app/domain && gradle :buildSrc:jar

# 下载项目依赖（≈60s）
COPY --chown=gradle:gradle app/bootstrap/build.gradle.kts /home/<USER>/app/bootstrap/
COPY --chown=gradle:gradle app/api/build.gradle.kts /home/<USER>/app/api/
COPY --chown=gradle:gradle app/biz/build.gradle.kts /home/<USER>/app/biz/
COPY --chown=gradle:gradle app/infra/build.gradle.kts /home/<USER>/app/infra/
COPY --chown=gradle:gradle app/domain/build.gradle.kts /home/<USER>/app/domain/
RUN gradle compileJava --info

# 构建项目模块（≈70s）
COPY --chown=gradle:gradle app /home/<USER>/app/
RUN gradle build -x test

# 解压构建产物
WORKDIR /home/<USER>/app/bootstrap/build/libs
RUN mv ${jar_name}.jar app.jar && java -Djarmode=tools -jar app.jar extract

#-------------------------- Final Image --------------------------#
FROM registry.cn-hangzhou.aliyuncs.com/creatlyai/openjdk:21-jre-jammy-multimedia
LABEL maintainer="joton <<EMAIL>>"

ARG profile_active
ARG jvm_debug_arg

RUN mkdir -p /home/<USER>/logs/jvm

COPY --chown=admin:admin --from=builder /home/<USER>/app/bootstrap/build/libs/app/ /home/<USER>/

EXPOSE 8808

HEALTHCHECK --interval=5s --timeout=3s --start-period=10s --retries=4 \
  CMD curl -f http://localhost:8809/actuator/health | jq -e '.status == "UP"' || exit 1

ENV SPRING_PROFILES_ACTIVE=${profile_active} JVM_DEBUG_ARG=${jvm_debug_arg}
ENV JAVA_OPTS="\
    -Xms1536m \
    -Xmx2048m \
    -Xss512k \
    -XX:+DisableExplicitGC \
    -Xlog:async \
    -Xlog:gc*=info:file=/home/<USER>/logs/jvm/gc.log:time,uptime,pid,level,tags:filecount=5,filesize=10M \
    -Xlog:jit+compilation=info:file=/home/<USER>/logs/jvm/jit_compile.log:time,level,tags:filecount=5,filesize=10M \
    -Xlog:safepoint=info:file=/home/<USER>/logs/jvm/safepoint.log:time,level,tags:filecount=5,filesize=10M \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/home/<USER>/logs/jvm/heapdump.hprof"

CMD ["sh", "-c", "java $JAVA_OPTS $JVM_DEBUG_ARG -jar /home/<USER>/app.jar"]
