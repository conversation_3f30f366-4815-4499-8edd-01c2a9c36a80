
> Task :dependencies

------------------------------------------------------------
Root project 'buildSrc'
------------------------------------------------------------

runtimeClasspath - Runtime classpath of null/main.
+--- org.springframework.boot:spring-boot-gradle-plugin:3.2.0
|    +--- org.springframework.boot:spring-boot-buildpack-platform:3.2.0
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.2 -> 2.15.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.15.3
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.15.3
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.15.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.15.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.15.3 (c)
|    |    |    |         \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.15.3 (c)
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.15.3
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.15.3 (*)
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.15.3 (*)
|    |    +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.14.2 -> 2.15.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.15.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.3 (*)
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.15.3 (*)
|    |    +--- net.java.dev.jna:jna-platform:5.13.0
|    |    |    \--- net.java.dev.jna:jna:5.13.0
|    |    +--- org.apache.commons:commons-compress:1.23.0
|    |    +--- org.apache.httpcomponents.client5:httpclient5:5.2.1
|    |    |    +--- org.apache.httpcomponents.core5:httpcore5:5.2 -> 5.2.3
|    |    |    +--- org.apache.httpcomponents.core5:httpcore5-h2:5.2 -> 5.2.3
|    |    |    |    \--- org.apache.httpcomponents.core5:httpcore5:5.2.3
|    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.9
|    |    +--- org.springframework:spring-core:6.0.10 -> 6.1.1
|    |    |    \--- org.springframework:spring-jcl:6.1.1
|    |    \--- org.tomlj:tomlj:1.0.0
|    |         \--- org.antlr:antlr4-runtime:4.7.2
|    +--- org.springframework.boot:spring-boot-loader-tools:3.2.0
|    |    +--- org.apache.commons:commons-compress:1.23.0
|    |    \--- org.springframework:spring-core:6.0.10 -> 6.1.1 (*)
|    +--- io.spring.gradle:dependency-management-plugin:1.1.4
|    +--- org.apache.commons:commons-compress:1.23.0
|    \--- org.springframework:spring-core:6.0.10 -> 6.1.1 (*)
+--- io.spring.gradle:dependency-management-plugin -> 1.1.4
\--- nu.studer:gradle-jooq-plugin:8.2.1
     +--- org.jooq:jooq-codegen:3.18.4 -> 3.18.7
     |    +--- org.jooq:jooq:3.18.7
     |    |    \--- io.r2dbc:r2dbc-spi:1.0.0.RELEASE
     |    |         \--- org.reactivestreams:reactive-streams:1.0.3 -> 1.0.4
     |    \--- org.jooq:jooq-meta:3.18.7
     |         +--- org.jooq:jooq:3.18.7 (*)
     |         \--- jakarta.xml.bind:jakarta.xml.bind-api:3.0.0 -> 4.0.1
     |              \--- jakarta.activation:jakarta.activation-api:2.1.2
     +--- org.glassfish.jaxb:jaxb-core:3.0.2 -> 4.0.4
     |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.1 (*)
     |    +--- jakarta.activation:jakarta.activation-api:2.1.2
     |    +--- org.eclipse.angus:angus-activation:2.0.1
     |    |    \--- jakarta.activation:jakarta.activation-api:2.1.2
     |    +--- org.glassfish.jaxb:txw2:4.0.4
     |    \--- com.sun.istack:istack-commons-runtime:4.1.2
     \--- org.glassfish.jaxb:jaxb-runtime:3.0.2 -> 4.0.4
          \--- org.glassfish.jaxb:jaxb-core:4.0.4 (*)

(c) - A dependency constraint, not a dependency. The dependency affected by the constraint occurs elsewhere in the tree.
(*) - Indicates repeated occurrences of a transitive dependency subtree. Gradle expands transitive dependency subtrees only once per project; repeat occurrences only display the root of the subtree, followed by this annotation.

A web-based, searchable dependency report is available by adding the --scan option.

BUILD SUCCESSFUL in 1s
1 actionable task: 1 executed
