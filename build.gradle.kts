tasks.wrapper {
    gradleVersion = "8.9"
    distributionType = Wrapper.DistributionType.BIN
    System.setProperty("org.gradle.internal.services.base.url", properties["gradle.distributions.url"] as String)
}

defaultTasks(":app:bootstrap:bootRun")

tasks.register("printOS") {
    doLast {
        println("Operating System: ${System.getProperty("os.name")}, ${System.getProperty("os.arch")}")
    }
}
