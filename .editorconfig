# EditorConfig helps developers define and maintain consistent
# coding styles between different editors and IDEs
# editorconfig.org

root = true

[*]
# We recommend you to keep these unchanged
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true
max_line_length = 140

# Change these settings to your own preference
indent_style = space

[*.{ts,tsx,js,jsx,json,css,scss,yml,yaml,html,vue}]
indent_size = 2

[*.md]
trim_trailing_whitespace = false

[*.java]
ij_java_align_group_field_declarations = true
ij_java_doc_align_exception_comments = true
ij_java_doc_align_param_comments = true
