spring:
  datasource:
    url: *****************************************
    username: app_sky_creation
    password: ']R!8YiT*j,N.z'
  data:
    redis:
      host: **************
      port: 6379
---
logging:
  level:
    "[org.jooq.tools.LoggerListener]": debug
---
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
  pre-loading-enabled: true
---
alibaba:
  cloud:
    max-error-retry: 0 # 无重试次数，可以尽早暴露问题
    oss:
      endpoint: https://oss-cn-hangzhou.aliyuncs.com # 公网写
      read-endpoint: https://oss-cn-hangzhou.aliyuncs.com # 公网读（无CDN加速）
      bucket: creatly-dev
---
jspeeder:
  rocketmq:
    name-servers: **************:9876
# =========================================================================
# Application specific properties. Add your own application properties here
# =========================================================================
application:
#  ai-task:
#    scheduler:
#      disabled-task-types:
#        - 'AI_IMAGE'
  fei-shu:
    group:
      notice-token: '0de9260f-3f78-4543-9947-a652dea864e4'
  app-notify:
    fei-shu-token: '6756dc71-7ed8-4d1d-a602-b4206ba77372'
  talking-photo:
    endpoints:
      - hostname: ubuntu-d11
        base-url: 'http://**************:5001'
  kylin-trade:
    base-url: 'http://trade-dev:8808'
  rvc:
    base-url: 'http://**************:7866'
  ai-artistic:
    endpoints:
      - hostname: d196-0
        base-url: 'http://**************:8001'
  asr:
    endpoint: 'http://**************:15999'
  mj-proxy:
    endpoints:
      - hostname: s155-0
        account-key: zhoudong-premium
        base-url: 'http://mj-proxy:13000'
        weight: 8
  stable-diffuser:
    camera-movement:
      - hostname: 170
        endpoint: "http:/**************:13999"
    shot-effect:
      - hostname: 197
        endpoint: "http://**************:13999"
  story-jy-draft:
    endpoints:
      - hostname: 153
        base-url: 'http://**************:8000'
  video-re-talking:
    callback-base-url: 'http://**************:8808'
    endpoints:
      - hostname: d196
        base-url: 'http://**************:8808'
  digital-human:
    alpha-video:
      endpoint: 'http://**************:11999'
  video-render:
    base-url: 'http://**************:30000'
  local-file:
    enabled: true
    base-dir: '/mnt/nas/creatly'
  wechat:
    mp-endpoint: 'https://api-weixin-mp.creatly.team'
  drama:
    preprocess:
      base-url: 'http://**************:12345'
    ads-direct:
      base-url: 'http://**************:12345'
    file-sync:
      local-dir: '/mnt/nas/creatly/Application/Dataset/dev/dramas'
