spring:
  devtools:
    restart:
      enabled: false

server:
  port: 9900
management:
  server:
    port: 9901

jspeeder:
  rocketmq:
    consumer:
      enabled: false # 需要本地调试时可以打开
      enabled-listeners: UserVoicePayAsyncManagerImpl # 本地调试只能启用一个消息监听器（配置类名，不带包名）

application:
  ai-task:
    scheduler:
      enabled: true # 需要本地调试时可以打开
      enabled-task-types: AI_STORY # 本地调试只能启用一个任务类型

debug: false
