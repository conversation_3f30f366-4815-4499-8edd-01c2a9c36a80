spring:
  devtools:
    livereload:
      enabled: false
      port: "${random.int[35700,35800]}"
    restart:
      enabled: true
    remote:
      restart:
        enabled: false
  output:
    ansi:
      enabled: always
  datasource:
    url: *****************************************
    username: app_sky_creation
    password: ']R!8YiT*j,N.z'
    hikari:
      maximum-pool-size: 10
      keepalive-time: 60_000 # 1分钟，本地调试时，提高测活频率，防止数据库连接断开
  flyway:
    enabled: true
    user: app_sky_creation
    password: ']R!8YiT*j,N.z'
  data:
    redis:
      host: **************
      port: 6379
  mvc:
    request-timeout: 0s # 同步阻塞请求超时时间，禁用方便本地调试
---
server:
  shutdown: immediate
  port: 8808
---
logging:
  config: classpath:log4j2-local.xml
  level:
    "[org.springframework.boot.devtools.restart.ChangeableUrls]": warn
    "[org.jooq.tools.LoggerListener]": debug
---
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
  pre-loading-enabled: true
---
alibaba:
  cloud:
    request-timeout-enabled: true # 本地需要调试OSS时，可以在debug配置中覆盖
    max-error-retry: 0 # 无重试次数，可以尽早暴露问题
    oss:
      endpoint: https://oss-cn-hangzhou.aliyuncs.com # 公网写
      read-endpoint: https://oss-cn-hangzhou.aliyuncs.com # 公网读（无CDN加速）
      bucket: creatly-dev
---
jspeeder:
  rocketmq:
    name-servers: **************:9876
    consumer: # 在 application-debug.yml 中配置
      enabled: false # 需要本地调试时可以打开
      enabled-listeners: UserVoicePayAsyncManagerImpl # 本地调试只能启用一个消息监听器（配置类名，不带包名）
# =========================================================================
# Application specific properties. Add your own application properties here
# =========================================================================
application:
  ai-task:
    scheduler: # 在 application-debug.yml 中覆盖配置
      enabled: false # 需要本地调试时，在 application-debug.yml 中覆盖
      enabled-task-types: AI_DIG_HUMAN # 本地调试只能启用一个任务类型
  fei-shu:
    group:
      notice-token: '0de9260f-3f78-4543-9947-a652dea864e4'
  app-notify:
    fei-shu-token: '6756dc71-7ed8-4d1d-a602-b4206ba77372'
  talking-photo:
    endpoints:
      - hostname: ubuntu-d04
        base-url: 'http://**************:5001'
  kylin-trade:
    base-url: 'http://**************:8812'
  rvc:
    base-url: 'http://**************:7866'
  ai-artistic:
    endpoints:
      - hostname: d196-0
        base-url: 'http://**************:8001'
  mj-proxy:
    endpoints:
      - hostname: s155-0
        account-key: zhoudong-premium
        base-url: 'http://**************:13000'
        weight: 8
  video-synthesis:
    endpoint: 'http://**************:4000'
  ai-product-image:
    endpoints:
      - hostname: d196-0
        base-url: 'http://**************:14000'
  stable-diffuser:
    camera-movement:
      - hostname: d170
        endpoint: 'http://**************:13999'
    shot-effect:
      - hostname: d197
        endpoint: 'http://**************:13999'
  story-jy-draft:
    endpoints:
      - hostname: s153
        base-url: 'http://**************:8000'
  asr:
    endpoint: 'http://**************:15999'
  video-re-talking:
    callback-base-url: 'http://**************:9900'
    endpoints:
      - hostname: d196
        base-url: 'http://**************:8808'
  digital-human:
    alpha-video:
      endpoint: 'http://**************:11999'
  video-render:
    base-url: 'http://**************:30000'
  local-file:
    enabled: true
    base-dir: /mnt/nas/creatly
  wechat:
    mp-endpoint: 'https://api-weixin-mp.creatly.team'
  drama:
    preprocess:
      base-url: 'http://**************:12344'
    ads-direct:
      base-url: 'http://**************:12344'
    file-sync:
      local-dir: '/mnt/nas/creatly/Application/Dataset/dev/dramas'
