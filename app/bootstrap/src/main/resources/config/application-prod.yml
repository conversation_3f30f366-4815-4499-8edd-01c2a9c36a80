spring:
  datasource:
    url: **************************************************************
    username: dms_user_9e85d3f
    password: 'Creatly@2025'
  flyway:
    enabled: true # todo 临时开一下，后面集成CI工具后，这个账号就没权限了
    user: dms_user_9e85d3f
    password: 'Creatly@2025'
  data:
    redis:
      host: r-bp1epp3ngvy8f2ontv.redis.rds.aliyuncs.com
      port: 6379
---
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
  group-configs:
    - group: 2-openapi
      packages-to-scan: ai.creatly.sky.creation.api.rest.openapi
      paths-to-match: /openapi/**
      display-name: 开放平台接口
---
jspeeder:
  rocketmq:
    name-servers: ************:9876
# =========================================================================
# Application specific properties. Add your own application properties here
# =========================================================================
application:
  audio-slice:
    proxy: offline_http
  video-slice:
    proxy: offline_http
  video-shot:
    proxy: offline_http
  camera-motion:
    proxy: offline_http
  avatar-mask-video:
    proxy: offline_http
  video-render:
    proxy: offline_http
  drama:
    preprocess:
      proxy: offline_http
    ads-direct:
      proxy: offline_http
  asset:
    video-segment:
      proxy: offline_http
    describe:
      proxy: offline_http
