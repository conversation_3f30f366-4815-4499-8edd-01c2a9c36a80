spring:
  main:
    web-application-type: servlet
  application:
    name: creation
  profiles:
    default: local
    group:
      local: local,debug
  jmx:
    enabled: false
  jackson:
    default-property-inclusion: non_absent
    time-zone: "Asia/Shanghai"
    deserialization:
      fail-on-unknown-properties: false
      read-date-timestamps-as-nanoseconds: false
      accept-float-as-int: false
    serialization:
      write-date-timestamps-as-nanoseconds: false
      fail-on-empty-beans: false
  task:
    execution:
      pool:
        core-size: 8
        max-size: 8
        queue-capacity: 16
        allow-core-thread-timeout: true
      shutdown:
        await-termination: true
        await-termination-period: 20s
    scheduling:
      pool:
        size: 4
      shutdown:
        await-termination: true
        await-termination-period: 20s
  lifecycle:
    timeout-per-shutdown-phase: 30s
  data:
    web:
      pageable:
        one-indexed-parameters: false
        default-page-size: 10
    redis:
      database: 0
      repositories:
        enabled: false
    jdbc:
      repositories:
        enabled: false
  mvc:
    request-timeout: 60s # 同步IO请求超时时间，如可能超过，则使用异步IO请求
    file-upload-timeout: 60s # 文件上传请求超时时间
    async:
      request-timeout: 60s # Default of 30000 (30s) is set by the connector(org.apache.catalina.core.AsyncContextImpl#timeout)
    converters:
      preferred-json-mapper: jackson
    format:
      date: yyyy-MM-dd
      date-time: yyyy-MM-dd HH:mm:ss
      time: HH:mm:ss
  servlet:
    multipart:
      max-file-size: 3GB #上传请求的单个文件大小
      max-request-size: 3GB #上传请求中的所有文件大小
  jooq:
    sql-dialect: postgres
  datasource:
    hikari:
      validation-timeout: 2000 # 包含在连接耗时里
      keepalive-time: 580_000 # 9.7分钟，必须小于连接的最大生命周期时间
      max-lifetime: 1800_000 # 30分钟
      maximum-pool-size: 30
      pool-name: 'datasource-pool'
      schema: ${spring.application.name}
      connection-test-query: 'SELECT now()'
      data-source-properties:
        connectTimeout: 3 # 表示socket层面连接到服务端的时间
        loginTimeout: 4 # 表示建立起数据库连接的整体时间，包含了socket层面的连接耗时+应用层面的身份认证耗时
        socketTimeout: 10 # 表示数据库连接的socket层面的超时时间，包含了读写耗时
        options: '-c statement_timeout=5s -c lock_timeout=3s -c client_encoding=UTF8 -c TimeZone=Asia/Shanghai' # 慢SQL超时阻断
  flyway:
    enabled: false
    create-schemas: false
    default-schema: ${spring.application.name}
    schemas: ${spring.application.name}
    fail-on-missing-locations: true
    tablespace: pg_default
#  mail:
#    host: 'smtp.qiye.aliyun.com'
#    protocol: smtps
#    username: '<EMAIL>'
#    password: 'C8fSiGeYKx49THUI'
#    default-encoding: UTF-8
#    port: 465
#    properties:
#      "[mail.smtp.connectiontimeout]": 5000
#      "[mail.smtp.timeout]": 3000
#      "[mail.smtp.writetimeout]": 5000
#      "[mail.smtp.ssl.enabled]": true
#      "[mail.smtp.starttls.enabled]": true
  ai:
    openai:
      api-key: '***************************************************'
      base-url: 'https://api.openai.com'
      chat:
        completions-path: '/v1/chat/completions'
        options:
          model: 'gpt-4o-mini'
    deep-seek:
      api-key: 'sk-36bb82e14a2543be9010bc5388eee27f'
      chat:
        options:
          model: 'deepseek-chat'
---
server:
  port: 8808
  shutdown: graceful
  error:
    include-message: always
  tomcat:
    connection-timeout: 10s
    threads:
      max: 200
    accept-count: 100
  servlet:
    encoding:
      force-response: true
---
logging:
  level:
    root: info
  file:
    path: logs
  config: classpath:log4j2-spring.xml #明确指定日志配置文件的好处是可以感知到在初始化该配置之前是否存在使用默认配置打印日志的不当操作
---
management:
  server:
    port: 8809
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      probes:
        enabled: true
      show-details: always
---
springdoc:
  enable-data-rest: false
  enable-groovy: false
  enable-hateoas: false
  enable-kotlin: false
  api-docs:
    path: /v3/api-docs
    enabled: false
    groups:
      enabled: true
  swagger-ui:
    path: /swagger-ui
    enabled: false
    operations-sorter: alpha
    tags-sorter: alpha
  group-configs:
    - group: 1-home
      packages-to-scan: ai.creatly.sky.creation.api.rest.home
      paths-to-match: /api/**
      display-name: 主站接口
    - group: 2-openapi
      packages-to-scan: ai.creatly.sky.creation.api.rest.openapi
      paths-to-match: /openapi/**
      display-name: 开放平台接口
    - group: 3-admin
      packages-to-scan: ai.creatly.sky.creation.api.rest.admin
      paths-to-match: /api/admin/**
      display-name: 管理后台接口
    - group: 4-intranet
      packages-to-scan: ai.creatly.sky.creation.api.rest.intranet
      paths-to-match:
        - /api/webhook/**
        - /api/dev/**
      display-name: 内部调用接口
  extend:
    group-security-configs:
      - group: 1-home
        type: HTTP
        header-name: Authorization
        scheme: bearer
        bearer-format: JWT
      - group: 2-openapi
        type: APIKEY
        header-name: Authorization
      - group: 3-admin
        type: HTTP
        header-name: Authorization
        scheme: bearer
        bearer-format: JWT
    description: APIs related to AI.
    contact:
      name: creatly.ai
      email: <EMAIL>
      url: api.creatly.ai
---
alibaba:
  cloud:
    access-key: LTAI5tKWSyJXtk7vDryHwgdW
    secret-key: ******************************
    appKey: 979Nhd22ELRONMY5
    request-timeout-enabled: true
    connection-timeout: 50_000
    socket-timeout: 100_000
    request-timeout: 600_000
    slow-requests-threshold: 30_000
    max-error-retry: 3
    oss:
      endpoint: https://oss-cn-hangzhou-internal.aliyuncs.com # 内网写
      read-endpoint: https://oss-accelerate.aliyuncs.com # 公网读（CDN加速）
      bucket: creatly-online # 生产环境存储空间
      health:
        test-bucket: creatly
        read-key: 'creation/system/policy/privacy_policy.html'
        write-key: 'creation/system/policy/test.html'
---
jspeeder:
  rocketmq:
    consumer:
      enabled: true
# =========================================================================
# Application specific properties. Add your own application properties here
# =========================================================================
application:
  ai-task:
    scheduler:
      enabled: true
  chat:
    message:
      loader: redis
  proxy:
    us-http:
      host: privoxy
      port: 8118
    us-socks:
      host: ss-local
      port: 1086
    hk-http:
      host: privoxy-hk
      port: 8118
    hk-socks:
      host: ss-local-hk
      port: 1086
    offline-http:
      host: ***********
      port: 8118
  fei-shu:
    group:
      notice-token: 'f321010a-38a1-46f6-8a2e-ea44021fbc55'
  app-notify:
    fei-shu-token: 'a894f11a-d08a-4fa6-a113-b46b247ad701'
  open-ai:
    api-key: '***************************************************'
    read-timeout: 20s
  baichuan-cloud:
    endpoint: 'https://api.baichuan-ai.com/v1'
  kimi-cloud:
    endpoint: 'https://api.moonshot.cn/v1'
  azure:
    speech:
      subscription-key: 'ec2d1e93cf3e4e95a8486654c7ca6554'
      region: eastasia
  asr:
    endpoint: 'http://***********:15999'
  did:
    api-basic-token: '***************************:NdWIpZ23UXSG5IMz4SW6h'
  talking-photo:
    endpoints:
      - hostname: ubuntu-d11
        base-url: 'http://**********:6001'
  kylin-trade:
    base-url: 'http://trade-prod:8808'
  rvc:
    base-url: 'http://**********:7866'
  ai-artistic:
    endpoints:
      - hostname: d196-0
        base-url: 'http://***********:8001'
  mj-proxy:
    endpoints:
      - hostname: nj-hk-1
        account-key: clark-premium
        base-url: 'http://***********:13001'
        weight: 8
      - hostname: nj-hk-2
        account-key: zhoudong-premium
        base-url: 'http://***********:13002'
        weight: 8
  video-synthesis:
    endpoint: 'http://***********:4000'
  ai-product-image:
    endpoints:
      - hostname: d196-0
        base-url: 'http://***********:14000'
  wechat:
    sns-endpoint: 'https://api.weixin.qq.com'
    mp-endpoint: 'https://api.weixin.qq.com'
    mp-offline-forward-endpoint: 'http://**********:8888'
    apps:
      mp-zxy:
        app-id: wxcac9661182c77cd5
        app-secret: 091bf02b48e5cc87cc6cce818f628b5a
        token: 'W1FNvW7G87t2P5VqT6r6ddwxDXknJo'
        encoding-aes-key: '45qNrsp4m1Oqx0yPvCur3mx1E6cpm4iHatAafETGc4t'
      web-creatly-ai:
        app-id: wx2ec05733d711a876
        app-secret: 0cc57c491a0438f4ac204fc7711a8cf3
    notify:
      app-name: mp_zxy
  stable-diffuser:
    camera-movement:
      - hostname: 170
        endpoint: 'http://***********:13999'
    shot-effect:
      - hostname: 197
        endpoint: 'http://***********:13999'
  story-jy-draft:
    endpoints:
      - hostname: 153
        base-url: 'http://**********:8000'
  digital-human:
    yuanzhong:
      app-id: '29ec9078c90177166bac7f2ac2bdaa11'
      app-secret: 'lIDcLqx4gH9zBCrPkUypqxAcErZEq6xKoZJxJesNAtLX6GIxZdT3u19n7bOUGiEZ'
      endpoint: 'https://api2.tideo.cn'
    alpha-video:
      endpoint: 'http://**********:11999'
  more-api:
    endpoint: 'http://api.moreapi.cn'
    token: 'ALvNax4eEjoMxE9EZb0ZbZPBMRLpmFgXfdcZadEbN1tX3b38XMEAzvoPdGAlqSbn'
  video-re-talking:
    callback-base-url: 'http://*************:8811'
    endpoints:
      - hostname: d196
        base-url: 'http://***********:8808'
  audio-slice:
    base-url: 'http://**************:13879'
  video-slice:
    base-url: 'http://**************:13879'
  video-shot:
    base-url: 'http://**************:13933'
  camera-motion:
    base-url: 'http://**************:12976'
  avatar-mask-video:
    base-url: 'http://**************:12971'
  video-render:
    base-url: 'http://**************:30000'
  local-file:
    enabled: false
  drama:
    preprocess:
      base-url: 'http://**************:12345'
    ads-direct:
      base-url: 'http://**************:12345'
    file-sync:
      local-dir: '/mnt/nas/creatly/Application/Dataset/prod/dramas'
      offline-forward-endpoint: 'http://**********:8888'
  asset:
    describe:
      base-url: 'http://**************:12345'
    video-segment:
      base-url: 'http://**************:12345'
  video-project:
    drama-ads:
      rendered-wechat-message-template-id: 'Q1Z2Z3'
  ai-image:
    apis:
      image-generate:
        provider: volc_engine_sync
      image-expand:
        provider: volc_engine_sync
      image-repaint:
        provider: volc_engine_sync
    volc-engine:
      access-key: 'AKLTYjViYThkMTY1ZmQ2NDY1NmFlNDMyMjY0ZjI0ZTJhOTA'
      secret-key: 'WldRMU5qUTRaVFprWVRRek5EVXdPV0ZpTmpBNU0yTmpaRFU1T0RJek16TQ=='
      image-api:
        region: 'cn-north-1'
        endpoint: 'https://visual.volcengineapi.com'
        service: 'cv'
  liblib:
    api:
      access-key: 'msoyVlvaXBaY5xxGOyHqnQ'
      secret-key: 'DgYX1Ev7z_AtX8iWr2E1cbGBUhcHHsSY'
      url : 'https://openapi.liblibai.cloud'
  kling:
    api:
      access-key: '25d178cb959448f392869f26bbe5ab54'
      secret-key: '113ccc8058a84a1782d54448690743cd'
      url: 'https://api.klingai.com'
  volcengine-audio-clone:
    api:
      access-token: 'x36lolLsheD6yYQuTGWYotbMNLdyFfE5'
      secret-key: 'poKRxWEcefmS7i5t1m_3z2qosUU1EwtC'
      url: 'https://openspeech.bytedance.com'
      app-id: **********
  ai-music-volc:
    api:
      access-key: 'AKLTYjViYThkMTY1ZmQ2NDY1NmFlNDMyMjY0ZjI0ZTJhOTA'
      secret-key: 'WldRMU5qUTRaVFprWVRRek5EVXdPV0ZpTmpBNU0yTmpaRFU1T0RJek16TQ=='
  chanjing:
    api:
      appId: 'cae38f1b'
      secretKey: '3cd6101c3aa347418536ccf2fa1537b9'
      url: 'https://www.chanjing.cc/api'
  bailian:
    apiKey: 'sk-7a16c422fbb9405cbc39d99d97316e88'
---
message:
  phone:
    aliyun:
      endpoint: 'dysmsapi.aliyuncs.com'
      region-id: 'cn-hangzhou'
      access-key-id: 'LTAI5tDF2kf5wy4bRXbF75jp'
      access-key-secret: '******************************'
      sign-name: '杭州知行元科技'
      verify-template-code: 'SMS_487370143'
