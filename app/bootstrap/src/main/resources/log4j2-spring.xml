<?xml version="1.0" encoding="UTF-8"?>
<!--用于服务器上的日志配置-->
<Configuration status="WARN" xmlns="http://logging.apache.org/log4j/2.x/config">
    <Properties>
        <!--#region SpringBoot日志系统标准系统属性（LoggingSystemProperties）-->
        <Property name="LOG_PATH">${spring:logging.file.path:-logs}</Property>
        <Property name="LOG_EXCEPTION_CONVERSION_WORD">%xwEx</Property>
        <!--Patterns，其中MDC的取值来源：LoggingContextMDCFilter-->
        <Property name="CONSOLE_LOG_PATTERN">
            %d{yyyy-MM-dd HH:mm:ss.SSS} %5p %pid --- [%31.31t] %-40.40c{1.} : %m%n${sys:LOG_EXCEPTION_CONVERSION_WORD}
        </Property>
        <Property name="FILE_LOG_PATTERN">
            <!--TODO 增加 trace 系统-->
            %d{yyyy-MM-dd HH:mm:ss.SSS} %5p [%10t] : [%c{1}]%m%n${sys:LOG_EXCEPTION_CONVERSION_WORD}
        </Property>
        <!--#region SpringBoot日志系统标准系统属性 -->

        <!--#region 非标属性（不在系统属性里）-->
        <Property name="APP_LOG_PATH">${sys:LOG_PATH}</Property>
        <Property name="LOG_LEVEL">${spring:logging.level.root:-info}</Property>
        <!--#endregion 非标属性-->
    </Properties>

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="${sys:CONSOLE_LOG_PATTERN}" charset="UTF-8"/>
        </Console>

        <RollingRandomAccessFile name="ERROR-APPENDER" fileName="${APP_LOG_PATH}/common-error.log"
                                 filePattern="${APP_LOG_PATH}/common-error.%d{yyyy-MM-dd}.%i.log">
            <ThresholdFilter level="ERROR"/>
            <PatternLayout pattern="${sys:FILE_LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10">
                <Delete basePath="${APP_LOG_PATH}">
                    <IfFileName glob="common-error.*.*.log"/>
                    <IfLastModified age="P5D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="APP-DEFAULT-APPENDER" fileName="${APP_LOG_PATH}/app-default.log"
                                 filePattern="${APP_LOG_PATH}/app-default.%d{yyyy-MM-dd}.%i.log">
            <PatternLayout pattern="${sys:FILE_LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10">
                <Delete basePath="${APP_LOG_PATH}">
                    <IfFileName glob="app-default.*.*.log"/>
                    <IfLastModified age="P5D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>


        <!--#region 核心子域-->
        <RollingRandomAccessFile name="APP-TASK-SCHEDULER-APPENDER" fileName="${APP_LOG_PATH}/app-task-scheduler.log"
                                 filePattern="${APP_LOG_PATH}/app-task-scheduler.%d{yyyy-MM-dd}.%i.log">
            <PatternLayout pattern="${sys:FILE_LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10">
                <Delete basePath="${APP_LOG_PATH}">
                    <IfFileName glob="app-task-scheduler.*.*.log"/>
                    <IfLastModified age="P5D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="APP-TASK-APPENDER" fileName="${APP_LOG_PATH}/app-task.log"
                                 filePattern="${APP_LOG_PATH}/app-task.%d{yyyy-MM-dd}.%i.log">
            <PatternLayout pattern="${sys:FILE_LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10">
                <Delete basePath="${APP_LOG_PATH}">
                    <IfFileName glob="app-task.*.*.log"/>
                    <IfLastModified age="P5D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <!--#endregion 核心子域-->

        <!--#region 支撑子域-->
        <RollingRandomAccessFile name="APP-ALERT-APPENDER" fileName="${APP_LOG_PATH}/support/app-alert.log"
                                 filePattern="${APP_LOG_PATH}/support/app-alert.%d{yyyy-MM-dd}.%i.log">
            <PatternLayout pattern="${sys:FILE_LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10">
                <Delete basePath="${APP_LOG_PATH}/support">
                    <IfFileName glob="app-alert.*.*.log"/>
                    <IfLastModified age="P5D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <!--#endregion 支撑子域-->

        <!--#region 通用子域-->
        <RollingRandomAccessFile name="APP-MQ-APPENDER" fileName="${APP_LOG_PATH}/common/app-mq.log"
                                 filePattern="${APP_LOG_PATH}/common/app-mq.%d{yyyy-MM-dd}.%i.log">
            <PatternLayout pattern="${sys:FILE_LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10">
                <Delete basePath="${APP_LOG_PATH}/common">
                    <IfFileName glob="app-mq.*.*.log"/>
                    <IfLastModified age="P5D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="ROCKETMQ-CLIENT-APPENDER" fileName="${APP_LOG_PATH}/common/rocketmq-client.log"
                                 filePattern="${APP_LOG_PATH}/common/rocketmq-client.%d{yyyy-MM-dd}.%i.log">
            <PatternLayout pattern="${sys:FILE_LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="3">
                <Delete basePath="${APP_LOG_PATH}/common">
                    <IfFileName glob="rocketmq-client.*.*.log"/>
                    <IfLastModified age="P5D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <!--#endregion 通用子域-->

        <!--#region 基础设施-->
        <RollingRandomAccessFile name="APP-DAL-APPENDER" fileName="${APP_LOG_PATH}/app-dal.log"
                                 filePattern="${APP_LOG_PATH}/common/app-dal.%d{yyyy-MM-dd}.%i.log">
            <PatternLayout pattern="${sys:FILE_LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="3">
                <Delete basePath="${APP_LOG_PATH}">
                    <IfFileName glob="app-dal.*.*.log"/>
                    <IfLastModified age="P5D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <!--#endregion 基础设施-->
    </Appenders>

    <Loggers>
        <!--#region 核心子域-->
        <AsyncLogger name="ai.creatly.sky.creation.domain.core.aitask.engine.scheduler" level="INFO" additivity="false">
            <AppenderRef ref="APP-TASK-SCHEDULER-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="ai.creatly.sky.creation.domain.core.aitask" level="INFO" additivity="false">
            <AppenderRef ref="APP-TASK-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="ai.creatly.sky.creation.infra.dal.adaptor.aitask" level="INFO" additivity="false">
            <AppenderRef ref="APP-TASK-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>
        <!--#endregion 核心子域-->

        <!--#region 支撑子域-->
        <AsyncLogger name="ai.creatly.sky.creation.domain.support.alert" level="INFO" additivity="false">
            <AppenderRef ref="APP-ALERT-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>
        <!--#endregion 支撑子域-->

        <!--#region 通用子域-->
        <AsyncLogger name="ai.creatly.sky.creation.domain.common.messaging" level="INFO" additivity="false">
            <AppenderRef ref="APP-MQ-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="ai.creatly.sky.creation.infra.integration.feishu" level="INFO" additivity="false">
            <AppenderRef ref="APP-ALERT-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>
        <!--#endregion 通用子域-->

        <!--#region 基础设施-->
        <AsyncLogger name="ai.creatly.sky.creation.infra.messaging" level="INFO" additivity="false">
            <AppenderRef ref="APP-MQ-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="org.jooq.tools.LoggerListener" level="INFO" additivity="false">
            <AppenderRef ref="APP-DAL-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="RocketmqClient" level="INFO" additivity="false">
            <AppenderRef ref="ROCKETMQ-CLIENT-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="RocketmqBroker" level="INFO" additivity="false">
            <AppenderRef ref="ROCKETMQ-CLIENT-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="RocketmqTools" level="INFO" additivity="false">
            <AppenderRef ref="ROCKETMQ-CLIENT-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="RocketmqCommon" level="INFO" additivity="false">
            <AppenderRef ref="ROCKETMQ-CLIENT-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="RocketmqRemoting" level="INFO" additivity="false">
            <AppenderRef ref="ROCKETMQ-CLIENT-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>
        <!--#endregion 基础设施-->

        <AsyncRoot level="INFO">
            <AppenderRef ref="APP-DEFAULT-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
            <AppenderRef ref="Console"/>
        </AsyncRoot>
    </Loggers>
</Configuration>
