/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation;

import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.jul.LogManager;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 * @version Application.java, v 0.1 2023-01-02 16:15 joton
 */
@SpringBootApplication
@Slf4j
public class Application {

    public static void main(String[] args) {
        // jul -> log4j2
        System.setProperty("java.util.logging.manager", LogManager.class.getName());
        SpringApplication.run(Application.class, args);
        log.info("#####################creatlyai start success#####################");
    }

}
