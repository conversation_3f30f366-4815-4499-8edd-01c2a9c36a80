/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.voice;

import ai.creatly.sky.creation.domain.core.plan.model.UserPlan;
import ai.creatly.sky.creation.domain.core.plan.repository.UserPlanRepository;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceStatus;
import ai.creatly.sky.creation.domain.core.voice.repository.VoiceRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @version VoiceRepositoryTest.java, v 0.1 2024-06-24 下午12:05 zhoudong
 */
@SpringBootTest
public class VoiceRepositoryTest {

    @Autowired
    private VoiceRepository    voiceRepository;
    @Autowired
    private UserPlanRepository userPlanRepository;

    @Test
    public void testUpdate() {
        Voice voice = new Voice().setId(11L).setUid(111L).setStatus(VoiceStatus.UNPAID);
        voiceRepository.updateById(voice);
    }

    @Test
    public void testUpdate1() {
        UserPlan userPlan = new UserPlan().setId(111L)
                .setName("test")
                .setDescription("xxx")
                .setLevel(4)
                .setAutoRenewed(true);
        userPlanRepository.updateById(userPlan);
    }
}
