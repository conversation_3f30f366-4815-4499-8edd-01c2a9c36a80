/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.digitalhuman;

import ai.creatly.sky.creation.domain.common.integration.avatar.AvatarClient;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @version AvatarClientTest.java, 2024-10-30 下午3:04 zhoudong
 */
@SpringBootTest
public class AvatarClientTest {

    @Autowired
    public AvatarClient avatarClient;

    @Test
    public void getAddAvatarStatus() {
        String status = avatarClient.getAddAvatarStatus("598970526179397");
        System.out.println(status);
    }
}
