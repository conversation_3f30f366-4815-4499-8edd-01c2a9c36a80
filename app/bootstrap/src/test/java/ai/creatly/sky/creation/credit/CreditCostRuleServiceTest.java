/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.credit;

import ai.creatly.sky.creation.domain.core.aiimage_old.model.task.AiImageTaskBizType;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.credit.cost.CreditCostRuleService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @version CostRuleServiceTest.java, v 0.1 2023-12-04 下午23:25 heb
 */
@SpringBootTest
public class CreditCostRuleServiceTest {

    @Autowired
    private CreditCostRuleService creditCostRuleService;

    @Test
    public void testBasicSynthesizeBySystem() {
        Integer cost = creditCostRuleService.calcCredits(AiTaskType.CREATE_AI_IMAGE.name(), AiImageTaskBizType.MID_JOURNEY_IMAGE.name(), 1);
        System.out.println(cost);
    }
}
