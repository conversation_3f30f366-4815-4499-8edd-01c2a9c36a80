/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.file;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.common.integration.oss.FileContentMetadata;
import ai.creatly.sky.creation.domain.common.integration.oss.OssTemplate;
import ai.creatly.sky.creation.domain.support.file.FileContentUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @version OssTemplateTest.java, 2024-10-27 下午9:11 zhoudong
 */
@SpringBootTest
public class OssTemplateTest {

    @Autowired
    private OssTemplate ossTemplate;

    @Test
    public void testUpload() {
        File file = new File("build.gradle.kts");

        String md5;
        try (InputStream inputStream = new FileInputStream(file)) {
            md5 = FileContentUtil.md5Base64(inputStream.readAllBytes());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        System.out.println(md5);

        try (InputStream inputStream = new FileInputStream(file)) {
            String key = "test/zhoudong/build.gradle.kts";
            String url = ossTemplate.upload("creatly-dev", key, FileAcl.PRIVATE, inputStream);
            System.out.println(url);

            FileContentMetadata contentMetadata = ossTemplate.getContentMetadata("creatly-dev", key);
            System.out.println(contentMetadata);

            Assertions.assertEquals(md5, contentMetadata.getMd5());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
