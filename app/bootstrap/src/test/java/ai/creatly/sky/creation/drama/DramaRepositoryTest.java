/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.drama;

import ai.creatly.sky.creation.domain.core.drama.service.DramaRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 *
 * <AUTHOR>
 * @version DramaRepositoryTest.java, 2024-12-16 下午3:13 zhoudong
 */
@SpringBootTest
public class DramaRepositoryTest {

    @Autowired
    private DramaRepository dramaRepository;

    @Test
    public void summaryProductCategoryTrees() {
        dramaRepository.summaryProductCategoryPaths();
    }
}
