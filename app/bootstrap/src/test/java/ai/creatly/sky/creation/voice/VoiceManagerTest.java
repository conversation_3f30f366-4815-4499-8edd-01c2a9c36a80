/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.voice;

import ai.creatly.sky.creation.biz.voice.VoiceManager;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceCategory;
import ai.creatly.sky.creation.domain.core.voice.model.response.VoiceVM;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @version VoiceManagerTest.java, v 0.1 2024-03-01 上午11:44 zhoudong
 */
@SpringBootTest
public class VoiceManagerTest {

    @Autowired
    private VoiceManager voiceManager;

    @Test
    public void queryOnlineVoices() {
        List<VoiceVM> voices =  voiceManager.queryOnlineVoices(VoiceCategory.market, 10348161143943168L);
        System.out.println(voices);
    }
}
