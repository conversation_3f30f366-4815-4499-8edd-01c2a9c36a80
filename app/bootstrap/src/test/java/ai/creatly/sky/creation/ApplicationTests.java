package ai.creatly.sky.creation;

import ai.creatly.sky.creation.biz.aitalk.AiTalkVideoManager;
import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.common.integration.oss.OssTemplate;
import ai.creatly.sky.creation.domain.common.integration.talkingphoto.TalkingPhotoClient;
import ai.creatly.sky.creation.domain.common.integration.talkingphoto.model.GenerateVideoRequest;
import ai.creatly.sky.creation.domain.core.aittalk.model.request.CreateTalkVideoRequest;
import ai.creatly.sky.creation.domain.core.aittalk.model.request.CreateTalkVideoTextInput;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.voice.model.enums.EmotionDegreeEnum;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechPitchType;
import com.jspeeder.core.data.id.IdHelper;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @version CreationApplicationTests.java, v 0.1 2023-01-02 18:25 joton
 */
@SpringBootTest
public class ApplicationTests {

    @Autowired
    private OssTemplate        ossTemplate;
    @Autowired
    private ResourceLoader     resourceLoader;
    @Autowired
    private AiTalkVideoManager aiTalkVideoManager;
    @Autowired
    private TalkingPhotoClient talkingPhotoClient;
    @Autowired
    private JavaMailSender     javaMailSender;

    @Test
    void testOss() throws IOException {
        // 从第三方下载
        String url = "https://d-id-talks-prod.s3.us-west-2.amazonaws.com/google-oauth2%7C115708460927699928848/tlk_pfQ9Kdx6bOO4pV_RvVwT8/1687881387128.mp4?AWSAccessKeyId=AKIA5CUMPJBIK65W6FGA&Expires=1687967789&Signature=KrKgp8MeE85EwObAurvkhrraNTY%3D&X-Amzn-Trace-Id=Root%3D1-649b06ad-0383b4d77f1769744ea09899%3BParent%3D31f9ddee7fbd5583%3BSampled%3D1%3BLineage%3D6b931dd4%3A0";

        // 转存到oss里
        Resource resource = resourceLoader.getResource("classpath:test.txt");
        try (InputStream in = resource.getInputStream()) {
            String ossUrl = ossTemplate.upload("creatly-dev", "users/1/test/test.txt", FileAcl.PRIVATE, in);
            Assertions.assertNotNull(ossUrl);
        }
    }

    @Test
    void testAiTalkVideo() {
        CreateTalkVideoTextInput textInput = new CreateTalkVideoTextInput();
        textInput.setText("阁中帝子今何在？槛外长江空自流。");
        textInput.setVoiceName("zh-CN-XiaoshuangNeural");
        textInput.setStyle("sad");
        textInput.setStyleDegree(EmotionDegreeEnum.STRONG);
        textInput.setPitch(SpeechPitchType.high);

        CreateTalkVideoRequest request = new CreateTalkVideoRequest();
        request.setActorId("319582494535680");
        request.setTextInput(textInput);

        UserContext userContext = new UserContext();
        userContext.setSessionUid(1L);
        userContext.setSessionUserName("test");
        String taskId = aiTalkVideoManager.synthesizeTalkVideo(request, userContext);
        System.out.println(taskId);
    }

    @Test
    void testTalkingPhoto() {
        GenerateVideoRequest request = new GenerateVideoRequest();
        request.setRequestId(String.valueOf(IdHelper.getId()));
        request.setSourceImage("creation/users/1/AI_TALK/319563196542976.png");
        request.setDrivenAudio("creation/users/9746607005249536/AI_TALK/10047091518742528.wav");
        request.setUploadPath("creation/users/9746607005249536/ai-talk/");
        talkingPhotoClient.submitGeneration(request);
    }

    @Test
    void testMail() throws MessagingException {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mimeMessage);
        helper.setFrom("<EMAIL>");
        helper.setTo("<EMAIL>");
        helper.setSubject("创作任务完成通知");
        helper.setText("您的创作任务已完成，请打开网站查看：<a href=\"https://creatly.ai\">https://creatly.ai</a>", true);
        javaMailSender.send(mimeMessage);
    }
}
