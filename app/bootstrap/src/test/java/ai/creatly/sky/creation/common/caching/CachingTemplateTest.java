/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.common.caching;

import ai.creatly.sky.creation.domain.common.caching.CachingTemplate;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceQO;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceCategory;
import ai.creatly.sky.creation.domain.core.voice.repository.VoiceRepository;
import com.jspeeder.core.data.page.PageBuilder;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.json.JSON;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version CachingTemplateTest.java, v 0.1 2024-03-01 下午11:34 zhoudong
 */
@SpringBootTest
public class CachingTemplateTest {

    @Autowired
    private CachingTemplate cachingTemplate;
    @Autowired
    private VoiceRepository voiceRepository;

    @Test
    public void setNullBinary() {
        Assertions.assertThrows(SysException.class, () -> {
            // 字节数组不能为空
            cachingTemplate.set("test", (byte[]) null, Duration.ofMinutes(10));
        });
    }

    @Test
    public void cacheablePage() {
        Pageable pageable = PageBuilder.firstPage().build();
        VoiceQO qo = new VoiceQO().setCategory(VoiceCategory.market);
        cachingTemplate.cacheablePage(Voice.class)
                .key("test_voice_page_", JSON.toJSONString(qo), pageable.toIdentity())
                .timeout(Duration.ofMinutes(10))
                .pushKeyToList("test_voice_key_list")
                .get(pageable, () -> voiceRepository.queryPage(qo, pageable));
    }
}
