/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.common.integration;

import ai.creatly.sky.creation.domain.core.auth.model.WechatAppName;
import ai.creatly.sky.creation.infra.integration.wechat.config.WechatProperties;
import ai.creatly.sky.creation.infra.integration.wechat.mp.WechatMpApi;
import ai.creatly.sky.creation.infra.integration.wechat.mp.model.token.StableTokenRequest;
import ai.creatly.sky.creation.infra.integration.wechat.mp.model.token.StableTokenResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @version WechatApiTest.java, v 0.1 2024-10-15 下午3:48 zhoudong
 */
@SpringBootTest
public class WechatApiTest {

    @Autowired
    private WechatMpApi      wechatMpApi;
    @Autowired
    private WechatProperties wechatProperties;

    @Test
    public void getAccessToken() {
        WechatProperties.WechatApp wechatApp = wechatProperties.getApps().get(WechatAppName.mp_zxy);
        StableTokenRequest request = new StableTokenRequest()
                .setAppid(wechatApp.getAppId())
                .setSecret(wechatApp.getAppSecret());
        StableTokenResponse response = wechatMpApi.getAccessToken(request);
        Assertions.assertTrue(response.isSuccess());
        System.out.println(response);
    }
}
