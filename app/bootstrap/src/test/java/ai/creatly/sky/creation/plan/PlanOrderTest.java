/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.plan;

import ai.creatly.sky.creation.biz.plan.PlanManager;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.plan.model.request.PlanWxNativePayRequest;
import ai.creatly.sky.creation.domain.core.plan.model.response.PlanWxNativePayResultVM;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 付费计划购买测试
 *
 * <AUTHOR>
 * @version PlanOrderTest.java, v 0.1 2023-10-16 下午7:56 zhoudong
 */
@SpringBootTest
public class PlanOrderTest {

    @Autowired
    private PlanManager planManager;

    @Test
    public void testOrderAndPay() {
        PlanWxNativePayRequest payRequest = new PlanWxNativePayRequest();
        UserContext userContext = new UserContext().setSessionUid(2L).setSessionUserName("TEST");
        String planId = "subscription-basic-member";
        PlanWxNativePayResultVM resultVM = planManager.buyPlan(planId, payRequest, "127.0.0.1", userContext);
        System.out.println(resultVM);
        Assertions.assertNotNull(resultVM);
    }
}
