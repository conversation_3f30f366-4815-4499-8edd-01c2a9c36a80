/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.support.sound;

import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.FileMetadata;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.deprecated.sysfile.model.SysFileInput;
import ai.creatly.sky.creation.domain.deprecated.sysfile.service.SysFileHelper;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import ai.creatly.sky.creation.domain.support.sound.model.Sound;
import ai.creatly.sky.creation.domain.support.sound.model.enums.SoundSourceType;
import ai.creatly.sky.creation.domain.support.sound.repository.SoundRepository;
import ai.creatly.sky.creation.domain.support.sound.repository.SoundTagRepository;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.model.BizStatus;
import com.jspeeder.core.util.text.FormatUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Duration;
import java.util.Set;

/**
 * <AUTHOR>
 * @version SoundRepositoryTest.java, v 0.1 2024-04-17 下午2:33 zhoudong
 */
@SpringBootTest
public class SoundRepositoryTest {

    @Autowired
    private SoundRepository     soundRepository;
    @Autowired
    private SysFileHelper       sysFileHelper;
    @Autowired
    private UserFileRepository  userFileRepository;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private SoundTagRepository  soundTagRepository;

    @Test
    public void createSound() {
        String sourceNo = "20350";
        String authorName = "Schnappi";
        String name = "吹着口哨骑马声";
        Set<String> tagNames = Set.of("人类声音", "日常动作", "口哨", "影视", "自然", "交通", "电影", "日常", "人物动作", "影视特效", "游戏音效", "影视音效");
        Duration duration = Duration.ofSeconds(34);
        int bitrate = 320;
        int sampleRate = 44100;
        String audioFormat = "wav";

        FileBizSource bizSource = FileBizSource.houzi8_SOUND_LIB;
        String fileKey = FormatUtil.format("{}/{}/{}.{}", AppConstants.APP_NAME, bizSource.getDir(), sourceNo, audioFormat);
        SysFileInput fileInput = new SysFileInput()
                .setBucket(SysFileHelper.PRIVATE_BUCKET)
                .setType(FileType.AUDIO)
                .setBizSource(bizSource)
                .setExtension(audioFormat)
                .setKey(fileKey)
                .setFileMetadata(new FileMetadata()
                        .setDuration(duration)
                        .addBizData("bitrate", String.valueOf(bitrate))
                        .addBizData("sampleRate", String.valueOf(sampleRate)));
        UserFile audioFile = sysFileHelper.buildSysFile(IdHelper.getId(), fileInput);

        Sound sound = new Sound()
                .setId(IdHelper.getId())
                .setUid(AppConstants.SYSTEM_UID)
                .setStatus(BizStatus.VALID)
                .setSourceType(SoundSourceType.houzi8)
                .setSourceNo(sourceNo)
                .setAuthorName(authorName)
                .setName(name)
                .setTagNames(tagNames)
                .setAudioId(audioFile.getId())
                .setAudioUrl(UserFileHelper.toOssUrl(audioFile))
                .setAudioFormat(audioFormat)
                .setDuration(duration)
                .setBitrate(bitrate)
                .setSampleRate(sampleRate)
                .setCreator(UserContext.buildSystem().toOperatorRef())
                .setUpdater(UserContext.buildSystem().toOperatorRef());

        // 将标签列表合并到标签库里（已去重）
        soundTagRepository.mergeTags(tagNames);

        // 持久化
        transactionTemplate.executeWithoutResult(status -> {
            userFileRepository.create(audioFile);
            long id = soundRepository.create(sound);
            System.out.println(id);
        });
    }
}
