/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.digitalhuman;

import ai.creatly.sky.creation.domain.common.integration.asr.AsrSegment;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.digitalhuman.error.DigHumanErrorCode;
import ai.creatly.sky.creation.domain.core.digitalhuman.mapper.AsrSegmentVMMapper;
import ai.creatly.sky.creation.domain.core.digitalhuman.mapper.NarrativeStyleVMMapper;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.NarrativeStyle;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.enums.ShortVideoNarrativeStyle;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.request.HotVideoRewriteRequest;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.response.AsrSegmentVM;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.response.ShortVideoNarrativeStyleVM;
import ai.creatly.sky.creation.domain.core.digitalhuman.service.DigHumanHotVideoService;
import ai.creatly.sky.creation.domain.core.digitalhuman.service.LLMHotVideo;
import ai.creatly.sky.creation.domain.core.userfile.model.FileInput;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import ai.creatly.sky.creation.domain.support.multimedia.resolver.MediaFileResolver;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version DigHumanHotVideoManager.java, v 0.1 2024-06-28 14:37 syoka
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigitalHumanHotVideoManager {

    private final UserFileHelper          userFileHelper;
    private final UserFileService         userFileService;
    private final UserFileRepository      userFileRepository;
    private final DigHumanHotVideoService digHumanHotVideoService;
    private final LLMHotVideo             llmHotVideo;
    private final AsrSegmentVMMapper      asrSegmentVMMapper;
    private final NarrativeStyleVMMapper  narrativeStyleVMMapper;
    private final MediaFileResolver       mediaFileResolver;

    /**
     * 解析视频并存储音频文件
     *
     * @param userContext -
     * @param link        -
     */
    public List<AsrSegmentVM> resolveAudioFromShortVideoLink(UserContext userContext, String link) {
        // 校验视频链接是否正确
        Asserts.isTrue(checkVideoLinkIsCorrect(link), "短视频链接格式不正确");
        // 解析短视频链接
        String shortVideoUrl = digHumanHotVideoService.parseLink(link);
        // 获取视频流
        InputStream videoStream = getVideoStreamFromURL(shortVideoUrl);
        Asserts.notNull(videoStream, "短视频解析失败，请稍后重试");
        InputStream audioStream = mediaFileResolver.getAudioFromVideoStream(videoStream);
        // 音频文件上传
        final FileInput fileInput = new FileInput()
                .setType(FileType.AUDIO)
                .setBizSource(FileBizSource.DIG_HUMAN_HOT_VIDEO_AUDIO)
                .setOriginalFilename(IdHelper.get32UUID() + ".mp3");
        // 构建文件对象
        UserFile userFile = userFileHelper.buildUserFile(userContext, IdHelper.getId(), fileInput);
        // 上传物理文件
        userFileService.upload(audioStream, userFile);
        // 持久化用户文件记录
        userFileRepository.create(userFile);
        // asr语音识别
        List<AsrSegment> segments = digHumanHotVideoService.recognizeSpeechFromAudio(userFile);
        return asrSegmentVMMapper.toVM(segments);
    }


    private boolean checkVideoLinkIsCorrect(String link) {
        // TODO 链接格式信息待校验
        return true;
    }

    private static InputStream getVideoStreamFromURL(String urlString) {
        try {
            URL url = new URI(urlString).toURL();
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");
            connection.setDoInput(true);
            return connection.getInputStream();
        } catch (Exception e) {
            log.info("[getInputStreamFromURL] 获取链接流失败, url: {}", urlString, e);
        }
        return null;
    }

    public String rewriteHotVideoContent(HotVideoRewriteRequest request) {
        String method = request.getMethod();
        String originInput = request.getOriginInput();
        String userInput = request.getUserInput();
        String styleInput = request.getStyleInput();
        // 参数对比
        if (StringUtils.equals(LLMHotVideo.STYLE_REWRITE, method)) {
            NarrativeStyle style = ShortVideoNarrativeStyle.of(styleInput);
            return llmHotVideo.rewriteNarrativeStyle(originInput, style);
        } else if (StringUtils.equals(LLMHotVideo.VIEW_FUSION, method)) {
            return llmHotVideo.rewriteViewPoint(originInput, userInput);
        }
        throw new BizException(DigHumanErrorCode.DH_HOT_VIDEO_UN_RECOGNIZE_REWRITE_METHOD);
    }

    /**
     * 获取叙事风格
     *
     * @return 叙事风格集合
     */
    public List<ShortVideoNarrativeStyleVM> getNarrativeStyles() {
        return Arrays.stream(ShortVideoNarrativeStyle.values()).map(narrativeStyleVMMapper::toVM).toList();
    }
}
