/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.product;

import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.product.model.request.VideoProductCreateRequest;
import ai.creatly.sky.creation.domain.core.product.model.request.VideoProductUpdateRequest;
import ai.creatly.sky.creation.domain.core.product.model.response.ProductRelatedAssetVM;
import ai.creatly.sky.creation.domain.core.product.model.response.UserProductEntryVM;
import ai.creatly.sky.creation.domain.core.product.model.response.UserProductVM;
import ai.creatly.sky.creation.domain.core.userfile.model.response.UserFileVM;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import org.jetbrains.annotations.Nullable;
import org.springframework.web.multipart.MultipartFile;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version UserProductManager.java, v 0.1 2024-10-16 下午5:43 zhoudong
 */
public interface UserProductManager {

    UserFileVM uploadCoverImage(MultipartFile file, UserContext userContext);

    UserProductVM createVideoProduct(VideoProductCreateRequest request, UserContext userContext);

    UserProductVM updateVideoProduct(long id, VideoProductUpdateRequest request, long uid);

    UserProductVM getProductWithAssets(long id, long uid);

    /**
     * 软删除
     *
     * @param id  -
     * @param uid -
     */
    void deleteProduct(long id, long uid);

    Page<UserProductEntryVM> queryPage(long uid, Pageable pageable);

    /**
     * 根据产品分页查询用户素材（排序规则：先返回商品已关联的素材，再返回未关联的）
     *
     * @param uid           用户ID
     * @param id            产品ID
     * @param lastAssetId   上一条素材的更新时间
     * @param lastUpdatedAt 上一条素材ID
     * @param size          分页大小
     * @return 用户素材分页
     */
    List<ProductRelatedAssetVM> queryAssets(long uid, long id, @Nullable ZonedDateTime lastUpdatedAt, @Nullable Long lastAssetId, int size);
}
