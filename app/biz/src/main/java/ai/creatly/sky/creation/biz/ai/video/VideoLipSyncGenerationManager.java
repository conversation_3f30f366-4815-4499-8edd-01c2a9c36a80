/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.ai.video;

import ai.creatly.sky.creation.domain.core.ai.model.AiTaskBizType;
import ai.creatly.sky.creation.domain.core.ai.video.AiVideoLipSyncGenerateClient;
import ai.creatly.sky.creation.domain.core.ai.video.model.KlingVideoLipSyncVM;
import ai.creatly.sky.creation.domain.core.ai.video.model.VideoLipSyncRequest;
import ai.creatly.sky.creation.domain.core.ai.video.model.VideoLipSyncTaskInput;
import ai.creatly.sky.creation.domain.core.ai.video.service.KlingLipSyncService;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskService;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.problem.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * @version ImageGenerationManager.java, v0.1 2025-02-19 20:45
 */
@Service
@RequiredArgsConstructor
public class VideoLipSyncGenerationManager {

    private final AiTaskService                aiTaskService;
    private final AiVideoLipSyncGenerateClient aiVideoLipSyncGenerateClient;
    private final UserCreditDomainService      userCreditDomainService;
    private final TransactionTemplate          transactionTemplate;
    private final KlingLipSyncService          klingLipSyncService;

    public long submitGeneration(UserContext userContext, VideoLipSyncRequest request) {

        VideoLipSyncTaskInput taskInput = new VideoLipSyncTaskInput();
        taskInput.setRequest(request);
        AiTask aiTask = AiTask.buildNew()
                .owner(userContext)
                .taskType(AiTaskType.AI_VIDEO)
                .bizType(AiTaskBizType.video_lip_sync_video)
                .bizNo(IdHelper.getId())
                .subBizNo(StringUtils.EMPTY)
                .bizInput(taskInput)
                .estimatedDuration(Duration.ofMinutes(3))
                .timeoutFromStartedAt(Duration.ofMinutes(15))
                .build();

        // 元气校验
        return transactionTemplate.execute(status -> {
            CreditsExpense expense = aiVideoLipSyncGenerateClient.expense(aiTask);
            boolean isEnough = userCreditDomainService.isCreditsEnough(userContext.getUid(), expense.getAmount(), CreditAccountType.GENERAL);
            if (!isEnough) {
                throw new BizException(UserErrorCode.USER_CREDIT_NOT_ENOUGH);
            } else {
                userCreditDomainService.consumeCredits(expense);
            }
            return aiTaskService.submit(aiTask, true);
        });
    }

    public List<KlingVideoLipSyncVM> getLipSyncList(String language, long uid) {
        return klingLipSyncService.getLipSyncList(language, uid);
    }

}
