/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.feedback.impl;

import ai.creatly.sky.creation.biz.feedback.FeedbackAdminManager;
import ai.creatly.sky.creation.domain.core.feedback.mapper.FeedbackVMMapper;
import ai.creatly.sky.creation.domain.core.feedback.model.Feedback;
import ai.creatly.sky.creation.domain.core.feedback.model.response.FeedbackVM;
import ai.creatly.sky.creation.domain.core.feedback.repository.FeedbackRepository;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version FeedbackAdminManagerImpl.java, v 0.1 2023-12-15 下午23:32 heb
 */
@Service
@RequiredArgsConstructor
public class FeedbackAdminManagerImpl implements FeedbackAdminManager {

    private final FeedbackRepository feedbackRepository;
    private final FeedbackVMMapper   feedbackVMMapper;

    @Override
    public Page<FeedbackVM> queryPage(String keyword, Pageable pageable) {
        Page<Feedback> feedbacks = feedbackRepository.queryPage(keyword, pageable);
        return feedbacks.map(feedbackVMMapper::toFeedbackVM);
    }
}
