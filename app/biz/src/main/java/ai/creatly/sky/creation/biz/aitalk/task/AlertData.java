/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.aitalk.task;

import ai.creatly.sky.creation.domain.support.notification.app.AlertMessage;
import lombok.Builder;
import lombok.With;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version RetryAlertMessage.java, v 0.1 2023-08-05 12:25 joton
 */
@Builder
@With
public record AlertData(Long taskId,
                        String requestId,
                        String ownerName,
                        String lastRequestHost,
                        String requestHost,
                        Integer retryCount,
                        Duration duration,
                        String processHost,
                        String failReason)
        implements AlertMessage {
}
