/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.common.file.processor;

import ai.creatly.sky.creation.biz.common.file.MultipartFileHelper;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.error.FileErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.mapper.UserFileMapper;
import ai.creatly.sky.creation.domain.core.userfile.model.InputStreamFile;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.service.builder.VideoFileBuilder;
import ai.creatly.sky.creation.domain.support.multimedia.model.VideoMetadata;
import com.jspeeder.core.data.problem.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version VideoFileProcessor.java, v 0.1 2024-09-24 上午11:22 zhoudong
 */
@RequiredArgsConstructor
@Slf4j
class VideoFileProcessor implements FileProcessor {

    private final MultipartFileHelper multipartFileHelper;
    private final VideoFileBuilder    videoFileBuilder;
    private final UserFileMapper      userFileMapper;
    private final FileBizSource       bizSource;

    @Override
    public InputStreamFile buildUserFile(MultipartFile file, UserContext userContext) {
        String originalFilename = file.getOriginalFilename();
        VideoMetadata videoMetadata = multipartFileHelper.resolveVideoMetadata(file, bizSource);
        UserFile userFile = videoFileBuilder.build(bizSource, videoMetadata, userContext, originalFilename);
        return userFileMapper.toInputStreamFile(userFile, () -> {
            try {
                return file.getInputStream();
            } catch (IOException e) {
                log.warn("上传视频文件失败", e);
                throw new BizException(FileErrorCode.FILE_UPLOAD_ERROR, userFile.getType().getDesc(), e);
            }
        });
    }
}
