/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.plan;

import ai.creatly.sky.creation.domain.core.plan.model.admin.request.UserPlanCreateRequest;
import ai.creatly.sky.creation.domain.core.plan.model.admin.request.UserPlanUpdateRequest;
import ai.creatly.sky.creation.domain.core.plan.model.admin.response.UserPlanVM;

/**
 *
 * <AUTHOR>
 * @version UserPlanAdminManager.java, v 0.1 2023-12-21 下午9:27 zhoudong
 */
public interface UserPlanAdminManager {

    /**
     * 创建私人定价计划
     *
     * @param request 请求参数
     * @return 私人定价计划
     */
    UserPlanVM create(UserPlanCreateRequest request);

    /**
     * 修改私人定价计划
     *
     * @param request 请求参数
     * @return 私人定价计划
     */
    UserPlanVM update(UserPlanUpdateRequest request);
}
