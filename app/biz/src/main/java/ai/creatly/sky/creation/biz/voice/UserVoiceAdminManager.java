/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.voice;

import ai.creatly.sky.creation.domain.core.aitask.admin.model.AiTaskVM;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;

/**
 * 定制声音后台管理
 *
 * <AUTHOR>
 * @version UserVoiceAdminManager.java, v 0.1 2024-01-19 下午22:43 heb
 */
public interface UserVoiceAdminManager {

    /**
     * 更新声音任务状态
     *
     * @param id      声音ID
     * @param status 更新参数
     * @return
     */
    public boolean updateVoiceTaskStatus(long id, AiTaskStatus status);


    /**
     * 更新声音任务执行方式
     *
     * @param id      声音ID
     * @param autoExec 更新参数
     * @return
     */
    public boolean updateVoiceTaskAutoExec(long id, Boolean autoExec);

    /**
     * 分页查询声音任务列表
     *
     * @param pageable 分页参数
     * @return 声音列表
     */
    Page<AiTaskVM> queryVoiceTaskPage(Pageable pageable);
}
