/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.story.async;

import ai.creatly.sky.creation.biz.common.deferred.DeferredContext;
import ai.creatly.sky.creation.biz.common.deferred.DeferredKey;
import ai.creatly.sky.creation.biz.common.deferred.DeferredResultExpiry;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.story.error.StoryErrorCode;
import ai.creatly.sky.creation.domain.core.story.model.shot.response.StoryShotCameraMoveGenTaskVM;
import ai.creatly.sky.creation.domain.core.story.model.shot.task.StoryShotCameraGenEvent;
import ai.creatly.sky.creation.domain.core.story.model.shot.task.result.ShotCameraMoveResult;
import ai.creatly.sky.creation.domain.core.userfile.model.BizFile;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.async.DeferredResult;

/**
 * 分镜的运镜生成异步任务控制器
 *
 * <AUTHOR>
 * @version StoryShotCameraGenAsyncManager.java, v 0.1 2024-04-07 22:43 heb
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class StoryShotCameraGenAsyncManager {

    private final AiTaskRepository aiTaskRepository;
    private final UserFileHelper   userFileHelper;

    private final LoadingCache<DeferredKey, CachedValue> DEFERRED_CACHE = Caffeine.newBuilder()
            .expireAfter(DeferredResultExpiry.INSTANCE)
            .build(this::buildCacheValue);

    private CachedValue buildCacheValue(DeferredKey deferredKey) {
        return new CachedValue(deferredKey.newDeferredResult(DEFERRED_CACHE), deferredKey.getStartedAt());
    }

    private record CachedValue(DeferredResult<ApiResult<StoryShotCameraMoveGenTaskVM>> deferredResult, long startedAt) {
    }

    /**
     * 异步查询任务运行结果
     *
     * @param aiTask ai任务
     * @return -
     */
    public DeferredResult<ApiResult<StoryShotCameraMoveGenTaskVM>> futureShotCameraMove(AiTask aiTask, DeferredContext context) {
        // 限流校验
        Validates.isTrue(DEFERRED_CACHE.estimatedSize() < 100, CommonErrorCode.SLA_LIMITED);

        // 从缓存中懒加载异步结果
        DeferredKey deferredKey = DeferredKey.task(aiTask.getId(), context);
        DeferredResult<ApiResult<StoryShotCameraMoveGenTaskVM>> deferredResult = DEFERRED_CACHE.get(deferredKey).deferredResult();

        // 如果已经生成结束，则直接设置确定的结果，并失效缓存
        if (aiTask.getStatus().isFinal()) {
            this.onStoryShotCameraCompleted(aiTask);
        }
        return deferredResult;
    }

    /**
     * 任务结束时回调
     *
     * @param aiTask -
     */
    private void onStoryShotCameraCompleted(AiTask aiTask) {
        Asserts.isTrue(aiTask.getStatus().isFinal(), "will not happen!");
        long taskId = aiTask.getId();
        CachedValue cachedValue = DEFERRED_CACHE.getIfPresent(DeferredKey.task(taskId));
        if (cachedValue == null) {
            return;
        }
        log.info("[onStoryShotCameraCompleted]result found,taskId={}", taskId);

        if (aiTask.getStatus() == AiTaskStatus.CANCELED) {
            // 生成失败
            cachedValue.deferredResult().setResult(ApiResult.err(StoryErrorCode.STORY_SHOT_CAMERA_GEN_FAIL));
        } else {
            // 生成成功
            ShotCameraMoveResult bizResult = aiTask.parseBizResult(ShotCameraMoveResult.class);
            String httpFileUrl = userFileHelper.getHttpUrl(bizResult.getFileUrl(), aiTask.getOwnerId());
            BizFile bizFile = new BizFile()
                    .setId(String.valueOf(aiTask.getId()))
                    .setFileId(bizResult.getFileId())
                    .setFileUrl(httpFileUrl);
            StoryShotCameraMoveGenTaskVM taskVM = new StoryShotCameraMoveGenTaskVM()
                    .setStatus(aiTask.getBizStatus())
                    .setFile(bizFile);
            cachedValue.deferredResult().setResult(ApiResult.ok(taskVM));
        }

        long actualMills = System.currentTimeMillis() - cachedValue.startedAt();
        log.info("[onStoryShotCameraCompleted]time cost={}ms", actualMills);

        // 缓存失效
        DEFERRED_CACHE.invalidate(DeferredKey.task(taskId));
    }

    @EventListener(classes = StoryShotCameraGenEvent.class)
    public void onApplicationEvent(StoryShotCameraGenEvent event) {
        Long taskId = event.getTaskId();
        AiTask aiTask = aiTaskRepository.queryById(taskId);
        this.onStoryShotCameraCompleted(aiTask);
    }
}
