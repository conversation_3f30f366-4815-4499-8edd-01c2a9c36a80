/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.ai.chat;

import ai.creatly.sky.creation.domain.core.ai.chat.mapper.ConversationMapper;
import ai.creatly.sky.creation.domain.core.ai.chat.model.Conversation;
import ai.creatly.sky.creation.domain.core.ai.chat.model.ConversationMessage;
import ai.creatly.sky.creation.domain.core.ai.chat.model.MessageChunk;
import ai.creatly.sky.creation.domain.core.ai.chat.model.request.ConversationChatRequest;
import ai.creatly.sky.creation.domain.core.ai.chat.model.response.ConversationMessageVM;
import ai.creatly.sky.creation.domain.core.ai.chat.model.response.ConversationVM;
import ai.creatly.sky.creation.domain.core.ai.chat.service.AiChatService;
import ai.creatly.sky.creation.domain.core.ai.chat.service.ConversationMessageRepository;
import ai.creatly.sky.creation.domain.core.ai.chat.service.ConversationRepository;
import ai.creatly.sky.creation.domain.core.ai.prompt.mapper.UserPromptTemplateMapper;
import ai.creatly.sky.creation.domain.core.ai.prompt.model.PromptTemplateBizType;
import ai.creatly.sky.creation.domain.core.ai.prompt.model.UserPromptTemplate;
import ai.creatly.sky.creation.domain.core.ai.prompt.model.response.UserPromptTemplateVM;
import ai.creatly.sky.creation.domain.core.ai.prompt.service.UserPromptTemplateRepository;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * <AUTHOR>
 * @version ChatManager.java, v0.1 2025-02-19 20:48
 */
@Service
@RequiredArgsConstructor
public class ChatManager {

    private final ConversationMapper            conversationMapper;
    private final AiChatService                 aiChatService;
    private final ConversationRepository        conversationRepository;
    private final ConversationMessageRepository conversationMessageRepository;
    private final UserPromptTemplateRepository userPromptTemplateRepository;
    private final UserPromptTemplateMapper     userPromptTemplateMapper;
    private final TransactionTemplate          transactionTemplate;

    public List<UserPromptTemplateVM> queryPromptTemplates(PromptTemplateBizType bizType) {
        List<UserPromptTemplate> promptTemplates = userPromptTemplateRepository.queryByBizType(bizType);
        return userPromptTemplateMapper.toPromptTemplateVMs(promptTemplates);
    }

    public Flux<MessageChunk> chat(long uid, ConversationChatRequest request) {
        final Conversation conversation;
        if (request.getConversationId() != null) {
            long conversationId = Validates.requireLong(request.getConversationId(), "conversationId");
            conversation = this.queryCheckedConversation(uid, conversationId);
        } else {
            conversation = new Conversation().setUid(uid).setTitle("新对话");
        }
        String userInput = request.getMessages().getFirst().getText();
        return aiChatService.generate(conversation, userInput);
    }

    public Page<ConversationVM> queryPage(long uid, Pageable pageable) {
        Page<Conversation> page = conversationRepository.queryPageByUid(uid, pageable);
        return page.map(conversationMapper::toConversationVM);
    }

    public List<ConversationMessageVM> queryMessages(long uid, long conversationId) {
        Conversation conversation = this.queryCheckedConversation(uid, conversationId);
        List<ConversationMessage> messages = conversationMessageRepository.queryByConversationId(conversation.getId());
        return conversationMapper.toConversationMessageVMs(messages);
    }

    public void deleteConversation(long uid, long id) {
        Conversation conversation = this.queryCheckedConversation(uid, id);
        transactionTemplate.executeWithoutResult(status -> {
            conversationRepository.deleteById(conversation.getId());
            conversationMessageRepository.deleteByConversationId(conversation.getId());
        });
    }

    private Conversation queryCheckedConversation(long uid, long id) {
        Conversation conversation = conversationRepository.queryOptionalById(id).orElse(null);
        Validates.notNull(conversation, "会话不存在");
        Validates.isTrue(conversation.belongsTo(uid), "会话不存在");
        return conversation;
    }
}
