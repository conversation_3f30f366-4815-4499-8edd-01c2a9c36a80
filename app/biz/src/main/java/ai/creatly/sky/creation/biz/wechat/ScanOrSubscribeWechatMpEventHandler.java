/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.wechat;

import ai.creatly.sky.creation.domain.common.env.RuntimeEnv;
import ai.creatly.sky.creation.domain.common.env.StandardEnv;
import ai.creatly.sky.creation.domain.common.http.HttpRequest;
import ai.creatly.sky.creation.domain.common.http.RequestForwardClient;
import ai.creatly.sky.creation.domain.core.auth.model.WechatAppName;
import ai.creatly.sky.creation.domain.core.auth.service.WechatAuthService;
import ai.creatly.sky.creation.domain.support.wechat.model.WechatUser;
import ai.creatly.sky.creation.domain.support.wechat.mp.WechatMpClient;
import ai.creatly.sky.creation.domain.support.wechat.mp.WechatMpEventHandler;
import ai.creatly.sky.creation.domain.support.wechat.mp.WechatMpReplyBuilder;
import ai.creatly.sky.creation.domain.support.wechat.mp.model.message.receive.WechatMpEvent;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version ScanOrSubscribeWechatMpEventHandler.java, v 0.1 2024-10-15 下午7:54 zhoudong
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ScanOrSubscribeWechatMpEventHandler implements WechatMpEventHandler {

    private final WechatAuthService    wechatAuthService;
    private final WechatMpClient       wechatMpClient;
    private final RequestForwardClient requestForwardClient;
    private final RuntimeEnv           runtimeEnv;
    private final WechatMpReplyBuilder wechatMpReplyBuilder;

    @Value("${application.wechat.mp-offline-forward-endpoint}")
    private String offlineForwardEndpoint;

    @Override
    public boolean support(WechatMpEvent event) {
        return event.getEvent().equals("SCAN") || event.getEvent().equals("subscribe");
    }

    @Override
    public String handle(HttpRequest originalRequest, WechatMpEvent event) {
        final String sid;
        if (event.getEvent().equals("SCAN")) {
            sid = event.getEventKey();
        } else if (event.getEvent().equals("subscribe")) {
            if (event.getEventKey().startsWith("qrscene_")) {
                // 带二维码场景值的订阅事件
                sid = event.getEventKey().substring("qrscene_".length());
            } else {
                // 常规订阅事件
                return this.welcomeMessage(event, "感谢关注爱影工坊！");
            }
        } else {
            // 暂时不处理其他事件
            return "success";
        }

        // 获取场景值对应的环境
        StandardEnv env = wechatAuthService.getSignInWebScanSidEnv(sid);
        if (env == null) {
            // 非网页端发起的扫码或订阅事件，暂不处理
            log.warn("未找到场景值对应的环境，sid: {}", sid);
            return "success";
        }

        // 当线上环境遇到线下环境的场景值时，走VPN通道转发到办公内网Nginx上，再由该Nginx转发到内网实例
        if (runtimeEnv.isProd() && env.isOffline()) {
            String endpoint = offlineForwardEndpoint;
            String path = FormatUtil.format("/{}{}", env.name().toLowerCase(), originalRequest.getPath());
            String query = originalRequest.getQuery();
            String body = originalRequest.getBody();
            log.info("线上环境遇到线下环境场景值，转发请求到内网，endpoint={}, path={}, query={}, body=\n{}", endpoint, path, query, body);
            return requestForwardClient.post(endpoint, path, query, originalRequest.getHeaders(), body);
        }

        // 获取微信用户信息
        WechatAppName appName = event.getAppName();
        WechatUser wechatUser = wechatMpClient.getUserInfo(event.getFromUser(), appName);
        wechatAuthService.handleWechatUserEvent(sid, wechatUser);

        // 回复欢迎消息
        return this.welcomeMessage(event, "欢迎使用爱影工坊！");
    }

    private String welcomeMessage(WechatMpEvent event, String content) {
        final String welcome;
        if (runtimeEnv.isProd()) {
            welcome = content;
        } else {
            welcome = FormatUtil.format("【{}】{}", runtimeEnv.getEnv().getDesc(), content);
        }
        return wechatMpReplyBuilder.buildTextMessage(welcome, event.getNonce(), event.getToUser(), event.getFromUser(), event.getAppName());
    }
}
