/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.voice.task;

import ai.creatly.sky.creation.domain.common.messaging.producer.Destination;
import ai.creatly.sky.creation.domain.common.messaging.producer.MessagingTemplate;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.voice.model.task.event.AudioGeneratedEvent;
import ai.creatly.sky.creation.domain.core.voice.model.task.result.VoiceTaskResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version VoiceTaskHelper.java, v 0.1 2024-06-25 下午6:16 zhoudong
 */
@Component
@RequiredArgsConstructor
public class VoiceTaskHelper {

    private final MessagingTemplate       messagingTemplate;

    /**
     * 发送声音合成任务完成的消息
     *
     * @param voiceTask 语音创作任务
     */
    public void sendMessageOnFinished(AiTask voiceTask) {
        String destination = Destination.rocketMQ()
                .topic(AppConstants.MQ_TOPIC)
                .tag(AppConstants.MQTags.VOICE_AUDIO_GENERATED)
                .build();

        // 已完成：发送MQ消息
        if (voiceTask.getStatus() == AiTaskStatus.FINISHED) {
            // 发送生成成功消息
            var result = voiceTask.parseBizResult(VoiceTaskResult.class);
            var event = new AudioGeneratedEvent()
                    .setSuccess(true)
                    .setTaskId(voiceTask.getId())
                    .setAudioId(result.getAudioId());
            messagingTemplate.send(destination, voiceTask.getId().toString(), event);
        }

        // 已取消：发送MQ消息
        if (voiceTask.getStatus() == AiTaskStatus.CANCELED) {
            // 发送生成失败消息
            var event = new AudioGeneratedEvent()
                    .setSuccess(false)
                    .setCancelReason(voiceTask.getSysResult().getCancelReason())
                    .setTaskId(voiceTask.getId());
            messagingTemplate.send(destination, voiceTask.getId().toString(), event);
        }
    }
}
