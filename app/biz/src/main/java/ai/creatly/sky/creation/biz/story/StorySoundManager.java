/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.story;

import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsFactory;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.story.error.StoryErrorCode;
import ai.creatly.sky.creation.domain.core.story.model.shot.response.ShotSoundVM;
import ai.creatly.sky.creation.domain.core.story.model.story.Story;
import ai.creatly.sky.creation.domain.core.story.repository.StoryRepository;
import ai.creatly.sky.creation.domain.core.story.repository.StoryShotRepository;
import ai.creatly.sky.creation.domain.core.story.service.LLMStory;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.support.sound.mapper.ShotSoundMapper;
import ai.creatly.sky.creation.domain.support.sound.model.Sound;
import ai.creatly.sky.creation.domain.support.sound.repository.SoundRepository;
import ai.creatly.sky.creation.domain.support.sound.repository.SoundTagRepository;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version StorySoundManager.java, v 0.1 2024-04-13 下午3:06 zhoudong
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class StorySoundManager {

    private static final Cache<String, List<Sound>> RECOMMEND_SOUNDS_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(8, TimeUnit.HOURS)
            .build();

    private final SoundRepository         soundRepository;
    private final SoundTagRepository      soundTagRepository;
    private final StoryRepository         storyRepository;
    private final StoryShotRepository     storyShotRepository;
    private final ShotSoundMapper         shotSoundMapper;
    private final UserFileHelper          userFileHelper;
    private final LLMStory                llmStory;
    private final CreditsFactory          creditsFactory;
    private final UserCreditDomainService userCreditDomainService;

    /**
     * 推荐分镜的音效
     *
     * @param storyId 故事ID
     * @param shotId  分镜ID
     * @param uid     用户ID
     * @return 音效列表
     */
    public List<ShotSoundVM> recommendShotSounds(long storyId, long shotId, long uid) {
        // 基础存在性校验
        Story story = storyRepository.findOptionalStoryById(storyId)
                .orElseThrow(() -> new BizException(StoryErrorCode.STORY_NOT_FOUND));
        Validates.isTrue(story.belongsTo(uid), StoryErrorCode.STORY_NOT_FOUND);

        String soundDesc = storyShotRepository.queryStoryShotById(storyId, shotId)
                .orElseThrow(() -> new BizException(StoryErrorCode.STORY_SHOT_NOT_FOUND))
                .getSoundsDesc();

        if (StringUtils.isBlank(soundDesc)) {
            // 分镜没有音效描述，返回空列表
            log.warn("No sound description for shot. storyId={}, shotId={}", storyId, shotId);
            return Collections.emptyList();
        }

        // 本地缓存
        List<Sound> sounds = RECOMMEND_SOUNDS_CACHE.getIfPresent(soundDesc);
        if (sounds != null) {
            return sounds.stream()
                    .map(sound -> shotSoundMapper.toShotSoundVM(sound, userFileHelper))
                    .collect(toList());
        }

        // 计费
        CreditsExpense expense = creditsFactory.story().calcShotSoundRecommendExpense(storyId, shotId, uid);
        Validates.isTrue(userCreditDomainService.isCreditsEnough(expense), UserErrorCode.USER_CREDIT_NOT_ENOUGH);

        // 标签推荐
        Set<String> soundTags = soundTagRepository.queryAllTags();
        Set<String> tagNames = llmStory.inferStoryShotSoundTags(soundDesc, soundTags);

        // 根据推荐的标签进行音效检索
        if (tagNames.isEmpty()) {
            // 没有推荐的标签，返回空列表
            log.warn("No recommended sound tags for shot. storyId={}, shotId={}", storyId, shotId);
            return List.of();
        }

        // 扣费
        boolean consumed = userCreditDomainService.consumeCredits(expense);
        Validates.isTrue(consumed, UserErrorCode.USER_CREDIT_NOT_ENOUGH);

        List<Sound> shotSounds = soundRepository.queryByTagNames(tagNames, 3);
        RECOMMEND_SOUNDS_CACHE.put(soundDesc, shotSounds);
        return shotSounds.stream()
                .map(sound -> shotSoundMapper.toShotSoundVM(sound, userFileHelper))
                .collect(toList());
    }
}
