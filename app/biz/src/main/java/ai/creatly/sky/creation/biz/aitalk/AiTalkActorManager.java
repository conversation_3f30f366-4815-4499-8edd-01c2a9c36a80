/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.aitalk;

import ai.creatly.sky.creation.domain.core.aittalk.model.response.AiTalkActorVM;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version AiTalkActorManager.java, v 0.1 2023-06-24 02:15 joton
 */
public interface AiTalkActorManager {

    AiTalkActorVM create(MultipartFile file, UserContext userContext);

    /**
     * 查询用户的关联演员
     *
     * @return -
     */
    List<AiTalkActorVM> queryByUserId(long uid);

    void delete(String id, UserContext userContext);
}
