/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.videoproject.task.render;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.common.integration.videorender.VideoRenderClient;
import ai.creatly.sky.creation.domain.common.integration.videorender.model.VideoRenderRequest;
import ai.creatly.sky.creation.domain.common.integration.videorender.model.VideoRenderResult;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.transaction.AiTaskTransactionManager;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskHelper;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.core.userfile.service.VideoFileService;
import ai.creatly.sky.creation.domain.core.userfile.service.builder.VideoFileBuilder;
import ai.creatly.sky.creation.domain.core.videoproject.mapper.VideoProjectTaskMapper;
import ai.creatly.sky.creation.domain.core.videoproject.model.RenderedVideo;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoSubtitle;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoSubtitleLayer;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.VideoProjectTaskBizType;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.input.VideoEditorRenderTaskInput;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.result.VideoRenderTaskResult;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.vars.VideoRenderTaskVars;
import ai.creatly.sky.creation.domain.support.multimedia.model.VideoMetadata;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.util.time.Times;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

import static java.util.stream.Collectors.toList;

/**
 * 视频编辑器渲染任务处理器
 *
 * <AUTHOR>
 * @version VideoEditorRenderTaskHandler.java, 2024-10-21 上午11:37 zhoudong
 */
//@Component
@RequiredArgsConstructor
@Slf4j
public class VideoEditorRenderTaskHandler implements AiTaskHandler {

    private final VideoRenderClient      videoRenderClient;
    private final AiTaskHelper           aiTaskHelper;
    private final VideoFileBuilder       videoFileBuilder;
    private final UserFileHelper         userFileHelper;
    private final VideoProjectTaskMapper videoProjectTaskMapper;
    private final UserFileService        userFileService;
    private final UserFileRepository     userFileRepository;
    private final VideoFileService       videoFileService;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.VIDEO_PROJECT)
                .setBizType(VideoProjectTaskBizType.video_editor_render)
                .setLoadSize(1)
                .setQueueMode(TaskQueueMode.POP_UNTIL_COMPLETED)
                .setExecCountAlertThreshold(60)
                .setNotifyOnSubmit(false);
    }

    private UserFile buildOutputVideoFile(AiTask aiTask, VideoEditorRenderTaskInput bizInput, long fileId) {
        UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);
        FileBizSource bizSource = FileBizSource.VIDEO_PROJECT_OUTPUT;
        VideoMetadata metadata = new VideoMetadata()
                .setDuration(bizInput.getDuration())
                .setFormat("mp4")
                // TODO 如果用户指定视频输出质量，那么这里需要根据用户指定的质量来设置（比如4K,60fps）
                .setFrameRate(25.0)
                .setWidth(bizInput.getWidth())
                .setHeight(bizInput.getHeight());
        return videoFileBuilder.build(fileId, bizSource, metadata, userContext);
    }

    private VideoRenderRequest buildRenderRequest(long requestId, VideoEditorRenderTaskInput bizInput, UserFile outputVideoFile) {
        VideoRenderRequest.VideoRenderData renderData = new VideoRenderRequest.VideoRenderData();

        // 添加音轨
        var audios = bizInput.getAudioTracks()
                .stream()
                .map(audioTrack -> {
                    String url = userFileHelper.getHttpUrl(audioTrack.getUrl(), FileAcl.PRIVATE);
                    var audio = new VideoRenderRequest.VideoRenderData.Audio().setAssetUrl(url);
                    if (audioTrack.getStart() != null && audioTrack.getEnd() != null) {
                        audio.setStartTime(Times.toSeconds(audioTrack.getStart()))
                                .setEndTime(Times.toSeconds(audioTrack.getEnd()));
                    }
                    return audio;
                })
                .collect(toList());
        renderData.setAudios(audios);

        // 添加视频轨
        var videos = bizInput.getVideoTracks()
                .stream()
                .map(videoTrack -> {
                    String url = userFileHelper.getHttpUrl(videoTrack.getUrl(), FileAcl.PRIVATE);
                    return new VideoRenderRequest.VideoRenderData.Video()
                            .setAssetUrl(url)
                            .setStartTime(Times.toSeconds(Objects.requireNonNull(videoTrack.getStart())))
                            .setEndTime(Times.toSeconds(Objects.requireNonNull(videoTrack.getEnd())))
                            // 默认静音
                            .setMute(videoTrack.getMute() == null || videoTrack.getMute())
                            // 默认不加水印
                            .setWatermark(videoTrack.getWatermark() != null && videoTrack.getWatermark());
                })
                .collect(toList());
        renderData.setVideos(videos);

        // 添加字幕层
        if (bizInput.getSubtitleLayer() != null) {
            VideoSubtitleLayer subtitleLayer = bizInput.getSubtitleLayer();
            List<VideoSubtitle> subtitles = bizInput.getSubtitles();
            renderData.setSubtitle(videoProjectTaskMapper.toRenderSubtitle(subtitleLayer, subtitles));
        }

        return new VideoRenderRequest()
                .setId(String.valueOf(requestId))
                .setHeight(bizInput.getHeight())
                .setWidth(bizInput.getWidth())
                .setDuration(Times.toSeconds(bizInput.getDuration()))
                .setOssBucket(outputVideoFile.getBucket())
                .setOssKey(outputVideoFile.getKey())
                .setData(renderData);
    }

    @Override
    public TaskPreAction preHandle(AiTask aiTask) {
        VideoEditorRenderTaskInput bizInput = aiTask.parseBizInput(VideoEditorRenderTaskInput.class);
        VideoRenderTaskVars bizVars = aiTask.parseBizVars(VideoRenderTaskVars.class);
        if (bizVars.getOutputVideoFileId() != null) {
            // 任务回滚后，业务过程数据会被清空，所以回滚后再首次执行是不会进入这里的
            return TaskPreAction.FORWARD_RUNNING;
        }

        // 构建视频文件
        long fileId = IdHelper.getId();
        UserFile outputVideoFile = this.buildOutputVideoFile(aiTask, bizInput, fileId);

        // 发起渲染任务（每次使用不一样的请求ID）
        VideoRenderRequest request = this.buildRenderRequest(fileId, bizInput, outputVideoFile);
        videoRenderClient.renderVideo(request);
        log.info("已发起视频编辑器渲染，requestId:{}", fileId);
        // 注意⚠️：这里为什么不直接推进状态，因为任务发起都是异步的，所以这里最好再等待一个调度周期
        return TaskPreAction.KEEP_STILL.updateBizVars(bizVars.setOutputVideoFileId(fileId));
    }

    @Override
    public TaskAction handle(AiTask aiTask) {
        if (Objects.requireNonNull(aiTask.getStartedAt()).plusSeconds(30).isAfter(ZonedDateTime.now())) {
            // 等待30秒后再查询渲染进度 TODO 用任务的延迟参数来实现
            return TaskAction.KEEP_STILL;
        }
        VideoRenderTaskVars bizVars = aiTask.parseBizVars(VideoRenderTaskVars.class);
        VideoRenderResult renderResult = videoRenderClient.getRenderProgress(bizVars.getOutputVideoFileId());
        if (renderResult.isFailed()) {
            String reason = ObjectUtils.defaultIfNull(renderResult.getMessage(), "视频渲染失败");
            if (aiTask.getExecInfo().getRollbackCount() <= 5) {
                return TaskAction.rollback(reason);
            }
            log.error("视频编辑器渲染失败，taskId:{},reason:{}", aiTask.getId(), reason);
            return TaskAction.FORWARD_CANCELED.withReason(reason + "(重试次数已用完)");
        }

        if (renderResult.isWaiting()) {
            log.info("视频编辑器渲染进度查询，taskId:{},renderResult:{}", aiTask.getId(), renderResult);
            return TaskAction.KEEP_STILL;
        }

        // 确保视频文件上传到 OSS 了
        VideoEditorRenderTaskInput bizInput = aiTask.parseBizInput(VideoEditorRenderTaskInput.class);
        UserFile outputVideoFile = this.buildOutputVideoFile(aiTask, bizInput, bizVars.getOutputVideoFileId());
        if (!userFileService.isStoragePresent(outputVideoFile)) {
            if (aiTask.getRetryCount("output_video_file") > 5) {
                String reason = "OSS上未检测到视频文件";
                if (aiTask.getExecInfo().getRollbackCount() <= 5) {
                    return TaskAction.rollback(reason);
                }
                log.error("视频编辑器渲染失败，taskId:{},reason:{}", aiTask.getId(), reason);
                return TaskAction.FORWARD_CANCELED.withReason(reason + "(重试次数已用完)");
            }
            // 等待视频文件上传到 OSS
            return TaskAction.KEEP_STILL.withRetry("output_video_file", "等待视频文件上传到OSS");
        }
        final long bytes;
        if (renderResult.getBytes() != null) {
            bytes = renderResult.getBytes();
        } else {
            bytes = userFileService.getContentMetadata(outputVideoFile).getLength();
        }
        outputVideoFile.getMetadata().setBytes(bytes);
        log.info("视频编辑器渲染成功，taskId:{}", aiTask.getId());

        // 持久化
        AiTaskTransactionManager.registerSynchronization(() -> userFileRepository.create(outputVideoFile));
        RenderedVideo renderedVideo = this.toRenderedVideo(aiTask, outputVideoFile);
        VideoRenderTaskResult bizResult = new VideoRenderTaskResult().setVideo(renderedVideo);
        return TaskAction.FORWARD_FINISHED.updateBizResult(bizResult);
    }

    private RenderedVideo toRenderedVideo(AiTask aiTask, UserFile outputVideoFile) {
        String coverUrl = videoFileService.clipCoverImageUrl(outputVideoFile, 1);
        return new RenderedVideo()
                .setRenderTaskId(aiTask.getId())
                .setFileId(outputVideoFile.getId())
                .setUrl(UserFileHelper.toOssUrl(outputVideoFile))
                .setCoverUrl(coverUrl)
                .setSize(outputVideoFile.size())
                .setDuration(outputVideoFile.getDuration())
                .setFrameRate(outputVideoFile.getMetadata().getFrameRate());
    }

    @Override
    public TaskPostAction postHandle(AiTask aiTask) {
        return TaskPostAction.COMPLETE;
    }
}
