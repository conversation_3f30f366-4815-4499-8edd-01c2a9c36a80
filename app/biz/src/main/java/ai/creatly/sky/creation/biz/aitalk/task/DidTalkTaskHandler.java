/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.aitalk.task;

import ai.creatly.sky.creation.domain.common.integration.did.DidClient;
import ai.creatly.sky.creation.domain.common.integration.did.model.*;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.UpdatableAiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aittalk.error.AiTalkErrorCode;
import ai.creatly.sky.creation.domain.core.aittalk.model.task.AiTalkTaskBizType;
import ai.creatly.sky.creation.domain.core.aittalk.model.task.result.AiTalkVideoResult;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * <AUTHOR>
 * @version DidTalkTaskHandler.java, v 0.1 2023-07-28 21:02 joton
 */
@Slf4j
//@Component
@RequiredArgsConstructor
public class DidTalkTaskHandler implements AiTaskHandler {

    private final DidClient didClient;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.CREATE_AI_TALK_VIDEO)
                .setBizType(AiTalkTaskBizType.DID_TALK)
                .setLoadSize(10);
    }

    @Override
    public TaskPreAction preHandle(AiTask aiTask) {
        // 已创建 -> 进行中：发起DID请求
        // 检查用户信用余额 todo 设计用户账户体系
        DidCredits credits = didClient.getCredits();
        Validates.isTrue(credits.getRemaining() > 0, AiTalkErrorCode.ACCOUNT_CREDIT_INSUFFICIENT);

        CreateTalkRequest talkRequest = aiTask.parseBizParams(CreateTalkRequest.class);
        CreateTalkResponse talkResponse = didClient.createTalk(talkRequest);

        AiTalkVideoResult result = aiTask.parseBizResult(AiTalkVideoResult.class);
        result.setCreatedBy(talkResponse.getCreatedBy());

        UpdatableAiTask updatableAiTask = new UpdatableAiTask()
                .setBizStatus(talkResponse.getStatus().name())
                .setBizNo(talkResponse.getId())
                .setBizResult(result);
        return TaskPreAction.FORWARD_RUNNING.withAiTask(updatableAiTask);
    }

    @Override
    public TaskAction handle(AiTask aiTask) {
        // 进行中 -> 已完成：轮询DID结果
        UpdatableAiTask updatableAiTask = new UpdatableAiTask();

        String didTalkId = aiTask.getBizNo();
        final DidTalk talk = didClient.getTalk(didTalkId);
        final String bizStatus = talk.getStatus().name();
        ZonedDateTime startedAt = null;
        if (StringUtils.isNotBlank(talk.getStartedAt())) {
            try {
                startedAt = LocalDateTime.parse(talk.getStartedAt(), DateTimeFormatter.ISO_DATE_TIME)
                        .atZone(ZoneOffset.UTC)
                        .withZoneSameInstant(ZoneId.systemDefault());
            } catch (DateTimeParseException e) {
                log.error("[pollDidTalks][startedAt_unresolvable]talk={}", talk, e);
            }
        }
        updatableAiTask.setBizStatus(bizStatus).setStartedAt(startedAt);

        // 正常完成
        if (talk.getStatus() == DidTalkStatus.done) {
            AiTalkVideoResult result = new AiTalkVideoResult()
                    .setResultUrl(talk.getResultUrl())
                    // TODO: 2023/6/27 转存到OSS，存文件ID
                    .setAudioUrl(talk.getAudioUrl())
                    .setMetadata(talk.getMetadata());
            updatableAiTask.setBizResult(result);
            return TaskAction.FORWARD_FINISHED.withAiTask(updatableAiTask);
        }

        // 错误或拒绝
        if (talk.getStatus() == DidTalkStatus.error || talk.getStatus() == DidTalkStatus.rejected) {
            log.error("[pollDidTalks][error_or_rejected]taskId={},talk={}", aiTask.getId(), talk);
            return TaskAction.FORWARD_CANCELED.withAiTask(updatableAiTask);
        }

        // 已开始
        if (talk.getStatus() == DidTalkStatus.started) {
            log.info("[pollDidTalks][started]taskId={},talk={}", aiTask.getId(), talk);
            return TaskAction.KEEP_STILL.withAiTask(updatableAiTask);
        }

        // 空跑（即业务状态仍然是 created）
        log.info("[pollDidTalks][dryRun]taskId={},talk={}", aiTask.getId(), talk);
        return TaskAction.KEEP_STILL;
    }

    @Override
    public TaskPostAction postHandle(AiTask aiTask) {
        return TaskPostAction.COMPLETE;
    }
}
