/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.deprecated.workspace;

import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.deprecated.workspace.model.WorkspaceTemplate;
import ai.creatly.sky.creation.domain.deprecated.workspace.model.response.WorkspaceTemplateVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @version UserWorkspaceMapper.java, v 0.1 2023-09-23 22:23 syoka
 */
@Mapper(config = BaseMapperConfig.class)
@RequiredArgsConstructor
public abstract class WorkspaceTemplateMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    @Mapping(target = "coverImage", ignore = true)
    public abstract WorkspaceTemplateVM toWorkspaceTemplateVM(WorkspaceTemplate template);

    public abstract List<WorkspaceTemplateVM> toWorkspaceTemplateVMs(List<WorkspaceTemplate> workspaceTemplates);

    @AfterMapping
    protected void toWorkspaceTemplateAfter(WorkspaceTemplate template, @MappingTarget WorkspaceTemplateVM templateVM) {
        String coverOssKey = template.getCoverOssKey();
        String httpUrl = userFileHelper.getHttpUrl(coverOssKey, AppConstants.SYSTEM_UID);
        templateVM.setCoverImage(httpUrl);
    }
}
