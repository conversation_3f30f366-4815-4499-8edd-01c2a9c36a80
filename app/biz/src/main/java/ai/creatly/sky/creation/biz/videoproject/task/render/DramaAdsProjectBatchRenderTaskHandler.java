/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.videoproject.task.render;

import ai.creatly.sky.creation.biz.videoproject.task.AbstractVideoProjectRenderTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.videoproject.mapper.VideoProjectTaskMapper;
import ai.creatly.sky.creation.domain.core.videoproject.model.RenderedVideo;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoProject;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoProjectConstants;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.VideoProjectTaskBizType;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.input.DramaAdsBatchRenderTaskInput;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.input.DramaAdsComposeTaskInput;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.input.DramaAdsRenderTaskInput;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.result.VideoRenderTaskResult;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.vars.DramaAdsComposeTaskVars;
import com.jspeeder.core.data.enums.ICode;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;

import java.time.Duration;
import java.util.*;

/**
 * <AUTHOR>
 * @version DramaAdsProjectBatchRenderTaskHandler.java, 2024-11-27 上午8:58 zhoudong
 */
//@Component
@RequiredArgsConstructor
@Slf4j
public class DramaAdsProjectBatchRenderTaskHandler extends AbstractVideoProjectRenderTaskHandler implements AiTaskHandler {

    private final VideoProjectTaskMapper videoProjectTaskMapper;

    @Value("${application.video-project.drama-ads.rendered-wechat-message-template-id}")
    private String renderedWechatMessageTemplateId;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.VIDEO_PROJECT)
                .setBizType(VideoProjectTaskBizType.drama_ads_batch_render)
                .setLoadSize(1)
                .setQueueMode(TaskQueueMode.POP_UNTIL_COMPLETED)
                .setExecCountAlertThreshold(60)
                .setNotifyOnSubmit(true)
                .setNotifyUserOnCompleted(true);
    }

    @Override
    protected TaskPreAction preHandle(AiTask aiTask, VideoProject project) {
        DramaAdsBatchRenderTaskInput bizInput = aiTask.parseBizInput(DramaAdsBatchRenderTaskInput.class);
        UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);

        // 校验项目素材是否打标完成
        if (bizInput.getAssets() != null) {
            boolean allAssetPreprocessed = super.checkAllAssetPreprocessed(userContext, project.getId(), bizInput.getAssets());
            if (!allAssetPreprocessed) {
                return TaskPreAction.KEEP_STILL;
            }
        }

        // 拆分剧情视频渲染任务
        List<AiTask> renderTasks = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(project.getComposeTasks())) {
            List<AiTask> composeTasks = this.queryComposeTasks(aiTask.getBizNo(), bizInput.getComposeTaskIds());
            Asserts.notEmpty(composeTasks, "will not happen");
            for (AiTask composeTask : composeTasks) {
                // 沿用编排任务生成的视频协议，继续渲染视频
                DramaAdsComposeTaskInput composeTaskInput = composeTask.parseBizInput(DramaAdsComposeTaskInput.class);
                long composeTaskId = project.getComposeTasks().get(composeTaskInput.getComposeIdx()).getTaskId();
                DramaAdsRenderTaskInput renderTaskInput = videoProjectTaskMapper.toDramaAdsRenderTaskInput(composeTaskInput, composeTaskId);
                DramaAdsComposeTaskVars renderTaskVars = composeTask.parseBizVars(DramaAdsComposeTaskVars.class);
                // 仅执行渲染逻辑，不用设置超时，因为子任务会超时
                final AiTask renderTask = AiTask.buildNew()
                        .owner(userContext)
                        .taskType(aiTask.getTaskType())
                        .bizType(VideoProjectTaskBizType.drama_ads_render)
                        // projectId:composeVersion
                        .bizNo(composeTask.getBizNo())
                        // 剧情ID
                        .subBizNo(composeTask.getSubBizNo())
                        .bizInput(renderTaskInput)
                        .bizStatus(VideoProjectConstants.VIDEO_COMPOSED)
                        .bizVars(renderTaskVars)
                        .build();
                renderTasks.add(renderTask);
            }
        } else {
            // 该项目没有编排过剧情视频，则走全流程渲染
            final DramaAdsRenderTaskInput renderTaskInput = videoProjectTaskMapper.toDramaAdsRenderTaskInput(bizInput);
            final AiTask renderTask = AiTask.buildNew()
                    .owner(userContext)
                    .taskType(aiTask.getTaskType())
                    .bizType(VideoProjectTaskBizType.drama_ads_render)
                    // projectId:composeVersion
                    .bizNo(VideoProjectTaskBizType.getSingleBizNo(project))
                    .subBizNo(StringUtils.EMPTY)
                    .bizInput(renderTaskInput)
                    .timeoutFromStartedAt(Duration.ofMinutes(10))
                    .build();
            renderTasks.add(renderTask);
        }

        renderTasks.forEach(task -> aiTaskService.submit(task, true));

        return TaskPreAction.FORWARD_RUNNING;
    }

    @Override
    protected TaskAction handle(AiTask aiTask, VideoProject project) {
        List<AiTask> renderTasks = this.queryRenderTasks(project);
        if (renderTasks.stream().allMatch(AiTask::isCanceled)) {
            // 渲染任务全部取消了，则这一批次渲染任务也取消
            return TaskAction.FORWARD_CANCELED.withReason(renderTasks.getFirst().getSysResult().getCancelReason());
        }
        if (renderTasks.stream().allMatch(AiTask::isCompleted)) {
            return TaskAction.FORWARD_FINISHED;
        }
        return TaskAction.KEEP_STILL;
    }

    @Override
    public TaskPostAction postHandle(AiTask aiTask) {
        long projectId = this.getProjectId(aiTask);
        VideoProject project = videoProjectRepository.queryById(projectId);
        if (aiTask.getStatus().isCanceled()) {
            // 渲染失败
            transactionTemplate.executeWithoutResult(status -> {
                videoProjectService.renderFailed(project.getId(), aiTask.getSysResult().getCancelReason());
                // TODO 退款
            });
        } else {
            // 渲染成功（相同编排批次的都会被查询出来）
            List<RenderedVideo> videos = this.queryRenderTasks(project)
                    .stream()
                    .filter(task -> task.getStatus().isFinished())
                    .map(task -> task.parseBizResult(VideoRenderTaskResult.class).getVideo())
                    .filter(Objects::nonNull)
                    .toList();
            videoProjectService.renderSuccess(project.getId(), videos);

            // 发送微信公众号消息通知 TODO 微信端还没审核通过
            String templateId = renderedWechatMessageTemplateId;
            Map<String, String> params = new HashMap<>();
            aiTaskHelper.sendWechatMessageSilently(aiTask, templateId, params);
        }

        return TaskPostAction.COMPLETE;
    }

    private List<AiTask> queryComposeTasks(String composeTaskBizNo, @Nullable Set<Long> composeTaskIds) {
        ICode composeTaskBizType = VideoProjectTaskBizType.drama_ads_compose;
        List<AiTask> composeTasks = aiTaskRepository.queryByTaskTypeAndBizNo(AiTaskType.VIDEO_PROJECT, composeTaskBizType, composeTaskBizNo);
        return composeTasks.stream()
                .filter(AiTask::isCompleted)
                .filter(AiTask::isFinished)
                .filter(composeTask -> composeTaskIds == null || composeTaskIds.contains(composeTask.getId()))
                .toList();
    }

    private List<AiTask> queryRenderTasks(VideoProject project) {
        ICode renderTaskBizType = VideoProjectTaskBizType.drama_ads_render;
        String renderTaskBizNo = VideoProjectTaskBizType.getSingleBizNo(project);
        return aiTaskRepository.queryByTaskTypeAndBizNo(AiTaskType.VIDEO_PROJECT, renderTaskBizType, renderTaskBizNo);
    }
}
