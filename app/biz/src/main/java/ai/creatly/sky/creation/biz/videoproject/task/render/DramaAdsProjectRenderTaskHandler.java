/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.videoproject.task.render;

import ai.creatly.sky.creation.biz.videoproject.task.AbstractVideoProjectRenderTaskHandler;
import ai.creatly.sky.creation.biz.videoproject.task.compose.DramaAdsProjectComposeTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskService;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.videoproject.mapper.VideoProjectTaskMapper;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoComposition;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoProject;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.VideoProjectTaskBizType;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.input.DramaAdsRenderTaskInput;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.vars.DramaAdsRenderTaskVars;
import ai.creatly.sky.creation.domain.core.videoproject.service.VideoProjectTaskService;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;

/**
 * 剧情项目渲染任务处理器
 *
 * <AUTHOR>
 * @version DramaAdsProjectRenderTaskHandler.java, 2024-10-21 上午11:36 zhoudong
 */
//@Component
@RequiredArgsConstructor
@Slf4j
public class DramaAdsProjectRenderTaskHandler extends AbstractVideoProjectRenderTaskHandler implements AiTaskHandler {

    private final VideoProjectTaskMapper            videoProjectTaskMapper;
    private final AiTaskService                     aiTaskService;
    private final DramaAdsProjectComposeTaskHandler composeTaskHandler;
    private final VideoProjectTaskService           videoProjectTaskService;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.VIDEO_PROJECT)
                .setBizType(VideoProjectTaskBizType.drama_ads_render)
                .setLoadSize(1)
                .setQueueMode(TaskQueueMode.POP_UNTIL_COMPLETED)
                .setExecCountAlertThreshold(60)
                .setNotifyOnSubmit(true)
                .setNotifyUserOnCompleted(true);
    }

    @Override
    protected TaskPreAction preHandle(AiTask aiTask, VideoProject project) {
        if (aiTask.getBizStatus().isBlank()) {
            // 校验项目素材是否打标完成
            DramaAdsRenderTaskInput bizInput = aiTask.parseBizInput(DramaAdsRenderTaskInput.class);
            Asserts.notEmpty(bizInput.getAssets(), "will not happen");
            UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);
            boolean allAssetPreprocessed = super.checkAllAssetPreprocessed(userContext, project.getId(), bizInput.getAssets());
            if (!allAssetPreprocessed) {
                return TaskPreAction.KEEP_STILL;
            }
            return TaskPreAction.FORWARD_RUNNING.updateBizStatus(ASSETS_READY);
        }
        return TaskPreAction.FORWARD_RUNNING;
    }

    @Override
    protected TaskAction handle(AiTask aiTask, VideoProject project) {
        DramaAdsRenderTaskInput bizInput = aiTask.parseBizInput(DramaAdsRenderTaskInput.class);
        DramaAdsRenderTaskVars bizVars = aiTask.parseBizVars(DramaAdsRenderTaskVars.class);
        switch (aiTask.getBizStatus()) {
            // 第1阶段：编排视频
            case ASSETS_READY, VIDEO_DIRECTING, VIDEO_DIRECTED -> {
                TaskAction taskAction = composeTaskHandler.handle(aiTask, bizInput, bizVars);
                if (!taskAction.getType().isForwardFinished()) {
                    return taskAction;
                }
                return taskAction.KEEP_STILL.updateBizStatusAndVars(VIDEO_COMPOSED, bizVars);
            }
            // 第2阶段：渲染视频（发起视频渲染任务）
            case VIDEO_COMPOSED -> {
                UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);
                long editorRenderTaskId = this.submitVideoRenderTask(aiTask, userContext, bizInput, bizVars);
                log.info("已发起视频渲染任务，taskId:{},editorRenderTaskId:{}", aiTask.getId(), editorRenderTaskId);
                bizVars.setEditorRenderTaskId(editorRenderTaskId);
                return TaskAction.KEEP_STILL.updateBizStatusAndVars(VIDEO_RENDERING, bizVars);
            }
            // 第2阶段：渲染视频（查询渲染进度）
            case VIDEO_RENDERING -> {
                @Nullable Long composeTaskId = bizInput.getComposeTaskId();
                long editorRenderTaskId = bizVars.getEditorRenderTaskId();
                return this.videoRenderResultCheck(aiTask, composeTaskId, editorRenderTaskId);
            }
            default -> {
                log.warn("未知的任务状态，taskId:{},bizStatus:{}", aiTask.getId(), aiTask.getBizStatus());
                throw new IllegalStateException("未知的任务状态:" + aiTask.getBizStatus());
            }
        }
    }

    private long submitVideoRenderTask(AiTask mainTask, UserContext userContext, DramaAdsRenderTaskInput bizInput,
                                       DramaAdsRenderTaskVars bizVars) {
        VideoComposition videoComposition = videoProjectTaskService.buildVideoComposition(bizInput, bizVars);
        AiTask videoRenderTask = AiTask.buildNew()
                .owner(userContext)
                .taskType(AiTaskType.VIDEO_PROJECT)
                .bizType(VideoProjectTaskBizType.video_editor_render)
                .bizNo(mainTask.getBizNo())
                .subBizNo(mainTask.getSubBizNo())
                .bizInput(videoProjectTaskMapper.toVideoEditorRenderTaskInput(videoComposition))
                // 视频渲染，8分钟超时
                .timeoutFromStartedAt(Duration.ofMinutes(8))
                .build();
        // 幂等保障
        return aiTaskService.submit(videoRenderTask, true);
    }

    @Override
    public TaskPostAction postHandle(AiTask aiTask) {
        return TaskPostAction.COMPLETE;
    }
}
