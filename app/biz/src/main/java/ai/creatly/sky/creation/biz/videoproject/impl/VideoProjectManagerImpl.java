/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.videoproject.impl;

import ai.creatly.sky.creation.biz.common.file.processor.FileProcessors;
import ai.creatly.sky.creation.biz.videoproject.VideoProjectManager;
import ai.creatly.sky.creation.domain.common.integration.videoshot.VideoScriptClient;
import ai.creatly.sky.creation.domain.common.integration.videoshot.model.request.ShotsGenerateRequest;
import ai.creatly.sky.creation.domain.common.integration.videoshot.model.request.VideoShotAsset;
import ai.creatly.sky.creation.domain.common.integration.videoshot.model.response.VideoScriptShot;
import ai.creatly.sky.creation.domain.common.integration.videoshot.model.response.VideoShotMediaType;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskService;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsFactory;
import ai.creatly.sky.creation.domain.core.userasset.mapper.UserAssetMapper;
import ai.creatly.sky.creation.domain.core.userasset.service.UserAssetRepository;
import ai.creatly.sky.creation.domain.core.userfile.mapper.UserFileMapper;
import ai.creatly.sky.creation.domain.core.userfile.model.InputStreamFile;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.response.UserFileVM;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.core.userfile.service.builder.LocalFileBuilder;
import ai.creatly.sky.creation.domain.core.videoproject.error.VideoProjectErrorCode;
import ai.creatly.sky.creation.domain.core.videoproject.mapper.VideoProjectMapper;
import ai.creatly.sky.creation.domain.core.videoproject.mapper.VideoProjectVMMapper;
import ai.creatly.sky.creation.domain.core.videoproject.model.*;
import ai.creatly.sky.creation.domain.core.videoproject.model.enums.AudioTrackType;
import ai.creatly.sky.creation.domain.core.videoproject.model.enums.VideoProjectStage;
import ai.creatly.sky.creation.domain.core.videoproject.model.enums.VideoProjectStatus;
import ai.creatly.sky.creation.domain.core.videoproject.model.enums.VideoProjectType;
import ai.creatly.sky.creation.domain.core.videoproject.model.reponse.VideoProjectEntryVM;
import ai.creatly.sky.creation.domain.core.videoproject.model.reponse.VideoProjectVM;
import ai.creatly.sky.creation.domain.core.videoproject.model.reponse.VideoRenderConsumptionVM;
import ai.creatly.sky.creation.domain.core.videoproject.model.request.VideoProjectRenderRequest;
import ai.creatly.sky.creation.domain.core.videoproject.model.request.VideoShotSaveDTO;
import ai.creatly.sky.creation.domain.core.videoproject.model.request.VideoShotsSaveRequest;
import ai.creatly.sky.creation.domain.core.videoproject.model.request.drama.DramaProjectSaveRequest;
import ai.creatly.sky.creation.domain.core.videoproject.model.request.product.ProductVideoProjectSaveRequest;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.VideoComposeStage;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.VideoProjectTaskBizType;
import ai.creatly.sky.creation.domain.core.videoproject.service.VideoProjectRepository;
import ai.creatly.sky.creation.domain.core.videoproject.service.VideoProjectService;
import ai.creatly.sky.creation.domain.core.videoproject.service.VideoProjectTaskBuilder;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceTimeEstimation;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceSynthesizeRequest;
import ai.creatly.sky.creation.domain.core.voice.service.VoiceService;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.Validates;
import com.jspeeder.core.util.time.Times;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.jooq.lambda.Seq;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.IntStream;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * @version VideoProjectManagerImpl.java, v 0.1 2024-09-21 下午5:24 zhoudong
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VideoProjectManagerImpl implements VideoProjectManager {

    private final VideoProjectRepository  videoProjectRepository;
    private final VideoProjectMapper      videoProjectMapper;
    private final VideoProjectVMMapper    videoProjectVMMapper;
    private final UserFileRepository      userFileRepository;
    private final UserFileService         userFileService;
    private final FileProcessors          fileProcessors;
    private final TransactionTemplate     transactionTemplate;
    private final UserFileMapper          userFileMapper;
    private final VideoScriptClient       videoScriptClient;
    private final VideoProjectService     videoProjectService;
    private final LocalFileBuilder        localFileBuilder;
    private final VoiceService            voiceService;
    private final CreditsFactory          creditsFactory;
    private final AiTaskService           aiTaskService;
    private final VideoProjectTaskBuilder videoProjectTaskBuilder;
    private final UserAssetRepository     userAssetRepository;
    private final UserAssetMapper         userAssetMapper;

    @Override
    public VideoProjectVM createProject(ProductVideoProjectSaveRequest request, UserContext userContext) {
        long uid = userContext.getUid();
        SavingVideoProject savingVideoProject = videoProjectMapper.toSavingVideoProject(request);
        long id = videoProjectService.createVideoProject(savingVideoProject, uid);
        return this.getProject(id, uid);
    }

    @Override
    public VideoProjectVM updateProject(long id, ProductVideoProjectSaveRequest request, long uid) {
        SavingVideoProject savingVideoProject = videoProjectMapper.toSavingVideoProject(request);
        videoProjectService.updateVideoProject(id, savingVideoProject, uid);
        return this.getProject(id, uid);
    }

    @Override
    public VideoProjectVM createProject(DramaProjectSaveRequest request, UserContext userContext) {
        long uid = userContext.getUid();
        SavingVideoProject savingVideoProject = videoProjectMapper.toSavingVideoProject(request);
        long id = videoProjectService.createVideoProject(savingVideoProject, uid, videoProject -> {
            // 设置默认的字幕层
            VideoSubtitleLayer subtitleLayer = VideoProjectConstants.DEFAULT_SUBTITLE_LAYER.apply(videoProject.getHeight());
            videoProject.setSubtitleLayer(subtitleLayer);
        });
        return this.getProject(id, uid);
    }

    @Override
    public VideoProjectVM updateProject(long id, DramaProjectSaveRequest request, long uid) {
        SavingVideoProject savingVideoProject = videoProjectMapper.toSavingVideoProject(request);
        videoProjectService.updateVideoProject(id, savingVideoProject, uid, (videoProject, updatingVideoProject) -> {
            if (updatingVideoProject.getSubtitleLayer() == null) {
                // 剧情广告片有默认的字幕配置，如果用户没有干预的话，保留默认配置
                updatingVideoProject.setSubtitleLayer(videoProject.getSubtitleLayer());
            }
            // 如果修改了视频高度，则需要重新计算默认字幕层的top位置
            if (updatingVideoProject.getHeight() != null) {
                // TODO 配置化
                VideoSubtitleLayer subtitleLayer = VideoProjectConstants.DEFAULT_SUBTITLE_LAYER.apply(updatingVideoProject.getHeight());
                updatingVideoProject.setSubtitleLayer(subtitleLayer);
            }
        });
        return this.getProject(id, uid);
    }

    @Override
    public VideoProjectVM getProject(long id, long uid) {
        VideoProject videoProject = videoProjectService.getCheckedVideoProject(id, uid);
        return videoProjectVMMapper.toVideoProjectVM(videoProject);
    }

    @Override
    public Page<VideoProjectEntryVM> getProjectPage(long uid, Pageable pageable) {
        return videoProjectRepository.queryPageByUidAndStatus(uid, VideoProjectStatus.notDeleted(), pageable).map(videoProject -> {
            @Nullable Long renderEstimatedMills = this.estimateVideoRenderMills(videoProject.getDuration());
            return videoProjectVMMapper.toVideoProjectEntryVM(videoProject, renderEstimatedMills);
        });
    }

    @Nullable
    private Long estimateVideoRenderMills(@Nullable Duration duration) {
        if (duration == null) {
            return null;
        }
        return duration.toMillis() * VideoProjectConstants.VIDEO_RENDER_TIME_RATE;
    }

    @Override
    public void deleteProject(long projectId, long uid) {
        // 数据校验
        VideoProject videoProject = videoProjectRepository.queryOptionalById(projectId).orElse(null);
        Validates.notNull(videoProject, VideoProjectErrorCode.VIDEO_PROJECT_NOT_FOUND);
        Validates.isTrue(Objects.equals(videoProject.getUid(), uid), VideoProjectErrorCode.VIDEO_PROJECT_NOT_FOUND);
        Validates.isTrue(videoProject.isDeletable(), VideoProjectErrorCode.VIDEO_PROJECT_DELETE_NOT_ALLOWED);

        // 幂等校验
        if (videoProject.getStatus().isDeleted()) {
            return;
        }

        // 软删除视频项目
        videoProjectRepository.updateById(new VideoProject().setId(projectId).setStatus(VideoProjectStatus.deleted));
    }

    @Override
    public UserFileVM uploadVoiceoverAudio(long projectId, MultipartFile file, UserContext userContext) {
        // 存在性校验
        long uid = userContext.getUid();
        VideoProject videoProject = videoProjectService.getCheckedVideoProject(projectId, uid);
        Validates.isTrue(videoProject.isEditable(), VideoProjectErrorCode.VIDEO_PROJECT_EDIT_NOT_ALLOWED);

        // 上传文件
        InputStreamFile audioFile = fileProcessors.audio(FileBizSource.VIDEO_PROJECT_VOICEOVER).buildUserFile(file, userContext);
        String ossUrl = userFileService.upload(audioFile);

        // 修改视频音轨（等用户确定要生成视频时，再去提取字幕内容，避免算力浪费）
        AudioTrack audioTrack = new AudioTrack()
                .setType(AudioTrackType.voiceover)
                .setFileId(audioFile.getId())
                .setUrl(ossUrl);
        VideoScript script = new VideoScript()
                // 删除原有的文案和提示词，仅保留商品信息
                .setProduct(videoProject.getScript().getProduct());
        VideoProject updatingVideoProject = new VideoProject()
                .setId(videoProject.getId())
                .setScript(script)
                .setAudioTracks(Seq.seq(videoProject.getAudioTracks()).append(audioTrack).collect(toList()))
                .setDuration(audioFile.getDuration())
                // 视频需要重新编排，因为音频会改变文案
                .setComposeExpired(true);

        // 持久化
        transactionTemplate.executeWithoutResult(status -> {
            // 新增用户文件
            userFileRepository.create(audioFile);
            // 修改视频配音
            videoProjectRepository.updateById(updatingVideoProject);
        });

        return userFileMapper.toUserFileVM(audioFile);
    }

    @Override
    public VideoProjectVM directShots(long id, long uid) {
        // 数据校验
        VideoProject videoProject = videoProjectService.getCheckedVideoProject(id, uid);
        Validates.isTrue(videoProject.isComposable(), VideoProjectErrorCode.VIDEO_SHOTS_DIRECT_NOT_ALLOWED);
        Validates.isTrue(videoProject.getAssets().size() >= 5, VideoProjectErrorCode.VIDEO_ASSETS_SIZE_NOT_ENOUGH, 5);

        // 生成视频分镜列表
        ShotsGenerateRequest request = this.buildShotsGenerateRequest(videoProject);
        List<VideoScriptShot> scriptShots = videoScriptClient.generateScriptShots(request);

        // 校验视频素材数量是否符合预期（暂时不允许出现系统素材）
        if (scriptShots.stream().anyMatch(videoScriptShot -> videoScriptShot.getMediaType() == VideoShotMediaType.sys_asset)) {
            long assetsCount = scriptShots.stream()
                    .filter(scriptShot -> scriptShot.getMediaType() != VideoShotMediaType.avatar)
                    .count();
            throw new BizException(VideoProjectErrorCode.VIDEO_ASSETS_SIZE_NOT_ENOUGH, assetsCount);
        }

        // 更新视频分镜
        Map<String, VideoAsset> userAssetMap = videoProject.getAssets()
                .stream()
                .collect(toMap(VideoAsset::getFileKey, identity()));
        List<InputStreamFile> sysAssetStreamFiles = new ArrayList<>();
        List<VideoShot> shots = scriptShots.stream().map(videoScriptShot -> {
            VideoShot shot = new VideoShot()
                    .setText(videoScriptShot.getText())
                    .setMediaType(videoScriptShot.getMediaType());
            Asserts.notNull(shot.getText(), VideoProjectErrorCode.VIDEO_SHOTS_TEXT_EMPTY);
            switch (shot.getMediaType()) {
                case user_asset -> {
                    // 用户上传的素材
                    VideoAsset asset = userAssetMap.get(videoScriptShot.getAssetUrl());
                    Asserts.notNull(asset, VideoProjectErrorCode.VIDEO_ASSET_NOT_FOUND);
                    shot.setAssetId(asset.getId()).setAssetFile(userAssetRepository.queryById(asset.getId()).getFile());
                }
                case sys_asset -> {
                    // 系统素材（将本地文件上传到 OSS 里去，幂等）
                    String filename = videoScriptShot.getAssetUrl();
                    FileBizSource bizSource = FileBizSource.VIDEO_PROJECT_SYS_ASSET;
                    try {
                        UserContext sysUserContext = UserContext.buildSystem();
                        InputStreamFile streamFile = localFileBuilder.buildStreamFile(filename, bizSource, filename, sysUserContext);
                        // 判断用户文件是否已经持久化
                        UserFile userFile = userFileRepository.queryOptionalByKeyAndUidAndBizSource(streamFile).orElse(streamFile);
                        shot.setAssetId(userFile.getId()).setAssetFile(userAssetMapper.toAssetFile(userFile));
                        if (streamFile == userFile) {
                            // 说明数据库里不存在，则 OSS 物理文件可能不存在，加到上传队里
                            sysAssetStreamFiles.add(streamFile);
                        }
                    } catch (Exception e) {
                        log.error("[directShots]Failed to process sys asset file: {}", filename, e);
                        throw new SysException(VideoProjectErrorCode.VIDEO_SYS_ASSET_PROCESS_FAIL, e);
                    }
                }
            }
            return shot;
        }).collect(toList());

        // 批量上传本地系统素材（幂等）
        sysAssetStreamFiles.forEach(file -> {
            if (!userFileService.isStoragePresent(file)) {
                // 上传的素材文件记录必须持久化到数据库
                userFileService.upload(file);
            }
        });

        // 持久化
        VideoProject updatingVideoProject = new VideoProject()
                .setId(videoProject.getId())
                .setShots(shots)
                .setComposeAt(ZonedDateTime.now());
        transactionTemplate.executeWithoutResult(status -> {
            // 新增系统素材文件
            userFileRepository.batchCreate(new ArrayList<>(sysAssetStreamFiles));
            // 更新视频分镜
            videoProjectRepository.updateById(updatingVideoProject);
        });

        // 返回视频分镜
        return this.getProject(id, uid);
    }

    private ShotsGenerateRequest buildShotsGenerateRequest(VideoProject videoProject) {
        ShotsGenerateRequest request = new ShotsGenerateRequest();

        // 视频文案
        VideoScript script = videoProject.getScript();
        Validates.notNull(script, VideoProjectErrorCode.VIDEO_SCRIPT_NOT_EXISTS);
        if (StringUtils.isNotBlank(script.getText())) {
            request.setText(script.getText());
        } else if (StringUtils.isNotBlank(script.getPrompt())) {
            request.setPrompt(script.getPrompt());
        } else {
            // TODO 音频提取文案，并生成逐字时间轴
            throw new BizException(VideoProjectErrorCode.VIDEO_SCRIPT_TEXT_OR_PROMPT_REQUIRED);
        }

        // 是否启用数字人
        request.setAvatarInsertable(videoProject.getAvatarLayer() != null);

        // 商品信息
        Validates.notNull(script.getProduct(), VideoProjectErrorCode.VIDEO_SCRIPT_PRODUCT_NOT_EXISTS);
        VideoProduct product = script.getProduct();
        Validates.notBlank(product.getTitle(), VideoProjectErrorCode.VIDEO_SCRIPT_PRODUCT_NAME_REQUIRED);
        Validates.notEmpty(product.getCategory().getNames(), VideoProjectErrorCode.VIDEO_SCRIPT_PRODUCT_CATEGORY_REQUIRED);
        Validates.notEmpty(product.getCrowdCategory().getNames(), VideoProjectErrorCode.VIDEO_SCRIPT_CROWD_CATEGORY_REQUIRED);
        request.setProductName(product.getTitle())
                .setProductCategoryDesc(String.join("/", product.getCategory().getNames()))
                .setCrowdCategoryDesc(String.join("/", product.getCrowdCategory().getNames()));

        // 视频素材
        Validates.notEmpty(videoProject.getAssets(), VideoProjectErrorCode.VIDEO_ASSET_NOT_FOUND);
        List<VideoShotAsset> shotAssets = videoProject.getAssets()
                .stream()
                .map(videoAsset -> {
                    String desc = String.join(",", videoAsset.getName());
                    return new VideoShotAsset().setUrl(videoAsset.getUrl()).setDesc(desc);
                })
                .collect(toList());
        return request.setAssets(shotAssets);
    }

    @Override
    public void updateShots(long projectId, VideoShotsSaveRequest request, long uid) {
        // 数据校验
        VideoProject videoProject = videoProjectService.getCheckedVideoProject(projectId, uid);
        Validates.isTrue(videoProject.isComposable(), VideoProjectErrorCode.VIDEO_PROJECT_EDIT_NOT_ALLOWED);

        int shotSize = videoProject.getShots().size();
        Validates.isTrue(shotSize > 0, VideoProjectErrorCode.VIDEO_SHOTS_UPDATE_EMPTY);
        Validates.isTrue(request.getShots().size() == shotSize, VideoProjectErrorCode.VIDEO_SHOTS_UPDATE_SIZE_NOT_MATCH);
        request.getShots().forEach(shot -> {
            switch (shot.getMediaType()) {
                case user_asset, sys_asset -> Validates.notBlank(shot.getAssetId(), VideoProjectErrorCode.VIDEO_SHOTS_ASSET_ID_REQUIRED);
                case avatar -> Asserts.notNull(videoProject.getAvatarLayer(), VideoProjectErrorCode.VIDEO_AVATAR_NOT_EXISTS);
                default -> throw new SysException(CommonErrorCode.UNSPECIFIED);
            }
        });

        // 更新视频分镜
        List<VideoShot> shots = IntStream.range(0, shotSize).mapToObj(i -> {
            VideoShotSaveDTO shotSaveDTO = request.getShots().get(i);
            VideoShot shot = videoProjectMapper.copyVideoShot(videoProject.getShots().get(i));
            if (!Objects.equals(shotSaveDTO.getText(), shot.getText())) {
                // 修改了分镜文案
                shot.setText(shotSaveDTO.getText());
            }
            VideoShotMediaType originalMediaType = shot.getMediaType();
            switch (shotSaveDTO.getMediaType()) {
                case user_asset -> {
                    long assetId = Validates.requireLong(shotSaveDTO.getAssetId(), "assetId");
                    if (!Objects.equals(assetId, shot.getAssetId())) {
                        // 分镜关联的用户素材发生变化
                        VideoAsset videoAsset = videoProject.findAssetById(assetId).orElse(null);
                        Validates.notNull(videoAsset, VideoProjectErrorCode.VIDEO_ASSET_NOT_FOUND);
                        shot.setMediaType(VideoShotMediaType.user_asset)
                                .setAssetId(assetId)
                                .setAssetFile(userAssetRepository.queryById(assetId).getFile());
                    }
                }
                // 保持不变
                case sys_asset -> Validates.isTrue(originalMediaType == VideoShotMediaType.sys_asset,
                        VideoProjectErrorCode.VIDEO_SHOTS_UPDATE_USER_ASSET_TO_SYS_ASSET_NOT_ALLOWED);
                case avatar -> {
                    Asserts.notNull(videoProject.getAvatarLayer(), VideoProjectErrorCode.VIDEO_AVATAR_NOT_EXISTS);
                    shot.setMediaType(VideoShotMediaType.avatar)
                            .setAssetId(null)
                            .setAssetFile(null);
                }
                default -> throw new SysException(CommonErrorCode.UNSPECIFIED);
            }
            return shot;
        }).collect(toList());

        // 持久化
        VideoProject updatingVideoProject = new VideoProject()
                .setId(videoProject.getId())
                .setStage(VideoProjectStage.editing)
                .setShots(shots);
        videoProjectRepository.updateById(updatingVideoProject);
    }

    @Override
    public VideoRenderConsumptionVM estimateRenderConsumption(long projectId, long uid) {
        // 数据校验
        VideoProject videoProject = videoProjectService.getCheckedVideoProject(projectId, uid);
        if (videoProject.getType() == VideoProjectType.product_ads) {
            Validates.notEmpty(videoProject.getShots(), VideoProjectErrorCode.VIDEO_SHOTS_NOT_EXISTS);
        }

        // 获取或估算视频时长
        final Duration duration;
        if (videoProject.getDuration() != null) {
            // 音频输入
            duration = videoProject.getDuration();
        } else {
            // 文案输入
            String text = videoProject.concatAllShotsText();
            Asserts.notBlank(text, VideoProjectErrorCode.VIDEO_SHOTS_TEXT_EMPTY);
            VoiceSynthesizeRequest voiceSynthesizeRequest = videoProjectMapper.toVoiceSynthesizeRequest(text, videoProject.getVoiceover());
            VoiceTimeEstimation estimation = voiceService.estimateTime(voiceSynthesizeRequest, 750);
            duration = estimation.getAudioDuration();
        }

        // 计算消耗的余额 TODO 动态配置
        VideoProjectTaskBizType taskBizType = VideoProjectTaskBizType.product_ads_render;
        int credits = creditsFactory.videoProject().calcRenderCredits(taskBizType, duration);

        // 返回结果
        return new VideoRenderConsumptionVM()
                .setUnpaidCredits(credits)
                .setVideoSeconds(Times.toFormattedSeconds(duration, 2));
    }

    @Override
    public void composeVideos(long id, UserContext userContext) {
        // 数据校验
        long uid = userContext.getUid();
        VideoProject videoProject = videoProjectService.getCheckedVideoProject(id, uid);
        Validates.isTrue(videoProject.isComposable(), VideoProjectErrorCode.VIDEO_PROJECT_COMPOSE_NOT_ALLOWED);
        this.checkVideoProject(videoProject);

        //  幂等保障
        if (videoProject.getStage() == VideoProjectStage.composed) {
            log.warn("[composeVideos]The video project has been composed, projectId: {}", id);
            return;
        }

        // 商品关联素材（幂等）
        videoProjectService.relateAssetsForVideoProduct(videoProject);

        // 构建视频编排任务
        AiTask aiTask = videoProjectTaskBuilder.buildProjectBatchComposeTask(videoProject, userContext);

        // 持久化
        transactionTemplate.executeWithoutResult(status -> {
            // 累加视频编排版本号
            boolean success = videoProjectRepository.incComposeVersion(id, videoProject.getComposeVersion());
            Validates.isTrue(success, VideoProjectErrorCode.VIDEO_PROJECT_RENDER_NOT_ALLOWED);
            // 提交任务
            aiTaskService.submit(aiTask, true);
            // 更新视频项目状态
            VideoProject updatingVideoProject = new VideoProject()
                    .setId(videoProject.getId())
                    .setStage(VideoProjectStage.compose_queuing);
            videoProjectRepository.updateById(updatingVideoProject);
        });
    }

    @Override
    public void renderVideos(long id, VideoProjectRenderRequest request, UserContext userContext) {
        // 数据校验
        long uid = userContext.getUid();
        VideoProject videoProject = videoProjectService.getCheckedVideoProject(id, uid);

        // 幂等判断
        Set<Long> composeTaskIds = request.getComposeTaskIds() == null ? null :
                request.getComposeTaskIds().stream().map(Long::parseLong).collect(toSet());
        if (composeTaskIds != null) {
            Set<Long> composedTaskIds = videoProject.getComposeTasks()
                    .stream()
                    .filter(videoComposeTask -> videoComposeTask.getStage() == VideoComposeStage.composed)
                    .map(VideoComposeTask::getTaskId)
                    .collect(toSet());
            composeTaskIds.retainAll(composedTaskIds);
            Validates.notEmpty(composeTaskIds, "指定的视频编排任务不存在或未完成");
        } else {
            composeTaskIds = videoProject.getComposeTasks()
                    .stream()
                    .filter(videoComposeTask -> videoComposeTask.getStage() == VideoComposeStage.composed)
                    .map(VideoComposeTask::getTaskId)
                    .collect(toSet());
        }
        // 剔除已经渲染的编排任务（幂等保障）
        Set<Long> renderedComposeTaskIds = videoProject.getVideos()
                .stream()
                .map(RenderedVideo::getComposeTaskId)
                .filter(Objects::nonNull)
                .collect(toSet());
        composeTaskIds.removeAll(renderedComposeTaskIds);
        if (composeTaskIds.isEmpty()) {
            log.warn("[renderVideos]All compose tasks have been rendered, projectId: {}", id);
            return;
        }
        this.checkVideoProject(videoProject);

        // 商品关联素材（幂等）
        videoProjectService.relateAssetsForVideoProduct(videoProject);

        // 构建视频渲染任务
        AiTask aiTask = videoProjectTaskBuilder.buildProjectRenderTask(videoProject, composeTaskIds, userContext);

        // 持久化
        transactionTemplate.executeWithoutResult(status -> {
            if (!videoProject.hasComposeTask()) {
                // 当没有编排任务时，需要走全流程编排-渲染，此时需要累加视频编排版本号
                boolean success = videoProjectRepository.incComposeVersion(id, videoProject.getComposeVersion());
                Validates.isTrue(success, VideoProjectErrorCode.VIDEO_PROJECT_RENDER_NOT_ALLOWED);
            }
            // 提交任务
            aiTaskService.submit(aiTask, true);
            // 更新视频项目状态
            VideoProject updatingVideoProject = new VideoProject()
                    .setId(videoProject.getId())
                    .setStage(VideoProjectStage.render_queuing);
            videoProjectRepository.updateById(updatingVideoProject);
        });
    }

    private void checkVideoProject(VideoProject videoProject) {
        Validates.notNull(videoProject.getScript().getProduct(), VideoProjectErrorCode.VIDEO_SCRIPT_PRODUCT_NOT_EXISTS);
        Validates.notEmpty(videoProject.getAssets(), VideoProjectErrorCode.VIDEO_ASSET_NOT_FOUND);
        if (videoProject.getType() == VideoProjectType.product_ads) {
            Validates.notNull(videoProject.getScript().getExpectedDuration(), "视频期望时长不能为空");
            Validates.notBlank(videoProject.getScript().getText(), "视频文案不能为空");
            Validates.notEmpty(videoProject.getShots(), VideoProjectErrorCode.VIDEO_SHOTS_NOT_EXISTS);
            Validates.notNull(videoProject.getVoiceover(), VideoProjectErrorCode.VIDEO_VOICEOVER_NOT_EXISTS);
            Validates.isTrue(videoProject.getAssets().size() >= 5, VideoProjectErrorCode.VIDEO_ASSETS_SIZE_NOT_ENOUGH, 5);
        }
        if (videoProject.getType() == VideoProjectType.drama_ads) {
            Validates.isTrue(videoProject.getAssets().size() >= 3, VideoProjectErrorCode.VIDEO_ASSETS_SIZE_NOT_ENOUGH, 3);
        }
        if (videoProject.getAvatarLayer() != null) {
            // 存在数字人时，必须要有背景图
            Validates.notNull(videoProject.getBackgroundLayer(), VideoProjectErrorCode.VIDEO_BACKGROUND_NOT_EXISTS);
        }
        boolean hasImagAssets = videoProject.getAssets().stream().anyMatch(videoAsset -> videoAsset.getFileType() == FileType.IMAGE);
        boolean hasVideoAssets = videoProject.getAssets().stream().anyMatch(videoAsset -> videoAsset.getFileType() == FileType.VIDEO);
        if (!hasImagAssets && hasVideoAssets) {
            Duration totalDuration = videoProject.getAssets().stream()
                    .filter(videoAsset -> videoAsset.getFileType() == FileType.VIDEO)
                    .map(VideoAsset::getDuration)
                    .peek(Objects::requireNonNull)
                    .reduce(Duration::plus)
                    .orElse(Duration.ZERO);
            int minSecs = VideoProjectConstants.VIDEO_ASSETS_MIN_TOTAL_SECONDS;
            Validates.isTrue(totalDuration.compareTo(Duration.ofSeconds(minSecs)) >= 0, "视频素材总时长不得低于{}秒", minSecs);
        }
    }
}
