/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.deprecated.menu;

import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.deprecated.menu.mapper.MenuMapper;
import ai.creatly.sky.creation.domain.deprecated.menu.model.Menu;
import ai.creatly.sky.creation.domain.deprecated.menu.model.MenuItem;
import ai.creatly.sky.creation.domain.deprecated.menu.model.MenuItemBeta;
import ai.creatly.sky.creation.domain.deprecated.menu.model.response.MenuVM;
import ai.creatly.sky.creation.domain.deprecated.menu.repository.MenuItemRepository;
import ai.creatly.sky.creation.domain.deprecated.menu.repository.MenuRepository;
import ai.creatly.sky.creation.domain.support.config.repository.SystemPreferenceRepository;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version MenuServiceImpl.java, v 0.1 2023-06-13 23:21 joton
 */
@Service
@RequiredArgsConstructor
public class MenuServiceImpl implements MenuService {

    private final MenuRepository             menuRepository;
    private final MenuItemRepository         menuItemRepository;
    private final MenuMapper                 menuMapper;
    private final SystemPreferenceRepository systemPreferenceRepository;

    //白名单配置系统配置key
    public static final String BETA_MENU_ACCESS_CONFIG = "menuAccessConfig";


    @Override
    public MenuVM queryMenu(String code, UserContext userContext) {
        Menu menu = menuRepository.queryValidByCode(code)
                .orElseThrow(() -> new BizException(CommonErrorCode.RECORD_NOT_FOUND, "菜单不存在"));

        String uid = String.valueOf(userContext.getouid());
        List<MenuItem> menuItems = menuItemRepository.queryByMenuId(menu.getId())
                .stream()
                .filter(e -> isBetaOpenForUser(e, uid))
                .collect(Collectors.toList());
        //增加菜单黑名单数据过滤
        menu.setItems(menuItems);
        return menuMapper.toMenuVM(menu);
    }

    public boolean isBetaOpenForUser(MenuItem item, String uid) {
        if (StringUtils.equalsAnyIgnoreCase(MenuItem.FORMAL, item.getTag())) {
            return true;
        }

        // beta
        String inputString = systemPreferenceRepository.getSystemPreferences(BETA_MENU_ACCESS_CONFIG);
        if (inputString == null || inputString.isEmpty()) {
            return true;
        }
        try {
            List<MenuItemBeta> menuItemBetas = JSON.parseList(inputString, MenuItemBeta.class);
            List<String> whiteList = menuItemBetas.stream()
                    .filter(e -> StringUtils.equalsAnyIgnoreCase(e.getCode(), item.getCode()))
                    .findFirst()
                    .map(MenuItemBeta::getUidList)
                    .orElse(Collections.emptyList());
            // 将字符串分割为数字数组，然后使用 Stream 进行判断
            return whiteList.contains(uid);
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
