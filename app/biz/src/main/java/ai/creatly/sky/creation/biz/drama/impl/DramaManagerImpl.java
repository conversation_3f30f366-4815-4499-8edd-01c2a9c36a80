/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.drama.impl;

import ai.creatly.sky.creation.biz.drama.DramaManager;
import ai.creatly.sky.creation.biz.userfile.UserFileCommonManager;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.drama.mapper.DramaVMMapper;
import ai.creatly.sky.creation.domain.core.drama.model.*;
import ai.creatly.sky.creation.domain.core.drama.model.enums.*;
import ai.creatly.sky.creation.domain.core.drama.model.request.DramaCreateRequest;
import ai.creatly.sky.creation.domain.core.drama.model.response.DramaVM;
import ai.creatly.sky.creation.domain.core.drama.service.DramaRepository;
import ai.creatly.sky.creation.domain.core.user.model.UserRoleType;
import ai.creatly.sky.creation.domain.core.user.repository.UserRepository;
import ai.creatly.sky.creation.domain.core.userfile.error.FileErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.response.UserFileVM;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.userfile.service.VideoFileService;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalBizType;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalTask;
import ai.creatly.sky.creation.domain.support.approval.service.ApprovalService;
import ai.creatly.sky.creation.domain.support.category.model.CategoryPath;
import ai.creatly.sky.creation.domain.support.category.model.enums.CategoryDomain;
import ai.creatly.sky.creation.domain.support.category.model.request.CategoryPathDTO;
import ai.creatly.sky.creation.domain.support.category.service.CategoryService;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import ai.creatly.sky.creation.domain.support.notification.app.AppNotifyHelper;
import com.jspeeder.core.data.enums.Option;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.util.Validates;
import com.jspeeder.core.util.json.JSON;
import com.jspeeder.core.util.time.Dates;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.time.Duration;
import java.util.*;

import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version DramaManagerImpl.java, v0.1 2024-10-18 下午9:47 zhoudong
 */
@Service
@RequiredArgsConstructor
public class DramaManagerImpl implements DramaManager {

    private final UserFileRepository    userFileRepository;
    private final DramaRepository       dramaRepository;
    private final DramaVMMapper         dramaVMMapper;
    private final CategoryService       categoryService;
    private final VideoFileService      videoFileService;
    private final AppNotifyHelper       appNotifyHelper;
    private final TransactionTemplate   transactionTemplate;
    private final ApprovalService       approvalService;
    private final UserRepository        userRepository;
    private final UserFileCommonManager userFileCommonManager;

    @Override
    public List<Option> getDramaTypes() {
        return Arrays.stream(DramaType.values()).map(e -> Option.of(e.getCode(), e.getDesc())).toList();
    }

    @Override
    public UserFileVM uploadDramaVideo(MultipartFile file, UserContext userContext) {
        // 校验
        long uid = userContext.getUid();
        boolean isIpCreator = userRepository.queryById(uid).existsRole(UserRoleType.ip_creator);
        Validates.isTrue(isIpCreator, "您不是创作者，无法上传剧情视频，请先申请成为创作者");

        // 上传文件并持久化
        return userFileCommonManager.uploadAndCreate(file, userContext, FileType.VIDEO, FileBizSource.DRAMA_VIDEO);
    }

    @Override
    public DramaVM createDrama(DramaCreateRequest request, UserContext userContext) {
        long uid = userContext.getUid();

        // 校验
        boolean isIpCreator = userRepository.queryById(uid).existsRole(UserRoleType.ip_creator);
        Validates.isTrue(isIpCreator, "您不是创作者，无法上传剧情视频，请先申请成为创作者");

        // 剧情文件校验
        List<Long> fileIds = request.getVideoFileIds().stream().map(Long::parseLong).toList();
        List<UserFile> files = userFileRepository.queryByIds(new HashSet<>(fileIds));
        files.forEach(userFile -> {
            Validates.isTrue(userFile.getUid() == uid, "您不是该视频的拥有者");
            Validates.isTrue(userFile.getBizSource() == FileBizSource.DRAMA_VIDEO, "该视频不是剧情视频");
        });

        // 剧情内容重复性校验
        String contendMd5 = files.stream().map(UserFile::getMd5).collect(joining(DramaConstants.CONTENT_MD5_DELIMITER));
        boolean exists = dramaRepository.querySyncedByContentMd5(contendMd5).isPresent();
        Validates.isTrue(!exists, "该剧情内容已存在，请勿重复上传");

        // 构建剧情
        Drama drama = new Drama()
                .setUid(uid)
                .setStatus(DramaStatus.pending_review)
                .setName(Dates.currentDateTimeString())
                .setVideos(new ArrayList<>())
                .setRoles(Collections.emptyList())
                .setContent(new DramaContent().setMd5(contendMd5))
                .setStats(new DramaStats().setUsedCount(0))
                .setReview(new DramaReview())
                .setAnnotation(new DramaAnnotation())
                .setLocalSyncStatus(DramaLocalSyncStatus.out_of_sync);
        this.fillDrama(request, drama);

        // 持久化
        long dramaId = Objects.requireNonNull(transactionTemplate.execute(status -> {
            long id = dramaRepository.create(drama);
            long taskId = this.startReview(id, userContext);
            dramaRepository.action(id, DramaAction.start_review, drama.getReview().setTaskId(taskId));
            return id;
        }));

        DramaVM dramaVM = this.getDrama(dramaId, uid);
        appNotifyHelper.info("有人提交了版权作品，请审核！\nphone: {}\n{}", userContext.getPhone(), JSON.toPrettyJSONString(dramaVM));
        return dramaVM;
    }

    private long startReview(long dramaId, UserContext userContext) {
        // 发起一个审批任务
        ApprovalTask task = new ApprovalTask()
                .setBizType(ApprovalBizType.drama_review)
                .setBizNo(String.valueOf(dramaId))
                .setSubBizNo(StringUtils.EMPTY)
                .setApplicantId(userContext.getouid())
                .setApplicantName(userContext.getCurrentUsername())
                .setCreator(userContext.toOperatorRef());
        return approvalService.submit(task);
    }

    @Override
    public DramaVM getDrama(long id, long uid) {
        Drama drama = dramaRepository.queryById(id);
        return dramaVMMapper.toDramaVM(drama);
    }

    private void fillDrama(DramaCreateRequest request, Drama drama) {
        if (request.getName() != null) {
            drama.setName(request.getName());
        }

        if (request.getType() != null) {
            drama.setType(request.getType());
        }

        if (request.getProductCategories() != null) {
            List<CategoryPath> categories = request.getProductCategories()
                    .stream()
                    .map(CategoryPathDTO::getCodes)
                    .map(codes -> categoryService.toCategoryPath(CategoryDomain.video_product, codes))
                    .collect(toList());
            drama.setProductCategories(categories);
        }

        if (request.getCrowdCategories() != null) {
            List<CategoryPath> categories = request.getCrowdCategories()
                    .stream()
                    .map(CategoryPathDTO::getCodes)
                    .map(codes -> categoryService.toCategoryPath(CategoryDomain.product_crowd, codes))
                    .collect(toList());
            drama.setCrowdCategories(categories);
        }

        if (request.getVideoFileIds() != null && !request.getVideoFileIds().isEmpty()) {
            List<Long> fileIds = request.getVideoFileIds().stream().map(id -> Validates.requireLong(id, "videoFileId")).toList();
            List<UserFile> videoFiles = userFileRepository.queryByIds(new HashSet<>(fileIds));
            Validates.isTrue(videoFiles.size() == fileIds.size(), FileErrorCode.FILE_NOT_EXISTS);
            List<DramaVideo> dramaVideos = videoFiles.stream()
                    .map(videoFile -> new DramaVideo()
                            .setFileId(videoFile.getId())
                            .setUrl(UserFileHelper.toOssUrl(videoFile))
                            // 创作者直接上传已经裁剪好的视频
                            .setType(DramaVideoType.original_split)
                            .setDuration(videoFile.getDuration())
                            .setHeight(videoFile.getMetadata().getHeight())
                            .setWidth(videoFile.getMetadata().getWidth())
                    )
                    .collect(toList());
            dramaVideos.getFirst().setEndText(request.getEndText());
            drama.setVideos(dramaVideos);
            // 截取封面图
            String coverUrl = videoFileService.clipCoverImageUrl(videoFiles.getFirst(), 1);
            drama.setCoverUrl(coverUrl);
            // 剧情总时长
            drama.setDuration(dramaVideos.stream().reduce(Duration.ZERO, (d, v) -> d.plus(v.getDuration()), Duration::plus));
        }
    }

    @Override
    public Page<DramaVM> queryPage(long uid, Pageable pageable) {
        return dramaRepository.queryPageByUidAndStatus(uid, DramaStatus.notDeleted(), pageable).map(dramaVMMapper::toDramaVM);
    }
}
