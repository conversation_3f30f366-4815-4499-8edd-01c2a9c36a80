/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.userasset;

import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userasset.model.request.UserAssetUpdateRequest;
import ai.creatly.sky.creation.domain.core.userasset.model.response.UploadUserAssetVM;
import ai.creatly.sky.creation.domain.core.userasset.model.response.UserAssetVM;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version UserAssetManager.java, v 0.1 2024-09-21 下午6:19 zhoudong
 */
public interface UserAssetManager {

    Page<UserAssetVM> queryPage(long uid, Pageable pageable);

    UploadUserAssetVM uploadAsset(MultipartFile file, UserContext userContext);

    List<UploadUserAssetVM> batchUploadAssets(List<MultipartFile> files, UserContext userContext);

    UserAssetVM updateAsset(long id, UserAssetUpdateRequest request, UserContext userContext);

    /**
     * 软删除
     *
     * @param id  素材ID
     * @param uid 用户ID
     */
    void deleteAsset(long id, long uid);
}
