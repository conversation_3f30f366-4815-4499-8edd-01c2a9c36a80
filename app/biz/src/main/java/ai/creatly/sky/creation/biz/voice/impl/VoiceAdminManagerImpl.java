/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.voice.impl;

import ai.creatly.sky.creation.biz.voice.VoiceAdminManager;
import ai.creatly.sky.creation.domain.common.integration.oss.FileAccessOption;
import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.common.integration.oss.OssTemplate;
import ai.creatly.sky.creation.domain.common.integration.oss.OssUtil;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.FileInput;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.voice.mapper.VoiceAdminMapper;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.admin.request.VoiceCreateRequest;
import ai.creatly.sky.creation.domain.core.voice.model.admin.request.VoiceQueryRequest;
import ai.creatly.sky.creation.domain.core.voice.model.admin.request.VoiceUpdateRequest;
import ai.creatly.sky.creation.domain.core.voice.model.admin.response.SelfVoiceCheckResult;
import ai.creatly.sky.creation.domain.core.voice.model.admin.response.VoiceVM;
import ai.creatly.sky.creation.domain.core.voice.repository.VoiceRepository;
import ai.creatly.sky.creation.domain.core.voice.service.VoiceService;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.util.Validates;
import jodd.io.FileNameUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version VoiceAdminManager.java, v 0.1 2023-11-19 上午12:45 zhoudong
 */
@Service
@RequiredArgsConstructor
public class VoiceAdminManagerImpl implements VoiceAdminManager {

    private final VoiceService       voiceService;
    private final VoiceAdminMapper   voiceAdminMapper;
    private final VoiceRepository    voiceRepository;
    private final OssTemplate        ossTemplate;
    private final UserFileRepository userFileRepository;

    @Override
    public VoiceVM createDraftVoice(VoiceCreateRequest request) {
        long voiceId = voiceService.createDraftVoice(request);
        return voiceAdminMapper.toVoiceVM(voiceRepository.queryById(voiceId));
    }

    @Override
    public SelfVoiceCheckResult checkSelfVoiceModel(String voiceCode) {
        return voiceService.checkSelfVoiceModel(voiceCode);
    }

    @Override
    public VoiceVM updateVoice(long id, VoiceUpdateRequest request) {
        Voice existingVoice = voiceRepository.queryOptionalById(id).orElse(null);
        Validates.notNull(existingVoice, "声音不存在");
        String avatarUrl = moveAvatarUrl(request.getAvatar());
        Voice voice = new Voice()
                .setId(id)
                .setEnName(request.getEnName())
                .setCnName(request.getCnName())
                .setGender(request.getGender())
                .setAgeLevel(request.getAgeLevel())
                .setAvatar(avatarUrl);
        if (request.getLocales() != null) {
            Set<Long> fileIds = voiceAdminMapper.getFileIds(request.getLocales());
            Map<Long, UserFile> fileMap = userFileRepository.queryMapByIds(fileIds);
            voice.setLocales(voiceAdminMapper.toVoiceLocales(existingVoice, request.getLocales(), fileMap));
        }
        voiceRepository.updateById(voice);
        return voiceAdminMapper.toVoiceVM(voiceRepository.queryById(id));
    }

    @Override
    public Page<VoiceVM> queryVoicePage(VoiceQueryRequest request, Pageable pageable) {
        return voiceRepository.queryPage(voiceAdminMapper.toVoiceQO(request), pageable)
                .map(voiceAdminMapper::toVoiceVM);
    }

    private String moveAvatarUrl(String url) {
        String bucket = OssUtil.resolveBucket(url);
        String sourceKey = OssUtil.resolveKey(url);
        var targetKey = UserFileHelper.buildFileKey(new UserContext().setSessionUid(AppConstants.SYSTEM_UID), IdHelper.getId(),
                new FileInput()
                        .setBizSource(FileBizSource.VOICE_MARKET)
                        .setType(FileType.IMAGE)
                        .setExtension(FileNameUtil.getExtension(url)));
        ossTemplate.copyWithSameBucket(bucket, sourceKey, targetKey, FileAcl.PUBLIC);
        return ossTemplate.getHttpUrl(bucket, targetKey, FileAccessOption.of(FileAcl.PUBLIC));
    }
}
