/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.voice.impl;

import ai.creatly.sky.creation.biz.voice.VoiceFileManager;
import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.common.integration.oss.OssTemplate;
import ai.creatly.sky.creation.domain.common.integration.oss.OssUtil;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.mapper.UserFileMapper;
import ai.creatly.sky.creation.domain.core.userfile.model.FileInput;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.response.UserFileVM;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceEmotion;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceLocale;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceRole;
import ai.creatly.sky.creation.domain.core.voice.model.admin.request.VoiceUpdatePreviewRequest;
import ai.creatly.sky.creation.domain.core.voice.repository.VoiceRepository;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.util.Validates;
import jodd.io.FileNameUtil;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @version VoiceFileManagerImpl.java, v 0.1 2023-11-17 下午8:07 zhoudong
 */
@Service
@RequiredArgsConstructor
public class VoiceFileManagerImpl implements VoiceFileManager {

    private final UserFileService     userFileService;
    private final UserFileMapper      userFileMapper;
    private final UserFileHelper      userFileHelper;
    private final UserFileRepository  userFileRepository;
    private final VoiceRepository     voiceRepository;
    private final OssTemplate         ossTemplate;
    private final TransactionTemplate transactionTemplate;

    @Override
    public String uploadAvatar(InputStream inputStream, String voiceCode, String originalFilename) {
        var fileInput = new FileInput()
                .setBizSource(FileBizSource.VOICE_TEMP)
                .setType(FileType.IMAGE)
                .setOriginalFilename(originalFilename);
        UserFile userFile = userFileHelper.buildUserFile(UserContext.buildSystem(), IdHelper.getId(), fileInput);
        userFileService.upload(inputStream, userFile);
        UserFileVM userFileVM = userFileMapper.toUserFileVM(userFile);
        return userFileVM.getUrl();
    }

    @Override
    public void updatePreviewAudio(VoiceUpdatePreviewRequest request) {
        transactionTemplate.executeWithoutResult(status -> {
            UserFile userFile = movePreviewVoice(request);
            String ossUrl = OssUtil.toOssUrl(userFile.getBucket(), userFile.getKey());
            if (StringUtil.isNotEmpty(request.getEmotionCode())) {
                VoiceLocale voiceLocale = voiceRepository.queryByUidAndCode(AppConstants.SYSTEM_UID, request.getVoiceCode())
                        .getLocaleByCode(request.getLocaleCode());
                VoiceEmotion emotion = voiceLocale.getEmotions()
                        .stream()
                        .filter(e -> e.getCode().equals(request.getEmotionCode()))
                        .findFirst()
                        .orElse(null);
                Validates.notNull(emotion, "emotion not found");
                emotion.setPreviewAudioId(userFile.getId())
                        .setPreviewAudioUrl(ossUrl);
                voiceRepository.updateVoiceLocaleById(voiceLocale);
            } else if (StringUtil.isNotEmpty(request.getRoleCode())) {
                VoiceLocale voiceLocale = voiceRepository.queryByUidAndCode(AppConstants.SYSTEM_UID, request.getVoiceCode())
                        .getLocaleByCode(request.getLocaleCode());
                VoiceRole role = voiceLocale.getRoles()
                        .stream()
                        .filter(e -> e.getCode().equals(request.getRoleCode()))
                        .findFirst()
                        .orElse(null);
                Validates.notNull(role, "role not found");
                role.setPreviewAudioId(userFile.getId())
                        .setPreviewAudioUrl(ossUrl);
                voiceRepository.updateVoiceLocaleById(voiceLocale);
            } else {
                voiceRepository.updateLocalePreviewAudio(Long.parseLong(request.getVoiceId()), request.getLocaleCode(), userFile.getId(), ossUrl);
            }
        });
    }

    private UserFile movePreviewVoice(VoiceUpdatePreviewRequest request) {
        String bucket = OssUtil.resolveBucket(request.getPreviewAudioUrl());
        String sourceKey = OssUtil.resolveKey(request.getPreviewAudioUrl());
        var fileInput = new FileInput()
                .setBizSource(FileBizSource.VOICE_MARKET)
                .setType(FileType.AUDIO)
                .setOriginalFilename("")
                .setExtension(FileNameUtil.getExtension(request.getPreviewAudioUrl()));
        var targetKey = UserFileHelper.buildFileKey(new UserContext().setSessionUid(AppConstants.SYSTEM_UID), IdHelper.getId(),
                fileInput);
        fileInput.setFileKey(targetKey);
        ossTemplate.copyWithSameBucket(bucket, sourceKey, targetKey, FileAcl.PUBLIC);
        UserFile userFile = userFileHelper.buildUserFile(UserContext.buildSystem(), IdHelper.getId(), fileInput);
        userFileRepository.create(userFile);
        return userFile;
    }
}
