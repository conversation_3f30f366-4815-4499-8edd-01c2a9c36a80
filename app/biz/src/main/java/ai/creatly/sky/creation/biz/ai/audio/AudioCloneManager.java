/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.ai.audio;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.ai.audio.AiAudioCloneClient;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiAudioCloneVM;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiAudioTaskResult;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioCloneRequest;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioCloneTaskInput;
import ai.creatly.sky.creation.domain.core.ai.audio.repository.AiAudioCloneRepository;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskBizType;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskService;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version AudioCloneManager.java, v0.1 2025-02-19 20:45
 */
@Service
@RequiredArgsConstructor
public class AudioCloneManager {

    private final AiTaskService           aiTaskService;
    private final UserCreditDomainService userCreditDomainService;
    private final AiAudioCloneClient      aiAudioCloneClient;
    private final TransactionTemplate     transactionTemplate;
    private final AiAudioCloneRepository  aiAudioCloneRepository;
    private final AiTaskRepository        aiTaskRepository;
    private final UserFileHelper          userFileHelper;


    public Page<AiAudioCloneVM> queryGenerationPage(long uid, Pageable pageable) {
        return aiAudioCloneRepository.queryPages(uid, pageable);
    }

    public long submit(UserContext userContext, AudioCloneRequest request){

        AudioCloneTaskInput taskInput = new AudioCloneTaskInput();
        taskInput.setRequest(request);

        AiTask aiTask = AiTask.buildNew()
                .owner(userContext)
                .taskType(AiTaskType.AI_AUDIO)
                .bizType(AiTaskBizType.audio_clone)
                .bizNo(IdHelper.getId())
                .subBizNo(StringUtils.EMPTY)
                .bizInput(taskInput)
                .estimatedDuration(Duration.ofMinutes(3))
                .timeoutFromStartedAt(Duration.ofMinutes(3))
                .build();

        return transactionTemplate.execute(status -> {
            // 元气校验
            CreditsExpense expense = aiAudioCloneClient.expense(aiTask);
            boolean isEnough = userCreditDomainService.isCreditsEnough(userContext.getUid(), expense.getAmount(), CreditAccountType.GENERAL);
            if (!isEnough) {
                throw new BizException(UserErrorCode.USER_CREDIT_NOT_ENOUGH);
            } else {
                userCreditDomainService.consumeCredits(expense);
            }
            return aiTaskService.submit(aiTask, true);
        });
     }

    public AiAudioCloneVM queryGenerationByTaskId(String taskId){
        AiTask aiTask = aiTaskRepository.queryById(Long.parseLong(taskId));
        var taskInput = aiTask.parseBizInput(AudioCloneTaskInput.class);
        var taskResult = aiTask.parseBizResult(AiAudioTaskResult.class);

        AiAudioCloneVM aiAudioCloneVM = new AiAudioCloneVM();
        if (taskInput.getRequest().getAudioFile() != null && taskInput.getRequest().getAudioFile().getUrl().startsWith("oss")) {
            aiAudioCloneVM.setAudioUrl(userFileHelper.getHttpUrl(taskInput.getRequest().getAudioFile().getUrl(), FileAcl.PRIVATE));
        }
        if ((taskInput.getRequest().getPhotoFile() != null && taskInput.getRequest().getPhotoFile().getUrl().startsWith("oss"))) {
            aiAudioCloneVM.setAvatar(userFileHelper.getHttpUrl(taskInput.getRequest().getPhotoFile().getUrl(), FileAcl.PRIVATE));
        }
        aiAudioCloneVM.setCnName(taskInput.getRequest().getAudioName());

        if (taskResult.getAssets() != null && !taskResult.getAssets().isEmpty()) {
            aiAudioCloneVM.setCode(taskResult.getAssets().getFirst() != null ? taskResult.getAssets().getFirst().getId() : null);
        }
        aiAudioCloneVM.setStatus(aiTask.getStatus());
        aiAudioCloneVM.setTaskId(aiTask.getId().toString());
        if (aiTask.getBizResult()!=null && aiTask.getBizResult().has("errorMsg")) {
            aiAudioCloneVM.setErrorMsg(aiTask.getBizResult().getString("errorMsg"));
        }
        return aiAudioCloneVM;
    }
}
