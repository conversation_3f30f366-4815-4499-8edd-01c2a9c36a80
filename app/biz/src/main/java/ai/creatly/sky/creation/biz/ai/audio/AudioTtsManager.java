/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.ai.audio;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.ai.audio.AiAudioTtsClient;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiAudioTaskResult;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioTtsRequest;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioTtsTaskInput;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioTtsVM;
import ai.creatly.sky.creation.domain.core.ai.audio.service.AiTtsService;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskBizType;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskQO;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskService;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.model.response.FileRefVM;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.voice.model.response.AliVoiceVM;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * @version AudioCloneManager.java, v0.1 2025-02-19 20:45
 */
@Service
@RequiredArgsConstructor
public class AudioTtsManager {

    private final AiTaskService           aiTaskService;
    private final UserCreditDomainService userCreditDomainService;
    private final AiAudioTtsClient        aiAudioTtsClient;
    private final TransactionTemplate     transactionTemplate;
    private final AiTaskRepository        aiTaskRepository;
    private final UserFileHelper          userFileHelper;
    private final AiTtsService            aiTtsService;

    public long submitGeneration(UserContext userContext, AudioTtsRequest request) {

        AudioTtsTaskInput taskInput = new AudioTtsTaskInput();
        taskInput.setRequest(request);

        AiTask aiTask = AiTask.buildNew()
                .owner(userContext)
                .taskType(AiTaskType.AI_AUDIO)
                .bizType(AiTaskBizType.audio_tts)
                .bizNo(IdHelper.getId())
                .subBizNo(StringUtils.EMPTY)
                .bizInput(taskInput)
                .estimatedDuration(Duration.ofMinutes(3))
                .timeoutFromStartedAt(Duration.ofMinutes(3))
                .build();

         return transactionTemplate.execute(status -> {
            // 元气校验
            CreditsExpense expense = aiAudioTtsClient.expense(aiTask);
            boolean isEnough = userCreditDomainService.isCreditsEnough(userContext.getUid(), expense.getAmount(), CreditAccountType.GENERAL);
            if (!isEnough) {
                throw new BizException(UserErrorCode.USER_CREDIT_NOT_ENOUGH);
            } else {
                userCreditDomainService.consumeCredits(expense);
            }
            return aiTaskService.submit(aiTask, true);
        });
    }

    public Page<AudioTtsVM> queryGenerationPage(long uid, Pageable pageable) {
        AiTaskQO qo = new AiTaskQO()
                .setTaskType(AiTaskType.AI_AUDIO)
                .setBizType(AiTaskBizType.audio_tts)
                .setOwnerId(uid);
        Page<AiTask> page = aiTaskRepository.queryPage(qo, pageable);

        List<AudioTtsVM> vmList = page.getContent().stream()
                .map(aiTask -> {
                    var taskInput = aiTask.parseBizInput(AudioTtsTaskInput.class);
                    var taskResult = aiTask.parseBizResult(AiAudioTaskResult.class);

                    AudioTtsVM audioTtsVM = new AudioTtsVM();
                    FileRefVM audio = new FileRefVM();
                    if (taskResult.getAssets() != null && !taskResult.getAssets().isEmpty()) {
                        audio.setUrl(userFileHelper.getHttpUrl(taskResult.getAssets().get(0).getUrl(), FileAcl.PRIVATE));
                    }
                    audioTtsVM.setAudioFile(audio);
                    audioTtsVM.setAudioName(taskInput.getRequest().getAudioName());
                    audioTtsVM.setStatus(aiTask.getStatus());
                    audioTtsVM.setTaskId(aiTask.getId().toString());
                    if (aiTask.getBizResult()!=null && aiTask.getBizResult().has("errorMsg")) {
                        audioTtsVM.setErrorMsg(aiTask.getBizResult().getString("errorMsg"));
                    }
                    return audioTtsVM;
                })
                .toList();
        return Page.of(vmList, page.current(), page.getTotal());
    }

    public AudioTtsVM queryGenerationByTaskId(String taskId){
        AiTask aiTask = aiTaskRepository.queryById(Long.parseLong(taskId));
        var taskInput = aiTask.parseBizInput(AudioTtsTaskInput.class);
        var taskResult = aiTask.parseBizResult(AiAudioTaskResult.class);

        AudioTtsVM audioTtsVM = new AudioTtsVM();
        FileRefVM audio = new FileRefVM();
        if (taskResult.getAssets() != null && !taskResult.getAssets().isEmpty()) {
            audio.setUrl(userFileHelper.getHttpUrl(taskResult.getAssets().get(0).getUrl(), FileAcl.PRIVATE));
        }
        audioTtsVM.setAudioFile(audio);
        audioTtsVM.setAudioName(taskInput.getRequest().getAudioName());
        audioTtsVM.setStatus(aiTask.getStatus());
        audioTtsVM.setTaskId(aiTask.getId().toString());
        if (aiTask.getBizResult()!=null && aiTask.getBizResult().has("errorMsg")) {
            audioTtsVM.setErrorMsg(aiTask.getBizResult().getString("errorMsg"));
        }
        return audioTtsVM;
    }

    public List<AliVoiceVM> getBasicVoices(){
        return aiTtsService.getBasicVoices();
    }
}
