/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.pay;

import ai.creatly.kylin.trade.sdk.event.MessageTags;
import ai.creatly.kylin.trade.sdk.event.MessageTopics;
import ai.creatly.kylin.trade.sdk.order.event.OrderPaidEvent;
import ai.creatly.sky.creation.domain.common.messaging.consumer.ConsumeAction;
import ai.creatly.sky.creation.domain.common.messaging.consumer.ConsumeMethod;
import ai.creatly.sky.creation.domain.common.messaging.consumer.ConsumerConfig;
import ai.creatly.sky.creation.domain.common.messaging.consumer.MQListener;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.ai.tool.AiRechargeService;
import ai.creatly.sky.creation.domain.core.course.service.CoursePayService;
import ai.creatly.sky.creation.domain.support.trade.OrderBizType;
import ai.creatly.sky.creation.domain.support.trade.TradeConstants;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 付费订单支付结果消息处理
 *
 * <AUTHOR>
 * @version PlanOrderPayResultListener.java, v 0.1 2023-10-16 下午6:29 zhoudong
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OrderPayResultListener implements MQListener<OrderPaidEvent> {

    private final CoursePayService  coursePayService;
    private final AiRechargeService aiRechargeService;

    @Override
    public ConsumerConfig consumerConfig() {
        return ConsumerConfig.builder()
                .consumerGroup(AppConstants.APP_NAME + "%OrderPayResultListener")
                .topic(MessageTopics.TRADE_TOPIC)
                .tags(MessageTags.orderPaid(TradeConstants.TENANT_CODE))
                .consumeMethod(ConsumeMethod.CLUSTERING)
                .build();
    }

    @Override
    public OrderPaidEvent doConvertMessage(String body) {
        return JSON.parseObject(body, OrderPaidEvent.class);
    }

    @Override
    public ConsumeAction onMessage(OrderPaidEvent event) {

        log.info("支付成功通知,orderId:{},orderType:{}", event.getId(), event.getBizType());
        //保证订单状态最终一致性
        if (OrderBizType.COURSE.name().equals(event.getBizType())) {
            coursePayService.payResult(event.getId());
        } else if (OrderBizType.RECHARGE.name().equals(event.getBizType())) {
            aiRechargeService.payResult(String.valueOf(event.getId()));
        }

        return ConsumeAction.CONSUME_OK;
    }
}
