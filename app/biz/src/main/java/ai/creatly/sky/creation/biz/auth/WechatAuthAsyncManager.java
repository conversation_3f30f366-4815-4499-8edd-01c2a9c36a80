/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.auth;

import ai.creatly.sky.creation.biz.common.deferred.DeferredContext;
import ai.creatly.sky.creation.biz.common.deferred.DeferredKey;
import ai.creatly.sky.creation.biz.common.deferred.DeferredResultExpiry;
import ai.creatly.sky.creation.domain.common.util.crypto.JwtUtils;
import ai.creatly.sky.creation.domain.core.auth.event.WechatSignInEvent;
import ai.creatly.sky.creation.domain.core.auth.model.response.WechatSignInVM;
import ai.creatly.sky.creation.domain.core.auth.service.WechatAuthService;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.jspeeder.core.data.result.ApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.async.DeferredResult;

/**
 * <AUTHOR>
 * @version WechatAuthAsyncManager.java, v 0.1 2024-04-16 18:10 syoka
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WechatAuthAsyncManager {

    private final WechatAuthService wechatAuthService;

    private final LoadingCache<DeferredKey, CachedValue> DEFERRED_CACHE = Caffeine.newBuilder()
            .expireAfter(DeferredResultExpiry.INSTANCE)
            .build(this::buildCacheValue);

    private CachedValue buildCacheValue(DeferredKey deferredKey) {
        return new CachedValue(deferredKey.newDeferredResult(DEFERRED_CACHE));
    }

    private record CachedValue(DeferredResult<ApiResult<WechatSignInVM>> deferredResult) {
    }

    public DeferredResult<ApiResult<WechatSignInVM>> getWechatLoginResult(String sid, DeferredContext context) {
        // 校验sid的合法性
        wechatAuthService.checkWechatSignInSid(sid);

        // 从缓存中懒加载异步结果
        DeferredKey deferredKey = DeferredKey.of(sid, context);
        DeferredResult<ApiResult<WechatSignInVM>> deferredResult = DEFERRED_CACHE.get(deferredKey).deferredResult();

        // 如果缓存中有结果，说明微信授权回调优先于本方法调用，则直接使用该结果
        String token = wechatAuthService.getWechatLoginToken(sid);
        if (StringUtils.isNotEmpty(token)) {
            this.onCompleted(sid, token);
        }
        return deferredResult;
    }

    @EventListener(WechatSignInEvent.class)
    public void onApplicationEvent(WechatSignInEvent event) {
        String sid = event.sid();
        // 该token可能是新用户微信登录临时token，也可能是已注册用户的登录token
        String token = event.jwtToken();
        this.onCompleted(sid, token);
    }

    private void onCompleted(String sid, String token) {
        CachedValue cachedValue = DEFERRED_CACHE.getIfPresent(DeferredKey.of(sid));
        if (cachedValue == null) {
            return;
        }
        WechatSignInVM signInVM = new WechatSignInVM()
                .setIsNew(JwtUtils.isWechatLoginToken(token))
                .setToken(token);
        cachedValue.deferredResult().setResult(ApiResult.ok(signInVM));
        // 主动失效缓存
        DEFERRED_CACHE.invalidate(DeferredKey.of(sid));
    }
}
