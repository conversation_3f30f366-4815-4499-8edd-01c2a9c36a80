/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.deprecated.aivideo;

import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskConstant;
import ai.creatly.sky.creation.domain.core.aitask.model.UpdatableAiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskHelper;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.userfile.model.FileInput;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.deprecated.synthesizer.SynthesizerClient;
import ai.creatly.sky.creation.domain.deprecated.synthesizer.request.SynthesisVideoRequest;
import ai.creatly.sky.creation.domain.deprecated.synthesizer.response.SynthesisVideoBaseResponse;
import ai.creatly.sky.creation.domain.deprecated.synthesizer.response.SynthesisVideoProgressResponse;
import ai.creatly.sky.creation.domain.deprecated.synthesizer.task.param.AISynthesizerVideoParam;
import ai.creatly.sky.creation.domain.deprecated.synthesizer.task.result.AISynthesisVideoResult;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.util.json.JSON;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;

import java.util.Objects;

import static ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource.AI_SYNTHESIS_VIDEO;

/**
 * 合成器合成视频处理器
 *
 * <AUTHOR>
 * @version SynthesisMakingVideoHandler.java, v 0.1 2023-10-07 10:28 syoka
 */
@Deprecated
@Slf4j
//@Component
@RequiredArgsConstructor
public class SynthesisVideoHandler implements AiTaskHandler {

    private static final String FINISH = "FINISH";

    private final UserCreditDomainService userCreditDomainService;
    private final UserFileRepository      userFileRepository;
    private final UserFileHelper          userFileHelper;
    private final AiTaskHelper            aiTaskHelper;
    private final SynthesizerClient       synthesizerClient;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.COLLABORATION_CREATION)
                .setBizType("VIDEO_SYNTHESIS")
                .setLoadSize(10);
    }

    @Override
    public TaskPreAction preHandle(AiTask aiTask) {
        AISynthesizerVideoParam param = aiTask.parseBizParams(AISynthesizerVideoParam.class);
        AISynthesisVideoResult result = aiTask.parseBizResult(AISynthesisVideoResult.class);

        String buildId = IdHelper.getStrId();
        // 准备自定义参数
        UserFile userFile = this.buildUserFile(aiTaskHelper.getOwnerUserContext(aiTask), Long.valueOf(buildId));
        JSONObject jsonObject = prepareCustomizeState(userFile);

        SynthesisVideoRequest request = new SynthesisVideoRequest()
                .setProjectId(param.getWorkspaceId())
                .setBuildId(buildId)
                .setState(jsonObject.toString())
                // 设置合成视频，请求模型
                .setVideoData(JSON.parseMap(param.getConfig(), Object.class));

        SynthesisVideoBaseResponse<Void> synthesisResponse = synthesizerClient.synthesisVideo(request);

        if (Objects.isNull(synthesisResponse) || !synthesisResponse.isSuccess()) {
            log.warn("[SynthesisVideoHandler] preHandler failure.taskId:{},result:{}", aiTask.getId(), synthesisResponse);
            JSONObject bizExecInfo = new JSONObject();
            bizExecInfo.put(AiTaskConstant.BIZ_ERROR_MSG, Objects.isNull(synthesisResponse) ? "synthesisResponse is empty" : synthesisResponse.getErrorMsg());
            UpdatableAiTask updatableAiTask = new UpdatableAiTask()
                    .setBizExecInfo(bizExecInfo)
                    .setBizStatus(AiTaskConstant.BIZ_STATUS_FAILURE);
            String cancelReason = FormatUtil.format("合成视频模型服务异常,{}", synthesisResponse);
            return TaskPreAction.FORWARD_CANCELED.withReason(cancelReason, updatableAiTask);
        }


        result.setBuildId(buildId);
        result.setVideoFileId(buildId);
        // 获取oss objectKey路径
        result.setOssUri(UserFileHelper.toOssUrl(userFile));
        UpdatableAiTask updatableAiTask = new UpdatableAiTask()
                .setBizResult(result)
                .setBizNo(buildId);
        return TaskPreAction.FORWARD_RUNNING.withAiTask(updatableAiTask);
    }

    @Override
    public TaskAction handle(AiTask aiTask) {
        AISynthesizerVideoParam param = aiTask.parseBizParams(AISynthesizerVideoParam.class);
        AISynthesisVideoResult result = aiTask.parseBizResult(AISynthesisVideoResult.class);

        SynthesisVideoBaseResponse<SynthesisVideoProgressResponse> synthesisResponse = synthesizerClient.getSynthesisVideoProgress(param.getWorkspaceId(), aiTask.getBizNo());
        if (Objects.isNull(synthesisResponse) || !synthesisResponse.isSuccess()) {
            JSONObject bizExecInfo = new JSONObject();
            bizExecInfo.put(AiTaskConstant.BIZ_ERROR_MSG, Objects.isNull(synthesisResponse) ? "synthesisResponse is empty" : synthesisResponse.getErrorMsg());
            UpdatableAiTask updatableAiTask = new UpdatableAiTask()
                    .setBizExecInfo(bizExecInfo)
                    .setBizStatus(AiTaskConstant.BIZ_STATUS_FAILURE);

            return TaskAction.FORWARD_CANCELED.withAiTask(updatableAiTask);
        }

        SynthesisVideoProgressResponse progressResponse = synthesisResponse.getData();
        result.setProgress(progressResponse.getProgress());
        result.setSubStage(progressResponse.getSubStage());

        UpdatableAiTask updatableAiTask = new UpdatableAiTask()
                .setBizResult(result);
        if (StringUtils.equals(FINISH, progressResponse.getStatus())) {
            updatableAiTask.setBizStatus(AiTaskConstant.BIZ_STATUS_SUCCESS);
            // 异步任务模型拿不到用户信息，只能拼凑
            UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);
            // 记录用户文件
            FileInput fileInput = new FileInput()
                    .setType(FileType.VIDEO)
                    .setBizSource(AI_SYNTHESIS_VIDEO)
                    .setOriginalFilename(result.getVideoFileId() + ".mp4");
            UserFile userFile = userFileHelper.buildUserFile(userContext, Long.parseLong(result.getVideoFileId()), fileInput);
            userFileRepository.create(userFile);
            return TaskAction.FORWARD_FINISHED.withAiTask(updatableAiTask);
        }

        updatableAiTask.setBizStatus(AiTaskConstant.BIZ_STATUS_PROCESSING);
        return TaskAction.KEEP_STILL.withAiTask(updatableAiTask);
    }

    @Override
    public TaskPostAction postHandle(AiTask aiTask) {
        if (AiTaskStatus.CANCELED == aiTask.getStatus()) {
            // 金额回退逻辑
            userCreditDomainService.refundCredits(aiTask.getOwnerId(), CreditLogBizType.COLLABORATION_CREATION, String.valueOf(aiTask.getId()));
        }
        return TaskPostAction.COMPLETE;
    }

    private UserFile buildUserFile(UserContext userContext, Long fileId) {
        FileInput fileInput = new FileInput()
                .setType(FileType.VIDEO)
                .setBizSource(FileBizSource.AI_SYNTHESIS_VIDEO)
                .setExtension("mp4");
        return userFileHelper.buildUserFile(userContext, fileId, fileInput);
    }

    private JSONObject prepareCustomizeState(UserFile userFile) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("fileId", userFile.getId());
        jsonObject.put("key", userFile.getKey());
        jsonObject.put("bucket", userFile.getBucket());
        return jsonObject;
    }

}
