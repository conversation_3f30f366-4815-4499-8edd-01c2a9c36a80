/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.videoproject;

import ai.creatly.sky.creation.domain.core.videoproject.model.request.VideoAssetsSaveRequest;

/**
 * 视频素材管理
 *
 * <AUTHOR>
 * @version VideoAssetManager.java, v 0.1 2024-09-21 下午5:00 zhoudong
 */
public interface VideoAssetManager {

    void saveVideoAssets(long projectId, VideoAssetsSaveRequest request, long uid);
}
