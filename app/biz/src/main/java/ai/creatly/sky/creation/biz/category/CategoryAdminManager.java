/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.category;

import ai.creatly.sky.creation.domain.support.category.model.Category;
import ai.creatly.sky.creation.domain.support.category.model.admin.mapper.CategoryVMAdminMapper;
import ai.creatly.sky.creation.domain.support.category.model.admin.model.CategoryVM;
import ai.creatly.sky.creation.domain.support.category.model.enums.CategoryDomain;
import ai.creatly.sky.creation.domain.support.category.service.CategoryRepository;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @version CategoryAdminManager.java, 2024-12-12 下午5:52 zhoudong
 */
@Service
@RequiredArgsConstructor
public class CategoryAdminManager {

    private final CategoryRepository    categoryRepository;
    private final CategoryVMAdminMapper categoryVMAdminMapper;

    public Page<CategoryVM> getPage(CategoryDomain domain, Pageable pageable) {
        return categoryRepository.queryPage(domain, pageable).map(categoryVMAdminMapper::toCategoryVM);
    }

    public CategoryVM getDetail(long id) {
        Category category = categoryRepository.queryOptionalById(id).orElse(null);
        Validates.notNull(category, "category.not.found");
        return categoryVMAdminMapper.toCategoryVM(category);
    }
}
