/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.common.file.processor;

import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.InputStreamFile;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version FileProcessor.java, v 0.1 2024-09-24 上午11:07 zhoudong
 */
public interface FileProcessor {

    InputStreamFile buildUserFile(MultipartFile file, UserContext userContext);
}
