/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.plan.impl;

import ai.creatly.sky.creation.biz.plan.PlanManager;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.benefit.log.model.BenefitLog;
import ai.creatly.sky.creation.domain.core.benefit.log.model.BenefitSourceType;
import ai.creatly.sky.creation.domain.core.benefit.log.repository.BenefitLogRepository;
import ai.creatly.sky.creation.domain.core.member.model.Member;
import ai.creatly.sky.creation.domain.core.member.respository.MemberRepository;
import ai.creatly.sky.creation.domain.core.plan.error.PlanErrorCode;
import ai.creatly.sky.creation.domain.core.plan.mapper.PlanMapper;
import ai.creatly.sky.creation.domain.core.plan.mapper.PlanOrderMapper;
import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.PlanOrder;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanBuyMethod;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import ai.creatly.sky.creation.domain.core.plan.model.request.EnterpriseMemberConsultRequest;
import ai.creatly.sky.creation.domain.core.plan.model.request.PlanWxNativePayRequest;
import ai.creatly.sky.creation.domain.core.plan.model.response.PlanOrderVM;
import ai.creatly.sky.creation.domain.core.plan.model.response.PlanWxNativePayResultVM;
import ai.creatly.sky.creation.domain.core.plan.model.response.SubscriptionPlanVM;
import ai.creatly.sky.creation.domain.core.plan.model.response.TopUpPlanVM;
import ai.creatly.sky.creation.domain.core.plan.model.specific.payment.PlanWxNativePrepayResult;
import ai.creatly.sky.creation.domain.core.plan.service.PlanOrderService;
import ai.creatly.sky.creation.domain.core.plan.service.PlanService;
import ai.creatly.sky.creation.domain.support.notification.app.AppNotifyHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;

/**
 * 付费计划管理实现
 *
 * <AUTHOR>
 * @version PlanManagerImpl.java, v 0.1 2023-10-10 下午10:04 zhoudong
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlanManagerImpl implements PlanManager {

    private final PlanService          planService;
    private final PlanMapper           planMapper;
    private final PlanOrderMapper      planOrderMapper;
    private final MemberRepository     memberRepository;
    private final PlanOrderService     planOrderService;
    private final BenefitLogRepository benefitLogRepository;
    private final AppNotifyHelper      appNotifyHelper;

    @Override
    public List<SubscriptionPlanVM> getPublicSubscriptionPlans() {
        // 显示全部的，包括免费版
        List<Plan> plans = planService.queryByTypeWithBenefits(PlanType.SUBSCRIPTION);
        return planMapper.toPublicSubscriptionPlanVMs(plans);
    }

    @Override
    public List<SubscriptionPlanVM> getSubscriptionPlans(UserContext userContext) {
        // 查询平台定价+私人定价
        List<Plan> plans = planService.queryByTypeWithBenefits(PlanType.SUBSCRIPTION, userContext.getouid());
        // 用户已经登录后，过滤掉免费版
        plans = plans.stream().filter(plan -> plan.getLevel() > 1).collect(toList());
        // 针对当前会员，显示会员的历史购买价
        Member member = memberRepository.queryActiveByUid(userContext.getouid()).orElse(null);
        return planMapper.toSubscriptionPlanVMs(plans, member);
    }

    @Override
    public List<TopUpPlanVM> getTopUpPlans(UserContext userContext) {
        List<Plan> plans = planService.queryByTypeWithBenefits(PlanType.TOP_UP);

        // 查询当前生效会员关联的订阅计划（如果是会员的话）
        Plan subscriptionPlan = null;
        Member member = memberRepository.queryActiveByUid(userContext.getouid()).orElse(null);
        if (member != null) {
            // 针对会员，显示每个充值档位赠送的 credits
            subscriptionPlan = planService.queryByIdWithBenefits(member.getPlanId()).orElse(null);
        }
        return planMapper.toTopUpPlanVMs(plans, subscriptionPlan);
    }

    @Override
    public PlanWxNativePayResultVM buyPlan(String planId, PlanWxNativePayRequest request, String clientIp, UserContext userContext) {
        // 付费计划校验
        Plan plan = planService.queryByIdWithBenefits(planId)
                .orElseThrow(() -> new BizException(PlanErrorCode.PLAN_NOT_EXISTS));
        Validates.objectEquals(plan.getBuyMethod(), PlanBuyMethod.ONLINE_PAY, PlanErrorCode.PLAN_ONLINE_PAY_UNSUPPORTED);

        // 自动续费：当付款计划上没有配置，需要用户自行选择，如果不选，则不自动续费
        if (plan.getAutoRenewed() == null) {
            plan.setAutoRenewed(Boolean.TRUE.equals(request.getAutoRenewed()));
        }

        // 下单并支付
        PlanWxNativePrepayResult prepayResult = planOrderService.doWxNativePay(plan, clientIp, userContext);
        return new PlanWxNativePayResultVM()
                .setOrderId(prepayResult.getOrderId().toString())
                .setExpireAt(prepayResult.getExpireAt())
                .setCodeUrl(prepayResult.getCodeUrl());
    }

    @Override
    public Page<PlanOrderVM> queryPlanOrderPage(Pageable pageable, UserContext userContext) {
        // 将订阅和充值订单放一起查询（取决于产品 UI 形态）
        List<PlanType> planTypes = List.of(PlanType.SUBSCRIPTION, PlanType.TOP_UP);
        Page<PlanOrder> page = planOrderService.queryPaidPage(userContext.getouid(), planTypes, pageable);

        // 批量查询权益发放记录（订阅和充值都查），权益的有效时间段，以权益发放记录里的为准
        List<String> orderIds = page.getContent().stream().map(PlanOrder::getId).map(Object::toString).toList();
        Map<String, BenefitLog> benefitLogMap = benefitLogRepository.queryBySources(BenefitSourceType.SUBSCRIPTION_PLAN_ORDER, orderIds);
        benefitLogMap.putAll(benefitLogRepository.queryBySources(BenefitSourceType.TOP_UP_PLAN_ORDER, orderIds));

        return page.map(planOrder -> {
            BenefitLog benefitLog = benefitLogMap.get(planOrder.getId().toString());
            return planOrderMapper.toPlanOrderVM(planOrder, benefitLog);
        });
    }

    @Override
    public void consultEnterpriseMember(EnterpriseMemberConsultRequest request, UserContext userContext) {
        String template = "企业询价，用户名：{}，企业名称：{}，联系方式：{}";
        appNotifyHelper.info(template, userContext.getCurrentUsername(), request.getName(), request.getMobile());
    }
}
