/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.user;

import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import ai.creatly.sky.creation.domain.core.user.model.request.UserVerifyType;
import ai.creatly.sky.creation.domain.core.user.model.response.UserProfileVM;

/**
 * <AUTHOR>
 * @version UserManager.java, v 0.1 2023-08-21 01:36 joton
 */
public interface UserManager {

    UserProfileVM getSessionProfile(long uid);
    /**
     * 生成一次性短信验证码
     *
     * @param verifyType 验证类型 EMAIL/PHONE
     * @param value 验证值
     */
    void createOneTimeSignUpVerifyCode(UserVerifyType verifyType, String value);
    void createOneTimeSignInVerifyCode(UserVerifyType verifyType, String value);

    void createOneTimeSignInOrUpVerifyCode(UserVerifyType verifyType, String value);
    void createOneTimeChangePhoneVerifyCode(UserVerifyType verifyType, String value);

    UserInfo saveOrUpdate(UserInfo userInfo);

    UserInfo saveOrUpdateOrgAdmin(UserInfo userInfo);

    UserInfo getUserInfo(long uid);
}
