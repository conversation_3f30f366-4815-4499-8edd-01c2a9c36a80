/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.aiproduct.video.impl;

import ai.creatly.sky.creation.biz.aiproduct.video.ProductVideoManager;
import ai.creatly.sky.creation.biz.common.file.MultipartFileHelper;
import ai.creatly.sky.creation.domain.core.aiproduct.video.error.ProductVideoErrorCode;
import ai.creatly.sky.creation.domain.core.aiproduct.video.mapper.ProductVideoMapper;
import ai.creatly.sky.creation.domain.core.aiproduct.video.mapper.ProductVideoTaskMapper;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.ProductVideo;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.ProductVideoReTalkTask;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.enums.ReTalkTaskStatus;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.request.ProductVideoReTalkRequest;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.response.ProductVideoReTalkTaskVM;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.response.ProductVideoVM;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.task.ProductVideoTaskBizType;
import ai.creatly.sky.creation.domain.core.aiproduct.video.repository.ProductVideoRepository;
import ai.creatly.sky.creation.domain.core.aiproduct.video.service.ProductVideoService;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskHelper;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskService;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsFactory;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.error.FileErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.mapper.UserFileMapper;
import ai.creatly.sky.creation.domain.core.userfile.model.InputStreamFile;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.core.userfile.service.builder.AudioFileBuilder;
import ai.creatly.sky.creation.domain.core.userfile.service.builder.ImageFileBuilder;
import ai.creatly.sky.creation.domain.core.userfile.service.builder.VideoFileBuilder;
import ai.creatly.sky.creation.domain.support.multimedia.model.AudioMetadata;
import ai.creatly.sky.creation.domain.support.multimedia.model.ImageMetadata;
import ai.creatly.sky.creation.domain.support.multimedia.model.VideoMetadata;
import ai.creatly.sky.creation.domain.support.multimedia.resolver.MediaFileResolver;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.Validates;
import com.jspeeder.core.util.file.HttpFileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version ProductVideoManagerImpl.java, v 0.1 2024-05-25 下午5:54 zhoudong
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductVideoManagerImpl implements ProductVideoManager {

    private final UserFileService         userFileService;
    private final UserFileMapper          userFileMapper;
    private final UserFileRepository      userFileRepository;
    private final VideoFileBuilder        videoFileBuilder;
    private final AudioFileBuilder        audioFileBuilder;
    private final ImageFileBuilder        imageFileBuilder;
    private final ProductVideoService     productVideoService;
    private final ProductVideoRepository  productVideoRepository;
    private final ProductVideoMapper      productVideoMapper;
    private final ProductVideoTaskMapper  productVideoTaskMapper;
    private final CreditsFactory          creditsFactory;
    private final AiTaskRepository        aiTaskRepository;
    private final AiTaskService           aiTaskService;
    private final AiTaskHelper            aiTaskHelper;
    private final TransactionTemplate     transactionTemplate;
    private final UserCreditDomainService userCreditDomainService;
    private final MultipartFileHelper     multipartFileHelper;
    private final MediaFileResolver       mediaFileResolver;

    @Override
    public InputStreamFile buildReTalkInputVideo(@Nullable MultipartFile videoFile, @Nullable String videoUrl, UserContext userContext) {
        Validates.isTrue(!(videoFile == null && StringUtils.isBlank(videoUrl)), "请提供视频文件");
        Validates.isTrue(!(videoFile != null && StringUtils.isNotBlank(videoUrl)), "视频文件地址和上传只能二选一");
        final FileBizSource bizSource = FileBizSource.PRODUCT_VIDEO_RE_TALK_INPUT;

        // 通过上传文件的方式
        if (videoFile != null) {
            String originalFilename = videoFile.getOriginalFilename();
            VideoMetadata videoMetadata = multipartFileHelper.resolveVideoMetadata(videoFile, bizSource);
            UserFile inputVideoFile = videoFileBuilder.build(bizSource, videoMetadata, userContext, originalFilename);
            return userFileMapper.toInputStreamFile(inputVideoFile, () -> {
                try {
                    return videoFile.getInputStream();
                } catch (IOException e) {
                    log.warn("[buildReTalkInputVideo]上传视频文件失败", e);
                    throw new BizException(FileErrorCode.FILE_UPLOAD_ERROR, inputVideoFile.getType().getDesc(), e);
                }
            });
        }

        // 通过URL下载的方式
        final String originalFilename = HttpFileUtil.extractFilename(videoUrl);
        try {
            final byte[] bytes = HttpFileUtil.downloadAsBytes(videoUrl);
            VideoMetadata videoMetadata = mediaFileResolver.resolveVideoMetadata(originalFilename, bytes, bizSource);
            UserFile inputVideoFile = videoFileBuilder.build(bizSource, videoMetadata, userContext, originalFilename);
            return userFileMapper.toInputStreamFile(inputVideoFile, () -> new ByteArrayInputStream(bytes));
        } catch (IOException e) {
            log.warn("[buildReTalkInputVideo]下载视频文件失败", e);
            throw new BizException(FileErrorCode.FILE_DOWNLOAD_ERROR, "视频", e);
        }
    }

    @Override
    public InputStreamFile buildReTalkInputAudio(@Nullable MultipartFile audioFile, @Nullable String audioUrl, UserContext userContext) {
        Validates.isTrue(!(audioFile == null && StringUtils.isBlank(audioUrl)), "请提供音频文件");
        Validates.isTrue(!(audioFile != null && StringUtils.isNotBlank(audioUrl)), "音频文件地址和上传只能二选一");
        final FileBizSource bizSource = FileBizSource.PRODUCT_VIDEO_RE_TALK_INPUT;

        // 通过上传文件的方式
        if (audioFile != null) {
            String originalFilename = audioFile.getOriginalFilename();
            AudioMetadata audioMetadata = multipartFileHelper.resolveAudioMetadata(audioFile, bizSource);
            UserFile inputAudioFile = audioFileBuilder.build(bizSource, audioMetadata, userContext, originalFilename);
            return userFileMapper.toInputStreamFile(inputAudioFile, () -> {
                try {
                    return audioFile.getInputStream();
                } catch (IOException e) {
                    log.warn("[buildReTalkInputAudio]上传音频文件失败", e);
                    throw new BizException(FileErrorCode.FILE_UPLOAD_ERROR, inputAudioFile.getType().getDesc(), e);
                }
            });
        }

        // 通过URL下载的方式
        final String originalFilename = HttpFileUtil.extractFilename(audioUrl);
        try {
            final byte[] bytes = HttpFileUtil.downloadAsBytes(audioUrl);
            AudioMetadata audioMetadata = multipartFileHelper.resolveAudioMetadata(originalFilename, bytes, bizSource);
            UserFile inputAudioFile = audioFileBuilder.build(bizSource, audioMetadata, userContext, originalFilename);
            return userFileMapper.toInputStreamFile(inputAudioFile, () -> new ByteArrayInputStream(bytes));
        } catch (IOException e) {
            log.warn("[buildReTalkInputAudio]下载音频文件失败", e);
            throw new BizException(FileErrorCode.FILE_DOWNLOAD_ERROR, "音频", e);
        }
    }

    @Nullable
    @Override
    public InputStreamFile buildReTalkInputFaceImage(@Nullable MultipartFile faceImage, @Nullable String faceImageUrl,
                                                     UserContext userContext) {
        if (faceImage == null && faceImageUrl == null) {
            // 不进行换脸操作
            return null;
        }
        Validates.isTrue(!(faceImage != null && StringUtils.isNotBlank(faceImageUrl)), "换脸图片地址和上传只能二选一");
        final FileBizSource bizSource = FileBizSource.PRODUCT_VIDEO_RE_TALK_INPUT;

        // 通过上传文件的方式
        if (faceImage != null) {
            String originalFilename = faceImage.getOriginalFilename();
            ImageMetadata imageMetadata = multipartFileHelper.resolveImageMetadata(faceImage, bizSource);
            UserFile inputFaceImageFile = imageFileBuilder.build(bizSource, imageMetadata, userContext, originalFilename);
            return userFileMapper.toInputStreamFile(inputFaceImageFile, () -> {
                try {
                    return faceImage.getInputStream();
                } catch (IOException e) {
                    log.warn("[buildReTalkInputFaceImage]上传换脸图片失败", e);
                    throw new BizException(FileErrorCode.FILE_UPLOAD_ERROR, inputFaceImageFile.getType().getDesc(), e);
                }
            });
        }

        // 通过URL下载的方式
        final String originalFilename = HttpFileUtil.extractFilename(faceImageUrl);
        try {
            final byte[] bytes = HttpFileUtil.downloadAsBytes(faceImageUrl);
            ImageMetadata imageMetadata = multipartFileHelper.resolveImageMetadata(originalFilename, bytes, bizSource);
            UserFile inputFaceImageFile = imageFileBuilder.build(bizSource, imageMetadata, userContext, originalFilename);
            return userFileMapper.toInputStreamFile(inputFaceImageFile, () -> new ByteArrayInputStream(bytes));
        } catch (IOException e) {
            log.warn("[buildReTalkInputFaceImage]下载换脸图片失败", e);
            throw new BizException(FileErrorCode.FILE_DOWNLOAD_ERROR, "换脸图片", e);
        }
    }

    @Override
    public ProductVideoReTalkTaskVM submitReTalkTask(ProductVideoReTalkRequest request, UserContext userContext) {
        // 单用户并发最多3个进行中的
        long uid = userContext.getouid();
        long count = aiTaskRepository.countInProgressWithUser(uid, AiTaskType.AI_PRODUCT_VIDEO, ProductVideoTaskBizType.re_talk.getCode());
        Validates.isTrue(count < 3, CommonErrorCode.SLA_LIMITED);

        // 音视频校验
        InputStreamFile audioFile = request.getAudioFile();
        InputStreamFile videoFile = request.getVideoFile();
        Duration audioDuration = Objects.requireNonNull(audioFile.getDuration());
        Duration videoDuration = Objects.requireNonNull(videoFile.getDuration());
        Validates.isTrue(audioDuration.compareTo(videoDuration) <= 0, "音频时长不能超过视频时长，请重新上传");

        // 构建任务
        AiTask aiTask = productVideoTaskMapper.buildTask(request, userContext);

        // 计费校验（根据视频时长计费）
        CreditsExpense expense = creditsFactory.productVideo().calcReTalkExpense(aiTask, videoFile);
        Validates.isTrue(userCreditDomainService.isCreditsEnough(expense), UserErrorCode.USER_CREDIT_NOT_ENOUGH);

        // 上传输入的音频文件、视频文件、换脸图片到OSS
        try (InputStream stream = videoFile.getStream().get()) {
            userFileService.upload(stream, videoFile);
        } catch (Exception e) {
            this.deleteFiles(videoFile, audioFile, request.getFaceImage());
            log.warn("[submitReTalkTask]上传视频文件失败", e);
            throw new BizException(FileErrorCode.FILE_UPLOAD_ERROR, videoFile.getType().getDesc(), e);
        }
        try (InputStream stream = audioFile.getStream().get()) {
            userFileService.upload(stream, audioFile);
        } catch (Exception e) {
            this.deleteFiles(videoFile, audioFile, request.getFaceImage());
            log.warn("[submitReTalkTask]上传音频文件失败", e);
            throw new BizException(FileErrorCode.FILE_UPLOAD_ERROR, audioFile.getType().getDesc(), e);
        }
        @Nullable InputStreamFile faceImage = request.getFaceImage();
        if (request.getFaceImage() != null) {
            try (InputStream stream = faceImage.getStream().get()) {
                userFileService.upload(stream, faceImage);
            } catch (Exception e) {
                this.deleteFiles(videoFile, audioFile, request.getFaceImage());
                log.warn("[submitReTalkTask]上传换脸图片失败", e);
                throw new BizException(FileErrorCode.FILE_UPLOAD_ERROR, faceImage.getType().getDesc(), e);
            }
        }

        // 持久化文件记录和任务，并扣费
        boolean success = Objects.requireNonNull(transactionTemplate.execute(status -> {
            boolean consumed = userCreditDomainService.consumeCredits(expense);
            if (consumed) {
                final List<UserFile> userFiles;
                if (faceImage == null) {
                    userFiles = List.of(audioFile, videoFile);
                } else {
                    userFiles = List.of(audioFile, videoFile, faceImage);
                }
                userFileRepository.batchCreate(userFiles);
                aiTaskService.submit(aiTask);
            }
            return consumed;
        }));
        if (!success) {
            // 扣费失败，删除上传的文件（异步静默）
            this.deleteFiles(videoFile, audioFile, request.getFaceImage());
        }
        Validates.isTrue(success, UserErrorCode.USER_CREDIT_NOT_ENOUGH);

        // 立刻触发任务执行（异步执行）
        aiTaskHelper.asyncExecute(aiTask.getId());
        return this.queryReTalkTask(aiTask.getId(), userContext.getouid());
    }

    private void deleteFiles(UserFile videoFile, UserFile audioFile, @Nullable UserFile faceImage) {
        userFileService.asyncDeleteStorageSilently(videoFile);
        userFileService.asyncDeleteStorageSilently(audioFile);
        if (faceImage != null) {
            userFileService.asyncDeleteStorageSilently(faceImage);
        }
    }

    @Override
    public ProductVideoReTalkTaskVM queryReTalkTask(long taskId, long uid) {
        ProductVideoReTalkTask reTalkTask = productVideoService.queryReTalkTask(taskId, uid).orElse(null);
        Validates.notNull(reTalkTask, ProductVideoErrorCode.PRODUCT_VIDEO_RE_TALK_TASK_NOT_FOUND);
        return productVideoTaskMapper.toReTalkTaskVM(reTalkTask, uid);
    }

    @Override
    public Page<ProductVideoReTalkTaskVM> queryReTalkTaskPage(List<ReTalkTaskStatus> statusList, long uid, Pageable pageable) {
        return productVideoService.queryReTalkTaskPage(uid, statusList, pageable)
                .map(reTalkTask -> productVideoTaskMapper.toReTalkTaskVM(reTalkTask, uid));
    }

    @Override
    public ProductVideoVM queryProductVideo(long id, long uid) {
        ProductVideo productVideo = productVideoRepository.queryOptionalById(id).orElse(null);
        Validates.notNull(productVideo, ProductVideoErrorCode.PRODUCT_VIDEO_NOT_FOUND);
        Validates.isTrue(productVideo.belongsTo(uid), ProductVideoErrorCode.PRODUCT_VIDEO_NOT_FOUND);
        return productVideoMapper.toProductVideoVM(productVideo);
    }

    @Override
    public Page<ProductVideoVM> queryProductVideoPage(long uid, Pageable pageable) {
        return productVideoRepository.queryPage(uid, pageable).map(productVideoMapper::toProductVideoVM);
    }
}
