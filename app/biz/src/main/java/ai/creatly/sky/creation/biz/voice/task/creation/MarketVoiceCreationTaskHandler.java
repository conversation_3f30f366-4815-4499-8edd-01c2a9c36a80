/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.voice.task.creation;

import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.FileInput;
import ai.creatly.sky.creation.domain.core.userfile.model.FileOutput;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceTimeEstimation;
import ai.creatly.sky.creation.domain.core.voice.model.task.VoiceTaskBizType;
import ai.creatly.sky.creation.domain.core.voice.model.task.exec.BasicVoiceSynthesisResult;
import ai.creatly.sky.creation.domain.core.voice.model.task.param.VoiceCreationParams;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.jspeeder.core.data.id.IdHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;

/**
 * 声音演员的语音创作任务处理器
 *
 * <AUTHOR>
 * @version MarketVoiceCreationTaskHandler.java, v 0.1 2023-12-09 下午11:11 zhoudong
 */
@Slf4j
//@Component
@RequiredArgsConstructor
public class MarketVoiceCreationTaskHandler extends AbstractMarketVoiceCreationTaskHandler implements AiTaskHandler {

    private final BasicVoiceCreationHandler basicVoiceCreationHandler;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.CREATE_AI_AUDIO)
                .setBizType(VoiceTaskBizType.market_creation.name())
                .setQueueMode(TaskQueueMode.POP_UNTIL_COMPLETED)
                .setLoadSize(10);
    }

    @Override
    public BasicVoiceSynthesisResult synthesize(AiTask aiTask, FileOutput fileOutput) {
        return basicVoiceCreationHandler.synthesize(aiTask, fileOutput);
    }

    @Override
    protected @Nullable Duration getEstimatedConvertCost(AiTask aiTask) {
        VoiceTimeEstimation timeEstimation = aiTask.parseBizParams(VoiceCreationParams.class).getTimeEstimation();
        return timeEstimation == null ? null : timeEstimation.getConversionCost();
    }

    @Override
    protected @NotNull UserFile buildOutputAudio(AiTask aiTask, UserContext userContext) {
        FileInput fileInput = new FileInput()
                .setBizSource(FileBizSource.VOICE_MARKET)
                .setOriginalFilename("")
                .setExtension(this.getOutputAudioFileExtension())
                .setType(FileType.AUDIO);
        return userFileHelper.buildUserFile(userContext, IdHelper.getId(), fileInput);
    }
}
