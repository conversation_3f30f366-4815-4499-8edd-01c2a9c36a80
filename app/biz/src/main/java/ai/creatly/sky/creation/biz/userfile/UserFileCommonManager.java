/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.userfile;

import ai.creatly.sky.creation.biz.common.file.processor.FileProcessors;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.mapper.UserFileMapper;
import ai.creatly.sky.creation.domain.core.userfile.model.InputStreamFile;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.response.UserFileVM;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version UserFileCommonManager.java, 2024-10-31 下午4:32 zhoudong
 */
@Component
@RequiredArgsConstructor
public class UserFileCommonManager {

    private final FileProcessors     fileProcessors;
    private final UserFileRepository userFileRepository;
    private final UserFileService    userFileService;
    private final UserFileMapper     userFileMapper;

    public UserFileVM uploadAndCreate(MultipartFile file, UserContext userContext, FileType type, FileBizSource bizSource) {
        // 构建文件对象
        InputStreamFile streamFile = fileProcessors.file(type, bizSource).buildUserFile(file, userContext);

        // 幂等判断 TODO 如果是临时文件，则在需要对临时文件做续期处理
        UserFile existingFile = userFileRepository.queryByUidAndMd5(streamFile).orElse(null);
        if (existingFile != null) {
            return userFileMapper.toUserFileVM(existingFile);
        }

        // 上传文件
        userFileService.upload(streamFile);

        // 持久化
        userFileRepository.create(streamFile);
        return userFileMapper.toUserFileVM(streamFile);
    }
}
