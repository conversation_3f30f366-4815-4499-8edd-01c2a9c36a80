/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.videoproject.impl;

import ai.creatly.sky.creation.biz.product.ProductCategoryManager;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoProduct;
import ai.creatly.sky.creation.domain.support.category.model.Category;
import ai.creatly.sky.creation.domain.support.category.model.CategoryNode;
import ai.creatly.sky.creation.domain.support.category.model.CategoryPath;
import ai.creatly.sky.creation.domain.support.category.model.enums.CategoryDomain;
import ai.creatly.sky.creation.domain.support.category.service.CategoryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version VideoCategoryManagerImpl.java, v 0.1 2024-09-21 下午5:29 zhoudong
 */
@Service
@RequiredArgsConstructor
public class ProductCategoryManagerImpl implements ProductCategoryManager {

    private final CategoryRepository categoryRepository;

    @Override
    public List<CategoryNode> getProductCategories() {
        return categoryRepository.queryTreesByDomain(CategoryDomain.video_product);
    }

    @Override
    public List<CategoryNode> getCrowdCategories() {
        return categoryRepository.queryTreesByDomain(CategoryDomain.product_crowd);
    }

    @Override
    public VideoProduct recommendCategory(String productName) {
        List<Category> productCategories = categoryRepository.queryLeafsByDomain(CategoryDomain.video_product);
        List<Category> crowdCategories = categoryRepository.queryLeafsByDomain(CategoryDomain.product_crowd);
        // TODO 通过产品名称推荐分类
        return new VideoProduct()
                .setTitle(productName)
                .setCategory(new CategoryPath().setCodes(productCategories.getFirst().getPathCodes()))
                .setCrowdCategory(new CategoryPath().setCodes(crowdCategories.getFirst().getPathCodes()));
    }
}
