/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.voice.task.creation;

import ai.creatly.sky.creation.biz.voice.task.BasicVoiceTaskSynthesizer;
import ai.creatly.sky.creation.biz.voice.task.conversion.AbstractMarketVoiceConversionTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.transaction.AiTaskTransactionManager;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.userfile.model.FileOutput;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.core.voice.model.task.VoiceTaskBizType;
import ai.creatly.sky.creation.domain.core.voice.model.task.exec.BasicVoiceSynthesisResult;
import ai.creatly.sky.creation.domain.core.voice.model.task.exec.MarketVoiceSynthesisExecInfo;
import com.jspeeder.core.util.Asserts;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version AbstractMarketVoiceCreationTaskHandler.java, v 0.1 2023-12-02 下午9:03 zhoudong
 */
@Slf4j
public abstract class AbstractMarketVoiceCreationTaskHandler extends AbstractMarketVoiceConversionTaskHandler
        implements BasicVoiceTaskSynthesizer {

    @Autowired
    protected UserFileHelper  userFileHelper;
    @Autowired
    protected UserFileService userFileService;

    @Override
    public TaskPreAction preHandle(AiTask aiTask) {
        // 预处理基础语音合成
        Asserts.isTrue(VoiceTaskBizType.valueOf(aiTask.getBizType()).dependsOnBasicSynthesis(), "should not happen!");
        BasicVoiceSynthesisResult synthesisResult = this.synthesize(aiTask, FileOutput.of(FileBizSource.VOICE_MARKET_BASIC));

        // 合成失败则取消任务
        if (!synthesisResult.isSuccess()) {
            return TaskPreAction.FORWARD_CANCELED.withReason(synthesisResult.getCancelReason());
        }

        // 持久化音频文件
        UserFile audioFile = synthesisResult.getAudioFile();
        AiTaskTransactionManager.registerSynchronization(() -> userFileRepository.create(audioFile));

        // 更新生成的音频结果
        MarketVoiceSynthesisExecInfo bizExecInfo = aiTask.parseBizExecInfo(MarketVoiceSynthesisExecInfo.class)
                .setInputAudioId(audioFile.getId())
                .setInputAudioDuration(audioFile.getDuration())
                .setInputAudioUrl(UserFileHelper.toOssUrl(audioFile))
                .setInputAudioSynthesizedCost(Objects.requireNonNull(audioFile.audioMetadata()).getSynthesisDuration());
        return TaskPreAction.FORWARD_RUNNING.updateBizExecInfo(bizExecInfo);
    }

    @Override
    protected final @Nullable UserFile getInputAudio(AiTask aiTask) {
        var bizExecInfo = aiTask.parseBizExecInfo(MarketVoiceSynthesisExecInfo.class);
        if (bizExecInfo.getInputAudioId() == null) {
            return null;
        }
        return userFileRepository.queryOptionalById(bizExecInfo.getInputAudioId()).orElse(null);
    }

    @Override
    public final TaskPostAction postHandle(AiTask aiTask) {
        // 删除输入的音频文件（合成的中间产物）
        UserFile inputAudio = this.getInputAudio(aiTask);
        if (inputAudio != null) {
            userFileRepository.trashById(inputAudio.getId());
        }
        return super.postHandle(aiTask);
    }
}
