/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.ai.image;

import ai.creatly.sky.creation.domain.core.ai.image.mapper.ImageGenerationMapper;
import ai.creatly.sky.creation.domain.core.ai.image.model.request.ImageGenerateRequest;
import ai.creatly.sky.creation.domain.core.ai.image.model.response.ImageGenerationVM;
import ai.creatly.sky.creation.domain.core.ai.image.service.ImageGenerationService;
import ai.creatly.sky.creation.domain.core.ai.image.task.AiImageTaskBizType;
import ai.creatly.sky.creation.domain.core.ai.image.task.generate.model.ImageGenerateTaskInput;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskService;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.model.FileRef;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * @version ImageGenerationManager.java, v0.1 2025-02-19 20:45
 */
@Service
@RequiredArgsConstructor
@Deprecated
public class ImageGenerationManager {

    private final ImageGenerationService  imageGenerationService;
    private final ImageGenerationMapper   imageGenerationMapper;
    private final AiTaskService           aiTaskService;
    private final UserFileRepository      userFileRepository;
    private final UserCreditDomainService userCreditDomainService;

    public long submitGeneration(UserContext userContext, ImageGenerateRequest request) {

        boolean isEnough = userCreditDomainService.isCreditsEnough(userContext.getUid(), 10, CreditAccountType.GENERAL);
        if (!isEnough) {
            throw new BizException(UserErrorCode.USER_CREDIT_NOT_ENOUGH);
        }

        List<FileRef> referImageFiles = null;
        if (request.getFileIds() != null) {
            List<Long> referImageFileIds = request.getFileIds().stream().map(Long::parseLong).toList();
            referImageFiles = userFileRepository.queryByIds(referImageFileIds)
                    .stream()
                    .map(UserFile::toFileRef)
                    .toList();
        }
        ImageGenerateTaskInput taskInput = imageGenerationMapper.toTaskInput(request, referImageFiles);
        AiTask aiTask = AiTask.buildNew()
                .owner(userContext)
                .taskType(AiTaskType.AI_IMAGE)
                .bizType(AiImageTaskBizType.image_generate)
                .bizNo(IdHelper.getId())
                .subBizNo(StringUtils.EMPTY)
                .bizInput(taskInput)
                .estimatedDuration(Duration.ofMinutes(3))
                .timeoutFromCreatedAt(Duration.ofMinutes(30))
                .timeoutFromStartedAt(Duration.ofMinutes(5))
                .build();
        return aiTaskService.submit(aiTask, true);
    }

    public Page<ImageGenerationVM> queryGenerationPage(long uid, Pageable pageable) {
        return imageGenerationService.queryGenerationPage(uid, pageable)
                .map(imageGenerationMapper::toImageGenerationVM);
    }
}
