/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.voice.impl;

import ai.creatly.sky.creation.biz.common.file.MultipartFileHelper;
import ai.creatly.sky.creation.biz.voice.VoiceManager;
import ai.creatly.sky.creation.domain.common.integration.rvc.RvcClient;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskQO;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskHelper;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.model.response.CreditsExpenseVM;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsFactory;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.error.FileErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.mapper.UserFileMapper;
import ai.creatly.sky.creation.domain.core.userfile.model.FileInput;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.response.UserFileVM;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.core.userfile.service.builder.AudioFileBuilder;
import ai.creatly.sky.creation.domain.core.voice.error.VoiceErrorCode;
import ai.creatly.sky.creation.domain.core.voice.mapper.*;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceTimeEstimation;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceCategory;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceContourPreviewRequest;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceCreationRequest;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceSynthesizeRequest;
import ai.creatly.sky.creation.domain.core.voice.model.response.AliVoiceVM;
import ai.creatly.sky.creation.domain.core.voice.model.response.VoiceVM;
import ai.creatly.sky.creation.domain.core.voice.model.response.task.VoiceConversionTaskVM;
import ai.creatly.sky.creation.domain.core.voice.model.response.task.VoiceCreationTaskVM;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.VoiceCreationContent;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.VoiceDialogue;
import ai.creatly.sky.creation.domain.core.voice.model.task.VoiceTaskBizType;
import ai.creatly.sky.creation.domain.core.voice.repository.VoiceCacheableRepository;
import ai.creatly.sky.creation.domain.core.voice.service.VoiceChecker;
import ai.creatly.sky.creation.domain.core.voice.service.VoiceService;
import ai.creatly.sky.creation.domain.core.voice.util.VoiceUtil;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import ai.creatly.sky.creation.domain.support.multimedia.model.AudioMetadata;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StreamUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.Duration;
import java.util.List;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * 声音业务管理实现
 *
 * <AUTHOR>
 * @version VoiceManagerImpl.java, v 0.1 2023-11-04 下午8:11 zhoudong
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VoiceManagerImpl implements VoiceManager {

    private final VoiceCacheableRepository  voiceCacheableRepository;
    private final VoiceMapper               voiceMapper;
    private final VoiceService              voiceService;
    private final VoiceSynthesisMapper      voiceSynthesisMapper;
    private final VoiceConversionTaskMapper voiceConversionTaskMapper;
    private final VoiceCreationTaskMapper   voiceCreationTaskMapper;
    private final CreditsFactory            creditsFactory;
    private final RvcClient                 rvcClient;
    private final AiTaskRepository          aiTaskRepository;
    private final UserFileHelper            userFileHelper;
    private final UserFileService           userFileService;
    private final UserFileRepository        userFileRepository;
    private final AudioFileBuilder          audioFileBuilder;
    private final TransactionTemplate       transactionTemplate;
    private final UserCreditDomainService   userCreditDomainService;
    private final AiTaskHelper              aiTaskHelper;
    private final UserFileMapper            userFileMapper;
    private final VoiceChecker        voiceChecker;
    private final MultipartFileHelper multipartFileHelper;

    @Override
    public List<VoiceVM> queryOnlineVoices(VoiceCategory category, long uid) {
        List<Voice> sysVoices = voiceCacheableRepository.queryOnlineVoices(category, AppConstants.SYSTEM_UID);
        if (AppConstants.isSystemUser(uid)) {
            return voiceMapper.toVoiceVMs(sysVoices);
        }
        // 排序：先系统声音，再定制声音
        List<Voice> userVoices = voiceCacheableRepository.queryOnlineVoices(category, uid);
        return voiceMapper.toVoiceVMs(Stream.of(sysVoices, userVoices).flatMap(List::stream).collect(toList()));
    }

    @Override
    public Duration estimateAudioDuration(VoiceSynthesizeRequest request) {
        VoiceDialogue voiceDialogue = voiceChecker.checkSimpleSynthesis(request, 1000);
        VoiceTimeEstimation voiceTimeEstimation = VoiceUtil.estimationSingleVoiceTime(voiceDialogue);
        return voiceTimeEstimation.getAudioDuration();
    }

    @Override
    public String submitSynthesis(VoiceSynthesizeRequest request, UserContext userContext) {
        VoiceDialogue voiceDialogue = voiceChecker.checkSimpleSynthesis(request, 750);

        // 任务限流校验
        String taskBizType = voiceDialogue.getVoice().getCategory().getSynthesisTaskBizType().getCode();
        this.checkLimiting(userContext.getouid(), taskBizType);

        // 耗时预估
        VoiceTimeEstimation timeEstimation = VoiceUtil.estimateVoiceTime(voiceDialogue);
        log.info("[submitSynthesis]time estimation={}", timeEstimation);

        // 构建合成任务
        AiTask aiTask = voiceSynthesisMapper.toVoiceSynthesizeTask(voiceDialogue, userContext, timeEstimation);

        // 预估扣费校验
        CreditsExpense expense = creditsFactory.voice().estimateExpense(aiTask);
        Validates.isTrue(userCreditDomainService.isCreditsEnough(expense), UserErrorCode.USER_CREDIT_NOT_ENOUGH);

        // 持久化合成任务
        long taskId = aiTaskRepository.create(aiTask);

        // 立刻触发任务执行（异步执行）
        aiTaskHelper.asyncExecute(taskId);
        return String.valueOf(taskId);
    }

    @Override
    public UserFileVM simpleSynthesize(VoiceSynthesizeRequest request, UserContext userContext) {
        // 基础校验
        VoiceDialogue voiceDialogue = voiceChecker.checkSimpleSynthesis(request, 1000);

        // 合成语音
        VoiceCategory category = voiceDialogue.getVoice().getCategory();
        FileBizSource bizSource = category == VoiceCategory.market ? FileBizSource.VOICE_MARKET : FileBizSource.VOICE_BASIC;
        UserFile audioFile = voiceService.synthesize(voiceDialogue, bizSource, userContext);
        Validates.notNull(audioFile, VoiceErrorCode.VOICE_SYNTHESIS_TEXT_INCORRECT);
        Validates.isTrue(userFileService.isStoragePresent(audioFile), VoiceErrorCode.AI_AUDIO_GENERATION_FAIL);

        // 持久化语音文件
        userFileRepository.create(audioFile);
        return userFileMapper.toUserFileVM(audioFile);
    }

    @Override
    public CreditsExpenseVM estimateCreationExpense(VoiceCreationRequest request, UserContext userContext) {
        // 构建语音创作任务
        AiTask aiTask = this.buildVoiceCreationTask(request, userContext);

        // 预估扣费校验
        CreditsExpense expense = creditsFactory.voice().estimateExpense(aiTask);

        return new CreditsExpenseVM()
                .setAmount(expense.getAmount())
                .setType(CreditAccountType.GENERAL)
                .setBizType(VoiceTaskBizType.valueOf(aiTask.getBizType()).getCreditLogBizType());
    }

    @Override
    public String previewContour(VoiceContourPreviewRequest request, UserContext userContext, OutputStream outputStream) {
        // 语调校验
        voiceChecker.checkContour(request.getContour());

        // 声音校验
        VoiceDialogue voiceDialogue = voiceChecker.checkSimpleSynthesis(request);

        // 合成音频 -> 上传到OSS临时目录-> 写入输出流
        FileBizSource bizSource = FileBizSource.VOICE_TEMP;
        UserFile basicAudio = voiceService.synthesize(voiceDialogue, bizSource, userContext);
        Validates.notNull(basicAudio, VoiceErrorCode.VOICE_SYNTHESIS_TEXT_INCORRECT);
        Validates.isTrue(userFileService.isStoragePresent(basicAudio), VoiceErrorCode.AI_AUDIO_GENERATION_FAIL);

        // 转换为目标声音
        UserFile rvcAudio = null;
        if (voiceDialogue.getVoice().getCategory() == VoiceCategory.market) {
            String bucket = basicAudio.getBucket();
            String inputKey = basicAudio.getKey();
            FileInput fileInput = new FileInput()
                    .setBizSource(bizSource)
                    .setOriginalFilename("")
                    .setExtension("wav")
                    .setType(FileType.AUDIO);
            rvcAudio = userFileHelper.buildUserFile(userContext, IdHelper.getId(), fileInput);
            rvcClient.convertVoice(voiceDialogue.getVoice().getCode(), bucket, inputKey, rvcAudio.getKey());
            if (AppConstants.isSystemUser(userContext.getouid())) {
                // 如果是系统用户生成的，则将文件设置为可公开访问，比如在管理后台中生成预览音频
                userFileService.makePublic(rvcAudio);
            }
        }

        // 输出到流
        UserFile targetFile = rvcAudio != null ? rvcAudio : basicAudio;
        if (outputStream != null) {
            try (InputStream inputStream = userFileService.download(targetFile)) {
                int bytes = StreamUtils.copy(inputStream, outputStream);
                Validates.isTrue(bytes > 0, VoiceErrorCode.VOICE_SYNTHESIS_TEXT_INCORRECT);
            } catch (IOException e) {
                log.error("[previewContour]error", e);
                throw new BizException(VoiceErrorCode.AI_AUDIO_GENERATION_FAIL);
            }
        }
        return userFileHelper.getHttpUrl(targetFile);
    }

    @Override
    public String submitCreation(VoiceCreationRequest request, UserContext userContext) {
        // 构建语音创作任务
        AiTask aiTask = this.buildVoiceCreationTask(request, userContext);

        // 预估扣费校验
        CreditsExpense expense = creditsFactory.voice().estimateExpense(aiTask);
        Validates.isTrue(userCreditDomainService.isCreditsEnough(expense), UserErrorCode.USER_CREDIT_NOT_ENOUGH);

        // 持久化合成任务
        long taskId = aiTaskRepository.create(aiTask);

        // 立刻触发任务执行（异步执行）
        aiTaskHelper.asyncExecute(taskId);
        return String.valueOf(taskId);
    }

    @Override
    public Page<VoiceCreationTaskVM> queryCreationTaskPage(VoiceCategory category, long uid, List<AiTaskStatus> statuses,
                                                           Pageable pageable) {
        List<String> bizTypes = List.of(category.getCreationTaskBizType().getCode());
        AiTaskQO qo = new AiTaskQO()
                .setTaskType(AiTaskType.CREATE_AI_AUDIO)
                .setBizTypes(bizTypes)
                .setOwnerId(uid)
                .setStatuses(statuses);
        Page<AiTask> page = aiTaskRepository.queryPage(qo, pageable);
        return page.map(aiTask -> voiceCreationTaskMapper.toVoiceCreationTaskVM(aiTask, userFileHelper));
    }

    private AiTask buildVoiceCreationTask(VoiceCreationRequest request, UserContext userContext) {
        // 语音内容校验
        VoiceCreationContent content = voiceChecker.checkCreation(request, userContext);

        // 限流校验
        Voice firstVoice = content.getDialogues().getFirst().getVoice();
        this.checkLimiting(userContext.getouid(), firstVoice.getCategory().getCreationTaskBizType().getCode());

        // 耗时预估
        VoiceTimeEstimation timeEstimation = VoiceUtil.estimateVoiceTime(content);
        log.info("[submitCreation]time estimation={}", timeEstimation);

        // 构建合成任务
        return voiceCreationTaskMapper.toVoiceCreationTask(firstVoice, request, userContext, timeEstimation);
    }

    /**
     * 限流校验
     */
    private void checkLimiting(long uid, String taskBizType) {
        // 单用户并发最多3个进行中的
        long count = aiTaskRepository.countInProgressWithUser(uid, AiTaskType.CREATE_AI_AUDIO, taskBizType);
        // TODO: 2023/8/1 做成可配置的
        Validates.isTrue(count < 3, CommonErrorCode.SLA_LIMITED);
    }

    @Override
    public String submitConversion(long voiceId, MultipartFile audioFile, UserContext userContext) {
        // 音频校验
        AudioMetadata audioMetadata = multipartFileHelper.resolveAudioMetadata(audioFile, FileBizSource.VOICE_MARKET);

        // 声音校验
        Voice voice = voiceCacheableRepository.queryOptionalById(voiceId).orElse(null);
        Validates.notNull(voice, VoiceErrorCode.VOICE_NOT_FOUND);
        Validates.isTrue(voice.getCategory() == VoiceCategory.market, VoiceErrorCode.VOICE_CONVERSION_NOT_SUPPORT);

        // 限流校验
        String taskBizType = VoiceTaskBizType.market_conversion.getCode();
        this.checkLimiting(userContext.getouid(), taskBizType);

        // 上传输入音频
        String originalFilename = audioFile.getOriginalFilename();
        UserFile inputAudioFile = audioFileBuilder.build(FileBizSource.VOICE_MARKET, audioMetadata, userContext, originalFilename);
        try (InputStream audioStream = audioFile.getInputStream()) {
            userFileService.upload(audioStream, inputAudioFile);
        } catch (IOException e) {
            throw new BizException(FileErrorCode.FILE_UPLOAD_ERROR, inputAudioFile.getType().getDesc(), e);
        }

        // 构建变声任务
        AiTask aiTask = voiceConversionTaskMapper.toVoiceConversionTask(voice, taskBizType, inputAudioFile, userContext);

        // 持久化输入的音频文件 + 变声任务并扣费
        @Nullable
        Long taskId = transactionTemplate.execute(status -> {
            // 持久化文件
            userFileRepository.create(inputAudioFile);
            aiTaskRepository.create(aiTask);
            // 扣费计算逻辑，根据音频时长来
            CreditsExpense expense = creditsFactory.voice().calcExpense(aiTask, inputAudioFile);
            boolean consumed = userCreditDomainService.consumeCredits(expense);
            if (!consumed) {
                // 扣费失败，回滚事务
                status.setRollbackOnly();
                return null;
            }
            return aiTask.getId();
        });

        // 如果扣费失败，删除输入音频，主动抛出异常
        if (taskId == null) {
            userFileService.deleteStorageSilently(inputAudioFile);
            throw new BizException(UserErrorCode.USER_CREDIT_NOT_ENOUGH);
        }

        // 立刻触发任务执行（异步执行）
        aiTaskHelper.asyncExecute(taskId);
        return String.valueOf(taskId);
    }

    @Override
    public Page<VoiceConversionTaskVM> queryConversionTaskPage(VoiceTaskBizType bizType, long uid, List<AiTaskStatus> statuses,
                                                               Pageable pageable) {
        Validates.isTrue(bizType.isConversion(), "bizType is invalid");
        AiTaskQO qo = new AiTaskQO()
                .setTaskType(AiTaskType.CREATE_AI_AUDIO)
                .setBizTypes(List.of(bizType.name()))
                .setOwnerId(uid)
                .setStatuses(statuses);
        Page<AiTask> page = aiTaskRepository.queryPage(qo, pageable);
        return page.map(aiTask -> {
            long voiceUid = VoiceTaskUtil.parseVoiceUid(aiTask.getBizNo());
            String voiceCode = VoiceTaskUtil.parseVoiceCode(aiTask.getBizNo());
            Voice voice = voiceCacheableRepository.queryByUidAndCode(voiceUid, voiceCode);
            return voiceConversionTaskMapper.toVoiceConversionTaskVM(aiTask, voice, userFileHelper);
        });
    }

    @Override
    public List<AliVoiceVM> getALiVoices(Long uid) {
        //TODO
        return null;
    }



}
