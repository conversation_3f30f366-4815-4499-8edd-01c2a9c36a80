/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.deprecated.prompt;

import ai.creatly.sky.creation.domain.common.ddd.ApplicationService;
import ai.creatly.sky.creation.domain.deprecated.consult.mapper.LLMConversationMapper;
import ai.creatly.sky.creation.domain.deprecated.consult.modal.request.ScenarioPromptQueryRequest;
import ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal.LLMConversation;
import ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal.LLMMessage;
import ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal.response.LLMConversationVM;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.CopilotService;
import ai.creatly.sky.creation.domain.support.prompt.mapper.ScenarioPromptMapper;
import ai.creatly.sky.creation.domain.support.prompt.model.ScenarioPrompt;
import ai.creatly.sky.creation.domain.support.prompt.model.category.response.PromptCategoryVM;
import ai.creatly.sky.creation.domain.support.prompt.model.response.ScenarioPromptVM;
import ai.creatly.sky.creation.domain.support.prompt.repository.PromptCategoryRepository;
import ai.creatly.sky.creation.domain.support.prompt.repository.ScenarioPromptRepository;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * 提示词服务
 */
@Deprecated
@ApplicationService
@RequiredArgsConstructor
public class PromptApplicationService {

    private final CopilotService           copilotService;
    private final ScenarioPromptRepository scenarioPromptRepository;
    private final PromptCategoryRepository promptCategoryRepository;
    private final ScenarioPromptMapper     scenarioPromptMapper;
    private final LLMConversationMapper    conversationMapper;

    public Page<ScenarioPromptVM<?>> queryScenariosPromptPage(ScenarioPromptQueryRequest request, Pageable pageable) {
        Page<ScenarioPrompt> promptPage = scenarioPromptRepository.queryScenarioPrompt(request, pageable);
        return promptPage.map(scenarioPromptMapper::toScenarioPromptVM);
    }

    /**
     * 查询用户会话历史记录详情
     */
    public List<LLMMessage> getUserConversationDetail(Long uid, Long conversationId) {
        return copilotService.getUserConversationDetail(uid, conversationId);
    }

    /**
     * 新建用户会话
     */
    public String createUserConversation(Long uid, Long scenarioId) {
        return String.valueOf(copilotService.newUserConversation(uid, scenarioId));
    }

    /**
     * 删除用户会话信息
     */
    public void deleteConversation(Long uid, Long conversationId) {
        copilotService.deleteConversation(uid, conversationId);
    }

    /**
     * 获取用户会话列表
     */
    public List<LLMConversationVM> getUserConversations(Long uid) {
        // 查询会话历史记录
        List<LLMConversation> conversations = copilotService.getUserConversations(uid);
        return conversationMapper.toConversationVMS(conversations);
    }

    /**
     * 按照类目检索的业务意义存在么
     *
     * @param pageable -
     * @param source -
     * @return -
     */
    public List<PromptCategoryVM> queryScenarioCategories(Pageable pageable, String source) {
        return promptCategoryRepository.findPageablePromptCategory(pageable, source, null, null)
                .map(e -> {
                    PromptCategoryVM promptCategoryVM = new PromptCategoryVM();
                    promptCategoryVM.setCode(e.getCode());
                    promptCategoryVM.setName(e.getName());
                    promptCategoryVM.setBizSource(e.getBizSource().name());
                    promptCategoryVM.setEnName(e.getEnName());
                    return promptCategoryVM;
                }).getContent();
    }
}
