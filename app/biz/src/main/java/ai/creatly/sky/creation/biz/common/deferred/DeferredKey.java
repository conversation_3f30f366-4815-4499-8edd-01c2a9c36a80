/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.common.deferred;

import com.github.benmanes.caffeine.cache.Cache;
import com.jspeeder.core.util.Asserts;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.jetbrains.annotations.Nullable;
import org.springframework.web.context.request.async.DeferredResult;

import java.time.Duration;

/**
 * 用于缓存延迟结果的key
 *
 * <AUTHOR>
 * @version DeferredKey.java, v 0.1 2024-06-16 下午11:19 zhoudong
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode(of = "key")
public class DeferredKey {

    static final String DEFAULT_KEY_NAME = "bizKey";
    static final String TASK_KEY_NAME    = "taskId";
    static final String ORDER_KEY_NAME   = "orderId";

    private final String          key;
    private final String          keyName;
    @Nullable
    private final DeferredContext context;

    public static DeferredKey of(String key) {
        return of(key, null);
    }

    public static DeferredKey of(String key, DeferredContext context) {
        return new DeferredKey(key, DEFAULT_KEY_NAME, context);
    }

    public static DeferredKey of(long key) {
        return of(key, null);
    }

    public static DeferredKey of(long key, DeferredContext context) {
        return new DeferredKey(String.valueOf(key), DEFAULT_KEY_NAME, context);
    }

    public static DeferredKey task(long taskId) {
        return task(taskId, null);
    }

    public static DeferredKey task(long taskId, DeferredContext context) {
        return new DeferredKey(String.valueOf(taskId), TASK_KEY_NAME, context);
    }

    public static DeferredKey order(long orderId) {
        return task(orderId, null);
    }

    public static DeferredKey order(long orderId, DeferredContext context) {
        return new DeferredKey(String.valueOf(orderId), ORDER_KEY_NAME, context);
    }

    public <T> DeferredResult<T> newDeferredResult(Cache<DeferredKey, ?> deferredCache) {
        Asserts.notNull(context, "deferred context is null!");
        return context.newDeferredResult(key, keyName, deferredCache);
    }

    public DeferredContext context() {
        Asserts.notNull(context, "deferred context is null!");
        return context;
    }

    public long getStartedAt() {
        Asserts.notNull(context, "deferred context is null!");
        return context.getStartedAt();
    }

    public Duration getTimeout() {
        Asserts.notNull(context, "deferred context is null!");
        return context.getTimeout();
    }

    @Override
    public String toString() {
        return keyName + "=" + key;
    }
}
