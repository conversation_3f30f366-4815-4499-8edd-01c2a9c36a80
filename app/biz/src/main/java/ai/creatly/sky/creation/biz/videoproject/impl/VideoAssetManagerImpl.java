/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.videoproject.impl;

import ai.creatly.sky.creation.biz.videoproject.VideoAssetManager;
import ai.creatly.sky.creation.domain.core.userasset.model.UserAsset;
import ai.creatly.sky.creation.domain.core.userasset.service.UserAssetService;
import ai.creatly.sky.creation.domain.core.videoproject.error.VideoProjectErrorCode;
import ai.creatly.sky.creation.domain.core.videoproject.mapper.VideoProjectMapper;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoAsset;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoProject;
import ai.creatly.sky.creation.domain.core.videoproject.model.enums.VideoProjectStage;
import ai.creatly.sky.creation.domain.core.videoproject.model.request.VideoAssetSaveDTO;
import ai.creatly.sky.creation.domain.core.videoproject.model.request.VideoAssetsSaveRequest;
import ai.creatly.sky.creation.domain.core.videoproject.service.VideoProjectRepository;
import ai.creatly.sky.creation.domain.core.videoproject.service.VideoProjectService;
import com.jspeeder.core.model.BizStatus;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

/**
 * <AUTHOR>
 * @version VideoAssetManagerImpl.java, v 0.1 2024-09-21 下午5:28 zhoudong
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VideoAssetManagerImpl implements VideoAssetManager {

    private final VideoProjectRepository videoProjectRepository;
    private final VideoProjectService    videoProjectService;
    private final VideoProjectMapper     videoProjectMapper;
    private final UserAssetService       userAssetService;

    @Override
    public void saveVideoAssets(long projectId, VideoAssetsSaveRequest request, long uid) {
        // 数据校验
        VideoProject project = videoProjectService.getCheckedVideoProject(projectId, uid);
        Validates.isTrue(project.isEditable(), VideoProjectErrorCode.VIDEO_PROJECT_EDIT_NOT_ALLOWED);

        // 更新项目素材
        Set<Long> assetIds = request.getAssets()
                .stream()
                .map(VideoAssetSaveDTO::getId)
                .map(id -> Validates.requireLong(id, "assetId"))
                .collect(toSet());
        Map<Long, UserAsset> assetMap = userAssetService.getCheckedUserAssetMap(assetIds, uid);
        List<VideoAsset> assets = assetMap.values()
                .stream()
                .peek(asset -> Validates.isTrue(asset.getStatus() == BizStatus.VALID, VideoProjectErrorCode.VIDEO_ASSET_INVALID))
                .map(videoProjectMapper::toVideoAsset)
                .collect(toList());

        // 持久化
        VideoProject updatingVideoProject = new VideoProject()
                .setId(project.getId())
                .setStage(VideoProjectStage.editing)
                .setAssets(assets);
        if (!assets.equals(project.getAssets()) && !project.getShots().isEmpty()) {
            // 素材更新后，需要重新生成分镜
            updatingVideoProject.setComposeExpired(true);
        }
        if (project.getCoverUrl() == null && !assets.isEmpty()) {
            // 如果封面为空，设置第一个素材为封面
            UserAsset asset = assetMap.get(assets.getFirst().getId());
            project.setCoverFileId(asset.getCoverFileId()).setCoverUrl(asset.getCoverUrl());
        }
        videoProjectRepository.updateById(updatingVideoProject);
    }
}
