/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.common.file.processor;

import ai.creatly.sky.creation.biz.common.file.MultipartFileHelper;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.error.FileErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.mapper.UserFileMapper;
import ai.creatly.sky.creation.domain.core.userfile.model.InputStreamFile;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.service.builder.AudioFileBuilder;
import ai.creatly.sky.creation.domain.support.multimedia.model.AudioMetadata;
import com.jspeeder.core.data.problem.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version AudioFileProcessor.java, v 0.1 2024-09-24 上午11:08 zhoudong
 */
@Slf4j
@RequiredArgsConstructor
class AudioFileProcessor implements FileProcessor {

    private final MultipartFileHelper multipartFileHelper;
    private final AudioFileBuilder    audioFileBuilder;
    private final UserFileMapper      userFileMapper;
    private final FileBizSource       bizSource;

    @Override
    public InputStreamFile buildUserFile(MultipartFile file, UserContext userContext) {
        String originalFilename = file.getOriginalFilename();
        AudioMetadata audioMetadata = multipartFileHelper.resolveAudioMetadata(file, bizSource);
        UserFile userFile = audioFileBuilder.build(bizSource, audioMetadata, userContext, originalFilename);
        return userFileMapper.toInputStreamFile(userFile, () -> {
            try {
                return file.getInputStream();
            } catch (IOException e) {
                log.warn("上传音频文件失败", e);
                throw new BizException(FileErrorCode.FILE_UPLOAD_ERROR, userFile.getType().getDesc(), e);
            }
        });
    }
}
