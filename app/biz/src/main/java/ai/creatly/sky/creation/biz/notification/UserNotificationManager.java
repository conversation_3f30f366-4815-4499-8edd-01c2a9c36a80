/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.notification;

import ai.creatly.sky.creation.domain.common.async.EventStream;
import ai.creatly.sky.creation.domain.support.notification.inbox.NotificationRepository;
import ai.creatly.sky.creation.domain.support.notification.inbox.mapper.NotificationVMMapper;
import ai.creatly.sky.creation.domain.support.notification.inbox.model.UserNotificationBox;
import ai.creatly.sky.creation.domain.support.notification.inbox.model.response.NotificationVM;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version UserNotificationManager.java, v 0.1 2024-07-11 10:15 syoka
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserNotificationManager {

    private final NotificationVMMapper   notificationVMMapper;
    private final NotificationRepository notificationRepository;


    public Page<NotificationVM> getUserNotification(Pageable pageable, Long uid) {
        return notificationRepository.queryPageable(uid, pageable)
                .map(notificationVMMapper::toVM);
    }

    /**
     * 检测用户是否有新通知
     */
    public void checkUserNewNotification(EventStream producer) {
        try {
            producer.write("notificationVMMapper.toVM()");
        } catch (Exception e) {
            log.info("[checkUserNewNotification][发生异常]", e);
            producer.complete();
        }
    }

    /**
     * 已读用户所有通知消息
     *
     * @param uid 用户id
     */
    public void readAllNotification(Long uid) {
        notificationRepository.readAllNotification(uid);
    }

    /**
     * 已读用户指定消息
     *
     * @param uid            用户id
     * @param notificationId 消息id
     */
    public void readSingleNotification(Long uid, Long notificationId) {
        notificationRepository.readSingleNotification(uid, notificationId);
    }

    /**
     * 获取用户未读的通知
     *
     * @param uid 用户id
     */
    public UserNotificationBox getUserUnreadNotification(long uid) {
        Long unreadCount = notificationRepository.countUserUnreadNotification(uid);
        return new UserNotificationBox()
                .setUnreadNotificationCount(unreadCount)
                .setHasUnreadNotifications(unreadCount > 0);
    }
}
