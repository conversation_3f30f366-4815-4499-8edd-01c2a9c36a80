/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.voice.task.sync;

import ai.creatly.kylin.trade.sdk.order.client.TradeOrderClient;
import ai.creatly.kylin.trade.sdk.order.event.OrderPaidEvent;
import ai.creatly.kylin.trade.sdk.order.request.OrderCreateRequest;
import ai.creatly.sky.creation.domain.common.integration.azure.model.SynthesisVoiceNameEnum;
import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.common.integration.rvc.RvcClient;
import ai.creatly.sky.creation.domain.common.messaging.consumer.ConsumeAction;
import ai.creatly.sky.creation.domain.common.messaging.consumer.ConsumerConfig;
import ai.creatly.sky.creation.domain.common.messaging.consumer.MQListener;
import ai.creatly.sky.creation.domain.common.messaging.producer.Destination;
import ai.creatly.sky.creation.domain.common.messaging.producer.MessagingTemplate;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizResult;
import ai.creatly.sky.creation.domain.core.aitask.model.UpdatableAiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskHelper;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.FileInput;
import ai.creatly.sky.creation.domain.core.userfile.model.FileMetadata;
import ai.creatly.sky.creation.domain.core.userfile.model.FileOutput;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.core.voice.error.VoiceErrorCode;
import ai.creatly.sky.creation.domain.core.voice.mapper.UserVoiceOrderMapper;
import ai.creatly.sky.creation.domain.core.voice.mapper.VoiceCreationMapper;
import ai.creatly.sky.creation.domain.core.voice.model.*;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceCategory;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceProviderType;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceStatus;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.VoiceDialogue;
import ai.creatly.sky.creation.domain.core.voice.model.task.UserVoiceTaskBizStatus;
import ai.creatly.sky.creation.domain.core.voice.model.task.param.UserVoiceCreateParam;
import ai.creatly.sky.creation.domain.core.voice.repository.UserVoiceRepository;
import ai.creatly.sky.creation.domain.core.voice.repository.VoiceRepository;
import ai.creatly.sky.creation.domain.core.voice.service.VoiceService;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import ai.creatly.sky.creation.domain.support.notification.app.AppNotifyHelper;
import ai.creatly.sky.creation.domain.support.trade.OrderBizType;
import ai.creatly.sky.creation.domain.support.trade.TradeConstants;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.Validates;
import com.jspeeder.core.util.json.JSON;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户声音定制处理器
 *
 * <AUTHOR>
 * @version UserVoiceCustomizeTaskHandler.java, v 0.1 2024-01-20 18:22 heb
 */
//@Component
@Slf4j
@RequiredArgsConstructor
@Deprecated
public class UserVoiceCustomizeTaskHandler implements AiTaskHandler, MQListener<OrderPaidEvent> {

    private final static String ZH_PREVIEW_TEXT_PATTERN = "我是{}，您的 creatly a i 语音助手，祝您拥有美好的体验！";
    private final static String EN_PREVIEW_TEXT_PATTERN = "Hi, I'm {}. Your creatly ai voice assistant, wish you a pleasant journey!";
    private final static String JP_PREVIEW_TEXT_PATTERN = "私は{}、あなたのCreatly AI音声アシスタントです，素晴らしい体験を楽しみください";

    private final UserFileHelper       userFileHelper;
    private final UserFileService      userFileService;
    private final UserVoiceRepository  userVoiceRepository;
    private final VoiceRepository      voiceRepository;
    private final TradeOrderClient     tradeOrderClient;
    private final UserVoiceOrderMapper userVoiceOrderMapper;
    private final VoiceService         voiceService;
    private final VoiceCreationMapper  voiceCreationMapper;
    private final RvcClient            rvcClient;
    private final UserFileRepository   userFileRepository;
    private final TransactionTemplate  transactionTemplate;
    private final MessagingTemplate    messagingTemplate;
    private final AiTaskHelper         aiTaskHelper;
    private final AppNotifyHelper      appNotifyHelper;
    private final AppAlertHelper       appAlertHelper;
    private final AiTaskRepository     aiTaskRepository;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.CREATE_USER_VOICE)
                .setLoadSize(10)
                .setNotifyUserOnCompleted(true);
    }

    @Override
    public TaskPreAction preHandle(AiTask aiTask) {
        appNotifyHelper.info("您有新的声音定制任务，请及时处理！\n{}", JSON.toPrettyJSONString(aiTask));
        UpdatableAiTask updatableAiTask = new UpdatableAiTask().setAutoExec(false);
        return TaskPreAction.FORWARD_RUNNING.withAiTask(updatableAiTask);
    }

    @Override
    public TaskAction handle(AiTask aiTask) {
        UserVoiceCreateParam param = aiTask.parseBizParams(UserVoiceCreateParam.class);
        if (StringUtils.isAnyBlank(param.getEnName(), param.getVoiceCode())
                || param.getGender() == null
                || param.getAgeLevel() == null
                || CollectionUtils.isEmpty(param.getMsVoiceCodes())) {
            log.warn("voice data incomplete");
            return TaskAction.KEEP_STILL;
        }

        // 检查模型文件是否存在
        // 参考oss路径 oss://creatly-online/creation/users/10348161143943171/voice/model/{voiceCode}.pth
        String voiceCode = param.getVoiceCode();
        UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);
        String modelFileKey = UserFileHelper.buildFileKey(userContext, voiceCode, "pth", FileBizSource.USER_VOICE_MODEL);
        if (!userFileService.isStoragePresent(modelFileKey)) {
            log.warn("voice model file not exists");
            return TaskAction.KEEP_STILL;
        }
        // 参考oss路径 oss://creatly-online/creation/users/10348161143943171/voice/model/{voiceCode}.index
        String indexFileKey = UserFileHelper.buildFileKey(userContext, voiceCode, "index", FileBizSource.USER_VOICE_MODEL);
        if (!userFileService.isStoragePresent(indexFileKey)) {
            log.warn("voice model index file not exists");
            return TaskAction.KEEP_STILL;
        }

        // 持久化声音
        long voiceId = Long.parseLong(aiTask.getBizNo());
        Voice voice = this.createDraftVoice(voiceId, param, userContext);

        // 更新预览声音
        voice.getLocales().forEach(voiceLocale -> {
            String localeCode = voiceLocale.getCode();
            FileBizSource fileBizSource = FileBizSource.USER_VOICE_PREVIEW;

            // 校验预览音频是否存在
            // 参考oss路径 oss://creatly-online/creation/users/10348161143943171/voice/preview/{voiceCode}-{localeCode}.wav
            String previewAudioBaseName = voiceCode + "-" + localeCode;
            String previewAudioKey = UserFileHelper.buildFileKey(userContext, previewAudioBaseName, "wav", fileBizSource);
            if (voiceLocale.getPreviewAudioId() != null && userFileService.isStoragePresent(previewAudioKey)) {
                // 已存在预览音频
                return;
            }

            // 合成该口音的预览音频
            // 1.先合成基础声音的预览音频（仅上传到OSS，不公开，无DB持久化）
            String msVoiceCode = voiceLocale.parseExtInfo(SelfVoiceLocalExtInfo.class).getMsVoiceCode();
            Voice msVoice = Objects.requireNonNull(SynthesisVoiceNameEnum.getByName(msVoiceCode)).buildVoice();
            String inputText = this.getPreviewText(voice, voiceLocale.getLangCode());
            VoiceDialogue voiceDialogue = voiceCreationMapper.toVoiceDialogue(inputText, voiceLocale.getCode(), msVoice);
            // 参考oss路径 oss://creatly-online/creation/users/10348161143943171/voice/preview/{voiceCode}-{localeCode}-basic.wav
            String basicAudioBaseName = voiceCode + "-" + localeCode + "-basic";
            String basicAudioKey = UserFileHelper.buildFileKey(userContext, basicAudioBaseName, "wav", fileBizSource);
            FileOutput basicAudioFileOutput = FileOutput.of(fileBizSource).setFileKey(basicAudioKey);
            UserFile basicAudio = voiceService.synthesize(voiceDialogue, basicAudioFileOutput, userContext);
            Asserts.notNull(basicAudio, "基础声音的预览音频合成失败！{}", basicAudioKey);
            Asserts.isTrue(userFileService.isStoragePresent(basicAudio.getKey()), "基础声音的预览音频不存在！{}", basicAudioKey);

            // 2.再转换成定制的声音（上传到OSS，不公开，做DB持久化）
            AudioMetadata audioMetadata = rvcClient.convertVoice(voiceCode, basicAudio.getBucket(), basicAudioKey, previewAudioKey);

            // 3.持久化声音演员的声音文件记录，并更新口音的预览音频
            FileMetadata fileMetadata = new FileMetadata()
                    .setDuration(audioMetadata.getAudioDuration())
                    .setBytes(audioMetadata.getAudioBytes());
            FileInput fileInput = new FileInput()
                    .setType(FileType.AUDIO)
                    .setBizSource(fileBizSource)
                    .setOriginalFilename("")
                    .setExtension(audioMetadata.getAudioFormat())
                    .setFileKey(previewAudioKey)
                    .setFileMetadata(fileMetadata);
            UserFile audioFile = userFileHelper.buildSysFile(IdHelper.getId(), fileInput);
            this.updateLocalePreviewAudio(voice, voiceLocale, audioFile);
        });

        // 下单
        OrderCreateRequest orderCreateRequest = userVoiceOrderMapper.buildOrderCreateRequest(voice, param.getRealPrice(), userContext);
        long orderId = tradeOrderClient.createOrder(orderCreateRequest).getId();

        // 更新声音状态
        voiceRepository.updateById(new Voice().setId(voiceId).setUid(aiTask.getOwnerId()).setStatus(VoiceStatus.UNPAID));

        UpdatableAiTask updatableAiTask = new UpdatableAiTask()
                .setBizResult(new BizResult().setOrderId(orderId))
                .setAutoExec(false);
        return TaskAction.FORWARD_FINISHED.withAiTask(updatableAiTask);
    }

    @Override
    public TaskPostAction postHandle(AiTask aiTask) {
        // 任务已取消，则做一些数据清理工作
        long voiceId = Long.parseLong(aiTask.getBizNo());
        if (aiTask.getStatus() == AiTaskStatus.CANCELED) {
            // 取消订单
            Long orderId = aiTask.parseBizResult(BizResult.class).getOrderId();
            if (orderId != null) {
                tradeOrderClient.cancelOrder(TradeConstants.TENANT_CODE, orderId);
            }

            // 删除文件：预览音频，删除声音
            userVoiceRepository.queryOptionalById(voiceId).ifPresent(userCustomVoice -> {
                userCustomVoice.getLocales().forEach(voiceLocale -> {
                    if (voiceLocale.getPreviewAudioId() != null) {
                        UserFile userFile = userFileRepository.queryById(voiceLocale.getPreviewAudioId());
                        userFileService.trash(userFile.getId(), aiTask.getOwnerId());
                        userFileService.deleteStorageSilently(userFile);
                    }
                });
                userVoiceRepository.deleteById(voiceId);
            });

            UpdatableAiTask updatableAiTask = new UpdatableAiTask()
                    .setBizStatus(UserVoiceTaskBizStatus.canceled.name());
            return TaskPostAction.COMPLETE.withAiTask(updatableAiTask);
        }

        // 定制声音未支付，则任务将持续运行，不能完结
        if (userVoiceRepository.queryById(voiceId).getStatus() != VoiceStatus.ONLINE) {
            return TaskPostAction.KEEP_STILL;
        }

        UpdatableAiTask updatableAiTask = new UpdatableAiTask()
                .setBizStatus(UserVoiceTaskBizStatus.online.name());
        return TaskPostAction.COMPLETE.withAiTask(updatableAiTask);
    }

    @Data
    @Accessors(chain = true)
    private static class BizResult implements TaskBizResult {
        private Long orderId;
    }

    /**
     * 录入定制声音
     *
     * @param voiceId     声音ID
     * @param param       创建参数
     * @param userContext 用户上下文
     */
    private Voice createDraftVoice(long voiceId, UserVoiceCreateParam param, UserContext userContext) {
        // 幂等判断
        long uid = userContext.getouid();
        Voice existingVoice = voiceRepository.queryOptionalByUidAndCode(uid, param.getVoiceCode()).orElse(null);
        if (existingVoice != null) {
            Validates.isTrue(existingVoice.getId().equals(voiceId), "该声音编号已被用户使用过了，请更换或取消任务！param={}", param);
            Validates.isTrue(existingVoice.getStatus().isDraftOrUnpaid(), "该声音已支付，请检查声音编号是否录入正确！param={}", param);
            return existingVoice;
        }

        // 头像文件
        UserFile avatarFile = userFileRepository.queryById(param.getAvatarFileId());
        if (avatarFile.getAcl() == FileAcl.PRIVATE) {
            userFileService.makePublic(avatarFile.getStorageType(), avatarFile.getKey());
            userFileRepository.makePublic(avatarFile.getId());
            avatarFile.setAcl(FileAcl.PUBLIC);
        }

        // 构建
        Voice voice = new Voice()
                .setUid(uid)
                .setId(voiceId)
                .setCode(param.getVoiceCode())
                .setProviderType(VoiceProviderType.self)
                .setCategory(VoiceCategory.market)
                .setStatus(VoiceStatus.DRAFT)
                .setEnName(param.getEnName())
                .setCnName(param.getCnName())
                .setGender(param.getGender())
                .setAvatar(userFileHelper.getHttpUrl(avatarFile))
                .setAgeLevel(param.getAgeLevel())
                .setOrdinal(0)
                .setLabels(Collections.emptyList())
                .setLocales(this.toVoiceLocales(uid, voiceId, param.getVoiceCode(), param.getMsVoiceCodes()));

        // 持久化
        voiceRepository.create(voice);
        return userVoiceRepository.queryById(voiceId);
    }

    private List<VoiceLocale> toVoiceLocales(long uid, long voiceId, String voiceCode, List<String> msVoiceCodes) {
        List<VoiceLocale> voiceLocales = msVoiceCodes.stream().map(msVoiceCode -> {
            SynthesisVoiceNameEnum voiceNameEnum = SynthesisVoiceNameEnum.getByName(msVoiceCode);
            Validates.notNull(voiceNameEnum, "{}:没有映射到正确的基础声音", msVoiceCode);
            return this.toVoiceLocale(uid, voiceId, voiceCode, voiceNameEnum);
        }).collect(Collectors.toList());
        for (int i = 0; i < voiceLocales.size(); i++) {
            VoiceLocale voiceLocale = voiceLocales.get(i);
            voiceLocale.setOrdinal(i);
        }
        return voiceLocales;
    }

    protected VoiceLocale toVoiceLocale(long uid, long voiceId, String voiceCode, SynthesisVoiceNameEnum voiceNameEnum) {
        Voice msVoice = voiceNameEnum.buildVoice();
        VoiceLocale voiceLocale = msVoice.getLocales().getFirst();
        SelfVoiceLocalExtInfo extInfo = new SelfVoiceLocalExtInfo()
                .setMsVoiceCode(msVoice.getCode())
                .setMsVoiceCnName(msVoice.getCnName());
        return voiceLocale
                .setVoiceId(voiceId)
                .setUid(uid)
                .setVoiceCode(voiceCode)
                .setExtInfo(JSON.toJSONObject(extInfo));
    }

    protected void updateLocalePreviewAudio(Voice voice, VoiceLocale voiceLocale, UserFile audioFile) {
        transactionTemplate.executeWithoutResult(status -> {
            // 持久化文件记录
            long fileId = userFileRepository.create(audioFile);
            String ossUrl = UserFileHelper.toOssUrl(audioFile);
            // 更新预览音频
            userVoiceRepository.updateLocalePreviewAudio(voice.getId(), voiceLocale.getCode(), fileId, ossUrl);
        });
    }

    protected String getPreviewText(Voice voice, String langCode) {
        return switch (langCode) {
            case "zh" -> FormatUtil.format(ZH_PREVIEW_TEXT_PATTERN, voice.getCnName());
            case "en" -> FormatUtil.format(EN_PREVIEW_TEXT_PATTERN, voice.getEnName());
            case "ja" -> FormatUtil.format(JP_PREVIEW_TEXT_PATTERN, voice.getCnName());
            default -> throw new SysException("unknown lang code:" + langCode);
        };
    }

    /*-------------------------------------------- 消息监听逻辑 --------------------------------------------*/

    @Override
    public ConsumerConfig consumerConfig() {
//        return ConsumerConfig.builder()
//                .consumerGroup(AppConstants.APP_NAME + "%UserVoiceOrderPayResultListener")
//                .topic(MessageTopics.TRADE_TOPIC)
//                .tags(MessageTags.orderPaid(TradeConstants.TENANT_CODE))
//                .consumeMethod(ConsumeMethod.CLUSTERING)
//                .build();
        return null;
    }

    @Override
    public OrderPaidEvent doConvertMessage(String body) {
        return JSON.parseObject(body, OrderPaidEvent.class);
    }

    @Override
    public ConsumeAction onMessage(OrderPaidEvent event) {
        // 仅消费用户定制声音的订单，其它类型的订单都过滤掉
        if (!OrderBizType.CUSTOM_VOICE.name().equals(event.getBizType())) {
            log.warn("[not a voice order]orderId={},bizType={}", event.getId(), event.getBizType());
            return ConsumeAction.CONSUME_OK;
        }
        UserVoiceOrder userVoiceOrder = userVoiceOrderMapper.toVoiceOrder(event);

        // 查询声音定制任务
        String taskBizNo = userVoiceOrder.getVoiceId().toString();
        AiTask task = aiTaskRepository.queryByUnique(AiTaskType.CREATE_USER_VOICE, taskBizNo).orElse(null);
        Asserts.notNull(task, "声音定制任务不存在！voiceOrderId={},taskBizNo={}", event.getId(), taskBizNo);

        // 发送MQ消息：订单已支付成功(结束支付结果获取长轮训)
        String destination = Destination.rocketMQ()
                .topic(AppConstants.MQ_TOPIC)
                .tag(AppConstants.MQTags.USER_VOICE_ORDER_PAID)
                .build();
        messagingTemplate.send(destination, event.getId().toString(), userVoiceOrder);

        // 执行发货逻辑
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            // 上架定制声音
            userVoiceRepository.online(userVoiceOrder.getVoiceId());
            // 将声音定制任务开启自动调度
            aiTaskRepository.updateAutoExec(task.getId(), true);
        });

        // 将订单推进到已发货
        boolean success = tradeOrderClient.shipOrder(TradeConstants.TENANT_CODE, event.getId());
        if (!success) {
            log.error("[shipOnPaid][shipOrder fail]voiceOrderId={}", event.getId());
            appAlertHelper.alertText("[shipOnPaid][shipOrder fail]voiceOrderId={}", event.getId());
            throw new SysException(VoiceErrorCode.USER_VOICE_ORDER_SHIP_FAIL);
        }

        // 立刻触发任务执行（异步执行）
        aiTaskHelper.asyncExecute(task.getId());
        return ConsumeAction.CONSUME_OK;
    }
}
