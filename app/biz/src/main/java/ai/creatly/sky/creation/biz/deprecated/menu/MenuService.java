/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.deprecated.menu;

import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.deprecated.menu.model.response.MenuVM;

/**
 * <AUTHOR>
 * @version MenuService.java, v 0.1 2023-06-13 23:20 joton
 */
public interface MenuService {

    MenuVM queryMenu(String code, UserContext userContext);
}
