/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.aiproduct.image.impl;

import ai.creatly.sky.creation.biz.aiproduct.image.ProductImageAsyncManager;
import ai.creatly.sky.creation.biz.common.deferred.DeferredContext;
import ai.creatly.sky.creation.biz.common.deferred.DeferredKey;
import ai.creatly.sky.creation.biz.common.deferred.DeferredResultExpiry;
import ai.creatly.sky.creation.domain.common.messaging.consumer.ConsumeAction;
import ai.creatly.sky.creation.domain.common.messaging.consumer.ConsumeMethod;
import ai.creatly.sky.creation.domain.common.messaging.consumer.ConsumerConfig;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.aiproduct.image.mapper.ProductAiImageMapper;
import ai.creatly.sky.creation.domain.core.aiproduct.image.model.response.ProductAiImageVM;
import ai.creatly.sky.creation.domain.core.aiproduct.image.model.task.event.ProductAiImageImaginedEvent;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.Validates;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.async.DeferredResult;

/**
 * <AUTHOR>
 * @version AiProductAsyncManagerImpl.java, v 0.1 2024-02-29 下午11:48 zhoudong
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiProductImageAsyncManagerImpl implements ProductImageAsyncManager {

    private final AiTaskRepository     aiTaskRepository;
    private final ProductAiImageMapper productAiImageMapper;

    /**
     * 任务延迟结果缓存
     */
    private final LoadingCache<DeferredKey, CachedValue> DEFERRED_CACHE = Caffeine.newBuilder()
            .expireAfter(DeferredResultExpiry.INSTANCE)
            .build(this::buildCacheValue);

    private CachedValue buildCacheValue(DeferredKey deferredKey) {
        return new CachedValue(deferredKey.newDeferredResult(DEFERRED_CACHE), deferredKey.getStartedAt());
    }

    private record CachedValue(DeferredResult<ApiResult<ProductAiImageVM>> deferredResult, long startedAt) {
    }

    @Override
    public DeferredResult<ApiResult<ProductAiImageVM>> getAiImageResult(long taskId, DeferredContext context) {
        // 限流校验
        Validates.isTrue(DEFERRED_CACHE.estimatedSize() < 5000, CommonErrorCode.SLA_LIMITED);

        // 生成任务存在性校验
        AiTask aiTask = aiTaskRepository.queryById(taskId);

        // 从缓存中懒加载异步结果
        DeferredKey deferredKey = DeferredKey.task(taskId, context);
        DeferredResult<ApiResult<ProductAiImageVM>> deferredResult = DEFERRED_CACHE.get(deferredKey).deferredResult();

        // 如果已经生成结束，则直接设置确定的结果，并失效缓存
        if (aiTask.getStatus().isFinal()) {
            this.onAiImageImagined(aiTask, deferredKey);
        }
        return deferredResult;
    }

    /**
     * 完成异步结果
     *
     * @param aiTask      创作任务
     * @param deferredKey 缓存key
     */
    private void onAiImageImagined(AiTask aiTask, DeferredKey deferredKey) {
        Asserts.isTrue(aiTask.getStatus().isFinal(), "will not happen!");
        long taskId = aiTask.getId();
        CachedValue cachedValue = DEFERRED_CACHE.getIfPresent(deferredKey);
        if (cachedValue == null) {
            return;
        }
        log.info("[onAiImageImagined]result found,taskId={}", taskId);

        final ApiResult<ProductAiImageVM> result = ApiResult.ok(productAiImageMapper.toProductAiImageVM(aiTask));
        cachedValue.deferredResult().setResult(result);

        long actualMills = System.currentTimeMillis() - cachedValue.startedAt();
        log.info("[onAiImageImagined]time cost = {}ms", actualMills);

        // 缓存失效
        DEFERRED_CACHE.invalidate(deferredKey);
    }

    @Override
    public ConsumerConfig consumerConfig() {
        return ConsumerConfig.builder()
                .consumerGroup(AppConstants.APP_NAME + "%AiProductAsyncManager")
                .topic(AppConstants.MQ_TOPIC)
                .tags(AppConstants.MQTags.PRODUCT_AI_IMAGE_IMAGINED)
                // 广播消费模式
                .consumeMethod(ConsumeMethod.BROADCASTING)
                .build();
    }

    @Override
    public ProductAiImageImaginedEvent doConvertMessage(String body) {
        return JSON.parseObject(body, ProductAiImageImaginedEvent.class);
    }

    @Override
    public ConsumeAction onMessage(ProductAiImageImaginedEvent event) {
        long taskId = event.getTaskId();
        // 由于是广播消息，集群里每个节点都会收到，所以需要过滤一下本地是否存在
        DeferredKey deferredKey = DeferredKey.task(taskId);
        CachedValue cachedValue = DEFERRED_CACHE.getIfPresent(deferredKey);
        if (cachedValue == null) {
            return ConsumeAction.CONSUME_OK;
        }
        AiTask aiTask = aiTaskRepository.queryById(taskId);
        this.onAiImageImagined(aiTask, deferredKey);
        return ConsumeAction.CONSUME_OK;
    }
}
