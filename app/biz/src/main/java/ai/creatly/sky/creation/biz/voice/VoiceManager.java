/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.voice;

import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.response.CreditsExpenseVM;
import ai.creatly.sky.creation.domain.core.userfile.model.response.UserFileVM;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceCategory;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceContourPreviewRequest;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceCreationRequest;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceSynthesizeRequest;
import ai.creatly.sky.creation.domain.core.voice.model.response.AliVoiceVM;
import ai.creatly.sky.creation.domain.core.voice.model.response.VoiceVM;
import ai.creatly.sky.creation.domain.core.voice.model.response.task.VoiceConversionTaskVM;
import ai.creatly.sky.creation.domain.core.voice.model.response.task.VoiceCreationTaskVM;
import ai.creatly.sky.creation.domain.core.voice.model.task.VoiceTaskBizType;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.OutputStream;
import java.time.Duration;
import java.util.List;

/**
 * 声音业务管理
 *
 * <AUTHOR>
 * @version VoiceManager.java, v 0.1 2023-11-04 下午8:05 zhoudong
 */
public interface VoiceManager {

    /**
     * 查询已上架的声音列表
     *
     * @param category 声音分类
     * @param uid      用户ID（用于查询用户定制的声音）
     * @return 声音列表
     */
    List<VoiceVM> queryOnlineVoices(VoiceCategory category, long uid);

    /*------------------------------ 合成（简单文本） ------------------------------*/

    /**
     * 通过文本信息评估声音时长（误差5s）
     *
     * @param request -
     * @return -
     */
    Duration estimateAudioDuration(VoiceSynthesizeRequest request);

    /**
     * 提交声音合成任务
     *
     * @param request     请求
     * @param userContext 登录用户上下文
     * @return 任务ID
     */
    String submitSynthesis(VoiceSynthesizeRequest request, UserContext userContext);

    /**
     * 简单文本的语音合成
     *
     * @param request     -
     * @param userContext -
     * @return -
     */
    UserFileVM simpleSynthesize(VoiceSynthesizeRequest request, UserContext userContext);

    /*------------------------------ 创作（结构化文档） ------------------------------*/

    /**
     * 声音语调预览
     *
     * @param request      请求
     * @param userContext  用户登录上下文
     * @param outputStream 输出流
     * @return httpUrl
     */
    String previewContour(VoiceContourPreviewRequest request, UserContext userContext, OutputStream outputStream);

    /**
     * 估算语音创作费用
     *
     * @param request     请求
     * @param userContext 登录用户上下文
     * @return 预估费用
     */
    CreditsExpenseVM estimateCreationExpense(VoiceCreationRequest request, UserContext userContext);

    /**
     * 提交语音创作任务
     *
     * @param request     请求
     * @param userContext 登录用户上下文
     * @return 任务ID
     */
    String submitCreation(VoiceCreationRequest request, UserContext userContext);

    /**
     * 分页查询声音内容创作任务（包含简单文本的合成任务）
     *
     * @param category 声音分类
     * @param uid      用户ID
     * @param statuses 状态列表
     * @param pageable 分页参数
     * @return 任务分页列表
     */
    Page<VoiceCreationTaskVM> queryCreationTaskPage(VoiceCategory category, long uid, List<AiTaskStatus> statuses, Pageable pageable);

    /*------------------------------ 变声 ------------------------------*/

    /**
     * 提交声音变声任务
     *
     * @param voiceId     声音ID
     * @param audioFile   音频文件
     * @param userContext 登录用户上下文
     * @return 任务ID
     */
    String submitConversion(long voiceId, MultipartFile audioFile, UserContext userContext);

    /**
     * 分页查询声音变声任务
     *
     * @param bizType  合成任务业务类型
     * @param uid      用户ID
     * @param statuses 状态列表
     * @param pageable 分页参数
     * @return 变声任务分页列表
     */
    Page<VoiceConversionTaskVM> queryConversionTaskPage(VoiceTaskBizType bizType, long uid, List<AiTaskStatus> statuses, Pageable pageable);

    List<AliVoiceVM> getALiVoices(Long uid);

}
