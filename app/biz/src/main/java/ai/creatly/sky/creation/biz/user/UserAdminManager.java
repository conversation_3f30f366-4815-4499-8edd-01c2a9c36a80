package ai.creatly.sky.creation.biz.user;

import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.UserCreditAccount;
import ai.creatly.sky.creation.domain.core.credit.model.UserWithCreditVM;
import ai.creatly.sky.creation.domain.core.credit.repository.UserCreditRepository;
import ai.creatly.sky.creation.domain.core.user.admin.mapper.UserAdminMapper;
import ai.creatly.sky.creation.domain.core.user.admin.model.UserQuery;
import ai.creatly.sky.creation.domain.core.user.admin.model.UserVM;
import ai.creatly.sky.creation.domain.core.user.admin.service.UserAdminService;
import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import ai.creatly.sky.creation.domain.core.user.repository.UserRepository;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserAdminManager {

    @Autowired
    UserCreditRepository userCreditRepository;
    @Autowired
    UserRepository       userRepository;
    @Autowired
    UserAdminService     userAdminService;
    @Autowired
    UserAdminMapper      userAdminMapper;

    public boolean addCredit(long uid, int credit){

        //检查用户是否存在
        Optional<UserInfo> user = userRepository.getUserById(uid);
        if (user.isEmpty()) {
            return false;
        }

        List<UserCreditAccount> creditAccounts = userCreditRepository.queryUserValidCreditAccounts(uid, ZonedDateTime.now());
        if (!creditAccounts.isEmpty()) {
            int creditAmount = userCreditRepository.getUserCredits(uid);
            return userCreditRepository.plusCredits(creditAccounts.getFirst().getId(), credit) &&
            userAdminService.addCreditLog(uid,credit, creditAccounts.getFirst().getId(), creditAmount);
        }
        else {
            UserCreditAccount userCreditAccount = new UserCreditAccount();
            userCreditAccount.setBalance(credit);
            userCreditAccount.setType(CreditAccountType.GENERAL);
            userCreditAccount.setEffectAt(ZonedDateTime.now());
            userCreditAccount.setUid(uid);
            long creditId;
            try {
                creditId = userCreditRepository.create(userCreditAccount);
            } catch (Exception e) {
                log.error("添加元气失败", e);
                return false;
            }
            return userAdminService.addCreditLog(uid, credit, creditId, 0);
        }
    }

    public Page<UserWithCreditVM> searchUser(Pageable pageable, UserQuery query) {
        Page<UserInfo> users = userAdminService.searchUser(pageable, query);
        if (users!=null) {
            return userInfoPage2VM(users.map(userAdminMapper::toUserVM));
        }
        else {
            return null;
        }
    }

    public Page<UserWithCreditVM> userInfoPage2VM(Page<UserVM> users){
        return users.map(userVM -> {
            UserWithCreditVM userWithCreditVM = new UserWithCreditVM();
            BeanUtils.copyProperties(userVM, userWithCreditVM);
            Integer credit = userCreditRepository.getUserCredits(Long.parseLong(userVM.getId()));
            userWithCreditVM.setCredit(credit);
            return userWithCreditVM;
        });
    }

}
