/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.story;

import ai.creatly.sky.creation.domain.common.ddd.ApplicationService;
import ai.creatly.sky.creation.domain.core.story.mapper.StoryVMMapper;
import ai.creatly.sky.creation.domain.core.story.model.shot.CameraMovement;
import ai.creatly.sky.creation.domain.core.story.model.shot.response.StoryCameraMovementVM;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryStyleValue;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryType;
import ai.creatly.sky.creation.domain.core.story.model.story.enums.ArtisticStyleType;
import ai.creatly.sky.creation.domain.core.story.model.story.enums.HistoricalBackgroundType;
import ai.creatly.sky.creation.domain.core.story.model.story.response.StoryTopicVM;
import ai.creatly.sky.creation.domain.core.story.model.story.response.StyleValueVM;
import ai.creatly.sky.creation.domain.core.story.service.StoryTypeDescriptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version StoryConfigManager.java, v 0.1 2024-04-30 下午5:46 zhoudong
 */
@ApplicationService
@RequiredArgsConstructor
@Slf4j
public class StoryConfigManager {

    /**
     * 历史背景
     */
    public static final String HISTORICAL_BACKGROUND = "historicalBackground";
    /**
     * 艺术风格
     */
    public static final String ARTISTIC_STYLE        = "artisticStyle";

    private final StoryTypeDescriptor storyTypeDescriptor;
    private final StoryVMMapper       storyVMMapper;

    /**
     * 获取故事类型列表
     */
    public List<StoryTopicVM> getStoryTopics() {
        List<StoryType> allStoryType = storyTypeDescriptor.getAllStoryType();
        return allStoryType.stream().map(storyVMMapper::toStoryTypeVM).toList();
    }

    /**
     * 获取故事类型列表
     */
    public List<StyleValueVM> getStoryStyleList(String styleKey) {
        List<StoryStyleValue> styleValues = switch (styleKey) {
            case HISTORICAL_BACKGROUND -> Arrays.stream(HistoricalBackgroundType.values())
                    .map(HistoricalBackgroundType::buildStyleValue)
                    .collect(toList());
            case ARTISTIC_STYLE -> Arrays.stream(ArtisticStyleType.values())
                    .map(ArtisticStyleType::buildStyleValue)
                    .collect(toList());
            default -> throw new IllegalArgumentException("Invalid styleKey: " + styleKey);
        };
        return styleValues.stream().map(storyVMMapper::toStyleValueVM).toList();
    }

    public List<StoryCameraMovementVM> queryStoryCameraMovement() {
        List<CameraMovement> styleValues = CameraMovement.getAvailableCameraMovement();
        return styleValues.stream().map(storyVMMapper::toStoryCameraMovementVM).toList();
    }
}
