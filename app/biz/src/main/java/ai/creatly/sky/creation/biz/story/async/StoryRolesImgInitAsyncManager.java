/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.story.async;

import ai.creatly.sky.creation.biz.common.deferred.DeferredContext;
import ai.creatly.sky.creation.biz.common.deferred.DeferredKey;
import ai.creatly.sky.creation.biz.common.deferred.DeferredResultExpiry;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.story.error.StoryErrorCode;
import ai.creatly.sky.creation.domain.core.story.mapper.StoryRoleVMMapper;
import ai.creatly.sky.creation.domain.core.story.model.role.response.StoryRoleInitTaskVM;
import ai.creatly.sky.creation.domain.core.story.model.role.response.StoryRoleVM;
import ai.creatly.sky.creation.domain.core.story.model.role.task.StoryRoleImageInitEvent;
import ai.creatly.sky.creation.domain.core.story.model.story.Story;
import ai.creatly.sky.creation.domain.core.story.repository.StoryRepository;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.List;

/**
 * 角色图初始化生成异步任务控制器（多角色批量生图）
 *
 * <AUTHOR>
 * @version StoryRolesImgInitAsyncManager.java, v 0.1 2024-03-29 14:43 syoka
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class StoryRolesImgInitAsyncManager {

    private final StoryRepository   storyRepository;
    private final AiTaskRepository  aiTaskRepository;
    private final StoryRoleVMMapper storyRoleVMMapper;

    /**
     * 任务延迟结果缓存
     */
    private final LoadingCache<DeferredKey, CachedValue> DEFERRED_CACHE = Caffeine.newBuilder()
            .expireAfter(DeferredResultExpiry.INSTANCE)
            .build(this::buildCacheValue);

    private CachedValue buildCacheValue(DeferredKey deferredKey) {
        return new CachedValue(deferredKey.newDeferredResult(DEFERRED_CACHE), deferredKey.getStartedAt());
    }

    private record CachedValue(DeferredResult<ApiResult<StoryRoleInitTaskVM>> deferredResult, long startedAt) {
    }

    public DeferredResult<ApiResult<StoryRoleInitTaskVM>> futureStoryRolesImg(AiTask aiTask, DeferredContext context) {
        // 限流校验
        Validates.isTrue(DEFERRED_CACHE.estimatedSize() < 100, CommonErrorCode.SLA_LIMITED);

        // 从缓存中懒加载异步结果
        DeferredKey deferredKey = DeferredKey.task(aiTask.getId(), context);
        DeferredResult<ApiResult<StoryRoleInitTaskVM>> deferredResult = DEFERRED_CACHE.get(deferredKey).deferredResult();

        // 如果已经生成结束，则直接设置确定的结果，并失效缓存
        if (aiTask.getStatus().isFinal()) {
            this.onStoryRoleInitFinished(aiTask);
        }
        return deferredResult;
    }

    private void onStoryRoleInitFinished(AiTask aiTask) {
        Asserts.isTrue(aiTask.getStatus().isFinal(), "will not happen!");
        long taskId = aiTask.getId();
        CachedValue cachedValue = DEFERRED_CACHE.getIfPresent(DeferredKey.task(taskId));
        if (cachedValue == null) {
            return;
        }
        log.info("[onStoryRoleInitFinished]result found,taskId={}", taskId);

        if (aiTask.getStatus() == AiTaskStatus.CANCELED || "FAILURE".equals(aiTask.getBizStatus())) {
            cachedValue.deferredResult().setResult(ApiResult.err(StoryErrorCode.STORY_ROLE_IMG_INIT_FAILURE));
        } else {
            Story story = storyRepository.findStory(aiTask.getOwnerId(), Long.parseLong(aiTask.getBizNo()));

            List<StoryRoleVM> storyRoleVMs = storyRoleVMMapper.toStoryRoleVMs(story.getRoles());
            StoryRoleInitTaskVM taskVM = new StoryRoleInitTaskVM()
                    .setInitialized(true)
                    .setRoles(storyRoleVMs)
                    .setTaskId(String.valueOf(aiTask.getId()))
                    .setTaskStatus(aiTask.getBizStatus());
            cachedValue.deferredResult().setResult(ApiResult.ok(taskVM));
        }

        long actualMills = System.currentTimeMillis() - cachedValue.startedAt();
        log.info("[onStoryRoleInitFinished]elapsed={}ms", actualMills);

        // 缓存失效
        DEFERRED_CACHE.invalidate(DeferredKey.task(taskId));
    }

    @EventListener(StoryRoleImageInitEvent.class)
    public void onApplicationEvent(StoryRoleImageInitEvent event) {
        long taskId = event.getTaskId();
        AiTask aiTask = aiTaskRepository.queryById(taskId);
        this.onStoryRoleInitFinished(aiTask);
    }
}
