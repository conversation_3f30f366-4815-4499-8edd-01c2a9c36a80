/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.biz.voice.task.creation;

import ai.creatly.sky.creation.domain.common.integration.azure.model.SynthesisVoiceNameEnum;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.userfile.model.HttpFile;
import ai.creatly.sky.creation.domain.core.voice.mapper.VoiceCreationMapper;
import ai.creatly.sky.creation.domain.core.voice.mapper.VoiceCreationTaskMapper;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceCreationRequest;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.VoiceCreationContent;
import ai.creatly.sky.creation.domain.core.voice.model.task.VoiceTaskBizType;
import ai.creatly.sky.creation.domain.core.voice.model.task.param.VoiceCreationParams;
import ai.creatly.sky.creation.domain.core.voice.repository.VoiceRepository;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version BasicVoiceCreationTaskHandler.java, v 0.1 2024-06-25 下午5:54 zhoudong
 */
@Slf4j
//@Component
@RequiredArgsConstructor
public class BasicVoiceCreationTaskHandler extends AbstractBasicVoiceCreationTaskHandler implements BasicVoiceCreationHandler {

    private final VoiceRepository         voiceRepository;
    private final VoiceCreationMapper     voiceCreationMapper;
    private final VoiceCreationTaskMapper voiceCreationTaskMapper;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.CREATE_AI_AUDIO)
                .setBizType(VoiceTaskBizType.basic_creation.getCode())
                .setQueueMode(TaskQueueMode.POP_UNTIL_COMPLETED)
                .setLoadSize(10);
    }

    protected @NotNull VoiceCreationContent buildCreationContent(AiTask aiTask) {
        var params = aiTask.parseBizParams(VoiceCreationParams.class);
        VoiceCreationRequest request = voiceCreationTaskMapper.toCreationRequest(params);

        Set<Long> voiceIds = VoiceCreationMapper.getAllVoiceIds(request.getDialogues());
        // TODO 待统一
        Map<Long, Voice> voiceMap = voiceIds.stream()
                .map(SynthesisVoiceNameEnum::getVoiceById)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Voice::getId, Function.identity()));
        if (voiceMap.isEmpty()) {
            voiceMap = voiceRepository.queryMapByIds(voiceIds);
        }
        Asserts.isTrue(voiceMap.size() == voiceIds.size(), "will not happen!");

        Set<Long> fileIds = VoiceCreationMapper.getAllFileIds(request);
        Map<Long, HttpFile> fileMap = userFileService.queryHttpFileMapByIds(aiTask.getOwnerId(), fileIds);

        return voiceCreationMapper.toVoiceCreationContent(request, voiceMap, fileMap);
    }
}
