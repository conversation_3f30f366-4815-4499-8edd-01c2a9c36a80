/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.admin;

import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.ai.exam.model.AiExamCert;
import ai.creatly.sky.creation.domain.core.ai.exam.repository.AiCertRepository;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.course.model.AiCourse;
import ai.creatly.sky.creation.domain.core.course.model.CourseBizErrorCode;
import ai.creatly.sky.creation.domain.core.course.model.CourseOrgSettingVM;
import ai.creatly.sky.creation.domain.core.course.service.AiCourseRepository;
import ai.creatly.sky.creation.domain.core.course.service.CourseOrgSettingRepository;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.data.result.ApiResult;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机构开课管理
 * <AUTHOR>
 * @version OrgCourseResource.java, 2024-12-17 下午6:56 zhoudong
 */
@RestController
@RequestMapping(value = "/api/admin/org/course/setting", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class OrgCourseSettingResource {

    private final CourseOrgSettingRepository courseOrgSettingRepository;
    private final AiCourseRepository aiCourseRepository;
    private final AiCertRepository aiCertRepository;


    /**
     * 查询课程列表
     */
    @GetMapping("/course")
    public ApiResult<List<AiCourse>> getCourseList() {
        return ApiResult.ok(aiCourseRepository.queryCourseList());
    }


    /**
     * 查询课程证书模板
     */
    @GetMapping("/course/cert/{orgCode}/{courseId}")
    public ApiResult<AiExamCert> getCourseCert(@PathVariable String courseId, @PathVariable String orgCode) {
        return ApiResult.ok(aiCertRepository.getCertByCourseId(Long.parseLong(courseId), orgCode));
    }

    /**
     * 保存课程证书模板
     */
    @PostMapping("/course/cert")
    public ApiResult<AiExamCert> saveCourseCert(@RequestBody AiExamCert aiExamCert) {
        return ApiResult.ok(aiCertRepository.saveOrUpdate(aiExamCert));
    }

    /**
     * 查询所有机构开课列表
     */
    @GetMapping("/list")
    public ApiResult<List<CourseOrgSettingVM>> getList() {
        UserContext userContext = UserContextUtils.getUserContext();
        if (userContext.getUserOrganization().getOrgCode().equals(AppConstants.DEFAULT_ORG)) {
            return ApiResult.ok(courseOrgSettingRepository.getList());
        } else {
            return ApiResult.ok(courseOrgSettingRepository.getByOrgCode(userContext.getUserOrganization().getOrgCode()));
        }
    }

    /**
     * 机构开课设置
     */
    @PostMapping("/add")
    public ApiResult<CourseOrgSettingVM> add(@RequestBody CourseOrgSettingVM courseOrgSettingVM) {
        if (courseOrgSettingRepository.getByOrgCode(courseOrgSettingVM.getOrgCode(), courseOrgSettingVM.getCourseId()) != null) {
            throw new BizException(CourseBizErrorCode.ORG_COURSE_EXIST);
        }
        return ApiResult.ok(courseOrgSettingRepository.saveOrUpdate(courseOrgSettingVM));
    }

    /**
     * 机构开课编辑
     */
    @PostMapping("/edit")
    public ApiResult<CourseOrgSettingVM> edit(@RequestBody CourseOrgSettingVM courseOrgSettingVM) {
        return ApiResult.ok(courseOrgSettingRepository.saveOrUpdate(courseOrgSettingVM));
    }

    /**
     * 机构开课情况
     */
    @GetMapping("/{orgCode}")
    public ApiResult<List<CourseOrgSettingVM>> getByCode(@PathVariable String orgCode) {
        return ApiResult.ok(courseOrgSettingRepository.getByOrgCode(orgCode));
    }


}
