/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.common.problem.servlet;

import ai.creatly.sky.creation.api.common.util.MvcUtil;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.util.json.JSON;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.boot.web.error.ErrorAttributeOptions;
import org.springframework.boot.web.servlet.error.DefaultErrorAttributes;
import org.springframework.http.HttpStatus;
import org.springframework.http.ProblemDetail;
import org.springframework.stereotype.Component;
import org.springframework.web.ErrorResponse;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;

import java.util.Map;

/**
 * <AUTHOR>
 * @version GlobalErrorAttributes.java, v 0.1 2023-03-11 14:55 joton
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GlobalWebErrorAttributes extends DefaultErrorAttributes {

    /**
     * @param webRequest the source request
     * @param options    options for error attribute contents
     */
    @Override
    public Map<String, Object> getErrorAttributes(WebRequest webRequest, ErrorAttributeOptions options) {
        Map<String, Object> errorAttributes = super.getErrorAttributes(webRequest, options);

        final String errorCode;
        String errorMsg;
        Integer status = (Integer) webRequest.getAttribute(RequestDispatcher.ERROR_STATUS_CODE, RequestAttributes.SCOPE_REQUEST);
        if (status == null) {
            errorCode = CommonErrorCode.UNSPECIFIED.getCode();
            errorMsg = HttpStatus.SERVICE_UNAVAILABLE.getReasonPhrase();
        } else {
            errorCode = HttpStatus.valueOf(status).name();
            errorMsg = HttpStatus.valueOf(status).getReasonPhrase();
            if (HttpStatus.valueOf(status).is4xxClientError()) {
                String message = (String) errorAttributes.get("message");
                if (!"No message available".equals(message)) {
                    errorMsg = message;
                }
            }
        }

        // 打印日志
        Throwable error = super.getError(webRequest);
        HttpServletRequest request = webRequest instanceof ServletWebRequest servletWebRequest ? servletWebRequest.getRequest() : null;
        this.log(request, errorMsg, error instanceof Throwable t ? t : null);

        return JSON.toMap(ApiResult.err(errorCode, errorMsg));
    }

    private void log(HttpServletRequest request, String errorMsg, @Nullable Throwable t) {
        String requestLog = MvcUtil.toLogMessage(request);
        if (t instanceof ErrorResponse) {
            ProblemDetail body = ((ErrorResponse) t).getBody();
            log.warn("Exception occurred,errorMsg={},request={},{}", errorMsg, requestLog, body, t);
        } else if (t != null) {
            log.warn("Exception occurred,errorMsg={},request={}", errorMsg, requestLog, t);
        } else {
            log.warn("Exception occurred,errorMsg={},request={}", errorMsg, requestLog);
        }
    }
}
