/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.common.async;

import ai.creatly.sky.creation.api.common.problem.servlet.GlobalWebErrorController;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.util.Assert;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.AsyncHandlerMethodReturnValueHandler;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor;
import org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod;

/**
 * 异步的响应值处理器在所有同步的响应值处理器之前执行
 *
 * <AUTHOR>
 * @version ConcurrentResultReturnValueHandler.java, v 0.1 2024-06-21 上午2:53 zhoudong
 */
@RequiredArgsConstructor
public class ConcurrentResultReturnValueHandler implements AsyncHandlerMethodReturnValueHandler {

    private final RequestResponseBodyMethodProcessor delegate;

    private final static String RETURN_TYPE_NAME = ServletInvocableHandlerMethod.class.getName() + "$ConcurrentResultMethodParameter";

    @Override
    public boolean isAsyncReturnValue(Object returnValue, @NotNull MethodParameter returnType) {
        return this.supportsReturnType(returnType);
    }

    @Override
    public boolean supportsReturnType(@NotNull MethodParameter returnType) {
        // 这种情况是因为进入到了 ERROR Dispatch 中，又拿到了业务线程抢先设置好的异步结果，这种情况需要特殊处理，否则无法将响应状态从 500 改为 200
        return GlobalWebErrorController.class.equals(returnType.getContainingClass())
                && returnType.getClass().getName().equals(RETURN_TYPE_NAME);
    }

    @Override
    public void handleReturnValue(Object returnValue, @NotNull MethodParameter returnType, @NotNull ModelAndViewContainer mavContainer,
                                  @NotNull NativeWebRequest webRequest) throws Exception {
        HttpServletResponse response = webRequest.getNativeResponse(HttpServletResponse.class);
        Assert.state(response != null, "No HttpServletResponse");
        response.setStatus(HttpStatus.OK.value());
        delegate.handleReturnValue(returnValue, returnType, mavContainer, webRequest);
    }
}
