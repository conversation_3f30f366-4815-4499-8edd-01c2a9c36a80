/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure.user.story;

import ai.creatly.sky.creation.api.common.async.WebAsyncTaskHelper;
import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.biz.story.StorySceneManager;
import ai.creatly.sky.creation.domain.common.async.AsyncPools;
import ai.creatly.sky.creation.domain.common.async.AsyncTimeout;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.story.error.StoryErrorCode;
import ai.creatly.sky.creation.domain.core.story.model.scene.response.StorySceneVM;
import com.jspeeder.core.data.result.ApiListResult;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.WebAsyncTask;

import java.util.List;

/**
 * AI故事-场景接口
 *
 * <AUTHOR>
 * @version UserStorySceneResource.java, v 0.1 2024-03-29 11:11 syoka
 */
@RequestMapping(value = "/api/user/story/{storyId}/scenes", produces = MediaType.APPLICATION_JSON_VALUE)
@Slf4j
@RestController
@RequiredArgsConstructor
public class StorySceneUserResource {

    private final StorySceneManager  storySceneManager;
    private final AsyncTimeout       asyncTimeout;
    private final WebAsyncTaskHelper webAsyncTaskHelper;

    /**
     * 基于故事初始化故事板
     */
    @PostMapping
    public WebAsyncTask<ApiListResult<StorySceneVM>> initStoryScenes(@PathVariable String storyId) {
        UserContext userContext = UserContextUtils.getUserContext();
        return webAsyncTaskHelper.builder()
                .timeout(asyncTimeout.ofSeconds(180))
                .timeoutErrorCode(StoryErrorCode.STORY_SHOT_INFO_INIT_TIMEOUT)
                .threadPoolName(AsyncPools.AI_STORY_SCENE_SHOT_INIT_POOL)
                .futureListResult(() -> storySceneManager.initStoryScenes(userContext, Validates.requireLong(storyId, "storyId")))
                .build();
    }

    /**
     * 查看故事的场景列表
     */
    @GetMapping
    public ApiListResult<StorySceneVM> getStoryScenes(@PathVariable String storyId) {
        List<StorySceneVM> storyScenes = storySceneManager.queryStoryScenes(Validates.requireLong(storyId, "storyId"));
        return ApiListResult.ok(storyScenes);
    }
}
