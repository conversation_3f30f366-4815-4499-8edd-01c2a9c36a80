/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.pub;

import ai.creatly.sky.creation.api.common.async.DeferredResultHelper;
import ai.creatly.sky.creation.biz.auth.WechatAuthAsyncManager;
import ai.creatly.sky.creation.domain.common.env.RuntimeEnv;
import ai.creatly.sky.creation.domain.core.auth.model.WechatAppName;
import ai.creatly.sky.creation.domain.core.auth.model.response.WechatSignInH5Param;
import ai.creatly.sky.creation.domain.core.auth.model.response.WechatSignInVM;
import ai.creatly.sky.creation.domain.core.auth.model.response.WechatSignInWebParam;
import ai.creatly.sky.creation.domain.core.auth.service.WechatAuthService;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.util.Validates;
import com.jspeeder.core.util.text.FormatUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.servlet.view.RedirectView;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.web.util.UriUtils;

import java.nio.charset.StandardCharsets;
import java.time.Duration;

/**
 * <AUTHOR>
 * @version WechatAuthResource.java, v 0.1 2024-06-17 下午4:27 zhoudong
 */
@Controller
@RequestMapping(value = "/api/auth/wechat", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class WechatAuthResource {

    private final WechatAuthService      wechatAuthService;
    private final DeferredResultHelper   deferredResultHelper;
    private final WechatAuthAsyncManager wechatAuthAsyncManager;
    private final RuntimeEnv             runtimeEnv;

    /**
     * 获取PC端微信扫码登录所需参数
     * </p>
     * 扫码后的回调：{@link WechatMpResource#receive(HttpServletRequest, HttpHeaders, String)}  }
     */
    @GetMapping(value = "/sign-in/code", params = "from=website")
    @ResponseBody
    public ApiResult<WechatSignInWebParam> getWechatSignInPageParam() {
        WechatSignInWebParam param = wechatAuthService.getWechatSignInWebScanParam(WechatAppName.mp_zxy, Duration.ofMinutes(5));
        return ApiResult.ok(param);
    }

    /**
     * 获取H5网页端微信登录所需参数
     *
     * @param redirectUrl 微信授权后的跳转地址
     */
    @GetMapping(value = "/sign-in/code", params = "from=wechat_h5")
    @ResponseBody
    public ApiResult<WechatSignInH5Param> getWechatSignInPageParam(@RequestParam String redirectUrl) {
        WechatSignInH5Param param = wechatAuthService.getWechatSignInH5Param(Duration.ofMinutes(5));
        // 在公网nginx里根据不同环境前缀转发到不同的后端地址上
        String env = runtimeEnv.getEnv().name().toLowerCase();
        redirectUrl = UriComponentsBuilder.fromHttpUrl(redirectUrl).queryParam("sid", param.getSid()).build().toString();
        redirectUrl = UriUtils.encode(redirectUrl, StandardCharsets.UTF_8);
        param.setRedirectPath(FormatUtil.format("/{}/api/auth/wechat/oauth2/callback/h5?redirect={}", env, redirectUrl));
        return ApiResult.ok(param);
    }

    /**
     * 微信回调登录接口（H5应用回调）
     *
     * @param code  用户登录后的回调code
     * @param state 自定义state参数
     */
    @GetMapping("/oauth2/callback/h5")
    public RedirectView wechatLoginCallbackH5(@RequestParam String code, @RequestParam String state, @RequestParam String redirect) {
        // 获取微信原生的access_token，并置换为平台自己的token
        wechatAuthService.handleWechatLoginCallback(code, state, WechatAppName.mp_zxy);
        return new RedirectView(redirect);
    }

    /**
     * 前端感知微信登陆接口
     * 长轮询：当终端扫码登陆成功后异步同步
     *
     * @return 真实toke或匿名token
     */
    @PostMapping("/sign-in/result")
    @ResponseBody
    public DeferredResult<ApiResult<WechatSignInVM>> getWechatLoginResult(@RequestParam String sid) {
        Validates.notBlank(sid, "sid 不能为空");
        return deferredResultHelper.builder()
                .timeout(Duration.ofSeconds(290))
                .timeoutErrorCode(UserErrorCode.USER_SIGN_IN_SCAN_CODE_TIMEOUT)
                .timeoutAlert(false)
                .result(context -> wechatAuthAsyncManager.getWechatLoginResult(sid, context))
                .build();
    }
}
