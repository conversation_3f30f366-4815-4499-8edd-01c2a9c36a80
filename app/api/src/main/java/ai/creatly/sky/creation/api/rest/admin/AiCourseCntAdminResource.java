/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.admin;

import ai.creatly.sky.creation.api.common.openapi.PageQueryParam;
import ai.creatly.sky.creation.domain.core.course.model.AiCourseContent;
import ai.creatly.sky.creation.domain.core.course.service.AiCourseContentRepository;
import com.jspeeder.core.data.result.ApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 课件管理
 *
 * <AUTHOR>
 * @version AiCourseCntAdminResource.java, v 0.1 2024-05-06 上午11:59 yongliang.j
 */
@RestController
@Slf4j
@RequestMapping(value = "/api/admin/course/content", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class AiCourseCntAdminResource {

    private final AiCourseContentRepository aiCourseContentRepository;


    /**
     * 获取课件列表
     * @return
     */
    @GetMapping("/list/{courseId}")
    @PageQueryParam
    public ApiResult<List<AiCourseContent>> getList(@PathVariable String courseId) {
        return ApiResult.ok(aiCourseContentRepository.queryCourseContentList(Long.parseLong(courseId)));
    }

    /**
     * 获取课件详情
     * @return
     */
    @GetMapping("/detail/{courseContentId}")
    public ApiResult<AiCourseContent> getCourse(@PathVariable String courseContentId) {
        return ApiResult.ok(aiCourseContentRepository.queryById(Long.parseLong(courseContentId)));
    }

    /**
     * 新增课件
     * @return
     */
    @PostMapping("/add")
    public ApiResult<AiCourseContent> addCourseContent(@RequestBody AiCourseContent aiCourseContent) {
        return ApiResult.ok(aiCourseContentRepository.addCourseContent(aiCourseContent));
    }

    /**
     * 编辑课件
     * @return
     */
    @PostMapping("/edit")
    public ApiResult<AiCourseContent> editCourseContent(@RequestBody AiCourseContent aiCourseContent) {
        return ApiResult.ok(aiCourseContentRepository.updateCourseContent(aiCourseContent));
    }

}
