/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure.user.story;

import ai.creatly.sky.creation.api.common.async.WebAsyncTaskHelper;
import ai.creatly.sky.creation.api.common.async.sse.SseEmitterHelper;
import ai.creatly.sky.creation.api.common.openapi.PageQueryParam;
import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.biz.story.StoryManager;
import ai.creatly.sky.creation.biz.story.async.StoryScriptAsyncManager;
import ai.creatly.sky.creation.domain.common.async.AsyncPools;
import ai.creatly.sky.creation.domain.common.async.AsyncTimeout;
import ai.creatly.sky.creation.domain.common.async.EventStream;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.story.error.StoryErrorCode;
import ai.creatly.sky.creation.domain.core.story.model.story.request.StoryCreateRequest;
import ai.creatly.sky.creation.domain.core.story.model.story.request.StoryGenerateRequest;
import ai.creatly.sky.creation.domain.core.story.model.story.request.StoryUpdateRequest;
import ai.creatly.sky.creation.domain.core.story.model.story.response.StoryDetailVM;
import ai.creatly.sky.creation.domain.core.story.model.story.response.StoryJYDraftVM;
import ai.creatly.sky.creation.domain.core.story.model.story.response.StoryVM;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.PageBuilder;
import com.jspeeder.core.data.result.ApiPageResult;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.data.result.ApiVoidResult;
import com.jspeeder.core.util.Validates;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.WebAsyncTask;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * AI故事接口
 *
 * <AUTHOR>
 * @version StoryUserResource.java, v 0.1 2024-03-02 11:24 heb
 */
@RequestMapping(value = "/api/user/story", produces = MediaType.APPLICATION_JSON_VALUE)
@Slf4j
@RestController
@RequiredArgsConstructor
public class StoryUserResource {

    private final StoryManager            storyManager;
    private final StoryScriptAsyncManager storyScriptAsyncManager;
    private final AsyncTimeout            asyncTimeout;
    private final SseEmitterHelper        sseEmitterHelper;
    private final WebAsyncTaskHelper      webAsyncTaskHelper;

    /**
     * 故事列表
     *
     * @param pageable 分页参数
     * @return 故事列表
     */
    @GetMapping("/list")
    @PageQueryParam
    public ApiPageResult<StoryVM> queryStoryPage(@PageableDefault Pageable pageable) {
        long uid = UserContextUtils.getOuid();
        Page<StoryVM> page = storyManager.queryStoryPage(uid, PageBuilder.build(pageable));
        return ApiPageResult.ok(page);
    }

    /**
     * 故事剧本生成
     * 1. 用户存在已有故事，因此不会调用此接口
     * 2. 用户没有故事或仅有一个概念,通过概念和故事类型牵引出内容，这就是这个接口做的事情
     */
    @PostMapping("/draft")
    public WebAsyncTask<ApiResult<String>> generateStoryScript(@Valid @RequestBody StoryGenerateRequest request) {
        long uid = UserContextUtils.getOuid();
        return webAsyncTaskHelper.builder()
                .timeout(asyncTimeout.ofSeconds(90))
                .timeoutErrorCode(StoryErrorCode.STORY_DRAFT_DOWNLOAD_TIMEOUT)
                .threadPoolName(AsyncPools.STORY_DRAFT_DOWN)
                .futureResult(() -> storyManager.generateStoryScript(request, uid))
                .build();
    }

    /**
     * 故事剧本生成
     * 1. 用户存在已有故事，因此不会调用此接口
     * 2. 用户没有故事或仅有一个概念,通过概念和故事类型牵引出内容，这就是这个接口做的事情
     */
    @PostMapping(value = "/draft-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generateStoryDraftStream(@Valid @RequestBody StoryGenerateRequest request) {
        UserContext userContext = UserContextUtils.getUserContext();
        SseEmitter sseEmitter = new SseEmitter(asyncTimeout.ofSeconds(60).toMillis());
        EventStream producer = sseEmitterHelper.toStreamingProducer(sseEmitter, userContext);
        storyScriptAsyncManager.generateStoryDraft(request, producer);
        return sseEmitter;
    }

    /**
     * 故事定稿(包含故事相关的基础配置)
     */
    @PostMapping
    public ApiResult<String> createStory(@Valid @RequestBody StoryCreateRequest request) {
        UserContext userContext = UserContextUtils.getUserContext();
        String storyId = storyManager.createStory(request, userContext);
        return ApiResult.ok(storyId);
    }

    /**
     * 故事更新
     */
    @PutMapping("/{storyId}")
    public ApiResult<String> updateStory(@PathVariable String storyId, @Valid @RequestBody StoryUpdateRequest request) {
        UserContext userContext = UserContextUtils.getUserContext();
        storyManager.updateStory(Validates.requireLong(storyId, "storyId"), request, userContext.getouid());
        return ApiResult.ok(storyId);
    }

    /**
     * 故事预览
     */
    @GetMapping("/{storyId}")
    public ApiResult<StoryDetailVM> getStory(@PathVariable String storyId) {
        UserContext userContext = UserContextUtils.getUserContext();
        StoryDetailVM storyDetailVM = storyManager.getStoryDetail(userContext, Validates.requireLong(storyId, "storyId"));
        return ApiResult.ok(storyDetailVM);
    }

    /**
     * 故事删除（软删除）
     */
    @DeleteMapping("/{storyId}")
    public ApiVoidResult deleteStory(@PathVariable String storyId) {
        UserContext userContext = UserContextUtils.getUserContext();
        storyManager.deleteStory(userContext, Validates.requireLong(storyId, "storyId"));
        return ApiVoidResult.ok();
    }

    /**
     * 故事导出到剪映草稿
     */
    @Deprecated
    @GetMapping("/{storyId}/download")
    public WebAsyncTask<ApiResult<StoryJYDraftVM>> downloadStoryDraft(@PathVariable String storyId) {
        UserContext userContext = UserContextUtils.getUserContext();
        return webAsyncTaskHelper.builder()
                .timeout(asyncTimeout.ofSeconds(90))
                .timeoutErrorCode(StoryErrorCode.STORY_DRAFT_DOWNLOAD_TIMEOUT)
                .threadPoolName(AsyncPools.STORY_DRAFT_DOWN)
                .futureResult(() -> storyManager.exportToJyDraft(userContext, Validates.requireLong(storyId, "storyId")))
                .build();
    }

    /**
     * 故事导出到剪映草稿
     */
    @GetMapping("/{storyId}/export-to-jy-draft")
    public WebAsyncTask<ApiResult<StoryJYDraftVM>> exportToJyDraft(@PathVariable String storyId) {
        UserContext userContext = UserContextUtils.getUserContext();
        return webAsyncTaskHelper.builder()
                .timeout(asyncTimeout.ofSeconds(90))
                .timeoutErrorCode(StoryErrorCode.STORY_DRAFT_DOWNLOAD_TIMEOUT)
                .threadPoolName(AsyncPools.STORY_DRAFT_DOWN)
                .futureResult(() -> storyManager.exportToJyDraft(userContext, Validates.requireLong(storyId, "storyId")))
                .build();
    }
}
