/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure.user;

import ai.creatly.sky.creation.api.common.openapi.PageQueryParam;
import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.biz.ai.audio.AudioTtsManager;
import ai.creatly.sky.creation.biz.userfile.UserFileManager;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioTtsRequest;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioTtsVM;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.response.UserFileVM;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.PageBuilder;
import com.jspeeder.core.data.result.ApiPageResult;
import com.jspeeder.core.data.result.ApiResult;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 声音克隆
 *
 * <AUTHOR>
 * @version AiAudioUserResource.java, v0.1 2025-02-19 21:24
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/user/ai-audio", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Deprecated
public class AiAudioUserResource {

    private final UserFileManager   userFileManager;
    private final AudioTtsManager audioCloneManager;

    /**
     * 声音克隆用户输入音频
     *
     * @return 任务ID
     */
    @PostMapping(path = "/refer/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResult<UserFileVM> uploadReferAudio(@RequestPart MultipartFile file) {
        UserContext userContext = UserContextUtils.getUserContext();
        FileType fileType = FileType.AUDIO;
        FileBizSource bizSource = FileBizSource.USER_VOICE_INPUT;
        UserFileVM userFile = userFileManager.uploadAndCreate(file, fileType, bizSource, userContext);
        return ApiResult.ok(userFile);
    }

    /**
     * 上传声音克隆头像
     *
     * @return 任务ID
     */
    @PostMapping(path = "/photo/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResult<UserFileVM> uploadPhotoImage(@RequestPart MultipartFile file) {
        UserContext userContext = UserContextUtils.getUserContext();
        FileType fileType = FileType.IMAGE;
        FileBizSource bizSource = FileBizSource.USER_VOICE_AVATAR;
        UserFileVM userFile = userFileManager.uploadAndCreate(file, fileType, bizSource, userContext);
        return ApiResult.ok(userFile);
    }

    /**
     * 声音克隆
     *
     * @param request 请求
     * @return 任务ID
     */
    @PostMapping(path = "/clone", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ApiResult<String> submitAudioGeneration(@RequestBody @Valid AudioTtsRequest request) {
        UserContext userContext = UserContextUtils.getUserContext();
        long taskId = audioCloneManager.submitGeneration(userContext, request);
        return ApiResult.ok(Long.toString(taskId));
    }

    /**
     * 查询声音克隆
     *
     * @param pageable 分页参数
     * @return 生成记录
     */
    @GetMapping("/generations")
    @PageQueryParam
    public ApiPageResult<AudioTtsVM> queryVideoGenerationPage(@PageableDefault Pageable pageable) {
        long uid = UserContextUtils.getOuid();
        Page<AudioTtsVM> page = audioCloneManager.queryGenerationPage(uid, PageBuilder.buildFrom(pageable));
        return ApiPageResult.ok(page);
    }



}
