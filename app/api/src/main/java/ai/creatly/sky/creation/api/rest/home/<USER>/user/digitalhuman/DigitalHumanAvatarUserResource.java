/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure.user.digitalhuman;

import ai.creatly.sky.creation.api.common.openapi.PageQueryParam;
import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.biz.digitalhuman.DigitalHumanManager;
import ai.creatly.sky.creation.biz.userfile.UserFileManager;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.request.AvatarCreateRequest;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.response.AvatarVM;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.response.UserFileVM;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.PageBuilder;
import com.jspeeder.core.data.result.ApiListResult;
import com.jspeeder.core.data.result.ApiPageResult;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.data.result.ApiVoidResult;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 数字人形象API
 *
 * <AUTHOR>
 * @version DigitalHumanAvatarUserResource.java, v 0.1 2024-05-25 17:00 syoka
 */
@RequestMapping("/api/dig-human/avatar")
@RestController
@Slf4j
@RequiredArgsConstructor
public class DigitalHumanAvatarUserResource {

    private final UserFileManager     userFileManager;
    private final DigitalHumanManager digitalHumanManager;

    /**
     * 上传数字人训练视频
     *
     * @param file -
     * @return -
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResult<UserFileVM> uploadAvatar(@RequestPart MultipartFile file) {
        UserContext userContext = UserContextUtils.getUserContext();
        FileBizSource bizSource = FileBizSource.DIG_HUMAN_AVATAR_DATASET;
        UserFileVM userFile = userFileManager.uploadAndCreate(file, FileType.VIDEO, bizSource, userContext);
        return ApiResult.ok(userFile);
    }

    /**
     * 提交数字人形象定制
     *
     * @param request 请求参数
     */
    @PostMapping
    public ApiResult<String> createAvatar(@Valid @RequestBody AvatarCreateRequest request) {
        UserContext userContext = UserContextUtils.getUserContext();
        String avatarId = digitalHumanManager.createAvatar(userContext, request);
        return ApiResult.ok(avatarId);
    }

    @GetMapping(value = "/dataset/list")
    public ApiPageResult<UserFileVM> getUserUploadAvatarVideos(@PageableDefault Pageable pageable) {
        long uid = UserContextUtils.getOuid();
        Page<UserFileVM> page =  digitalHumanManager.getUserUploadAvatarVideos(uid, PageBuilder.build(pageable));
        return ApiPageResult.ok(page);
    }

    /**
     * 获取数字人形象（公模+定制）
     *
     * @param pageable 请求参数
     */
    @GetMapping("/list")
    @PageQueryParam
    public ApiPageResult<AvatarVM> getAvatarList(@PageableDefault(page = 1, size = 20) Pageable pageable) {
        long uid = UserContextUtils.getOuid();
        Page<AvatarVM> page = digitalHumanManager.getAvatarList(uid, PageBuilder.build(pageable));
        return ApiPageResult.ok(page);
    }

    /**
     * 获取定制的数字人形象
     */
    @GetMapping("/customized")
    public ApiListResult<AvatarVM> getCustomizedAvatars() {
        long uid = UserContextUtils.getOuid();
        List<AvatarVM> list = digitalHumanManager.getCustomizedAvatars(uid);
        return ApiListResult.ok(list);
    }

    /**
     * 获取数字人形象详情
     *
     * @param avatarId 形象id
     */
    @GetMapping("/{id}")
    public ApiResult<AvatarVM> getAvatar(@PathVariable("id") String avatarId) {
        long uid = UserContextUtils.getOuid();
        AvatarVM avatar = digitalHumanManager.getAvatar(uid, Long.valueOf(avatarId));
        return ApiResult.ok(avatar);
    }

    /**
     * 删除数字人形象
     *
     * @param avatarId 形象id
     */
    @DeleteMapping("/{id}")
    public ApiVoidResult deleteAvatar(@PathVariable("id") String avatarId) {
        long uid = UserContextUtils.getOuid();
        digitalHumanManager.deleteAvatar(uid, Long.valueOf(avatarId));
        return ApiVoidResult.ok();
    }
}
