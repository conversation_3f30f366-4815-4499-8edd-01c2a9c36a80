/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.creatly.sky.creation.api.rest.home.secure;

import ai.creatly.sky.creation.domain.support.config.model.PreferenceConfig;
import ai.creatly.sky.creation.domain.support.config.service.PreferenceDomainService;
import com.jspeeder.core.data.result.ApiResult;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version : SystemPreferenceResource.java, v 1.0 2023年07月30日 20时13分 syoka Exp$
 */
@RestController
@RequestMapping("/api/system")
@RequiredArgsConstructor
public class SystemPreferenceResource {

    private final PreferenceDomainService preferenceDomainService;

    private static final String GLOBAL_NOTIFICATION = "GLOBAL_NOTIFICATION";


    @GetMapping("/preference/config")
    public ApiResult<String> getSystemPreference(@RequestParam("configKey") String configKey) {
        String value = preferenceDomainService.getSystemPreference(configKey, "");
        return ApiResult.ok(value);
    }


    @PostMapping("/preference/config")
    public ApiResult<?> setSystemPreference(@RequestBody PreferenceConfig config) {
        preferenceDomainService.setSystemPreference(config.getConfigKey(), config.getConfigValue(), "1");
        return ApiResult.ok();
    }

    @GetMapping("/global-notification")
    public ApiResult<?> getGlobalNotification() {
        String notification = preferenceDomainService.getSystemPreference(GLOBAL_NOTIFICATION, null);
        if (StringUtils.isNotEmpty(notification)) {
            return ApiResult.ok(notification);
        }
        return ApiResult.ok();
    }
}
