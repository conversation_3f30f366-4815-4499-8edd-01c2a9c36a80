/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure;

import ai.creatly.sky.creation.api.common.openapi.PageQueryParam;
import ai.creatly.sky.creation.api.common.util.MvcUtil;
import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.course.model.*;
import ai.creatly.sky.creation.domain.core.course.service.AiCourseRepository;
import ai.creatly.sky.creation.domain.core.course.service.AiCourseService;
import ai.creatly.sky.creation.domain.core.course.service.CoursePayService;
import com.jspeeder.core.data.result.ApiResult;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ai课程展示
 *
 * <AUTHOR>
 * @version AiCourseResource.java, v 0.1 2024-05-06 上午11:59 yongliang.j
 */
@RestController
@Slf4j
@RequestMapping(value = "/api/ai-course", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class AiCourseResource {

    private final AiCourseRepository aiCourseRepository;
    private final AiCourseService  aiCourseService;
    private final CoursePayService coursePayService;

    /**
     * 首页展示AI课程
     * @return
     */
    @GetMapping("/index")
    @PageQueryParam
    public ApiResult<List<AiCourse>> getIndexAiCourse() {
        return ApiResult.ok(aiCourseRepository.queryIndexHotCourse());
    }

    /**
     * AI图像-展示AI课程
     * @return
     */
    @GetMapping("/image")
    @PageQueryParam
    public ApiResult<List<AiCourse>> getAmageAiCourse() {
        return ApiResult.ok(aiCourseRepository.queryImageHotCourse());
    }


    /**
     * AI音频-展示AI课程
     * @return
     */
    @GetMapping("/audio")
    @PageQueryParam
    public ApiResult<List<AiCourse>> getAudioAiCourse() {
        return ApiResult.ok(aiCourseRepository.queryAudioHotCourse());
    }


    /**
     * AI视频-展示AI课程
     * @return
     */
    @GetMapping("/video")
    @PageQueryParam
    public ApiResult<List<AiCourse>> getVideoAiCourse() {
        return ApiResult.ok(aiCourseRepository.queryVideoHotCourse());
    }

    /**
     * 课程页面-展示AI课程
     * @return
     */
    @GetMapping("/list")
    @PageQueryParam
    public ApiResult<List<AiCourse>> getAiCourseHotList() {
        return ApiResult.ok(aiCourseRepository.queryCourseHotList());
    }

    /**
     * 课程详情页
     * @return
     */
    @GetMapping("/detail/{courseId}")
    @PageQueryParam
    public ApiResult<AiCourseVM> getAiCourseDetail(@PathVariable Long courseId) {
        UserContext userContext = UserContextUtils.getUserContext();
        return ApiResult.ok(aiCourseService.getCourseDetail(courseId,userContext));
    }

    /**
     * 学习记录更新
     * @return
     */
    @PostMapping("/learn/record")
    @PageQueryParam
    public ApiResult<AiCourseLearnRecord> learnRecord(@RequestBody AiCourseRequest request) {
        UserContext userContext = UserContextUtils.getUserContext();
        return ApiResult.ok(aiCourseService.learnRecord(request, userContext));
    }


    /**
     * 课程支付二维码
     * @return
     */
    @GetMapping("/prepay/{courseId}")
    public ApiResult<CourseWxPrepayVM> getCoursePrePayPage(@PathVariable Long courseId, HttpServletRequest servletRequest) {
        UserContext userContext = UserContextUtils.getUserContext();
        String clientIp = MvcUtil.getClientIp(servletRequest);
        return ApiResult.ok(coursePayService.createOrderAndPay(courseId, clientIp, userContext));
    }

    /**
     * 轮询获取支付结果
     * @return
     */
    @GetMapping("/pay/result/{orderId}")
    public ApiResult<CoursePayResultVM> getCoursePayResult(@PathVariable String orderId) {
        return ApiResult.ok(coursePayService.payResult(Long.valueOf(orderId)));
    }

}
