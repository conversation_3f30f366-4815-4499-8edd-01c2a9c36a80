/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.pub;

import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.api.config.security.filter.WebJwtTokenAuthManager;
import ai.creatly.sky.creation.biz.user.UserManager;
import ai.creatly.sky.creation.domain.common.util.crypto.AuthTokenHelper;
import ai.creatly.sky.creation.domain.core.auth.model.request.StudioSignInRequest;
import ai.creatly.sky.creation.domain.core.auth.service.AuthService;
import ai.creatly.sky.creation.domain.core.auth.service.WechatAuthService;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import ai.creatly.sky.creation.domain.core.user.model.request.SendVerifyCodeCommand;
import ai.creatly.sky.creation.domain.core.user.model.request.UserVerifyType;
import ai.creatly.sky.creation.domain.core.user.model.request.VerifyCodeScenario;
import ai.creatly.sky.creation.domain.core.user.model.response.UserLoginVM;
import ai.creatly.sky.creation.domain.core.user.service.SmsVerifyCodeManager;
import ai.creatly.sky.creation.domain.core.user.service.UserService;
import ai.creatly.sky.creation.domain.support.wechat.model.WechatUser;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.data.result.ApiVoidResult;
import com.jspeeder.core.util.Validates;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import ai.creatly.sky.creation.domain.core.auth.model.request.PasswordSignInRequest;

/**
 * <AUTHOR>
 * @version SignInResource.java, v 0.1 2023-05-27 14:57 syoka
 */
@RestController
@RequestMapping(value = "/api", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class SignInResource {

    private final UserService          userService;
    private final UserManager          userManager;
    private final WechatAuthService    wechatAuthService;
    private final SmsVerifyCodeManager smsVerifyCodeManager;
    private final AuthService          authService;
    private final AuthTokenHelper      authTokenHelper;

    /**
     * 发送验证码（登录/注册）
     */
    @PostMapping(value = "/verify-code/send", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ApiVoidResult sendSmsVerifyCode(@RequestBody @Valid SendVerifyCodeCommand command) {
        VerifyCodeScenario scenario = command.getScenario();
        // 发送验证码
        if (VerifyCodeScenario.SIGN_UP == scenario) {
            // TODO 公开接口发送短信，需要对同一个IP限制发送频率，提高恶意攻击成本
            userManager.createOneTimeSignUpVerifyCode(UserVerifyType.PHONE, command.getPhone());
        } else if (VerifyCodeScenario.SIGN_IN == scenario) {
            userManager.createOneTimeSignInVerifyCode(UserVerifyType.PHONE, command.getPhone());
        } else if (VerifyCodeScenario.CHANGE_PHONE == scenario) {
            // 校验当前用户的登录状态
            UserContextUtils.getUserContext();
            userManager.createOneTimeChangePhoneVerifyCode(UserVerifyType.PHONE, command.getPhone());
        } else if (VerifyCodeScenario.SIGN_IN_OR_UP == scenario) {
            // TODO 公开接口发送短信，需要对同一个IP限制发送频率，提高恶意攻击成本
            userManager.createOneTimeSignInOrUpVerifyCode(UserVerifyType.PHONE, command.getPhone());
        }
        return ApiVoidResult.ok();
    }

    /**
     * 手机号登录
     * </p>
     * 登录逻辑：输入手机号+验证码 => 校验验证码 => 【手机号不存在则自动注册】 => 返回用户登录token
     *
     * @param request 请求参数
     * @return -
     */
    @PostMapping(value = "/auth/sign-in-by-phone", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ApiResult<UserLoginVM> signInByPhone(@RequestBody @Valid StudioSignInRequest request) {
        // 基于手机号登录
        String phone = request.getPhone();
        String smsCode = request.getSmsCode();

        // TODO 演示账号
        if(!"15088888888".equals(phone)) {
            boolean validateResult = smsVerifyCodeManager.validateUserVerifyCode(VerifyCodeScenario.SIGN_IN_OR_UP, phone, smsCode);
            if (!validateResult) {
                // 万能登录验证码校验
                validateResult = authService.checkUniversalLoginCode(phone, smsCode);
            }
            Validates.isTrue(validateResult, UserErrorCode.USER_SIGN_IN_SMS_CODE_INVALID);
        }

        UserInfo userInfo = userService.getUserByPhoneIfNotExistThenCreate(phone, request.getInviteCode());
        // TODO 如果是万能登录验证码登录进来的，给 token 打个标记
        String token = authTokenHelper.generateToken(userInfo.getUsername());
        return ApiResult.ok(new UserLoginVM().setAccessToken(token));
    }

    /**
     * 手机号+微信登录
     * </p>
     * 登录逻辑：输入手机号+验证码+微信授权token => 校验验证码 => 【手机号不存在则自动注册】 => 用户绑定微信（幂等校验+绑定冲突校验）=> 返回用户登录token
     *
     * @param bearerToken 微信授权Token
     * @param request     请求参数
     * @return -
     */
    @PostMapping(value = "/auth/sign-in-by-phone-wechat", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ApiResult<UserLoginVM> signInByPhoneAndWechat(@RequestHeader("Authorization") String bearerToken,
                                                         @RequestBody @Valid StudioSignInRequest request) {
        String loginToken = WebJwtTokenAuthManager.getToken(bearerToken);
        Validates.notBlank(loginToken, UserErrorCode.USER_ILLEGAL_TOKEN);
        // 微信登录token校验
        WechatUser wechatUser = wechatAuthService.checkWechatLoginToken(loginToken);

        // 校验验证码
        String phone = request.getPhone();
        String smsCode = request.getSmsCode();
        boolean isSmsCodeCorrect = smsVerifyCodeManager.validateUserVerifyCode(VerifyCodeScenario.SIGN_UP, phone, smsCode);
        if (isSmsCodeCorrect) {
            UserInfo userInfo = userService.registerThenBindWechatPhone(phone, wechatUser, request.getInviteCode());
            // 替换为平台自己的登录token
            String token = authTokenHelper.generateToken(userInfo.getUsername());
            return ApiResult.ok(new UserLoginVM().setAccessToken(token));
        }
        return ApiResult.err(UserErrorCode.USER_VERIFY_CODE_NOT_MATCH);
    }

    /**
     * 账号密码登录
     *
     * @param request 请求参数(用户名+密码)
     * @return -
     */
    @PostMapping(value = "/auth/sign-in-by-password", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ApiResult<UserLoginVM> signInByPassword(@RequestBody @Valid PasswordSignInRequest request) {
        String userName = request.getUserName();
        String password = request.getPassword();
        UserInfo userInfo = userService.getUserByPassword(userName, password);
        if (userInfo != null) {
            String token = authTokenHelper.generateToken(userInfo.getUsername());
            return ApiResult.ok(new UserLoginVM().setAccessToken(token));
        }
        else {
            return ApiResult.err(UserErrorCode.LOGIN_PWD_NOT_CORRECT);
        }
    }
}
