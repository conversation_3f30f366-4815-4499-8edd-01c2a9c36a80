/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.admin;

import ai.creatly.sky.creation.domain.core.auth.service.AuthService;
import com.jspeeder.core.data.result.ApiResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version : ToolsResource.java, v 1.0 2023年07月31日 22时17分 syoka Exp$
 */
@RestController
@RequestMapping("/api/admin/tools")
@RequiredArgsConstructor
public class ToolsAdminResource {

    private final AuthService authService;

    /**
     * 万能登录验证码
     *
     * @param phone 手机号
     * @return -
     */
    @GetMapping("/universal-login-code")
    public ApiResult<String> getUniversalLoginCode(@RequestParam String phone) {
        String code = authService.generateUniversalLoginCode(phone);
        return ApiResult.ok(code);
    }
}
