/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.pub;

import ai.creatly.sky.creation.domain.common.http.HttpRequest;
import ai.creatly.sky.creation.domain.core.auth.model.WechatAppName;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import ai.creatly.sky.creation.domain.support.wechat.mp.WechatMpClient;
import ai.creatly.sky.creation.domain.support.wechat.mp.WechatMpService;
import ai.creatly.sky.creation.domain.support.wechat.mp.cipher.DecryptedResult;
import ai.creatly.sky.creation.domain.support.wechat.mp.cipher.WechatMpCipher;
import ai.creatly.sky.creation.domain.support.wechat.mp.model.message.receive.WechatMpEvent;
import com.fasterxml.jackson.databind.JsonNode;
import com.jspeeder.core.util.xml.XML;
import jakarta.servlet.http.HttpServletRequest;
import jodd.net.HttpMethod;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version WechatMpResource.java, v 0.1 2024-10-14 下午8:13 zhoudong
 */
@RestController
@RequestMapping(value = "/api/wechat/mp")
@RequiredArgsConstructor
@Slf4j
public class WechatMpResource {

    private final WechatMpClient  wechatMpClient;
    private final WechatMpService wechatMpService;
    private final WechatMpCipher  wechatMpCipher;
    private final AppAlertHelper  appAlertHelper;

    /**
     * 微信公众号接入
     */
    @GetMapping("/receive")
    public String receive(HttpServletRequest request, @RequestHeader HttpHeaders headers) {
        logRequest(request, headers);
        String echoStr = request.getParameter("echostr");
        String timestamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");
        String signature = request.getParameter("signature");
        if (StringUtils.isAnyBlank(timestamp, nonce, signature, echoStr)) {
            return "error";
        }

        // 验签
        String token = wechatMpClient.getToken(WechatAppName.mp_zxy);
        if (isSignatureInvalid(signature, timestamp, nonce, token)) {
            return "error";
        }
        log.info("签名校验成功");
        return echoStr;
    }

    /**
     * 微信公众号接入
     */
    @PostMapping(value = "/receive", produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> receive(HttpServletRequest request, @RequestHeader HttpHeaders headers, @RequestBody String body) {
        logRequest(request, headers);
        log.info("request body=\n{}", body);

        final WechatAppName appName = WechatAppName.mp_zxy;
        final String timestamp = request.getParameter("timestamp");
        final String nonce = request.getParameter("nonce");
        final String msgSignature = request.getParameter("msg_signature");
        final String openId = request.getParameter("openid");
        if (StringUtils.isAnyBlank(timestamp, nonce, msgSignature, openId)) {
            log.error("请求参数不完整");
            return ResponseEntity.badRequest().build();
        }

        // 密文
        String encrypt;
        try {
            encrypt = XML.parseTree(body).get("Encrypt").asText();
        } catch (Exception e) {
            log.warn("请求体格式错误，无法获取到密文", e);
            return ResponseEntity.badRequest().build();
        }

        // 验签
        String token = wechatMpClient.getToken(appName);
        if (isSignatureInvalid(msgSignature, timestamp, nonce, token, encrypt)) {
            return ResponseEntity.badRequest().build();
        }

        // 解密
        byte[] aesKey = wechatMpClient.getAesKey(appName);
        String appId = wechatMpClient.getAppId(appName);
        DecryptedResult decryptedResult = wechatMpCipher.decrypt(aesKey, appId, encrypt);
        if (!decryptedResult.isSuccess()) {
            return ResponseEntity.badRequest().build();
        }

        try {
            // 处理消息
            JsonNode node = XML.parseTree(decryptedResult.getXml());
            String msgType = node.get("MsgType").asText();

            // 处理事件消息
            if (msgType.equals("event") && node.get("EventKey") != null) {
                WechatMpEvent event = new WechatMpEvent()
                        .setNonce(nonce)
                        .setAppName(appName)
                        .setAppId(decryptedResult.getFromAppId())
                        .setEvent(node.get("Event").asText())
                        .setEventKey(node.get("EventKey").asText())
                        .setFromUser(node.get("FromUserName").asText())
                        .setToUser(node.get("ToUserName").asText());
                HttpRequest originalRequest = new HttpRequest()
                        .setMethod(HttpMethod.POST)
                        .setPath(request.getRequestURI())
                        .setQuery(request.getQueryString())
                        .setHeaders(headers)
                        .setBody(body);
                String responseBody = wechatMpService.handleEvent(originalRequest, event);
                return ResponseEntity.ok(responseBody);
            }

            // 其它消息类型暂不处理
            return ResponseEntity.ok("success");
        } catch (Exception e) {
            log.error("微信消息处理异常", e);
            appAlertHelper.alertText("微信消息处理异常,params={},body=\n{}", request.getParameterMap(), body, e);
            return ResponseEntity.internalServerError().body("error");
        }
    }

    private void logRequest(HttpServletRequest request, HttpHeaders headers) {
        log.info("request params={}", request.getParameterMap());
        log.info("request headers={}", headers);
    }

    private boolean isSignatureInvalid(String signature, String... params) {
        if (!signature.equals(wechatMpCipher.sign(params))) {
            log.error("签名校验失败");
            return true;
        }
        return false;
    }
}
