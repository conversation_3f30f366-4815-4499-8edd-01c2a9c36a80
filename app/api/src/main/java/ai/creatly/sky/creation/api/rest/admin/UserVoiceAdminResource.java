/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.admin;

import ai.creatly.sky.creation.api.common.openapi.PageQueryParam;
import ai.creatly.sky.creation.biz.voice.UserVoiceAdminManager;
import ai.creatly.sky.creation.domain.core.aitask.admin.model.AiTaskVM;
import ai.creatly.sky.creation.domain.core.voice.model.admin.request.VoiceCustomUpdateRequest;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.PageBuilder;
import com.jspeeder.core.data.result.ApiPageResult;
import com.jspeeder.core.data.result.ApiVoidResult;
import com.jspeeder.core.util.Validates;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 用户声音定制管理
 *
 * <AUTHOR>
 * @version UserVoiceAdminResource.java, v 0.1 2024-01-20 下午7:48 heb
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/admin/user-voices", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class UserVoiceAdminResource {

    private final UserVoiceAdminManager userVoiceAdminManager;


    /**
     * 分页查询定制声音列表数据
     *
     * @param pageable 分页参数
     * @return 声音列表
     */
    @GetMapping("/tasks")
    @PageQueryParam
    public ApiPageResult<AiTaskVM> getPage(@PageableDefault Pageable pageable) {
        Page<AiTaskVM> page = userVoiceAdminManager.queryVoiceTaskPage(PageBuilder.buildFrom(pageable));
        return ApiPageResult.ok(page);
    }

    /**
     * 后台接口提交声音变声任务
     * 功能：支持修改任务表中的字段，暂时圈定可以修改的类型仅为用户定制声音的任务执行状态 （处理中，不合规）
     * 对任务的是否改为自动执行，任务根据这个进行自动执行后修改任务执行状态为已完成，后台不支持 已完成状态修改
     *
     * @param request 请求参数
     * @return 响应
     */
    @PostMapping(value = "/tasks/{taskId}/forward", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ApiVoidResult customVoiceTaskUpdate(@PathVariable String taskId, @RequestBody @Valid VoiceCustomUpdateRequest request) {
        if (request.getStatus() != null) {
            userVoiceAdminManager.updateVoiceTaskStatus(Validates.requireLong(taskId, "taskId"), request.getStatus());
        }
        userVoiceAdminManager.updateVoiceTaskAutoExec(Validates.requireLong(taskId, "taskId"), request.getAutoExec());
        return ApiVoidResult.ok();
    }
}
