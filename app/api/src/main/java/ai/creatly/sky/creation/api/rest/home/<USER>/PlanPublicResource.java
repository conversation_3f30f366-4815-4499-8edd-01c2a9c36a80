/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.pub;

import ai.creatly.sky.creation.biz.plan.PlanManager;
import ai.creatly.sky.creation.domain.core.plan.model.response.SubscriptionPlanVM;
import com.jspeeder.core.data.result.ApiListResult;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 定价计划API
 *
 * <AUTHOR>
 * @version PlanPublicResource.java, v 0.1 2023-10-08 下午9:19 zhoudong
 */
@RestController
@RequestMapping(path = "/api/public/plans", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class PlanPublicResource {

    private final PlanManager planManager;

    /**
     * 获取订阅计划列表
     *
     * @return 订阅计划列表
     */
    @GetMapping(params = "type=subscription")
    public ApiListResult<SubscriptionPlanVM> getSubscriptionPlans() {
        List<SubscriptionPlanVM> subscriptionPlans = planManager.getPublicSubscriptionPlans();
        return ApiListResult.ok(subscriptionPlans);
    }
}
