/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure;

import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.domain.core.ai.exam.model.AiUserExamCertVM;
import ai.creatly.sky.creation.domain.core.ai.exam.service.AiExamService;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import com.jspeeder.core.data.result.ApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * ai证书列表
 *
 * <AUTHOR>
 * @version AiCertResource.java, v 0.1 2024-05-06 上午11:59 yongliang.j
 */
@RestController
@Slf4j
@RequestMapping(value = "/api/ai-cert", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class AiCertResource {

    private final AiExamService aiExamService;


    /**
     * 获取AI证书列表
     * @return
     */
    @GetMapping("/list")
    public ApiResult<List<AiUserExamCertVM>> getAiCert() {
        UserContext userContext = UserContextUtils.getUserContext();
        return ApiResult.ok(aiExamService.getUserCertList(userContext));
    }


}
