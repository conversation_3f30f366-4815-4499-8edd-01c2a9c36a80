/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.admin;

import ai.creatly.sky.creation.api.common.openapi.PageQueryParam;
import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.biz.organization.OrganizationManager;
import ai.creatly.sky.creation.biz.user.UserManager;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.course.model.CourseOrgSettingVM;
import ai.creatly.sky.creation.domain.core.course.model.CourseOrgUserVM;
import ai.creatly.sky.creation.domain.core.course.service.CourseOrgSettingRepository;
import ai.creatly.sky.creation.domain.core.user.model.OrgUserInfo;
import ai.creatly.sky.creation.domain.core.user.model.OrgUserQuery;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.PageBuilder;
import com.jspeeder.core.data.result.ApiPageResult;
import com.jspeeder.core.data.result.ApiResult;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 组织学员管理
 *
 * <AUTHOR>
 * @version UserOrganizationResource.java, v 0.1 2024-01-22 15:17 syoka
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/admin/course/orgs/user", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class CourseOrgUserResource {

    @NonNull
    private final OrganizationManager organizationManager;

    @NonNull
    private final UserManager userManager;

    private final CourseOrgSettingRepository courseOrgSettingRepository;


    /**
     * 机构已购课程列表
     */
    @GetMapping("/course")
    public ApiResult<List<CourseOrgSettingVM>> getCourseList(String orgCode) {
        return ApiResult.ok(courseOrgSettingRepository.getByOrgCode(orgCode));
    }

    /**
     * 机构学员列表
     */
    @PostMapping("/list")
    @PageQueryParam
    public ApiPageResult<CourseOrgUserVM> getCourseOrgUserageable(@PageableDefault Pageable pageable, @RequestBody OrgUserQuery orgUserQuery) {
        UserContext userContext = UserContextUtils.getUserContext();
        if (!userContext.getUserOrganization().getOrgCode().equals(AppConstants.DEFAULT_ORG)) {
            orgUserQuery.setOrgCode(userContext.getUserOrganization().getOrgCode());
        }
        Page<CourseOrgUserVM> page = organizationManager.getCourseOrgUserPageable(PageBuilder.buildFrom(pageable), orgUserQuery);
        return ApiPageResult.ok(page);
    }


    /**
     * 机构新增学员
     */
    @PostMapping("/add")
    public ApiResult<Boolean> addOrgUser(@RequestBody OrgUserInfo userInfo) {
        if (organizationManager.addOrgUser(userInfo)) {
            return ApiResult.ok();
        }
        else {
            return ApiResult.err("400", "开课数量达到上限，不予开通");
        }
    }

    /**
     * 机构编辑学员
     */
    @PostMapping("/edit")
    public ApiResult<Boolean> editOrgUser(@RequestBody CourseOrgUserVM userInfo) {
        organizationManager.updateOrgUser(userInfo);
        return ApiResult.ok();
    }
}
