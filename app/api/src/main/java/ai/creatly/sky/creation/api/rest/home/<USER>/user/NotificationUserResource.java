/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure.user;

import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.biz.notification.UserNotificationManager;
import ai.creatly.sky.creation.domain.support.notification.inbox.model.UserNotificationBox;
import ai.creatly.sky.creation.domain.support.notification.inbox.model.response.NotificationVM;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.PageBuilder;
import com.jspeeder.core.data.result.ApiPageResult;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.data.result.ApiVoidResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version UserNotificationResource.java, v 0.1 2024-07-11 10:03 syoka
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/user/notifications", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class NotificationUserResource {

    private final UserNotificationManager userNotificationManager;

    /**
     * 查询用户的所有通知记录
     */
    @PostMapping("/list")
    public ApiPageResult<NotificationVM> getUserNotification(@PageableDefault Pageable pageable) {
        Page<NotificationVM> page = userNotificationManager.getUserNotification(PageBuilder.build(pageable), UserContextUtils.getOuid());
        return ApiPageResult.ok(page);
    }

    /**
     * 检查是否有新的通知
     */
    @GetMapping(value = "/poll")
    public ApiResult<UserNotificationBox> checkUserNewNotification() {
        UserNotificationBox box = userNotificationManager.getUserUnreadNotification(UserContextUtils.getOuid());
        return ApiResult.ok(box);
    }

    /**
     * 设置所有通知已经
     */
    @PostMapping("/read-all")
    public ApiVoidResult readAllNotification() {
        Long uid = UserContextUtils.getOuid();
        userNotificationManager.readAllNotification(uid);
        return ApiVoidResult.ok();
    }

    /**
     * 已度单条消息
     */
    @PostMapping("/{id}/read")
    public ApiVoidResult readSingleNotification(@PathVariable String id) {
        long uid = UserContextUtils.getOuid();
        userNotificationManager.readSingleNotification(uid, Long.valueOf(id));
        return ApiVoidResult.ok();
    }
}
