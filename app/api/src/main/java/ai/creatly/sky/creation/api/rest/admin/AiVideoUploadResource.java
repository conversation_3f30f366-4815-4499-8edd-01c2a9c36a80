/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.admin;

import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.biz.userfile.UserFileManager;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.response.UserFileVM;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.jspeeder.core.data.result.ApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 课程管理
 *
 * <AUTHOR>
 * @version AiVideoAdminResource.java, v 0.1 2024-05-06 上午11:59 yongliang.j
 */
@RestController
@Slf4j
@RequestMapping(value = "/api/admin/video", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class AiVideoUploadResource {

    private final UserFileManager userFileManager;

    /**
     * 相关视频
     *
     * @return 文件信息
     */
    @PostMapping(path = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResult<UserFileVM> uploadReferVideo(@RequestPart MultipartFile file) {
        UserContext userContext = UserContextUtils.getUserContext();
        FileType fileType = FileType.VIDEO;
        FileBizSource bizSource = FileBizSource.VIDEO_UPLOAD_REF;
        UserFileVM userFile = userFileManager.uploadAndCreate(file, fileType, bizSource, userContext);
        return ApiResult.ok(userFile);
    }

}
