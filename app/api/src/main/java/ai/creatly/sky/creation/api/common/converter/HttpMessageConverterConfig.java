/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.common.converter;

import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.web.servlet.server.Encoding;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.ConversionService;
import org.springframework.format.support.DefaultFormattingConversionService;
import org.springframework.http.converter.*;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 配置HTTP请求和响应的数据转换器（官方默认的配置采用依赖自动发现机制，随着依赖变化会跟着变，不可控。所以最好把转换器固定下来，自己掌控）
 *
 * <AUTHOR>
 * @version HttpMessageConverterConfig.java, v 0.1 2023-10-12 下午10:32 zhoudong
 * @see org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport
 * @see org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#configureMessageConverters(List)
 * @see org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration#messageConverters(org.springframework.beans.factory.ObjectProvider)
 */
@Configuration
public class HttpMessageConverterConfig {

    @Bean
    public HttpMessageConverters messageConverters(ByteArrayHttpMessageConverter byteArrayHttpMessageConverter,
                                                   StringHttpMessageConverter stringHttpMessageConverter,
                                                   ResourceHttpMessageConverter resourceHttpMessageConverter,
                                                   ResourceRegionHttpMessageConverter resourceRegionHttpMessageConverter,
                                                   FormHttpMessageConverter formHttpMessageConverter,
                                                   MappingJackson2HttpMessageConverter jsonMessageConverter) {
        // 手动添加可控的数据转换器（禁止添加 XML 转换器）
        List<HttpMessageConverter<?>> messageConverters = this.listOf(
                byteArrayHttpMessageConverter,
                stringHttpMessageConverter,
                resourceHttpMessageConverter,
                resourceRegionHttpMessageConverter,
                formHttpMessageConverter,
                jsonMessageConverter
        );
        this.applyDefaultCharset(messageConverters);
        return new HttpMessageConverters(false, messageConverters);
    }

    @Bean
    public FormHttpMessageConverter formHttpMessageConverter(ByteArrayHttpMessageConverter byteArrayHttpMessageConverter,
                                                             StringHttpMessageConverter stringHttpMessageConverter,
                                                             ResourceHttpMessageConverter resourceHttpMessageConverter,
                                                             MappingJackson2HttpMessageConverter jsonMessageConverter) {
        // 手动添加可控的数据转换器（禁止添加 XML 转换器）
        List<HttpMessageConverter<?>> messageConverters = this.listOf(
                byteArrayHttpMessageConverter,
                stringHttpMessageConverter,
                resourceHttpMessageConverter,
                jsonMessageConverter
        );

        FormHttpMessageConverter messageConverter = new FormHttpMessageConverter();
        messageConverter.setPartConverters(messageConverters);
        return messageConverter;
    }

    private List<HttpMessageConverter<?>> listOf(HttpMessageConverter<?>... messageConverters) {
        return Arrays.stream(messageConverters).collect(Collectors.toList());
    }

    @Bean
    public StringToObjectHttpMessageConverter stringToObjectHttpMessageConverter(StringHttpMessageConverter stringHttpMessageConverter) {
        ConversionService conversionService = new DefaultFormattingConversionService();
        return new StringToObjectHttpMessageConverter(conversionService, stringHttpMessageConverter);
    }

    @Bean
    public ByteArrayHttpMessageConverter byteArrayHttpMessageConverter() {
        return new ByteArrayHttpMessageConverter();
    }

    @Bean
    public ResourceHttpMessageConverter resourceHttpMessageConverter() {
        return new ResourceHttpMessageConverter();
    }

    @Bean
    public ResourceRegionHttpMessageConverter resourceRegionHttpMessageConverter() {
        return new ResourceRegionHttpMessageConverter();
    }

    /**
     * Apply the configured charset as a default to registered part converters.
     */
    private void applyDefaultCharset(List<HttpMessageConverter<?>> messageConverters) {
        for (HttpMessageConverter<?> candidate : messageConverters) {
            if (candidate instanceof AbstractHttpMessageConverter<?> converter) {
                // Only override default charset if the converter operates with a charset to begin with...
                if (converter.getDefaultCharset() != null) {
                    converter.setDefaultCharset(Encoding.DEFAULT_CHARSET);
                }
            }
        }
    }
}
