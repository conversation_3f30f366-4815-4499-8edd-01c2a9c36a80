/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure.user;

import ai.creatly.sky.creation.api.common.openapi.PageQueryParam;
import ai.creatly.sky.creation.api.common.problem.reactive.WebErrorFallback;
import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.biz.ai.chat.ChatManager;
import ai.creatly.sky.creation.domain.core.ai.chat.model.MessageChunk;
import ai.creatly.sky.creation.domain.core.ai.chat.model.request.ConversationChatRequest;
import ai.creatly.sky.creation.domain.core.ai.chat.model.response.ConversationMessageVM;
import ai.creatly.sky.creation.domain.core.ai.chat.model.response.ConversationVM;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.PageBuilder;
import com.jspeeder.core.data.result.ApiListResult;
import com.jspeeder.core.data.result.ApiPageResult;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.data.result.ApiVoidResult;
import com.jspeeder.core.util.Validates;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.List;

/**
 * 创意助手-对话API
 *
 * <AUTHOR>
 * @version AiChatUserResource.java, v0.1 2025-02-19 20:18
 */
@RestController
@Slf4j
@RequestMapping(value = "/api/user/ai-chat", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class AiChatUserResource {

    private final ChatManager      chatManager;
    private final WebErrorFallback webErrorFallback;

    @PostMapping(path = "/conversations", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ApiResult<MessageChunk>> chat(@RequestBody @Valid ConversationChatRequest request) {
        long uid = UserContextUtils.getOuid();
        return chatManager.chat(uid, request)
                .map(ApiResult::ok)
                .onErrorResume(webErrorFallback::apply)
                .contextWrite(webErrorFallback.context())
                .timeout(Duration.ofMinutes(5));
    }

    @GetMapping(path = "/conversations")
    @PageQueryParam
    public ApiPageResult<ConversationVM> queryPage(@PageableDefault Pageable pageable) {
        long uid = UserContextUtils.getOuid();
        Page<ConversationVM> page = chatManager.queryPage(uid, PageBuilder.buildFrom(pageable));
        return ApiPageResult.ok(page);
    }

    @GetMapping("/conversations/{id}/messages")
    public ApiListResult<ConversationMessageVM> queryMessages(@PathVariable String id) {
        long uid = UserContextUtils.getOuid();
        long conversationId = Validates.requireLong(id, "conversationId");
        List<ConversationMessageVM> messages = chatManager.queryMessages(uid, conversationId);
        return ApiListResult.ok(messages);
    }

    @DeleteMapping("/conversations/{id}")
    public ApiVoidResult deleteConversation(@PathVariable String id) {
        long uid = UserContextUtils.getOuid();
        long conversationId = Validates.requireLong(id, "conversationId");
        chatManager.deleteConversation(uid, conversationId);
        return ApiVoidResult.ok();
    }
}
