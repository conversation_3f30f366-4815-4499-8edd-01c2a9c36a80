/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.common.async;

import ai.creatly.sky.creation.domain.common.async.AsyncTemplate;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version WebAsyncTaskHelper.java, v 0.1 2024-06-11 下午3:05 zhoudong
 */
@Component
@RequiredArgsConstructor
public class WebAsyncTaskHelper {

    private final AsyncTemplate  asyncTemplate;
    private final AppAlertHelper appAlertHelper;

    public WebAsyncTaskBuilder builder() {
        return new WebAsyncTaskBuilder(asyncTemplate, appAlertHelper);
    }
}
