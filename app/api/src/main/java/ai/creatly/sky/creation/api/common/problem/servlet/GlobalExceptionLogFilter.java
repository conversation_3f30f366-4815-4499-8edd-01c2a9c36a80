/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.common.problem.servlet;

import ai.creatly.sky.creation.api.common.util.MvcUtil;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * 记录Filter链中抛出的异常
 *
 * <AUTHOR>
 * @version GlobalExceptionLogFilter.java, v 0.1 2024-06-14 上午10:29 zhoudong
 */
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
@Component
@RequiredArgsConstructor
public class GlobalExceptionLogFilter extends OncePerRequestFilter {

    private final AppAlertHelper appAlertHelper;

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
                                    @NotNull FilterChain filterChain) throws ServletException, IOException {
        try {
            filterChain.doFilter(request, response);
        } catch (RuntimeException e) {
            String ip = MvcUtil.getClientIp(request);
            String requestLog = MvcUtil.toLogMessage(request);
            String username = MvcUtil.getAuthenticatedUsername(request);
            log.error("[Exception occurred]request=[{}],user=[{}],ip=[{}]", requestLog, username, ip, e);
            appAlertHelper.alertText("[Exception occurred]request=[{}],user=[{}],ip=[{}]", requestLog, username, ip, e);
            throw e;
        }
    }
}
