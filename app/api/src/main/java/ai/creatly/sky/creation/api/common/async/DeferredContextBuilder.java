/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.common.async;

import ai.creatly.sky.creation.api.common.util.MvcUtil;
import ai.creatly.sky.creation.biz.common.deferred.DeferredContext;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import com.jspeeder.core.data.problem.error.ErrorCode;
import com.jspeeder.core.data.result.ApiCoreResult;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.data.result.ApiVoidResult;
import jakarta.servlet.DispatcherType;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.request.async.DeferredResult;

import java.time.Duration;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version DeferredContextBuilder.java, v 0.1 2024-06-16 下午1:28 zhoudong
 */
public class DeferredContextBuilder {

    private final AppAlertHelper      appAlertHelper;
    private final HttpServletRequest  request;
    @Nullable
    private final HttpServletResponse response;
    private final String              ip;
    private final String              user;
    private final String              requestLog;
    private final String              responseLog;

    private Duration  timeout;
    private ErrorCode timeoutErrorCode;
    private boolean   timeoutAlert = true;

    DeferredContextBuilder(AppAlertHelper appAlertHelper) {
        this.appAlertHelper = appAlertHelper;

        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        this.request = requestAttributes.getRequest();
        this.response = requestAttributes.getResponse();
        this.ip = MvcUtil.getClientIp(request);
        this.user = MvcUtil.getAuthenticatedUsername(request);
        this.requestLog = MvcUtil.toLogMessage(request);
        this.responseLog = MvcUtil.toLogMessage(response);
    }

    public DeferredContextBuilder timeout(Duration timeout) {
        this.timeout = timeout;
        return this;
    }

    public DeferredContextBuilder timeoutErrorCode(ErrorCode timeoutErrorCode) {
        this.timeoutErrorCode = timeoutErrorCode;
        return this;
    }

    public DeferredContextBuilder timeoutAlert(boolean timeoutAlert) {
        this.timeoutAlert = timeoutAlert;
        return this;
    }

    /**
     * 设置构建延迟结果的函数
     *
     * @param resultBuilder 构建延迟结果的函数（注意📢📢📢：请务必把业务的同步逻辑放到此函数里）
     * @param <T>           业务结构对象
     * @return this
     */
    public <T extends ApiCoreResult<?>> DeferredResultBuilder<T> result(Function<DeferredContext, DeferredResult<T>> resultBuilder) {
        DeferredContext deferredContext = DeferredContext.builder()
                .appAlertHelper(appAlertHelper)
                .requestIp(ip)
                .requestApi(requestLog)
                .requestUser(user)
                .responseLog(responseLog)
                .timeout(timeout)
                .timeoutResult(() -> ApiResult.err(timeoutErrorCode))
                .timeoutAlert(timeoutAlert)
                .build();
        return new DeferredResultBuilder<>(this, deferredContext, resultBuilder);
    }

    @Slf4j
    @RequiredArgsConstructor
    public static class DeferredResultBuilder<T> {

        private final DeferredContextBuilder                       builder;
        private final DeferredContext                              deferredContext;
        private final Function<DeferredContext, DeferredResult<T>> deferredResultBuilder;

        public DeferredResult<T> build() {
            // 异步请求防重校验
            if (builder.request.getDispatcherType() == DispatcherType.ASYNC || MvcUtil.isCommitted(builder.response)) {
                String ip = builder.ip;
                String user = builder.user;
                String requestLog = builder.requestLog;
                String responseLog = builder.responseLog;
                String template = "[DeferredResult ignore][repeat]request=[{}],user=[{}],ip=[{}],response=[{}]";
                log.warn(template, requestLog, user, ip, responseLog);
                builder.appAlertHelper.alertText(template, requestLog, user, ip, responseLog);

                // 这里不管设置什么结果都无所谓了，因为响应已经提交了
                ApiVoidResult result = ApiVoidResult.err(builder.timeoutErrorCode);
                DeferredResult<T> deferredResult = new DeferredResult<>(builder.timeout.toMillis(), result);
                deferredResult.setErrorResult(result);
                return deferredResult;
            }
            return deferredResultBuilder.apply(deferredContext);
        }
    }
}
