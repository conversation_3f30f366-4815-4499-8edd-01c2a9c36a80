/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.admin;

import ai.creatly.sky.creation.biz.approval.ApprovalAdminManager;
import ai.creatly.sky.creation.domain.support.approval.admin.ApprovalTaskVM;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalStatus;
import ai.creatly.sky.creation.domain.support.approval.service.ApprovalService;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.PageBuilder;
import com.jspeeder.core.data.result.ApiPageResult;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.data.result.ApiVoidResult;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 审批任务 API
 *
 * <AUTHOR>
 * @version ApprovalAdminResource.java, 2024-10-29 下午2:24 zhoudong
 */
@RestController
@RequestMapping(value = "/api/admin/approvals", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class ApprovalAdminResource {

    private final ApprovalAdminManager approvalAdminManager;
    private final ApprovalService      approvalService;

    /**
     * 分页查询审批任务
     *
     * @param status   审批状态
     * @param pageable 分页参数
     * @return 审批任务分页
     */
    @GetMapping
    public ApiPageResult<ApprovalTaskVM> getPage(@RequestParam ApprovalStatus status, Pageable pageable) {
        Page<ApprovalTaskVM> page = approvalAdminManager.queryPage(status, PageBuilder.buildFrom(pageable));
        return ApiPageResult.ok(page);
    }

    /**
     * 查询审批任务详情
     *
     * @param id 审批任务ID
     * @return 审批任务详情
     */
    @GetMapping("/{id}")
    public ApiResult<ApprovalTaskVM> getDetail(@PathVariable String id) {
        ApprovalTaskVM approvalTask = approvalAdminManager.getDetail(Validates.requireLong(id, "approvalId"));
        return ApiResult.ok(approvalTask);
    }

    /**
     * 接受审批任务
     */
    @PostMapping("/{id}/accept")
    public ApiVoidResult accept(@PathVariable String id) {
        approvalService.accept(Validates.requireLong(id, "审批任务ID"));
        return ApiVoidResult.ok();
    }

    /**
     * 审批通过
     */
    @PostMapping("/{id}/approve")
    public ApiVoidResult approve(@PathVariable String id) {
        approvalService.approve(Validates.requireLong(id, "审批任务ID"));
        return ApiVoidResult.ok();
    }

    /**
     * 审批拒绝
     */
    @PostMapping("/{id}/reject")
    public ApiVoidResult reject(@PathVariable String id) {
        approvalService.reject(Validates.requireLong(id, "审批任务ID"));
        return ApiVoidResult.ok();
    }
}
