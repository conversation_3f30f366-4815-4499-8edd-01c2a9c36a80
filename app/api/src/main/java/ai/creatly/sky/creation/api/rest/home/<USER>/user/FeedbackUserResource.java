/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure.user;

import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.biz.feedback.FeedbackManager;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.feedback.model.request.FeedbackAddRequest;
import com.jspeeder.core.data.result.ApiVoidResult;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 用户反馈
 *
 * <AUTHOR>
 * @version FeedbackUserResource.java, v 0.1 2023-12-12 23:32 heb
 */
@RestController
@RequestMapping("/api/user/feedback")
@RequiredArgsConstructor
public class FeedbackUserResource {

    private final FeedbackManager feedbackManager;

    /**
     * @param attachments 附加列表（截图、音频、视频等）
     * @param request     标题
     * @return 响应
     */
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiVoidResult createFeedback(@RequestPart(required = false) @Size(max = 10, message = "附件不能超过10个") List<MultipartFile> attachments,
                                        @RequestPart @Valid FeedbackAddRequest request) {
        UserContext userContext = UserContextUtils.getUserContext();
        feedbackManager.createFeedback(request, attachments, userContext);
        return ApiVoidResult.ok();
    }
}
