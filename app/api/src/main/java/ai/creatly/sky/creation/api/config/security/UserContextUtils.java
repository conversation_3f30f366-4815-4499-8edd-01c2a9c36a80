/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.config.security;

import ai.creatly.sky.creation.domain.core.auth.model.CustomizeUser;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.support.credential.model.ClientContext;
import com.jspeeder.core.model.OperatorSource;
import com.jspeeder.core.util.Validates;
import lombok.experimental.UtilityClass;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version : UserContextUtils.java, v 1.0 2023年07月29日 17时24分 syoka Exp$
 */
@UtilityClass
public class UserContextUtils {

    public static Optional<UserContext> getOptionalUserContext() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (Objects.isNull(authentication) || Objects.isNull(authentication.getPrincipal())
                || !(authentication.getPrincipal() instanceof CustomizeUser user)) {
            return Optional.empty();
        }

        UserContext userContext = UserContext.builder()
                .sessionUid(user.getUid())
                .sessionUserName(user.getUsername())
                .phone(user.getPhone())
                .email(user.getEmail())
                .userOrganization(user.getUserOrganization())
                .build();

        if (OperatorSource.OPEN_API == user.getSource()) {
            ClientContext clientContext = ClientContext.of(user.getAppKey(), user.getAppKeyName());
            userContext = userContext.withClientContext(clientContext);
        }
        return Optional.of(userContext);
    }

    public static UserContext getUserContext() {
        UserContext userContext = getOptionalUserContext().orElse(null);
        Validates.notNull(userContext, UserErrorCode.USER_NOT_LOGIN);
        return userContext;
    }

    /**
     * 获取用户登录账号uid
     */
    public static long getSessionUid() {
        return getUserContext().getSessionUid();
    }

    /**
     * 获取用户组织主账号id
     */
    public static long getOuid() {
        return getUserContext().getouid();
    }
}
