/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure;

import ai.creatly.sky.creation.api.common.openapi.PageQueryParam;
import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.domain.core.ai.tool.AiTool;
import ai.creatly.sky.creation.domain.core.ai.tool.AiToolRepository;
import com.jspeeder.core.data.result.ApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * ai工具集
 *
 * <AUTHOR>
 * @version AiToolResource.java, v 0.1 2024-05-06 上午11:59 yongliang.j
 */
@RestController
@Slf4j
@RequestMapping(value = "/api/ai-tools", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class AiToolResource {

    private final AiToolRepository aiToolRepository;


    /**
     * AI图像工具集
     * @return
     */
    @GetMapping("/index")
    @PageQueryParam
    public ApiResult<List<AiTool>> getAiIndexTools() {
        return ApiResult.ok(aiToolRepository.queryIndexTools());
    }

    /**
     * AI图像工具集
     * @return
     */
    @GetMapping("/image")
    @PageQueryParam
    public ApiResult<List<AiTool>> getAiImageTools() {
        return ApiResult.ok(aiToolRepository.queryImageTools());
    }

    /**
     * AI音频工具集
     * @return
     */
    @GetMapping("/audio")
    @PageQueryParam
    public ApiResult<List<AiTool>> getAiAudioTools() {
        return ApiResult.ok(aiToolRepository.queryAudioTools());
    }

    /**
     * AI视频工具集
     * @return
     */
    @GetMapping("/video")
    @PageQueryParam
    public ApiResult<List<AiTool>> getAiVideoTools() {
        return ApiResult.ok(aiToolRepository.queryVideoTools(UserContextUtils.getUserContext().getPhone()));
    }

    /**
     * lora工具集
     * @return
     */
    @GetMapping("/lora")
    @PageQueryParam
    public ApiResult<List<AiTool>> getAiLoraTools() {
        return ApiResult.ok(aiToolRepository.queryLoraTools());
    }

    /**
     * AI图片、视频、音频工具集
     * @return
     */
    @GetMapping("/tools")
    @PageQueryParam
    public ApiResult<List<AiTool>> getTools() {
        return ApiResult.ok(aiToolRepository.queryTools());
    }

}
