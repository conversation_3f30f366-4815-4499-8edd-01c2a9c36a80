/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure.user;

import ai.creatly.sky.creation.api.common.openapi.PageQueryParam;
import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.domain.core.invitation.mapper.UserInvitationMapper;
import ai.creatly.sky.creation.domain.core.invitation.model.UserInvitationVM;
import ai.creatly.sky.creation.domain.core.invitation.service.UserInvitationRepository;
import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import ai.creatly.sky.creation.domain.core.user.repository.UserRepository;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.PageBuilder;
import com.jspeeder.core.data.result.ApiPageResult;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户邀请API
 *
 * <AUTHOR>
 * @version UserInvitationResource.java, v 0.1 2024-09-06 下午4:54 zhoudong
 */
@RestController
@RequestMapping(value = "/api/user/invitations", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class InvitationUserResource {

    private final UserRepository           userRepository;
    private final UserInvitationRepository userInvitationRepository;
    private final UserInvitationMapper     userInvitationMapper;

    /**
     * 获取个人专属邀请码
     *
     * @return 邀请码
     */
    @GetMapping("/invite-code")
    public ApiResult<String> getInviteCode() {
        long uid = UserContextUtils.getUserContext().getUid();
        UserInfo userInfo = userRepository.queryOptionalById(uid).orElse(null);
        Asserts.notNull(userInfo, "用户不存在");
        if (userInfo.getInviteCode() == null) {
            String inviteCode = IdHelper.get32UUID();
            userRepository.updateById(new UserInfo().setId(String.valueOf(uid)).setInviteCode(inviteCode));
            return ApiResult.ok(inviteCode);
        }
        return ApiResult.ok(userInfo.getInviteCode());
    }

    /**
     * 获取邀请记录
     *
     * @return 邀请记录
     */
    @GetMapping
    @PageQueryParam
    public ApiPageResult<UserInvitationVM> getInvitations(@PageableDefault Pageable pageable) {
        long uid = UserContextUtils.getUserContext().getUid();
        Page<UserInvitationVM> page = userInvitationRepository.queryPage(uid, PageBuilder.buildFrom(pageable))
                .map(userInvitationMapper::toVM);
        return ApiPageResult.ok(page);
    }
}
