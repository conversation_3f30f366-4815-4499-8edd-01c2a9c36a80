/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure.user;

import ai.creatly.sky.creation.api.common.async.DeferredResultHelper;
import ai.creatly.sky.creation.api.common.util.MvcUtil;
import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.biz.userfile.UserFileManager;
import ai.creatly.sky.creation.biz.voice.UserVoicePayAsyncManager;
import ai.creatly.sky.creation.domain.common.integration.azure.model.SynthesisVoiceNameEnum;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskQO;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.response.UserFileVM;
import ai.creatly.sky.creation.domain.core.voice.error.VoiceErrorCode;
import ai.creatly.sky.creation.domain.core.voice.mapper.UserVoiceOrderMapper;
import ai.creatly.sky.creation.domain.core.voice.mapper.UserVoiceTaskMapper;
import ai.creatly.sky.creation.domain.core.voice.mapper.VoiceLocaleMapper;
import ai.creatly.sky.creation.domain.core.voice.mapper.VoiceMapper;
import ai.creatly.sky.creation.domain.core.voice.model.UserVoiceOrder;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.pay.UserVoiceWxNativePrepayResult;
import ai.creatly.sky.creation.domain.core.voice.model.request.UserVoiceContactRequest;
import ai.creatly.sky.creation.domain.core.voice.model.request.UserVoiceCustomizeRequest;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceCustomSearchRequest;
import ai.creatly.sky.creation.domain.core.voice.model.response.*;
import ai.creatly.sky.creation.domain.core.voice.model.task.UserVoiceTaskBizStatus;
import ai.creatly.sky.creation.domain.core.voice.model.task.param.UserVoiceCreateParam;
import ai.creatly.sky.creation.domain.core.voice.repository.UserVoiceRepository;
import ai.creatly.sky.creation.domain.core.voice.service.UserVoiceService;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.data.result.ApiListResult;
import com.jspeeder.core.data.result.ApiResult;
import com.jspeeder.core.data.result.ApiVoidResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.multipart.MultipartFile;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 声音定制API
 *
 * <AUTHOR>
 * @version UserMockingbirdCustomizeResource.java, v 0.1 2024-01-18 下午23:41 heb
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/user/mockingbird/user-voices", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class MockingbirdCustomizeUserResource {

    private final UserFileManager          userFileManager;
    private final UserVoiceService         userVoiceService;
    private final UserVoiceRepository      userVoiceRepository;
    private final VoiceMapper              voiceMapper;
    private final AiTaskRepository         aiTaskRepository;
    private final UserVoicePayAsyncManager userVoicePayAsyncManager;
    private final UserVoiceOrderMapper     userVoiceOrderMapper;
    private final UserVoiceTaskMapper      userVoiceTaskMapper;
    private final DeferredResultHelper     deferredResultHelper;
    private final VoiceLocaleMapper        voiceLocaleMapper;

    /**
     * 获取声音定制的语言选项
     *
     * @return 语言选项列表
     */
    @GetMapping("/locale-options")
    public ApiListResult<VoiceLocaleOptionVM> getVoiceLocaleOptions() {
        List<VoiceLocaleOptionVM> voiceLocaleOptionVMS = SynthesisVoiceNameEnum.getVoices()
                .stream()
                .map(Voice::getLocales)
                .flatMap(Collection::stream)
                .map(voiceLocaleMapper::toVoiceLocaleOptionVM)
                .distinct()
                .collect(Collectors.toList());
        return ApiListResult.ok(voiceLocaleOptionVMS);
    }

    /**
     * 用户定制声音上传头像
     *
     * @param file 头像图片
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, params = "type=avatar")
    public ApiResult<UserFileVM> uploadUserAvatar(@RequestPart MultipartFile file) {
        UserContext userContext = UserContextUtils.getUserContext();
        UserFileVM userFile = userFileManager.uploadAndCreate(file, FileType.IMAGE, FileBizSource.USER_VOICE_AVATAR, userContext);
        return ApiResult.ok(userFile);
    }

    /**
     * 用户定制声音上传音频
     *
     * @param file 音频文件
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, params = "type=audio")
    public ApiResult<UserFileVM> uploadUserAudio(@RequestPart MultipartFile file) {
        UserContext userContext = UserContextUtils.getUserContext();
        UserFileVM userFile = userFileManager.uploadAndCreate(file, FileType.AUDIO, FileBizSource.USER_VOICE_INPUT, userContext);
        return ApiResult.ok(userFile);
    }

    /**
     * 创建声音定制任务
     *
     * @param request 请求参数
     * @return 响应
     */
    @PostMapping(value = "/submit", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ApiResult<VoiceTaskSubmittedVM> submitUserVoiceCustomizeTask(@RequestBody @Valid UserVoiceCustomizeRequest request) {
        UserContext userContext = UserContextUtils.getUserContext();
        //检测声音是否存在中文名相同的
        if (userVoiceRepository.checkExistsByCnName(userContext.getouid(), request.getCnName())) {
            throw new BizException(VoiceErrorCode.USER_VOICE_CNAME_FOUND);
        }
        //检测任务运行中的
        AiTaskQO qo = new AiTaskQO()
                .setOwnerId(userContext.getouid())
                .setTaskType(AiTaskType.CREATE_USER_VOICE)
                .setStatuses(List.of(AiTaskStatus.CREATED, AiTaskStatus.RUNNING));
        AiTask aiTask = aiTaskRepository.queryLatestByCondition(qo).orElse(null);
        if (aiTask != null) {
            throw new BizException(VoiceErrorCode.USER_VOICE_RUNNING);
        }
        String taskId = userVoiceService.createUserVoiceTask(request, userContext);
        return ApiResult.ok(new VoiceTaskSubmittedVM().setTaskId(taskId));
    }

    /**
     * 获取当前最新的定制任务
     *
     * @return 返回结果
     */
    @GetMapping("/tasks/latest")
    public ApiResult<UserVoiceCustomizeTaskVM> getLatestUserVoiceCustomizeTask() {
        UserContext userContext = UserContextUtils.getUserContext();
        AiTaskQO qo = new AiTaskQO()
                .setOwnerId(userContext.getouid())
                .setTaskType(AiTaskType.CREATE_USER_VOICE)
                .setBizStatuses(List.of(UserVoiceTaskBizStatus.ongoing.name()));
        return aiTaskRepository.queryLatestByCondition(qo)
                .map(aiTask -> {
                    UserVoiceCreateParam param = aiTask.parseBizParams(UserVoiceCreateParam.class);
                    long voiceId = Long.parseLong(aiTask.getBizNo());
                    return userVoiceTaskMapper.toUserVoiceCustomizeTaskVM(aiTask, voiceId, param.getCnName());
                })
                .map(ApiResult::ok)
                .orElse(ApiResult.ok());
    }

    /**
     * 定制的声音列表（包括已定制/待付款的）
     */
    @PostMapping("/list")
    public ApiListResult<VoiceVM> searchUserVoices(@RequestBody VoiceCustomSearchRequest request) {
        UserContext userContext = UserContextUtils.getUserContext();
        List<Voice> voices = userVoiceRepository.queryByUidAndStatus(userContext.getouid(), request.getStatus());
        return ApiListResult.ok(voiceMapper.toVoiceVMs(voices));
    }

    /**
     * 获取定制声音的订单详情
     *
     * @param voiceId 声音ID
     * @return 响应结果
     */
    @GetMapping("/{voiceId}/order")
    public ApiResult<UserVoiceOrderVM> getUserVoiceOrder(@PathVariable String voiceId) {
        UserContext userContext = UserContextUtils.getUserContext();
        UserVoiceOrder userVoiceOrder = userVoiceService.queryVoiceOrder(voiceId, userContext.getouid());
        return ApiResult.ok(userVoiceOrderMapper.toUserVoiceOrderVM(userVoiceOrder));
    }

    /**
     * 付费（微信扫码支付）
     *
     * @param voiceId 付费声音ID
     * @return 微信扫码支付预支付结果
     */
    @PostMapping(path = "/{voiceId}/order/pay", params = "payMethod=wx-native")
    public ApiResult<UserVoiceWxNativePayResultVM> buyCustomVoice(@PathVariable String voiceId, HttpServletRequest request) {
        UserContext userContext = UserContextUtils.getUserContext();
        String payerClientIp = MvcUtil.getClientIp(request);
        UserVoiceWxNativePrepayResult prepayResult = userVoiceService.buyUserVoice(voiceId, payerClientIp, userContext);
        UserVoiceWxNativePayResultVM payResultVM = new UserVoiceWxNativePayResultVM()
                .setOrderId(prepayResult.getOrderId().toString())
                .setExpireAt(prepayResult.getExpireAt())
                .setCodeUrl(prepayResult.getCodeUrl());
        return ApiResult.ok(payResultVM);
    }

    /**
     * 获取声音定制订单的支付结果（长轮询，5分钟超时）
     *
     * @param voiceId 声音ID
     * @return 是否支付成功
     */
    @GetMapping(path = "/{voiceId}/order/pay-result")
    public DeferredResult<ApiVoidResult> getOrderPayResult(@PathVariable String voiceId) {
        long uid = UserContextUtils.getOuid();
        return deferredResultHelper.builder()
                .timeout(Duration.ofMinutes(5))
                .timeoutErrorCode(VoiceErrorCode.USER_VOICE_ORDER_PAY_POLL_TIMEOUT)
                .result(context -> userVoicePayAsyncManager.deferredResult(voiceId, uid, context))
                .build();
    }

    /**
     * 联系我们
     *
     * @param request 请求参数
     * @return 响应
     */
    @PostMapping(value = "/contact-us", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ApiVoidResult contactUs(@RequestBody @Valid UserVoiceContactRequest request) {
        UserContext userContext = UserContextUtils.getUserContext();
        userVoiceService.contactUs(request, userContext);
        return ApiVoidResult.ok();
    }
}
