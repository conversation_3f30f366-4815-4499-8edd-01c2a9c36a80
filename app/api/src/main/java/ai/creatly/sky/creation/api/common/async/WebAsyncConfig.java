/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.common.async;

import ai.creatly.sky.creation.domain.common.async.TaskExecutorHelper;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.task.ThreadPoolTaskExecutorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.accept.ContentNegotiationManager;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * @version WebAsyncConfig.java, v 0.1 2023-05-21 17:47 joton
 */
@Configuration
@RequiredArgsConstructor
public class WebAsyncConfig implements WebMvcConfigurer {

    private static final String WEB_ASYNC_TASK_EXECUTOR_BEAN_NAME = "WebAsyncTaskExecutor";

    private final BeanFactory               beanFactory;
    private final TaskExecutorHelper        taskExecutorHelper;
    private final HttpMessageConverters     httpMessageConverters;

    @Override
    public void addReturnValueHandlers(@NotNull List<HandlerMethodReturnValueHandler> handlers) {
        List<HttpMessageConverter<?>> converters = httpMessageConverters.getConverters();
        ContentNegotiationManager contentNegotiationManager = new ContentNegotiationManager();
        var delegate = new RequestResponseBodyMethodProcessor(converters, contentNegotiationManager);
        handlers.add(new ConcurrentResultReturnValueHandler(delegate));
    }

    /**
     * 由于引入了spring-messaging包，导致spring上下文中已经存在Executor实例，所以全局默认的ThreadPoolTaskExecutor实例并没有被创建出来。
     *
     * @param configurer Helps with configuring options for asynchronous request processing.
     * @see org.springframework.messaging.simp.config.AbstractMessageBrokerConfiguration
     * @see org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration
     */
    @Override
    public void configureAsyncSupport(@NotNull AsyncSupportConfigurer configurer) {
        if (beanFactory.containsBean(WEB_ASYNC_TASK_EXECUTOR_BEAN_NAME)) {
            Object taskExecutor = this.beanFactory.getBean(WEB_ASYNC_TASK_EXECUTOR_BEAN_NAME);
            if (taskExecutor instanceof AsyncTaskExecutor) {
                configurer.setTaskExecutor(((AsyncTaskExecutor) taskExecutor));
            }
        }
    }

    @Bean(name = WEB_ASYNC_TASK_EXECUTOR_BEAN_NAME)
    public ThreadPoolTaskExecutor webAsyncTaskExecutor() {
        ThreadPoolTaskExecutorBuilder builder = new ThreadPoolTaskExecutorBuilder();
        // todo 改成全局可配置属性
        builder.queueCapacity(512)
                .corePoolSize(256)
                .maxPoolSize(256)
                .allowCoreThreadTimeOut(true)
                .keepAlive(Duration.ofSeconds(60))
                .awaitTermination(true)
                .awaitTerminationPeriod(Duration.ofSeconds(20))
                .threadNamePrefix("web-async-")
                .customizers(taskExecutorHelper.rejectedHandlerCustomizer(WEB_ASYNC_TASK_EXECUTOR_BEAN_NAME));
        return builder.build();
    }
}
