/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.config.security.filter;

import ai.creatly.sky.creation.api.common.request.CachingBodyServletRequestWrapper;
import ai.creatly.sky.creation.api.common.util.MvcUtil;
import ai.creatly.sky.creation.api.config.security.error.BizAuthenticationException;
import ai.creatly.sky.creation.domain.core.auth.model.CustomizeUser;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import ai.creatly.sky.creation.domain.core.user.repository.UserRepository;
import ai.creatly.sky.creation.domain.support.credential.ClientCredentialRepository;
import ai.creatly.sky.creation.domain.support.credential.model.ClientCredential;
import ai.creatly.sky.creation.domain.support.credential.model.OpenApiToken;
import ai.creatly.sky.creation.domain.support.credential.model.RequestContent;
import ai.creatly.sky.creation.domain.support.credential.service.OpenApiService;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.model.BizStatus;
import com.jspeeder.core.model.OperatorSource;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.json.JSON;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 开放API统一认证
 *
 * <AUTHOR>
 * @version : OpenapiTokenAuthManager.java, v 1.0 2023年07月09日 20时28分 syoka Exp$
 */
@Component
@AllArgsConstructor
@Slf4j
public class OpenapiTokenAuthManager implements TokenAuthManager {

    private final static long SIGNATURE_TIMEOUT_MILLS = 5 * 60 * 1000;

    private final UserDetailsService         userDetailsService;
    private final ClientCredentialRepository clientCredentialRepository;
    private final UserRepository             userRepository;
    private final AppAlertHelper             appAlertHelper;
    private final OpenApiService             openApiService;

    /**
     * 从令牌中解析出签名信息
     *
     * @param token -
     * @return -
     */
    @Nullable
    private OpenApiToken parseOpenApiToken(String token) {
        String[] tokens = StringUtils.split(token, " ");
        String algo = tokens[0];
        tokens = StringUtils.split(tokens[1], ",");
        if (ArrayUtils.isNotEmpty(tokens) && tokens.length == 2) {
            String credentialPart = tokens[0];
            String signaturePart = tokens[1];

            if (StringUtils.isNotEmpty(credentialPart) && StringUtils.isNotEmpty(signaturePart)) {
                String[] credentialsArr = StringUtils.split(StringUtils.substring(credentialPart, 11), "/");
                String signature = StringUtils.substring(signaturePart, 10);
                return new OpenApiToken()
                        .setAlgo(algo)
                        .setAppKey(credentialsArr[0])
                        .setTimestamp(NumberUtils.toLong(credentialsArr[1]))
                        .setService(credentialsArr[2])
                        .setSignature(signature);
            }
        }
        return null;
    }

    /**
     * 判断签名的时间段是否在有效期内
     *
     * @param timestamp 客户端签名的时间戳
     * @return -
     */
    private boolean validateTimestamp(Long timestamp) {
        long serverTimes = System.currentTimeMillis();
        // 客户端签名时间戳要落在服务端时间的前5分钟后，且后10s前
        return timestamp >= serverTimes - SIGNATURE_TIMEOUT_MILLS && timestamp < serverTimes + 10_000;
    }

    @Override
    public HttpServletRequest authenticate(final HttpServletRequest request, final HttpServletResponse response) {
        String token = request.getHeader("Authorization");
        if (token == null) {
            return request;
        }
        String ip = MvcUtil.getClientIp(request);

        // 从鉴权令牌中还原请求签名信息
        OpenApiToken openApiToken = this.parseOpenApiToken(token);
        if (openApiToken == null) {
            String requestLog = MvcUtil.toLogMessage(request);
            appAlertHelper.alertText("[OPEN_API_AUTH]token invalid,request:{},token:{},ip:{}", requestLog, token, ip);
            throw new BizAuthenticationException(UserErrorCode.USER_OPENAPI_SIGN_INVALID);
        }

        // 验证签名算法是否正确
        if (!"XR1-HMAC-SHA256".equals(openApiToken.getAlgo())) {
            String requestLog = MvcUtil.toLogMessage(request);
            appAlertHelper.alertText("[OPEN_API_AUTH]sign algorithm invalid,request:{},token:{},ip:{}", requestLog, token, ip);
            throw new BizAuthenticationException(UserErrorCode.USER_OPENAPI_SIGN_INVALID);
        }

        // 签名有效期最多5分钟（允许超前10s）
        if (!this.validateTimestamp(openApiToken.getTimestamp())) {
            String requestLog = MvcUtil.toLogMessage(request);
            appAlertHelper.alertText("[OPEN_API_AUTH]sign timeout,request:{},token:{},ip:{}", requestLog, token, ip);
            throw new BizAuthenticationException(UserErrorCode.USER_OPENAPI_SIGN_EXPIRED);
        }

        final HttpServletRequest finalRequest;

        // 客户端凭证
        ClientCredential credential = clientCredentialRepository.getAppCredentialsByAppKey(openApiToken.getAppKey()).orElse(null);
        if (credential == null || BizStatus.VALID != credential.getStatus()) {
            String requestLog = MvcUtil.toLogMessage(request);
            appAlertHelper.alertText("[OPEN_API_AUTH]credential invalid,request:{},client:{},ip:{}", requestLog, credential, ip);
            throw new BizAuthenticationException(UserErrorCode.USER_OPENAPI_CREDENTIALS_INVALID);
        }
        if (credential.isExpired()) {
            String requestLog = MvcUtil.toLogMessage(request);
            appAlertHelper.alertText("[OPEN_API_AUTH]credential expired,request:{},client:{},ip:{}", requestLog, credential, ip);
            throw new BizAuthenticationException(UserErrorCode.ENTERPRISE_MEMBER_EXPIRED);
        }

        // 验签
        HttpUrl httpUrl = HttpUrl.get(request.getRequestURL().toString());
        RequestContent requestContent = new RequestContent()
                .setEncodedPath(httpUrl.encodedPath())
                .setHttpMethod(request.getMethod())
                .setParameterMap(request.getParameterMap());
        if (request.getContentLength() != 0 && MediaType.APPLICATION_JSON_VALUE.equals(request.getContentType())) {
            // 此处只是包装了一下，还没有实际读流
            finalRequest = new CachingBodyServletRequestWrapper(request);
            requestContent.setRequestJsonBody(() -> {
                try {
                    // 此处实际读流
                    return finalRequest.getInputStream();
                } catch (IOException e) {
                    String requestLog = MvcUtil.toLogMessage(request);
                    log.error("read request body error,request={}", requestLog, e);
                    appAlertHelper.alertText("[OPEN_API_AUTH]read request body error,request={}", requestLog, e);
                    throw new BizAuthenticationException(CommonErrorCode.UNSPECIFIED, e);
                }
            });
        } else {
            finalRequest = request;
        }
        boolean verified = openApiService.verifyOpenApiToken(openApiToken, credential, requestContent);
        if (!verified) {
            String requestLog = JSON.toJSONString(requestContent);
            log.info("[OPEN_API_AUTH]sign unmatched. request:{},token:{},ip={}", requestLog, openApiToken, ip);
            appAlertHelper.alertText("[OPEN_API_AUTH]sign unmatched,request:{},token:{},ip:{}", requestLog, openApiToken, ip);
            throw new BizAuthenticationException(UserErrorCode.USER_OPENAPI_SIGN_INVALID);
        }

        // 验签通过
        UserInfo userInfo = userRepository.queryOptionalById(credential.getUid()).orElse(null);
        Asserts.notNull(userInfo, "will not happen");
        UserDetails userDetails = userDetailsService.loadUserByUsername(userInfo.getUsername());

        // 设置登录态用户
        CustomizeUser customizeUser = (CustomizeUser) userDetails;
        customizeUser.setAppKey(credential.getAppKey());
        customizeUser.setAppKeyName(credential.getName());
        customizeUser.setSource(OperatorSource.OPEN_API);
        var authorities = customizeUser.getAuthorities();
        var authentication = UsernamePasswordAuthenticationToken.authenticated(customizeUser, null, authorities);
        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authentication);
        request.setAttribute(AuthTokenFilter.AUTHENTICATED_USERNAME, userDetails.getUsername());

        return finalRequest;
    }
}
