/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.common.async.sse;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 * @version SseEmitterExceptionHandler.java, v 0.1 2024-07-12 11:19 syoka
 */
public interface SseEmitterExceptionHandler {

    void handle(SseEmitter sseEmitter);
}
