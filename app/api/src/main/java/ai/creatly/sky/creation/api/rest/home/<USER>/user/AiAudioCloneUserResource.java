package ai.creatly.sky.creation.api.rest.home.secure.user;

import ai.creatly.sky.creation.api.common.openapi.PageQueryParam;
import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.biz.ai.audio.AudioCloneManager;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiAudioCloneVM;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioCloneRequest;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.PageBuilder;
import com.jspeeder.core.data.result.ApiPageResult;
import com.jspeeder.core.data.result.ApiResult;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RequestMapping(value = "/api/user/ai-audio-clone", produces = MediaType.APPLICATION_JSON_VALUE)
@Slf4j
@RestController
@RequiredArgsConstructor
public class AiAudioCloneUserResource {

    private final AudioCloneManager audioCloneManager;


    //创建克隆音色
    @PostMapping(path = "/submit", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ApiResult<String> createVoice(@RequestBody @Valid AudioCloneRequest request) {
        long taskId = audioCloneManager.submit(UserContextUtils.getUserContext(), request);
        return ApiResult.ok(Long.toString(taskId));
    }


    /**
     * 查询声音克隆
     *
     * @param pageable 分页参数
     * @return 生成记录
     */
    @GetMapping("/generations")
    @PageQueryParam
    public ApiPageResult<AiAudioCloneVM> queryGenerationPage(@PageableDefault Pageable pageable) {
        Page<AiAudioCloneVM> page = audioCloneManager.queryGenerationPage(UserContextUtils.getUserContext().getUid(), PageBuilder.buildFrom(pageable));
        return ApiPageResult.ok(page);
    }

    /**
     * 查询单一clone任务状态
     *
     * @return 生成记录
     */
    @GetMapping("/generations/{taskId}")
    @PageQueryParam
    public ApiResult<AiAudioCloneVM> queryGenerationOne(@PathVariable String taskId) {
        return ApiResult.ok(audioCloneManager.queryGenerationByTaskId(taskId));
    }


}
