/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.secure;

import ai.creatly.sky.creation.api.common.async.WebAsyncTaskHelper;
import ai.creatly.sky.creation.api.config.security.UserContextUtils;
import ai.creatly.sky.creation.biz.digitalhuman.DigitalHumanHotVideoManager;
import ai.creatly.sky.creation.domain.common.async.AsyncPools;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.digitalhuman.error.DigHumanErrorCode;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.request.HotVideoLinkResolveRequest;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.request.HotVideoRewriteRequest;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.response.AsrSegmentVM;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.response.ShortVideoNarrativeStyleVM;
import com.jspeeder.core.data.result.ApiListResult;
import com.jspeeder.core.data.result.ApiResult;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.WebAsyncTask;

import java.time.Duration;

/**
 * 爆款视频仿写API
 *
 * <AUTHOR>
 * @version HotAvatarVideoResource.java, v 0.1 2024-06-27 14:34 syoka
 */
@RestController
@RequestMapping("/api/dig-human/hot-video")
@Slf4j
@RequiredArgsConstructor
public class HotVideoResource {

    private final DigitalHumanHotVideoManager digitalHumanHotVideoManager;
    private final WebAsyncTaskHelper          webAsyncTaskHelper;

    /**
     * 视频链接解析
     *
     * @param request 爆款视频链接解析
     */
    @PostMapping("/link/resolve")
    public WebAsyncTask<ApiListResult<AsrSegmentVM>> resolveAudioFromVideoLink(@RequestBody HotVideoLinkResolveRequest request) {
        UserContext userContext = UserContextUtils.getUserContext();
        return webAsyncTaskHelper.builder()
                .timeout(Duration.ofSeconds(300))
                .timeoutErrorCode(DigHumanErrorCode.DH_HOT_VIDEO_LINK_RESOLVE_TIMEOUT)
                .threadPoolName(AsyncPools.DIGITAL_HUMAN_HOT_VIDEO_POOL)
                .futureListResult(() -> digitalHumanHotVideoManager.resolveAudioFromShortVideoLink(userContext, request.getLink()))
                .build();
    }

    /**
     * 爆款仿写功能
     *
     * @param request 爆款改写请求参数
     * @return 改写结果
     */
    @PostMapping("/content/rewrite")
    public WebAsyncTask<ApiResult<String>> contentRewrite(@Valid @RequestBody HotVideoRewriteRequest request) {
        return webAsyncTaskHelper.builder()
                .timeout(Duration.ofSeconds(100))
                .timeoutErrorCode(DigHumanErrorCode.DH_HOT_VIDEO_TEXT_REWRITE_TIMEOUT)
                .threadPoolName(AsyncPools.DIGITAL_HUMAN_HOT_VIDEO_POOL)
                .futureResult(() -> digitalHumanHotVideoManager.rewriteHotVideoContent(request))
                .build();
    }

    /**
     * 获取爆款仿写的叙事风格列表
     */
    @GetMapping("/narrative-style")
    public ApiListResult<ShortVideoNarrativeStyleVM> getNarrativeStyle() {
        return ApiListResult.ok(digitalHumanHotVideoManager.getNarrativeStyles());
    }
}
