/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.api.rest.home.pub;

import ai.creatly.sky.creation.domain.support.sitemetadata.*;
import com.jspeeder.core.data.result.ApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 网站元数据接口
 *
 * <AUTHOR>
 * @version SiteMetadataPublicResource.java, v 0.1 2024-05-11 下午4:14 zhoudong
 */
@RestController
@RequestMapping(value = "/api/public/site-metadata", produces = MediaType.APPLICATION_JSON_VALUE)
@Slf4j
@RequiredArgsConstructor
public class SiteMetadataResource {

    private static final Map<SiteSourceType, List<SiteMetadataItemVM>> REPOSITORY = new ConcurrentHashMap<>();

    static {
        REPOSITORY.put(SiteSourceType.creatlyai, List.of(
                new SiteMetadataItemVM(SiteMetadataCodeEnum.COPYRIGHT)
                        .put("text", "Made by Zhixingyuan Team."),
                new SiteMetadataItemVM(SiteMetadataCodeEnum.ICP_INFO)
                        .put("link", "https://beian.miit.gov.cn/")
                        .put("text", "沪ICP备2023018192号"),
                new SiteMetadataItemVM(SiteMetadataCodeEnum.ALGORITHM_BEIAN)
                        .put("link", "https://beian.cac.gov.cn/")
                        .put("list", "网信算备310112139741801240011号,网信算备310112139741801240029号"),
                new SiteMetadataItemVM(SiteMetadataCodeEnum.COMPANY)
                        .put("text", "上海知行元网络技术有限公司版权所有"),
                new SiteMetadataItemVM(SiteMetadataCodeEnum.WX_MP_QR_CODE_URL)
                        .put("url", "https://creatly.oss-cn-hangzhou.aliyuncs.com/creation/site/creatlyai/wx_mp_qrcode.webp"),
                new SiteMetadataItemVM(SiteMetadataCodeEnum.WX_HELPER_QR_CODE_URL)
                        .put("url", "https://creatly.oss-cn-hangzhou.aliyuncs.com/creation/site/creatlyai/wx_helper_qr_code_url.webp")
        ));
    }


    /**
     * 查询网站元数据
     *
     * @return -
     */
    @PostMapping
    public ApiResult<SiteMetadataVM> queryMetadata(@RequestBody SiteMetadataQueryBody queryBody) {
        SiteMetadataVM siteMetadataVM = new SiteMetadataVM().setItems(REPOSITORY.get(queryBody.getSourceType()));
        return ApiResult.ok(siteMetadataVM);
    }

}
