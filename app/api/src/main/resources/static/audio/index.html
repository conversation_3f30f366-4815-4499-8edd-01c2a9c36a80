<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/css/bootstrap.css" rel="stylesheet">
  <script src="https://cdn.bootcdn.net/ajax/libs/axios/1.3.6/axios.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
  <title>Document</title>
</head>
<body>
<div class="container">
  <div class="row">
    <div class="col-2">
      <button type="button" class="btn btn-primary" onclick="loadVoices()">加载声音列表</button>
    </div>
    <div class="col-4">
      <select class="form-select" id="voices" onchange="onSelectVoice(this)"></select>
    </div>
  </div>
  <br>
  <div class="row" id="selectRole" style="display: none;">
    <div class="col-2">
      <p>定制角色：</p>
    </div>
    <div class="col-4">
      <select class="form-select" id="roles"></select>
    </div>
  </div>
  <br>
  <div class="row" id="selectStyle" style="display: none;">
    <div class="col-2">
      <p>定制风格：</p>
    </div>
    <div class="col-4">
      <select class="form-select" id="styles"></select>
    </div>
  </div>
  <br>
  <div>
    <textarea id="text" cols="60" rows="5" placeholder="请输入文本，支持中英文"></textarea>
  </div>
  <br>
  <div>
    <button type="button" class="btn btn-primary" id="fetchAudio" onclick="fetchAudio()">请求音频数据流</button>
  </div>
  <div>
    <p>请求后将流式数据加载到以下的音频控件中</p>
  </div>
  <div>
    <audio id="myAudio" controls controlsList="nodownload" autoplay></audio>
  </div>
</div>

<script>
  const voicesMap = new Map();

  function loadVoices() {
    axios.get("/api/nlp/tts/voices").then(response => {
      $("#voices").empty();
      const voices = response.data.data;
      for (const voice of voices) {
        const option = $('<option>', {
          value: voice.name,
          text: `${voice.locale.desc} ${voice.displayName} ${voice.gender}`
        });
        $("#voices").append(option)
        voicesMap.set(voice.name, voice);
      }
    }).catch(error => {
      console.log('Error fetching voices:', error);
      console.log('Error fetching voices:', error.response);
      alert("Error fetching voices")
    });
  }

  function onSelectVoice(selectElement) {
    const voiceName = $(selectElement).val();
    const voice = voicesMap.get(voiceName);
    if (voice.roles && voice.roles.length > 0) {
      $('#roles').empty();
      $.each(voice.roles, function (index, item) {
        const option = $('<option>', {
          value: item.code,
          text: item.desc
        });
        $('#roles').append(option);
      });
      $('#selectRole').show();
    } else {
      $('#roles').empty();
      $('#selectRole').hide();
    }
    if (voice.styles && voice.styles.length > 0) {
      $('#styles').empty();
      $.each(voice.styles, function (index, item) {
        const option = $('<option>', {
          value: item.code,
          text: item.desc
        });
        $('#styles').append(option);
      });
      $('#selectStyle').show();
    } else {
      $('#styles').empty();
      $('#selectStyle').hide();
    }
  }

  function fetchAudio() {
    $('#fetchAudio').prop('disabled', true);
    $('#fetchAudio').text('合成中....');
    const text = $("#text").val();
    const voiceName = $("#voices").val();
    if (!voiceName) {
      alert("请先加载声音列表")
      return
    }
    if (!text) {
      alert("请输入文本")
      return
    }
    const role = $("#roles").val();
    const style = $("#styles").val();
    const data = {text, voiceName, role, style};
    axios.post('/api/nlp/tts/audio', data, {
      headers: {
        'Content-Type': 'application/json' // Set the desired content type
      },
      responseType: 'blob'
    }).then(response => {
      const contentType = response.headers['content-type'];
      if (contentType && contentType.startsWith('audio')) {
        // Play the audio
        const audioBlob = response.data;
        const audioURL = URL.createObjectURL(audioBlob);
        const audioElement = document.getElementById('myAudio');
        audioElement.src = audioURL;
      } else {
        // 非音乐数据，则一定是JSON数据返回
        const reader = new FileReader();
        reader.onload = function () {
          console.log(this.result instanceof String)
          if (typeof this.result === 'string') {
            const result = JSON.parse(this.result);
            alert(result.errorMsg)
          } else {
            const decoder = new TextDecoder('utf-8');
            const result = JSON.parse(decoder.decode(this.result));
            alert(result.errorMsg)
          }
        };
        reader.readAsText(response.data);
      }
    }).catch(error => {
      console.log('Error fetching audio:', error);
      console.log('Error fetching audio:', error.response);
      alert("Error fetching audio: " + JSON.stringify(error.response))
    }).finally(() => {
      $('#fetchAudio').prop('disabled', false);
      $('#fetchAudio').text('请求音频数据流');
    });
  }
</script>
</body>
</html>
