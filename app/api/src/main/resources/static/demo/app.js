let client = null;
let isConnected = false;

function setConnected(connected) {
  $("#connect").prop("disabled", connected);
  $("#disconnect").prop("disabled", !connected);
  if (connected) {
    $("#conversation").show();
  } else {
    $("#conversation").hide();
  }
  $("#greetings").html("");
  isConnected = connected;
}

function connect() {
  // 在前端工程化项目中，baseUrl应该是放在打包配置里的
  const baseUrl = window.location.origin.replace("http", "ws").replace("https", "wss");
  console.log(baseUrl)
  client = new StompJs.Client({
    brokerURL: baseUrl + "/ws"
  });
  client.onConnect = frame => {
    setConnected(true);
    console.log('Connected: ' + frame);
    client.subscribe('/topic/aigc', message => {
      const payload = JSON.parse(message.body);
      showText(payload.data.text);
    });
  }
  client.activate();
}

function disconnect() {
  if (client !== null) {
    client.deactivate();
  }
  setConnected(false);
  console.log("Disconnected");
}

function sendRecords() {
  if (isConnected) {
    // 输入校验
    const text = $("#record").val();
    if (!text) {
      alert("请输入提问内容！")
      return
    }

    let records = [];
    records.push(text);
    client.publish({
      destination: '/text/chat',
      body: JSON.stringify({'records': records})
    });
    $("#waiting").show();
  } else {
    $("#websocket_not_connected").addClass("show").show();
    window.setTimeout(function () {
      $("#websocket_not_connected").removeClass("show").hide();
    }, 1500);
  }
  $("#result").empty();
}

function showText(message) {
  $("#waiting").hide();
  $("#result").append(message);
}

$(function () {
  $("#websocket_not_connected").hide();
  $("#waiting").hide();
  $("form").on('submit', function (e) {
    e.preventDefault();
  });
  $("#connect").click(function () {
    connect();
  });
  $("#disconnect").click(function () {
    disconnect();
  });
  $("#send").click(function () {
    sendRecords();
  });
});
