plugins {
    id("ai.creatly.java-conventions")
}

dependencies {
    api(project(":app:biz"))

    // Web
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-webflux")

    // OpenApi (generate docs)
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui")
}
