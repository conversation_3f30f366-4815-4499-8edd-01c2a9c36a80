[{"id": 41225561548261370, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-free-member^MEMBER^free", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-free-member"}, {"key": "type", "dataType": "STRING", "value": "MEMBER"}, {"key": "code", "dataType": "STRING", "value": "free"}, {"key": "name", "dataType": "STRING", "value": "免费版"}, {"key": "value", "dataType": "OBJECT", "value": "{\"memberType\":\"FREE\",\"memberBgColor\":\"#9BA6BC\",\"memberCardBgImgUrl\":\"https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/subscription-plans/freeBgImg.png\",\"memberBadgeImgUrl\":\"\"}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-free-member^CREDITS^monthly_gift_credits", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-free-member"}, {"key": "type", "dataType": "STRING", "value": "CREDITS"}, {"key": "code", "dataType": "STRING", "value": "monthly_gift_credits"}, {"key": "name", "dataType": "STRING", "value": "元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"totalCredits\":500,\"topUpCredits\":0,\"giftCredits\":500}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-free-member^ABILITY^ip_video_free", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-free-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "ip_video_free"}, {"key": "name", "dataType": "STRING", "value": "IP 成片"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"ip_video_free\"}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-free-member^ABILITY^product_ads_video_free", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-free-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "product_ads_video_free"}, {"key": "name", "dataType": "STRING", "value": "商品讲解广告"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"product_ads_video_free\"}"}]}, {"id": 41225561548262374, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-free-member^ABILITY^drama_ads_video_free", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-free-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "drama_ads_video_free"}, {"key": "name", "dataType": "STRING", "value": "剧情广告片"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"drama_ads_video_free\"}"}]}, {"id": 41225561548261372, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-free-member^ABILITY^mockingbird_free", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-free-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "mockingbird_free"}, {"key": "name", "dataType": "STRING", "value": "声音演员"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"mockingbird_free\"}"}]}, {"id": 41225561548261373, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-free-member^ABILITY^magical_brush_free", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-free-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "magical_brush_free"}, {"key": "name", "dataType": "STRING", "value": "神笔马良"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"magical_brush_free\"}"}]}, {"id": 40836122833586170, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-basic-member^MEMBER^basic", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-basic-member"}, {"key": "type", "dataType": "STRING", "value": "MEMBER"}, {"key": "code", "dataType": "STRING", "value": "basic"}, {"key": "name", "dataType": "STRING", "value": "普通版"}, {"key": "value", "dataType": "OBJECT", "value": "{\"memberType\":\"BASIC\",\"memberBgColor\":\"#295A3D\",\"memberCardBgImgUrl\":\"https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/subscription-plans/basicBgImg.png\",\"memberBadgeImgUrl\":\"https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/subscription-plans/basicBadge.png\"}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-basic-member^CREDITS^gift_credits", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-basic-member"}, {"key": "type", "dataType": "STRING", "value": "CREDITS"}, {"key": "code", "dataType": "STRING", "value": "gift_credits"}, {"key": "name", "dataType": "STRING", "value": "元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"totalCredits\":9000,\"topUpCredits\":0,\"giftCredits\":9000}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-basic-member^TOP_UP_MORE_CREDITS^top-up-5", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-basic-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-5"}, {"key": "name", "dataType": "STRING", "value": "充5元送5元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"topUpPlanId\":\"top-up-5\",\"giftCredits\":5}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-basic-member^TOP_UP_MORE_CREDITS^top-up-10", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-basic-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-10"}, {"key": "name", "dataType": "STRING", "value": "充10元送10元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"topUpPlanId\":\"top-up-10\",\"giftCredits\":10}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-basic-member^TOP_UP_MORE_CREDITS^top-up-20", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-basic-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-20"}, {"key": "name", "dataType": "STRING", "value": "充20元送20元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"topUpPlanId\":\"top-up-20\",\"giftCredits\":20}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-basic-member^TOP_UP_MORE_CREDITS^top-up-50", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-basic-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-50"}, {"key": "name", "dataType": "STRING", "value": "充50元送50元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"topUpPlanId\":\"top-up-50\",\"giftCredits\":50}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-basic-member^TOP_UP_MORE_CREDITS^top-up-100", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-basic-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-100"}, {"key": "name", "dataType": "STRING", "value": "充100元送100元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"topUpPlanId\":\"top-up-100\",\"giftCredits\":100}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-basic-member^ABILITY^ip_video_basic", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-basic-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "ip_video_basic"}, {"key": "name", "dataType": "STRING", "value": "IP成片（高清）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"ip_video_basic\"}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-basic-member^ABILITY^product_ads_video_basic", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-basic-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "product_ads_video_basic"}, {"key": "name", "dataType": "STRING", "value": "商品讲解广告（高清）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"product_ads_video_basic\"}"}]}, {"id": 40836122833586278, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-basic-member^ABILITY^drama_ads_video_basic", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-basic-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "drama_ads_video_basic"}, {"key": "name", "dataType": "STRING", "value": "剧情广告片（高清）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"drama_ads_video_basic\"}"}]}, {"id": 40836122833586178, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-basic-member^ABILITY^magical_brush_basic", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-basic-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "magical_brush_basic"}, {"key": "name", "dataType": "STRING", "value": "神笔马良（高清画质）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"magical_brush_basic\"}"}]}, {"id": 40836122833586179, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-basic-member^ABILITY^mockingbird_basic", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-basic-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "mockingbird_basic"}, {"key": "name", "dataType": "STRING", "value": "声音演员（高清音质）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"mockingbird_basic\"}"}]}, {"id": 40843607724263420, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-premium-member^MEMBER^premium", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-premium-member"}, {"key": "type", "dataType": "STRING", "value": "MEMBER"}, {"key": "code", "dataType": "STRING", "value": "premium"}, {"key": "name", "dataType": "STRING", "value": "高级版"}, {"key": "value", "dataType": "OBJECT", "value": "{\"memberType\":\"PREMIUM\",\"memberBgColor\":\"#F1E1C6\",\"memberCardBgImgUrl\":\"https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/subscription-plans/premiumBgImg.png\",\"memberBadgeImgUrl\":\"https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/subscription-plans/premiumBadge.png\"}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-premium-member^CREDITS^gift_credits", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-premium-member"}, {"key": "type", "dataType": "STRING", "value": "CREDITS"}, {"key": "code", "dataType": "STRING", "value": "gift_credits"}, {"key": "name", "dataType": "STRING", "value": "元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"totalCredits\":19800,\"topUpCredits\":0,\"giftCredits\":19800}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-premium-member^TOP_UP_MORE_CREDITS^top-up-5", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-premium-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-5"}, {"key": "name", "dataType": "STRING", "value": "充5元送10元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"topUpPlanId\":\"top-up-5\",\"giftCredits\":10}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-premium-member^TOP_UP_MORE_CREDITS^top-up-10", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-premium-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-10"}, {"key": "name", "dataType": "STRING", "value": "充10元送30元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"topUpPlanId\":\"top-up-10\",\"giftCredits\":30}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-premium-member^TOP_UP_MORE_CREDITS^top-up-20", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-premium-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-20"}, {"key": "name", "dataType": "STRING", "value": "充20元送80元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"topUpPlanId\":\"top-up-20\",\"giftCredits\":80}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-premium-member^TOP_UP_MORE_CREDITS^top-up-50", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-premium-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-50"}, {"key": "name", "dataType": "STRING", "value": "充50元送260元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"topUpPlanId\":\"top-up-50\",\"giftCredits\":260}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-premium-member^TOP_UP_MORE_CREDITS^top-up-100", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-premium-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-100"}, {"key": "name", "dataType": "STRING", "value": "充100元送600元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"topUpPlanId\":\"top-up-100\",\"giftCredits\":600}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-premium-member^ABILITY^ip_video_premium", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-premium-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "ip_video_premium"}, {"key": "name", "dataType": "STRING", "value": "IP成片（4K）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"ip_video_premium\"}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-premium-member^ABILITY^product_ads_video_premium", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-premium-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "product_ads_video_premium"}, {"key": "name", "dataType": "STRING", "value": "商品讲解广告（4K）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"product_ads_video_premium\"}"}]}, {"id": 40843607724263528, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-premium-member^ABILITY^drama_ads_video_premium", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-premium-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "drama_ads_video_premium"}, {"key": "name", "dataType": "STRING", "value": "剧情广告片（4K）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"drama_ads_video_premium\"}"}]}, {"id": 40843607724263428, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-premium-member^ABILITY^magical_brush_premium", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-premium-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "magical_brush_premium"}, {"key": "name", "dataType": "STRING", "value": "神笔马良（4K画质）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"magical_brush_premium\"}"}]}, {"id": 40843607724263429, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-premium-member^ABILITY^mockingbird_premium", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-premium-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "mockingbird_premium"}, {"key": "name", "dataType": "STRING", "value": "声音演员（无损音质）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"mockingbird_premium\"}"}]}, {"id": 40852717391841280, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^MEMBER^enterprise", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "MEMBER"}, {"key": "code", "dataType": "STRING", "value": "enterprise"}, {"key": "name", "dataType": "STRING", "value": "企业版"}, {"key": "value", "dataType": "OBJECT", "value": "{\"memberType\":\"ENTERPRISE\",\"memberBgColor\":\"#1A1A1A\",\"memberCardBgImgUrl\":\"https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/subscription-plans/enterpriseBgImg.png\",\"memberBadgeImgUrl\":\"https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/subscription-plans/enterpriseBadge.png\"}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^CREDITS^gift_credits", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "CREDITS"}, {"key": "code", "dataType": "STRING", "value": "gift_credits"}, {"key": "name", "dataType": "STRING", "value": "元气（按需定制）"}, {"key": "value", "dataType": "OBJECT", "value": null}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^TOP_UP_MORE_CREDITS^top-up-5", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-5"}, {"key": "name", "dataType": "STRING", "value": "按需定制"}, {"key": "value", "dataType": "OBJECT", "value": null}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^TOP_UP_MORE_CREDITS^top-up-10", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-10"}, {"key": "name", "dataType": "STRING", "value": "按需定制"}, {"key": "value", "dataType": "OBJECT", "value": null}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^TOP_UP_MORE_CREDITS^top-up-20", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-20"}, {"key": "name", "dataType": "STRING", "value": "按需定制"}, {"key": "value", "dataType": "OBJECT", "value": null}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^TOP_UP_MORE_CREDITS^top-up-50", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-50"}, {"key": "name", "dataType": "STRING", "value": "按需定制"}, {"key": "value", "dataType": "OBJECT", "value": null}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^TOP_UP_MORE_CREDITS^top-up-100", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "TOP_UP_MORE_CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top-up-100"}, {"key": "name", "dataType": "STRING", "value": "按需定制"}, {"key": "value", "dataType": "OBJECT", "value": null}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^ABILITY^ip_video_enterprise", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "ip_video_enterprise"}, {"key": "name", "dataType": "STRING", "value": "IP成片（按需定制）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"ip_video_enterprise\"}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^ABILITY^product_ads_video_enterprise", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "product_ads_video_enterprise"}, {"key": "name", "dataType": "STRING", "value": "商品讲解广告（按需定制）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"product_ads_video_enterprise\"}"}]}, {"id": 40852717391841388, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^ABILITY^drama_ads_video_enterprise", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "drama_ads_video_enterprise"}, {"key": "name", "dataType": "STRING", "value": "剧情广告片（按需定制）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"drama_ads_video_enterprise\"}"}]}, {"id": 40852717391841288, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^ABILITY^magical_brush_enterprise", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "magical_brush_enterprise"}, {"key": "name", "dataType": "STRING", "value": "神笔马良（按需定制）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"magical_brush_enterprise\"}"}]}, {"id": 40852717391841289, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^ABILITY^mockingbird_enterprise", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "mockingbird_enterprise"}, {"key": "name", "dataType": "STRING", "value": "声音演员（按需定制）"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"mockingbird_enterprise\"}"}]}, {"id": 40852717391841292, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "subscription-enterprise-member^ABILITY^openapi", "props": [{"key": "planId", "dataType": "STRING", "value": "subscription-enterprise-member"}, {"key": "type", "dataType": "STRING", "value": "ABILITY"}, {"key": "code", "dataType": "STRING", "value": "openapi"}, {"key": "name", "dataType": "STRING", "value": "开放平台API"}, {"key": "value", "dataType": "OBJECT", "value": "{\"abilityCode\":\"openapi\"}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "top-up-5^CREDITS^top_up_credits", "props": [{"key": "planId", "dataType": "STRING", "value": "top-up-5"}, {"key": "type", "dataType": "STRING", "value": "CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top_up_credits"}, {"key": "name", "dataType": "STRING", "value": "元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"totalCredits\":500,\"topUpCredits\":500,\"giftCredits\":0}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "top-up-10^CREDITS^top_up_credits", "props": [{"key": "planId", "dataType": "STRING", "value": "top-up-10"}, {"key": "type", "dataType": "STRING", "value": "CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top_up_credits"}, {"key": "name", "dataType": "STRING", "value": "元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"totalCredits\":1000,\"topUpCredits\":1000,\"giftCredits\":0}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "top-up-20^CREDITS^top_up_credits", "props": [{"key": "planId", "dataType": "STRING", "value": "top-up-20"}, {"key": "type", "dataType": "STRING", "value": "CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top_up_credits"}, {"key": "name", "dataType": "STRING", "value": "元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"totalCredits\":2000,\"topUpCredits\":2000,\"giftCredits\":0}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "top-up-50^CREDITS^top_up_credits", "props": [{"key": "planId", "dataType": "STRING", "value": "top-up-50"}, {"key": "type", "dataType": "STRING", "value": "CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top_up_credits"}, {"key": "name", "dataType": "STRING", "value": "元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"totalCredits\":5000,\"topUpCredits\":5000,\"giftCredits\":0}"}]}, {"id": *****************, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "schemaCode": "plan_benefit", "uniqueValue": "top-up-100^CREDITS^top_up_credits", "props": [{"key": "planId", "dataType": "STRING", "value": "top-up-100"}, {"key": "type", "dataType": "STRING", "value": "CREDITS"}, {"key": "code", "dataType": "STRING", "value": "top_up_credits"}, {"key": "name", "dataType": "STRING", "value": "元气"}, {"key": "value", "dataType": "OBJECT", "value": "{\"totalCredits\":10000,\"topUpCredits\":10000,\"giftCredits\":0}"}]}]