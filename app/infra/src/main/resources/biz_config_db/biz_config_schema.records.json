[{"id": 40717913396611072, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "code": "plan", "name": "付费计划", "description": "", "uniqueKeys": ["id"], "props": [{"key": "id", "dataType": "STRING", "comment": "计划ID", "description": "全局唯一", "enumerable": false, "enums": [], "constraints": [{"type": "NOT_BLANK", "message": "计划ID不能为空"}], "dataSchema": null}, {"key": "type", "dataType": "STRING", "comment": "计划类型", "description": "", "enumerable": true, "enums": [{"code": "SUBSCRIPTION", "desc": "订阅计划"}, {"code": "TOP_UP", "desc": "充值计划"}], "constraints": [{"type": "NOT_BLANK", "message": "计划类型不能为空"}], "dataSchema": null}, {"key": "name", "dataType": "STRING", "comment": "计划名称", "description": "", "enumerable": false, "enums": [], "constraints": [{"type": "NOT_BLANK", "message": "计划名称不能为空"}], "dataSchema": null}, {"key": "description", "dataType": "STRING", "comment": "计划描述", "description": "", "enumerable": false, "enums": [], "constraints": [{"type": "NOT_NULL", "message": "计划描述不能为空"}], "dataSchema": null}, {"key": "buyMethod", "dataType": "STRING", "comment": "购买方式", "description": "", "enumerable": true, "enums": [{"code": "ONLINE_PAY", "desc": "在线支付"}, {"code": "FACE_TO_FACE", "desc": "线下面谈"}], "constraints": [{"type": "NOT_NULL", "message": "购买方式不能为空"}], "dataSchema": null}, {"key": "originalFee", "dataType": "INT_64", "comment": "原价", "description": "单位：分", "enumerable": false, "enums": [], "constraints": [], "dataSchema": null}, {"key": "realFee", "dataType": "INT_64", "comment": "实价", "description": "单位：分", "enumerable": false, "enums": [], "constraints": [], "dataSchema": null}, {"key": "orderTitle", "dataType": "STRING", "comment": "订单标题", "description": "下单时使用", "enumerable": false, "enums": [], "constraints": [], "dataSchema": null}, {"key": "periodType", "dataType": "STRING", "comment": "计划周期类型", "description": "", "enumerable": true, "enums": [{"code": "MONTHLY", "desc": "每月"}, {"code": "ANNUALLY", "desc": "每年"}, {"code": "ONE_TIME", "desc": "一次性"}], "constraints": [{"type": "NOT_BLANK", "message": "计划周期类型不能为空"}], "dataSchema": null}, {"key": "autoRenewed", "dataType": "BOOL", "comment": "到期是否自动续费", "description": "", "enumerable": false, "enums": [], "constraints": [{"type": "NOT_NULL", "message": "到期是否自动续费不能为空"}], "dataSchema": null}, {"key": "level", "dataType": "INT_32", "comment": "计划阶梯档位", "description": "用于排序、有序展示", "enumerable": false, "enums": [], "constraints": [{"type": "NOT_NULL", "message": "到期是否自动续费不能为空"}], "dataSchema": null}]}, {"id": 40717913396611073, "createdAt": "2023-10-15T11:25:35.209+08:00", "updatedAt": "2023-10-15T11:25:35.209+08:00", "code": "plan_benefit", "name": "付费计划内的权益", "description": "", "uniqueKeys": ["planId", "type", "code"], "props": [{"key": "planId", "dataType": "STRING", "comment": "所属付费计划ID", "description": "", "enumerable": false, "enums": [], "constraints": [{"type": "NOT_BLANK", "message": "所属付费计划ID不能为空"}], "dataSchema": null}, {"key": "type", "dataType": "STRING", "comment": "权益类型", "description": "", "enumerable": true, "enums": [{"code": "MEMBER", "desc": "会员"}, {"code": "CREDITS", "desc": "余额"}, {"code": "ABILITY", "desc": "能力"}, {"code": "TOP_UP_MORE_CREDITS", "desc": "充值送余额"}], "constraints": [{"type": "NOT_BLANK", "message": "所属付费计划ID不能为空"}], "dataSchema": null}, {"key": "code", "dataType": "STRING", "comment": "权益编号", "description": "", "enumerable": false, "enums": [], "constraints": [{"type": "NOT_BLANK", "message": "权益编号不能为空"}], "dataSchema": null}, {"key": "name", "dataType": "STRING", "comment": "权益名称", "description": "", "enumerable": false, "enums": [], "constraints": [{"type": "NOT_BLANK", "message": "权益名称不能为空"}], "dataSchema": null}, {"key": "value", "dataType": "OBJECT", "comment": "权益内容", "description": "JSON对象，根据不同权益类型区分不同模型，在消费时自行转换", "enumerable": false, "enums": [], "constraints": [{"type": "NOT_BLANK", "message": "权益内容不能为空"}], "dataSchema": null}]}]