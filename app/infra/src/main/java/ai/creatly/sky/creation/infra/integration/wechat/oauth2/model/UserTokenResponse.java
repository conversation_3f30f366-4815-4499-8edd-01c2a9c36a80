/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.wechat.oauth2.model;

import ai.creatly.sky.creation.infra.integration.wechat.common.WechatResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version WechatUserTokenResponse.java, v 0.1 2024-03-31 11:00 syoka
 */
@Data
public class UserTokenResponse extends WechatResponse {

    @JsonProperty("access_token")
    private String  accessToken;
    @JsonProperty("expires_in")
    private Integer expiresIn;
    @JsonProperty("refresh_token")
    private String  refreshToken;
    private String  openid;
    private String  scope;
    @JsonProperty("unionid")
    private String  unionId;
}
