package ai.creatly.sky.creation.infra.ai.video;

import ai.creatly.sky.creation.domain.core.ai.video.model.KlingVideoLipSyncVM;
import ai.creatly.sky.creation.domain.core.ai.video.service.KlingLipSyncService;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.infra.dal.tables.daos.AiVoiceCloneDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiVoiceClone;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static ai.creatly.sky.creation.infra.dal.tables.AiVoiceCloneTable.CREATION_AI_VOICE_CLONE;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KlingLipSyncServiceImpl implements KlingLipSyncService {

    @Autowired
    AiVoiceCloneDAO aiVoiceCloneDAO;
    @Autowired
    UserFileHelper  userFileHelper;
    private final DSLContext dsl;

    @Override
    public List<KlingVideoLipSyncVM> getLipSyncList(String language, long uid) {

        List<KlingVideoLipSyncVM> vmList = new ArrayList<>();
        Condition condition = DSL.trueCondition()
                .and(CREATION_AI_VOICE_CLONE.BIZ_TYPE.eq("video_lip"))
                .and(CREATION_AI_VOICE_CLONE.LANGUAGE.eq(language));

        List<CreationAiVoiceClone> list = dsl.selectFrom(CREATION_AI_VOICE_CLONE)
                .where(condition)
                .orderBy(CREATION_AI_VOICE_CLONE.UPDATED_AT.desc())
                .fetchStreamInto(CreationAiVoiceClone.class)
                .toList();

        for (CreationAiVoiceClone clone : list) {
            KlingVideoLipSyncVM vm = new KlingVideoLipSyncVM();
            vm.setVoiceId(clone.getCode());
            vm.setName(clone.getCnName());
            vm.setUrl(userFileHelper.getHttpUrl(clone.getAudioUrl(),uid));
            vm.setLanguage(clone.getLanguage());
            vmList.add(vm);
        }

        return vmList;
    }
}
