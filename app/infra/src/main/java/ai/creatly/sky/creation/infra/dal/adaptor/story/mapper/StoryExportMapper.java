/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.story.mapper;

import ai.creatly.sky.creation.domain.core.story.model.story.StoryExport;
import ai.creatly.sky.creation.infra.dal.common.ModelMapper;
import ai.creatly.sky.creation.infra.dal.common.UpdatableBuilder;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationStoryExport;
import ai.creatly.sky.creation.infra.dal.tables.records.StoryExportRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_STORY_EXPORT;

/**
 * <AUTHOR>
 * @version StoryExportMapper.java, v 0.1 2024-05-08 23:56 heb
 */
@Mapper(config = BaseMapperConfig.class)
public interface StoryExportMapper extends ModelMapper<StoryExport, CreationStoryExport, StoryExportRecord> {

    @Override
    CreationStoryExport toEntity(StoryExport storyExport);

    @Override
    StoryExport toModel(CreationStoryExport creationStoryExport);

    @Override
    default void updatable(UpdatableBuilder<StoryExportRecord> builder) {
        builder.updatable(CREATION_STORY_EXPORT.ID)
                .updatable(CREATION_STORY_EXPORT.STATUS)
                .updatable(CREATION_STORY_EXPORT.UID)
                .updatable(CREATION_STORY_EXPORT.DOWN_FILE_ID)
                .updatable(CREATION_STORY_EXPORT.DOWN_FILE_URL)
                .updatable(CREATION_STORY_EXPORT.EXPORT_FORMAT);
    }
}
