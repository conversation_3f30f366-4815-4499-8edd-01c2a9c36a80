/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.drama.preprocess;

import ai.creatly.sky.creation.domain.core.drama.client.DramaPreprocessClient;
import ai.creatly.sky.creation.domain.core.drama.client.model.DramaPreprocessResult;
import ai.creatly.sky.creation.domain.core.drama.model.Drama;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.infra.integration.drama.preprocess.api.DramaPreprocessApi;
import ai.creatly.sky.creation.infra.integration.drama.preprocess.model.DramaPreprocessRequest;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version DramaPreprocessClientImpl.java, 2024-10-21 下午8:18 zhoudong
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DramaPreprocessClientImpl implements DramaPreprocessClient {

    private final DramaPreprocessApi dramaPreprocessApi;
    private final UserFileRepository userFileRepository;

    @Override
    public DramaPreprocessResult preprocess(Drama drama) {
        UserFile firstVideoFile = userFileRepository.queryById(drama.getVideos().getFirst().getFileId());
        DramaPreprocessRequest request = new DramaPreprocessRequest()
                .setBucket(firstVideoFile.getBucket())
                .setDramaId(drama.getId())
                .setEndText(drama.getVideos().getFirst().getEndText());
        JSONObject response;
        try {
            response = dramaPreprocessApi.preprocess(request);
        } catch (Exception e) {
            log.error("[preprocess]error,request={}", request, e);
            throw new SysException(CommonErrorCode.REMOTE_API_INVOKE_ERROR, "剧情预处理", e);
        }
        JSONObject content = response.getJSONObject("content");
        String contentJson = JSON.toJSONString(content);
        Asserts.notBlank(contentJson, CommonErrorCode.REMOTE_API_RESULT_UNEXPECTED, "剧情预处理");

        return new DramaPreprocessResult().setContent(contentJson);
    }
}
