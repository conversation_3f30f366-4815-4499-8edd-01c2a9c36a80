/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.story.mapper;

import ai.creatly.sky.creation.domain.core.story.model.scene.SceneStatus;
import ai.creatly.sky.creation.domain.core.story.model.scene.StoryScene;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryStyle;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationStoryScene;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.mapper.EnumValueMapping;
import com.jspeeder.core.data.repository.PojoMapper;
import com.jspeeder.core.model.OperatorRef;
import com.jspeeder.core.util.json.JSON;
import org.jooq.JSONB;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version StorySceneMapper.java, v 0.1 2024-03-09 23:56 heb
 */
@Mapper(config = BaseMapperConfig.class)
public interface StorySceneMapper extends PojoMapper<StoryScene, CreationStoryScene> {

    @Override
    CreationStoryScene toEntity(StoryScene scene);

    @Override
    @Mapping(target = "atmosphere", ignore = true)
    StoryScene toModel(CreationStoryScene creationStory);

    default JSONB styleToJsonb(StoryStyle storyStyle) {
        if (storyStyle == null) {
            return null;
        }
        return JSONB.jsonbOrNull(JSON.toJSONString(storyStyle));
    }

    default StoryStyle jsonbToStyle(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseObject(jsonb.data(), StoryStyle.class);
    }

    default OperatorRef jsonbToOperatorRef(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseObject(jsonb.data(), OperatorRef.class);
    }

    default JSONB operatorRefToJsonb(OperatorRef operatorRef) {
        if (operatorRef == null) {
            return null;
        }
        return JSONB.jsonbOrNull(JSON.toJSONString(operatorRef));
    }

    @EnumValueMapping
    SceneStatus stringToStoryStatus(String status);
}
