package ai.creatly.sky.creation.infra.dal.adaptor.audio;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiAudioCloneVM;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiAudioTaskResult;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioCloneTaskInput;
import ai.creatly.sky.creation.domain.core.ai.audio.repository.AiAudioCloneRepository;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskBizType;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskQO;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.infra.dal.tables.daos.AiVoiceCloneDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiVoiceClone;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version AudioCloneRepositoryImpl.java, v0.1 2025-05-05 10:35
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AudioCloneRepositoryImpl implements AiAudioCloneRepository {

    private final AiVoiceCloneDAO aiVoiceCloneDAO;
    private final UserFileHelper  userFileHelper;
    private final AiTaskRepository aiTaskRepository;

    @Override
    public Page<AiAudioCloneVM> queryPages(Long uid, Pageable pageable) {

        AiTaskQO aiTaskQO = new AiTaskQO();
        aiTaskQO.setBizType(AiTaskBizType.audio_clone);
        aiTaskQO.setOwnerId(uid);
        Page<AiTask> page = aiTaskRepository.queryPage(aiTaskQO,pageable);

        List<AiAudioCloneVM> vmList = page.map(item -> {
            AiAudioCloneVM vm = new AiAudioCloneVM();
            // 手动转换类型不一致的字段
            vm.setId(item.getId() != null ? item.getId().toString() : null);
            vm.setUid(item.getOwnerId() != null ? item.getOwnerId().toString() : null);
            var taskInput = item.parseBizInput(AudioCloneTaskInput.class);
            var taskResult = item.parseBizResult(AiAudioTaskResult.class);
            if (taskInput.getRequest().getAudioFile() != null && taskInput.getRequest().getAudioFile().getUrl().startsWith("oss")) {
                vm.setAudioUrl(userFileHelper.getHttpUrl(taskInput.getRequest().getAudioFile().getUrl(), FileAcl.PRIVATE));
            }
            if ((taskInput.getRequest().getPhotoFile() != null && taskInput.getRequest().getPhotoFile().getUrl().startsWith("oss"))) {
                vm.setAvatar(userFileHelper.getHttpUrl(taskInput.getRequest().getPhotoFile().getUrl(), FileAcl.PRIVATE));
            }
            vm.setCnName(taskInput.getRequest().getAudioName());
            if (taskResult.getAssets() != null && !taskResult.getAssets().isEmpty()) {
                vm.setCode(taskResult.getAssets().getFirst() != null ? taskResult.getAssets().getFirst().getId() : null);
            }
            vm.setStatus(item.getStatus());
            vm.setTaskId(item.getId().toString());
            if (item.getBizResult() != null && item.getBizResult().has("errorMsg")) {
                vm.setErrorMsg(item.getBizResult().getString("errorMsg"));
            }
            return vm;
        }).getContent();

        return Page.of(vmList, pageable, page.getTotal());
    }

    @Override
    public AiAudioCloneVM searchVoice(Long voiceId){
        CreationAiVoiceClone voice = aiVoiceCloneDAO.fetchOneById(voiceId);
        if (voice != null) {
            AiAudioCloneVM vm = new AiAudioCloneVM();
            BeanUtils.copyProperties(voice, vm);
            vm.setId(voice.getId() != null ? voice.getId().toString() : null);
            vm.setUid(voice.getUid() != null ? voice.getUid().toString() : null);
            if (vm.getAudioUrl() != null && vm.getAudioUrl().startsWith("oss")) {
                vm.setAudioUrl(userFileHelper.getHttpUrl(vm.getAudioUrl(), FileAcl.PRIVATE));
            }
            if (vm.getAvatar() != null && vm.getAvatar().startsWith("oss")) {
                vm.setAvatar(userFileHelper.getHttpUrl(vm.getAvatar(), FileAcl.PRIVATE));
            }
            return vm;
        }
        return null;
    }
}
