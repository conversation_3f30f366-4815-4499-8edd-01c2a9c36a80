/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.asset.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version SegmentedVideo.java, 2024-11-01 下午6:52 zhoudong
 */
@Data
public class SegmentedVideo {

    /**
     * 仅文件名（不含目录）
     */
    @JsonProperty("objectKey")
    private String filename;
    @JsonProperty("duration")
    private Long   durationMills;
    @JsonProperty("size")
    private Long   sizeInBytes;
}
