/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.cameramotion.config;

import ai.creatly.sky.creation.infra.integration.ProxyName;
import lombok.Data;
import org.jetbrains.annotations.Nullable;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version CameraMotionProperties.java, v 0.1 2024-09-19 下午7:02 zhoudong
 */
@Data
@ConfigurationProperties(prefix = "application.camera-motion")
public class CameraMotionProperties {
    private String    baseUrl;
    @Nullable
    private ProxyName proxy;
}
