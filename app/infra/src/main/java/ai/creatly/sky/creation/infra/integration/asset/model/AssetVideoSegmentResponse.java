/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.asset.model;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version AssetVideoSegmentResponse.java, 2024-11-01 下午6:00 zhoudong
 */
@Data
public class AssetVideoSegmentResponse {

    private boolean              success;
    private List<SegmentedVideo> content;

    public boolean isSuccess() {
        return success || content == null || content.isEmpty();
    }
}
