/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.infra.dal.adaptor.aichat.chatmsg;

import ai.creatly.sky.creation.domain.support.aichat.langchain.message.ChatMessageService;
import ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal.LLMConversation;
import ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal.LLMMessage;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.LLMChainContext;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.util.json.JSON;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@ConditionalOnProperty(name = "application.chat.message.loader", havingValue = "redis")
@RequiredArgsConstructor
public class RedisChatMessageHistoryLoader implements ChatMessageService {

    private static final String USER_CONVERSATION_REGEX_META_PREFIX = "creation:conversation:meta:{}";
    private static final String USER_CONVERSATION_META_PREFIX = "creation:conversation:meta:{}:{}";
    private static final String USER_CONVERSATION_MSG_PREFIX = "creation:conversation:msg:{}:{}";

    private final StringRedisTemplate redisTemplate;

    @Override
    public List<LLMMessage> getUserChatMessagesHistories(Long uid, Long conversationId) {
        // 查询最多50条会话
        List<String> dataList = redisTemplate.opsForList().range(getUserConversationOriginMsgKey(uid, conversationId), 0, 50);
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        return dataList.stream().map(e -> JSON.parseObject(e, LLMMessage.class)).collect(Collectors.toList());
    }

    @Override
    public List<LLMMessage> getUserChatMessagesScenariosHistories(Long uid, Long conversationId, Long scenarioId, Long scenarioCtxNo) {
        // TODO 待实现
        return null;
    }

    @Override
    public void addUserChatMessage(LLMChainContext context, LLMMessage llmMessage) {
        redisTemplate.opsForList().rightPush(getUserConversationOriginMsgKey(context.getUserId(), context.getConversationId()), JSON.toJSONString(llmMessage));
    }


    @Override
    public void clearUserChatMessageHistory(Long uid, Long conversationId) {
        redisTemplate.opsForList().trim(getUserConversationOriginMsgKey(uid, conversationId), 0, -1);
    }

    /*--------------------------------------------会话------------------------------------------------------*/


    @Override
    public Long newUserConversation(Long uid, Long scenario) {
        // 创建会话Id
        Long conversationId = IdHelper.getId();
        // 设置会话消息列表
        // redisTemplate.opsForList().leftPush(getUserConversationMsgKey(uid, conversationId), JSON.toJSONString(Collections.emptyList()));

        // 设置会话元信息
        LLMConversation llmConversation = new LLMConversation().setUid(uid).setConversationId(conversationId).setCreateTime(LocalDateTime.now());
        redisTemplate.opsForValue().set(getUserConversationMetaKey(uid, conversationId), JSON.toJSONString(llmConversation));
        return conversationId;
    }

    @Override
    public Integer findUserConversationNumber(Long uid) {
        // TODO 后期要修改
        Set<String> userConversationKeys = redisTemplate.keys(getUserConversationMetaKey(uid) + "*");
        return CollectionUtils.size(userConversationKeys);
    }

    @Override
    public LLMConversation findUserConversation(Long uid, Long conversationId) {
        return null;
    }

    @Override
    public void removeUserChatConversation(Long uid, Long conversationId) {
        // 删除会话元数据
        redisTemplate.delete(getUserConversationMetaKey(uid, conversationId));
        // 删除会话数据（origin）
        redisTemplate.delete(getUserConversationOriginMsgKey(uid, conversationId));
    }

    @Override
    public List<LLMConversation> findUserConversations(Long uid) {
        // 查看用户的所有会话 TODO 后期要修改
        Set<String> userConversationKeys = redisTemplate.keys(getUserConversationMetaKey(uid) + "*");
        if (CollectionUtils.isEmpty(userConversationKeys)) {
            return Collections.emptyList();
        }

        return userConversationKeys.stream()
                .map(key -> {
                    // 获取会话Id
                    Long conversationId = Long.valueOf(StringUtils.substringAfterLast(key, ":"));
                    // 获取最近1条会话id
                    List<String> msgList = redisTemplate.opsForList().range(getUserConversationOriginMsgKey(uid, conversationId), 0, 3);
                    if (CollectionUtils.isEmpty(msgList)) {
                        return null;
                    }

                    String conversationMeta = redisTemplate.opsForValue().get(getUserConversationMetaKey(uid, conversationId));
                    if (conversationMeta == null) {
                        return null;
                    }
                    LLMConversation conversation = JSON.parseObject(conversationMeta, LLMConversation.class);

                    // 找到最近一条用户信息
                    String latestUserMsg = msgList.stream()
                            .map(e -> JSON.parseObject(e, LLMMessage.class))
                            .filter(e -> LLMMessage.LLMMessageType.USER == e.getMessageType())
                            .findFirst()
                            .map(LLMMessage::getText)
                            .orElse("");

                    return new LLMConversation()
                            .setUid(uid)
                            .setConversationId(conversationId)
                            .setCreateTime(conversation.getCreateTime())
                            .setTitle(conversation.getTitle())
                            .setTitle(latestUserMsg);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public void updateConversation(Long uid, Long conversationId, String title) {

    }

    private String getUserConversationMetaKey(Long uid, Long conversationId) {
        return FormatUtil.format(USER_CONVERSATION_META_PREFIX, uid, conversationId);
    }

    private String getUserConversationOriginMsgKey(Long uid, Long conversationId) {
        return FormatUtil.format(USER_CONVERSATION_MSG_PREFIX, uid, conversationId);
    }

    private String getUserConversationMetaKey(Long uid) {
        return FormatUtil.format(USER_CONVERSATION_REGEX_META_PREFIX, uid);
    }

}
