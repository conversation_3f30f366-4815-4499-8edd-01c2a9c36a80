package ai.creatly.sky.creation.infra.ai.model.kling.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version KlingRequest.java, v0.1 2025-03-04 15:16
 */
@Data
@Accessors(chain = true)
public class KlingRequest {

    @JsonProperty("model_name")
    private String modelName;

    private String prompt;

    //可选
    @JsonProperty("negative_prompt")
    private String negativePrompt;

    private String mode = "pro";

    //可选
    private String image;

    @JsonProperty("image_tail")
    private String imageTail;

    //可选，参考模式
    @JsonProperty("image_reference")
    private String imageReference;

    //可选
    @JsonProperty("image_fidelity")
    private Float imageFidelity;

    //可选
    @JsonProperty("aspect_ratio")
    private String aspectRatio;

    //生成数量
    private Integer n;

    //生成时长
    private Integer duration;
}
