/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.deprecated.aiproduct;

import ai.creatly.sky.creation.domain.deprecated.content.model.Content;
import ai.creatly.sky.creation.domain.deprecated.content.model.Tag;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationContent;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.repository.PojoMapper;
import com.jspeeder.core.util.time.Dates;
import org.jooq.JSONB;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;


@Mapper(config = BaseMapperConfig.class)
public interface StarHubContentMapper extends PojoMapper<Content, CreationContent> {


    @Override
    @Mapping(target = "author", ignore = true)
    @Mapping(target = "tags", ignore = true)
    @Mapping(target = "views", ignore = true)
    @Mapping(target = "sourceCategory", ignore = true)
    @Mapping(target = "category", ignore = true)
    CreationContent toEntity(Content content);


    @Override
    @Mapping(target = "contentType", ignore = true)
    @Mapping(target = "desc", ignore = true)
    @Mapping(target = "tags", ignore = true)
    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "hots", ignore = true)
    Content toModel(CreationContent content);

    default List<Tag> JSONBArrayToList(JSONB[] tags) {
        return Collections.emptyList();
    }

    default JSONB[] listTagToJSONBArray(List<Tag> tags) {
        return new JSONB[]{};
    }

    default String localDateToString(LocalDate localDate) {
        return Dates.FORMATTER_DATE.format(localDate);
    }

    default LocalDate stringToLocalDate(String date) {
        return LocalDate.parse(date, Dates.FORMATTER_DATE);
    }

    default List<Tag> stringToLocalDate(JSONB[] jsonb) {
        return Collections.emptyList();
    }

    default JSONB[] tagsToJSONB(List<Tag> tags) {
        return new JSONB[]{};
    }


    default List<Tag> toTags(JSONB[] jsonb) {
        return Collections.emptyList();
    }
}
