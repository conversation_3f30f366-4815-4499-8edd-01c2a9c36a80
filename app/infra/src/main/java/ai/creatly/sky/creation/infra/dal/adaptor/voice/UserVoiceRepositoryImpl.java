/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.voice;

import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceLocale;
import ai.creatly.sky.creation.domain.core.voice.model.enums.UserVoiceStatus;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceStatus;
import ai.creatly.sky.creation.domain.core.voice.repository.UserVoiceRepository;
import ai.creatly.sky.creation.infra.dal.adaptor.voice.mapper.UserVoiceLocalePojoMapper;
import ai.creatly.sky.creation.infra.dal.adaptor.voice.mapper.UserVoicePojoMapper;
import ai.creatly.sky.creation.infra.dal.tables.daos.UserVoiceDAO;
import ai.creatly.sky.creation.infra.dal.tables.daos.UserVoiceLocaleDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserVoice;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_USER_VOICE;
import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_USER_VOICE_LOCALE;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version UserVoiceRepositoryImpl.java, v 0.1 2024-01-24 下午23:57 heb
 */
@Repository
@RequiredArgsConstructor
public class UserVoiceRepositoryImpl implements UserVoiceRepository {

    private final DSLContext                dsl;
    private final UserVoicePojoMapper       userVoicePojoMapper;
    private final UserVoiceLocalePojoMapper userVoiceLocalePojoMapper;
    private final TransactionTemplate       transactionTemplate;
    private final UserVoiceDAO              userVoiceDAO;
    private final UserVoiceLocaleDAO        userVoiceLocaleDAO;

    @Override
    public void deleteById(long id) {
        transactionTemplate.executeWithoutResult(status -> {
            userVoiceDAO.deleteById(id);
            dsl.deleteFrom(CREATION_USER_VOICE_LOCALE)
                    .where(CREATION_USER_VOICE_LOCALE.VOICE_ID.eq(id))
                    .execute();
        });
    }

    @Override
    public void updateLocalePreviewAudio(long voiceId, String localeCode, long previewAudioId, String previewAudioUrl) {
        Asserts.notBlank(localeCode, "localeCode must not be blank!");
        dsl.update(CREATION_USER_VOICE_LOCALE)
                .set(CREATION_USER_VOICE_LOCALE.PREVIEW_AUDIO_ID, previewAudioId)
                .set(CREATION_USER_VOICE_LOCALE.PREVIEW_AUDIO_URL, previewAudioUrl)
                .where(CREATION_USER_VOICE_LOCALE.VOICE_ID.eq(voiceId))
                .and(CREATION_USER_VOICE_LOCALE.CODE.eq((localeCode)))
                .execute();
    }

    @Override
    public Optional<Voice> queryOptionalById(long id) {
        return userVoiceDAO.fetchOptionalById(id)
                .map(userVoicePojoMapper::toModel)
                .map(voice -> {
                    List<VoiceLocale> voiceLocales = userVoiceLocaleDAO.fetchByVoiceId(id)
                            .stream()
                            .map(userVoiceLocalePojoMapper::toModel)
                            .sorted(Comparator.comparing(VoiceLocale::getOrdinal))
                            .collect(toList());
                    voice.setLocales(voiceLocales);
                    return voice;
                });
    }

    @Override
    public boolean checkExistsByCnName(long uid, String cnName) {
        return dsl.selectFrom(CREATION_USER_VOICE)
                .where(CREATION_USER_VOICE.UID.eq(uid))
                .and(CREATION_USER_VOICE.CN_NAME.eq(cnName))
                .limit(1)
                .fetchOptional()
                .isPresent();
    }


    @Override
    public List<Voice> queryByUidAndStatus(long uid, @Nullable UserVoiceStatus status) {
        List<Voice> voices = dsl.selectFrom(CREATION_USER_VOICE)
                .where(CREATION_USER_VOICE.UID.eq(uid))
                .and(status == null ? DSL.noCondition() : CREATION_USER_VOICE.STATUS.eq(status.name()))
                .orderBy(CREATION_USER_VOICE.CREATED_AT.desc())
                .fetchStreamInto(CreationUserVoice.class)
                .map(userVoicePojoMapper::toModel)
                .collect(toList());
        this.fillLocales(voices);
        return voices;
    }

    /**
     * @param voices 声音定制列表
     */
    private void fillLocales(List<Voice> voices) {
        Long[] voiceIds = voices.stream().map(Voice::getId).toArray(Long[]::new);
        Map<Long, List<VoiceLocale>> localeMap = userVoiceLocaleDAO.fetchByVoiceId(voiceIds)
                .stream()
                .map(userVoiceLocalePojoMapper::toModel)
                .collect(Collectors.groupingBy(VoiceLocale::getVoiceId));
        voices.forEach(voice -> {
            List<VoiceLocale> voiceLocales = localeMap.get(voice.getId());
            if (voiceLocales != null) {
                voiceLocales.sort(Comparator.comparing(VoiceLocale::getOrdinal));
                voice.setLocales(voiceLocales);
            } else {
                voice.setLocales(new ArrayList<>());
            }
        });
    }

    @Override
    public boolean online(long id) {
        return dsl.update(CREATION_USER_VOICE)
                .set(CREATION_USER_VOICE.STATUS, VoiceStatus.ONLINE.name())
                .where(CREATION_USER_VOICE.ID.eq(id))
                // 乐观锁
                .and(CREATION_USER_VOICE.STATUS.eq(VoiceStatus.UNPAID.name()))
                .execute() == 1;
    }
}
