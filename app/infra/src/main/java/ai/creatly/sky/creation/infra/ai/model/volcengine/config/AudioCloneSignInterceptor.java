/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.model.volcengine.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * <AUTHOR>
 * @version AudioCloneSignInterceptor.java, v0.1 2025-02-28 12:15
 */
@RequiredArgsConstructor
public class AudioCloneSignInterceptor implements RequestInterceptor {

    @Autowired
    private AudioCloneApiProperties audioCloneApiProperties;

    public AudioCloneSignInterceptor(AudioCloneApiProperties properties) {
        this.audioCloneApiProperties = properties;
    }

    @Override
    public void apply(RequestTemplate template) {
        template.header("Authorization", "Bearer;"+audioCloneApiProperties.getAccessToken());
        template.header("Resource-Id", "volc.megatts.voiceclone");
    }

}
