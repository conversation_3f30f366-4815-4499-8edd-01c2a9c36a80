package ai.creatly.sky.creation.infra.ai.video;

import ai.creatly.sky.creation.domain.core.ai.video.AiVideoEffectsGenerateClient;
import ai.creatly.sky.creation.domain.core.ai.video.model.AiVideoTaskResult;
import ai.creatly.sky.creation.domain.core.ai.video.model.VideoEffectsTaskInput;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version AIImageGenerateClientFacade.java, v0.1 2025-03-10 18:59
 */
@Component
@Primary
@RequiredArgsConstructor
public class AiVideoEffectsGenerateClientFacade implements AiVideoEffectsGenerateClient {

    @Autowired
    private KlingEffectsGenerateClientImpl klingClient;

    @Override
    public AiVideoTaskResult generate(VideoEffectsTaskInput input, UserContext userContext) {
        return klingClient.generate(input,userContext);
    }

    @Override
    public CreditsExpense expense(AiTask aiTask) {
        return klingClient.expense(aiTask);
    }

    @Override
    public AiVideoTaskResult queryGeneration(String taskId, UserContext userContext) {
        return klingClient.queryGeneration(taskId,userContext);
    }
}
