/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.wechat.mp.model.account;

import ai.creatly.sky.creation.infra.integration.wechat.common.WechatResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version QrCodeCreateResponse.java, v 0.1 2024-10-14 下午6:39 zhoudong
 */
@Data
public class QrCodeCreateResponse extends WechatResponse {
    private String  ticket;
    @JsonProperty("expire_seconds")
    private Integer expireSeconds;
    private String  url;
}
