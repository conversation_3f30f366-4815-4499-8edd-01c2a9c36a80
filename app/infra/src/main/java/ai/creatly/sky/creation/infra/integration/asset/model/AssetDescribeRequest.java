/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.asset.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version AssetDescribeRequest.java, 2024-10-22 下午7:17 zhoudong
 */
@Data
@Accessors(chain = true)
public class AssetDescribeRequest {

    private String bucket;
    private String assetKey;
    /**
     * 输出的打标文件key
     */
    private String outputLabelKey;
}
