/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.aiproductimage.api;

import com.jspeeder.core.util.loadbalancer.RoundRobinCounter;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version AiProductImageApiFactory.java, v 0.1 2024-02-28 下午5:32 zhoudong
 */
public class AiProductImageApiFactory {

    private final List<AiProductImageApiContainer> apiContainers;
    private final Map<String, RoundRobinCounter>   counterMap = new ConcurrentHashMap<>();

    public AiProductImageApiFactory(List<AiProductImageApiContainer> apiContainers) {
        this.apiContainers = apiContainers;
    }

    public AiProductImageApiContainer next(String name) {
        counterMap.computeIfAbsent(name, k -> new RoundRobinCounter(apiContainers.size()));
        final RoundRobinCounter counter = counterMap.get(name);
        return this.apiContainers.get(counter.next());
    }
}
