/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.deprecated;

import ai.creatly.sky.creation.domain.deprecated.config.UserPreferenceRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version : UserPreferenceRepositoryImpl.java, v 1.0 2023年07月24日 22时34分 syoka Exp$
 */
@Repository
@RequiredArgsConstructor
public class UserPreferenceRepositoryImpl implements UserPreferenceRepository {

}
