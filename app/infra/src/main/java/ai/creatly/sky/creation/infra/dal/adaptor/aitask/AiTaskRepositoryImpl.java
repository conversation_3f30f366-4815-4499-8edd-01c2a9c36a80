/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.aitask;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskExecInfo;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskQO;
import ai.creatly.sky.creation.domain.core.aitask.model.UpdatableAiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskExecStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskStatsRepository;
import ai.creatly.sky.creation.infra.dal.Tables;
import ai.creatly.sky.creation.infra.dal.common.PgDSL;
import ai.creatly.sky.creation.infra.dal.tables.daos.AiTaskDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiTask;
import ai.creatly.sky.creation.infra.dal.tables.records.AiTaskRecord;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Repository;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.jooq.impl.DSL.*;

/**
 * <AUTHOR>
 * @version AiTaskRepositoryImpl.java, v 0.1 2023-06-24 01:58 joton
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AiTaskRepositoryImpl extends Tables implements AiTaskRepository, AiTaskStatsRepository {

    private final DSLContext       dsl;
    private final AiTaskDAO        aiTaskDAO;
    private final AiTaskPojoMapper aiTaskPojoMapper;

    @Override
    public long create(AiTask aiTask) {
        if (aiTask.getId() == null) {
            aiTask.setId(IdHelper.getId());
        }
        CreationAiTask entity = aiTaskPojoMapper.toEntity(aiTask);
        aiTaskDAO.insert(entity);
        return entity.getId();
    }

    @Override
    public AiTask createIdempotent(AiTask aiTask) {
        try {
            this.create(aiTask);
            return aiTask;
        } catch (DuplicateKeyException e) {
            aiTask = this.queryByUnique(aiTask.getTaskType(), aiTask.getBizType(), aiTask.getBizNo(), aiTask.getSubBizNo()).orElse(null);
            Asserts.notNull(aiTask, "will not happen");
            return aiTask;
        }
    }

    @Override
    public void deleteById(long id) {
        aiTaskDAO.deleteById(id);
    }

    @Override
    public void updateById(AiTask aiTask) {
        AiTaskRecord record = aiTaskPojoMapper.toUpdatingRecord(aiTask);
        if (record.changed()) {
            return;
        }
        dsl.update(CREATION_AI_TASK)
                .set(record)
                .where(CREATION_AI_TASK.ID.eq(aiTask.getId()))
                .execute();
    }

    @Override
    public Page<AiTask> queryPageByOwnerIdAndTaskType(long ownerId, AiTaskType taskType, Pageable pageable) {
        // count
        long count = dsl.selectCount()
                .from(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.OWNER_ID.eq(ownerId))
                .and(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.notDeleted()))
                .fetchSingleInto(Long.class);

        // list
        List<AiTask> aiTasks = dsl.selectFrom(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.OWNER_ID.eq(ownerId))
                .and(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.notDeleted()))
                .orderBy(CREATION_AI_TASK.UPDATED_AT.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchInto(CreationAiTask.class)
                .stream()
                .map(aiTaskPojoMapper::toModel)
                .collect(toList());

        return Page.of(aiTasks, pageable, count);
    }

    @Override
    public Page<AiTask> queryPageByOwnerIdAndTaskTypeAndBizType(long ownerId, AiTaskType taskType, List<String> bizTypes,
                                                                Pageable pageable) {
        Asserts.notNull(taskType, "taskType must not be null");
        Asserts.notNull(bizTypes, "bizTypes must not be null");
        if (CollectionUtils.isEmpty(bizTypes)) {
            return Page.empty(pageable);
        }
        // count
        long count = dsl.selectCount()
                .from(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.OWNER_ID.eq(ownerId))
                .and(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                .and(CREATION_AI_TASK.BIZ_TYPE.in(bizTypes))
                .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.notDeleted()))
                .fetchSingleInto(Long.class);

        // list
        List<AiTask> aiTasks = dsl.selectFrom(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.OWNER_ID.eq(ownerId))
                .and(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                .and(CREATION_AI_TASK.BIZ_TYPE.in(bizTypes))
                .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.notDeleted()))
                .orderBy(CREATION_AI_TASK.UPDATED_AT.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchInto(CreationAiTask.class)
                .stream()
                .map(aiTaskPojoMapper::toModel)
                .collect(toList());

        return Page.of(aiTasks, pageable, count);
    }

    @Override
    public List<AiTask> query(AiTaskQO qo, Pageable pageable) {
        Condition condition = this.toCondition(qo);
        if (condition == null) {
            return Collections.emptyList();
        }
        return dsl.selectFrom(CREATION_AI_TASK)
                .where(condition)
                .orderBy(CREATION_AI_TASK.CREATED_AT.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchStreamInto(CreationAiTask.class)
                .map(aiTaskPojoMapper::toModel)
                .collect(Collectors.toList());
    }

    @Override
    public int count(AiTaskQO qo) {
        Condition condition = this.toCondition(qo);
        if (condition == null) {
            return 0;
        }
        return dsl.selectCount()
                .from(CREATION_AI_TASK)
                .where(condition)
                .fetchSingleInto(Integer.class);
    }

    @Nullable
    private Condition toCondition(AiTaskQO qo) {
        Condition condition = DSL.trueCondition();
        if (qo.getTaskType() != null) {
            condition = condition.and(CREATION_AI_TASK.TASK_TYPE.eq(qo.getTaskType().name()));
        }else {
            condition = condition.and(CREATION_AI_TASK.TASK_TYPE.eq(AiTaskType.AI_AUDIO.name()).or(CREATION_AI_TASK.TASK_TYPE.eq(AiTaskType.AI_IMAGE.name())).or(CREATION_AI_TASK.TASK_TYPE.eq(AiTaskType.AI_VIDEO.name())));
        }
        if (qo.getOwnerId() != null) {
            condition = condition.and(CREATION_AI_TASK.OWNER_ID.eq(qo.getOwnerId()));
        }
        if (qo.getStatuses() != null) {
            if (qo.getStatuses().isEmpty()) {
                return null;
            }
            condition = condition.and(CREATION_AI_TASK.STATUS.in(qo.getStatuses().stream().map(AiTaskStatus::name).collect(toSet())));
        } else {
            condition = condition.and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.notDeleted()));
        }
        if (qo.getBizTypes() != null) {
            if (qo.getBizTypes().isEmpty()) {
                return null;
            }
            condition = condition.and(CREATION_AI_TASK.BIZ_TYPE.in(qo.getBizTypes()));
        }
        if (qo.getBizNos() != null) {
            if (qo.getBizNos().isEmpty()) {
                return null;
            }
            condition = condition.and(CREATION_AI_TASK.BIZ_NO.in(qo.getBizNos()));
        }
        if (qo.getBizStatuses() != null) {
            if (qo.getBizStatuses().isEmpty()) {
                return null;
            }
            condition = condition.and(CREATION_AI_TASK.BIZ_STATUS.in(qo.getBizStatuses()));
        }
        if (qo.getSubBizNo() != null) {
            condition = condition.and(CREATION_AI_TASK.SUB_BIZ_NO.eq(qo.getSubBizNo()));
        }
        if (StringUtils.isNotBlank(qo.getSubBizNoPrefix())) {
            condition = condition.and(CREATION_AI_TASK.SUB_BIZ_NO.startsWith(qo.getSubBizNoPrefix()));
        }
        return condition;
    }

    @Override
    public List<AiTask> queryWaitingForAutoExec(AiTaskType taskType, String bizType, int limit) {
        Condition condition = CREATION_AI_TASK.EXEC_STATUS.eq(AiTaskExecStatus.WAITING.name())
                .and(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                .and(CREATION_AI_TASK.AUTO_EXEC.eq(true))
                .and(CREATION_AI_TASK.DELAYED_AT.isNull().or(CREATION_AI_TASK.DELAYED_AT.lt(ZonedDateTime.now())));
        if (Objects.nonNull(bizType)) {
            condition = condition.and(CREATION_AI_TASK.BIZ_TYPE.eq(bizType));
        }
        return dsl.selectFrom(CREATION_AI_TASK)
                .where(condition)
                // 数字越小，优先级越高，相同优先级，按照创建时间升序
                .orderBy(CREATION_AI_TASK.PRIORITY.asc(), CREATION_AI_TASK.CREATED_AT.asc())
                .limit(limit)
                .fetchInto(CreationAiTask.class)
                .stream()
                .map(aiTaskPojoMapper::toModel)
                .collect(toList());
    }

    @Override
    public Optional<AiTask> queryOptionalById(long id) {
        return aiTaskDAO.fetchOptionalById(id).map(aiTaskPojoMapper::toModel);
    }

    @Override
    public List<AiTask> queryByIds(Set<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }
        return dsl.selectFrom(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.ID.in(ids))
                .fetchInto(CreationAiTask.class)
                .stream()
                .map(aiTaskPojoMapper::toModel)
                .collect(toList());
    }

    @Override
    public long countInProgressWithUser(long ownerId, AiTaskType taskType, @Nullable String bizType) {
        if (Objects.isNull(bizType)) {
            return dsl.selectCount()
                    .from(CREATION_AI_TASK)
                    .where(CREATION_AI_TASK.OWNER_ID.eq(ownerId))
                    .and(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                    .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.CREATED.name(), AiTaskStatus.RUNNING.name()))
                    .fetchSingleInto(Long.class);
        }
        return dsl.selectCount()
                .from(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.OWNER_ID.eq(ownerId))
                .and(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                .and(CREATION_AI_TASK.BIZ_TYPE.eq(bizType))
                .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.CREATED.name(), AiTaskStatus.RUNNING.name()))
                .fetchSingleInto(Long.class);
    }

    @Override
    public long countInProgress(AiTaskType taskType, @Nullable String bizType) {
        if (Objects.isNull(bizType)) {
            return dsl.selectCount()
                    .from(CREATION_AI_TASK)
                    .where(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                    .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.CREATED.name(), AiTaskStatus.RUNNING.name()))
                    .fetchSingleInto(Long.class);
        }
        return dsl.selectCount()
                .from(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                .and(CREATION_AI_TASK.BIZ_TYPE.eq(bizType))
                .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.CREATED.name(), AiTaskStatus.RUNNING.name()))
                .fetchSingleInto(Long.class);
    }

    @Override
    public Optional<AiTask> queryByUnique(AiTaskType taskType, String bizType, String bizNo, String subBizNo) {
        Asserts.notNull(taskType, "taskType must not be null");
        Asserts.notNull(bizType, "bizType must not be null");
        Asserts.notNull(bizNo, "bizNo must not be null");
        Asserts.notNull(subBizNo, "subBizNo must not be null");
        return dsl.selectFrom(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                .and(CREATION_AI_TASK.BIZ_TYPE.eq(bizType))
                .and(CREATION_AI_TASK.BIZ_NO.eq(bizNo))
                .and(CREATION_AI_TASK.SUB_BIZ_NO.eq(subBizNo))
                .fetchOptionalInto(CreationAiTask.class)
                .map(aiTaskPojoMapper::toModel);
    }

    @Override
    public Optional<AiTask> queryLatestByCondition(AiTaskQO qo) {
        Condition condition = this.toCondition(qo);
        if (condition == null) {
            return Optional.empty();
        }
        return dsl.selectFrom(CREATION_AI_TASK)
                .where(condition)
                .orderBy(CREATION_AI_TASK.CREATED_AT.desc())
                .limit(1)
                .fetchOptionalInto(CreationAiTask.class)
                .map(aiTaskPojoMapper::toModel);
    }

    @Override
    public void startById(long id, @Nullable UpdatableAiTask aiTask, AiTaskExecInfo execInfo) {
        AiTaskRecord record = this.toRecordForUpdate(aiTask, execInfo);
        final ZonedDateTime startedAt;
        if (aiTask != null && aiTask.getStartedAt() != null) {
            startedAt = aiTask.getStartedAt();
        } else {
            startedAt = ZonedDateTime.now();
        }
        dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.STATUS, AiTaskStatus.RUNNING.name())
                .set(CREATION_AI_TASK.EXEC_STATUS, AiTaskExecStatus.WAITING.name())
                .set(CREATION_AI_TASK.STARTED_AT, startedAt)
                .set(record)
                .where(CREATION_AI_TASK.ID.eq(id))
                .and(CREATION_AI_TASK.STATUS.eq(AiTaskStatus.CREATED.name()))
                .and(CREATION_AI_TASK.EXEC_STATUS.in(AiTaskExecStatus.WAITING.name(), AiTaskExecStatus.EXECUTING.name()))
                .execute();
    }

    @Override
    public void rollbackRunningToCreatedById(long id, @Nullable UpdatableAiTask aiTask, AiTaskExecInfo execInfo) {
        AiTaskRecord record = this.toRecordForUpdate(aiTask, execInfo);
        dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.STATUS, AiTaskStatus.CREATED.name())
                .set(CREATION_AI_TASK.EXEC_STATUS, AiTaskExecStatus.WAITING.name())
                .setNull(CREATION_AI_TASK.STARTED_AT)
                // 清空业务过程数据
                .set(CREATION_AI_TASK.BIZ_EXEC_INFO, JSONB.jsonb("{}"))
                // 清空业务结果数据
                .set(CREATION_AI_TASK.BIZ_RESULT, JSONB.jsonb("{}"))
                // 清空系统结果数据
                .set(CREATION_AI_TASK.SYS_RESULT, JSONB.jsonb("{}"))
                .set(record)
                .where(CREATION_AI_TASK.ID.eq(id))
                .and(CREATION_AI_TASK.STATUS.eq(AiTaskStatus.RUNNING.name()))
                .and(CREATION_AI_TASK.EXEC_STATUS.in(AiTaskExecStatus.WAITING.name(), AiTaskExecStatus.EXECUTING.name()))
                .execute();
    }

    @Override
    public void cancelById(long id, String reason, @Nullable UpdatableAiTask aiTask, AiTaskExecInfo execInfo) {
        AiTaskRecord record = this.toRecordForUpdate(aiTask, execInfo);
        dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.STATUS, AiTaskStatus.CANCELED.name())
                .set(CREATION_AI_TASK.EXEC_STATUS, AiTaskExecStatus.WAITING.name())
                .set(CREATION_AI_TASK.SYS_RESULT, PgDSL.jsonbSet(CREATION_AI_TASK.SYS_RESULT, "cancelReason", reason))
                .set(record)
                .where(CREATION_AI_TASK.ID.eq(id))
                .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.CREATED.name(), AiTaskStatus.RUNNING.name()))
                .and(CREATION_AI_TASK.EXEC_STATUS.in(AiTaskExecStatus.WAITING.name(), AiTaskExecStatus.EXECUTING.name()))
                .execute();
    }

    @Override
    public void finishById(long id, ZonedDateTime createdAt, @Nullable UpdatableAiTask aiTask, AiTaskExecInfo execInfo) {
        AiTaskRecord record = this.toRecordForUpdate(aiTask, execInfo);
        if (record.getFinishedAt() == null) {
            record.setFinishedAt(ZonedDateTime.now());
        }
        Duration finishedDuration = Duration.between(createdAt, record.getFinishedAt());
        dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.STATUS, AiTaskStatus.FINISHED.name())
                .set(CREATION_AI_TASK.EXEC_STATUS, AiTaskExecStatus.WAITING.name())
                .set(CREATION_AI_TASK.SYS_RESULT, PgDSL.jsonbSet(CREATION_AI_TASK.SYS_RESULT, "finishedDuration", finishedDuration))
                .set(record)
                .where(CREATION_AI_TASK.ID.eq(id))
                .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.CREATED.name(), AiTaskStatus.RUNNING.name(), AiTaskStatus.FINISHED.name()))
                .and(CREATION_AI_TASK.EXEC_STATUS.in(AiTaskExecStatus.WAITING.name(), AiTaskExecStatus.EXECUTING.name()))
                .execute();
    }

    @Override
    public void dryRunById(long id, @Nullable UpdatableAiTask aiTask, @Nullable AiTaskExecInfo execInfo) {
        AiTaskRecord record = this.toRecordForUpdate(aiTask, execInfo);
        boolean updated = dsl.update(CREATION_AI_TASK)
                .set(record)
                .where(CREATION_AI_TASK.ID.eq(id))
                .and(CREATION_AI_TASK.EXEC_STATUS.in(AiTaskExecStatus.WAITING.name(), AiTaskExecStatus.EXECUTING.name()))
                .execute() == 1;
        if (!updated) {
            log.warn("[dryRunById]no need to update,taskId={}", id);
        }
    }

    @Override
    public void completeById(long id, @Nullable UpdatableAiTask aiTask, AiTaskExecInfo execInfo) {
        AiTaskRecord record = this.toRecordForUpdate(aiTask, execInfo);
        dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.EXEC_STATUS, AiTaskExecStatus.COMPLETED.name())
                .set(record)
                .where(CREATION_AI_TASK.ID.eq(id))
                .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.FINISHED.name(), AiTaskStatus.CANCELED.name()))
                .and(CREATION_AI_TASK.EXEC_STATUS.in(AiTaskExecStatus.WAITING.name(), AiTaskExecStatus.EXECUTING.name()))
                .execute();
    }

    private AiTaskRecord toRecordForUpdate(@Nullable UpdatableAiTask updatableAiTask, @Nullable AiTaskExecInfo execInfo) {
        if (execInfo != null && updatableAiTask != null && updatableAiTask.getProgress() != null) {
            execInfo.setProgress(updatableAiTask.getProgress());
        }
        AiTask aiTask = aiTaskPojoMapper.toModel(updatableAiTask, execInfo, null);
        return aiTaskPojoMapper.toUpdatingRecord(aiTask);
    }

    @Override
    public void forceCancelById(long id) {
        dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.STATUS, AiTaskStatus.CANCELED.name())
                .set(CREATION_AI_TASK.EXEC_STATUS, AiTaskExecStatus.COMPLETED.name())
                .where(CREATION_AI_TASK.ID.eq(id))
                .execute();
    }

    @Override
    public void softDeleteById(long id) {
        dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.STATUS, AiTaskStatus.DELETED.name())
                .set(CREATION_AI_TASK.EXEC_STATUS, AiTaskExecStatus.COMPLETED.name())
                .where(CREATION_AI_TASK.ID.eq(id))
                .execute();
    }

    @Override
    public boolean suspendById(long id) {
        return dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.EXEC_STATUS, AiTaskExecStatus.SUSPENDED.name())
                .where(CREATION_AI_TASK.ID.eq(id))
                .and(CREATION_AI_TASK.EXEC_STATUS.eq(AiTaskExecStatus.WAITING.name()))
                .execute() == 1;
    }

    @Override
    public boolean resumeById(long id) {
        return dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.EXEC_STATUS, AiTaskExecStatus.WAITING.name())
                .where(CREATION_AI_TASK.ID.eq(id))
                .and(CREATION_AI_TASK.EXEC_STATUS.eq(AiTaskExecStatus.SUSPENDED.name()))
                .execute() == 1;
    }

    @Override
    public void updatePriority(long id, int priority) {
        dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.PRIORITY, priority)
                .where(CREATION_AI_TASK.ID.eq(id))
                .execute();
    }

    @Override
    public boolean updateBizNo(long id, String bizNo) {
        return dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.BIZ_NO, bizNo)
                .where(CREATION_AI_TASK.ID.eq(id))
                .execute() == 1;
    }

    @Override
    public void updateBizStatus(long id, String bizStatusFrom, String bizStatusTo) {
        dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.BIZ_STATUS, bizStatusTo)
                .where(CREATION_AI_TASK.ID.eq(id))
                .and(CREATION_AI_TASK.BIZ_STATUS.eq(bizStatusFrom))
                .execute();
    }

    @Override
    public List<AiTask> queryByTaskTypeAndBizNo(AiTaskType taskType, String bizType, String bizNo) {
        return dsl.selectFrom(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                .and(CREATION_AI_TASK.BIZ_TYPE.eq(bizType))
                .and(CREATION_AI_TASK.BIZ_NO.eq(bizNo))
                .fetchInto(CreationAiTask.class)
                .stream()
                .map(aiTaskPojoMapper::toModel)
                .collect(toList());
    }

    @Override
    public boolean updateAutoExec(long id, boolean autoExec) {
        return dsl.update(CREATION_AI_TASK)
                .set(CREATION_AI_TASK.AUTO_EXEC, autoExec)
                .where(CREATION_AI_TASK.ID.eq(id))
                .execute() == 1;
    }

    @Override
    public Page<AiTask> queryPageByTaskType(AiTaskType taskType, Pageable pageable) {
        // count
        long count = dsl.selectCount()
                .from(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.notDeleted()))
                .fetchSingleInto(Long.class);

        // list
        List<AiTask> aiTasks = dsl.selectFrom(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.TASK_TYPE.eq(taskType.name()))
                .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.notDeleted()))
                .orderBy(CREATION_AI_TASK.UPDATED_AT.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchInto(CreationAiTask.class)
                .stream()
                .map(aiTaskPojoMapper::toModel)
                .collect(toList());
        return Page.of(aiTasks, pageable, count);
    }

    @Override
    public long totalTasksCount(ZonedDateTime startedAtInclude, ZonedDateTime endedAtExclude) {
        return dsl.selectCount()
                .from(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.CREATED_AT.ge(startedAtInclude))
                .and(CREATION_AI_TASK.CREATED_AT.lt(endedAtExclude))
                .fetchSingleInto(Long.class);
    }

    @Override
    public long failedTasksCount(ZonedDateTime startedAtInclude, ZonedDateTime endedAtExclude) {
        return dsl.selectCount()
                .from(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.CREATED_AT.ge(startedAtInclude))
                .and(CREATION_AI_TASK.CREATED_AT.lt(endedAtExclude))
                .and(CREATION_AI_TASK.STATUS.eq(AiTaskStatus.CANCELED.name()))
                .fetchSingleInto(Long.class);
    }

    @Override
    public long erroredTasksCount(ZonedDateTime startedAtInclude, ZonedDateTime endedAtExclude) {
        return dsl.selectCount()
                .from(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.CREATED_AT.ge(startedAtInclude))
                .and(CREATION_AI_TASK.CREATED_AT.lt(endedAtExclude))
                .and(field("EXEC_INFO").contains("\"errorCount\":"))
                .fetchSingleInto(Long.class);
    }

    @Override
    public long calcSelfHealedTasksCount(ZonedDateTime startedAtInclude, ZonedDateTime endedAtExclude) {
        return dsl.selectCount()
                .from(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.CREATED_AT.ge(startedAtInclude))
                .and(CREATION_AI_TASK.CREATED_AT.lt(endedAtExclude))
                .and(field("EXEC_INFO").contains("\"errorCount\":"))
                .and(CREATION_AI_TASK.BIZ_STATUS.eq("SUCCESS"))
                .fetchSingleInto(Long.class);
    }

    @Override
    public List<Map<String, String>> calculateTaskDurationStats(ZonedDateTime startedAtInclude, ZonedDateTime endedAtExclude) {
        // 判断是否为 JSON 对象
        var records = dsl.select(
                        field(CREATION_AI_TASK.TASK_TYPE).as("task_type"),
                        avg(field("({EXTRACT(EPOCH FROM (sys_result ->> 'finishedDuration')::interval)} * 1000)::bigint", Long.class)).as("avg_duration"),
                        max(field("({EXTRACT(EPOCH FROM (sys_result ->> 'finishedDuration')::interval)} * 1000)::bigint", Long.class)).as("max_duration"),
                        min(field("({EXTRACT(EPOCH FROM (sys_result ->> 'finishedDuration')::interval)} * 1000)::bigint", Long.class)).as("min_duration")
                )
                .from(CREATION_AI_TASK)
                // 只算成功的
                .where(CREATION_AI_TASK.STATUS.eq(AiTaskStatus.FINISHED.name()))
                .and(CREATION_AI_TASK.STARTED_AT.ge(startedAtInclude))
                .and(CREATION_AI_TASK.STARTED_AT.lt(endedAtExclude))
                .groupBy(CREATION_AI_TASK.TASK_TYPE)
                .fetchMaps();

        List<Map<String, String>> resList = new ArrayList<>();
        records.forEach(record -> {
            Map<String, String> retOne = new HashMap<>();
            retOne.put("taskType", record.get("task_type").toString());
            retOne.put("avgDuration", record.get("avg_duration").toString());
            retOne.put("maxDuration", record.get("max_duration").toString());
            retOne.put("minDuration", record.get("min_duration").toString());
            resList.add(retOne);
        });
        return resList;
    }

    @Override
    public long calcRollbackTasksCount(ZonedDateTime startedAtInclude, ZonedDateTime endedAtExclude) {
        return dsl.selectCount()
                .from(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.CREATED_AT.ge(startedAtInclude))
                .and(CREATION_AI_TASK.CREATED_AT.lt(endedAtExclude))
                .and(field("EXEC_INFO").contains("\"rollbackCount\":"))
                .fetchSingleInto(Long.class);
    }
}
