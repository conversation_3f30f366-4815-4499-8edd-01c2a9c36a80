/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.model.volcengine.model;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version KlingResponse.java, v0.1 2025-02-28 12:08
 */
@Data
public class VolceAudioCloneResponse {

    private Object BaseResp;

    private String speaker_id;

    private Integer status;

    private Long create_time;

    private String version;

    private String demo_audio;

    public boolean isSuccess() {
        return StringUtils.isNotEmpty(demo_audio);
    }
}
