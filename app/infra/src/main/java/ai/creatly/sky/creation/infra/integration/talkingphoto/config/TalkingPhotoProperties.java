/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.talkingphoto.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 * @version TalkingPhotoProperties.java, v 0.1 2023-07-29 12:01 joton
 */
@Data
@ConfigurationProperties(prefix = "application.talking-photo")
public class TalkingPhotoProperties {

    private List<ServerEndpoint> endpoints;

    @Data
    public static class ServerEndpoint {
        private String hostname;
        private String baseUrl;
    }
}
