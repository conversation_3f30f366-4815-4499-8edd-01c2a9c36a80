/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.stablediffusion.api;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @version StableDiffusionApiFactory.java, v 0.1 2024-03-20 23:16 heb
 */
@Component
@RequiredArgsConstructor
public class StableDiffusionApiFactory {

    private final List<CameraMovementApiContainer> cameraMovementContainers;
    private final List<ShotEffectApiContainer>     shotEffectContainers;

    public CameraMovementApiContainer nextCameraMovement() {
        return cameraMovementContainers.getFirst();
    }

    public ShotEffectApiContainer nextShotEffect() {
        return shotEffectContainers.getFirst();
    }
}
