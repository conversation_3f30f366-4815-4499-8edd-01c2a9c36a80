/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.videoretalking;

import ai.creatly.sky.creation.domain.common.integration.videoretalking.VideoReTalkingClient;
import ai.creatly.sky.creation.domain.common.integration.videoretalking.model.VideoReTalkingRequest;
import ai.creatly.sky.creation.infra.integration.videoretalking.api.VideoReTalkingApi;
import ai.creatly.sky.creation.infra.integration.videoretalking.api.VideoReTalkingApiFactory;
import ai.creatly.sky.creation.infra.integration.videoretalking.config.VideoReTalkingProperties;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.data.result.ApiVoidResult;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version VideoReTalkingClientImpl.java, v 0.1 2024-05-26 上午1:00 zhoudong
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VideoReTalkingClientImpl implements VideoReTalkingClient {

    private final VideoReTalkingApiFactory videoReTalkingApiFactory;
    private final VideoReTalkingProperties videoReTalkingProperties;

    @Override
    public String getProgressCallbackUrl(String path) {
        Asserts.isTrue(path.startsWith("/"), "[getProgressCallbackUrl]path is invalid");
        return videoReTalkingProperties.getCallbackBaseUrl() + path;
    }

    @Override
    public ApiVoidResult submitReTalking(VideoReTalkingRequest request) {
        VideoReTalkingApi api = videoReTalkingApiFactory.next("submitReTalking").getApi();
        ApiVoidResult result;
        try {
            result = api.submit(request);
        } catch (Exception e) {
            // 网络异常导致的失败，上游是可重试的
            log.error("[submitReTalking]error,request={}", request, e);
            throw new SysException(CommonErrorCode.REMOTE_CALL_ERROR, e);
        }
        // 非网络异常，明确失败，不可重试
        if (!result.isSuccess()) {
            log.error("[submitReTalking]fail,request={},result={}", request, result);
        }
        return result;
    }
}
