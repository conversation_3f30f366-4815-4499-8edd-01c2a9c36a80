/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.messaging.consumer;

import ai.creatly.sky.creation.domain.support.notification.app.AlertMessage;
import lombok.Builder;

/**
 * <AUTHOR>
 * @version RocketMQAlertMessage.java, v 0.1 2023-08-05 22:53 joton
 */
@Builder
public record RocketMQAlertMessage(String messageId,
                                   String topic,
                                   String tags,
                                   Integer reconsumeTimes,
                                   String body) implements AlertMessage {
}
