/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.multimedia.api;

import ai.creatly.sky.creation.domain.support.multimedia.client.model.AudioSliceRequest;
import feign.Headers;
import feign.RequestLine;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @version AudioSliceApi.java, v 0.1 2024-09-19 下午2:57 zhoudong
 */
public interface AudioSliceApi {

    @RequestLine("POST /api/audio_slice/audio")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    JSONObject slice(AudioSliceRequest request);
}
