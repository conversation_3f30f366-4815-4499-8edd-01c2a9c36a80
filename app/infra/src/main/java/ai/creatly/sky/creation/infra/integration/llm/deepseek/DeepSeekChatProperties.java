/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.llm.deepseek;

import lombok.Data;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 *
 * <AUTHOR>
 * @version DeepSeekChatProperties.java, v0.1 2025-02-25 20:02
 */
@Data
@ConfigurationProperties("spring.ai.deep-seek.chat")
public class DeepSeekChatProperties {

    private static final Double DEFAULT_TEMPERATURE = 0.7;
    private static final String DEFAULT_CHAT_MODEL  = "deepseek-chat";

    private String            apiKey;
    private String            baseUrl;
    /**
     * Enable OpenAI chat model.
     */
    private boolean           enabled         = true;
    private String            completionsPath = "/chat/completions";
    @NestedConfigurationProperty
    private OpenAiChatOptions options         = OpenAiChatOptions.builder()
            .model(DEFAULT_CHAT_MODEL)
            .temperature(DEFAULT_TEMPERATURE)
            .build();
}
