/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.wechat.config;

import ai.creatly.sky.creation.infra.integration.FeignUtil;
import ai.creatly.sky.creation.infra.integration.wechat.mp.WechatMpApi;
import ai.creatly.sky.creation.infra.integration.wechat.oauth2.WechatOAuth2Api;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Feign;
import feign.Retryer;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import lombok.RequiredArgsConstructor;
import okhttp3.OkHttpClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version WechatConfiguration.java, v 0.1 2024-03-31 11:46 syoka
 */
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(WechatProperties.class)
public class WechatConfiguration {

    private final ObjectMapper     objectMapper;
    private final WechatProperties wechatProperties;

    @Bean
    public WechatOAuth2Api wechatOAuth2Api(){
        OkHttpClient okHttpClient = this.okHttpClient();
        return Feign.builder()
                .encoder(new JacksonEncoder(objectMapper))
                .decoder(new JacksonDecoder(objectMapper))
                .client(new feign.okhttp.OkHttpClient(okHttpClient))
                .options(FeignUtil.requestOptions(okHttpClient))
                .retryer(Retryer.NEVER_RETRY)
                .target(WechatOAuth2Api.class, wechatProperties.getSnsEndpoint());
    }

    @Bean
    public WechatMpApi wechatMpApi() {
        OkHttpClient okHttpClient = this.okHttpClient();
        return Feign.builder()
                .encoder(new JacksonEncoder(objectMapper))
                .decoder(new JacksonDecoder(objectMapper))
                .client(new feign.okhttp.OkHttpClient(okHttpClient))
                .options(FeignUtil.requestOptions(okHttpClient))
                .retryer(Retryer.NEVER_RETRY)
                .target(WechatMpApi.class, wechatProperties.getMpEndpoint());
    }

    private OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder()
                // 建立连接超时（库默认10s）
                .connectTimeout(Duration.ofSeconds(5))
                // 请求服务器超时（库默认10s）
                .writeTimeout(Duration.ofSeconds(5))
                // 等待服务器响应超时（库默认10s）
                .readTimeout(Duration.ofSeconds(5))
                // 总体调用耗时（库默认永不超时）：包含DNS解析+请求+响应
                .callTimeout(Duration.ofSeconds(15))
                .build();
    }
}
