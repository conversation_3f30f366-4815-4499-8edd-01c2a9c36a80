/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.userplan;

import ai.creatly.sky.creation.domain.core.plan.model.UserPlan;
import ai.creatly.sky.creation.domain.core.plan.model.UserPlanQO;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import ai.creatly.sky.creation.domain.core.plan.repository.UserPlanRepository;
import ai.creatly.sky.creation.infra.dal.tables.daos.UserPlanDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserPlan;
import ai.creatly.sky.creation.infra.dal.tables.records.UserPlanRecord;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_USER_PLAN;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version UserPlanRepositoryImpl.java, v 0.1 2023-12-21 下午4:19 zhoudong
 */
@Repository
@RequiredArgsConstructor
public class UserPlanRepositoryImpl implements UserPlanRepository {

    private final UserPlanDAO        userPlanDAO;
    private final DSLContext         dsl;
    private final UserPlanPojoMapper userPlanPojoMapper;

    @Override
    public void deleteById(long id) {
        userPlanDAO.deleteById(id);
    }

    @Override
    public List<UserPlan> query(UserPlanQO qo, Pageable pageable) {
        Condition condition = this.toCondition(qo);
        return dsl.selectFrom(CREATION_USER_PLAN)
                .where(condition)
                .orderBy(CREATION_USER_PLAN.UPDATED_AT.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchStreamInto(CreationUserPlan.class)
                .map(userPlanPojoMapper::toModel)
                .collect(toList());
    }

    @Override
    public int count(UserPlanQO qo) {
        Condition condition = this.toCondition(qo);
        return dsl.selectCount()
                .from(CREATION_USER_PLAN)
                .where(condition)
                .fetchSingleInto(Integer.class);
    }

    private Condition toCondition(UserPlanQO qo) {
        Condition condition = DSL.trueCondition();

        if (qo.getUid() != null) {
            condition = condition.and(CREATION_USER_PLAN.UID.eq(qo.getUid()));
        }

        if (qo.getPeriodType() != null) {
            condition = condition.and(CREATION_USER_PLAN.PERIOD_TYPE.eq(qo.getPeriodType().name()));
        }

        return condition;
    }

    @Override
    public long create(UserPlan userPlan) {
        CreationUserPlan entity = userPlanPojoMapper.toEntity(userPlan);
        if (entity.getId() == null) {
            entity.setId(IdHelper.getId());
        }
        userPlanDAO.insert(entity);
        return entity.getId();
    }

    @Override
    public void updateById(UserPlan userPlan) {
        UserPlanRecord userPlanRecord = userPlanPojoMapper.toUpdatingRecord(userPlan);
        if (!userPlanRecord.changed()) {
            return;
        }
        dsl.update(CREATION_USER_PLAN)
                .set(userPlanRecord)
                .where(CREATION_USER_PLAN.ID.eq(userPlan.getId()))
                .execute();
    }

    @Override
    public Optional<UserPlan> queryOptionalById(long id) {
        return userPlanDAO.fetchOptionalById(id).map(userPlanPojoMapper::toModel);
    }

    @Override
    public Optional<UserPlan> queryByPlatformPlanIdAndUid(String platformPlanId, long uid) {
        Asserts.notBlank(platformPlanId, "platformPlanId cannot be blank");
        return dsl.selectFrom(CREATION_USER_PLAN)
                .where(CREATION_USER_PLAN.PLATFORM_PLAN_ID.eq(platformPlanId))
                .and(CREATION_USER_PLAN.UID.eq(uid))
                .fetchOptionalInto(CreationUserPlan.class)
                .map(userPlanPojoMapper::toModel);
    }

    @Override
    public List<UserPlan> queryByTypeAndUid(PlanType type, long uid) {
        Asserts.notNull(type, "plan type cannot be null");
        return dsl.selectFrom(CREATION_USER_PLAN)
                .where(CREATION_USER_PLAN.TYPE.eq(type.name()))
                .and(CREATION_USER_PLAN.UID.eq(uid))
                .fetchStreamInto(CreationUserPlan.class)
                .map(userPlanPojoMapper::toModel)
                .collect(toList());
    }
}
