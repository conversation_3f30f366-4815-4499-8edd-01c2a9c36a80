/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.image.volcengine;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.common.util.TextUtil;
import ai.creatly.sky.creation.domain.core.ai.image.client.sync.AiImageSingleClient;
import ai.creatly.sky.creation.domain.core.ai.image.task.expand.model.ImageExpandTaskInput;
import ai.creatly.sky.creation.domain.core.ai.image.task.generate.model.ImageGenerateTaskInput;
import ai.creatly.sky.creation.domain.core.ai.image.task.repaint.model.ImageRepaintTaskInput;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.core.userfile.service.builder.ImageFileBuilder;
import ai.creatly.sky.creation.domain.support.multimedia.model.ImageMetadata;
import ai.creatly.sky.creation.domain.support.multimedia.resolver.MediaFileResolver;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import ai.creatly.sky.creation.infra.ai.image.AiImageApiProvider;
import ai.creatly.sky.creation.infra.ai.image.AiImageApiSupplier;
import ai.creatly.sky.creation.infra.ai.image.volcengine.api.VolcEngineImageApi;
import ai.creatly.sky.creation.infra.ai.image.volcengine.model.SingleImageGeneration;
import ai.creatly.sky.creation.infra.ai.image.volcengine.model.VolcEngineResponse;
import ai.creatly.sky.creation.infra.ai.image.volcengine.model.generate.ImageGenerateRequest;
import ai.creatly.sky.creation.infra.ai.image.volcengine.model.outpaint.ImageOutpaintingRequest;
import ai.creatly.sky.creation.infra.ai.image.volcengine.model.repaint.ImageRepaintRequest;
import ai.creatly.sky.creation.infra.integration.ApiTemplate;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.json.JSON;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Base64;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version VolcEngineImageClientImpl.java, v0.1 2025-02-28 11:49
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class VolcEngineImageClientImpl implements AiImageSingleClient, AiImageApiSupplier {

    private final VolcEngineImageApi volcEngineImageApi;
    private final ImageFileBuilder   imageFileBuilder;
    private final UserFileService    userFileService;
    private final MediaFileResolver  mediaFileResolver;
    private final UserFileHelper     userFileHelper;
    private final UserFileRepository userFileRepository;
    private final AppAlertHelper     appAlertHelper;

    @Override
    public AiImageApiProvider getProvider() {
        return AiImageApiProvider.volc_engine_sync;
    }

    private UserFile uploadImage(String base64Data, String filename, @Nullable String prompt, FileBizSource bizSource,
                                 UserContext userContext) {
        byte[] imageBytes = Base64.getDecoder().decode(base64Data);
        ImageMetadata imageMetadata = mediaFileResolver.resolveImageMetadata(imageBytes, bizSource).setPrompt(prompt);
        if (!filename.endsWith(imageMetadata.getExtension())) {
            filename = filename + StringPool.DOT + imageMetadata.getExtension();
        }
        UserFile userFile = imageFileBuilder.build(bizSource, imageMetadata, userContext, filename);
        userFileService.upload(imageBytes, userFile);
        return userFile;
    }

    private <R> R processFailedResult(VolcEngineResponse<?> response) {
        if (Objects.equals(response.getCode(), 50500)) {
            // fail-fast
            log.error("[generateImage]提示词生图失败，response:{}", response);
            appAlertHelper.alertText("[generateImage]提示词生图失败，response:{}", response);
            return null;
        }
        throw new SysException(CommonErrorCode.REMOTE_API_RESULT_UNEXPECTED, "[generateImage]提示词生图");
    }

    @Override
    public UserFile generateImage(ImageGenerateTaskInput input, UserContext userContext) {
        // 1.构造请求
        final ImageGenerateRequest request;
        if (CollectionUtils.isEmpty(input.getReferImageFiles())) {
            request = new ImageGenerateRequest()
                    .setReqKey("high_aes_general_v21_L")
                    .setPrompt(input.getPromptText())
                    .setWidth(input.getAspectRatio().getWidth())
                    .setHeight(input.getAspectRatio().getHeight());
        } else {
            String imageUrl = userFileHelper.getHttpUrl(input.getReferImageFiles().getFirst().getUrl(), FileAcl.PRIVATE);
            request = new ImageGenerateRequest()
                    .setReqKey("high_aes_ip_v20")
                    .setImageUrls(List.of(imageUrl))
                    .setPrompt(input.getPromptText())
                    .setWidth(input.getAspectRatio().getWidth())
                    .setHeight(input.getAspectRatio().getHeight());
        }

        // 2.调用接口
        VolcEngineResponse<SingleImageGeneration> response = ApiTemplate
                .logger(log, "[generateImage]提示词生图")
                .request("request:{}", () -> JSON.toJSONString(request))
                .invoke(() -> volcEngineImageApi.generateImage(request))
                .response("response:{}", JSON::toJSONString)
                .success(VolcEngineResponse::isSuccess)
                .fetch();

        // 3.处理错误码
        if (!response.isSuccess()) {
            return this.processFailedResult(response);
        }

        // 4.处理结果
        SingleImageGeneration imageGeneration = response.getData();
        String base64Data = imageGeneration.getBinaryDataBase64()[0];
        String prompt = imageGeneration.getLlmResult();
        String filename = TextUtil.getTopWords(input.getPromptText(), 10);
        return this.uploadImage(base64Data, filename, prompt, FileBizSource.AI_IMAGE, userContext);
    }

    @Override
    public UserFile expandImage(ImageExpandTaskInput input, UserContext userContext) {
        // 1.构造请求
        String imageUrl = userFileHelper.getHttpUrl(input.getImageFile().getUrl(), FileAcl.PRIVATE);
        var request = new ImageOutpaintingRequest()
                .setCustomPrompt(StringUtils.defaultString(input.getPromptText()))
                .setImageUrls(List.of(imageUrl));
        final Double ratio = 0.2d;
        switch (input.getDirection()) {
            case up -> request.setTop(ratio).setBottom(0d).setLeft(0d).setRight(0d);
            case down -> request.setTop(0d).setBottom(ratio).setLeft(0d).setRight(0d);
            case left -> request.setTop(0d).setBottom(0d).setLeft(ratio).setRight(0d);
            case right -> request.setTop(0d).setBottom(0d).setLeft(0d).setRight(ratio);
            case whole -> request.setTop(ratio).setBottom(ratio).setLeft(ratio).setRight(ratio);
        }

        // 2.调用接口
        VolcEngineResponse<SingleImageGeneration> response = ApiTemplate
                .logger(log, "[expandImage]智能扩图")
                .request("request:{}", () -> JSON.toJSONString(request))
                .invoke(() -> volcEngineImageApi.outpainting(request))
                .response("response:{}", JSON::toJSONString)
                .success(VolcEngineResponse::isSuccess)
                .fetch();

        // 3.处理错误码
        if (!response.isSuccess()) {
            return this.processFailedResult(response);
        }

        // 4.处理结果
        SingleImageGeneration imageGeneration = response.getData();
        String imageBase64Data = imageGeneration.getBinaryDataBase64()[0];
        String filename = FilenameUtils.getBaseName(userFileRepository.queryById(Long.parseLong(input.getImageFile().getId())).getOriginalFilename());
        return this.uploadImage(imageBase64Data, filename, null, FileBizSource.AI_IMAGE_EXPANSION, userContext);
    }

    @Override
    public UserFile repaintImage(ImageRepaintTaskInput input, UserContext userContext) {
        // 1.构造请求
        List<String> imageUrls = List.of(
                userFileHelper.getHttpUrl(input.getImageFile().getUrl(), FileAcl.PRIVATE),
                userFileHelper.getHttpUrl(input.getMaskImageFile().getUrl(), FileAcl.PRIVATE)
        );
        final ImageRepaintRequest request = new ImageRepaintRequest()
                .setCustomPrompt(input.getPromptText())
                .setImageUrls(imageUrls);

        // 2.调用接口
        VolcEngineResponse<SingleImageGeneration> response = ApiTemplate
                .logger(log, "[repaintImage]局部重绘")
                .request("request:{}", () -> JSON.toJSONString(request))
                .invoke(() -> volcEngineImageApi.repaintImage(request))
                .response("response:{}", JSON::toJSONString)
                .success(VolcEngineResponse::isSuccess)
                .fetch();

        // 3.处理错误码
        if (!response.isSuccess()) {
            return this.processFailedResult(response);
        }

        // 4.处理结果
        SingleImageGeneration imageGeneration = response.getData();
        String imageBase64Data = imageGeneration.getBinaryDataBase64()[0];
        String filename = TextUtil.getTopWords(input.getPromptText(), 10);
        return this.uploadImage(imageBase64Data, filename, null, FileBizSource.AI_IMAGE_REPAINT, userContext);
    }
}
