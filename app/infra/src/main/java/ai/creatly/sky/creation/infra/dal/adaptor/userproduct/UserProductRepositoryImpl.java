/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.userproduct;

import ai.creatly.sky.creation.domain.core.product.model.UserProduct;
import ai.creatly.sky.creation.domain.core.product.service.UserProductRepository;
import ai.creatly.sky.creation.infra.dal.tables.daos.UserProductDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserProduct;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.model.BizStatus;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_USER_PRODUCT;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version UserProductRepositoryImpl.java, v 0.1 2024-10-16 下午3:56 zhoudong
 */
@Repository
@RequiredArgsConstructor
public class UserProductRepositoryImpl implements UserProductRepository {

    private final DSLContext            dsl;
    private final UserProductDAO        userProductDAO;
    private final UserProductPojoMapper userProductPojoMapper;

    @Override
    public long create(UserProduct product) {
        CreationUserProduct creationUserProduct = userProductPojoMapper.toEntity(product);
        if (creationUserProduct.getId() == null) {
            creationUserProduct.setId(IdHelper.getId());
        }
        userProductDAO.insert(creationUserProduct);
        return creationUserProduct.getId();
    }

    @Override
    public void updateById(UserProduct product) {
        Asserts.notNull(product.getId(), "product.id must not be null");
        dsl.update(CREATION_USER_PRODUCT)
                .set(userProductPojoMapper.toUpdatingRecord(product))
                .where(CREATION_USER_PRODUCT.ID.eq(product.getId()))
                .execute();
    }

    @Override
    public Optional<UserProduct> queryOptionalById(long id) {
        return userProductDAO.fetchOptionalById(id).map(userProductPojoMapper::toModel);
    }

    @Override
    public Page<UserProduct> queryPageByUidAndStatus(long uid, BizStatus status, Pageable pageable) {
        Condition condition = CREATION_USER_PRODUCT.UID.eq(uid)
                .and(CREATION_USER_PRODUCT.STATUS.eq(status.name()));

        // count
        long count = dsl.selectCount()
                .from(CREATION_USER_PRODUCT)
                .where(condition)
                .fetchSingleInto(long.class);

        // list
        List<UserProduct> userProducts = dsl.selectFrom(CREATION_USER_PRODUCT)
                .where(condition)
                .orderBy(CREATION_USER_PRODUCT.UPDATED_AT.desc())
                .limit(pageable.getPageSize())
                .offset(pageable.getOffset())
                .fetchStreamInto(CreationUserProduct.class)
                .map(userProductPojoMapper::toModel)
                .collect(toList());

        return Page.of(userProducts, pageable, count);
    }

    @Override
    public void deleteById(long id) {
        userProductDAO.deleteById(id);
    }
}
