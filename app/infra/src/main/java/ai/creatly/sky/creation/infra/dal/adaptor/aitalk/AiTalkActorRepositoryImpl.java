/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.aitalk;

import ai.creatly.sky.creation.domain.core.aittalk.model.AiTalkActor;
import ai.creatly.sky.creation.domain.core.aittalk.repository.AiTalkActorRepository;
import ai.creatly.sky.creation.infra.dal.Tables;
import ai.creatly.sky.creation.infra.dal.tables.daos.AiTalkActorDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiTalkActor;
import com.jspeeder.core.data.id.IdHelper;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static ai.creatly.sky.creation.domain.common.util.AppConstants.SYSTEM_UID;

/**
 * <AUTHOR>
 * @version AiTalkActorRepositoryImpl.java, v 0.1 2023-06-23 12:40 joton
 */
@Repository
@RequiredArgsConstructor
public class AiTalkActorRepositoryImpl extends Tables implements AiTalkActorRepository {

    private final DSLContext            dsl;
    private final AiTalkActorPojoMapper aiTalkActorPojoMapper;
    private final AiTalkActorDAO        aiTalkActorDAO;

    @Override
    public long create(AiTalkActor actor) {
        CreationAiTalkActor entity = aiTalkActorPojoMapper.toEntity(actor);
        if (entity.getId() == null) {
            entity.setId(IdHelper.getId());
        }
        aiTalkActorDAO.insert(entity);
        return entity.getId();
    }

    @Override
    public Optional<AiTalkActor> queryOptionalById(long id) {
        return aiTalkActorDAO.fetchOptionalById(id).map(aiTalkActorPojoMapper::toModel);
    }

    @Override
    public void deleteById(long id) {
        dsl.deleteFrom(CREATION_AI_TALK_ACTOR)
                .where(CREATION_AI_TALK_ACTOR.ID.eq(id))
                .execute();
    }

    @Override
    public List<AiTalkActor> findUserAiTalkActor(Long uid) {

        List<CreationAiTalkActor> creationAiTalkActors = dsl.selectFrom(CREATION_AI_TALK_ACTOR)
                // 包含系统和用户
                .where(CREATION_AI_TALK_ACTOR.UID.in(uid, SYSTEM_UID))
                .fetchInto(CreationAiTalkActor.class);

        return creationAiTalkActors.stream().map(aiTalkActorPojoMapper::toModel).collect(Collectors.toList());
    }

}
