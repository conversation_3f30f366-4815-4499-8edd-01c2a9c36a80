/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.deprecated.alphavideo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @version VideoAlphaRequest.java, v 0.1 2024-06-17 10:33 syoka
 */
@Data
@Accessors(chain = true)
public class VideoAlphaRequest {

    private String bucket;
    @JsonProperty("green_mask_obj_key")
    private String greenMaskObjKey;
    @JsonProperty("green_video_obj_key")
    private String greenVideoObjKey;
    @JsonProperty("alpha_video_obj_key")
    private String alphaVideoObjKey;

    // TODO 以下为临时参数
    /**
     * 背景图（应该放到render端去下载）
     */
    @Nullable
    @JsonProperty("background_obj_key")
    private String backgroundObjKey;
    /**
     * 音频文件（应该放到render端去下载）
     */
    @Nullable
    @JsonProperty("audio_obj_key")
    private String audioObjKey;
}
