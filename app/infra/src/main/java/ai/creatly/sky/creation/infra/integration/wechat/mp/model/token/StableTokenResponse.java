/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.wechat.mp.model.token;

import ai.creatly.sky.creation.infra.integration.wechat.common.WechatResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version StableTokenResponse.java, v 0.1 2024-10-14 下午6:25 zhoudong
 */
@Data
public class StableTokenResponse extends WechatResponse {

    @JsonProperty("access_token")
    private String  accessToken;
    @JsonProperty("expires_in")
    private Integer expiresIn;
}
