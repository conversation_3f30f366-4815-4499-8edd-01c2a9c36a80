/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.story.mapper;

import ai.creatly.sky.creation.domain.core.story.model.scene.ShotDialogue;
import ai.creatly.sky.creation.domain.core.story.model.shot.ShotAsset;
import ai.creatly.sky.creation.domain.core.story.model.shot.ShotSound;
import ai.creatly.sky.creation.domain.core.story.model.shot.ShotStatus;
import ai.creatly.sky.creation.domain.core.story.model.shot.StoryShot;
import ai.creatly.sky.creation.infra.dal.common.ModelMapper;
import ai.creatly.sky.creation.infra.dal.common.UpdatableBuilder;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationStorySceneShot;
import ai.creatly.sky.creation.infra.dal.tables.records.StorySceneShotRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.mapper.EnumValueMapping;
import com.jspeeder.core.model.OperatorRef;
import com.jspeeder.core.util.json.JSON;
import org.jooq.JSONB;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_STORY_SCENE_SHOT;

/**
 * <AUTHOR>
 * @version StoryBoardMapper.java, v 0.1 2024-03-09 23:56 heb
 */
@Mapper(config = BaseMapperConfig.class)
public interface StoryShotPojoMapper extends ModelMapper<StoryShot, CreationStorySceneShot, StorySceneShotRecord> {

    @Mapping(target = "movement", ignore = true)
    @Override
    CreationStorySceneShot toEntity(StoryShot storyShot);

    @Mapping(target = "dialogue", ignore = true)
    @Override
    StoryShot toModel(CreationStorySceneShot creationStorySceneShot);

    default List<ShotDialogue> jsonbToDialogueList(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseList(jsonb.data(), ShotDialogue.class);
    }

    default JSONB dialogueListToJsonb(List<ShotDialogue> dialogues) {
        if (dialogues == null) {
            return null;
        }
        return JSONB.jsonbOrNull(JSON.toJSONString(dialogues));
    }

    default List<ShotSound> jsonbToSounds(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseList(jsonb.data(), ShotSound.class);
    }

    default JSONB soundsToJsonb(List<ShotSound> sounds) {
        if (sounds == null) {
            return null;
        }
        return JSONB.jsonbOrNull(JSON.toJSONString(sounds));
    }

    default OperatorRef jsonbToOperatorRef(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseObject(jsonb.data(), OperatorRef.class);
    }

    default JSONB operatorRefToJsonb(OperatorRef operatorRef) {
        if (operatorRef == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(operatorRef));
    }

    @EnumValueMapping
    ShotStatus stringToStoryStatus(String status);

    default ShotAsset jsonbToShotAsset(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseObject(jsonb.data(), ShotAsset.class);
    }

    default JSONB shotAssetToJsonb(ShotAsset asset) {
        if (asset == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(asset));
    }

    @Override
    default void updatable(UpdatableBuilder<StorySceneShotRecord> builder) {
        builder.updatable(CREATION_STORY_SCENE_SHOT.STORY_ID)
                .updatable(CREATION_STORY_SCENE_SHOT.SCENE_ID)
                .updatable(CREATION_STORY_SCENE_SHOT.SHOT_NAME)
                .updatable(CREATION_STORY_SCENE_SHOT.MOVEMENT)
                .updatable(CREATION_STORY_SCENE_SHOT.DURATION)
                .updatable(CREATION_STORY_SCENE_SHOT.INDEX)
                .updatable(CREATION_STORY_SCENE_SHOT.DESCRIPTION)
                .updatable(CREATION_STORY_SCENE_SHOT.DIALOGUES)
                .updatable(CREATION_STORY_SCENE_SHOT.SOUNDS)
                .updatable(CREATION_STORY_SCENE_SHOT.CREATOR)
                .updatable(CREATION_STORY_SCENE_SHOT.STATUS)
                .updatable(CREATION_STORY_SCENE_SHOT.ASSET);
    }
}
