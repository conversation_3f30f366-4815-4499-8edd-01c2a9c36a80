/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.oss.config;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.comm.Protocol;
import com.aliyun.oss.common.comm.SignVersion;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OSS Auto {@link Configuration}.
 *
 * <AUTHOR> href="mailto:<EMAIL>">Jim</a>
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties({AliOssProperties.class, AliCloudProperties.class})
public class OssContextConfiguration {

    @Bean
    public OSSClient ossClient(AliCloudProperties aliCloudProperties, AliOssProperties ossProperties) {
        return (OSSClient) new OSSClientBuilder().build(
                ossProperties.getEndpoint(),
                aliCloudProperties.getAccessKey(),
                aliCloudProperties.getSecretKey(),
                this.ossClientConfig(aliCloudProperties)
        );
    }

    private ClientBuilderConfiguration ossClientConfig(AliCloudProperties aliCloudProperties) {
        ClientBuilderConfiguration config = new ClientBuilderConfiguration();
        config.setMaxErrorRetry(aliCloudProperties.getMaxErrorRetry());
        config.setRequestTimeoutEnabled(aliCloudProperties.isRequestTimeoutEnabled());
        config.setConnectionTimeout(aliCloudProperties.getConnectionTimeout());
        config.setSocketTimeout(aliCloudProperties.getSocketTimeout());
        config.setRequestTimeout(aliCloudProperties.getRequestTimeout());
        config.setProtocol(Protocol.HTTPS);
        config.setSignatureVersion(SignVersion.V1);
        // 慢请求告警阈值
        config.setSlowRequestsThreshold(aliCloudProperties.getSlowRequestsThreshold());
        return config;
    }
}
