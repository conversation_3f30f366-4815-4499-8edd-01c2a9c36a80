package ai.creatly.sky.creation.infra.ai.audio;

import ai.creatly.sky.creation.domain.core.ai.audio.AiAudioTtsClient;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiAudioTaskResult;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioTtsTaskInput;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version AIImageGenerateClientFacade.java, v0.1 2025-03-10 18:59
 */
@Component
@Primary
@RequiredArgsConstructor
public class AiAudioTtsClientFacade implements AiAudioTtsClient {

    @Autowired
    private CosyVoiceAudioTtsClientImpl cosyVoiceAudioCloneClient;

    @Override
    public AiAudioTaskResult generate(AudioTtsTaskInput input, UserContext userContext) {
        return cosyVoiceAudioCloneClient.generate(input,userContext);
    }

    @Override
    public CreditsExpense expense(AiTask aiTask) {
        return cosyVoiceAudioCloneClient.expense(aiTask);
    }

    @Override
    public AiAudioTaskResult queryGeneration(String taskId,UserContext userContext) {
        return cosyVoiceAudioCloneClient.queryGeneration(taskId,userContext);
    }

    @Override
    public AiAudioTaskResult queryGeneration(AiTask aiTask,UserContext userContext) {
        return cosyVoiceAudioCloneClient.queryGeneration(aiTask,userContext);
    }
}
