package ai.creatly.sky.creation.infra.ai.model.kling.config;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @version KlingApiProperties.java, v0.1 2025-03-04 14:02
 */
@Data
@Validated
@ConfigurationProperties(prefix = "application.kling.api")
public class KlingApiProperties {
    @NotBlank
    private String accessKey;
    @NotBlank
    private String secretKey;
    @NotBlank
    private String url;
}
