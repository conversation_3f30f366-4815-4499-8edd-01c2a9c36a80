/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.model.request;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version AvatarAddRequest.java, v 0.1 2024-05-16 16:48 syoka
 */
@Data
@Accessors(chain = true)
public class AvatarAddRequest {

    /**
     * 数字人视频地址
     */
    private String avatarUrl;
    /**
     * 数字人名称
     */
    private String avatarName;
    /**
     * 数字人性别
     */
    private String gender;
}
