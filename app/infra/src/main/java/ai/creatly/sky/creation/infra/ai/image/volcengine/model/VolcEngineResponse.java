/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.image.volcengine.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.volcengine.model.ResponseMetadata;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version VolcEngineResponse.java, v0.1 2025-02-28 12:08
 */
@Data
public class VolcEngineResponse<T> {

    private Integer          code;
    private ResponseMetadata metadata;
    private String           message;
    @JsonProperty("request_id")
    private String           requestId;
    private T                data;

    public boolean isSuccess() {
        return code != null && code == 10000;
    }
}
