/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.capcut.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 * @version AiProductImageProperties.java, v 0.1 2024-02-28 下午5:35 zhoudong
 */
@Data
@ConfigurationProperties(prefix = "application.story-jy-draft")
public class StoryProperties {

    private List<Endpoint> endpoints;

    @Data
    public static class Endpoint {
        private String hostname;
        private String baseUrl;
    }
}
