/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.stablediffusion.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 * @version StableDiffuserProperties.java, v 0.1 2024-04-20 23:14 heb
 */
@Data
@ConfigurationProperties(prefix = "application.stable-diffuser")
public class StableDiffuserProperties {

    private List<StableDiffuserSrvProperty> shotEffect;
    private List<StableDiffuserSrvProperty> cameraMovement;

    @Data
    public static class StableDiffuserSrvProperty {

        private String  hostname;
        private String  endpoint;
    }
}

