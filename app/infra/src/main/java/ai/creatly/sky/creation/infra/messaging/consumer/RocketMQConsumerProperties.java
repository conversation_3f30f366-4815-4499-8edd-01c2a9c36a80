/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.messaging.consumer;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <AUTHOR>
 * @version RocketMQConsumerProperties.java, v 0.1 2023-08-06 02:07 joton
 */
@Data
@Validated
@ConfigurationProperties(prefix = "jspeeder.rocketmq.consumer")
public class RocketMQConsumerProperties {

    @NotNull
    private Boolean enabled;
    private List<String> enabledListeners;
}
