/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.videoshot.api;

import feign.Headers;
import feign.RequestLine;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @version VideoScriptApi.java, v 0.1 2024-09-19 下午7:46 zhoudong
 */
public interface VideoScriptApi {

    @RequestLine("POST /api/video_object/script_generation")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    JSONObject generateShots(JSONObject request);
}
