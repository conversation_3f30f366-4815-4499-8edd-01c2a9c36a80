/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.drama.preprocess.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 剧情预处理请求
 *
 * <AUTHOR>
 * @version DramaPreprocessRequest.java, 2024-10-21 下午7:34 zhoudong
 */
@Data
@Accessors(chain = true)
public class DramaPreprocessRequest {

    /**
     * 文件存储桶（暂时没有用到）
     */
    private String bucket;
    /**
     * 剧情ID
     */
    private Long   dramaId;
    /**
     * 视频文件key列表
     */
    @JsonProperty("last_sentence")
    private String endText;
}
