/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.file;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import ai.creatly.sky.creation.infra.dal.Tables;
import ai.creatly.sky.creation.infra.dal.tables.daos.UserFileDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserFile;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.util.*;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version UserFileRepositoryImpl.java, v 0.1 2023-06-24 02:00 joton
 */
@Repository
@RequiredArgsConstructor
public class UserFileRepositoryImpl extends Tables implements UserFileRepository {

    private static final int MAX_SEARCH_NUM = 1000;

    private final DSLContext         dsl;
    private final UserFileDAO        userFileDAO;
    private final UserFilePojoMapper userFilePojoMapper;

    @Override
    public long create(UserFile userFile) {
        if (userFile.getId() == null) {
            userFile.setId(IdHelper.getId());
        }
        CreationUserFile entity = userFilePojoMapper.toEntity(userFile);
        userFileDAO.insert(entity);
        return Objects.requireNonNull(entity.getId());
    }

    @Override
    public void batchCreate(List<UserFile> userFiles) {
        List<CreationUserFile> creationUserFiles = userFilePojoMapper.toEntities(userFiles);
        userFileDAO.insert(creationUserFiles);
    }

    @Override
    public Optional<UserFile> queryOptionalById(long id) {
        return userFileDAO.fetchOptionalById(id).map(userFilePojoMapper::toModel);
    }

    @Override
    public Page<UserFile> queryPageByBizSourceAndTypeAndUid(FileBizSource bizSource, FileType type, long uid, Pageable pageable) {
        // count
        long count = dsl.selectCount()
                .from(CREATION_USER_FILE)
                .where(CREATION_USER_FILE.UID.eq(uid))
                .and(CREATION_USER_FILE.TYPE.eq(type.name()))
                .and(CREATION_USER_FILE.BIZ_SOURCE.eq(bizSource.getCode()))
                .fetchSingleInto(Long.class);

        // list
        List<UserFile> userFiles = dsl.selectFrom(CREATION_USER_FILE)
                .where(CREATION_USER_FILE.UID.eq(uid))
                .and(CREATION_USER_FILE.TYPE.eq(type.name()))
                .and(CREATION_USER_FILE.BIZ_SOURCE.eq(bizSource.getCode()))
                .orderBy(CREATION_USER_FILE.CREATED_AT.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchInto(CreationUserFile.class)
                .stream()
                .map(userFilePojoMapper::toModel)
                .collect(toList());

        return Page.of(userFiles, pageable, count);
    }

    @Override
    public Optional<UserFile> queryOptionalByKeyAndUidAndBizSource(String key, long uid, FileBizSource bizSource) {
        return dsl.selectFrom(CREATION_USER_FILE)
                .where(CREATION_USER_FILE.KEY.eq(key))
                .and(CREATION_USER_FILE.UID.eq(uid))
                .and(CREATION_USER_FILE.BIZ_SOURCE.eq(bizSource.getCode()))
                // 非唯一键查询，所以要 limit 1
                .limit(1)
                .fetchOptionalInto(CreationUserFile.class)
                .map(userFilePojoMapper::toModel);
    }

    @Override
    public void deleteById(long id) {
        userFileDAO.deleteById(id);
    }

    @Override
    public void trashById(long id) {
        dsl.update(CREATION_USER_FILE)
                .set(CREATION_USER_FILE.TRASHED, true)
                .where(CREATION_USER_FILE.ID.eq(id))
                .execute();
    }

    @Override
    public void makePublic(long id) {
        dsl.update(CREATION_USER_FILE)
                .set(CREATION_USER_FILE.ACL, FileAcl.PUBLIC.name())
                .where(CREATION_USER_FILE.ID.eq(id))
                .execute();
    }

    @Override
    public List<UserFile> queryByIds(Set<Long> fileIds) {
        Asserts.notNull(fileIds, "fileIds must not be null");
        Asserts.isTrue(fileIds.size() <= MAX_SEARCH_NUM, "exceed max select limit");
        if (fileIds.isEmpty()) {
            return Collections.emptyList();
        }
        return dsl.selectFrom(Tables.CREATION_USER_FILE)
                .where(Tables.CREATION_USER_FILE.ID.in(fileIds))
                .fetchStreamInto(CreationUserFile.class)
                .map(userFilePojoMapper::toModel)
                .collect(toList());
    }

    @Override
    public Optional<UserFile> queryByUidAndMd5(long uid, FileBizSource bizSource, String md5, boolean trashed) {
        return dsl.selectFrom(CREATION_USER_FILE)
                .where(CREATION_USER_FILE.UID.eq(uid))
                .and(CREATION_USER_FILE.BIZ_SOURCE.eq(bizSource.getCode()))
                .and(CREATION_USER_FILE.MD5.eq(md5))
                .and(CREATION_USER_FILE.TRASHED.eq(trashed))
                .limit(1)
                .fetchOptionalInto(CreationUserFile.class)
                .map(userFilePojoMapper::toModel);
    }

    @Override
    public boolean exists(long uid, FileBizSource bizSource, String md5, boolean trashed) {
        return dsl.selectCount()
                .from(CREATION_USER_FILE)
                .where(CREATION_USER_FILE.UID.eq(uid))
                .and(CREATION_USER_FILE.BIZ_SOURCE.eq(bizSource.getCode()))
                .and(CREATION_USER_FILE.MD5.eq(md5))
                .and(CREATION_USER_FILE.TRASHED.eq(trashed))
                .limit(1)
                .fetchSingleInto(long.class) > 0;
    }
}
