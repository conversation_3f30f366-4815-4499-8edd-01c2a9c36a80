/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.chat;

import ai.creatly.sky.creation.domain.common.reactive.ErrorMapper;
import ai.creatly.sky.creation.domain.common.util.TextUtil;
import ai.creatly.sky.creation.domain.core.ai.chat.error.ChatErrorCode;
import ai.creatly.sky.creation.domain.core.ai.chat.model.*;
import ai.creatly.sky.creation.domain.core.ai.chat.service.AiChatService;
import ai.creatly.sky.creation.domain.core.ai.chat.service.ConversationMessageRepository;
import ai.creatly.sky.creation.domain.core.ai.chat.service.ConversationRepository;
import ai.creatly.sky.creation.domain.core.ai.prompt.service.UserPromptTemplateRepository;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.memory.InMemoryChatMemoryRepository;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import reactor.core.publisher.Flux;
import reactor.core.publisher.SignalType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 *
 * <AUTHOR>
 * @version AiChatServiceImpl.java, v0.1 2025-02-20 15:11
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AiChatServiceImpl implements AiChatService {

    private final ConversationRepository        conversationRepository;
    private final ChatClientFactory             chatClientFactory;
    private final ConversationMessageRepository conversationMessageRepository;
    private final TransactionTemplate           transactionTemplate;
    private final UserPromptTemplateRepository  userPromptTemplateRepository;
    private final AppAlertHelper                appAlertHelper;

    private ConversationMessage buildUserMessage(long uid, long conversationId, String userInput) {
        return new ConversationMessage()
                .setUid(uid)
                .setRole(MessageRole.user)
                .setConversationId(conversationId)
                .setContent(new MessageContent().setText(userInput));
    }

    @Override
    public Flux<MessageChunk> generate(Conversation conversation, String userInput) {
        final boolean firstRound;
        final long conversationId;
        final long uid = conversation.getUid();
        final ChatMemoryRepository chatMemoryRepository = new InMemoryChatMemoryRepository();

        List<Message> messages = new ArrayList<>();
        if (conversation.getId() == null) {
            // 初始化会话
            firstRound = true;
            transactionTemplate.setTimeout(60);
            conversationId = Objects.requireNonNull(transactionTemplate.execute(status -> {
                long convId = conversationRepository.create(conversation);
                var message = this.buildUserMessage(uid, convId, userInput);
                conversationMessageRepository.create(message);
                return convId;
            }));
            messages.add(new UserMessage(userInput));

        } else {
            // 多轮对话添加历史消息 TODO 如果历史轮数过多怎么处理？
            firstRound = false;
            conversationId = conversation.getId();
            messages = conversationMessageRepository.queryByConversationId(conversationId)
                    .stream()
                    .map(conversationMessage -> {
                        String text = conversationMessage.getContent().getText();
                        return switch (conversationMessage.getRole()) {
                            case user -> new UserMessage(text);
                            case system -> new SystemMessage(text);
                            case assistant -> new AssistantMessage(text);
                        };
                    })
                    .collect(toList());
            chatMemoryRepository.saveAll(String.valueOf(conversationId), messages);
            messages.add(new UserMessage(userInput));

            // 在会话中追加用户消息
            var message = this.buildUserMessage(conversation.getUid(), conversationId, userInput);
            conversationMessageRepository.create(message);
        }

        var context = new ConversationContext()
                .setUid(uid)
                .setConversationId(conversationId)
                .setFirstRound(firstRound)
                .setUserInput(userInput)
                .setMessageChunks(new ArrayList<>());
        var chatMemory = MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                // 向前滚动50条消息
                .maxMessages(50)
                .build();
        var advisor = MessageChatMemoryAdvisor.builder(chatMemory)
                .conversationId(String.valueOf(conversationId))
                .build();
        return chatClientFactory.deepSeekChatClient()
                .prompt()
                // TODO 增加自定义advisor拦截器，实时计费
                .advisors(advisor)
                .messages(messages)
                .user(userInput)
                .stream()
                .chatResponse()
                .onErrorMap(ErrorMapper.sysException(ChatErrorCode.CHAT_CALL_LLM_ERROR))
                .doOnError(context::setError)
                .map(this::toMessageChunk)
                .doOnNext(context.getMessageChunks()::add)
                .doOnComplete(() -> context.setSuccess(true))
                .doFinally(signalType -> this.updateConversationOnComplete(signalType, context));
    }

    private void updateConversationOnComplete(SignalType signalType, ConversationContext context) {
        log.info("[updateConversationOnComplete][{}]id:{}", signalType, context.getConversationId());
        // 新增助手消息
        String replyText = context.getMessageChunks()
                .stream()
                .map(MessageChunk::getText)
                .dropWhile(StringUtils::isBlank)
                .collect(Collectors.joining());
        var content = new MessageContent()
                .setText(replyText)
                .setStreamCompleted(context.isSuccess())
                .setStreamError(context.getError());
        ConversationMessage assistantMessage = new ConversationMessage()
                .setUid(context.getUid())
                .setRole(MessageRole.assistant)
                .setConversationId(context.getConversationId())
                .setContent(content);
        conversationMessageRepository.create(assistantMessage);

        // 更新会话标题（不放到事务里，失败就失败了）
        if (context.isFirstRound()) {
            String title = TextUtil.getTopWords(context.getUserInput(), 12);
//            try {
//                title = this.summaryTitle(context.getUserInput(), replyText);
//            } catch (Exception e) {
//                log.error("[generate]summary title error", e);
//                appAlertHelper.alertText("[generate]summary title error, context:{}", context, e);
//                title = TextUtil.getTopWords(context.getUserInput(), 12);
//            }
            Conversation updatingConversation = new Conversation().setId(context.getConversationId()).setTitle(title);
            conversationRepository.updateById(updatingConversation);
        }
    }

    private MessageChunk toMessageChunk(ChatResponse chatResponse) {
        log.info(chatResponse.toString());
        String text = chatResponse.getResult().getOutput().getText();
        if ("STOP".equalsIgnoreCase(chatResponse.getResult().getMetadata().getFinishReason())) {
            return new MessageChunk().setText(text).setFinished(true);
        }
        return new MessageChunk().setText(text).setFinished(false);
    }

    private final static String PROMPT_CODE_AI_CHAT_SUMMARY_TITLE = "AI_CHAT_SUMMARY_TITLE";

    private String summaryTitle(String userInput, String assistantReply) {
        String promptText = userPromptTemplateRepository.queryByCode(PROMPT_CODE_AI_CHAT_SUMMARY_TITLE).getPrompt().getText();
        PromptTemplate template = new PromptTemplate(promptText);
        Map<String, Object> parameters = Map.of(
                "maxChineseChars", 12,
                "maxEnglishWords", 10,
                "userInput", userInput,
                "assistantReply", assistantReply
        );
        Prompt prompt = template.create(parameters);
        log.info("[summaryTitle]{}", prompt);
        return chatClientFactory.openaiChatClient().prompt(prompt).call().content();
    }
}
