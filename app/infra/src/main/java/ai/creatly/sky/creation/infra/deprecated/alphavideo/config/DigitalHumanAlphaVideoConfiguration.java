/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.deprecated.alphavideo.config;

import ai.creatly.sky.creation.infra.deprecated.alphavideo.api.DigitalHumanAlphaApi;
import ai.creatly.sky.creation.infra.integration.FeignUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Feign;
import feign.Retryer;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import lombok.RequiredArgsConstructor;
import okhttp3.OkHttpClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version DigitalHumanAlphaVideoConfiguration.java, 2024-10-24 上午2:51 zhoudong
 */
@Deprecated
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(DigitalHumanAlphaVideoProperties.class)
public class DigitalHumanAlphaVideoConfiguration {

    private final ObjectMapper                     objectMapper;
    private final DigitalHumanAlphaVideoProperties digitalHumanAlphaVideoProperties;

    /**
     * 生成透明通道视频
     */
    @Bean
    public DigitalHumanAlphaApi digitalHumanAlphaApi() {
        OkHttpClient okHttpClient = this.okHttpClient();
        return Feign.builder()
                .encoder(new JacksonEncoder(objectMapper))
                .decoder(new JacksonDecoder(objectMapper))
                .client(new feign.okhttp.OkHttpClient(okHttpClient))
                .options(FeignUtil.requestOptions(okHttpClient))
                .retryer(Retryer.NEVER_RETRY)
                .target(DigitalHumanAlphaApi.class, digitalHumanAlphaVideoProperties.getEndpoint());
    }

    private OkHttpClient okHttpClient() {
        // 每秒视频需要7秒耗时生成透明通道视频，限制在3分钟内，如果达到3分钟，则耗时会达到21分钟
        return new OkHttpClient.Builder()
                // 建立连接超时（库默认10s）
                .connectTimeout(Duration.ofSeconds(5))
                // 请求服务器超时（库默认10s）
                .writeTimeout(Duration.ofSeconds(10))
                // 等待服务器响应超时（库默认10s）
                .readTimeout(Duration.ofSeconds(1500))
                // 总体调用耗时（库默认永不超时）：包含DNS解析+请求+响应
                .callTimeout(Duration.ofSeconds(1500))
                .build();
    }
}
