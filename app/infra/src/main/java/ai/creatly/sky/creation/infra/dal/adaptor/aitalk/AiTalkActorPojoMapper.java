/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.aitalk;

import ai.creatly.sky.creation.domain.core.aittalk.model.AiTalkActor;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiTalkActor;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.repository.PojoMapper;
import com.jspeeder.core.model.OperatorRef;
import com.jspeeder.core.util.json.JSON;
import org.jooq.JSONB;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version AiTalkActorPojoMapper.java, v 0.1 2023-06-23 13:14 joton
 */
@Mapper(config = BaseMapperConfig.class)
public interface AiTalkActorPojoMapper extends PojoMapper<AiTalkActor, CreationAiTalkActor> {

    @Override
    CreationAiTalkActor toEntity(AiTalkActor actor);

    @Override
    AiTalkActor toModel(CreationAiTalkActor creationAiTalkActor);

    default JSONB creatorToJSONB(OperatorRef creator) {
        if (creator == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(creator));
    }

    default OperatorRef toCreator(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseObject(jsonb.data(), OperatorRef.class);
    }
}
