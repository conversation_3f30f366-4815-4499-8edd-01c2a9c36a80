/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.oss.config;

import com.aliyun.oss.ClientConfiguration;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @version AliCloudProperties.java, v 0.1 2023-11-22 上午11:37 zhoudong
 */
@Validated
@Data
@ConfigurationProperties("alibaba.cloud")
public class AliCloudProperties {

    /**
     * alibaba cloud access key.
     */
    @NotBlank
    private String  accessKey;

    /**
     * alibaba cloud secret key.
     */
    @NotBlank
    private String  secretKey;

    /**
     * 智能语音项目appKey
     */
    @NotBlank
    private String  appKey;

    /**
     * 是否开启请求超时
     */
    private boolean requestTimeoutEnabled = true;
    /**
     * 单位：ms
     */
    private int     connectionTimeout     = 10 * 60 * 1000;
    /**
     * 单位：ms
     */
    private int     socketTimeout         = 10 * 60 * 1000;
    /**
     * 单位：ms
     */
    private int     requestTimeout        = 10 * 60 * 1000;
    /**
     * 超过多少时间视为慢请求，单位：ms
     */
    private long    slowRequestsThreshold = ClientConfiguration.DEFAULT_SLOW_REQUESTS_THRESHOLD;
    /**
     * 失败重试次数
     */
    private int     maxErrorRetry         = ClientConfiguration.DEFAULT_MAX_RETRIES;

}
