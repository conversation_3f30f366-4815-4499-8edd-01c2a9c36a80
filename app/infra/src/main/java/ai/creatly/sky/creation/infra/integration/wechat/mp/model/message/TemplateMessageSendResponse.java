/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.wechat.mp.model.message;

import ai.creatly.sky.creation.infra.integration.wechat.common.WechatResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version TemplateMessageSendResponse.java, 2024-11-02 下午9:29 zhoudong
 */
@Data
public class TemplateMessageSendResponse extends WechatResponse {

    @JsonProperty("msgid")
    private String msgId;
}
