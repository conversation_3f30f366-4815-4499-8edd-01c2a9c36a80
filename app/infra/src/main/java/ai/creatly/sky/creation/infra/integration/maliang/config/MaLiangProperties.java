/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.maliang.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 * @version MaLiangProperties.java, v 0.1 2023-12-07 18:14 syoka
 */
@Data
@ConfigurationProperties(prefix = "application.mj-proxy")
public class MaLiangProperties {

    private List<MaLiangEndpoint> endpoints;

    @Data
    public static class MaLiangEndpoint {
        /**
         * 服务器标识
         */
        private String  hostname;
        /**
         * 账户标识
         */
        private String  accountKey;
        /**
         * 地址
         */
        private String  baseUrl;
        /**
         * 权重信息
         */
        private Integer weight;
    }

}

