package ai.creatly.sky.creation.infra.ai.model.volcengine.config;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @version KlingApiProperties.java, v0.1 2025-03-04 14:02
 */
@Data
@Validated
@ConfigurationProperties(prefix = "application.volcengine-audio-clone.api")
public class AudioCloneApiProperties {
    @NotBlank
    private String accessToken;
    @NotBlank
    private String secretKey;
    @NotBlank
    private String url;
    @NotBlank
    private String appid;
}
