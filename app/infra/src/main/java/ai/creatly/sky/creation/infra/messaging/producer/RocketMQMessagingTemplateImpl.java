/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.messaging.producer;

import ai.creatly.sky.creation.domain.common.messaging.Message;
import ai.creatly.sky.creation.domain.common.messaging.TransactionMessage;
import ai.creatly.sky.creation.domain.common.messaging.producer.MQDestination;
import ai.creatly.sky.creation.domain.common.messaging.producer.MessagingTemplate;
import ai.creatly.sky.creation.domain.common.messaging.producer.transaction.TransactionCallback;
import ai.creatly.sky.creation.domain.common.messaging.support.MQErrorCode;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.json.JSON;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.*;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.messaging.MessageHeaders;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * RocketMQ 发送消息的实现
 *
 * <AUTHOR>
 * @version RocketMQMessagingTemplateImpl.java, v 0.1 2023-10-26 下午9:34 zhoudong
 */
@Slf4j
@RequiredArgsConstructor
public class RocketMQMessagingTemplateImpl implements MessagingTemplate, ApplicationContextAware {

    private final RocketMQProducerFactory rocketMQProducerFactory;
    private final AppAlertHelper          appAlertHelper;

    private ApplicationContext applicationContext;

    @Override
    public <T> String send(String destination, Message<T> message, @Nullable MessageHeaders headers) {
        // 构建消息
        MQDestination mqDestination = MQDestination.parse(destination);
        String topic = mqDestination.getTopic();
        String tag = mqDestination.getTag();
        var rocketmqMessage = this.toRocketmqMessage(mqDestination, message, headers);

        // 发送普通消息
        DefaultMQProducer producer = rocketMQProducerFactory.getProducer();
        try {
            SendResult result = producer.send(rocketmqMessage);
            log.info("[convertAndSend]success,topic={},tag={},result={}", topic, tag, result);
            return result.getMsgId();
        } catch (Exception e) {
            log.error("[convertAndSend]error,topic={},tag={}", topic, tag, e);
            RocketMQAlertMessage alertMessage = RocketMQAlertMessage.builder()
                    .topic(topic)
                    .tag(tag)
                    .messageKey(message.getMessageKey())
                    .build();
            appAlertHelper.alert("[send message fail]", alertMessage, e);
            throw new SysException(MQErrorCode.SEND_MESSAGE_FAIL, e);
        }
    }

    @Override
    public <T> String send(String destination, TransactionMessage<T> message, @Nullable MessageHeaders headers) {
        // 构建消息
        MQDestination mqDestination = MQDestination.parse(destination);
        String topic = mqDestination.getTopic();
        String tag = mqDestination.getTag();
        var rocketmqMessage = this.toRocketmqMessage(mqDestination, message, headers);

        // 发送事务消息
        var transactionCallbackClass = message.getTransactionCallbackClass();
        TransactionCallback<T> transactionCallback = applicationContext.getBean(transactionCallbackClass);
        String producerGroupId = transactionCallback.producerGroupId();
        TransactionMQProducer producer = rocketMQProducerFactory.getTransactionProducer(producerGroupId);
        TransactionSendResult result;
        try {
            result = producer.sendMessageInTransaction(rocketmqMessage, message);
        } catch (Exception e) {
            log.error("[send]error,topic={},tag={}", topic, tag, e);
            RocketMQAlertMessage alertMessage = RocketMQAlertMessage.builder()
                    .topic(topic)
                    .tag(tag)
                    .messageKey(message.getMessageKey())
                    .build();
            appAlertHelper.alert("[send message fail]", alertMessage, e);
            throw new SysException(MQErrorCode.SEND_MESSAGE_FAIL, e);
        }

        LocalTransactionState txState = result.getLocalTransactionState();
        if (txState == LocalTransactionState.ROLLBACK_MESSAGE) {
            log.error("[send]error,topic={},tag={}", topic, tag);
            RocketMQAlertMessage alertMessage = RocketMQAlertMessage.builder()
                    .topic(topic)
                    .tag(tag)
                    .messageKey(message.getMessageKey())
                    .build();
            appAlertHelper.alert("[send message fail]", alertMessage);
            throw new SysException(MQErrorCode.SEND_MESSAGE_FAIL);
        }
        log.info("[send]success,topic={},tag={},result={},txState={}", topic, tag, result, txState);
        return result.getMsgId();
    }

    private <T> org.apache.rocketmq.common.message.Message toRocketmqMessage(MQDestination destination,
                                                                             Message<T> message,
                                                                             @Nullable MessageHeaders headers) {
        String topic = destination.getTopic();
        String tag = destination.getTag();
        String messageKey = message.getMessageKey();
        final byte[] body = convertBody(message.getPayload());

        var rocketmqMessage = new org.apache.rocketmq.common.message.Message(topic, tag, messageKey, body);
        if (headers != null) {
            headers.forEach((key, value) -> {
                if (StringUtil.isNotBlank(key) && value != null) {
                    rocketmqMessage.putUserProperty(key, Objects.toString(value));
                }
            });
        }

        return rocketmqMessage;
    }

    private static byte[] convertBody(Object payload) {
        final byte[] body;
        if (payload instanceof String) {
            body = ((String) payload).getBytes(StandardCharsets.UTF_8);
        } else if (payload instanceof byte[]) {
            body = (byte[]) payload;
        } else {
            body = JSON.toJSONBytes(payload);
        }
        return body;
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
