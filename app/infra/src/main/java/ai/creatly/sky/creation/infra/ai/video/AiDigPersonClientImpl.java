package ai.creatly.sky.creation.infra.ai.video;

import ai.creatly.sky.creation.domain.core.ai.audio.model.AiVideoChanjingBizErrorCode;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskBizType;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskCommonInput;
import ai.creatly.sky.creation.domain.core.ai.video.AiDigPersonClient;
import ai.creatly.sky.creation.domain.core.ai.video.model.*;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskBizExecStatus;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.userfile.service.builder.AudioFileBuilder;
import ai.creatly.sky.creation.domain.core.userfile.service.impl.UserFileServiceImpl;
import ai.creatly.sky.creation.infra.ai.model.bailian.config.BailianProperties;
import ai.creatly.sky.creation.infra.ai.model.chanjing.api.ChanJingApi;
import ai.creatly.sky.creation.infra.ai.model.chanjing.model.ChanJingResponse;
import ai.creatly.sky.creation.infra.ai.model.chanjing.model.ChanJingVideoRequest;
import ai.creatly.sky.creation.infra.dal.adaptor.ai.AiToolsRepositoryImpl;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import com.alibaba.fastjson.JSONObject;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AiDigPersonClientImpl implements AiDigPersonClient {


    private final ChanJingApi           chanJingApi;
    private final BailianProperties     bailianProperties;
    private final UserFileServiceImpl   userFileService;
    private final AudioFileBuilder      audioFileBuilder;
    private final UserFileHelper        userFileHelper;
    private final AiToolsRepositoryImpl aiToolsRepository;

    @Override
    public AiVideoTaskResult generate(DigPersonVideoTaskInput input, UserContext userContext) {

        AiVideoTaskResult result = new AiVideoTaskResult();
        DigPersonVideoRequest videoRequest = input.getRequest();

        //处理TTS
        if (videoRequest.getAudioUrl()==null||videoRequest.getAudioUrl().isEmpty()){
            log.info("开始数字人视频配套TTS");
            String model;
            if (input.getRequest().getVoiceCode().endsWith("_v2")) {
                model = "cosyvoice-v2";
            } else {
                model = "cosyvoice-v1";
            }
            byte[] audioData = this.generateAudioBytesBL(input.getRequest().getText(),
                    input.getRequest().getVoiceCode(),
                    input.getRequest().getVolume(),
                    input.getRequest().getPitchRate(),
                    input.getRequest().getSpeechRate(),
                    model);

            if (audioData==null) {
                log.error("数字人视频配套音频生成失败");
                String errorMsg = AiVideoChanjingBizErrorCode.getErrorMsg(AiVideoChanjingBizErrorCode.ERROR_CODE_11.getCode());
                result.setErrorMsg(errorMsg);
                result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                return result;
            }
            //构建并上传
            UserFile file = audioFileBuilder.build(String.valueOf(IdHelper.getId()), audioData, FileBizSource.AI_AUDIO_CLONE, userContext);
            //上传音频到oss
            userFileService.upload(audioData, file);
            if (!file.getOssUrl().isEmpty()) {
                log.info("音频上传至:{}",file.getOssUrl());
                String httpUrl = userFileHelper.getHttpUrl(file);
                log.info("音频http url获取成功:{}", httpUrl);
                videoRequest.setAudioUrl(httpUrl);
            }
            else {
                log.error("数字人视频配套音频上传失败");
                String errorMsg = AiVideoChanjingBizErrorCode.getErrorMsg(AiVideoChanjingBizErrorCode.ERROR_CODE_10.getCode());
                result.setErrorMsg(errorMsg);
                result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                return result;
            }
        }

        ChanJingVideoRequest request = new ChanJingVideoRequest()
                .setPerson(new ChanJingVideoRequest.Person()
                        .setId(videoRequest.getId())
                        .setFigureType(videoRequest.getFigureType())
                        .setWidth(videoRequest.getPersonWidth())
                        .setHeight(videoRequest.getPersonHeight())
                        .setY(0)
                        .setX(0)
                )
                .setAudio(new ChanJingVideoRequest.Audio()
                        .setWavUrl(videoRequest.getAudioUrl())
                        .setLanguage(videoRequest.getLanguage())
                )
                .setScreenWidth(videoRequest.getScreenWidth())
                .setScreenHeight(videoRequest.getScreenHeight())
                .setBgColor("#EDEDED");

        log.info("蝉镜数字人视频，任务提交request:{}", JSON.toJSONString(request));
        ChanJingResponse<String> response = chanJingApi.generateVideo(request);

        if (response.isSuccess()) {
            log.info("数字人视频生成成功：{}" , response);
            result.setTaskId(response.getData());
            org.json.JSONObject data = new org.json.JSONObject();
            data.put("id", response.getData());
            result.setData(data);
            log.info("储存的TaskId为：{}" ,result.getTaskId());
        }
        else {
            log.info("数字人视频生成失败:");
            String code = String.valueOf(response.getCode());
            if (AiVideoChanjingBizErrorCode.ERROR_CODE_40001.getCode().equals(code)) {
                log.error("数字人提交超出QPS限制，尝试重新提交任务");
                result.setBizExecStatus(AiTaskBizExecStatus.PROCESSING);
            }
            else {
                String errorMsg = AiVideoChanjingBizErrorCode.getErrorMsg(code);
                result.setErrorMsg(errorMsg);
                result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                log.error("数字人视频，提交失败response:{},错误原因可能为:{}", response, errorMsg);
            }
        }
        return result;
    }

    @Override
    public CreditsExpense expense(AiTask aiTask) {
        var input = aiTask.parseBizInput(DigPersonVideoTaskInput.class);
        int amount;
        int credits = aiToolsRepository.getBaseCredits("chan_jing_video", AiTaskBizType.video_digital_person_video.name(), null);
        if (input.getRequest().getWordCount()!=null && input.getRequest().getSecondCount()==null && input.getRequest().getSpeechRate()!=null) {
            float speechRate = input.getRequest().getSpeechRate();
            int wordCount = input.getRequest().getWordCount();
            double standardSpeechSpeed = 0.2;
            amount = (int) Math.ceil(wordCount * credits * standardSpeechSpeed / speechRate);
            log.info("数字人任务元气计算成功，按字数计算，结果为:{}", amount);
        }
        else if (input.getRequest().getWordCount()==null && input.getRequest().getSecondCount()!=null) {
            int secondCount = input.getRequest().getSecondCount();
            amount = secondCount * credits;
            log.info("数字人任务元气计算成功，按秒数计算，结果为:{}", amount);
        }
        else {
            log.error("数字人任务元气消耗计算失败，参数错误");
            return null;
        }
        return CreditsExpense.builder()
                .uid(aiTask.getOwnerId())
                .type(CreditAccountType.GENERAL)
                .amount(amount)
                // 完成一条创作任务，扣除一次费用
                .bizType(CreditLogBizType.VIDEO_GENERATE)
                .bizNo(aiTask.getId().toString())
                .build();
    }

    @Override
    public AiVideoTaskResult queryGeneration(String taskId, UserContext userContext) {
        AiVideoTaskResult result = new AiVideoTaskResult();
        result.setBizExecStatus(AiTaskBizExecStatus.PROCESSING);

        log.info("蝉镜数字人视频，开始查询视频:{}", taskId);
        ChanJingResponse<JSONObject> responseData = chanJingApi.queryVideoDetail(taskId);
        log.info("蝉镜数字人视频，状态查询response:{}", responseData);
        if (!responseData.isSuccess()){
            String errorMsg = AiVideoChanjingBizErrorCode.getErrorMsg(String.valueOf(responseData.getCode()));
            result.setErrorMsg(errorMsg);
            result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
            log.error("数字人视频，查询失败response:{},错误原因可能为:{}", responseData, errorMsg);
            return result;
        }
        JSONObject data = responseData.getData();
        if ("30".equals(data.getString("status"))){
            log.info("数字人视频查询：生成完成");

            //视频转存
            List<OrderedVideoAsset> assetList = new ArrayList<>();
            OrderedVideoAsset orderedVideoAsset = new OrderedVideoAsset();
            orderedVideoAsset.setId(data.getString("id"));
            orderedVideoAsset.setVideoUrl(data.getString("video_url"));
            log.info("生成视频url：{}", data.getString("video_url"));
            assetList.add(orderedVideoAsset);
            result.setAssets(assetList);
            result.setBizExecStatus(AiTaskBizExecStatus.SUCCEED);
        }
        else if ("10".equals(data.getString("status"))) {
            log.info("数字人视频查询：生成中");
            result.setBizExecStatus(AiTaskBizExecStatus.PROCESSING);
        }
        else {
            //错误处理
            String code = String.valueOf(responseData.getCode());
            if (AiVideoChanjingBizErrorCode.ERROR_CODE_40001.getCode().equals(code)) {
                result.setBizExecStatus(AiTaskBizExecStatus.PROCESSING);
                log.error("数字人提交超出QPS限制，尝试重新提交任务");
            }
            else if (data.getString("msg")!=null&&AiVideoChanjingBizErrorCode.ERROR_CODE_12.getMsg().equals(data.getString("msg"))) {
                result.setErrorMsg(AiVideoChanjingBizErrorCode.getErrorMsg(AiVideoChanjingBizErrorCode.ERROR_CODE_12.getCode()));
                result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                log.error("蝉镜数字人视频，蝉豆余额不足，生成失败response:{}", responseData);
            }
            else {
                String errorMsg = AiVideoChanjingBizErrorCode.getErrorMsg(code);
                result.setErrorMsg(errorMsg);
                result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                log.error("数字人视频，生成失败response:{},错误原因可能为:{}", responseData, errorMsg);
            }
        }

        return result;

    }

    private byte[] generateAudioBytesBL(String text, String voice, Integer volume ,Float pitchRate, Float speechRate, String model) {
        if (volume == null) { volume = 50;}
        if (speechRate == null) { speechRate = 1f;}
        if (pitchRate == null) { pitchRate = 1f; }

        SpeechSynthesisParam param = SpeechSynthesisParam.builder()
                .apiKey(bailianProperties.getApiKey())
                .model(model)
                .voice(voice)
                .volume(volume)
                .pitchRate(pitchRate)
                .speechRate(speechRate)
                .build();
        SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, null);
        ByteBuffer audioBuffer = synthesizer.call(text);
        if (audioBuffer==null){
            return null;
        }
        byte[] audioBytes = new byte[audioBuffer.remaining()];
        audioBuffer.get(audioBytes);
        return audioBytes;
    }
}

