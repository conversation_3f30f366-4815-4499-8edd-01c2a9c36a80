/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.stablediffusion.api;

import lombok.Data;

/**
 * <AUTHOR>
 * @version ShotEffectApiContainer.java, v 0.1 2023-07-29 11:59 joton
 */
@Data(staticConstructor = "of")
public class ShotEffectApiContainer {


    private final Integer       index;
    private final String        hostName;
    private final ShotEffectApi shotEffectApi;
}
