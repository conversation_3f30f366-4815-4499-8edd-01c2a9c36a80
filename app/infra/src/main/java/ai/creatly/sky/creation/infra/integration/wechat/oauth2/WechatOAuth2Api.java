/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.wechat.oauth2;

import ai.creatly.sky.creation.infra.integration.wechat.common.WechatResponse;
import ai.creatly.sky.creation.infra.integration.wechat.oauth2.model.UserInfoResponse;
import ai.creatly.sky.creation.infra.integration.wechat.oauth2.model.UserTokenResponse;
import feign.Headers;
import feign.Param;
import feign.RequestLine;

/**
 * <AUTHOR>
 * @version WechatOAuth2Api.java, v 0.1 2024-03-31 10:55 syoka
 */
public interface WechatOAuth2Api {

    /**
     * <ul>
     *  <li>开放平台网页扫码登录：<a href="https://developers.weixin.qq.com/doc/oplatform/Mobile_App/WeChat_Login/Wechat_Login.html">文档</a><li/>
     *  <li>公众号网页授权登录：<a href="https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/Wechat_webpage_authorization.html">文档</a><li/>
     * <ul/>
     *
     * @param appId  应用唯一标识，在微信开放平台提交应用审核通过后获得
     * @param secret 应用密钥AppSecret，在微信开放平台提交应用审核通过后获得
     * @param code   填写第一步获取的code参数
     * @return -
     */
    @RequestLine("GET /sns/oauth2/access_token?appid={appId}&secret={secret}&code={code}&grant_type=authorization_code")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    UserTokenResponse getUserAccessToken(@Param("appId") String appId, @Param("secret") String secret, @Param("code") String code);

    /**
     * <a href="https://developers.weixin.qq.com/doc/oplatform/Website_App/WeChat_Login/Authorized_Interface_Calling_UnionID.html">文档</a>
     *
     * @param accessToken -
     * @param openId      -
     * @return -
     */
    @RequestLine("GET /sns/auth?access_token={accessToken}&openid={openId}")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    WechatResponse checkUserAccessToken(@Param("accessToken") String accessToken, @Param("openId") String openId);

    @RequestLine("GET /sns/oauth2/refresh_token?appid={appId}&grant_type=refresh_token&refresh_token={refreshToken}")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    UserTokenResponse refreshUserAccessToken(@Param("appId") String appId, @Param("refreshToken") String refreshToken);

    @RequestLine("GET /sns/userinfo?access_token={accessToken}&openid={openId}")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    UserInfoResponse getUserInfo(@Param("accessToken") String accessToken, @Param("openId") String openId);
}
