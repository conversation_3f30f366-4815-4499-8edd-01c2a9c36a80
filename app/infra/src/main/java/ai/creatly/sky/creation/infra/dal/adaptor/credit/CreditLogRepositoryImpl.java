/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.credit;

import ai.creatly.sky.creation.domain.core.credit.model.CreditHistory;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLog;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.repository.CreditLogRepository;
import ai.creatly.sky.creation.infra.dal.tables.daos.CreditHistoryDAO;
import ai.creatly.sky.creation.infra.dal.tables.daos.CreditLogDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationCreditHistory;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationCreditLog;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_CREDIT_HISTORY;
import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_CREDIT_LOG;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version CreditLogRepositoryImpl.java, v 0.1 2023-12-29 下午6:40 zhoudong
 */
@Repository
@RequiredArgsConstructor
public class CreditLogRepositoryImpl implements CreditLogRepository {

    private final DSLContext                dsl;
    private final CreditLogDAO              creditLogDAO;
    private final CreditLogPojoMapper       creditLogPojoMapper;
    private final CreditCreditHistoryMapper creditCreditHistoryMapper;
    private final CreditHistoryDAO          creditHistoryDAO;
    private final TransactionTemplate       transactionTemplate;

    @Override
    public void create(CreditLog creditLog) {
        CreationCreditLog creationCreditLog = creditLogPojoMapper.toEntity(creditLog);
        if (creationCreditLog.getId() == null) {
            creationCreditLog.setId(IdHelper.getId());
        }

        CreationCreditHistory[] creationCreditHistories = creditLog.getCreditDeltas()
                .stream()
                .map(creditDelta -> creditCreditHistoryMapper.toEntity(creditLog, creditDelta))
                .toArray(CreationCreditHistory[]::new);

        transactionTemplate.executeWithoutResult(status -> {
            creditLogDAO.insert(creationCreditLog);
            creditHistoryDAO.insert(creationCreditHistories);
        });
    }

    @Override
    public Page<CreditHistory> queryCreditHistoryPage(long uid, Pageable pageable) {
        // count
        long count = dsl.selectCount()
                .from(CREATION_CREDIT_HISTORY)
                .where(CREATION_CREDIT_HISTORY.UID.eq(uid))
                .fetchSingleInto(Long.class);

        // list
        List<CreditHistory> userCreditAccounts = dsl.selectFrom(CREATION_CREDIT_HISTORY)
                .where(CREATION_CREDIT_HISTORY.UID.eq(uid))
                .orderBy(CREATION_CREDIT_HISTORY.CREATED_AT.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchInto(CreationCreditHistory.class)
                .stream()
                .map(creditCreditHistoryMapper::toModel)
                .collect(toList());

        return Page.of(userCreditAccounts, pageable, count);
    }

    @Override
    public Page<CreditLog> queryPage(long uid, Pageable pageable) {
        // count
        long count = dsl.selectCount()
                .from(CREATION_CREDIT_LOG)
                .where(CREATION_CREDIT_LOG.UID.eq(uid))
                .fetchSingleInto(Long.class);

        // content
        List<CreditLog> creditLogs = dsl.selectCount()
                .from(CREATION_CREDIT_LOG)
                .where(CREATION_CREDIT_LOG.UID.eq(uid))
                .orderBy(CREATION_CREDIT_LOG.CREATED_AT.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchStreamInto(CreationCreditLog.class)
                .map(creditLogPojoMapper::toModel)
                .collect(toList());

        // todo CreditHistory 增加 log_id 字段，关联到 CreditLog 上。然后再加一个系统任务修复一下历史数据。

        return Page.of(creditLogs, pageable, count);
    }

    @Override
    public boolean existsExpenseLogByBizNo(long uid, CreditLogBizType bizType, String bizNo) {
        return dsl.selectCount()
                .from(CREATION_CREDIT_LOG)
                .where(CREATION_CREDIT_LOG.UID.eq(uid))
                .and(CREATION_CREDIT_LOG.BIZ_TYPE.eq(bizType.name()))
                .and(CREATION_CREDIT_LOG.BIZ_NO.eq(bizNo))
                .and(CREATION_CREDIT_LOG.TYPE.eq(CreditLog.EXPENSE))
                .fetchSingleInto(Integer.class) > 0;
    }

    @Override
    public @Nullable CreditLog queryExpenseLogByBizNo(long uid, CreditLogBizType bizType, String bizNo) {
        return dsl.selectFrom(CREATION_CREDIT_LOG)
                .where(CREATION_CREDIT_LOG.UID.eq(uid))
                .and(CREATION_CREDIT_LOG.BIZ_TYPE.eq(bizType.name()))
                .and(CREATION_CREDIT_LOG.BIZ_NO.eq(bizNo))
                .and(CREATION_CREDIT_LOG.TYPE.eq(CreditLog.EXPENSE))
                .fetchOptionalInto(CreationCreditLog.class)
                .map(creditLogPojoMapper::toModel)
                .map(creditLog -> {
                    List<CreditHistory> histories = dsl.selectFrom(CREATION_CREDIT_HISTORY)
                            .where(CREATION_CREDIT_HISTORY.UID.eq(uid))
                            .and(CREATION_CREDIT_HISTORY.BIZ_TYPE.eq(bizType.name()))
                            .and(CREATION_CREDIT_HISTORY.BIZ_NO.eq(bizNo))
                            .and(CREATION_CREDIT_HISTORY.TYPE.eq(CreditLog.EXPENSE))
                            .fetchStreamInto(CreationCreditHistory.class)
                            .map(creditCreditHistoryMapper::toModel)
                            .collect(toList());
                    return creditLog.setHistories(histories);
                })
                .orElse(null);
    }

    @Override
    public boolean existsIncomeLogByBizNo(long uid, CreditLogBizType bizType, String bizNo) {
        return dsl.selectCount()
                .from(CREATION_CREDIT_LOG)
                .where(CREATION_CREDIT_LOG.UID.eq(uid))
                .and(CREATION_CREDIT_LOG.BIZ_TYPE.eq(bizType.name()))
                .and(CREATION_CREDIT_LOG.BIZ_NO.eq(bizNo))
                .and(CREATION_CREDIT_LOG.TYPE.eq(CreditLog.INCOME))
                .fetchSingleInto(Integer.class) > 0;
    }
}
