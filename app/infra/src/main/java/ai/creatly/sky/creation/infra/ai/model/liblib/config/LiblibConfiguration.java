/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.model.liblib.config;

import ai.creatly.sky.creation.infra.ai.model.liblib.api.LiblibApi;
import ai.creatly.sky.creation.infra.integration.FeignUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Feign;
import feign.Retryer;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.okhttp.OkHttpClient;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version LiblibConfiguration.java, v0.1 2025-02-28 12:14
 */
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(LiblibApiProperties.class)
public class LiblibConfiguration {

    private final ObjectMapper objectMapper;
    private final LiblibApiProperties liblibApiProperties;

    @Bean
    public LiblibApi liblibImageApi() {
        var signInterceptor = new LiblibSignInterceptor(liblibApiProperties);
        okhttp3.OkHttpClient okHttpClient = okHttpClient();
        return Feign.builder()
                .encoder(new JacksonEncoder(objectMapper))
                .decoder(new JacksonDecoder(objectMapper))
                .client(new OkHttpClient(okHttpClient))
                .options(FeignUtil.requestOptions(okHttpClient))
                .retryer(Retryer.NEVER_RETRY)
                .requestInterceptor(signInterceptor)
                .target(LiblibApi.class, liblibApiProperties.getUrl());
    }

    private okhttp3.OkHttpClient okHttpClient() {
        return new okhttp3.OkHttpClient.Builder()
                // 建立连接超时（库默认10s）
                .connectTimeout(Duration.ofSeconds(30))
                // 请求服务器超时（库默认10s）
                .writeTimeout(Duration.ofSeconds(30))
                // 等待服务器响应超时（库默认10s）
                .readTimeout(Duration.ofSeconds(120))
                // 总体调用耗时（库默认永不超时）：包含DNS解析+请求+响应
                .callTimeout(Duration.ofSeconds(150))
                .build();
    }
}
