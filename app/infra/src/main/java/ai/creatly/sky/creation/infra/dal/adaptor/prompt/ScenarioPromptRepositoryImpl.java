/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.prompt;

import ai.creatly.sky.creation.domain.deprecated.consult.modal.request.ScenarioPromptQueryRequest;
import ai.creatly.sky.creation.domain.support.prompt.model.ScenarioPrompt;
import ai.creatly.sky.creation.domain.support.prompt.model.admin.bizsource.response.AdminPromptBizSourceQueryResponse;
import ai.creatly.sky.creation.domain.support.prompt.model.admin.category.response.AdminPromptCategoryQueryResponse;
import ai.creatly.sky.creation.domain.support.prompt.model.admin.prompt.request.AdminPromptUpdateRequest;
import ai.creatly.sky.creation.domain.support.prompt.model.category.PromptCategory;
import ai.creatly.sky.creation.domain.support.prompt.repository.ScenarioPromptRepository;
import ai.creatly.sky.creation.infra.dal.adaptor.aichat.CreationScenarioPromptMapper;
import ai.creatly.sky.creation.infra.dal.tables.daos.ScenarioPromptDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationScenarioPrompt;
import ai.creatly.sky.creation.infra.dal.tables.records.ScenarioPromptRecord;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.model.BizStatus;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.Nullable;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_SCENARIO_PROMPT;

@Repository
@RequiredArgsConstructor
public class ScenarioPromptRepositoryImpl implements ScenarioPromptRepository {


    private final DSLContext                   dsl;
    private final ScenarioPromptDAO            scenarioPromptDAO;
    private final CreationScenarioPromptMapper creationScenarioPromptMapper;

    @Override
    public Optional<ScenarioPrompt> getScenarioPrompt(Long scenarioId) {
        return dsl.selectFrom(CREATION_SCENARIO_PROMPT)
                .where(CREATION_SCENARIO_PROMPT.ID.eq(scenarioId))
                .and(CREATION_SCENARIO_PROMPT.STATUS.eq(BizStatus.VALID.name()))
                .fetchOptionalInto(CreationScenarioPrompt.class)
                .map(creationScenarioPromptMapper::toModel);
    }

    @Nullable
    @Override
    public ScenarioPrompt getScenarioPromptByCode(String code) {
        return dsl.selectFrom(CREATION_SCENARIO_PROMPT)
                .where(CREATION_SCENARIO_PROMPT.SCENARIO_CODE.eq(code))
                .and(CREATION_SCENARIO_PROMPT.STATUS.eq(BizStatus.VALID.name()))
                .fetchOptionalInto(CreationScenarioPrompt.class)
                .map(creationScenarioPrompt -> {
                    ScenarioPrompt scenarioPrompt = new ScenarioPrompt();
                    scenarioPrompt.setScenarioCode(creationScenarioPrompt.getScenarioCode());
                    scenarioPrompt.setScenarioName(creationScenarioPrompt.getScenarioName());
                    scenarioPrompt.setPrompt(creationScenarioPrompt.getPrompt());
                    return scenarioPrompt;
                })
                .orElse(null);
    }

    @Override
    public Optional<ScenarioPrompt> findScenarioOptionalById(Long scenarioId) {
        return dsl.selectFrom(CREATION_SCENARIO_PROMPT)
                .where(CREATION_SCENARIO_PROMPT.ID.eq(scenarioId))
                .and(CREATION_SCENARIO_PROMPT.STATUS.eq(BizStatus.VALID.name()))
                .fetchOptionalInto(CreationScenarioPrompt.class)
                .map(creationScenarioPrompt -> {
                    ScenarioPrompt scenarioPrompt = new ScenarioPrompt();
                    scenarioPrompt.setScenarioCode(creationScenarioPrompt.getScenarioCode());
                    scenarioPrompt.setScenarioName(creationScenarioPrompt.getScenarioName());
                    return scenarioPrompt;
                });
    }

    @Override
    public Optional<ScenarioPrompt> findScenarioOptionalByCode(String code) {
        return dsl.selectFrom(CREATION_SCENARIO_PROMPT)
                .where(CREATION_SCENARIO_PROMPT.CATEGORY_CODE.eq(code))
                .and(CREATION_SCENARIO_PROMPT.STATUS.eq(BizStatus.VALID.name()))
                .fetchOptionalInto(CreationScenarioPrompt.class)
                .map(creationScenarioPrompt -> {
                    ScenarioPrompt scenarioPrompt = new ScenarioPrompt();
                    scenarioPrompt.setScenarioCode(creationScenarioPrompt.getScenarioCode());
                    scenarioPrompt.setScenarioName(creationScenarioPrompt.getScenarioName());
                    return scenarioPrompt;
                });
    }

    @Override
    public Page<ScenarioPrompt> queryScenarioPrompt(ScenarioPromptQueryRequest request, Pageable pageable) {
        Condition condition = this.toCondition(request);

        Long count = dsl.selectCount()
                .from(CREATION_SCENARIO_PROMPT)
                .where(condition)
                .and(condition)
                .fetchSingleInto(Long.class);

        List<ScenarioPrompt> scenarioPrompts = dsl.selectFrom(CREATION_SCENARIO_PROMPT)
                .where(condition)
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchInto(CreationScenarioPrompt.class)
                .stream()
                .map(creationScenarioPromptMapper::toModel)
                .collect(Collectors.toList());

        return Page.of(scenarioPrompts, pageable, count);
    }

    @Override
    public Page<AdminPromptCategoryQueryResponse> getPageablePromptCategory(Pageable pageable, String bizSource) {
        // TODO
        return Page.of(new ArrayList<>(), pageable, 1);
    }

    @Override
    public List<AdminPromptBizSourceQueryResponse> getBizSourceList() {
        return Stream.of(
                        Pair.of("MAGICAL_BRUSH", "马良"),
                        Pair.of("INTELLIJ_WRITER", "丹青"),
                        Pair.of("SYSTEM", "系统"))
                .map(e -> {
                    AdminPromptBizSourceQueryResponse response = new AdminPromptBizSourceQueryResponse();
                    response.setCode(e.getLeft());
                    response.setName(e.getRight());
                    return response;
                }).collect(Collectors.toList());
    }

    @Override
    public void deletePromptById(Long id) {
        dsl.update(CREATION_SCENARIO_PROMPT)
                .set(CREATION_SCENARIO_PROMPT.STATUS, BizStatus.INVALID.name())
                .where(CREATION_SCENARIO_PROMPT.ID.eq(id))
                .execute();
    }

    @Override
    public void insert(ScenarioPrompt scenarioPrompt) {
        CreationScenarioPrompt creationScenarioPrompt = creationScenarioPromptMapper.toEntity(scenarioPrompt);
        if (Objects.isNull(creationScenarioPrompt.getId())) {
            creationScenarioPrompt.setId(IdHelper.getId());
        }
        scenarioPromptDAO.insert(creationScenarioPrompt);
    }

    @Override
    public void updateByPromptId(AdminPromptUpdateRequest request) {
        ScenarioPrompt scenarioPrompt = new ScenarioPrompt()
                .setId(Long.parseLong(request.getId()))
                .setPrompt(request.getPrompt())
                .setDescription(request.getDescription());
        ScenarioPromptRecord record = creationScenarioPromptMapper.toUpdatingRecord(scenarioPrompt);
        dsl.update(CREATION_SCENARIO_PROMPT)
                .set(record)
                .where(CREATION_SCENARIO_PROMPT.ID.eq(scenarioPrompt.getId()))
                .execute();
    }

    @Override
    public Page<PromptCategory> getPromptCategory(Pageable pageable, String bizSource, String name) {
        // TODO
        return Page.empty(pageable);
    }

    @Override
    public List<ScenarioPrompt> findScenarios() {
        return dsl.selectFrom(CREATION_SCENARIO_PROMPT)
                .limit(5)
                .fetchStreamInto(CreationScenarioPrompt.class)
                .map(creationScenarioPrompt -> new ScenarioPrompt()
                        .setId(creationScenarioPrompt.getId())
                        .setScenarioCode(creationScenarioPrompt.getScenarioCode())
                        .setScenarioName(creationScenarioPrompt.getScenarioName())
                        .setPrompt(creationScenarioPrompt.getPrompt()))
                .collect(Collectors.toList());
    }


    private Condition toCondition(ScenarioPromptQueryRequest request) {
        Condition condition = DSL.trueCondition();
        if (StringUtils.isNotEmpty(request.getKeyword())) {
            // 当前仅支持场景检索
            condition = condition.and(CREATION_SCENARIO_PROMPT.SCENARIO_NAME.contains(request.getKeyword()));
        }
        if (StringUtils.isNotEmpty(request.getSource())) {
            condition = condition.and(CREATION_SCENARIO_PROMPT.CATEGORY_BIZ_SOURCE.eq(request.getSource()));
        }
        if (StringUtils.isNotEmpty(request.getCategory())) {
            condition = condition.and(CREATION_SCENARIO_PROMPT.CATEGORY_CODE.eq(request.getCategory()));
        }
        if (StringUtils.isNotEmpty(request.getScenarioCode())) {
            condition = condition.and(CREATION_SCENARIO_PROMPT.SCENARIO_CODE.eq(request.getScenarioCode()));
        }
        condition = condition.and(CREATION_SCENARIO_PROMPT.STATUS.eq(BizStatus.VALID.name()));
        return condition;
    }
}
