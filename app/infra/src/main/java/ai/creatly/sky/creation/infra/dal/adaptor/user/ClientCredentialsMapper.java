/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.user;

import ai.creatly.sky.creation.domain.support.credential.model.ClientCredential;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationOpenapiCredentials;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.repository.PojoMapper;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version AiTalkActorPojoMapper.java, v 0.1 2023-06-23 13:14 joton
 */
@Mapper(config = BaseMapperConfig.class)
public interface ClientCredentialsMapper extends PojoMapper<ClientCredential, CreationOpenapiCredentials> {

    @Override
    CreationOpenapiCredentials toEntity(ClientCredential credential);

    @Override
    ClientCredential toModel(CreationOpenapiCredentials creationOpenapiCredentials);
}
