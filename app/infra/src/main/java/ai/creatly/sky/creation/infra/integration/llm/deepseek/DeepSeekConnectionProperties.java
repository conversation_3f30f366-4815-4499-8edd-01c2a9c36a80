/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.llm.deepseek;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 *
 * <AUTHOR>
 * @version DeepSeekConnectionProperties.java, v0.1 2025-02-25 19:58
 */
@Data
@ConfigurationProperties("spring.ai.deep-seek")
public class DeepSeekConnectionProperties {

    public static final String DEFAULT_BASE_URL = "https://api.deepseek.com";

    private String apiKey;
    private String baseUrl = DEFAULT_BASE_URL;
}
