/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.aiexam;

import ai.creatly.sky.creation.domain.core.ai.exam.model.AiExamRecord;
import ai.creatly.sky.creation.infra.dal.common.ModelMapper;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseExamRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseExamRecordRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 *
 * <AUTHOR>
 * @version AiExamRecordPojoMapper.java, v0.1 2025-02-20 17:49
 */
@Mapper(config = BaseMapperConfig.class)
public interface AiExamRecordPojoMapper extends ModelMapper<AiExamRecord, CourseExamRecord, CourseExamRecordRecord> {


    @Mapping(target = "showAnswer", ignore = true)
    @Override
    AiExamRecord toModel(CourseExamRecord examRecord);

    @Override
    CourseExamRecord toEntity(AiExamRecord aiExamRecord);
}
