/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.model.kling.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @version KlingResponse.java, v0.1 2025-02-28 12:08
 */
@Data
public class KlingResponse<T> {

    private Integer code;
    private String message;
    private String request_id;
    private T data;

    public boolean isSuccess() {
        return code != null && code == 0;
    }
}
