package ai.creatly.sky.creation.infra.ai.model.kling.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version KlingRequest.java, v0.1 2025-03-04 15:16
 */
@Data
@Accessors(chain = true)
public class KlingClothTryRequest {

    @JsonProperty("model_name")
    private String modelName = "kolors-virtual-try-on-v1-5";


    @JsonProperty("human_image")
    private String humanImage;


    @JsonProperty("cloth_image")
    private String clothImage;

}
