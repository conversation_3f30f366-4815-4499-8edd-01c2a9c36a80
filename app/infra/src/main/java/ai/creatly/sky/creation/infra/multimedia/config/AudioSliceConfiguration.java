/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.multimedia.config;

import ai.creatly.sky.creation.infra.integration.FeignUtil;
import ai.creatly.sky.creation.infra.integration.NetProxyPool;
import ai.creatly.sky.creation.infra.multimedia.api.AudioSliceApi;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Feign;
import feign.Retryer;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version AudioSliceConfiguration.java, v 0.1 2024-09-19 下午3:01 zhoudong
 */
@Configuration
@EnableConfigurationProperties(AudioSliceProperties.class)
@RequiredArgsConstructor
public class AudioSliceConfiguration {

    private final ObjectMapper         objectMapper;
    private final AudioSliceProperties audioSliceProperties;
    private final NetProxyPool         netProxyPool;

    @Bean
    public AudioSliceApi audioSliceApi() {
        okhttp3.OkHttpClient okHttpClient = this.okHttpClient();
        return Feign.builder()
                .encoder(new JacksonEncoder(objectMapper))
                .decoder(new JacksonDecoder(objectMapper))
                .retryer(Retryer.NEVER_RETRY)
                .client(new feign.okhttp.OkHttpClient(okHttpClient))
                .options(FeignUtil.requestOptions(okHttpClient))
                .retryer(Retryer.NEVER_RETRY)
                .target(AudioSliceApi.class, audioSliceProperties.getBaseUrl());
    }

    private okhttp3.OkHttpClient okHttpClient() {
        return new okhttp3.OkHttpClient.Builder()
                // 建立连接超时（库默认10s）
                .connectTimeout(Duration.ofSeconds(5))
                // 请求服务器超时（库默认10s）
                .writeTimeout(Duration.ofSeconds(5))
                // 等待服务器响应超时（库默认10s）
                .readTimeout(Duration.ofSeconds(30))
                // 总体调用耗时（库默认永不超时）：包含DNS解析+请求+响应
                .callTimeout(Duration.ofSeconds(35))
                .proxy(netProxyPool.getProxy(audioSliceProperties.getProxy()))
                .build();
    }
}
