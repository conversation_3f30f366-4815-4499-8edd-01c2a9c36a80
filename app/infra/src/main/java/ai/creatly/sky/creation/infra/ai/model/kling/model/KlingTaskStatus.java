/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.model.kling.model;

/**
 *
 * <AUTHOR>
 * @version AiImageAbility.java, v0.1 2025-03-04 15:00
 */
public enum KlingTaskStatus {

    /**
     * 已提交
     */
    submitted,
    /**
     * 处理中
     */
    processing,

    /**
     * 成功
     */
    succeed,

    /**
     * 失败
     */
    failed
}
