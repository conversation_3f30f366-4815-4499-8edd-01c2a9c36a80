/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.deprecated.alphamerge.config;

import ai.creatly.sky.creation.infra.integration.ProxyName;
import lombok.Data;
import org.jetbrains.annotations.Nullable;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version AvatarMaskVideoProperties.java, v 0.1 2024-09-26 下午6:46 zhoudong
 */
@Data
@ConfigurationProperties(prefix = "application.avatar-mask-video")
public class AvatarMaskVideoProperties {
    private String    baseUrl;
    @Nullable
    private ProxyName proxy;
}
