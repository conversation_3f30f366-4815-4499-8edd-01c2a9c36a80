/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration;

import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;

import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 接口调用模板
 *
 * <AUTHOR>
 * @version ApiTemplate.java, 2024-11-02 下午2:46 zhoudong
 */
public class ApiTemplate {

    /**
     * 设置日志相关参数
     *
     * @param log    日志记录器
     * @param prefix 日志内容前缀
     * @return 远程调用设置阶段
     */
    public static ApiInvokerLogger logger(Logger log, String prefix) {
        return new ApiInvokerLogger(log, prefix);
    }

    /**
     * 接口调用器
     */
    @RequiredArgsConstructor
    public static class ApiInvokerLogger {

        private final Logger log;
        private final String prefix;

        public ApiInvokeRequest request(String requestMessage, Supplier<String> argSupplier) {
            return new ApiInvokeRequest(this, requestMessage, argSupplier, null);
        }

        public ApiInvokeRequest request(String requestMessage, Object... args) {
            return new ApiInvokeRequest(this, requestMessage, null, args);
        }
    }

    /**
     * 接口调用器
     */
    @RequiredArgsConstructor
    public static class ApiInvokeRequest {

        private final ApiInvokerLogger logger;
        @Nullable
        private final String           requestMessage;
        @Nullable
        private final Supplier<String> requestMessageArgGetter;
        @Nullable
        private final Object[]         requestMessageArgs;

        /**
         * 设置远程调用函数
         *
         * @param invocation 远程调用函数
         * @param <RES>      调用结果对象类型
         * @return 远程调用结果处理阶段
         */
        public <RES> ApiInvoker<?, RES> invoke(Supplier<RES> invocation) {
            return new ApiInvoker<>(logger, this, invocation);
        }
    }


    /**
     * 调用结果处理阶段
     *
     * @param <REQ> 请求对象类型
     * @param <RES> 响应对象类型
     */
    @RequiredArgsConstructor
    public static class ApiInvoker<REQ, RES> {
        private final ApiInvokerLogger logger;
        private final ApiInvokeRequest request;
        private final Supplier<RES>    invocation;

        public ApiInvokeResponse<REQ, RES> response(String responseMessage, Function<RES, String> responseMessageArgGetter) {
            return new ApiInvokeResponse<>(logger, request, invocation, responseMessage, responseMessageArgGetter);
        }
    }


    /**
     * 调用结果处理阶段
     *
     * @param <REQ> 请求对象类型
     * @param <RES> 响应对象类型
     */
    @RequiredArgsConstructor
    public static class ApiInvokeResponse<REQ, RES> {
        private final ApiInvokerLogger      logger;
        private final ApiInvokeRequest      request;
        private final Supplier<RES>         invocation;
        @Nullable
        private final String                responseMessage;
        @Nullable
        private final Function<RES, String> responseMessageArgGetter;

        /**
         * 设置判断结果是否成功的函数
         *
         * @param successGetter 判断结果是否成功的函数
         * @return 远程调用结果
         */
        public ApiInvokeFetcher<REQ, RES> success(Function<RES, Boolean> successGetter) {
            return new ApiInvokeFetcher<>(logger, request, invocation, this, successGetter);
        }
    }

    /**
     * 调用结果处理阶段
     *
     * @param <REQ> 请求对象类型
     * @param <RES> 响应对象类型
     */
    @RequiredArgsConstructor
    public static class ApiInvokeFetcher<REQ, RES> {

        private final ApiInvokerLogger            logger;
        private final ApiInvokeRequest            request;
        private final Supplier<RES>               invocation;
        private final ApiInvokeResponse<REQ, RES> response;
        private final Function<RES, Boolean>      successGetter;

        /**
         * 获取调用结果
         *
         * @return 返回调用结果
         */
        public RES fetch() {
            return this.doInvoke();
        }

        /**
         * 获取调用结果
         *
         * @return 返回调用结果
         */
        public <T> T fetchInfo(Function<RES, T> responseMapping) {
            RES res = this.doInvoke();
            return responseMapping.apply(res);
        }

        /**
         * 进行远程调用
         *
         * @return 响应结果
         */
        private RES doInvoke() {
            RES res;
            try {
                res = invocation.get();
            } catch (Exception e) {
                final String requestLog = getRequestLog(request);
                if (requestLog != null) {
                    logger.log.error("{}接口调用异常,{}", logger.prefix, requestLog, e);
                } else {
                    logger.log.error("{}接口调用异常", logger.prefix, e);
                }
                throw new SysException(CommonErrorCode.REMOTE_API_INVOKE_ERROR, logger.prefix, e);
            }
            if (res == null || !successGetter.apply(res)) {
                final String requestLog = getRequestLog(request);
                final String responseLog = getResponseLog(response, res);
                if (requestLog == null && responseLog == null) {
                    logger.log.error("{}接口响应数据不符预期", logger.prefix);
                } else if (requestLog == null || responseLog == null) {
                    String arg = requestLog == null ? responseLog : requestLog;
                    logger.log.error("{}接口响应数据不符预期,{}", logger.prefix, arg);
                }  else {
                    logger.log.error("{}接口响应数据不符预期,{},{}", logger.prefix, requestLog, responseLog);
                }
                throw new SysException(CommonErrorCode.REMOTE_API_RESULT_UNEXPECTED, logger.prefix);
            }
            return res;
        }

        @Nullable
        private String getRequestLog(ApiInvokeRequest request) {
            final String requestLog;
            if (request.requestMessage != null) {
                if (request.requestMessageArgGetter != null) {
                    requestLog = FormatUtil.format(request.requestMessage, request.requestMessageArgGetter.get());
                } else if (request.requestMessageArgs != null) {
                    requestLog = FormatUtil.format(request.requestMessage, request.requestMessageArgs);
                } else {
                    requestLog = request.requestMessage;
                }
            } else {
                requestLog = null;
            }
            return requestLog;
        }

        @Nullable
        private String getResponseLog(ApiInvokeResponse<REQ, RES> response, RES res) {
            final String responseLog;
            if (response.responseMessage != null) {
                if (response.responseMessageArgGetter != null) {
                    responseLog = FormatUtil.format(response.responseMessage, response.responseMessageArgGetter.apply(res));
                } else {
                    responseLog = response.responseMessage;
                }
            } else {
                responseLog = null;
            }
            return responseLog;
        }
    }
}
