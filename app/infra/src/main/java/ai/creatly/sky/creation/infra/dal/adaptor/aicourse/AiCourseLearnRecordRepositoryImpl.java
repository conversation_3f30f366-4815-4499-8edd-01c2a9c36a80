/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.aicourse;

import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.course.model.AiCourseLearnRecord;
import ai.creatly.sky.creation.domain.core.course.service.AiCourseLearnRecordRepository;
import ai.creatly.sky.creation.infra.dal.tables.daos.CourseLearnRecordDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseLearnRecord;
import com.jspeeder.core.data.id.IdHelper;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

import static ai.creatly.sky.creation.infra.dal.Tables.COURSE_LEARN_RECORD;

/**
 * <AUTHOR>
 * @version AiToolsRepositoryImpl.java, v0.1 2025-02-20 09:44
 */
@RequiredArgsConstructor
@Repository
public class AiCourseLearnRecordRepositoryImpl implements AiCourseLearnRecordRepository {

    private final DSLContext                    dsl;
    private final AiCourseLearnRecordPojoMapper aiCourseLearnRecordPojoMapper;
    private final CourseLearnRecordDAO          courseLearnRecordDAO;

    @Override
    public AiCourseLearnRecord getById(Long courseId,Long contentId,UserContext context) {
        List<AiCourseLearnRecord> list = dsl.selectFrom(COURSE_LEARN_RECORD)
                .where(COURSE_LEARN_RECORD.COURSE_ID.eq(courseId).and(COURSE_LEARN_RECORD.CONTENT_ID.eq(contentId)).and(COURSE_LEARN_RECORD.OWNER_ID.eq(context.getUid())))
                .orderBy(COURSE_LEARN_RECORD.UPDATED_AT.desc())
                .fetchStreamInto(CourseLearnRecord.class)
                .map(aiCourseLearnRecordPojoMapper::toModel)
                .toList();
        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public void updateRecord(AiCourseLearnRecord record) {
        courseLearnRecordDAO.update(aiCourseLearnRecordPojoMapper.toEntity(record));
    }

    @Override
    public void createRecord(AiCourseLearnRecord record) {
        if (Objects.isNull(record.getId())) {
            record.setId(IdHelper.getId());
        }
        courseLearnRecordDAO.insert(aiCourseLearnRecordPojoMapper.toEntity(record));
    }

    @Override
    public List<AiCourseLearnRecord> getRecords(Long courseId, UserContext context) {
        return dsl.selectFrom(COURSE_LEARN_RECORD)
                .where(COURSE_LEARN_RECORD.COURSE_ID.eq(courseId).and(COURSE_LEARN_RECORD.OWNER_ID.eq(context.getUid())))
                .orderBy(COURSE_LEARN_RECORD.UPDATED_AT.asc())
                .fetchStreamInto(CourseLearnRecord.class)
                .map(aiCourseLearnRecordPojoMapper::toModel)
                .toList();
    }
}
