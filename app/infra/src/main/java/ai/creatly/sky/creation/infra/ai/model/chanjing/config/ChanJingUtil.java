package ai.creatly.sky.creation.infra.ai.model.chanjing.config;

import ai.creatly.sky.creation.infra.ai.model.chanjing.api.ChanJingApi;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;

public class ChanJingUtil {

    private final ChanJingApi chanJingApi;

    public ChanJingUtil(ChanJingApi chanJingApi) {
        this.chanJingApi = chanJingApi;
    }

    public static void main(String[] args) {
        try{
            //JSONObject data = chanJingApi.queryFontList().getData();
        } catch (Exception e){}
    }
}
