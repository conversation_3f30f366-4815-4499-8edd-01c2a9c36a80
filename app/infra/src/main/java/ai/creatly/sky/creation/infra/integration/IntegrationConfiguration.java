/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration;

import feign.okhttp.OkHttpClient;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version IntegrationConfiguration.java, v 0.1 2023-01-05 21:14 joton
 */
@Configuration
@RequiredArgsConstructor
public class IntegrationConfiguration {

    @Bean
    public OkHttpClient okHttpClient() {
        okhttp3.OkHttpClient delegate = new okhttp3.OkHttpClient.Builder()
                // 建立连接超时（库默认10s）
                .connectTimeout(Duration.ofSeconds(5))
                // 请求服务器超时（库默认10s）
                .writeTimeout(Duration.ofSeconds(5))
                // 等待服务器响应超时（库默认10s）
                .readTimeout(Duration.ofSeconds(5))
                // 总体调用耗时（库默认永不超时）：包含DNS解析+请求+响应
                .callTimeout(Duration.ofSeconds(15))
                .build();
        return new OkHttpClient(delegate);
    }
}
