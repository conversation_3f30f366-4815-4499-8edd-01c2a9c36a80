/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.user;

import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import ai.creatly.sky.creation.domain.core.user.model.UserRole;
import ai.creatly.sky.creation.domain.core.user.model.WechatOpenId;
import ai.creatly.sky.creation.domain.core.user.model.organization.UserOrganization;
import ai.creatly.sky.creation.domain.core.user.model.organization.UserOrganizationStatus;
import ai.creatly.sky.creation.infra.dal.common.ModelMapper;
import ai.creatly.sky.creation.infra.dal.common.UpdatableBuilder;
import ai.creatly.sky.creation.infra.dal.tables.pojos.User;
import ai.creatly.sky.creation.infra.dal.tables.records.UserRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.util.json.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.JSONB;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static ai.creatly.sky.creation.infra.dal.Tables.USER;

/**
 * <AUTHOR>
 * @version UserPojoMapper.java, v 0.1 2023-11-28 11:26 syoka
 */
@Mapper(config = BaseMapperConfig.class)
public interface UserPojoMapper extends ModelMapper<UserInfo, User, UserRecord> {

    @Override
    @Mapping(target = "organizations", ignore = true)
    User toEntity(UserInfo userInfo);

    @Override
    @Mapping(target = "activeOrganization", source = "organizations")
    UserInfo toModel(User user);

    default List<UserRole> jsonbToUserRoles(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseList(jsonb.data(), UserRole.class);
    }

    default JSONB userRolesToJsonb(List<UserRole> userRoles) {
        if (userRoles == null) {
            return null;
        }
        return JSONB.jsonb(JSON.toJSONString(userRoles));
    }

    default UserOrganization toUserOrganization(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        List<UserOrganization> organizations = JSON.parseList(jsonb.data(), UserOrganization.class);
        if (CollectionUtils.isNotEmpty(organizations)) {
            return organizations.stream()
                    .filter(organization -> organization.getStatus() == UserOrganizationStatus.ACTIVE)
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    default JSONB wechatOpenIdsToJsonB(Set<WechatOpenId> wechatOpenIds) {
        if (wechatOpenIds == null) {
            return null;
        }
        return JSONB.jsonb(JSON.toJSONString(wechatOpenIds));
    }

    default Set<WechatOpenId> jsonBToWechatOpenIds(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return new HashSet<>(JSON.parseList(jsonb.data(), WechatOpenId.class));
    }

    @Override
    default void updatable(UpdatableBuilder<UserRecord> builder) {
        builder.updatable(USER.NICKNAME)
                .updatable(USER.PHONE)
                .updatable(USER.EMAIL)
                .updatable(USER.PASSWORD)
                .updatable(USER.AVATAR)
                .updatable(USER.INVITE_CODE)
                .updatable(USER.ORGANIZATIONS)
                .updatable(USER.WECHAT_UNION_ID)
                .updatable(USER.WECHAT_OPEN_IDS)
                .updatable(USER.ROLES);
    }
}
