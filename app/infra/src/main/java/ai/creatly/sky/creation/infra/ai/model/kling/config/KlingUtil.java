package ai.creatly.sky.creation.infra.ai.model.kling.config;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version KlingUtil.java, v0.1 2025-03-10 11:30
 */
public class KlingUtil {

    public static void main(String[] args) {

        try {
            Date expiredAt = new Date(System.currentTimeMillis() + 1800*1000); // 有效时间，此处示例代表当前时间+1800s(30min)
            Date notBefore = new Date(System.currentTimeMillis() - 5*1000); //开始生效的时间，此处示例代表当前时间-5秒
            Algorithm algo = Algorithm.HMAC256("113ccc8058a84a1782d54448690743cd");
            Map<String, Object> header = new HashMap<String, Object>();
            header.put("alg", "HS256");
            String token =  JWT.create()
                    .withIssuer("25d178cb959448f392869f26bbe5ab54")
                    .withHeader(header)
                    .withExpiresAt(expiredAt)
                    .withNotBefore(notBefore)
                    .sign(algo);
            System.out.println(token);
        } catch (Exception e) {
        }
    }
}
