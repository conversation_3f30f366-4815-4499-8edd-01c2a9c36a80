/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.common;

import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.util.json.JSON;
import org.jooq.JSONB;
import org.json.JSONObject;
import org.mapstruct.Mapper;

/**
 *
 * <AUTHOR>
 * @version JSONBMapper.java, v 0.1 2023-11-16 下午4:59 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface JSONBMapper {

    default JSONB jsonObjectToJSONB(JSONObject jsonObject) {
        if (jsonObject == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(jsonObject));
    }

    default JSONObject toJSONObject(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseJSONObject(jsonb.data());
    }
}
