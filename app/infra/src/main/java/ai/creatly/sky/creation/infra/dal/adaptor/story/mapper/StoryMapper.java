/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.story.mapper;

import ai.creatly.sky.creation.domain.core.story.model.role.StoryRole;
import ai.creatly.sky.creation.domain.core.story.model.story.Story;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryStyle;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryType;
import ai.creatly.sky.creation.domain.core.story.model.story.enums.StoryStatus;
import ai.creatly.sky.creation.infra.dal.common.ModelMapper;
import ai.creatly.sky.creation.infra.dal.common.UpdatableBuilder;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationStory;
import ai.creatly.sky.creation.infra.dal.tables.records.StoryRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.mapper.EnumValueMapping;
import com.jspeeder.core.model.OperatorRef;
import com.jspeeder.core.util.json.JSON;
import org.jooq.JSONB;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.Objects;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_STORY;

/**
 * <AUTHOR>
 * @version StoryMapper.java, v 0.1 2024-03-04 23:56 heb
 */
@Mapper(config = BaseMapperConfig.class)
public interface StoryMapper extends ModelMapper<Story, CreationStory, StoryRecord> {

    @Mapping(target = "content", source = "script")
    @Override
    CreationStory toEntity(Story story);

    @Mapping(target = "script", source = "content")
    @Override
    Story toModel(CreationStory creationStory);

    default String typeToString(StoryType storyType) {
        if (Objects.isNull(storyType)) {
            return null;
        }
        return storyType.getCode();
    }

    default StoryType stringToType(String storyType) {
        if (Objects.isNull(storyType)) {
            return null;
        }
        return new StoryType().setCode(storyType);
    }

    default JSONB styleToJsonb(StoryStyle storyStyle) {
        if (Objects.isNull(storyStyle)) {
            return null;
        }
        return JSONB.jsonbOrNull(JSON.toJSONString(storyStyle));
    }

    default StoryStyle jsonbToStyle(JSONB jsonb) {
        if (Objects.isNull(jsonb)) {
            return null;
        }
        return JSON.parseObject(jsonb.data(), StoryStyle.class);
    }

    default List<StoryRole> jsonbToRoles(JSONB jsonb) {
        if (Objects.isNull(jsonb)) {
            return null;
        }
        return JSON.parseList(jsonb.data(), StoryRole.class);
    }

    default JSONB rolesToJsonb(List<StoryRole> roles) {
        if (roles == null) {
            return null;
        }
        return JSONB.jsonbOrNull(JSON.toJSONString(roles));
    }

    default OperatorRef jsonbToOperatorRef(JSONB jsonb) {
        if (Objects.isNull(jsonb)) {
            return null;
        }
        return JSON.parseObject(jsonb.data(), OperatorRef.class);
    }

    default JSONB operatorRefToJsonb(OperatorRef operatorRef) {
        if (Objects.isNull(operatorRef)) {
            return null;
        }
        return JSONB.jsonbOrNull(JSON.toJSONString(operatorRef));
    }

    @EnumValueMapping
    StoryStatus stringToStoryStatus(String status);

    @Override
    default void updatable(UpdatableBuilder<StoryRecord> builder) {
        builder.updatable(CREATION_STORY.NAME)
                .updatable(CREATION_STORY.CONTENT)
                .updatable(CREATION_STORY.STATUS)
                .updatable(CREATION_STORY.STORY_TYPE)
                .updatable(CREATION_STORY.STYLE_CONFIG)
                .updatable(CREATION_STORY.ROLES)
                .updatable(CREATION_STORY.CREATOR)
                .updatable(CREATION_STORY.SOURCE_TYPE)
                .updatable(CREATION_STORY.IDEA);
    }
}
