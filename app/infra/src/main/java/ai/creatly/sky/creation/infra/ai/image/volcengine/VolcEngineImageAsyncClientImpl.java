/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.image.volcengine;

import ai.creatly.sky.creation.domain.core.ai.image.client.async.AiImageSingleAsyncClient;
import ai.creatly.sky.creation.domain.core.ai.image.task.expand.model.ImageExpandTaskInput;
import ai.creatly.sky.creation.domain.core.ai.image.task.generate.model.ImageGenerateTaskInput;
import ai.creatly.sky.creation.domain.core.ai.image.task.repaint.model.ImageRepaintTaskInput;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.infra.ai.image.AiImageApiProvider;
import ai.creatly.sky.creation.infra.ai.image.AiImageApiSupplier;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version VolcEngineImageAsyncClientImpl.java, v0.1 2025-03-04 14:53
 */
@Component
@RequiredArgsConstructor
public class VolcEngineImageAsyncClientImpl implements AiImageSingleAsyncClient, AiImageApiSupplier {

    @Override
    public AiImageApiProvider getProvider() {
        return AiImageApiProvider.volc_engine_async;
    }

    @Override
    public String generate(ImageGenerateTaskInput input, String bizNo) {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public UserFile queryImageGeneration(String taskId, ImageGenerateTaskInput input, UserContext userContext) {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public String expand(ImageExpandTaskInput input, String bizNo) {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public UserFile queryExpandGeneration(String taskId, ImageExpandTaskInput input, UserContext userContext) {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public String repaint(ImageRepaintTaskInput input, String bizNo) {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public UserFile queryRepaintGeneration(String taskId, ImageRepaintTaskInput input, UserContext userContext) {
        throw new UnsupportedOperationException("Not supported yet.");
    }
}
