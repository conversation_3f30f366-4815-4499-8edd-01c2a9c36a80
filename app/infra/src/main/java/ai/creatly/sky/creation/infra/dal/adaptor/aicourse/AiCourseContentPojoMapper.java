/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.aicourse;

import ai.creatly.sky.creation.domain.core.course.model.AiCourseContent;
import ai.creatly.sky.creation.infra.dal.common.ModelMapper;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseContent;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseContentRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 *
 * <AUTHOR>
 * @version AiCourseContentPojoMapper.java, v0.1 2025-02-20 17:49
 */
@Mapper(config = BaseMapperConfig.class)
public interface AiCourseContentPojoMapper extends ModelMapper<AiCourseContent, CourseContent, CourseContentRecord> {

    @Mapping(target = "videoHttpUrl", ignore = true)
    @Override
    AiCourseContent toModel(CourseContent course);

    @Override
    CourseContent toEntity(AiCourseContent aiCourse);
}
