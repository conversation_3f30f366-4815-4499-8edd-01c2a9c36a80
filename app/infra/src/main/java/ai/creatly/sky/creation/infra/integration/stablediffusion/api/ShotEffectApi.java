/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.stablediffusion.api;

import ai.creatly.sky.creation.domain.common.integration.stablediffusion.response.ShotEffectTaskResponse;
import feign.Headers;
import feign.Request;
import feign.RequestLine;
import org.json.JSONObject;

/**
 * 基于svd的动效实现方案
 *
 * <AUTHOR>
 * @version SVDClientApi.java, v 0.1 2024-04-02 16:02 syoka
 */
public interface ShotEffectApi {

    /**
     * 图生动效视频
     *
     * @param body -
     * @return -
     */
    @RequestLine("POST /api/svd/generate")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    ShotEffectTaskResponse submitDynamicEffectTask(JSONObject body, Request.Options options);

}

