/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.videoretalking.api;

import com.jspeeder.core.util.loadbalancer.RoundRobinCounter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version VideoReTalkingApiFactory.java, v 0.1 2024-05-26 上午1:05 zhoudong
 */
@Component
@RequiredArgsConstructor
public class VideoReTalkingApiFactory {

    private final Map<String, RoundRobinCounter>   counterMap = new ConcurrentHashMap<>();
    private final List<VideoReTalkingApiContainer> apiContainers;

    public VideoReTalkingApiContainer next(String name) {
        counterMap.computeIfAbsent(name, k -> new RoundRobinCounter(apiContainers.size()));
        final RoundRobinCounter counter = counterMap.get(name);
        return this.apiContainers.get(counter.next());
    }
}
