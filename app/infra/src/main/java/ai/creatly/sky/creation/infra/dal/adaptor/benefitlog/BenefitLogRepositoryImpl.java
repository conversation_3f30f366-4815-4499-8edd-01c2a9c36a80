/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.benefitlog;

import ai.creatly.sky.creation.domain.core.benefit.log.model.BenefitLog;
import ai.creatly.sky.creation.domain.core.benefit.log.model.BenefitSourceType;
import ai.creatly.sky.creation.domain.core.benefit.log.repository.BenefitLogRepository;
import ai.creatly.sky.creation.infra.dal.tables.daos.BenefitLogDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationBenefitLog;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_BENEFIT_LOG;
import static java.util.stream.Collectors.toMap;

/**
 *
 * <AUTHOR>
 * @version BenefitLogRepositoryImpl.java, v 0.1 2023-10-27 下午6:27 zhoudong
 */
@Repository
@RequiredArgsConstructor
public class BenefitLogRepositoryImpl implements BenefitLogRepository {

    private final BenefitLogPojoMapper benefitLogPojoMapper;
    private final BenefitLogDAO        benefitLogDAO;
    private final DSLContext           dsl;

    @Override
    public long create(BenefitLog benefitLog) {
        CreationBenefitLog entity = benefitLogPojoMapper.toEntity(benefitLog);
        benefitLogDAO.insert(entity);
        Long id = entity.getId();
        Asserts.notNull(id, "will not happen");
        return id;
    }

    @Override
    public Optional<BenefitLog> queryOptionalBySource(BenefitSourceType sourceType, String sourceId) {
        Validates.notNull(sourceType, "sourceType cannot be null");
        Validates.notBlank(sourceId, "sourceId cannot be blank");
        return dsl.selectFrom(CREATION_BENEFIT_LOG)
                .where(CREATION_BENEFIT_LOG.SOURCE_TYPE.eq(sourceType.name()))
                .and(CREATION_BENEFIT_LOG.SOURCE_ID.eq(sourceId))
                .fetchOptionalInto(CreationBenefitLog.class)
                .map(benefitLogPojoMapper::toModel);
    }

    @Override
    public Map<String, BenefitLog> queryBySources(BenefitSourceType sourceType, List<String> sourceIds) {
        if (sourceType == null || sourceIds == null || sourceIds.isEmpty()) {
            return Collections.emptyMap();
        }
        return dsl.selectFrom(CREATION_BENEFIT_LOG)
                .where(CREATION_BENEFIT_LOG.SOURCE_TYPE.eq(sourceType.name()))
                .and(CREATION_BENEFIT_LOG.SOURCE_ID.in(sourceIds))
                .fetchStreamInto(CreationBenefitLog.class)
                .map(benefitLogPojoMapper::toModel)
                .collect(toMap(BenefitLog::getSourceId, Function.identity()));
    }
}
