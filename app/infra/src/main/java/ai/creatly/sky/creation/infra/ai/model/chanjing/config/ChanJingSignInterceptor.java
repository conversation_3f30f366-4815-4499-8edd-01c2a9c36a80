package ai.creatly.sky.creation.infra.ai.model.chanjing.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
public class ChanJingSignInterceptor implements RequestInterceptor {

    private final ChanJingTokenService tokenService;

    public ChanJingSignInterceptor(ChanJingTokenService tokenService){
        this.tokenService = tokenService;
    }

    @Override
    public void apply(RequestTemplate template) {
        template.header("access_token", tokenService.fetchToken());
    }
}
