/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.cameramotion;

import ai.creatly.sky.creation.domain.common.integration.cameramotion.CameraMotionClient;
import ai.creatly.sky.creation.domain.common.integration.cameramotion.model.ImageMoveRequest;
import ai.creatly.sky.creation.domain.common.integration.oss.FileContentMetadata;
import ai.creatly.sky.creation.domain.common.integration.oss.OssTemplate;
import ai.creatly.sky.creation.domain.support.file.model.FileSize;
import ai.creatly.sky.creation.domain.support.multimedia.model.VideoMetadata;
import ai.creatly.sky.creation.infra.integration.cameramotion.api.CameraMotionApi;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version CameraMotionClientImpl.java, v 0.1 2024-09-19 下午7:00 zhoudong
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CameraMotionClientImpl implements CameraMotionClient {

    private final CameraMotionApi cameraMotionApi;
    private final OssTemplate     ossTemplate;

    @Override
    public VideoMetadata imageToVideo(ImageMoveRequest request) {
        long start = System.currentTimeMillis();
        JSONObject response;
        try {
            response = cameraMotionApi.imageToVideo(request);
            long cost = System.currentTimeMillis() - start;
            log.info("[imageToVideo]request:{},response:{},cost:{}ms", request, JSON.toJSONString(response), cost);
        } catch (Exception e) {
            log.error("[imageToVideo]Failed to generate video,request:{}", request, e);
            throw new SysException(CommonErrorCode.REMOTE_API_INVOKE_ERROR, "图片运镜", e);
        }

        // 确保视频文件存在
        ossTemplate.assureStoragePresent(request.getBucket(), request.getOutputVideoKey());

        try {
            long sizeInBytes = response.optLong("size");
            if (sizeInBytes <= 0) {
                FileContentMetadata contentMetadata = ossTemplate.getContentMetadata(request.getBucket(), request.getOutputVideoKey());
                sizeInBytes = contentMetadata.getLength();
            }
            return new VideoMetadata()
                    .setBitrate(response.optInt("bitrate"))
                    .setSize(FileSize.sizeInBytes(sizeInBytes));
        } catch (JSONException e) {
            log.error("[imageToVideo]Failed to parse response:{}", response, e);
            throw new SysException(CommonErrorCode.REMOTE_API_RESULT_UNEXPECTED, "图片运镜", e);
        }
    }
}
