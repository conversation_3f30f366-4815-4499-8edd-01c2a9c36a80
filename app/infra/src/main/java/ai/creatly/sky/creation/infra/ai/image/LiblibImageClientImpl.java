/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.image;

import ai.creatly.sky.creation.domain.core.ai.audio.model.AiImageLiblibBizErrorCode;
import ai.creatly.sky.creation.domain.core.ai.audio.model.KlingBizErrorCode;
import ai.creatly.sky.creation.domain.core.ai.image.AiImageGenerateClient;
import ai.creatly.sky.creation.domain.core.ai.image.model.AiImaggeTaskVars;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskBizType;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskCommonInput;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskCommonResult;
import ai.creatly.sky.creation.domain.core.ai.model.OrderedAsset;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskBizExecStatus;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.infra.ai.model.kling.model.KlingTaskStatus;
import ai.creatly.sky.creation.infra.ai.model.liblib.api.LiblibApi;
import ai.creatly.sky.creation.infra.ai.model.liblib.model.LiblibRequest;
import ai.creatly.sky.creation.infra.ai.model.liblib.model.LiblibResponse;
import ai.creatly.sky.creation.infra.ai.model.liblib.model.LiblibStatusRequest;
import ai.creatly.sky.creation.infra.dal.adaptor.ai.AiToolsRepositoryImpl;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version LiblibImageClientImpl.java, v0.1 2025-02-28 11:49
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class LiblibImageClientImpl implements AiImageGenerateClient {

    private final LiblibApi             liblibApi;
    private final AiToolsRepositoryImpl aiToolsRepository;

    /**
     * AI局部涂抹替换
     * @param input
     * @param userContext
     * @return
     */
    LiblibRequest replaceGenerate(AiTaskCommonInput input, UserContext userContext){
        LiblibRequest liblibRequest = new LiblibRequest();
        liblibRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");
        JSONObject generateParams = new JSONObject();
        JSONObject param1 = new JSONObject();
        param1.put("class_type","LoadImage");
        param1.put("inputs",new JSONObject().put("image",input.getRequest().getImageFile3Refs().getUrl()));
        generateParams.put("158",param1);

        JSONObject param2 = new JSONObject();
        param2.put("class_type","LoadImage");
        param2.put("inputs",new JSONObject().put("image",input.getRequest().getImageFileRefs().getUrl()));
        generateParams.put("161",param2);

        JSONObject param3 = new JSONObject();
        param3.put("class_type","LoadImage");
        param3.put("inputs",new JSONObject().put("image",input.getRequest().getImageFile2Refs().getUrl()));
        generateParams.put("312",param3);
        generateParams.put("workflowUuid","e8cc4f5b1f064e4c8a4a82d8a2520471");

        liblibRequest.setGenerateParams(generateParams);

        log.info("request:{}", liblibRequest);
        return liblibRequest;
    }

    /**
     * AI商品图
     * @param input
     * @param userContext
     * @return
     */
    LiblibRequest goodsGenerate(AiTaskCommonInput input, UserContext userContext){
        LiblibRequest liblibRequest = new LiblibRequest();
        liblibRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");
        JSONObject generateParams = new JSONObject();
        JSONObject param1 = new JSONObject();
        param1.put("class_type","LoadImage");
        param1.put("inputs",new JSONObject().put("image",input.getRequest().getImageFileRefs().getUrl()));
        generateParams.put("11",param1);

        JSONObject param3 = new JSONObject();
        param3.put("class_type", "BaiduTranslateNode");
        param3.put("inputs", new JSONObject().put("text", input.getRequest().getPromptText()));
        generateParams.put("73", param3);
        generateParams.put("workflowUuid","c50183f5a5204ac8a766ae31a382ca46");
        liblibRequest.setGenerateParams(generateParams);
        log.info("request:{}", liblibRequest);
        return liblibRequest;
    }

    /**
     * AI模特图
     * @param input
     * @param userContext
     * @return
     */
    LiblibRequest modelGenerate(AiTaskCommonInput input, UserContext userContext){
        LiblibRequest liblibRequest = new LiblibRequest();
        liblibRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");
        JSONObject generateParams = new JSONObject();

        JSONObject param2 = new JSONObject();
        param2.put("class_type", "EmptyLatentImage");
        param2.put("inputs", new JSONObject().put("width", input.getRequest().getAspectRatio().getWidth()).put("height", input.getRequest().getAspectRatio().getHeight()));
        generateParams.put("5", param2);

        JSONObject param3 = new JSONObject();
        param3.put("class_type", "BaiduTranslateNode");
        param3.put("inputs", new JSONObject().put("text", input.getRequest().getPromptText()));
        generateParams.put("32", param3);
        generateParams.put("workflowUuid","e64fd9bf49254fc5990456a51aac3f4c");
        liblibRequest.setGenerateParams(generateParams);
        log.info("request:{}", liblibRequest);
        return liblibRequest;
    }

    /**
     * AI logo
     * @param input
     * @param userContext
     * @return
     */
    LiblibRequest logoGenerate(AiTaskCommonInput input, UserContext userContext){
        LiblibRequest liblibRequest = new LiblibRequest();
        liblibRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");
        JSONObject generateParams = new JSONObject();

        JSONObject param2 = new JSONObject();
        param2.put("class_type", "EmptyLatentImage");
        param2.put("inputs", new JSONObject().put("width", input.getRequest().getAspectRatio().getWidth()).put("height", input.getRequest().getAspectRatio().getHeight()));
        generateParams.put("5", param2);

        JSONObject param3 = new JSONObject();
        param3.put("class_type", "BaiduTranslateNode");
        param3.put("inputs", new JSONObject().put("text", input.getRequest().getPromptText()));
        generateParams.put("42", param3);
        generateParams.put("workflowUuid","83e958194c04415ab25c53524219d99d");
        liblibRequest.setGenerateParams(generateParams);
        log.info("request:{}", liblibRequest);
        return liblibRequest;
    }

    /**
     * AI 洗图
     * @param input
     * @param userContext
     * @return
     */
    LiblibRequest pImageGenerate(AiTaskCommonInput input, UserContext userContext){
        LiblibRequest liblibRequest = new LiblibRequest();
        liblibRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");
        JSONObject generateParams = new JSONObject();

        JSONObject param2 = new JSONObject();
        param2.put("class_type", "LoadImage");
        param2.put("inputs",new JSONObject().put("image",input.getRequest().getImageFileRefs().getUrl()));
        generateParams.put("404", param2);

        JSONObject param3 = new JSONObject();
        param3.put("class_type", "Float");
        param3.put("inputs", new JSONObject().put("Number", input.getRequest().getImageFidelity()));
        generateParams.put("472", param3);
        generateParams.put("workflowUuid","91eea2fa12624217a250f4b5d7c6c63a");
        liblibRequest.setGenerateParams(generateParams);
        log.info("request:{}", liblibRequest);
        return liblibRequest;
    }

    /**
     * AI 局部消除
     * @param input
     * @param userContext
     * @return
     */
    LiblibRequest removeGenerate(AiTaskCommonInput input, UserContext userContext){
        LiblibRequest liblibRequest = new LiblibRequest();
        liblibRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");
        JSONObject generateParams = new JSONObject();

        JSONObject param1 = new JSONObject();
        param1.put("class_type", "LoadImage");
        param1.put("inputs",new JSONObject().put("image",input.getRequest().getImageFileRefs().getUrl()));
        generateParams.put("10", param1);

        JSONObject param2 = new JSONObject();
        param2.put("class_type", "LoadImage");
        param2.put("inputs",new JSONObject().put("image",input.getRequest().getImageFile2Refs().getUrl()));
        generateParams.put("148", param2);
        generateParams.put("workflowUuid","e0e7f2dfb7d140abbf2bffad2935e271");
        liblibRequest.setGenerateParams(generateParams);
        log.info("request:{}", liblibRequest);
        return liblibRequest;
    }

    LiblibRequest loraGenerateFacade(AiTaskCommonInput input, UserContext userContext){
        if (input.getRequest().getImageFileRefs()!=null && StringUtils.isNotEmpty(input.getRequest().getImageFileRefs().getUrl())) {
            return loraImg2ImgGenerate(input,userContext);
        } else {
            return loraTxt2ImgGenerate(input,userContext);
        }
    }
    /**
     * LORA 增强文生图
     * @param input
     * @param userContext
     * @return
     */
    LiblibRequest loraTxt2ImgGenerate(AiTaskCommonInput input, UserContext userContext){

        String param = aiToolsRepository.getParam(input.getRequest().getLoraModel());

        LiblibRequest liblibRequest = new LiblibRequest();
        liblibRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");
        JSONObject generateParams = new JSONObject();

        JSONObject param1 = new JSONObject();
        param1.put("class_type", "EmptyLatentImage");
        param1.put("inputs",new JSONObject().put("width",input.getRequest().getAspectRatio().getWidth()).put("height",input.getRequest().getAspectRatio().getHeight()).put("batch_size",input.getRequest().getImgCount()));
        generateParams.put("5", param1);

        JSONObject param2 = new JSONObject();
        param2.put("class_type", "KSamplerSelect");
        param2.put("inputs",new JSONObject().put("sampler_name","dpmpp_2m"));
        generateParams.put("16", param2);

        JSONObject param4 = new JSONObject();
        param4.put("class_type", "LoraLoader");
        param4.put("inputs",new JSONObject().put("lora_name",input.getRequest().getLoraModel()).put("strength_model", input.getRequest().getLoraFidelity()));
        generateParams.put("26", param4);

        JSONObject param5 = new JSONObject();
        param5.put("class_type", "FluxGuidance");
        param5.put("inputs",new JSONObject().put("guidance",5.5));
        generateParams.put("41", param5);

        JSONObject param6 = new JSONObject();
        param6.put("class_type", "LibLibTranslate");
        if (StringUtils.isNotEmpty(param)) {
            param6.put("inputs", new JSONObject().put("text", param + " " + input.getRequest().getPromptText()));
        } else {
            param6.put("inputs", new JSONObject().put("text", input.getRequest().getPromptText()));
        }
        generateParams.put("45", param6);

        generateParams.put("workflowUuid","6e8086e9fe994fd1a28e855336a96e6c");
        liblibRequest.setGenerateParams(generateParams);
        log.info("request:{}", liblibRequest);
        return liblibRequest;
    }

    /**
     * LORA 增强图生图
     * @param input
     * @param userContext
     * @return
     */
    LiblibRequest loraImg2ImgGenerate(AiTaskCommonInput input, UserContext userContext){

        String param = aiToolsRepository.getParam(input.getRequest().getLoraModel());

        LiblibRequest liblibRequest = new LiblibRequest();
        liblibRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");
        JSONObject generateParams = new JSONObject();

        if (input.getRequest().getPromptText() != null && !input.getRequest().getPromptText().isEmpty()) {
            JSONObject param6 = new JSONObject();
            param6.put("class_type", "LibLibTranslate");
            if (StringUtils.isNotEmpty(param)) {
                param6.put("inputs", new JSONObject().put("text", param + " " + input.getRequest().getPromptText()));
            }
            else {
                param6.put("inputs", new JSONObject().put("text", input.getRequest().getPromptText()));
            }
            generateParams.put("79", param6);
        }

        JSONObject param1 = new JSONObject();
        param1.put("class_type", "LoadImage");
        param1.put("inputs",new JSONObject().put("image",input.getRequest().getImageFileRefs().getUrl()));
        generateParams.put("32", param1);

        JSONObject loraParam = new JSONObject();
        loraParam.put("class_type", "LoraLoaderModelOnly");
        loraParam.put("inputs",new JSONObject().put("lora_name",input.getRequest().getLoraModel()).put("strength_model", input.getRequest().getLoraFidelity()));
        generateParams.put("42", loraParam);

        JSONObject param2 = new JSONObject();
        param2.put("class_type", "ImageExpandBatch+");
        param2.put("inputs",new JSONObject().put("size",input.getRequest().getImgCount()));
        generateParams.put("56", param2);

        JSONObject param3 = new JSONObject();
        param3.put("class_type", "KSampler");
        param3.put("inputs",new JSONObject().put("denoise", input.getRequest().getImageFidelity()));
        generateParams.put("40", param3);

        JSONObject param4 = new JSONObject();
        param4.put("class_type", "Constant Number");
        param4.put("inputs",new JSONObject().put("number",input.getRequest().getAspectRatio().getWidth()));
        generateParams.put("65", param4);

        JSONObject param5 = new JSONObject();
        param5.put("class_type", "Constant Number");
        param5.put("inputs",new JSONObject().put("number",input.getRequest().getAspectRatio().getHeight()));
        generateParams.put("66", param5);

        generateParams.put("workflowUuid","d5832ee9ee104e1e9e0cea314edab3f2");
        liblibRequest.setGenerateParams(generateParams);
        log.info("图生图request:{}", liblibRequest);
        return liblibRequest;
    }

    @Override
    public AiTaskCommonResult generate(AiTaskCommonInput input, UserContext userContext) {

        AiTaskCommonResult result = new AiTaskCommonResult();

        LiblibRequest liblibRequest = null;
        switch (input.getRequest().getFunction()) {
            case replace -> liblibRequest = this.replaceGenerate(input, userContext);
            case goods -> liblibRequest = this.goodsGenerate(input, userContext);
            case model -> liblibRequest = this.modelGenerate(input, userContext);
            case logo -> liblibRequest = this.logoGenerate(input, userContext);
            case pimage -> liblibRequest = this.pImageGenerate(input, userContext);
            case remove -> liblibRequest = this.removeGenerate(input, userContext);
            case lora -> {
                if (input.getRequest().getLoraModel() != null && !input.getRequest().getLoraModel().isEmpty()) {
                    liblibRequest = this.loraGenerateFacade(input,userContext);
                }
                else {
                    result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                    result.setErrorMsg(AiImageLiblibBizErrorCode.getErrorMsg(AiImageLiblibBizErrorCode.ERROR_CODE_1.getCode()));
                    log.error("lora生图无lora model");
                    return result;
                }
            }
        }

        LiblibResponse<JSONObject> responseData;
        try {
            responseData = liblibApi.comfyuiGenerateImage(liblibRequest);
            log.info("Liblib {} 生图，任务提交response:{}", input.getRequest().getFunction(), responseData);
            JSONObject resultData = responseData.getData();
            if (resultData != null && StringUtils.isNotEmpty(resultData.getString("generateUuid"))) {
                result.setTaskId(resultData.getString("generateUuid"));
            } else {

                String code = String.valueOf(responseData.getCode());
                String errorMsg = AiImageLiblibBizErrorCode.getErrorMsg(code);

                if (AiImageLiblibBizErrorCode.ERROR_CODE_429.getCode().equals(code)||
                        AiImageLiblibBizErrorCode.ERROR_CODE_100054.getCode().equals(code)) {
                    result.setBizExecStatus(AiTaskBizExecStatus.PROCESSING);
                    result.setErrorMsg(errorMsg);
                    log.error("Liblib提交超QPS限制或并发限制，尝试重新提交");
                }else {
                    result.setErrorMsg(errorMsg);
                    result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                    result.setData(JSON.toJSONObject(responseData));
                    log.error("Liblib生图，提交失败response:{},错误原因可能为:{}", responseData, errorMsg);
                }
//                result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
//                LiblibErrorCode liblibErrorCode = LiblibErrorCode.valueOf(responseData.getCode());
//                if (liblibErrorCode != null) {
//                    if (liblibErrorCode == LiblibErrorCode.ERROR_CODE_403 || liblibErrorCode == LiblibErrorCode.ERROR_CODE_429) {
//                        log.error("Liblib 生图异常", liblibErrorCode.getMsg());
//                    }
//                    result.setErrorMsg(liblibErrorCode.getMsg());
//                } else {
//                    result.setData(JSON.toJSONObject(responseData));
//                }
                return result;
            }
            return result;
        } catch (Exception e) {
            log.error("Liblib 生图异常", e);
            result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
            return result;
        }
    }

    @Override
    public CreditsExpense expense(AiTask aiTask) {
        var input = aiTask.parseBizInput(AiTaskCommonInput.class);
        String function = String.valueOf(input.getRequest().getFunction());
        return CreditsExpense.builder()
                .uid(aiTask.getOwnerId())
                .type(CreditAccountType.GENERAL)
                .amount(input.getRequest().getImgCount() * aiToolsRepository.getBaseCredits("liblib_image", AiTaskBizType.image_liblib_generate.name(), function))
                // 完成一条创作任务，扣除一次费用
                .bizType(CreditLogBizType.GEN_IMAGE)
                .bizNo(aiTask.getId().toString())
                .build();
    }

    @Override
    public AiTaskCommonResult queryGeneration(AiTask aiTask, UserContext userContext) {
        var taskVars = aiTask.parseBizVars(AiImaggeTaskVars.class);
        return this.queryGeneration(taskVars.getTaskId(),userContext);
    }

    @Override
    public AiTaskCommonResult queryGeneration(String taskId, UserContext userContext) {
        AiTaskCommonResult result = new AiTaskCommonResult();

        LiblibStatusRequest liblibRequest = new LiblibStatusRequest();
        liblibRequest.setGenerateUuid(taskId);
        LiblibResponse<JSONObject> responseData = liblibApi.comfyuiQueryGeneration(liblibRequest);

        //没有Data的错误先行判断
        if (responseData.getData()==null||responseData.getData().isEmpty()) {
            String code = String.valueOf(responseData.getCode());
            String errorMsg = AiImageLiblibBizErrorCode.getErrorMsg(code);
            if (AiImageLiblibBizErrorCode.ERROR_CODE_429.getCode().equals(code)||
                    AiImageLiblibBizErrorCode.ERROR_CODE_100054.getCode().equals(code)) {
                result.setBizExecStatus(AiTaskBizExecStatus.PROCESSING);
            }
            else {
                result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
            }
            result.setErrorMsg(errorMsg);
            log.error("Liblib生成超QPS限制或并发限制，尝试重新提交:{}-{}", code, errorMsg);
            return result;
        }

        log.info("Liblib 生图，状态查询response:{}", responseData);
        JSONObject resultData = responseData.getData();
        if (resultData.getInt("generateStatus") < 5) {
            log.info("Liblib 生图，生成中...");
            result.setBizExecStatus(AiTaskBizExecStatus.PROCESSING);
        } else if (resultData.getInt("generateStatus") == 5) {

            JSONArray images = resultData.getJSONArray("images");
            //获取生成作品临时链接
            List<OrderedAsset> assetList = new ArrayList<>();
            for (int i = 0; i < images.length(); i++) {
                OrderedAsset orderedAsset = new OrderedAsset();
                orderedAsset.setId(IdHelper.getStrId());
                orderedAsset.setOrder(i);
                orderedAsset.setUrl(images.getJSONObject(i).getString("imageUrl"));
                assetList.add(orderedAsset);
            }
            result.setAssets(assetList);
            result.setBizExecStatus(AiTaskBizExecStatus.SUCCEED);
            log.info("Liblib 生图，生成成功response:{}", responseData);
            return result;
        } else if (KlingTaskStatus.submitted.name().equals(resultData.getString("task_status"))) {
            log.info("Liblib 生图，生成中...");
            result.setBizExecStatus(AiTaskBizExecStatus.PROCESSING);
        } else if (KlingTaskStatus.failed.name().equals(resultData.getString("task_status"))) {
            String code = String.valueOf(responseData.getCode());
            String errorMsg = AiImageLiblibBizErrorCode.getErrorMsg(code);

            if (AiImageLiblibBizErrorCode.ERROR_CODE_429.getCode().equals(code)) {
                result.setBizExecStatus(AiTaskBizExecStatus.PROCESSING);
                result.setErrorMsg(errorMsg);
                log.error("Liblib提交超QPS限制，尝试重新提交");
            }else {
                result.setErrorMsg(errorMsg);
                result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                result.setData(JSON.toJSONObject(responseData));
                log.error("Liblib生图，提交失败response:{},错误原因可能为:{}", responseData, errorMsg);
            }
        }
        else {

            //Lora生图API特殊错误处
            if (responseData.getData().has("generateMsg")) {
                if ("[-1]执行异常".equals(responseData.getData().getString(("generateMsg")))) {
                    result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                    result.setErrorMsg(KlingBizErrorCode.getErrorMsg(AiImageLiblibBizErrorCode.ERROR_CODE_1.getMsg()));
                    log.error("可灵生图，生成失败response:{}", responseData);
                    return result;
                }
            }

            String code = String.valueOf(responseData.getCode());
            String errorMsg = AiImageLiblibBizErrorCode.getErrorMsg(code);

            if (AiImageLiblibBizErrorCode.ERROR_CODE_429.getCode().equals(code)) {
                result.setBizExecStatus(AiTaskBizExecStatus.PROCESSING);
                result.setErrorMsg(errorMsg);
                log.error("Liblib提交超QPS限制，尝试重新提交");
            }else {
                result.setErrorMsg(errorMsg);
                result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                result.setData(JSON.toJSONObject(responseData));
                log.error("Liblib生图，提交失败response:{},错误原因可能为:{}", responseData, errorMsg);
            }
        }
        return result;
    }
}
