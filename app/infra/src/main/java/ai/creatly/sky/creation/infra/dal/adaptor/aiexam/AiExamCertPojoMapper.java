/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.aiexam;

import ai.creatly.sky.creation.domain.core.ai.exam.model.AiExamCert;
import ai.creatly.sky.creation.infra.dal.common.ModelMapper;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseCertificate;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseCertificateRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 *
 * <AUTHOR>
 * @version AiExamCertPojoMapper.java, v0.1 2025-02-20 17:49
 */
@Mapper(config = BaseMapperConfig.class)
public interface AiExamCertPojoMapper extends ModelMapper<AiExamCert, CourseCertificate, CourseCertificateRecord> {

    @Mapping(target = "showUrl", ignore = true)
    @Override
    AiExamCert toModel(CourseCertificate courseCertificate);

    @Override
    CourseCertificate toEntity(AiExamCert aiExamCert);
}
