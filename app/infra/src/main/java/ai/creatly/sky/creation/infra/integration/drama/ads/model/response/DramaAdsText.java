/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.drama.ads.model.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 剧情广告文本
 *
 * <AUTHOR>
 * @version DramaAdsText.java, 2024-10-22 上午10:41 zhoudong
 */
@Data
@Accessors(chain = true)
public class DramaAdsText {

    private String  text;
    /**
     * 单位：毫秒
     */
    private Integer start;
    /**
     * 单位：毫秒
     */
    private Integer end;
}
