/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.model.volcengine.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @version VolceAiMusicResponse.java, v0.1 2025-02-28 12:08
 */
@Data
public class VolcAiMusicResponse {

    private Integer Code;

    private String Message;

    private com.alibaba.fastjson.JSONObject Result;

    private com.alibaba.fastjson.JSONObject  ResponseMetadata;
    public boolean isSuccess() {
        return getCode()==0;
    }
}
