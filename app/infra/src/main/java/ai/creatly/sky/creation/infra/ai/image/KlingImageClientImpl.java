/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.image;

import ai.creatly.sky.creation.domain.core.ai.audio.model.KlingBizErrorCode;
import ai.creatly.sky.creation.domain.core.ai.image.AiImageGenerateClient;
import ai.creatly.sky.creation.domain.core.ai.image.model.AiImaggeTaskVars;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskBizType;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskCommonInput;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskCommonResult;
import ai.creatly.sky.creation.domain.core.ai.model.OrderedAsset;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskBizExecStatus;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.infra.ai.model.kling.api.KlingApi;
import ai.creatly.sky.creation.infra.ai.model.kling.model.KlingRequest;
import ai.creatly.sky.creation.infra.ai.model.kling.model.KlingResponse;
import ai.creatly.sky.creation.infra.ai.model.kling.model.KlingTaskStatus;
import ai.creatly.sky.creation.infra.dal.adaptor.ai.AiToolsRepositoryImpl;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version KlingClientImpl.java, v0.1 2025-02-28 11:49
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class KlingImageClientImpl implements AiImageGenerateClient {

    private final KlingApi              klingApi;
    private final AiToolsRepositoryImpl aiToolsRepository;

    @Override
    public AiTaskCommonResult generate(AiTaskCommonInput input, UserContext userContext) {

        KlingRequest request = new KlingRequest();
        request.setModelName("kling-v2");
        request.setAspectRatio("1:1");
        switch(input.getRequest().getAspectRatio()){
            case _1_1 ->  request.setAspectRatio("1:1");
            case _9_16 -> request.setAspectRatio("9:16");
            case _16_9 -> request.setAspectRatio("16:9");
            case _4_3 -> request.setAspectRatio("4:3");
            case _3_4 -> request.setAspectRatio("3:4");
            case _3_2 -> request.setAspectRatio("3:2");
            case _2_3 -> request.setAspectRatio("2:3");
        }
        //提示词
        if (StringUtils.isNotEmpty(input.getRequest().getPromptText())) {
            request.setPrompt(input.getRequest().getPromptText());
        }

        //参考生图
        if (StringUtils.isNotEmpty(input.getRequest().getImageReference())) {
            request.setImageReference(input.getRequest().getImageReference());
            request.setImage(input.getRequest().getImageFileRefs().getUrl());
            request.setImageFidelity(input.getRequest().getImageFidelity() == null ? 0.99f : input.getRequest().getImageFidelity());
        }

        request.setN(input.getRequest().getImgCount());

        AiTaskCommonResult result = new AiTaskCommonResult();
        log.info("可灵生图，任务提交request:{}", JSON.toJSONString(request));

        KlingResponse<JSONObject> responseData;
        try {
            responseData = klingApi.generateImage(request);

            log.info("可灵生图，任务提交response:{}", responseData);
            JSONObject resultData = responseData.getData();
            result.setData(resultData);
            if (KlingTaskStatus.submitted.name().equals(resultData.getString("task_status"))) {
                result.setTaskId(resultData.getString("task_id"));
                return result;
            } else if (KlingTaskStatus.failed.name().equals(resultData.getString("task_status"))) {

                //敏感词先行处理
                if (responseData.getData().has("task_status_msg")&&
                        KlingBizErrorCode.ERROR_CODE_10.getMsg().equals(responseData.getData().getString("task_status_msg"))){
                    result.setErrorMsg(KlingBizErrorCode.getErrorMsg(KlingBizErrorCode.ERROR_CODE_11.getCode()));
                    result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                    log.error("可灵生图，提交失败response:{},错误原因为检测到敏感信息", responseData);
                    return result;
                }

                String errorMsg = KlingBizErrorCode.getErrorMsg(String.valueOf(responseData.getCode()));
                result.setErrorMsg(errorMsg);
                result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                log.error("可灵生图，提交失败response:{},错误原因可能为:{}", responseData, errorMsg);
                return result;
            } else if (KlingTaskStatus.processing.name().equals(resultData.getString("task_status"))) {
                result.setTaskId(resultData.getString("task_id"));
                return result;
            }
        } catch (Exception e) {
            log.error("可灵生图异常", e);
            result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
            return result;
        }
        return result;
    }

    @Override
    public CreditsExpense expense(AiTask aiTask) {
        var input = aiTask.parseBizInput(AiTaskCommonInput.class);
        return CreditsExpense.builder()
                .uid(aiTask.getOwnerId())
                .type(CreditAccountType.GENERAL)
                .amount(aiToolsRepository.getBaseCredits("kling_image_v1.5", AiTaskBizType.image_kling_generate.name(), null) * input.getRequest().getImgCount())
                // 完成一条创作任务，扣除一次费用
                .bizType(CreditLogBizType.GEN_IMAGE)
                .bizNo(aiTask.getId().toString())
                .build();
    }

    @Override
    public AiTaskCommonResult queryGeneration(AiTask aiTask, UserContext userContext) {
        var taskVars = aiTask.parseBizVars(AiImaggeTaskVars.class);
        return this.queryGeneration(taskVars.getTaskId(),userContext);
    }

    @Override
    public AiTaskCommonResult queryGeneration(String taskId, UserContext userContext) {
        AiTaskCommonResult result = new AiTaskCommonResult();
        KlingResponse<JSONObject> responseData = klingApi.queryImageGeneration(taskId);
        log.info("可灵生图，状态查询response:{}", responseData);
        JSONObject resultData = responseData.getData();
        if (KlingTaskStatus.processing.name().equals(resultData.getString("task_status"))) {
            log.info("可灵生图，生成中...");
            result.setBizExecStatus(AiTaskBizExecStatus.PROCESSING);
        } else if (KlingTaskStatus.succeed.name().equals(resultData.getString("task_status"))) {

            JSONObject taskResult = resultData.getJSONObject("task_result");
            JSONArray images = taskResult.getJSONArray("images");
            //获取生成作品临时链接
            List<OrderedAsset> assetList = new ArrayList<>();
            for (int i = 0; i < images.length(); i++) {
                OrderedAsset orderedAsset = new OrderedAsset();
                orderedAsset.setId(IdHelper.getStrId());
                orderedAsset.setOrder(images.getJSONObject(i).getInt("index"));
                orderedAsset.setUrl(images.getJSONObject(i).getString("url"));
                assetList.add(orderedAsset);
            }

            result.setAssets(assetList);
            result.setBizExecStatus(AiTaskBizExecStatus.SUCCEED);
            log.info("可灵生图，生成成功response:{}", responseData);
            return result;
        } else if (KlingTaskStatus.submitted.name().equals(resultData.getString("task_status"))) {
            log.info("可灵生图，生成中...");
            result.setBizExecStatus(AiTaskBizExecStatus.PROCESSING);
        } else if (KlingTaskStatus.failed.name().equals(resultData.getString("task_status"))) {

            //敏感词先行处理
            if (responseData.getData().has("task_status_msg")&&
                    KlingBizErrorCode.ERROR_CODE_10.getMsg().equals(responseData.getData().getString("task_status_msg"))){
                result.setErrorMsg(KlingBizErrorCode.getErrorMsg(KlingBizErrorCode.ERROR_CODE_11.getCode()));
                result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                log.error("可灵生图，生成失败response:{},错误原因为检测到敏感信息", responseData);
                return result;
            }

            String code = String.valueOf(responseData.getCode());
            String errorMsg = KlingBizErrorCode.getErrorMsg(code);
            result.setErrorMsg(errorMsg);
            result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
            log.error("可灵生图，生成失败response:{},错误原因可能为:{}", responseData, errorMsg);
        }else {

            //敏感词先行处理
            if (responseData.getData().has("task_status_msg")&&
                    KlingBizErrorCode.ERROR_CODE_10.getMsg().equals(responseData.getData().getString("task_status_msg"))){
                result.setErrorMsg(KlingBizErrorCode.getErrorMsg(KlingBizErrorCode.ERROR_CODE_11.getCode()));
                result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                log.error("可灵生图，生成失败response:{},错误原因为检测到敏感信息", responseData);
                return result;
            }

            String code = String.valueOf(responseData.getCode());
            String errorMsg = KlingBizErrorCode.getErrorMsg(code);
            result.setErrorMsg(errorMsg);
            result.setBizExecStatus(AiTaskBizExecStatus.FAILED);
            log.error("可灵生图，生成失败response:{},错误原因可能为:{}", responseData, errorMsg);
        }
        return result;
    }
}
