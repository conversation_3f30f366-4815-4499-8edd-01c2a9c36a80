/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.digitalhuman;

import ai.creatly.sky.creation.domain.common.env.RuntimeEnv;
import ai.creatly.sky.creation.domain.common.integration.videorender.model.VideoRenderRequest;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.digitalhuman.client.DigitalHumanClient;
import ai.creatly.sky.creation.domain.core.digitalhuman.client.model.DigitalHumanOutFile;
import ai.creatly.sky.creation.domain.core.digitalhuman.error.DigHumanErrorCode;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.DigitalHumanVideo;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.task.input.DigHumanVideoParam;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.task.input.VideoAvatarLayer;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.task.process.DHumanVideoTaskExecInfo;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.task.process.VideoSubtitleContent;
import ai.creatly.sky.creation.domain.core.userfile.model.BizFile;
import ai.creatly.sky.creation.domain.core.userfile.model.FileRef;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.videoproject.model.request.VideoSubtitleLayerDTO;
import ai.creatly.sky.creation.infra.deprecated.alphavideo.api.DigitalHumanAlphaApi;
import ai.creatly.sky.creation.infra.deprecated.alphavideo.model.VideoAlphaRequest;
import ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.api.DigitalHumanApi;
import ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.model.request.AvatarVideoAddRequest;
import ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.model.request.VideoGetRequest;
import ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.model.response.VideoAddResponse;
import ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.model.response.VideoGetResponse;
import ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.model.response.YuanZResponse;
import ai.creatly.sky.creation.infra.integration.videorender.api.VideoRenderApi;
import ai.creatly.sky.creation.infra.integration.videorender.model.VideoRenderResponse;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.json.JSON;
import com.jspeeder.core.util.time.Times;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.jooq.tools.StringUtils;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version DigitalHumanClientImpl.java, v 0.1 2024-05-16 16:51 syoka
 */
@Deprecated
@Component
@RequiredArgsConstructor
@Slf4j
public class DigitalHumanClientImpl implements DigitalHumanClient {

    private static final Integer SUCCESS            = 200;

    private final DigitalHumanApi      digitalHumanApi;
    private final DigitalHumanAlphaApi digitalAlphaApi;
    private final VideoRenderApi       videoRenderApi;
    private final UserFileHelper       userFileHelper;
    private final UserFileRepository   userFileRepository;
    private final RuntimeEnv           runtimeEnv;

    @Override
    public String addAvatarVideo(DigitalHumanVideo video) {
        String audioUrl = userFileHelper.getHttpUrl(video.getAudioFile().getFileUrl(), video.getUid());
        AvatarVideoAddRequest request = new AvatarVideoAddRequest()
                .setAvatarId(video.getExtOutAvatarId())
                .setName(video.getName())
                .setPriority(3)
                .setAudio(audioUrl)
                .setSize(video.getSize())
                // 0：正式请求 1：测试请求
                .setIsTest(runtimeEnv.isOffline() ? 1 : 0)
                // 合成不带音轨的视频
                .setMute(1);
        YuanZResponse<VideoAddResponse> response;
        try {
            response = digitalHumanApi.addVideoPro(request);
        } catch (Exception e) {
            throw new SysException(DigHumanErrorCode.DH_VIDEO_GEN_FAILURE, e);
        }
        if (!SUCCESS.equals(response.getCode())) {
            throw new SysException(DigHumanErrorCode.DH_VIDEO_GEN_FAILURE);
        }
        return response.getData().getVideoId();
    }

    @Override
    public DigitalHumanOutFile getAvatarVideoResult(String outExtId) {
        VideoGetRequest request = new VideoGetRequest().setVideoId(outExtId);
        YuanZResponse<VideoGetResponse> response;
        try {
            response = digitalHumanApi.getVideoStatus(request);
        } catch (Exception e) {
            throw new SysException(DigHumanErrorCode.DH_UNKNOWN_EXCEPTION, e);
        }
        return this.handleCommonResponse(response, (res) -> {
            String status = res.getStatus();
            // 如果视频处理成功或者处理失败，最终状态
            if (StringUtils.equals(status, VideoGetResponse.VideoStatus.PROCESS_SUCCESS)) {
                return new DigitalHumanOutFile()
                        .setStatus(DigitalHumanVideo.SUCCESS)
                        .setVideoUrl(res.getVideoUrl())
                        .setMaskUrl(res.getMaskUrl());
            }
            if (StringUtils.equals(status, VideoGetResponse.VideoStatus.PROCESS_FAILED)) {
                return new DigitalHumanOutFile()
                        .setStatus(DigitalHumanVideo.FAILURE)
                        .setVideoUrl(res.getVideoUrl())
                        .setMaskUrl(res.getMaskUrl());
            }
            return new DigitalHumanOutFile()
                    .setStatus(DigitalHumanVideo.WAITING)
                    .setVideoUrl(res.getVideoUrl())
                    .setMaskUrl(res.getMaskUrl());
        });
    }

    @Override
    public void mergeAlphaVideo(DigitalHumanVideo video) {
        Asserts.notNull(video.getGreenVideoFile(), "will not happen");
        Asserts.notNull(video.getMaskVideoFile(), "will not happen");
        VideoAlphaRequest request = this.buildVideoAlphaRequest(video);
        YuanZResponse<Void> response;
        try {
            response = digitalAlphaApi.mergeAlphaVideo(request);
        } catch (Exception e) {
            log.error("[mergeAlphaVideo]request:{}", request, e);
            throw new SysException(DigHumanErrorCode.DH_UNKNOWN_EXCEPTION, e);
        }
        this.handleCommonResponse(response);
    }

    private VideoAlphaRequest buildVideoAlphaRequest(DigitalHumanVideo video) {
        String bucket = userFileHelper.resolveFileBucket(video.getAudioFile().getFileUrl());
        VideoAlphaRequest request = new VideoAlphaRequest().setBucket(bucket);
        if (video.getGreenVideoFile() != null) {
            request.setGreenVideoObjKey(userFileHelper.resolveFileKey(video.getGreenVideoFile().getFileUrl()));
        }
        if (video.getMaskVideoFile() != null) {
            request.setGreenMaskObjKey(userFileHelper.resolveFileKey(video.getMaskVideoFile().getFileUrl()));
        }
        if (video.getAlphaVideoFile() != null) {
            request.setAlphaVideoObjKey(userFileHelper.resolveFileKey(video.getAlphaVideoFile().getFileUrl()));
        }
        if (video.getBgFile() != null) {
            request.setBackgroundObjKey(userFileHelper.resolveFileKey(video.getBgFile().getFileUrl()));
        }
        request.setAudioObjKey(userFileHelper.resolveFileKey(video.getAudioFile().getFileUrl()));
        log.info("[alpha video params]:{}", JSON.toJSONString(request));
        return request;
    }

    @Override
    public void renderVideo(DigitalHumanVideo video, AiTask aiTask) {
        VideoRenderRequest request = this.toVideoRenderRequest(video, aiTask);
        log.info("[render video]taskId:{},params:{}", aiTask.getId(), request);
        VideoRenderResponse<?> response;
        try {
            response = videoRenderApi.renderVideo(request);
        } catch (Exception e) {
            log.error("[renderVideo]taskId:{},params:{}", aiTask.getId(), request, e);
            throw new SysException(CommonErrorCode.REMOTE_API_INVOKE_ERROR, "视频编辑器渲染", e);
        }
        if (!SUCCESS.equals(response.getCode())) {
            log.error("[renderVideo] response:{},reason:{}", response, response.getMsg());
            throw new SysException(CommonErrorCode.REMOTE_API_INVOKE_ERROR, "视频编辑器渲染");
        }
    }

    private static final String RES_PREFIX = "res/";

    private VideoRenderRequest toVideoRenderRequest(DigitalHumanVideo video, AiTask aiTask) {
        // 读取视频时长
        UserFile audioFile = userFileRepository.queryById(Objects.requireNonNull(video.getAudioFile()).getFileId());
        Duration duration = Objects.requireNonNull(audioFile.getDuration());

        DigHumanVideoParam bizParams = aiTask.parseBizParams(DigHumanVideoParam.class);
        DHumanVideoTaskExecInfo bizExecInfo = aiTask.parseBizExecInfo(DHumanVideoTaskExecInfo.class);
        VideoRenderRequest.VideoRenderData videoRenderData = new VideoRenderRequest.VideoRenderData()
                // 添加背景层
                .setBackground(this.toBackground(bizParams.getBackground()))
                // 添加配音轨（可能没有）
                .setAudios(this.toAudios(bizExecInfo.getAudio(), bizParams.getAudio(), duration))
                // 添加数字人视频轨（透明）
                .setAvatar(this.toAvatar(bizParams.getAvatar(), video))
                // 添加字幕层
                .setSubtitle(this.toSubTitle(bizParams.getSubtitle(), bizExecInfo.getSubtitleContents()));

        return new VideoRenderRequest()
                .setId(String.valueOf(aiTask.getId()))
                .setHeight(bizParams.getHeight())
                .setWidth(bizParams.getWidth())
                .setDuration(Times.toSeconds(duration))
                .setOssBucket(userFileHelper.resolveFileBucket(Objects.requireNonNull(video.getVideoFile()).getFileUrl()))
                .setOssKey(userFileHelper.resolveFileKey(video.getVideoFile().getFileUrl()))
                .setData(videoRenderData);
    }

    private VideoRenderRequest.VideoRenderData.AvatarVideo toAvatar(VideoAvatarLayer avatar, DigitalHumanVideo video) {
        BizFile alphaVideoFile = Objects.requireNonNull(video.getAlphaVideoFile());
        return new VideoRenderRequest.VideoRenderData.AvatarVideo()
                .setTop(avatar.getTop())
                .setLeft(avatar.getLeft())
                .setRotation(avatar.getRotation())
                .setHeight(avatar.getHeight())
                .setWidth(avatar.getWidth())
                .setAssetId(String.valueOf(avatar.getAssetId()))
                // 本地文件路径
                .setAssetUrl(RES_PREFIX + userFileHelper.resolveFileKey(alphaVideoFile.getFileUrl()));
    }

    @Nullable
    private VideoRenderRequest.VideoRenderData.Background toBackground(@Nullable FileRef background) {
        if (Objects.isNull(background)) {
            return null;
        }
        return new VideoRenderRequest.VideoRenderData.Background()
                // 本地路径
                .setAssetUrl(RES_PREFIX + userFileHelper.resolveFileKey(background.getUrl()));
    }

    @Nullable
    private List<VideoRenderRequest.VideoRenderData.Audio> toAudios(@Nullable FileRef sliceAudio, @Nullable FileRef audioFile,
                                                                    Duration duration) {
        if (sliceAudio != null) {
            // 如果音频是被切片过的，则不用合成进去（注意：这是隐性逻辑）
            return null;
        }
        if (audioFile == null) {
            return null;
        }
        var audio = new VideoRenderRequest.VideoRenderData.Audio()
                // 本地路径
                .setAssetUrl(RES_PREFIX + userFileHelper.resolveFileKey(audioFile.getUrl()))
                .setStartTime(Times.toSeconds(Duration.ZERO))
                .setEndTime(Times.toSeconds(duration));
        return List.of(audio);
    }

    @Nullable
    private VideoRenderRequest.VideoRenderData.Subtitle toSubTitle(@Nullable VideoSubtitleLayerDTO subtitleRequest,
                                                                   @Nullable List<VideoSubtitleContent> subtitleContents) {
        if (subtitleRequest == null || subtitleContents == null) {
            return null;
        }
        return new VideoRenderRequest.VideoRenderData.Subtitle()
                .setStyle(this.toStyle(subtitleRequest))
                .setContent(this.toContent(subtitleContents));
    }

    private VideoRenderRequest.VideoRenderData.Subtitle.Style toStyle(VideoSubtitleLayerDTO subtitleRequest) {
        return new VideoRenderRequest.VideoRenderData.Subtitle.Style()
                .setFont(this.toFont(subtitleRequest))
                .setPosition(this.toPosition(subtitleRequest))
                .setStroke(this.toStroke(subtitleRequest))
                .setColor(subtitleRequest.getColor())
                .setEffect(Objects.isNull(subtitleRequest.getEffect()) ? "none" : subtitleRequest.getEffect());
    }

    private List<VideoRenderRequest.VideoRenderData.Subtitle.SubtitleContent> toContent(List<VideoSubtitleContent> subtitleContents) {
        return subtitleContents.stream()
                .map(content -> new VideoRenderRequest.VideoRenderData.Subtitle.SubtitleContent()
                        .setText(content.getText())
                        .setStartTime(Times.toSeconds(content.getStart()))
                        .setEndTime(Times.toSeconds(content.getEnd()))
                )
                .collect(Collectors.toList());
    }

    private VideoRenderRequest.VideoRenderData.Subtitle.Style.Font toFont(VideoSubtitleLayerDTO subTitle) {
        return new VideoRenderRequest.VideoRenderData.Subtitle.Style.Font()
                .setFamily(subTitle.getFontFamily())
                .setSize(subTitle.getFontSize());
    }

    private VideoRenderRequest.VideoRenderData.Subtitle.Style.Stroke toStroke(VideoSubtitleLayerDTO subTitle) {
        return new VideoRenderRequest.VideoRenderData.Subtitle.Style.Stroke()
                .setColor(subTitle.getStrokeColor())
                .setWidth(subTitle.getStrokeWidth());
    }

    private VideoRenderRequest.VideoRenderData.Subtitle.Style.Position toPosition(VideoSubtitleLayerDTO subTitle) {
        return new VideoRenderRequest.VideoRenderData.Subtitle.Style.Position()
                .setTop(subTitle.getTop())
                .setAlign(subTitle.getAlign());
    }

    @Override
    public void clearRenderEffect(DigitalHumanVideo video) {
        // 如果绿幕视频都没有生成，就不需要进行清理了
        if (Objects.isNull(video.getGreenVideoFile())) {
            return;
        }
        VideoAlphaRequest request = this.buildVideoAlphaRequest(video);
        YuanZResponse<Void> response;
        try {
            response = digitalAlphaApi.clearRenderEffect(request);
        } catch (Exception e) {
            throw new SysException(DigHumanErrorCode.DH_UNKNOWN_EXCEPTION, e);
        }
        this.handleCommonResponse(response);
    }

    private <T> void handleCommonResponse(YuanZResponse<T> response) {
        this.handleCommonResponse(response, Function.identity());
    }

    private <T, E> E handleCommonResponse(YuanZResponse<T> response, Function<T, E> function) {
        if (!SUCCESS.equals(response.getCode())) {
            log.error("[invoke error] response:{},reason:{}", JSON.toJSONString(response), response.getMsg());
            throw new SysException(DigHumanErrorCode.DH_UNKNOWN_EXCEPTION);
        }
        T data = response.getData();
        return function.apply(data);
    }

}
