/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.file;

import ai.creatly.sky.creation.domain.core.userfile.model.FileContent;
import ai.creatly.sky.creation.domain.core.userfile.model.FileMetadata;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileStorageType;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserFile;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.mapper.EnumValueMapping;
import com.jspeeder.core.data.repository.PojoMapper;
import com.jspeeder.core.model.OperatorRef;
import com.jspeeder.core.util.json.JSON;
import org.jooq.JSONB;
import org.mapstruct.Mapper;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @version UserFilePojoMapper.java, v 0.1 2023-06-24 23:19 joton
 */
@Mapper(config = BaseMapperConfig.class)
public interface UserFilePojoMapper extends PojoMapper<UserFile, CreationUserFile> {

    @Override
    CreationUserFile toEntity(UserFile userFile);

    @Override
    UserFile toModel(CreationUserFile creationUserFile);

    @EnumValueMapping
    FileStorageType parseFileStorageType(String value);

    default FileBizSource parseBizSource(String bizSource) {
        if (bizSource == null) {
            return null;
        }
        return FileBizSource.getByCode(bizSource);
    }

    default String toBizSource(FileBizSource bizSource) {
        if (bizSource == null) {
            return null;
        }
        return bizSource.getCode();
    }

    default JSONB contentToJSONB(FileContent content) {
        if (content == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(content));
    }

    default FileContent toContent(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseObject(jsonb.data(), FileContent.class);
    }

    default JSONB creatorToJSONB(FileMetadata metadata) {
        if (metadata == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(metadata));
    }

    default FileMetadata toMetadata(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseObject(jsonb.data(), FileMetadata.class);
    }

    default JSONB creatorToJSONB(OperatorRef creator) {
        if (creator == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(creator));
    }

    default OperatorRef toCreator(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseObject(jsonb.data(), OperatorRef.class);
    }

    default Integer toDateNumber(LocalDate date) {
        if (date == null) {
            return null;
        }
        return Integer.parseInt(date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }

    default Integer toMonthNumber(YearMonth month) {
        if (month == null) {
            return null;
        }
        return Integer.parseInt(month.format(DateTimeFormatter.ofPattern("yyyyMM")));
    }

    default LocalDate parseDate(Integer date) {
        if (date == null) {
            return null;
        }
        return LocalDate.parse(date.toString(), DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    default YearMonth parseMonth(Integer month) {
        if (month == null) {
            return null;
        }
        return YearMonth.parse(month.toString(), DateTimeFormatter.ofPattern("yyyyMM"));
    }
}
