/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.ai;

import ai.creatly.sky.creation.domain.core.ai.tool.AiRecharge;
import ai.creatly.sky.creation.infra.dal.common.ModelMapper;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiRecharge;
import ai.creatly.sky.creation.infra.dal.tables.records.AiRechargeRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

/**
 *
 * <AUTHOR>
 * @version AiRechargePojoMapper.java, v0.1 2025-02-20 17:49
 */
@Mapper(config = BaseMapperConfig.class)
public interface AiRechargePojoMapper extends ModelMapper<AiRecharge, CreationAiRecharge, AiRechargeRecord> {

    @Override
    AiRecharge toModel(CreationAiRecharge creationAiRecharge);

    @Override
    CreationAiRecharge toEntity(AiRecharge aiRecharge);
}
