/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.aivideo;

import ai.creatly.sky.creation.domain.core.aivideo.model.AiVideo;
import ai.creatly.sky.creation.infra.dal.common.ModelMapper;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiVideo;
import ai.creatly.sky.creation.infra.dal.tables.records.AiVideoRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 *
 * <AUTHOR>
 * @version AiVideoPojoMapper.java, v0.1 2025-02-20 17:49
 */
@Mapper(config = BaseMapperConfig.class)
public interface AiVideoPojoMapper extends ModelMapper<AiVideo, CreationAiVideo, AiVideoRecord> {

    @Override
    @Mapping(target = "fileHttpUrl", ignore = true)
    @Mapping(target = "coverHttpUrl", ignore = true)
    @Mapping(target = "avatarHttpUrl", ignore = true)
    AiVideo toModel(CreationAiVideo creationAiVideo);

    @Override
    CreationAiVideo toEntity(AiVideo aiVideo);
}
