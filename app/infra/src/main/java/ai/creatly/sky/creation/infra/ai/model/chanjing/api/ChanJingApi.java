package ai.creatly.sky.creation.infra.ai.model.chanjing.api;

import ai.creatly.sky.creation.infra.ai.model.chanjing.model.*;
import com.alibaba.fastjson.JSONObject;
import feign.Headers;
import feign.Param;
import feign.RequestLine;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface ChanJingApi {

    //生成数字人视频提交
    @RequestLine("POST /open/v1/create_video")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json",
    })
    ChanJingResponse<String> generateVideo(@RequestBody ChanJingVideoRequest request);

    //创建定制数字人提交
    @RequestLine("POST /open/v1/create_customised_person")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json",
    })
    ChanJingResponse<String> createCcustomisedPerson(@RequestBody ChanJingCustomisedRequest request);

    //查询单一视频细节
    @RequestLine("GET /open/v1/video?id={id}")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json",
    })
    ChanJingResponse<JSONObject> queryVideoDetail(@Param("id") String id);

    //拉取字体列表
    @RequestLine("GET /open/v1/font_list")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json",
    })
    ChanJingResponse<JSONObject> queryFontList();

    // 拉取公共数字人列表
    @RequestLine("GET /open/v1/list_common_dp?page={page}&size={size}")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json",
    })
    ChanJingResponse<JSONObject> queryCommonDpList(@Param("page") Integer page, @Param("size") Integer pageSize);




}
