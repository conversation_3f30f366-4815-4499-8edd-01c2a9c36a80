/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.deprecated.material;

import ai.creatly.sky.creation.domain.deprecated.content.model.Hots;
import ai.creatly.sky.creation.domain.deprecated.material.model.AttachedResource;
import ai.creatly.sky.creation.domain.deprecated.material.model.Material;
import ai.creatly.sky.creation.domain.deprecated.material.model.MaterialBody;
import ai.creatly.sky.creation.domain.deprecated.material.model.MaterialType;
import ai.creatly.sky.creation.domain.support.label.model.LabelRef;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationMaterial;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.repository.PojoMapper;
import com.jspeeder.core.model.OperatorRef;
import com.jspeeder.core.util.json.JSON;
import org.jooq.JSONB;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(config = BaseMapperConfig.class)
public interface CreationMaterialMapper extends PojoMapper<Material, CreationMaterial> {

    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "creatorType", source = "creator.type")
    @Mapping(target = "creatorId", source = "creator.id")
    @Mapping(target = "creatorName", source = "creator.name")
    @Mapping(target = "fileId", source = "body.fileId")
    @Mapping(target = "fileUrl", source = "body.fileUrl")
    @Mapping(target = "text", source = "body.text")
    @Mapping(target = "likes", source = "hots.likes")
    @Mapping(target = "favorites", source = "hots.favorites")
    @Mapping(target = "views", source = "hots.views")
    @Override
    CreationMaterial toEntity(Material material);

    @Mapping(target = "creator", source = "creationMaterial")
    @Mapping(target = "body", source = "creationMaterial")
    @Mapping(target = "hots", source = "creationMaterial")
    @Override
    Material toModel(CreationMaterial creationMaterial);

    @Mapping(target = "type", source = "creatorType")
    @Mapping(target = "id", source = "creatorId")
    @Mapping(target = "name", source = "creatorName")
    OperatorRef toCreator(CreationMaterial creationMaterial);

    MaterialBody toMaterialBody(CreationMaterial creationMaterial);

    @Mapping(target = "hots", ignore = true)
    @Mapping(target = "shares", ignore = true)
    Hots toHots(CreationMaterial creationMaterial);

    default List<AttachedResource> parseAttachedResources(JSONB[] attachedResources) {
        if (attachedResources == null) {
            return null;
        }
        return Arrays.stream(attachedResources)
                .map(jsonb -> JSON.parseObject(jsonb.data(), AttachedResource.class))
                .collect(Collectors.toList());
    }

    default JSONB[] attachedResourcesToJSONB(List<AttachedResource> attachedResources) {
        if (attachedResources == null) {
            return null;
        }
        return attachedResources.stream()
                .map(attachedResource -> JSONB.valueOf(JSON.toJSONString(attachedResource)))
                .toArray(JSONB[]::new);
    }

    default List<LabelRef> parseLabels(JSONB[] labels) {
        if (labels == null) {
            return null;
        }
        return Arrays.stream(labels)
                .map(jsonb -> JSON.parseObject(jsonb.data(), LabelRef.class))
                .collect(Collectors.toList());
    }

    default JSONB[] labelsToJSONB(List<LabelRef> labels) {
        if (labels == null) {
            return null;
        }
        return labels.stream()
                .map(labelRef -> JSONB.valueOf(JSON.toJSONString(labelRef)))
                .toArray(JSONB[]::new);
    }

    default MaterialType parseMaterialType(String code) {
        if (code == null) {
            return null;
        }
        return MaterialType.of(code);
    }

    default String toCode(MaterialType type) {
        if (type == null) {
            return null;
        }
        return type.getCode();
    }
}
