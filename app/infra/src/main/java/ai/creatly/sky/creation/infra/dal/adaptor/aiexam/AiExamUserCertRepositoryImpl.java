/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.aiexam;

import ai.creatly.sky.creation.domain.core.ai.exam.model.AiUserExamCert;
import ai.creatly.sky.creation.domain.core.ai.exam.repository.AiUserCertRepository;
import ai.creatly.sky.creation.infra.dal.tables.daos.CourseUserCertificateDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseUserCertificate;
import com.jspeeder.core.data.id.IdHelper;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version AiExamCertRepositoryImpl.java, v0.1 2025-02-20 09:44
 */
@RequiredArgsConstructor
@Repository
public class AiExamUserCertRepositoryImpl implements AiUserCertRepository {

    private final DSLContext         dsl;
    private final AiExamUserCertPojoMapper aiExamCertPojoMapper;
    private final CourseUserCertificateDAO userCertificateDAO;



    @Override
    public AiUserExamCert getCertById(Long certId, Long uid) {

        List<CourseUserCertificate> list = userCertificateDAO.fetchByUid(uid);
        for (CourseUserCertificate cert: list) {
            if(Objects.equals(cert.getCertId(), certId)){
                return aiExamCertPojoMapper.toModel(cert);
            }
        }
        return null;
    }

    @Override
    public AiUserExamCert createCert(AiUserExamCert aiUserExamCert) {
        if (aiUserExamCert.getId() == null) {
            aiUserExamCert.setId(IdHelper.getId());
        }
        userCertificateDAO.insert(aiExamCertPojoMapper.toEntity(aiUserExamCert));
        return aiUserExamCert;
    }

    @Override
    public AiUserExamCert updateCertById(AiUserExamCert aiUserExamCert) {
        userCertificateDAO.update(aiExamCertPojoMapper.toEntity(aiUserExamCert));
        return aiUserExamCert;
    }
}
