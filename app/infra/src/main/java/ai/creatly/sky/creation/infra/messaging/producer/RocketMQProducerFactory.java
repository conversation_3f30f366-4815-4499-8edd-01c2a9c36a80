/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.messaging.producer;

import jakarta.validation.constraints.NotNull;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.TransactionMQProducer;

/**
 * 生产者实例工厂
 *
 * <AUTHOR>
 * @version RocketMQProducerFactory.java, v 0.1 2023-10-26 下午9:38 zhoudong
 */
public interface RocketMQProducerFactory {

    /**
     * 获取非事务消息的生产者实例（非事务消息的生产者实例，无需区分生产者分组，全局复用一个即可）
     *
     * @return 生产者实例
     */
    @NotNull
    DefaultMQProducer getProducer();

    /**
     * 获取事务消息的生产者实例（事务消息的生产者实例，需要区分生产者分组，Broker回查时需要定位）
     *
     * @param producerGroupId 生产者分组ID
     * @return 生产者实例
     */
    @NotNull
    TransactionMQProducer getTransactionProducer(String producerGroupId);
}
