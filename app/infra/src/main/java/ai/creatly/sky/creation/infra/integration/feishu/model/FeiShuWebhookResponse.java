/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.feishu.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version FeiShuWebhookResponse.java, v 0.1 2023-07-11 00:54
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FeiShuWebhookResponse extends CommonError {

    /**
     * 成功code
     */
    private String StatusCode;
    /**
     * 成功msg
     */
    private String StatusMessage;
    /**
     * 返回额外信息
     */
    private String Extra;
}
