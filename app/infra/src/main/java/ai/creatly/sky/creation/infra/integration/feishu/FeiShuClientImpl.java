/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.feishu;

import ai.creatly.sky.creation.domain.common.integration.feishu.FeiShuClient;
import ai.creatly.sky.creation.infra.integration.feishu.model.FeiShuWebhookRequest;
import ai.creatly.sky.creation.infra.integration.feishu.model.FeiShuWebhookResponse;
import ai.creatly.sky.creation.infra.integration.feishu.api.FeiShuClientApi;
import ai.creatly.sky.creation.infra.integration.feishu.config.FeiShuProperties;
import com.jspeeder.core.util.json.JSON;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Slf4j
@Component
public class FeiShuClientImpl implements FeiShuClient {

    private final FeiShuClientApi  feiShuClientApi;
    private final FeiShuProperties feiShuProperties;

    @Override
    public void sendMessageWithToken(String webhookToken, String template, Object... args) {
        this.sendMessage2Group(webhookToken, FormatUtil.format(template, args));
    }

    @Override
    public void sendMessage2Group(String content) {
        this.sendMessage2Group(feiShuProperties.getNoticeToken(), content);
    }

    private void sendMessage2Group(String webhookToken, String content) {
        try {
            FeiShuWebhookRequest.Content feishuContent = new FeiShuWebhookRequest.Content(content);
            FeiShuWebhookRequest request = new FeiShuWebhookRequest(feishuContent);
            log.info("飞书群消息发送请求：{}", JSON.toJSONString(request));
            FeiShuWebhookResponse response = feiShuClientApi.sendMessage(request, webhookToken);
            log.info("飞书群消息发送响应：{}", JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("[sendMessage2Group] failure", e);
        }
    }
}
