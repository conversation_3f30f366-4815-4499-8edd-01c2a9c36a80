/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.api;

import ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.model.request.AvatarProAddRequest;
import ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.model.request.AvatarSearchRequest;
import ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.model.request.AvatarVideoAddRequest;
import ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.model.request.VideoGetRequest;
import ai.creatly.sky.creation.infra.integration.avatar.yuanzhong.model.response.*;
import feign.Headers;
import feign.RequestLine;

/**
 * <a href="https://apifox.com/apidoc/shared-a31733e1-bf07-416f-8f1e-5f426b69bbc4/api-132911860">接入文档</a>
 *
 * <AUTHOR>
 * @version DigitalHumanApi.java, v 0.1 2024-05-16 16:29 syoka
 */
public interface DigitalHumanApi {

    /**
     * 创建形象Pro（异步非阻塞接口）
     *
     * @param request 参数
     * @return -
     */
    @RequestLine("POST /V1/AVATAR/addAvatarPro")
    YuanZResponse<AvatarId> addAvatarProd(AvatarProAddRequest request);

    /**
     * 查询数字人详情
     *
     * @return CommonResponse<AvatarStatusResponse>
     */
    @RequestLine("POST /V1/AVATAR/status")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    YuanZResponse<AvatarStatus> getAvatarStatus(AvatarSearchRequest request);

    /**
     * 生成数字人视频
     */
    @RequestLine("POST /V1/VIDEO/addVideoPro")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json",
    })
    YuanZResponse<VideoAddResponse> addVideoPro(AvatarVideoAddRequest request);

    /**
     * 查询视频生成进度
     */
    @RequestLine("POST /V1/VIDEO/status")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    YuanZResponse<VideoGetResponse> getVideoStatus(VideoGetRequest request);
}
