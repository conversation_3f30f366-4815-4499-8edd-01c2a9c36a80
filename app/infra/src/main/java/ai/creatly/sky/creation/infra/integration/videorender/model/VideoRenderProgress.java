/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.videorender.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version VideoRenderProgress.java, v 0.1 2024-07-15 20:24 syoka
 */
@Data
@Accessors(chain = true)
public class VideoRenderProgress {

    private String                    taskId;
    private VideoRenderProgressStatus status;
    private VideoFile                 file;

    @Data
    public static class VideoRenderProgressStatus {
        private String progress;
        private String status;
    }

    @Data
    public static class VideoFile {
        /**
         * 字节数
         */
        private Long size;
    }
}
