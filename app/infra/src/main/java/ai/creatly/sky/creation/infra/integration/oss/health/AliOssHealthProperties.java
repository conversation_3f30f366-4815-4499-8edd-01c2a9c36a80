/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.oss.health;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version AliOssHealthProperties.java, v 0.1 2024-09-12 下午4:50 zhoudong
 */
@Data
@ConfigurationProperties(prefix = "alibaba.cloud.oss.health")
@Component
public class AliOssHealthProperties {
    private String testBucket;
    private String readKey;
    private String writeKey;
}
