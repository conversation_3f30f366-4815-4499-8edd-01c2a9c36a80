/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.drama.ads.model.response;

import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * <AUTHOR>
 * @version DramaAdsDirectResponse.java, 2024-10-22 上午11:12 zhoudong
 */
@Data
public class DramaAdsDirectResponse {

    private boolean            success;
    private String             message;
    /**
     * 剧情ID
     */
    @Nullable
    private Long               dramaId;
    /**
     * 广告分镜列表
     */
    private List<DramaAdsShot> adsShots;
    /**
     * 广告音频文件大小（字节数）
     */
    private Integer            adsAudioSize;
    /**
     * 广告音频时长（毫秒）
     */
    private Integer            adsAudioMills;
    /**
     * 广告音频的字幕列表（逐字时间轴）
     */
    private List<DramaAdsText> adsSubtitles;

    public boolean isSuccess() {
        return success || "Empty_Drama".equalsIgnoreCase(message);
    }
}
