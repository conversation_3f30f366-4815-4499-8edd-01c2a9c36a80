/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.user;

import ai.creatly.sky.creation.domain.core.credit.model.UserCreditAccount;
import ai.creatly.sky.creation.infra.dal.tables.pojos.UserCredit;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.repository.PojoMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(config = BaseMapperConfig.class)
public interface UserCreditAccountMapper extends PojoMapper<UserCreditAccount, UserCredit> {

    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(source = "type", target = "creditType")
    UserCredit toEntity(UserCreditAccount userCreditAccount);

    @Mapping(source = "creditType", target = "type")
    UserCreditAccount toModel(UserCredit userCredit);

}

