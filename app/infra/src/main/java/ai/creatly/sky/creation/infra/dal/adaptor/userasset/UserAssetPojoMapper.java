/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.userasset;

import ai.creatly.sky.creation.domain.core.userasset.model.*;
import ai.creatly.sky.creation.domain.support.label.model.LabelRef;
import ai.creatly.sky.creation.infra.dal.common.ModelMapper;
import ai.creatly.sky.creation.infra.dal.common.UpdatableBuilder;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserAsset;
import ai.creatly.sky.creation.infra.dal.tables.records.UserAssetRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.util.json.JSON;
import org.jooq.JSONB;
import org.mapstruct.Mapper;

import java.util.List;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_USER_ASSET;

/**
 * <AUTHOR>
 * @version UserAssetPojoMapper.java, v 0.1 2024-09-22 上午12:10 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface UserAssetPojoMapper extends ModelMapper<UserAsset, CreationUserAsset, UserAssetRecord> {

    @Override
    CreationUserAsset toEntity(UserAsset userAsset);

    @Override
    UserAsset toModel(CreationUserAsset creationUserAsset);

    default AssetFile fileFromJsonb(JSONB file) {
        if (file == null) {
            return null;
        }
        return JSON.parseObject(file.data(), AssetFile.class);
    }

    default JSONB fileToJsonb(AssetFile file) {
        if (file == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(file));
    }

    default AssetContent contentFromJsonb(JSONB content) {
        if (content == null) {
            return null;
        }
        return JSON.parseObject(content.data(), AssetContent.class);
    }

    default JSONB contentToJsonb(AssetContent content) {
        if (content == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(content));
    }

    default String[] keywordsToArray(List<String> keywords) {
        if (keywords == null) {
            return null;
        }
        return keywords.toArray(String[]::new);
    }

    default List<String> keywordsFromArray(String[] keywords) {
        if (keywords == null) {
            return null;
        }
        return List.of(keywords);
    }

    default JSONB labelsToJsonb(List<LabelRef> labels) {
        if (labels == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(labels));
    }

    default List<LabelRef> labelsFromJsonb(JSONB labels) {
        if (labels == null) {
            return null;
        }
        return JSON.parseList(labels.data(), LabelRef.class);
    }

    default JSONB metadataToJsonb(UserAssetMetadata metadata) {
        if (metadata == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(metadata));
    }

    default UserAssetMetadata metadataFromJsonb(JSONB metadata) {
        if (metadata == null) {
            return null;
        }
        return JSON.parseObject(metadata.data(), UserAssetMetadata.class);
    }

    default List<AssetVideoSlice> videoSlicesFromJsonb(JSONB videoSlices) {
        if (videoSlices == null) {
            return null;
        }
        return JSON.parseList(videoSlices.data(), AssetVideoSlice.class);
    }

    default JSONB videoSlicesToJsonb(List<AssetVideoSlice> videoSlices) {
        if (videoSlices == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(videoSlices));
    }

    @Override
    default void updatable(UpdatableBuilder<UserAssetRecord> builder) {
        builder.updatable(CREATION_USER_ASSET.STATUS)
                .updatable(CREATION_USER_ASSET.NAME)
                .updatable(CREATION_USER_ASSET.COVER_FILE_ID)
                .updatable(CREATION_USER_ASSET.COVER_URL)
                .updatable(CREATION_USER_ASSET.DESCRIPTION)
                .updatable(CREATION_USER_ASSET.KEYWORDS)
                .updatable(CREATION_USER_ASSET.LABELS)
                .updatable(CREATION_USER_ASSET.FILE)
                .updatable(CREATION_USER_ASSET.CONTENT)
                .updatable(CREATION_USER_ASSET.METADATA)
                .updatable(CREATION_USER_ASSET.VIDEO_SLICES);
    }
}
