/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.wechat.mp.model.account;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version QRCodeCreateRequest.java, v 0.1 2024-10-14 下午4:41 zhoudong
 */
@Data
@Accessors(chain = true)
public class QRCodeCreateRequest {

    @JsonProperty("expire_seconds")
    private Long                   expireSeconds;
    @JsonProperty("action_name")
    private String                 actionName;
    @JsonProperty("action_info")
    private QRCodeCreateActionInfo actionInfo;
}
