/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.notification;

import ai.creatly.sky.creation.domain.support.notification.inbox.NotificationRepository;
import ai.creatly.sky.creation.domain.support.notification.inbox.model.Notification;
import ai.creatly.sky.creation.infra.dal.tables.daos.NotificationDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationNotification;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_NOTIFICATION;

/**
 * <AUTHOR>
 * @version NotificationRepositoryImpl.java, v 0.1 2024-07-12 17:18 syoka
 */
@Repository
@Slf4j
@RequiredArgsConstructor
public class NotificationRepositoryImpl implements NotificationRepository {

    private final DSLContext                 dsl;
    private final NotificationDAO        notificationDAO;
    private final NotificationPojoMapper notificationPojoMapper;

    @Override
    public Page<Notification> queryPageable(Long uid, Pageable pageable) {
        Long total = dsl.selectCount()
                .from(CREATION_NOTIFICATION)
                .where(CREATION_NOTIFICATION.RECEIVER.eq(uid))
                .fetchSingleInto(Long.class);

        if (total == 0) {
            return Page.empty(pageable);
        }

        List<Notification> res = dsl.selectFrom(CREATION_NOTIFICATION)
                .where(CREATION_NOTIFICATION.RECEIVER.eq(uid))
                .orderBy(CREATION_NOTIFICATION.CREATED_AT.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchStreamInto(CreationNotification.class)
                .map(notificationPojoMapper::toModel)
                .collect(Collectors.toList());
        return Page.of(res, pageable, total);
    }

    @Override
    public void readAllNotification(Long uid) {
        dsl.update(CREATION_NOTIFICATION)
                .set(CREATION_NOTIFICATION.STATUS, Notification.Status.READ)
                .where(CREATION_NOTIFICATION.STATUS.eq(Notification.Status.UNREAD))
                .and(CREATION_NOTIFICATION.RECEIVER.eq(uid))
                .execute();
    }

    @Override
    public void readSingleNotification(Long uid, Long notificationId) {
        dsl.update(CREATION_NOTIFICATION)
                .set(CREATION_NOTIFICATION.STATUS, Notification.Status.READ)
                .where(CREATION_NOTIFICATION.STATUS.eq(Notification.Status.UNREAD))
                .and(CREATION_NOTIFICATION.ID.eq(notificationId))
                .and(CREATION_NOTIFICATION.RECEIVER.eq(uid))
                .execute();
    }

    @Override
    public Long countUserUnreadNotification(long uid) {
        return dsl.selectCount()
                .from(CREATION_NOTIFICATION)
                .where(CREATION_NOTIFICATION.RECEIVER.eq(uid))
                .and(CREATION_NOTIFICATION.STATUS.eq(Notification.Status.UNREAD))
                .fetchSingleInto(Long.class);
    }

    @Override
    public void createNotification(Notification notification) {
        if (Objects.isNull(notification.getId())) {
            notification.setId(IdHelper.getId());
        }
        CreationNotification creationNotification = notificationPojoMapper.toEntity(notification);
        notificationDAO.insert(creationNotification);
    }
}
