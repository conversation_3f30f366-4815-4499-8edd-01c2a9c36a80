/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.scheduler;

import ai.creatly.sky.creation.domain.common.async.AsyncPools;
import ai.creatly.sky.creation.domain.common.async.AsyncTemplate;
import ai.creatly.sky.creation.domain.common.env.RuntimeEnv;
import ai.creatly.sky.creation.domain.core.aitask.engine.AiTaskEngine;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.factory.AiTaskHandlerFactory;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.scheduler.AiTaskSchedulerProperties;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import static java.util.stream.Collectors.toSet;

/**
 * AI任务调度器
 * <p/>
 * 本地启动默认不执行，如需调试，可以在local配置文件中打开
 *
 * <AUTHOR>
 * @version AiTaskScheduler.java, v 0.1 2023-07-28 21:12 joton
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "application.ai-task.scheduler", value = "enabled", havingValue = "true")
public class AiTaskScheduler {

    private final AiTaskRepository          aiTaskRepository;
    private final AiTaskHandlerFactory      aiTaskHandlerFactory;
    private final AiTaskEngine              aiTaskEngine;
    private final AiTaskSchedulerProperties aiTaskSchedulerProperties;
    private final AppAlertHelper            appAlertHelper;
    private final RuntimeEnv                runtimeEnv;
    private final AsyncTemplate             asyncTemplate;
    private final StringRedisTemplate       stringRedisTemplate;
    private static final String SCHEDULE_LOCK = "TASK_SCHEDULE_LOCK";


    /**
     * 每次以上一次调度的结束时间为开始时间，固定间隔8s触发一次调度，这里只是一个触发器。<br/>
     * 至于每次触发捞取多少任务，由loader层面解决（loader可以走db ，也可以走cache，甚至走mq死信队列）
     */
    @Scheduled(initialDelay = 10, fixedDelay = 5, timeUnit = TimeUnit.SECONDS)
    public void pollAiTasks() {
        // TODO: 2023/11/30 增加Trace能力
        // 第1层调度：获取业务分片
        Set<AiTaskConfig> taskConfigs = this.entry();
        if (taskConfigs.isEmpty()) {
            log.warn("[poll_tasks]no task needs to load.");
            return;
        }
//        log.info("[poll_tasks][split]size={}", taskConfigs.size());
        long loadStart = System.currentTimeMillis();
        boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(SCHEDULE_LOCK, "locked", 20, TimeUnit.SECONDS);
        try {
            if (locked) {
                // 第2层调度：捞取待处理的任务实例（只捞取特定数量的）
                List<AiTask> aiTasks = this.load(taskConfigs);
                if (aiTasks.isEmpty()) {
                    return;
                }
//                log.info("[poll_tasks][load]tasks={},cost={}ms", aiTasks.size(), System.currentTimeMillis() - loadStart);

                // 第3层调度：执行任务实例
                long executeStart = System.currentTimeMillis();
                this.execute(aiTasks);
//                log.info("[poll_tasks][execute]tasks={},cost={}ms", aiTasks.size(), System.currentTimeMillis() - loadStart);
            }
        } catch (Exception e) {
            log.error("ai task schedule error!", e);
        } finally {
            if (locked) {
                stringRedisTemplate.delete(SCHEDULE_LOCK);
            }
        }

    }

    /**
     * 获取业务分片 TODO: 2023/7/29 用redis对调度分片加锁，等上集群时，需要确保一个调度分片只能被一个应用实例抢占成功
     *
     * @return 业务分片列表
     */
    private Set<AiTaskConfig> entry() {
        // 环境判断
        Boolean enabled = aiTaskSchedulerProperties.getEnabled();
        List<String> enabledTaskTypes = aiTaskSchedulerProperties.getEnabledTaskTypes();
        if (runtimeEnv.isLocal() && Boolean.TRUE.equals(enabled) && CollectionUtils.size(enabledTaskTypes) != 1) {
            log.warn("[poll_tasks]only one task type can be scheduled on local env!");
            return Collections.emptySet();
        }

        // 调度黑名单
        final List<String> disabledTaskTypes;
        if (aiTaskSchedulerProperties.getDisabledTaskTypes() == null) {
            disabledTaskTypes = Collections.emptyList();
        } else {
            disabledTaskTypes = aiTaskSchedulerProperties.getDisabledTaskTypes();
        }

        // 获取分片列表
        Set<AiTaskConfig> taskConfigs = aiTaskHandlerFactory.getAllTaskConfigs();
        if (CollectionUtils.isNotEmpty(enabledTaskTypes)) {
            // 先过白名单
            taskConfigs = taskConfigs.stream()
                    .filter(taskConfig -> enabledTaskTypes.contains(taskConfig.getTaskType().name()))
                    .collect(toSet());
        }
        if (CollectionUtils.isNotEmpty(disabledTaskTypes)) {
            // 再过黑名单
            taskConfigs = taskConfigs.stream()
                    .filter(taskConfig -> !disabledTaskTypes.contains(taskConfig.getTaskType().name()))
                    .collect(toSet());
        }
        return taskConfigs;
    }

    /**
     * 并行捞取AI创作任务
     *
     * @param taskConfigs 调度分片
     * @return 可执行任务列表
     */
    private List<AiTask> load(Set<AiTaskConfig> taskConfigs) {

        List<AiTask> aiTasksLoad = new ArrayList<>();
        try {
            taskConfigs.forEach(taskConfig -> {
                AiTaskType taskType = taskConfig.getTaskType();
                String bizType = taskConfig.getBizType();
                Integer loadSize = taskConfig.getLoadSize();
                // TODO: 2023/11/21 根据不同的锁超时时间，适配不同的调度间隔时间
                // 通过分布式锁来控制集群模式下的任务调度
                long start = System.currentTimeMillis();
                List<AiTask> aiTasks = aiTaskRepository.queryWaitingForAutoExec(taskType, bizType, loadSize);
                long costMs = System.currentTimeMillis() - start;
                log.debug("[poll_tasks][load]taskType={},bizType={},loadSize={},actualSize={},cost={}ms",
                        taskType, bizType, loadSize, aiTasks.size(), costMs);
                if (!aiTasks.isEmpty()) {
                    aiTasksLoad.addAll(aiTasks);
                }
            });
        } catch (Exception e) {
            appAlertHelper.alertText("[poll_tasks][load][thread_pool_is_full]", e);
            return Collections.emptyList();
        }
        return aiTasksLoad;
    }

    /**
     * 并行执行AI创作任务
     *
     * @param aiTasks AI创作任务
     */
    private void execute(List<AiTask> aiTasks) {
        Executor executor = asyncTemplate.getTaskExecutor(AsyncPools.TASK_SCHEDULER_EXECUTE_POOL);

        // Build all futures
        CompletableFuture<?>[] futures;
        try {
            futures = aiTasks.stream()
                    .map(aiTask -> CompletableFuture.runAsync(() -> {
                        log.debug("[poll_tasks][execute][begin]taskId={},taskType={},bizType={}", aiTask.getId(),
                                aiTask.getTaskType(), aiTask.getBizType());

                        long s = System.currentTimeMillis();
                        aiTaskEngine.execute(aiTask);
                        long e = System.currentTimeMillis();

                        log.debug("[poll_tasks][execute][end]taskId={},taskType={},bizType={},cost={}ms", aiTask.getId(),
                                aiTask.getTaskType(), aiTask.getBizType(), e - s);
                    }, executor))
                    .toArray(CompletableFuture[]::new);
        } catch (Exception e) {
            appAlertHelper.alertText("[poll_tasks][execute][thread_pool_is_full]", e);
            return;
        }

        log.debug("[poll_tasks][execute]TASK_EXECUTE_POOL:{}", executor);

        // Wait for all futures to complete.
        try {
            CompletableFuture.allOf(futures).join();
        } catch (CompletionException e) {
            log.error("[poll_tasks][execute]error", e);
            // 告警
            appAlertHelper.alertText("[poll_tasks][execute]error", e);
        }
    }
}
