package ai.creatly.sky.creation.infra.integration.openai;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version OpenAiProperties.java, v 0.1 2023-05-08 14:55 joton
 */
@Data
@ConfigurationProperties(prefix = "application.open-ai")
public class OpenAiProperties {

    @NotBlank
    private String apiKey;
    @NotNull
    private Duration readTimeout;
}
