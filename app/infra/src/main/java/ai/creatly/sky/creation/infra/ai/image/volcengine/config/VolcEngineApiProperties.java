/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.image.volcengine.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.validation.annotation.Validated;

/**
 *
 * <AUTHOR>
 * @version VolcEngineApiProperties.java, v0.1 2025-02-28 15:12
 */
@Data
@Validated
@ConfigurationProperties(prefix = "application.ai-image.volc-engine")
public class VolcEngineApiProperties {

    @NotBlank
    private String             accessKey;
    @NotBlank
    private String             secretKey;
    @Valid
    @NotNull
    @NestedConfigurationProperty
    private ImageApiProperties imageApi;

    @Data
    public static class ImageApiProperties {
        @NotBlank
        private String service;
        @NotBlank
        private String region;
        @NotBlank
        private String endpoint;
    }
}
