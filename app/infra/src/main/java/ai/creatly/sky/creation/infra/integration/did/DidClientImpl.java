/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.did;

import ai.creatly.sky.creation.domain.common.integration.did.DidClient;
import ai.creatly.sky.creation.domain.common.integration.did.model.*;
import ai.creatly.sky.creation.infra.integration.did.api.DidClientApi;
import ai.creatly.sky.creation.infra.integration.did.config.DidProperties;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version DidClientImpl.java, v 0.1 2023-06-23 17:06 joton
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DidClientImpl implements DidClient {

    private final DidClientApi  didClientApi;
    private final DidProperties didProperties;

    @Override
    public CreateTalkResponse createTalk(CreateTalkRequest request) {
        Asserts.notBlank(didProperties.getApiBasicToken(), "DID token is blank");

        // 设置视频水印
        if (StringUtils.isNotBlank(didProperties.getLogoUrl())) {
            CreateTalkLogo logo = new CreateTalkLogo()
                    .setUrl(didProperties.getLogoUrl())
                    .setPosition(List.of(0, 0));
            final DidTalkConfig config = request.getConfig() != null ? request.getConfig() : new DidTalkConfig();
            config.setLogo(logo);
            request.setConfig(config);
        }

        log.info("[createTalk]request={}", JSON.toJSONString(request));
        return didClientApi.createTalk(request, didProperties.getApiBasicToken());
    }

    @Override
    public DidTalk getTalk(String id) {
        Asserts.notBlank(didProperties.getApiBasicToken(), "DID token is blank");
        return didClientApi.getTalk(id, didProperties.getApiBasicToken());
    }

    @Override
    public DidCredits getCredits() {
        Asserts.notBlank(didProperties.getApiBasicToken(), "DID token is blank");
        return didClientApi.getCredits(didProperties.getApiBasicToken());
    }
}
