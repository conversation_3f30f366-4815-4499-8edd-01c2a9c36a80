package ai.creatly.sky.creation.infra.ai.model.chanjing.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.checkerframework.checker.nullness.qual.Nullable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ChanJiangVideoDetailResponse {

    private data data = new data();

    @JsonProperty("trace_id")
    private String traceId;

    private Integer code;

    @JsonProperty("msg")
    private String message;

    @Data
    @Accessors(chain = true)
    public static class data {
        private String id;
        private Integer status;
        private Integer progress;

        @JsonProperty("msg")
        private String message;

        @JsonProperty("video_url")
        private String videoUrl;

        @JsonProperty("create_time")
        private Long createTime;

        @Nullable
        @JsonProperty("subtitle_data_url")
        private String subtitleDataUrl;

        @JsonProperty("preview_url")
        private String previewUrl;

        private Integer duration;
    }
}
