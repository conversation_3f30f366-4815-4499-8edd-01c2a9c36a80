package ai.creatly.sky.creation.infra.ai.model.kling.api;

import ai.creatly.sky.creation.infra.ai.model.kling.model.*;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.json.JSONObject;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version LiblibImageApi.java, v0.1 2025-03-04 13:47
 */
public interface KlingApi {

    @RequestLine("POST /v1/images/generations")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    KlingResponse<JSONObject> generateImage(@RequestBody KlingRequest request);

    @RequestLine("GET /v1/images/generations/{taskId}")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    KlingResponse<JSONObject> queryImageGeneration(@Param("taskId") String taskId);


    @RequestLine("POST /v1/videos/text2video")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    KlingResponse<JSONObject> generateText2Video(@RequestBody KlingRequest request);

    @RequestLine("GET  /v1/videos/text2video/{taskId}")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    KlingResponse<JSONObject> queryText2VideoGeneration(@Param("taskId") String taskId);

    @RequestLine("POST /v1/videos/image2video")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    KlingResponse<JSONObject> generateImage2Video(@RequestBody KlingRequest request);

    @RequestLine("GET  /v1/videos/image2video/{taskId}")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    KlingResponse<JSONObject> queryImage2VideoGeneration(@Param("taskId") String taskId);

    @RequestLine("POST  /v1/images/kolors-virtual-try-on")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    KlingResponse<JSONObject> clothTry(KlingClothTryRequest request);

    @RequestLine("GET  /v1/images/kolors-virtual-try-on/{taskId}")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    KlingResponse<JSONObject> queryClothTryGeneration(@Param("taskId") String taskId);

    @RequestLine("POST  /v1/videos/effects")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    KlingResponse<JSONObject> effects(KlingVideoEffectsRequest request);


    @RequestLine("GET  /v1/videos/effects/{taskId}")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    KlingResponse<JSONObject> queryVideoEffectsGeneration(@Param("taskId") String taskId);



    @RequestLine("POST  /v1/videos/lip-sync")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    KlingResponse<JSONObject> listSyncSubmit(KlingLipSyncRequest request);



    @RequestLine("GET  /v1/videos/lip-sync/{taskId}")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    KlingResponse<JSONObject> queryLipSyncGeneration(@Param("taskId") String taskId);
}
