/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.drama;

import ai.creatly.sky.creation.domain.core.drama.model.Drama;
import ai.creatly.sky.creation.domain.core.drama.model.DramaReview;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaAction;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaLocalSyncStatus;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaStatus;
import ai.creatly.sky.creation.domain.core.drama.service.DramaRepository;
import ai.creatly.sky.creation.infra.dal.tables.daos.DramaDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationDrama;
import ai.creatly.sky.creation.infra.dal.tables.records.DramaRecord;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.json.JSON;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.stereotype.Repository;

import java.util.*;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_DRAMA;
import static java.util.stream.Collectors.toList;
import static org.jooq.impl.DSL.*;

/**
 * <AUTHOR>
 * @version DramaRepositoryImpl.java, v 0.1 2024-10-18 下午8:47 zhoudong
 */
@Repository
@RequiredArgsConstructor
public class DramaRepositoryImpl implements DramaRepository {

    private final DSLContext      dsl;
    private final DramaDAO        dramaDAO;
    private final DramaPojoMapper dramaPojoMapper;

    @Override
    public long create(Drama drama) {
        CreationDrama creationDrama = dramaPojoMapper.toEntity(drama);
        if (creationDrama.getId() == null) {
            creationDrama.setId(IdHelper.getId());
        }
        dramaDAO.insert(creationDrama);
        return creationDrama.getId();
    }

    @Override
    public void updateById(Drama drama) {
        Asserts.notNull(drama.getId(), "drama.id must not be null");
        DramaRecord dramaRecord = dramaPojoMapper.toUpdatingRecord(drama);
        dsl.update(CREATION_DRAMA)
                .set(dramaRecord)
                .where(CREATION_DRAMA.ID.eq(drama.getId()))
                .execute();
    }

    @Override
    public void action(long id, DramaAction action, @Nullable DramaReview review) {
        DramaRecord dramaRecord = dramaPojoMapper.toUpdatingRecord(new Drama().setStatus(action.getTo()).setReview(review));
        dsl.update(CREATION_DRAMA)
                .set(dramaRecord)
                .where(CREATION_DRAMA.ID.eq(id))
                .and(CREATION_DRAMA.STATUS.eq(action.getFrom().name()))
                .execute();
    }

    @Override
    public Optional<Drama> queryOptionalById(long id) {
        return dramaDAO.fetchOptionalById(id).map(dramaPojoMapper::toModel);
    }

    @Override
    public Page<Drama> queryPageByStatus(DramaStatus status, Pageable pageable) {
        Condition condition = CREATION_DRAMA.STATUS.eq(status.name());
        return this.queryPage(condition, pageable);
    }

    @Override
    public Page<Drama> queryPageByUidAndStatus(long uid, List<DramaStatus> statuses, Pageable pageable) {
        Condition condition = CREATION_DRAMA.UID.eq(uid)
                .and(CREATION_DRAMA.STATUS.in(statuses.stream().map(DramaStatus::name).toList()));
        return this.queryPage(condition, pageable);
    }

    private Page<Drama> queryPage(Condition condition, Pageable pageable) {
        // count
        long count = dsl.selectCount()
                .from(CREATION_DRAMA)
                .where(condition)
                .fetchSingleInto(long.class);
        if (count == 0) {
            return Page.empty(pageable);
        }

        // list
        List<Drama> dramas = dsl.selectFrom(CREATION_DRAMA)
                .where(condition)
                .orderBy(CREATION_DRAMA.UPDATED_AT.desc())
                .limit(pageable.getPageSize())
                .offset(pageable.getOffset())
                .fetchStreamInto(CreationDrama.class)
                .map(dramaPojoMapper::toModel)
                .collect(toList());

        return Page.of(dramas, pageable, count);
    }

    @Override
    public Optional<Drama> querySyncedByContentMd5(String contentMd5) {
        return dsl.selectFrom(CREATION_DRAMA)
                .where(jsonbGetAttributeAsText(CREATION_DRAMA.CONTENT, "md5").eq(contentMd5))
                .and(CREATION_DRAMA.LOCAL_SYNC_STATUS.eq(DramaLocalSyncStatus.synced.name()))
                .limit(1)
                .fetchOptionalInto(CreationDrama.class)
                .map(dramaPojoMapper::toModel);
    }

    @Override
    public List<Drama> queryByIds(Set<Long> dramaIds) {
        if (dramaIds == null || dramaIds.isEmpty()) {
            return new ArrayList<>();
        }
        return dramaDAO.fetchById(dramaIds.toArray(Long[]::new))
                .stream()
                .map(dramaPojoMapper::toModel)
                .collect(toList());
    }

    @Override
    public List<String> summaryProductCategoryPaths() {
        // 需要对商品类目全局去重
        //  SELECT DISTINCT product_category_codes
        //  FROM creation.creation_drama,
        //  LATERAL jsonb_array_elements(product_categories) AS product_category,
        //  LATERAL jsonb_extract_path(product_category, 'codes') AS product_category_codes
        return dsl.selectDistinct(field("codes", JSONB.class))
                .from(CREATION_DRAMA,
                        lateral(table("jsonb_array_elements(?::jsonb)", CREATION_DRAMA.PRODUCT_CATEGORIES).as("product_category")),
                        lateral(table("jsonb_extract_path(product_category, 'codes')").as("codes"))
                )
                .fetchStreamInto(JSONB.class)
                .filter(Objects::nonNull)
                .map(JSONB::data)
                .map(codes -> JSON.parseList(codes, String.class))
                .map(codes -> String.join(StringPool.SLASH, codes))
                .collect(toList());
    }
}
