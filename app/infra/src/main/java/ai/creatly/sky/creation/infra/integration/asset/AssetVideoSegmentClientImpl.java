/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.asset;

import ai.creatly.sky.creation.domain.common.integration.oss.FileContentMetadata;
import ai.creatly.sky.creation.domain.common.integration.oss.OssTemplate;
import ai.creatly.sky.creation.domain.core.userasset.client.AssetVideoSegmentClient;
import ai.creatly.sky.creation.domain.core.userasset.model.AssetFile;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.builder.VideoFileBuilder;
import ai.creatly.sky.creation.infra.integration.ApiTemplate;
import ai.creatly.sky.creation.infra.integration.asset.api.AssetVideoSegmentApi;
import ai.creatly.sky.creation.infra.integration.asset.model.AssetVideoSegmentRequest;
import ai.creatly.sky.creation.infra.integration.asset.model.AssetVideoSegmentResponse;
import ai.creatly.sky.creation.infra.integration.asset.model.SegmentedVideo;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version AssetVideoSegmentClientImpl.java, 2024-11-01 下午7:23 zhoudong
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AssetVideoSegmentClientImpl implements AssetVideoSegmentClient {

    private final AssetVideoSegmentApi assetVideoSegmentApi;
    private final UserFileRepository   userFileRepository;
    private final OssTemplate          ossTemplate;
    private final VideoFileBuilder     videoFileBuilder;

    @Override
    public List<UserFile> segment(long assetId, AssetFile assetFile) {
        UserFile file = userFileRepository.queryById(assetFile.getFileId());

        String outputDir = FilenameUtils.getPathNoEndSeparator(file.getKey());
        AssetVideoSegmentRequest request = new AssetVideoSegmentRequest()
                .setBucket(file.getBucket())
                .setAssetKey(file.getKey())
                .setAssetId(assetId)
                .setOutputDir(outputDir);

        List<SegmentedVideo> videos = ApiTemplate.logger(log, "[segment]视频素材切片")
                .request("request:{}", request)
                .invoke(() -> assetVideoSegmentApi.segment(request))
                .response("response:{}", Objects::toString)
                .success(AssetVideoSegmentResponse::isSuccess)
                .fetchInfo(AssetVideoSegmentResponse::getContent);

        if (CollectionUtils.isEmpty(videos)) {
            log.warn("该视频素材无需切片, assetId:{}, assetFile:{}", assetId, assetFile);
            return List.of();
        }

        return videos.stream()
                .map(segmentedVideo -> {
                    Asserts.notBlank(segmentedVideo.getFilename(), "切片视频文件名不能为空");
                    String fileKey = this.getFileKey(outputDir, segmentedVideo.getFilename());
                    ossTemplate.assureStoragePresent(file.getBucket(), fileKey);
                    FileContentMetadata contentMetadata = ossTemplate.getContentMetadata(file.getBucket(), fileKey);

                    Asserts.notNull(segmentedVideo.getDurationMills(), "切片视频时长不能为空");
                    Duration duration = Duration.ofMillis(segmentedVideo.getDurationMills());

                    return videoFileBuilder.buildFrom(file, fileKey, contentMetadata, duration);
                })
                .collect(toList());
    }

    private String getFileKey(String dir, String filename) {
        return dir + "/" + filename;
    }
}
