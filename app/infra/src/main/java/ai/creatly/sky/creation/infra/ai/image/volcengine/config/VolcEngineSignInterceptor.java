/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.image.volcengine.config;

import com.volcengine.Pair;
import com.volcengine.sign.Credentials;
import com.volcengine.sign.VolcstackSign;
import feign.RequestInterceptor;
import feign.RequestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version VolcEngineSignInterceptor.java, v0.1 2025-02-28 12:15
 */
public class VolcEngineSignInterceptor implements RequestInterceptor {

    private final Credentials credentials;
    private final String      region;
    private final String      service;

    public VolcEngineSignInterceptor(VolcEngineApiProperties properties, String region, String service) {
        this.credentials = Credentials.getCredentials(properties.getAccessKey(), properties.getSecretKey());
        this.region = region;
        this.service = service;
    }

    @Override
    public void apply(RequestTemplate template) {
        VolcstackSign sign = new VolcstackSign();
        sign.setCredentials(credentials);
        sign.setRegion(region);
        sign.setMethod(template.method().toUpperCase());
        sign.setService(service);

        List<Pair> queryParams = new ArrayList<>();
        template.queries().forEach((name, values) -> {
            Pair nameValuePair = new Pair(name, values.iterator().next());
            queryParams.add(nameValuePair);
        });

        Map<String, String> headerParams = new HashMap<>();
        template.headers().forEach((name, values) -> headerParams.put(name, values.iterator().next()));

        String payload = new String(template.body(), template.requestCharset());

        sign.applyToParams(queryParams, headerParams, payload);

        template.header("X-Date", headerParams.get("X-Date"))
                .header("Authorization", headerParams.get("Authorization"));
    }
}
