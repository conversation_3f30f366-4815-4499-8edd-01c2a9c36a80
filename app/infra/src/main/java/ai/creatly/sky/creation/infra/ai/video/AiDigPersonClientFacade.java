package ai.creatly.sky.creation.infra.ai.video;

import ai.creatly.sky.creation.domain.core.ai.video.AiDigPersonClient;
import ai.creatly.sky.creation.domain.core.ai.video.model.AiVideoTaskResult;
import ai.creatly.sky.creation.domain.core.ai.video.model.DigPersonVideoTaskInput;
import ai.creatly.sky.creation.domain.core.ai.video.model.VideoEffectsTaskInput;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Primary
@RequiredArgsConstructor
public class AiDigPersonClientFacade implements AiDigPersonClient {

    @Autowired
    AiDigPersonClientImpl aiDigPersonClient;

    @Override
    public AiVideoTaskResult generate(DigPersonVideoTaskInput input, UserContext userContext) {
        return aiDigPersonClient.generate(input, userContext);
    }

    @Override
    public CreditsExpense expense(AiTask aiTask) {
        return aiDigPersonClient.expense(aiTask);
    }

    @Override
    public AiVideoTaskResult queryGeneration(String taskId, UserContext userContext) {
        return aiDigPersonClient.queryGeneration(taskId, userContext);
    }
}
