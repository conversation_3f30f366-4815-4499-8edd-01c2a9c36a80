/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.stablediffusion.config;

import ai.creatly.sky.creation.infra.integration.FeignUtil;
import ai.creatly.sky.creation.infra.integration.stablediffusion.api.CameraMovementApi;
import ai.creatly.sky.creation.infra.integration.stablediffusion.api.CameraMovementApiContainer;
import ai.creatly.sky.creation.infra.integration.stablediffusion.api.ShotEffectApi;
import ai.creatly.sky.creation.infra.integration.stablediffusion.api.ShotEffectApiContainer;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Feign;
import feign.Retryer;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import lombok.RequiredArgsConstructor;
import okhttp3.OkHttpClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.List;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.toList;

@Configuration
@EnableConfigurationProperties(StableDiffuserProperties.class)
@RequiredArgsConstructor
public class StableDiffuserConfiguration {

    private final ObjectMapper             objectMapper;
    private final StableDiffuserProperties stableDiffuserProperties;

    @Bean
    public List<ShotEffectApiContainer> shotEffectContainers() {
        OkHttpClient okHttpClient = this.okHttpClient();
        List<StableDiffuserProperties.StableDiffuserSrvProperty> dynamicEffects = stableDiffuserProperties.getShotEffect();
        return IntStream.range(0, dynamicEffects.size()).boxed().map(index -> {
                    StableDiffuserProperties.StableDiffuserSrvProperty property = dynamicEffects.get(index);
                    ShotEffectApi shotEffectApi = Feign.builder()
                            .encoder(new JacksonEncoder(objectMapper))
                            .decoder(new JacksonDecoder(objectMapper))
                            .client(new feign.okhttp.OkHttpClient(okHttpClient))
                            .options(FeignUtil.requestOptions(okHttpClient))
                            .retryer(Retryer.NEVER_RETRY)
                            .target(ShotEffectApi.class, property.getEndpoint());
                    return ShotEffectApiContainer.of(index, property.getHostname(), shotEffectApi);
                })
                .collect(toList());
    }

    @Bean
    public List<CameraMovementApiContainer> cameraMovementContainers() {
        OkHttpClient okHttpClient = this.okHttpClient();
        List<StableDiffuserProperties.StableDiffuserSrvProperty> cameraMovements = stableDiffuserProperties.getCameraMovement();
        return IntStream.range(0, cameraMovements.size()).boxed().map(index -> {
                    StableDiffuserProperties.StableDiffuserSrvProperty property = cameraMovements.get(index);
                    CameraMovementApi cameraMovementApi = Feign.builder()
                            .encoder(new JacksonEncoder(objectMapper))
                            .decoder(new JacksonDecoder(objectMapper))
                            .client(new feign.okhttp.OkHttpClient(okHttpClient))
                            .options(FeignUtil.requestOptions(okHttpClient))
                            .retryer(Retryer.NEVER_RETRY)
                            .target(CameraMovementApi.class, property.getEndpoint());
                    return CameraMovementApiContainer.of(index, property.getHostname(), cameraMovementApi);
                })
                .collect(toList());
    }

    private OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder()
                // 建立连接超时（库默认10s）
                .connectTimeout(Duration.ofSeconds(5))
                // 请求服务器超时（库默认10s）
                .writeTimeout(Duration.ofSeconds(5))
                // 等待服务器响应超时（库默认10s）
                .readTimeout(Duration.ofSeconds(180))
                // 总体调用耗时（库默认永不超时）：包含DNS解析+请求+响应
                .callTimeout(Duration.ofSeconds(185))
                .build();
    }
}
