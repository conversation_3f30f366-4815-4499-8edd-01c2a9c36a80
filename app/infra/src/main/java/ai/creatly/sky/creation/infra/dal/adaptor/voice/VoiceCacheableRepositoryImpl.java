/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.voice;

import ai.creatly.sky.creation.domain.common.caching.CachingTemplate;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceCategory;
import ai.creatly.sky.creation.domain.core.voice.repository.VoiceCacheableRepository;
import ai.creatly.sky.creation.domain.core.voice.repository.VoiceRepository;
import ai.creatly.sky.creation.domain.core.voice.util.VoiceUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.time.Duration;
import java.util.*;

/**
 * <AUTHOR>
 * @version VoiceCacheableRepositoryImpl.java, v 0.1 2023-11-09 下午9:02 zhoudong
 */
@Repository
@RequiredArgsConstructor
public class VoiceCacheableRepositoryImpl implements VoiceCacheableRepository, VoiceCacheKeys {

    private final VoiceRepository voiceRepository;
    private final CachingTemplate cachingTemplate;

    private static final Duration CACHE_TIMEOUT = Duration.ofHours(1);

    @Override
    public List<Voice> queryOnlineVoices(VoiceCategory category, long uid) {
        if (AppConstants.isSystemUser(uid)) {
            // 系统声音
            return cachingTemplate.cacheableList(Voice.class)
                    .key(CACHE_VOICES_BY_UID_CATEGORY, uid, category.name())
                    .timeout(CACHE_TIMEOUT)
                    .pushKeyToList(CACHE_VOICE_KEYS)
                    .get(() -> voiceRepository.queryOnlineVoices(category, uid));
        }
        // 用户声音
        return voiceRepository.queryOnlineVoices(category, uid);
    }

    @Override
    public Optional<Voice> queryOptionalById(long id) {
        if (VoiceUtil.isSysVoice(id)) {
            // 系统声音
            return cachingTemplate.cacheable(Voice.class)
                    .key(CACHE_VOICE_BY_ID, id)
                    .timeout(CACHE_TIMEOUT)
                    .optional(() -> voiceRepository.queryOptionalById(id));
        }
        // 用户声音
        return voiceRepository.queryOptionalById(id);
    }

    @Override
    public Map<Long, Voice> queryMapByIds(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        Map<Long, Voice> map = new HashMap<>();
        for (Long id : ids) {
            this.queryOptionalById(id).ifPresent(v -> map.put(id, v));
        }
        return map;
    }

    @Override
    public Voice queryByUidAndCode(long uid, String code) {
        if (AppConstants.isSystemUser(uid)) {
            // 系统声音
            return cachingTemplate.cacheable(Voice.class)
                    .key(CACHE_VOICE_BY_UID_CODE, uid, code)
                    .timeout(CACHE_TIMEOUT)
                    .pushKeyToList(CACHE_VOICE_KEYS_BY_ID, Voice::getId)
                    .get(() -> voiceRepository.queryByUidAndCode(uid, code));
        }
        // 用户声音
        return voiceRepository.queryByUidAndCode(uid, code);
    }
}
