/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.aiproductimage.api;

import ai.creatly.sky.creation.domain.core.aiproduct.image.client.model.AiImageImagineRequest;
import ai.creatly.sky.creation.domain.core.aiproduct.image.client.model.ImageBgRemoveRequest;
import com.jspeeder.core.data.result.ApiVoidResult;
import feign.Headers;
import feign.RequestLine;

/**
 * <AUTHOR>
 * @version AiProductImageApi.java, v 0.1 2024-02-28 下午5:27 zhoudong
 */
public interface AiProductImageApi {

    /**
     * 去除图片背景
     *
     * @return 响应结果
     */
    @RequestLine("POST /api/product-image/segment")
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    ApiVoidResult segment(ImageBgRemoveRequest request);

    /**
     * 图+场景生图
     *
     * @return 响应结果
     */
    @RequestLine("POST /api/product-image/generate")
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    ApiVoidResult generate(AiImageImagineRequest request);
}
