/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal;

import org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 *
 * <AUTHOR>
 * @version TransactionManagerConfiguration.java, v 0.1 2024-01-09 下午4:36 zhoudong
 */
@Configuration
public class TransactionManagerConfiguration {

    @Bean
    public TransactionManagerCustomizer<DataSourceTransactionManager> transactionManagerCustomizer() {
        // 设置默认的事务超时时间为3s，不允许超过3s的事务存在 TODO 没生效
        return transactionManager -> transactionManager.setDefaultTimeout(3);
    }
}
