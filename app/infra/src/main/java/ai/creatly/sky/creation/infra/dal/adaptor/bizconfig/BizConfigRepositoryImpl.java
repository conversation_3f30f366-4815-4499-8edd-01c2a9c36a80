/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.bizconfig;

import ai.creatly.sky.creation.domain.common.env.RuntimeEnv;
import ai.creatly.sky.creation.domain.core.aittalk.model.enums.AiTalkProvider;
import ai.creatly.sky.creation.domain.support.bizconfig.mapper.BizConfigMapper;
import ai.creatly.sky.creation.domain.support.bizconfig.model.instance.BizConfig;
import ai.creatly.sky.creation.domain.support.bizconfig.model.instance.BizConfigProp;
import ai.creatly.sky.creation.domain.support.bizconfig.model.schema.BizConfigSchema;
import ai.creatly.sky.creation.domain.support.bizconfig.model.schema.PropDataType;
import ai.creatly.sky.creation.domain.support.bizconfig.repository.BizConfigRepository;
import ai.creatly.sky.creation.infra.io.ResourceReader;
import com.jspeeder.core.util.Validates;
import com.jspeeder.core.util.json.JSON;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.jooq.lambda.Seq;
import org.springframework.stereotype.Repository;

import java.util.*;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * 通用业务配置持久化实现
 *
 * <AUTHOR>
 * @version BizConfigRepositoryImpl.java, v 0.1 2023-05-08 15:36 joton
 */
@Repository
@RequiredArgsConstructor
public class BizConfigRepositoryImpl implements BizConfigRepository {

    private final BizConfigMapper bizConfigMapper;
    private final ResourceReader  resourceReader;
    private final RuntimeEnv      runtimeEnv;

    private final Map<String, BizConfigSchema> bizConfigSchemaMap = new HashMap<>();
    private final Map<String, List<BizConfig>> bizConfigMap       = new HashMap<>();

    @PostConstruct
    public void initBizConfigData() {
        // 数据结构
        this.initSchemas();

        if (runtimeEnv.isOnline()) {
            // 线上环境数据
            this.initInstances("classpath:biz_config_db/online/plan/biz_config.records.json");
            this.initInstances("classpath:biz_config_db/online/plan_benefit/biz_config.records.json");
        } else {
            // 线下环境数据
            this.initInstances("classpath:biz_config_db/offline/plan/biz_config.records.json");
            this.initInstances("classpath:biz_config_db/offline/plan_benefit/biz_config.records.json");
        }
    }

    @Override
    public <T> List<T> queryList(String schemaCode, Map<String, String> queryParams, Class<T> type) {
        BizConfigSchema bizConfigSchema = bizConfigSchemaMap.get(schemaCode);
        Validates.notNull(bizConfigSchema, "业务配置模式不存在，schemaCode={}", schemaCode);

        // 查询符合条件的业务配置列表
        List<BizConfig> bizConfigs = bizConfigMap.get(schemaCode).stream().filter(bizConfig -> {
            Map<String, BizConfigProp> propMap = bizConfig.getProps()
                    .stream()
                    .collect(toMap(BizConfigProp::getKey, identity()));

            return queryParams.entrySet().stream().allMatch(entry -> {
                String propKey = entry.getKey();
                String propValue = entry.getValue();
                return Objects.equals(propValue, propMap.get(propKey).getValue());
            });
        }).toList();

        // 转换为业务模型
        return bizConfigs.stream().map(bizConfig -> this.convert(bizConfig, type)).collect(toList());
    }

    @Override
    public <T> Optional<T> queryOne(String schemaCode, List<String> uniqueValues, Class<T> type) {
        // TODO: 2023/7/29 数据库持久化关联
        if ("1".equals(schemaCode)) {
            T one = bizConfigMapper.toBizObj(OPEN_AI_CHAT_COMPLETION_CONFIG, type);
            return Optional.of(one);
        }
        if ("common_kv".equals(schemaCode)) {
            if (uniqueValues.getFirst().equals("ai_talk_provider")) {
                T one = bizConfigMapper.toBizObj(AI_TALK_CONFIG, type);
                return Optional.of(one);
            }
        }

        BizConfigSchema bizConfigSchema = bizConfigSchemaMap.get(schemaCode);
        Validates.notNull(bizConfigSchema, "业务配置模式不存在，schemaCode={}", schemaCode);

        List<String> uniqueKeys = bizConfigSchema.getUniqueKeys();
        Map<String, String> propValues = new HashMap<>();
        for (int i = 0; i < uniqueKeys.size(); i++) {
            propValues.put(uniqueKeys.get(i), uniqueValues.get(i));
        }
        List<T> list = this.queryList(schemaCode, propValues, type);
        return Seq.seq(list).findSingle();
    }

    /**
     * 业务配置实例转换为业务对象
     *
     * @param bizConfig 业务配置实例
     * @param <T> 业务对象类型
     * @return 业务对象
     */
    private <T> T convert(BizConfig bizConfig, Class<T> type) {
        List<BizConfigProp> props = bizConfig.getProps();
        if (props.isEmpty()) {
            return JSON.parseObject("{}", type);
        }

        final StringBuilder sb = new StringBuilder("{");
        for (BizConfigProp prop : props) {
            if (prop.getValue() == null) {
                continue;
            }
            sb.append("\"").append(prop.getKey()).append("\":");
            switch (prop.getDataType()) {
                case BOOL, INT_32, INT_64, DOUBLE -> sb.append(prop.getValue()).append(",");
                case STRING -> sb.append("\"").append(prop.getValue()).append("\",");
                default -> sb.append(JSON.toJSONString(prop.getValue())).append(",");
            }
        }
        if (sb.length() <= 1) {
            return JSON.parseObject("{}", type);
        }
        sb.deleteCharAt(sb.length() - 1).append("}");
        return JSON.parseObject(sb.toString(), type);
    }

    private void initSchemas() {
        // 业务配置模式
        String schemas = resourceReader.readContent("classpath:biz_config_db/biz_config_schema.records.json");
        JSON.parseList(schemas, BizConfigSchema.class).forEach(schema -> {
            String schemaCode = schema.getCode();
            bizConfigSchemaMap.put(schemaCode, schema);
        });
    }

    private void initInstances(String location) {
        // 业务配置实例
        String content = resourceReader.readContent(location);
        List<BizConfig> bizConfigs = new ArrayList<>(JSON.parseList(content, BizConfig.class));

        bizConfigSchemaMap.keySet().forEach(schemaCode -> {
            List<BizConfig> schemaBizConfigs = bizConfigs.stream()
                    .filter(bizConfig -> schemaCode.equals(bizConfig.getSchemaCode()))
                    .collect(toList());
            bizConfigMap.compute(schemaCode, (key, value) -> {
                if (value == null) {
                    return schemaBizConfigs;
                }
                value.addAll(schemaBizConfigs);
                return value;
            });
        });
    }



    private final static BizConfig OPEN_AI_CHAT_COMPLETION_CONFIG = new BizConfig();

    private final static BizConfig AI_TALK_CONFIG = new BizConfig();

    static {
        // TODO: 2023/5/13 持久化 + 后台管理
        BizConfigProp temperature = new BizConfigProp()
                .setKey("temperature")
                .setDataType(PropDataType.DOUBLE)
                .setValue("0.7");
        BizConfigProp model = new BizConfigProp()
                .setKey("model")
                .setDataType(PropDataType.STRING)
                .setValue("gpt-3.5-turbo");
        BizConfigProp maxTokens = new BizConfigProp()
                .setKey("maxTokens")
                .setDataType(PropDataType.INT_32)
                .setValue("4000");
        BizConfigProp maxInputTokens = new BizConfigProp()
                .setKey("maxInputTokens")
                .setDataType(PropDataType.INT_32)
                .setValue("3000");
        OPEN_AI_CHAT_COMPLETION_CONFIG.setProps(List.of(temperature, model, maxTokens, maxInputTokens));
    }

    static {
        BizConfigProp provider = new BizConfigProp()
                .setKey("value")
                .setDataType(PropDataType.STRING)
                .setValue(AiTalkProvider.SELF_TALK.name());
        AI_TALK_CONFIG.setProps(List.of(provider));
    }
}
