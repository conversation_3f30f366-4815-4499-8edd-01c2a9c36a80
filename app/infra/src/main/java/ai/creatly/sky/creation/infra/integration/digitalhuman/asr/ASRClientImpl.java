/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.digitalhuman.asr;

import ai.creatly.sky.creation.domain.common.integration.asr.AsrClient;
import ai.creatly.sky.creation.domain.common.integration.asr.AsrRequest;
import ai.creatly.sky.creation.domain.common.integration.asr.AsrResponse;
import ai.creatly.sky.creation.domain.common.integration.asr.AsrSegment;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.infra.integration.digitalhuman.asr.api.AsrApi;
import com.jspeeder.core.data.problem.exception.SysException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version ASRClientImpl.java, v 0.1 2024-06-28 15:00 syoka
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ASRClientImpl implements AsrClient {

    private final static String SUCCESS_CODE = "200";

    private final AsrApi asrApi;

    @Override
    public List<AsrSegment> recognize(UserFile userFile) {
        // 调用whisper解析中文信息
        AsrRequest request = new AsrRequest()
                .setBucket(userFile.getBucket())
                .setAudioObjectKey(userFile.getKey());
        AsrResponse response;
        try {
            response = asrApi.recognizeContent(request);
        } catch (Exception e) {
            log.error("语音内容分段解析异常，request={}", request, e);
            throw new SysException("语音内容分段解析异常，request=" + request, e);
        }
        if (!StringUtils.equals(SUCCESS_CODE, response.getCode())) {
            log.error("语音内容分段解析失败，request={}，response={}", request, response);
            throw new SysException("语音内容分段解析失败，request=" + request + "，response=" + request);
        }
        return response.getData();
    }
}
