/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.credit;

import ai.creatly.sky.creation.domain.core.credit.cost.model.CreditCostRule;
import ai.creatly.sky.creation.domain.core.credit.cost.model.UnitsName;
import ai.creatly.sky.creation.infra.dal.common.ModelMapper;
import ai.creatly.sky.creation.infra.dal.common.UpdatableBuilder;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationFeatureCostRule;
import ai.creatly.sky.creation.infra.dal.tables.records.FeatureCostRuleRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.mapper.EnumValueMapping;
import org.mapstruct.Mapper;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_FEATURE_COST_RULE;

@Mapper(config = BaseMapperConfig.class)
public interface CreditCostRuleMapper extends ModelMapper<CreditCostRule, CreationFeatureCostRule, FeatureCostRuleRecord> {

    @Override
    CreationFeatureCostRule toEntity(CreditCostRule creditCostRule);

    @Override
    CreditCostRule toModel(CreationFeatureCostRule creationFeatureCostRule);

    @EnumValueMapping
    UnitsName toUnitsName(String unitsName);

    @Override
    default void updatable(UpdatableBuilder<FeatureCostRuleRecord> builder) {
        builder.updatable(CREATION_FEATURE_COST_RULE.COST)
                .updatable(CREATION_FEATURE_COST_RULE.UNITS_NAME)
                .updatable(CREATION_FEATURE_COST_RULE.UNITS_AMOUNT)
                .updatable(CREATION_FEATURE_COST_RULE.TASK_TYPE)
                .updatable(CREATION_FEATURE_COST_RULE.TASK_BIZ_TYPE);
    }
}
