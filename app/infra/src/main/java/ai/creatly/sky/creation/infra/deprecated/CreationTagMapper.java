/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.deprecated;

import ai.creatly.sky.creation.domain.deprecated.content.model.Tag;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationTag;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.repository.PojoMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


@Mapper(config = BaseMapperConfig.class)
public interface CreationTagMapper extends PojoMapper<Tag, CreationTag> {

    @Override
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "creatorId", ignore = true)
    @Mapping(target = "status", ignore = true)
    CreationTag toEntity(Tag credentials);

    @Override
    Tag toModel(CreationTag credentials);
}
