/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.http;

import ai.creatly.sky.creation.domain.common.http.RequestForwardClient;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.nio.charset.StandardCharsets;
import java.time.Duration;

/**
 * <AUTHOR>
 * @version RequestForwardClientImpl.java, v 0.1 2024-10-15 下午2:29 zhoudong
 */
@Component
@Slf4j
public class RequestForwardClientImpl implements RequestForwardClient {

    private final RestTemplate restTemplate;

    public RequestForwardClientImpl(RestTemplateBuilder restTemplateBuilder) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(Duration.ofSeconds(60))
                .setReadTimeout(Duration.ofSeconds(60))
                .build();
    }

    @Override
    public @Nullable String post(String endpoint, String path, @Nullable String query, @Nullable MultiValueMap<String, String> headers,
                                 @Nullable String body) {
        // 构建完整的URL，包括查询参数
        String url = UriComponentsBuilder.fromHttpUrl(endpoint).path(path).query(query).build().toString();
        log.info("[post]url={},headers={},body=\n{}", url, headers, body);

        // 创建一个新的HttpEntity，包含请求体和请求头
        HttpEntity<String> entity = new HttpEntity<>(body, headers);

        // 发送POST请求
        ResponseEntity<byte[]> response;
        try {
            response = restTemplate.postForEntity(url, entity, byte[].class);
        } catch (Exception e) {
            log.error("[post]error,url={}", url, e);
            throw new SysException(CommonErrorCode.REMOTE_CALL_ERROR, e);
        }
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("[post]fail,status={},url={}", response.getStatusCode(), url);
            throw new SysException(CommonErrorCode.REMOTE_CALL_ERROR);
        }

        // 返回响应体
        if (response.getBody() == null) {
            log.info("[post]success,url={},body is null", url);
            return null;
        }
        String responseBody = new String(response.getBody(), StandardCharsets.UTF_8);
        log.info("[post]success,url={},body={}", url, responseBody);
        return responseBody;
    }
}
