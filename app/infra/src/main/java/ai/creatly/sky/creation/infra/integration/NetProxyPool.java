/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration;

import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.net.Proxy;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version ProxyPool.java, 2024-12-09 下午6:25 zhoudong
 */
@Component
@RequiredArgsConstructor
public class NetProxyPool {

    private final List<NetProxy> netProxies;

    @Nullable
    public Proxy getProxy(@Nullable ProxyName proxyName) {
        if (proxyName == null) {
            return null;
        }
        return netProxies.stream()
                .filter(proxy -> proxy.name().equals(proxyName))
                .findFirst()
                .map(NetProxy::proxy)
                .orElse(null);
    }
}
