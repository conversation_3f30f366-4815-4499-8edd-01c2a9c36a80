/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.wechat.mp.model.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @version TemplateMessageSendRequest.java, 2024-11-02 下午9:26 zhoudong
 */
@Data
@Accessors(chain = true)
public class TemplateMessageSendRequest {

    @JsonProperty("touser")
    private String                  toUser;
    @JsonProperty("template_id")
    private String                  templateId;
    private String                  url;
    @JsonProperty("miniprogram")
    private MiniProgram             miniProgram;
    private Map<String, ParamValue> data;
    @JsonProperty("client_msg_id")
    private String                  clientMsgId;

    @Data
    @Accessors(chain = true)
    public static class MiniProgram {
        private String appid;
        @JsonProperty("pagepath")
        private String pagePath;
    }

    public record ParamValue(String value) {
    }
}
