/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.drama.ads.model.request;

import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * <AUTHOR>
 * @version DramaAdsDirectRequest.java, 2024-10-22 上午11:16 zhoudong
 */
@Data
@Accessors(chain = true)
public class DramaAdsDirectRequest {

    /**
     * 文件存储桶
     */
    private String              bucket;
    /**
     * 指定剧情ID
     */
    @Nullable
    private Long                dramaId;
    private String              productTitle;
    private String              productCategory;
    private String              crowdCategory;
    /**
     * 产品卖点（多个卖点用英文逗号隔开）
     */
    private String              productFeature;
    /**
     * 素材列表（素材ID <=> 素材文件OSS Key）
     */
    private List<DramaAdsAsset> assets;
    /**
     * 输出广告配音文件key
     */
    private String              outputAdsAudioKey;
}
