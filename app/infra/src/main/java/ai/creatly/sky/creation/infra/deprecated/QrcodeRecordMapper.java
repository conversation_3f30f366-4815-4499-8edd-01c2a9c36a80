/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.deprecated;

import ai.creatly.sky.creation.domain.deprecated.qrcode.QrcodeRecordInfo;
import ai.creatly.sky.creation.infra.dal.tables.pojos.QrcodeRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.repository.PojoMapper;
import com.jspeeder.core.util.json.JSON;
import org.jooq.JSONB;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version : QrcodeHistoryMapper.java, v 1.0 2023年07月21日 17时29分 syoka Exp$
 */
@Mapper(config = BaseMapperConfig.class)
public interface QrcodeRecordMapper extends PojoMapper<QrcodeRecordInfo, QrcodeRecord> {

    @Override
    @Mapping(target = "issuerId", source = "issuer")
    QrcodeRecord toEntity(QrcodeRecordInfo recordInfo);

    @Override
    @Mapping(target = "issuer", source = "issuerId")
    QrcodeRecordInfo toModel(QrcodeRecord recordInfo);

    default JSONB codeContentToJSONB(String codeContent) {
        if (codeContent == null) {
            return null;
        }
        return JSONB.valueOf(JSON.toJSONString(codeContent));
    }

    default String toCodeContent(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return JSON.parseObject(jsonb.data(), String.class);
    }
}
