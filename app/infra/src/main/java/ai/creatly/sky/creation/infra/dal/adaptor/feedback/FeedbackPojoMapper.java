/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.feedback;

import ai.creatly.sky.creation.domain.core.feedback.model.Feedback;
import ai.creatly.sky.creation.domain.core.feedback.model.FeedbackAttachment;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationFeedback;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.repository.PojoMapper;
import com.jspeeder.core.util.json.JSON;
import org.jooq.JSONB;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(config = BaseMapperConfig.class)
public interface FeedbackPojoMapper extends PojoMapper<Feedback, CreationFeedback> {

    @Override
    CreationFeedback toEntity(Feedback feedback);

    @Override
    Feedback toModel(CreationFeedback creationFeedback);

    default List<FeedbackAttachment> parseAttachments(JSONB attachments) {
        if (attachments == null) {
            return null;
        }
        return JSON.parseList(attachments.data(), FeedbackAttachment.class);
    }

    default JSONB attachmentsToJSONB(List<FeedbackAttachment> attachments) {
        if (attachments == null) {
            return null;
        }
        return JSONB.jsonb(JSON.toJSONString(attachments));
    }
}
