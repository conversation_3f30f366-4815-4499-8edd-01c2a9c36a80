/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.invitation;

import ai.creatly.sky.creation.domain.core.invitation.model.UserInvitation;
import ai.creatly.sky.creation.domain.core.invitation.service.UserInvitationRepository;
import ai.creatly.sky.creation.infra.dal.tables.daos.UserInvitationDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserInvitation;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_USER_INVITATION;

/**
 * <AUTHOR>
 * @version UserInvitationRepositoryImpl.java, v 0.1 2024-09-06 下午5:15 zhoudong
 */
@Repository
@RequiredArgsConstructor
public class UserInvitationRepositoryImpl implements UserInvitationRepository {

    private final DSLContext               dsl;
    private final UserInvitationDAO        userInvitationDAO;
    private final UserInvitationPojoMapper userInvitationPojoMapper;

    @Override
    public long create(UserInvitation userInvitation) {
        CreationUserInvitation creationUserInvitation = userInvitationPojoMapper.toEntity(userInvitation);
        userInvitationDAO.insert(creationUserInvitation);
        return Objects.requireNonNull(creationUserInvitation.getId());
    }

    @Override
    public Page<UserInvitation> queryPage(long inviterId, Pageable pageable) {
        // count
        long count = dsl.selectCount()
                .from(CREATION_USER_INVITATION)
                .where(CREATION_USER_INVITATION.INVITER_ID.eq(inviterId))
                .fetchSingleInto(long.class);

        // list
        List<UserInvitation> userInvitations = dsl.selectFrom(CREATION_USER_INVITATION)
                .where(CREATION_USER_INVITATION.INVITER_ID.eq(inviterId))
                .limit(pageable.getPageSize())
                .offset(pageable.getOffset())
                .fetchStreamInto(CreationUserInvitation.class)
                .map(userInvitationPojoMapper::toModel)
                .collect(Collectors.toList());

        return Page.of(userInvitations, pageable, count);
    }
}
