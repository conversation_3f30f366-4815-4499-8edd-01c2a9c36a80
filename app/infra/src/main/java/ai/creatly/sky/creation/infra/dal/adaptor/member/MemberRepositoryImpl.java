/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.member;

import ai.creatly.sky.creation.domain.core.member.model.Member;
import ai.creatly.sky.creation.domain.core.member.respository.MemberRepository;
import ai.creatly.sky.creation.infra.dal.tables.daos.MemberDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationMember;
import ai.creatly.sky.creation.infra.dal.tables.records.MemberRecord;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_MEMBER;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version MemberRepositoryImpl.java, v 0.1 2023-10-12 下午2:19 zhoudong
 */
@Repository
@RequiredArgsConstructor
public class MemberRepositoryImpl implements MemberRepository {

    private final DSLContext   dsl;
    private final MemberDAO    memberDAO;
    private final MemberMapper memberMapper;

    @Override
    public long create(Member member) {
        CreationMember creationMember = memberMapper.toEntity(member);
        if (Objects.isNull(creationMember.getId())) {
            creationMember.setId(IdHelper.getId());
        }
        memberDAO.insert(creationMember);
        return creationMember.getId();
    }

    @Override
    public void updateById(Member member) {
        MemberRecord memberRecord = memberMapper.toUpdatingRecord(member);
        dsl.update(CREATION_MEMBER)
                .set(memberRecord)
                .where(CREATION_MEMBER.ID.eq(member.getId()))
                .execute();
    }

    @Override
    public List<Member> lockByUid(long uid) {
        Asserts.isTrue(TransactionSynchronizationManager.isActualTransactionActive(), "必须在事务内获取行级锁");
        return dsl.selectFrom(CREATION_MEMBER)
                .where(CREATION_MEMBER.UID.eq(uid))
                .forUpdate()
                .fetchStreamInto(CreationMember.class)
                .map(memberMapper::toModel)
                .collect(toList());
    }

    @Override
    public Optional<Member> queryActiveByUid(long uid, ZonedDateTime now) {
        Asserts.notNull(now, "queryActiveByUid, current time must not be null");
        return dsl.selectFrom(CREATION_MEMBER)
                .where(CREATION_MEMBER.UID.eq(uid))
                // 生效时间
                .and(CREATION_MEMBER.EFFECT_AT.le(now))
                // 失效时间
                .and(CREATION_MEMBER.EXPIRE_AT.gt(now))
                .fetchOptionalInto(CreationMember.class)
                .map(memberMapper::toModel);
    }
}
