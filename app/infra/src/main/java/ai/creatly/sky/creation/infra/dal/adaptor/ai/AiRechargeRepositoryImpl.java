/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.ai;

import ai.creatly.sky.creation.domain.core.ai.tool.AiRecharge;
import ai.creatly.sky.creation.domain.core.ai.tool.AiRechargeRepository;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.support.trade.OrderBizStatus;
import ai.creatly.sky.creation.infra.dal.tables.daos.AiRechargeDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiRecharge;
import com.jspeeder.core.data.id.IdHelper;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_AI_RECHARGE;

/**
 * <AUTHOR>
 * @version AiToolsRepositoryImpl.java, v0.1 2025-02-20 09:44
 */
@RequiredArgsConstructor
@Repository
public class AiRechargeRepositoryImpl implements AiRechargeRepository {

    private final DSLContext dsl;
    private final AiRechargePojoMapper  aiRechargePojoMapper;
    private final AiRechargeDAO aiRechargeDAO;

    @Override
    public AiRecharge getTemplateByCredit(Long credit) {
        List<AiRecharge> list = dsl.selectFrom(CREATION_AI_RECHARGE)
                .where(CREATION_AI_RECHARGE.CREDIT.eq(credit).and(CREATION_AI_RECHARGE.OWNER_ID.isNull()))
                .orderBy(CREATION_AI_RECHARGE.UPDATED_AT.desc())
                .fetchStreamInto(CreationAiRecharge.class)
                .map(aiRechargePojoMapper::toModel)
                .toList();
        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public AiRecharge getRecharge(Long credit, UserContext context) {
        List<AiRecharge> list = dsl.selectFrom(CREATION_AI_RECHARGE)
                .where(CREATION_AI_RECHARGE.CREDIT.eq(credit).and(CREATION_AI_RECHARGE.OWNER_ID.eq(context.getUid())).and(CREATION_AI_RECHARGE.PAY_STATUS.eq(OrderBizStatus.NOTPAY.name())))
                .orderBy(CREATION_AI_RECHARGE.UPDATED_AT.desc())
                .fetchStreamInto(CreationAiRecharge.class)
                .map(aiRechargePojoMapper::toModel)
                .toList();
        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public AiRecharge getRechargeByOrderId(Long orderId, UserContext context) {
        List<AiRecharge> list = dsl.selectFrom(CREATION_AI_RECHARGE)
                .where(CREATION_AI_RECHARGE.ORDER_ID.eq(orderId).and(CREATION_AI_RECHARGE.OWNER_ID.eq(context.getUid())))
                .orderBy(CREATION_AI_RECHARGE.UPDATED_AT.desc())
                .fetchStreamInto(CreationAiRecharge.class)
                .map(aiRechargePojoMapper::toModel)
                .toList();
        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public AiRecharge getRechargeByOrderId(Long orderId) {
        List<AiRecharge> list = dsl.selectFrom(CREATION_AI_RECHARGE)
                .where(CREATION_AI_RECHARGE.ORDER_ID.eq(orderId))
                .orderBy(CREATION_AI_RECHARGE.UPDATED_AT.desc())
                .fetchStreamInto(CreationAiRecharge.class)
                .map(aiRechargePojoMapper::toModel)
                .toList();
        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public List<AiRecharge> getRechargeList() {
       return dsl.selectFrom(CREATION_AI_RECHARGE)
                .where(CREATION_AI_RECHARGE.OWNER_ID.isNull())
                .orderBy(CREATION_AI_RECHARGE.PRIORITY.asc())
                .fetchStreamInto(CreationAiRecharge.class)
                .map(aiRechargePojoMapper::toModel)
                .toList();
    }

    @Override
    public AiRecharge update(AiRecharge aiRecharge) {
        aiRecharge.setUpdatedAt(ZonedDateTime.now(ZoneId.systemDefault()));
        aiRechargeDAO.update(aiRechargePojoMapper.toEntity(aiRecharge));
        return aiRecharge;
    }

    @Override
    public AiRecharge createRecord(AiRecharge aiRecharge) {
        aiRecharge.setId(IdHelper.getId());
        aiRechargeDAO.insert(aiRechargePojoMapper.toEntity(aiRecharge));
        return aiRecharge;
    }
}
