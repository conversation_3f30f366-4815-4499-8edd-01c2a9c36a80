/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.ai.model.volcengine.config;

import com.volcengine.Pair;
import com.volcengine.sign.Credentials;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicReference;

/**
 *
 * <AUTHOR>
 * @version VolcEngineSignInterceptor.java, v0.1 2025-02-28 12:15
 */
@Slf4j
@Deprecated
public class VolcAiMusicSignInterceptor implements RequestInterceptor {

    private final Credentials credentials;
    private final String      region;
    private final String      service;

    public VolcAiMusicSignInterceptor(VolcAiMusicApiProperties properties, String region, String service) {
        this.credentials = Credentials.getCredentials(properties.getAccessKey(), properties.getSecretKey());
        this.region = region;
        this.service = service;
    }

    @Override
    public void apply(RequestTemplate template) {

        AtomicReference<String> action = new AtomicReference<>("GenSongV4");
        AtomicReference<String> version = new AtomicReference<>("2024-08-12");
        List<Pair> queryParams = new ArrayList<>();
        template.queries().forEach((name, values) -> {
            Pair nameValuePair = new Pair(name, values.iterator().next());
            queryParams.add(nameValuePair);
            if("Action".equals(nameValuePair.getName())){
                action.set(nameValuePair.getValue());
            }else if("Version".equals(nameValuePair.getName())){
                version.set(nameValuePair.getValue());
            }
        });

        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Host", "open.volcengineapi.com");
            headers.put("X-Date", Sign.getXDate(new Date()));
            headers.put("Content-Type", "application/json");
            headers.put("X-Content-Sha256", Sign.hashSHA256(template.body()));

            String auth = Sign.getAuthorization("POST", "/", headers, new HashMap<>(), action.get(), version.get(), credentials.getAccessKey(), credentials.getSecretKey(), region, service);
            template.header("X-Date", headers.get("X-Date"))
                    .header("Authorization", auth)
                    .header("X-Content-Sha256", headers.get("X-Content-Sha256"))
                    .header("Host", headers.get("Host"));

            log.info("body=" + template.body());
            log.info("X-Date=" + headers.get("X-Date"));
            log.info("Authorization=" + auth);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public class Sign{
        private static final BitSet URLENCODER = new BitSet(256);
        private static final String CONST_ENCODE = "0123456789ABCDEF";
        public static final Charset UTF_8 = StandardCharsets.UTF_8;


        static {
            int i;
            for (i = 97; i <= 122; ++i) {
                URLENCODER.set(i);
            }

            for (i = 65; i <= 90; ++i) {
                URLENCODER.set(i);
            }

            for (i = 48; i <= 57; ++i) {
                URLENCODER.set(i);
            }
            URLENCODER.set('-');
            URLENCODER.set('_');
            URLENCODER.set('.');
            URLENCODER.set('~');
        }

        public static String getAuthorization(String method, String path, Map<String, String> headers,
                                              Map<String, String> queryList, String action, String version, String ak, String sk, String region,
                                              String service)
                throws Exception {

            String xDate = headers.get("X-Date");
            assert xDate != null;
            String shortXDate = xDate.substring(0, 8);
            // 表明有哪些 header 字段参与签名。
            StringBuilder signHeader = new StringBuilder();
            for (String key : headers.keySet()) {
                if ("Authorization".equals(key)) {
                    continue;
                }
                signHeader.append(key.toLowerCase()).append(";");
            }
            signHeader.deleteCharAt(signHeader.length() - 1);
            System.out.println("===>signHeader:" + signHeader);
            String queryStr = getQuery(queryList, action, version);

            StringBuilder canonicalStringBuilder = new StringBuilder();
            canonicalStringBuilder.append(method).append("\n")
                    .append(path).append("\n")
                    .append(queryStr).append("\n");

            for (Entry<String, String> entry : headers.entrySet()) {
                if ("Authorization".equals(entry.getKey())) {
                    continue;
                }
                canonicalStringBuilder.append(entry.getKey().toLowerCase()).append(":").append(entry.getValue())
                        .append("\n");
            }
            canonicalStringBuilder.append("\n")
                    .append(signHeader).append("\n");
            String xContentSha256 = headers.get("X-Content-Sha256");
            if (xContentSha256 != null) {
                canonicalStringBuilder.append(xContentSha256);
            }
            System.out.println("===>canonicalStringBuilder:" + canonicalStringBuilder);

            String hashcanonicalString = hashSHA256(canonicalStringBuilder.toString().getBytes());
            String credentialScope = shortXDate + "/" + region + "/" + service + "/request";
            String signString = "HMAC-SHA256" + "\n" + xDate + "\n" + credentialScope + "\n" + hashcanonicalString;

            byte[] signKey = genSigningSecretKeyV4(sk, shortXDate, region, service);
            String signature = HexFormat.of().formatHex(hmacSHA256(signKey, signString));
            return "HMAC-SHA256" +
                    " Credential=" + ak + "/" + credentialScope +
                    ", SignedHeaders=" + signHeader +
                    ", Signature=" + signature;
        }

        public static String getXDate(Date date) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
            sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
            return sdf.format(date);
        }

        public static String getQuery(Map<String, String> query, String action, String version) {
            SortedMap<String, String> realQueryList = new TreeMap<>(query);
            realQueryList.put("Action", action);
            realQueryList.put("Version", version);
            StringBuilder querySB = new StringBuilder();
            for (String key : realQueryList.keySet()) {
                querySB.append(signStringEncoder(key)).append("=").append(signStringEncoder(realQueryList.get(key)))
                        .append("&");
            }
            querySB.deleteCharAt(querySB.length() - 1);
            return querySB.toString();
        }

        private static String signStringEncoder(String source) {
            if (source == null) {
                return null;
            }
            StringBuilder buf = new StringBuilder(source.length());
            ByteBuffer bb = UTF_8.encode(source);
            while (bb.hasRemaining()) {
                int b = bb.get() & 255;
                if (URLENCODER.get(b)) {
                    buf.append((char) b);
                } else if (b == 32) {
                    buf.append("%20");
                } else {
                    buf.append("%");
                    char hex1 = CONST_ENCODE.charAt(b >> 4);
                    char hex2 = CONST_ENCODE.charAt(b & 15);
                    buf.append(hex1);
                    buf.append(hex2);
                }
            }

            return buf.toString();
        }

        public static String hashSHA256(byte[] content) throws Exception {
            try {
                MessageDigest md = MessageDigest.getInstance("SHA-256");

                return HexFormat.of().formatHex(md.digest(content));
            } catch (Exception e) {
                throw new Exception(
                        "Unable to compute hash while signing request: "
                                + e.getMessage(), e);
            }
        }

        public static byte[] hmacSHA256(byte[] key, String content) throws Exception {
            try {
                Mac mac = Mac.getInstance("HmacSHA256");
                mac.init(new SecretKeySpec(key, "HmacSHA256"));
                return mac.doFinal(content.getBytes());
            } catch (Exception e) {
                throw new Exception(
                        "Unable to calculate a request signature: "
                                + e.getMessage(), e);
            }
        }

        private static byte[] genSigningSecretKeyV4(String secretKey, String date, String region, String service)
                throws Exception {
            byte[] kDate = hmacSHA256((secretKey).getBytes(), date);
            byte[] kRegion = hmacSHA256(kDate, region);
            byte[] kService = hmacSHA256(kRegion, service);
            return hmacSHA256(kService, "request");
        }

    }
}
