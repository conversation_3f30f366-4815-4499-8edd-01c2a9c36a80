/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.maliang.api;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version WeightRobinLoadBalancer.java, v 0.1 2023-12-13 17:40 syoka
 */
public class WeightedRoundRobinCounter {

    private final List<MaLiangApiContainer> containers;
    private final AtomicInteger             currentIndex = new AtomicInteger(-1);
    private final int                       totalWeight;

    public WeightedRoundRobinCounter(List<MaLiangApiContainer> containers) {
        this.containers = containers;
        this.totalWeight = calculateTotalWeight(containers);
    }

    private int calculateTotalWeight(List<MaLiangApiContainer> containers) {
        int total = 0;
        for (MaLiangApiContainer container : containers) {
            total += container.getWeight();
        }
        return total;
    }

    public MaLiangApiContainer next() {
        while (true) {
            int index = currentIndex.get();
            int nextIndex = calculateNextIndex();
            if (currentIndex.compareAndSet(index, nextIndex)) {
                return containers.get(nextIndex);
            }
        }
    }

    private int calculateNextIndex() {
        int randomWeightPoint = ThreadLocalRandom.current().nextInt(totalWeight);
        int weightSum = 0;

        for (int i = 0; i < containers.size(); i++) {
            weightSum += containers.get(i).getWeight();
            if (randomWeightPoint < weightSum) {
                return i;
            }
        }

        throw new IllegalStateException("Failed to calculate the next index");
    }
}
