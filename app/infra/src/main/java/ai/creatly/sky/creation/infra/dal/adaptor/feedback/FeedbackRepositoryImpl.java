/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.feedback;

import ai.creatly.sky.creation.domain.core.feedback.model.Feedback;
import ai.creatly.sky.creation.domain.core.feedback.repository.FeedbackRepository;
import ai.creatly.sky.creation.infra.dal.tables.daos.FeedbackDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationFeedback;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_FEEDBACK;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version : FeedbackRepositoryImpl.java, v 1.0 2023年12月09日 22时44分 heb
 */
@Repository
@RequiredArgsConstructor
public class FeedbackRepositoryImpl implements FeedbackRepository {

    private final DSLContext         dsl;
    private final FeedbackDAO        feedbackDAO;
    private final FeedbackPojoMapper feedbackMapper;

    @Override
    public long create(Feedback feedback) {
        CreationFeedback entity = feedbackMapper.toEntity(feedback);
        if (entity.getId() == null) {
            entity.setId(IdHelper.getId());
        }
        feedbackDAO.insert(entity);
        return entity.getId();
    }

    @Override
    public Optional<Feedback> queryOptionalById(long id) {
        return dsl.selectFrom(CREATION_FEEDBACK)
                .where(CREATION_FEEDBACK.ID.eq(id))
                .fetchOptionalInto(CreationFeedback.class)
                .map(feedbackMapper::toModel);
    }

    @Override
    public Page<Feedback> queryPage(String keyword, Pageable pageable) {
        Condition condition = DSL.trueCondition();
        if (StringUtils.isBlank(keyword)) {
            condition = condition.and(CREATION_FEEDBACK.DETAIL.contains(keyword));
        }
        // count
        long count = dsl.selectCount()
                .from(CREATION_FEEDBACK)
                .where(condition)
                .fetchSingleInto(Long.class);

        // list
        List<Feedback> feedbacks = dsl.selectFrom(CREATION_FEEDBACK)
                .where(condition)
                .orderBy(CREATION_FEEDBACK.CREATED_AT.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchInto(CreationFeedback.class)
                .stream()
                .map(feedbackMapper::toModel)
                .collect(toList());

        return Page.of(feedbacks, pageable, count);
    }
}
