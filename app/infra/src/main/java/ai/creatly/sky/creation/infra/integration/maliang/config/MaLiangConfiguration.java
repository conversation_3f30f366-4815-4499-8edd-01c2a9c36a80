/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.maliang.config;

import ai.creatly.sky.creation.domain.support.config.repository.SystemPreferenceRepository;
import ai.creatly.sky.creation.infra.integration.maliang.api.MaLiangApiContainer;
import ai.creatly.sky.creation.infra.integration.maliang.api.MaLiangProxyClientApi;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Feign;
import feign.Retryer;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.okhttp.OkHttpClient;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.toList;

@Configuration
@EnableConfigurationProperties(MaLiangProperties.class)
@RequiredArgsConstructor
public class MaLiangConfiguration {

    private final        ObjectMapper               objectMapper;
    private final        MaLiangProperties          maLiangProperties;
    private final        SystemPreferenceRepository systemPreferenceRepository;
    private static final String                     MJ_ACCOUNT_POOL = "MJ_ACCOUNT_POOL";

    @Bean
    public List<MaLiangApiContainer> midJourneyProxyClientApi() {
        List<MaLiangProperties.MaLiangEndpoint> endpoints = maLiangProperties.getEndpoints();
        String systemPreferences = systemPreferenceRepository.getSystemPreferences(MJ_ACCOUNT_POOL);

        // 二次加工
        if (StringUtils.isNotBlank(systemPreferences)) {
            Set<String> availableAccounts = Arrays.stream(systemPreferences.split(",")).collect(Collectors.toSet());
            endpoints = endpoints.stream().filter(e -> availableAccounts.contains(e.getAccountKey())).toList();
        }
        final OkHttpClient okHttpClient = feignOkHttpClient();


        final List<MaLiangProperties.MaLiangEndpoint> finalEndpoints = endpoints;

        return IntStream.range(0, finalEndpoints.size()).boxed().map(index -> {
                    MaLiangProperties.MaLiangEndpoint endpoint = finalEndpoints.get(index);
                    MaLiangProxyClientApi maLiangProxyClientApi = Feign.builder()
                            .encoder(new JacksonEncoder(objectMapper))
                            .decoder(new JacksonDecoder(objectMapper))
                            .client(okHttpClient)
                            .retryer(Retryer.NEVER_RETRY)
                            .target(MaLiangProxyClientApi.class, endpoint.getBaseUrl());

                    return MaLiangApiContainer.of(index, endpoint.getHostname(), endpoint.getAccountKey(), endpoint.getWeight(), maLiangProxyClientApi);
                })
                .collect(toList());
    }

    private OkHttpClient feignOkHttpClient() {
        okhttp3.OkHttpClient delegate = new okhttp3.OkHttpClient.Builder()
                // 建立连接超时（库默认10s）
                .connectTimeout(Duration.ofSeconds(5))
                // 请求服务器超时（库默认10s）
                .writeTimeout(Duration.ofSeconds(5))
                // 等待服务器响应超时（库默认10s）
                .readTimeout(Duration.ofSeconds(20))
                // 总体调用耗时（库默认永不超时）：包含DNS解析+请求+响应
                .callTimeout(Duration.ofSeconds(25))
                .build();
        return new OkHttpClient(delegate);
    }
}
