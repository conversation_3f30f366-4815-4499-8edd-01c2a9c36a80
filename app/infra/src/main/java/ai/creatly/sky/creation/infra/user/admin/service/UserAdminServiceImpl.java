package ai.creatly.sky.creation.infra.user.admin.service;

import ai.creatly.sky.creation.domain.core.credit.model.CreditDelta;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLog;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.repository.CreditLogRepository;
import ai.creatly.sky.creation.domain.core.user.admin.model.UserQuery;
import ai.creatly.sky.creation.domain.core.user.admin.service.UserAdminService;
import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import java.util.List;

import static ai.creatly.sky.creation.infra.dal.Tables.USER;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 */
@Service
public class UserAdminServiceImpl implements UserAdminService {

    @Autowired
    DSLContext dsl;
    @Autowired
    CreditLogRepository creditLogRepository;

    @Override
    public Page<UserInfo> searchUser(Pageable pageable, UserQuery query){
        Condition condition = DSL.trueCondition();
        if (StringUtils.isNotEmpty(query.getKeywords())) {
            condition = condition.and(USER.PHONE.like(query.getKeywords()).or(USER.FULL_NAME.like(query.getKeywords())).or(USER.USERNAME.like(query.getKeywords())));
        }

        long count = dsl.selectCount()
                .from(USER)
                .where(condition)
                .fetchSingleInto(long.class);
        if (count == 0) {
            return null;
        }

        List<UserInfo> users = dsl.selectFrom(USER)
                .where(condition)
                .orderBy(USER.UPDATED_AT.desc())
                .limit(pageable.getPageSize())
                .offset(pageable.getOffset())
                .fetchStreamInto(UserInfo.class)
                .collect(toList());

        return Page.of(users, pageable, count);
    }

    @Override
    public Boolean addCreditLog(long uid, int amount, long creditId, int balance) {

        CreditDelta creditDelta = new CreditDelta()
                .setCreditId(creditId)
                .setAmount(amount)
                .setBalance(balance + amount);

        CreditLog creditLog = new CreditLog();
        creditLog.setUid(uid);
        creditLog.setAmount(amount);
        creditLog.setBizType(CreditLogBizType.ADMIN_RECHARGE);
        creditLog.setType(CreditLog.INCOME);
        creditLog.setCreditDeltas(List.of(creditDelta));
        creditLog.setBizNo(IdHelper.getPrettyId());

        try {
            creditLogRepository.create(creditLog);
        } catch (Exception e) {
            return false;
        }
        return true;

    }
}
