/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.asset.config;

import ai.creatly.sky.creation.infra.integration.ProxyName;
import lombok.Data;
import org.jetbrains.annotations.Nullable;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version AssetDescribeProperties.java, 2024-10-22 下午9:00 zhoudong
 */
@Data
@ConfigurationProperties(prefix = "application.asset.describe")
public class AssetDescribeProperties {
    private String    baseUrl;
    @Nullable
    private ProxyName proxy;
}
