package ai.creatly.sky.creation.infra.ai.audio;

import ai.creatly.sky.creation.domain.core.ai.audio.AiAudioCloneClient;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiAudioCosyVoiceBizErrorCode;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiAudioTaskResult;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioCloneTaskInput;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioTtsErrorCode;
import ai.creatly.sky.creation.domain.core.ai.model.OrderedAsset;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskBizExecStatus;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.infra.ai.model.bailian.config.BailianProperties;
import com.alibaba.dashscope.audio.ttsv2.enrollment.Voice;
import com.alibaba.dashscope.audio.ttsv2.enrollment.VoiceEnrollmentService;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.problem.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiAudioCloneServiceImpl implements AiAudioCloneClient {

    @Autowired
    UserFileHelper userFileHelper;

    private final BailianProperties bailianProperties;

    private final ObjectMapper objectMapper;

    public String cloneVoiceBL(String voicePrefix, String audioUrl) throws NoApiKeyException, InputRequiredException {
        String targetModel = "cosyvoice-v2";
        String apiKey = bailianProperties.getApiKey();
        VoiceEnrollmentService service = new VoiceEnrollmentService(apiKey);
        Voice myVoice = service.createVoice(targetModel, voicePrefix, audioUrl);
        if (myVoice == null) {
            throw new BizException(AudioTtsErrorCode.AUDIO_CLONE_FAILED);
        }
        return myVoice.getVoiceId();
    }

    @Override
    public AiAudioTaskResult generate(AudioCloneTaskInput input, UserContext userContext) {

        String fullId = IdHelper.getPrettyId();
        AiAudioTaskResult aiAudioTaskResult = new AiAudioTaskResult();
        try {
            String code = this.cloneVoiceBL(fullId.length() > 10 ? fullId.substring(4, 14) : fullId, userFileHelper.getHttpUrl(input.getRequest().getAudioFile().getUrl(), userContext.getUid()));
            if (StringUtils.isNotEmpty(code)) {
                //封装url到OrderedAudioAsset
                List<OrderedAsset> assetList = new ArrayList<>();
                OrderedAsset orderedAsset = new OrderedAsset();
                orderedAsset.setUrl(input.getRequest().getAudioFile().getUrl());
                orderedAsset.setId(code);
                orderedAsset.setCoverUrl(input.getRequest().getPhotoFile().getUrl());
                orderedAsset.setInfo(input.getRequest().getAudioName());
                orderedAsset.setOrder(0);
                assetList.add(orderedAsset);
                aiAudioTaskResult.setAssets(assetList);
                aiAudioTaskResult.setBizExecStatus(AiTaskBizExecStatus.SUCCEED);
                return aiAudioTaskResult;
            }
        } catch (NoApiKeyException e) {
            aiAudioTaskResult.setBizExecStatus(AiTaskBizExecStatus.FAILED);
            aiAudioTaskResult.setErrorMsg(AiAudioCosyVoiceBizErrorCode.getErrorMsg(AiAudioCosyVoiceBizErrorCode.ERROR_CODE_401.getCode()));
            log.error("克隆音色ApiKey异常：{ }", e);
            return aiAudioTaskResult;
        } catch (InputRequiredException e) {
            aiAudioTaskResult.setBizExecStatus(AiTaskBizExecStatus.FAILED);
            aiAudioTaskResult.setErrorMsg(AiAudioCosyVoiceBizErrorCode.getErrorMsg(AiAudioCosyVoiceBizErrorCode.ERROR_CODE_400.getCode()));
            log.error("克隆音色请求参数异常：{ }", e);
            return aiAudioTaskResult;
        } catch (Exception e) {
            log.error("克隆音色发生异常：{ }", e);
            // 尝试提取 JSON 信息
            String errorMessage = e.getMessage();
            if (errorMessage != null) {
                int jsonStartIndex = errorMessage.indexOf('{');
                if (jsonStartIndex != -1) {
                    String jsonPart = errorMessage.substring(jsonStartIndex);
                    try {
                        JsonNode rootNode = objectMapper.readTree(jsonPart);
                        String statusCode = rootNode.path("statusCode").asText();
                        String message = rootNode.path("message").asText();
                        String code = rootNode.path("code").asText();
                        if ("valid audio too short!".equals(message)) {
                            aiAudioTaskResult.setErrorMsg(AiAudioCosyVoiceBizErrorCode.getErrorMsg(AiAudioCosyVoiceBizErrorCode.ERROR_CODE_3.getCode()));
                        } else {
                            aiAudioTaskResult.setErrorMsg(AiAudioCosyVoiceBizErrorCode.getErrorMsg(code));
                        }
                        aiAudioTaskResult.setBizExecStatus(AiTaskBizExecStatus.FAILED);
                        log.error("音色克隆异常信息解析成功 - 状态码: {}, 错误信息: {}, 错误码: {}",statusCode, message, code);
                        return aiAudioTaskResult;
                    } catch (Exception jsonEx) {
                        log.error("音色克隆异常信息为JSON,解析失败", jsonEx);
                    }
                } else {
                    log.error("音色克隆异常信息非JSON,解析失败");
                }
            }
        }
        //使用兜底错误码
        aiAudioTaskResult.setBizExecStatus(AiTaskBizExecStatus.FAILED);
        aiAudioTaskResult.setErrorMsg(AiAudioCosyVoiceBizErrorCode.getErrorMsg(AiAudioCosyVoiceBizErrorCode.ERROR_CODE_1.getCode()));
        return aiAudioTaskResult;
    }

    @Override
    public CreditsExpense expense(AiTask aiTask) {
        // TODO 计算消耗元气
        return CreditsExpense.builder()
                .uid(aiTask.getOwnerId())
                .type(CreditAccountType.GENERAL)
                .amount(0)
                // 完成一条创作任务，扣除一次费用
                .bizType(CreditLogBizType.AUDIO_CLONE)
                .bizNo(aiTask.getId().toString())
                .build();
    }

    @Override
    public AiAudioTaskResult queryGeneration(String taskId, UserContext userContext) {
         return null;
    }

    @Override
    public AiAudioTaskResult queryGeneration(AiTask aiTask, UserContext userContext) {
        return null;
    }
}
