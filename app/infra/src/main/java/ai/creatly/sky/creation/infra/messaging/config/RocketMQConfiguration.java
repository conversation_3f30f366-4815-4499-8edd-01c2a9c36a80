/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.messaging.config;

import ai.creatly.sky.creation.domain.common.env.RuntimeEnv;
import ai.creatly.sky.creation.domain.common.messaging.consumer.MQListener;
import ai.creatly.sky.creation.domain.common.messaging.producer.MessagingTemplate;
import ai.creatly.sky.creation.domain.common.messaging.producer.transaction.TransactionCallback;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import ai.creatly.sky.creation.infra.messaging.consumer.ConsumerLifecycle;
import ai.creatly.sky.creation.infra.messaging.consumer.ConsumerLifecycleImpl;
import ai.creatly.sky.creation.infra.messaging.consumer.RocketMQConsumerProperties;
import ai.creatly.sky.creation.infra.messaging.producer.RocketMQMessagingTemplateImpl;
import ai.creatly.sky.creation.infra.messaging.producer.RocketMQProducerFactory;
import ai.creatly.sky.creation.infra.messaging.producer.RocketMQProducerFactoryImpl;
import ai.creatly.sky.creation.infra.messaging.producer.RocketMQProducerProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 客户端配置
 *
 * <AUTHOR>
 * @version RocketMQConfiguration.java, v 0.1 2023-10-26 下午9:36 zhoudong
 */
@Slf4j
@Configuration
@EnableConfigurationProperties({
        RocketMQProperties.class,
        RocketMQProducerProperties.class,
        RocketMQConsumerProperties.class
})
@RequiredArgsConstructor
public class RocketMQConfiguration {

    private final RocketMQProperties rocketMQProperties;
    private final AppAlertHelper     appAlertHelper;

    static {
        // 使用应用运行时的 SLF4J 打印日志，而不是 RocketMQ 内置的日志系统
        System.setProperty("rocketmq.client.logUseSlf4j", "true");
    }

    @Bean
    public RocketMQProducerFactory rocketMQProducerFactory(ObjectProvider<TransactionCallback<?>> callbackProvider) {
        return new RocketMQProducerFactoryImpl(rocketMQProperties, callbackProvider);
    }

    @Bean
    public MessagingTemplate messagingTemplate(RocketMQProducerFactory rocketMQProducerFactory) {
        return new RocketMQMessagingTemplateImpl(rocketMQProducerFactory, appAlertHelper);
    }

    @Configuration
    @RequiredArgsConstructor
    static class RocketMQConsumerConfiguration implements InitializingBean, DisposableBean {

        private final RuntimeEnv                    runtimeEnv;
        private final AppAlertHelper                appAlertHelper;
        private final RocketMQProperties            rocketMQProperties;
        private final RocketMQConsumerProperties    rocketMQConsumerProperties;
        private final ObjectProvider<MQListener<?>> rocketMQListenerProvider;

        private ConsumerLifecycle consumerLifecycle;

        @Override
        public void afterPropertiesSet() {
            Boolean enabled = rocketMQConsumerProperties.getEnabled();
            if (!Boolean.TRUE.equals(enabled)) {
                // 本地环境默认不监听消息
                log.info("[start consumer]all message listeners are disabled.");
            }

            List<String> enabledListeners = rocketMQConsumerProperties.getEnabledListeners();
            if (runtimeEnv.isLocal() && Boolean.TRUE.equals(enabled) && CollectionUtils.size(enabledListeners) != 1) {
                // 本地环境一旦打开消息监听，则只能调试一个监听器
                log.error("[start consumer]only one message listener can be enabled on local env!");
                throw new IllegalStateException("only one message listener can be enabled on local env!");
            }

            consumerLifecycle = new ConsumerLifecycleImpl(
                    rocketMQProperties,
                    rocketMQConsumerProperties,
                    appAlertHelper,
                    rocketMQListenerProvider
            );
            consumerLifecycle.startConsumers();
        }

        @Override
        public void destroy() {
            consumerLifecycle.stopConsumers();
        }
    }
}
