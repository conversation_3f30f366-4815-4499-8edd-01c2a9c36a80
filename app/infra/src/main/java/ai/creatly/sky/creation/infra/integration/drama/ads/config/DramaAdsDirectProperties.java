/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.drama.ads.config;

import ai.creatly.sky.creation.infra.integration.ProxyName;
import lombok.Data;
import org.jetbrains.annotations.Nullable;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version DramaAdsDirectProperties.java, 2024-10-22 上午11:56 zhoudong
 */
@Data
@ConfigurationProperties(prefix = "application.drama.ads-direct")
public class DramaAdsDirectProperties {
    private String    baseUrl;
    @Nullable
    private ProxyName proxy;
}
