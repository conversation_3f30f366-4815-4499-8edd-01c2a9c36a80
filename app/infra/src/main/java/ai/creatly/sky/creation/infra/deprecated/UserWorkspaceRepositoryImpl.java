/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.deprecated;

import ai.creatly.sky.creation.domain.common.caching.CachingTemplate;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.deprecated.workspace.model.UserWorkspace;
import ai.creatly.sky.creation.domain.deprecated.workspace.model.UserWorkspaceDigest;
import ai.creatly.sky.creation.domain.deprecated.workspace.model.WorkspaceSynchronize;
import ai.creatly.sky.creation.domain.deprecated.workspace.model.request.WorkspaceCompositeQuery;
import ai.creatly.sky.creation.domain.deprecated.workspace.service.UserWorkspaceRepository;
import ai.creatly.sky.creation.infra.dal.Tables;
import ai.creatly.sky.creation.infra.dal.common.JSONBMapper;
import ai.creatly.sky.creation.infra.dal.tables.daos.UserWorkspaceDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserWorkspace;
import ai.creatly.sky.creation.infra.dal.tables.records.UserWorkspaceRecord;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.util.json.JSON;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_AI_TASK;
import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_USER_WORKSPACE;

/**
 * <AUTHOR>
 * @version UserWorkspaceRepositoryImpl.java, v 0.1 2023-09-24 11:06 syoka
 */
@Deprecated
@Repository
@RequiredArgsConstructor
public class UserWorkspaceRepositoryImpl implements UserWorkspaceRepository {

    private final UserWorkspaceDAO    userWorkspaceDAO;
    private final DSLContext          dsl;
    private final UserWorkspaceMapper userWorkspaceMapper;
    private final JSONBMapper         jsonbMapper;
    private final CachingTemplate     cachingTemplate;

    @Override
    public Page<UserWorkspaceDigest> getUserWorkspaceDigest(Long uid, WorkspaceCompositeQuery query, Pageable pageable) {


        Condition condition = DSL.noCondition();

        if (Objects.nonNull(uid)) {
            condition = condition.and(CREATION_USER_WORKSPACE.UID.contains(uid));
        }

        if (Objects.nonNull(query) && StringUtils.isNotEmpty(query.getName())) {
            condition = condition.and(CREATION_USER_WORKSPACE.NAME.contains(query.getName()));
        }

        Long total = dsl.selectCount()
                .from(Tables.CREATION_USER_WORKSPACE)
                .where(condition)
                .fetchSingleInto(Long.class);

        if (Objects.isNull(total)) {
            return Page.empty(pageable.getPageNumber(), pageable.getPageSize());
        }

        List<UserWorkspaceDigest> userWorkspaceList = dsl.selectFrom(Tables.CREATION_USER_WORKSPACE)
                .where(condition)
                .orderBy(Tables.CREATION_USER_WORKSPACE.CREATED_AT.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchStreamInto(CreationUserWorkspace.class)
                .map(userWorkspaceMapper::toDigestModel)
                .collect(Collectors.toList());

        return Page.of(userWorkspaceList, pageable, total);
    }

    @Override
    public Optional<UserWorkspace> findWorkspaceById(Long workspaceId, Long uid) {
        return dsl.selectFrom(Tables.CREATION_USER_WORKSPACE)
                .where(Tables.CREATION_USER_WORKSPACE.UID.eq(uid))
                .and(Tables.CREATION_USER_WORKSPACE.ID.eq(workspaceId))
                .fetchOptionalInto(CreationUserWorkspace.class)
                .map(userWorkspaceMapper::toModel);
    }

    @Override
    public void deleteUserWorkspace(Long workspaceId, Long uid) {
        dsl.deleteFrom(Tables.CREATION_USER_WORKSPACE)
                .where(Tables.CREATION_USER_WORKSPACE.UID.eq(uid))
                .and(Tables.CREATION_USER_WORKSPACE.ID.eq(workspaceId))
                .execute();
    }

    @Override
    public void updateUserWorkspace(UserWorkspace workspace) {
        UserWorkspaceRecord record = userWorkspaceMapper.toUpdatingRecord(workspace);
        dsl.update(CREATION_USER_WORKSPACE)
                .set(record)
                .where(CREATION_USER_WORKSPACE.ID.eq(workspace.getId()))
                .and(CREATION_USER_WORKSPACE.UID.eq(workspace.getUid()))
                .execute();
    }

    @Override
    public String createUserWorkspace(UserWorkspace userWorkspace) {
        CreationUserWorkspace entity = userWorkspaceMapper.toEntity(userWorkspace);
        if (Objects.isNull(entity.getId())) {
            entity.setId(IdHelper.getId());
        }
        Map<String, String> map = new HashMap<>();
        entity.setCreator(JSONB.valueOf(JSON.toJSONString(map)));
        userWorkspaceDAO.insert(entity);
        return String.valueOf(entity.getId());
    }

    @Override
    public void synchronizeWorkspace(String workspaceId, WorkspaceSynchronize model) {
        String redisKey = "workspace:render";
        model.setWorkspaceId(workspaceId);
        cachingTemplate.listLeftPush(redisKey, JSON.toJSONString(model), Duration.ofMinutes(10));
    }

    @Override
    public String getSynchronizeWorkspaceProgress(String workspaceId) {
        String redisKey = "workspace:" + workspaceId + ":result";
        return cachingTemplate.getString(redisKey);
    }

    @Override
    public String queryOSSURIByOwnerIdAndWorkspaceId(long ownerId, String workspaceId) {
        return dsl.select(CREATION_AI_TASK.BIZ_RESULT)
                .from(CREATION_AI_TASK)
                .where(CREATION_AI_TASK.OWNER_ID.eq(ownerId))
                .and(CREATION_AI_TASK.TASK_TYPE.eq(AiTaskType.COLLABORATION_CREATION.name()))
                .and(CREATION_AI_TASK.STATUS.in(AiTaskStatus.notDeleted()))
                .and(DSL.jsonbGetAttributeAsText(CREATION_AI_TASK.BIZ_PARAMS, "workspaceId").eq(workspaceId))
                .orderBy(CREATION_AI_TASK.UPDATED_AT.desc())
                .limit(1)
                .fetchOptionalInto(JSONB.class)
                .map(jsonbMapper::toJSONObject)
                .map(jsonObject -> jsonObject.optString("ossUri", StringPool.EMPTY))
                .orElse(StringPool.EMPTY);
    }
}
