/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.digitalhuman;

import ai.creatly.sky.creation.domain.core.digitalhuman.model.DigitalHumanVideo;
import ai.creatly.sky.creation.domain.core.digitalhuman.repository.DigHumanVideoRepository;
import ai.creatly.sky.creation.infra.dal.Tables;
import ai.creatly.sky.creation.infra.dal.tables.daos.DigHumanVideoDAO;
import ai.creatly.sky.creation.infra.dal.tables.pojos.DigHumanVideo;
import ai.creatly.sky.creation.infra.dal.tables.records.DigHumanVideoRecord;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version DigHumanVideoRepositoryImpl.java, v 0.1 2024-05-25 15:32 syoka
 */
@Repository
@RequiredArgsConstructor
@Deprecated
public class DigHumanVideoRepositoryImpl implements DigHumanVideoRepository {

    private final DSLContext          dsl;
    private final DigHumanVideoMapper digHumanVideoMapper;
    private final DigHumanVideoDAO    digHumanVideoDAO;

    @Override
    public void create(DigitalHumanVideo video) {
        if (Objects.isNull(video.getId())) {
            video.setId(IdHelper.getId());
        }
        DigHumanVideo entity = digHumanVideoMapper.toEntity(video);
        digHumanVideoDAO.insert(entity);
    }

    @Override
    public void updateById(DigitalHumanVideo video) {
        DigHumanVideoRecord videoRecord = digHumanVideoMapper.toUpdatingRecord(video);
        if (videoRecord.changed()) {
            dsl.update(Tables.DIG_HUMAN_VIDEO)
                    .set(videoRecord)
                    .where(Tables.DIG_HUMAN_VIDEO.ID.eq(video.getId()))
                    .execute();
        }
    }

    @Override
    public Optional<DigitalHumanVideo> queryOptionalById(long id) {
        return digHumanVideoDAO.fetchOptionalById(id).map(digHumanVideoMapper::toModel);
    }

    @Override
    public Optional<DigitalHumanVideo> queryOptionalByUidAndId(long uid, long id) {
        return dsl.selectFrom(Tables.DIG_HUMAN_VIDEO)
                .where(Tables.DIG_HUMAN_VIDEO.UID.eq(uid))
                .and(Tables.DIG_HUMAN_VIDEO.ID.eq(id))
                .and(Tables.DIG_HUMAN_VIDEO.STATUS.notEqual(DigitalHumanVideo.DELETE))
                .fetchOptionalInto(DigHumanVideo.class)
                .map(digHumanVideoMapper::toModel);
    }

    @Override
    public Page<DigitalHumanVideo> queryPageDigHumanVideo(long uid, Pageable pageable) {
        Long total = dsl.selectCount()
                .from(Tables.DIG_HUMAN_VIDEO)
                .where(Tables.DIG_HUMAN_VIDEO.UID.eq(uid))
                .and(Tables.DIG_HUMAN_VIDEO.STATUS.notEqual(DigitalHumanVideo.DELETE))
                .fetchSingleInto(Long.class);

        if (total == 0) {
            return Page.empty(pageable);
        }

        List<DigitalHumanVideo> content = dsl.selectFrom(Tables.DIG_HUMAN_VIDEO)
                .where(Tables.DIG_HUMAN_VIDEO.UID.eq(uid))
                .and(Tables.DIG_HUMAN_VIDEO.STATUS.notEqual(DigitalHumanVideo.DELETE))
                .orderBy(Tables.DIG_HUMAN_VIDEO.CREATED_AT.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchInto(DigHumanVideo.class)
                .stream()
                .map(digHumanVideoMapper::toModel)
                .collect(Collectors.toList());

        return Page.of(content, pageable, total);
    }

    @Override
    public void deleteById(long id) {
        dsl.update(Tables.DIG_HUMAN_VIDEO)
                .set(Tables.DIG_HUMAN_VIDEO.STATUS, DigitalHumanVideo.DELETE)
                .where(Tables.DIG_HUMAN_VIDEO.ID.eq(id))
                .execute();
    }
}
