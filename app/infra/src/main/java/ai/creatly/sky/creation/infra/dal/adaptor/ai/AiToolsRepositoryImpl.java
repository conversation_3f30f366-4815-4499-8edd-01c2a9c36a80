/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.ai;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.ai.tool.AiTool;
import ai.creatly.sky.creation.domain.core.ai.tool.AiToolEnum;
import ai.creatly.sky.creation.domain.core.ai.tool.AiToolRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiTool;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_AI_TOOL;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version AiToolsRepositoryImpl.java, v0.1 2025-02-20 09:44
 */
@RequiredArgsConstructor
@Repository
public class AiToolsRepositoryImpl implements AiToolRepository {

    private final        DSLContext                  dsl;
    private final        AiToolPojoMapper            aiToolPojoMapper;
    private final        UserFileHelper              userFileHelper;

    private static final Cache<String, List<AiTool>> AI_TOOL_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    private static final Cache<String,Integer> AI_TOOL_CREDITS_CACHE = Caffeine.newBuilder().expireAfterWrite(5,TimeUnit.MINUTES).build();

    @Override
    public List<AiTool> queryLoraTools() {
        List<AiTool> aiTools = AI_TOOL_CACHE.getIfPresent("queryLoraTools");
        if (aiTools != null) {
            return aiTools;
        }
        aiTools = dsl.selectFrom(CREATION_AI_TOOL)
                .where(CREATION_AI_TOOL.CATEGORY.eq(AiToolEnum.lora.name()))
                .and(CREATION_AI_TOOL.HIDDEN.eq((short) 0))
                .orderBy(CREATION_AI_TOOL.PRIORITY.desc())
                .fetchStreamInto(CreationAiTool.class)
                .map(aiToolPojoMapper::toModel).peek(x->{
                    if(x.getCoverUrl().startsWith("oss")){
                        x.setCoverUrl(userFileHelper.getHttpUrl(x.getCoverUrl(), FileAcl.PRIVATE));
                    }
                }).collect(toList());
        AI_TOOL_CACHE.put("queryLoraTools", aiTools);
        return aiTools;
    }

    @Override
    public String getParam(String modelId) {

        return dsl.select(CREATION_AI_TOOL.INIT_PARAM)
                .from(CREATION_AI_TOOL)
                .where(CREATION_AI_TOOL.MODEL.eq(modelId))
                .orderBy(CREATION_AI_TOOL.PRIORITY.desc())
                .fetchInto(String.class)
                .getFirst();
    }

    @Override
    public List<AiTool> queryTools() {
        List<AiTool> aiTools = AI_TOOL_CACHE.getIfPresent("queryTools");
        if (aiTools != null) {
            return aiTools;
        }
        aiTools = dsl.selectFrom(CREATION_AI_TOOL)
                .where(CREATION_AI_TOOL.CATEGORY.eq(AiToolEnum.audio.name()))
                .or(CREATION_AI_TOOL.CATEGORY.eq(AiToolEnum.image.name()))
                .or(CREATION_AI_TOOL.CATEGORY.eq(AiToolEnum.video.name()))
                .orderBy(CREATION_AI_TOOL.PRIORITY.desc())
                .fetchStreamInto(CreationAiTool.class)
                .map(aiToolPojoMapper::toModel)
                .collect(toList());
        AI_TOOL_CACHE.put("queryTools", aiTools);
        return aiTools;
    }

    /**
     * 获取配置AI工具基础消耗元气
     * @param model
     * @param bizType
     * @param function
     * @return
     */
    public Integer getBaseCredits(String model, String bizType, String function) {

        String key = model + "_" + bizType + "_" + function;
        Integer credits = AI_TOOL_CREDITS_CACHE.getIfPresent(key);
        if (credits != null) {
            return credits;
        }

        Condition condition = DSL.trueCondition();
        if (StringUtils.isNotEmpty(model)) {
            condition = condition.and(CREATION_AI_TOOL.MODEL.eq(model));
        }
        if (StringUtils.isNotEmpty(bizType)) {
            condition = condition.and(CREATION_AI_TOOL.BIZ_TYPE.eq(bizType));
        }
        if (StringUtils.isNotEmpty(function)) {
            condition = condition.and(CREATION_AI_TOOL.FUNCTION.eq(function));
        }

        List<AiTool> aiTools = dsl.selectFrom(CREATION_AI_TOOL).where(condition).fetchStreamInto(CreationAiTool.class)
                .map(aiToolPojoMapper::toModel)
                .collect(toList());
        if (aiTools != null && !aiTools.isEmpty()) {
            AI_TOOL_CREDITS_CACHE.put(key, aiTools.getFirst().getCredits() == null ? 0 : aiTools.getFirst().getCredits().intValue());
            return aiTools.getFirst().getCredits() == null ? 0 : aiTools.getFirst().getCredits().intValue();
        }
        return 0;
    }

    @Override
    public List<AiTool> queryImageTools() {
        List<AiTool> aiTools = AI_TOOL_CACHE.getIfPresent("queryImageTools");
        if (aiTools != null) {
            return aiTools;
        }
        aiTools = dsl.selectFrom(CREATION_AI_TOOL)
                .where(CREATION_AI_TOOL.CATEGORY.eq(AiToolEnum.image.name()))
                .orderBy(CREATION_AI_TOOL.PRIORITY.desc())
                .fetchStreamInto(CreationAiTool.class)
                .map(aiToolPojoMapper::toModel)
                .collect(toList());
        AI_TOOL_CACHE.put("queryImageTools", aiTools);
        return aiTools;
    }

    @Override
    public List<AiTool> queryIndexTools() {
        List<AiTool> aiTools = AI_TOOL_CACHE.getIfPresent("queryIndexTools");
        if (aiTools != null) {
            return aiTools;
        }
        aiTools = dsl.selectFrom(CREATION_AI_TOOL)
                .where(CREATION_AI_TOOL.CATEGORY.eq(AiToolEnum.index.name()))
                .orderBy(CREATION_AI_TOOL.PRIORITY.desc())
                .fetchStreamInto(CreationAiTool.class)
                .map(aiToolPojoMapper::toModel)
                .collect(toList());
        AI_TOOL_CACHE.put("queryIndexTools", aiTools);
        return aiTools;
    }

    @Override
    public List<AiTool> queryAudioTools() {
        List<AiTool> aiTools = AI_TOOL_CACHE.getIfPresent("queryAudioTools");
        if (aiTools != null) {
            return aiTools;
        }
        aiTools = dsl.selectFrom(CREATION_AI_TOOL)
                .where(CREATION_AI_TOOL.CATEGORY.eq(AiToolEnum.audio.name()))
                .orderBy(CREATION_AI_TOOL.PRIORITY.desc())
                .fetchStreamInto(CreationAiTool.class)
                .map(aiToolPojoMapper::toModel)
                .collect(toList());
        AI_TOOL_CACHE.put("queryAudioTools", aiTools);
        return aiTools;
    }

    @Override
    public List<AiTool> queryVideoTools(String phone) {
        //李博士数字人
        if (phone.equals("18612508125") || phone.equals("15088888888")) {
            List<AiTool> aiTools = dsl.selectFrom(CREATION_AI_TOOL)
                    .where(CREATION_AI_TOOL.CATEGORY.eq(AiToolEnum.video.name()))
                    .orderBy(CREATION_AI_TOOL.PRIORITY.desc())
                    .fetchStreamInto(CreationAiTool.class)
                    .map(aiToolPojoMapper::toModel)
                    .collect(toList());
            aiTools.getFirst().setPath("/tools/digital-human");
            return aiTools;
        }

        List<AiTool> aiTools = AI_TOOL_CACHE.getIfPresent("queryVideoTools");
        if (aiTools != null) {
            return aiTools;
        }
        aiTools = dsl.selectFrom(CREATION_AI_TOOL)
                .where(CREATION_AI_TOOL.CATEGORY.eq(AiToolEnum.video.name()))
                .orderBy(CREATION_AI_TOOL.PRIORITY.desc())
                .fetchStreamInto(CreationAiTool.class)
                .map(aiToolPojoMapper::toModel)
                .collect(toList());
        AI_TOOL_CACHE.put("queryVideoTools", aiTools);
        return aiTools;
    }

}
