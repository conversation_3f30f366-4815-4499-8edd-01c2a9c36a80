/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.asset;

import ai.creatly.sky.creation.domain.common.integration.oss.OssTemplate;
import ai.creatly.sky.creation.domain.common.integration.oss.OssUtil;
import ai.creatly.sky.creation.domain.core.userasset.client.AssetDescribeClient;
import ai.creatly.sky.creation.domain.core.userasset.model.AssetFile;
import ai.creatly.sky.creation.domain.core.userasset.model.UserAsset;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.infra.integration.ApiTemplate;
import ai.creatly.sky.creation.infra.integration.asset.api.AssetDescribeApi;
import ai.creatly.sky.creation.infra.integration.asset.model.AssetDescribeRequest;
import com.jspeeder.core.data.result.ApiVoidResult;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version AssetDescribeClientImpl.java, 2024-10-22 下午9:05 zhoudong
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AssetDescribeClientImpl implements AssetDescribeClient {

    private final UserFileRepository userFileRepository;
    private final AssetDescribeApi   assetDescribeApi;
    private final OssTemplate        ossTemplate;

    @Override
    public String describe(UserAsset asset) {
        AssetFile assetFile = asset.getFile();
        return this.describeAsset(assetFile);
    }

    @Override
    public String describe(UserAsset asset, int videoSliceIndex) {
        AssetFile assetFile = asset.getVideoSlices().get(videoSliceIndex).getFile();
        return this.describeAsset(assetFile);
    }

    private String describeAsset(AssetFile assetFile) {
        UserFile file = userFileRepository.queryById(assetFile.getFileId());
        String bucket = file.getBucket();
        String outputLabelKey = OssUtil.renameKey(file.getKey(), "_label", "json");
        AssetDescribeRequest request = new AssetDescribeRequest()
                .setBucket(bucket)
                .setAssetKey(file.getKey())
                .setOutputLabelKey(outputLabelKey);

        ApiTemplate.logger(log, "[describe]素材打标")
                .request("request:{}", () -> JSON.toJSONString(request))
                .invoke(() -> assetDescribeApi.describe(request))
                .response("response:{}", JSON::toJSONString)
                .success(ApiVoidResult::isSuccess)
                .fetch();
        ossTemplate.assureStoragePresent(bucket, outputLabelKey);

        return OssUtil.toOssUrl(bucket, outputLabelKey);
    }
}
