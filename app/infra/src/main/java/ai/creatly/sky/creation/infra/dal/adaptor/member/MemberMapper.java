/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.dal.adaptor.member;

import ai.creatly.sky.creation.domain.core.member.model.Member;
import ai.creatly.sky.creation.infra.dal.common.ModelMapper;
import ai.creatly.sky.creation.infra.dal.common.UpdatableBuilder;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationMember;
import ai.creatly.sky.creation.infra.dal.tables.records.MemberRecord;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

import static ai.creatly.sky.creation.infra.dal.Tables.CREATION_MEMBER;

/**
 * <AUTHOR>
 * @version MemberMapper.java, v 0.1 2023-10-13 11:02 syoka
 */
@Mapper(config = BaseMapperConfig.class)
public interface MemberMapper extends ModelMapper<Member, CreationMember, MemberRecord> {

    @Override
    Member toModel(CreationMember member);

    @Override
    CreationMember toEntity(Member member);

    @Override
    default void updatable(UpdatableBuilder<MemberRecord> builder) {
        builder.updatable(CREATION_MEMBER.PLAN_ID)
                .updatable(CREATION_MEMBER.PLAN_REAL_FEE)
                .updatable(CREATION_MEMBER.PLAN_ORDER_ID)
                .updatable(CREATION_MEMBER.PLAN_PAID_TIME)
                .updatable(CREATION_MEMBER.EFFECT_AT)
                .updatable(CREATION_MEMBER.EXPIRE_AT)
                .updatable(CREATION_MEMBER.AUTO_RENEWED);
    }
}

