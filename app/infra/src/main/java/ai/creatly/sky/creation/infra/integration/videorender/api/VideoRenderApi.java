/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.infra.integration.videorender.api;

import ai.creatly.sky.creation.domain.common.integration.videorender.model.VideoRenderRequest;
import ai.creatly.sky.creation.infra.integration.videorender.model.VideoRenderProgress;
import ai.creatly.sky.creation.infra.integration.videorender.model.VideoRenderResponse;
import feign.Headers;
import feign.Param;
import feign.RequestLine;

/**
 * <AUTHOR>
 * @version VideoRenderApi.java, v 0.1 2024-09-27 下午8:33 zhoudong
 */
public interface VideoRenderApi {

    /**
     * 视频编辑
     *
     * @param request 视频编辑进度
     */
    @RequestLine("POST /render")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    VideoRenderResponse<?> renderVideo(VideoRenderRequest request);

    /**
     * 查询视频编辑进度
     *
     * @param taskId 视频编辑进度
     */
    @RequestLine("GET /status/{taskId}")
    @Headers({
            "Content-Type: application/json",
            "Accept: application/json"
    })
    VideoRenderResponse<VideoRenderProgress> getVideoRenderProgress(@Param("taskId") Long taskId);
}
