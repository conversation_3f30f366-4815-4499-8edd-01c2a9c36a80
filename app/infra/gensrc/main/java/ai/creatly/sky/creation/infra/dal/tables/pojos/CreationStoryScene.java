/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 故事场景
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationStoryScene implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long storyId;
    private String name;
    private Integer index;
    private String title;
    private JSONB config;
    private JSONB creator;
    private String status;

    public CreationStoryScene() {}

    public CreationStoryScene(CreationStoryScene value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.storyId = value.storyId;
        this.name = value.name;
        this.index = value.index;
        this.title = value.title;
        this.config = value.config;
        this.creator = value.creator;
        this.status = value.status;
    }

    public CreationStoryScene(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long storyId,
        String name,
        Integer index,
        String title,
        JSONB config,
        JSONB creator,
        String status
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.storyId = storyId;
        this.name = name;
        this.index = index;
        this.title = title;
        this.config = config;
        this.creator = creator;
        this.status = status;
    }

    /**
     * Getter for <code>creation.creation_story_scene.id</code>. 场景id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_story_scene.id</code>. 场景id
     */
    public CreationStoryScene setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_story_scene.created_at</code>. 创建时间
     */
    public CreationStoryScene setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_story_scene.updated_at</code>. 更新时间
     */
    public CreationStoryScene setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene.story_id</code>. 故事id
     */
    public Long getStoryId() {
        return this.storyId;
    }

    /**
     * Setter for <code>creation.creation_story_scene.story_id</code>. 故事id
     */
    public CreationStoryScene setStoryId(Long storyId) {
        this.storyId = storyId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene.name</code>. 场景名
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.creation_story_scene.name</code>. 场景名
     */
    public CreationStoryScene setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene.index</code>. 场景序号
     */
    public Integer getIndex() {
        return this.index;
    }

    /**
     * Setter for <code>creation.creation_story_scene.index</code>. 场景序号
     */
    public CreationStoryScene setIndex(Integer index) {
        this.index = index;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene.title</code>. 场景标题
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * Setter for <code>creation.creation_story_scene.title</code>. 场景标题
     */
    public CreationStoryScene setTitle(String title) {
        this.title = title;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene.config</code>. 场景配置
     */
    public JSONB getConfig() {
        return this.config;
    }

    /**
     * Setter for <code>creation.creation_story_scene.config</code>. 场景配置
     */
    public CreationStoryScene setConfig(JSONB config) {
        this.config = config;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return this.creator;
    }

    /**
     * Setter for <code>creation.creation_story_scene.creator</code>. 创建者
     */
    public CreationStoryScene setCreator(JSONB creator) {
        this.creator = creator;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene.status</code>. 状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_story_scene.status</code>. 状态
     */
    public CreationStoryScene setStatus(String status) {
        this.status = status;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationStoryScene other = (CreationStoryScene) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.storyId == null) {
            if (other.storyId != null)
                return false;
        }
        else if (!this.storyId.equals(other.storyId))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.index == null) {
            if (other.index != null)
                return false;
        }
        else if (!this.index.equals(other.index))
            return false;
        if (this.title == null) {
            if (other.title != null)
                return false;
        }
        else if (!this.title.equals(other.title))
            return false;
        if (this.config == null) {
            if (other.config != null)
                return false;
        }
        else if (!this.config.equals(other.config))
            return false;
        if (this.creator == null) {
            if (other.creator != null)
                return false;
        }
        else if (!this.creator.equals(other.creator))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.storyId == null) ? 0 : this.storyId.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.index == null) ? 0 : this.index.hashCode());
        result = prime * result + ((this.title == null) ? 0 : this.title.hashCode());
        result = prime * result + ((this.config == null) ? 0 : this.config.hashCode());
        result = prime * result + ((this.creator == null) ? 0 : this.creator.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationStoryScene (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(storyId);
        sb.append(", ").append(name);
        sb.append(", ").append(index);
        sb.append(", ").append(title);
        sb.append(", ").append(config);
        sb.append(", ").append(creator);
        sb.append(", ").append(status);

        sb.append(")");
        return sb.toString();
    }
}
