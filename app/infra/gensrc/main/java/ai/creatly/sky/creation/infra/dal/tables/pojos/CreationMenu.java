/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 菜单
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String code;
    private Integer version;
    private String desc;

    public CreationMenu() {}

    public CreationMenu(CreationMenu value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.code = value.code;
        this.version = value.version;
        this.desc = value.desc;
    }

    public CreationMenu(
        @Nullable Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        String code,
        Integer version,
        String desc
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.code = code;
        this.version = version;
        this.desc = desc;
    }

    /**
     * Getter for <code>creation.creation_menu.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_menu.id</code>. 主键
     */
    public CreationMenu setId(@Nullable Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_menu.created_at</code>. 创建时间
     */
    public CreationMenu setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_menu.updated_at</code>. 更新时间
     */
    public CreationMenu setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu.code</code>. 唯一标识
     */
    public String getCode() {
        return this.code;
    }

    /**
     * Setter for <code>creation.creation_menu.code</code>. 唯一标识
     */
    public CreationMenu setCode(String code) {
        this.code = code;
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu.version</code>. 版本
     */
    public Integer getVersion() {
        return this.version;
    }

    /**
     * Setter for <code>creation.creation_menu.version</code>. 版本
     */
    public CreationMenu setVersion(Integer version) {
        this.version = version;
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu.desc</code>. 描述
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * Setter for <code>creation.creation_menu.desc</code>. 描述
     */
    public CreationMenu setDesc(String desc) {
        this.desc = desc;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationMenu other = (CreationMenu) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.code == null) {
            if (other.code != null)
                return false;
        }
        else if (!this.code.equals(other.code))
            return false;
        if (this.version == null) {
            if (other.version != null)
                return false;
        }
        else if (!this.version.equals(other.version))
            return false;
        if (this.desc == null) {
            if (other.desc != null)
                return false;
        }
        else if (!this.desc.equals(other.desc))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.code == null) ? 0 : this.code.hashCode());
        result = prime * result + ((this.version == null) ? 0 : this.version.hashCode());
        result = prime * result + ((this.desc == null) ? 0 : this.desc.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationMenu (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(code);
        sb.append(", ").append(version);
        sb.append(", ").append(desc);

        sb.append(")");
        return sb.toString();
    }
}
