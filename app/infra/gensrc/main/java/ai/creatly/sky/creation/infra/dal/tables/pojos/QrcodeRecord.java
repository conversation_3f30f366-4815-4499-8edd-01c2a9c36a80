/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 权益中心-码业务平台
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class QrcodeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long issuerId;
    private String bizScene;
    private JSONB codeContent;
    private String code;
    private ZonedDateTime expireTime;
    private String status;
    private String type;
    private Integer maxThreshold;

    public QrcodeRecord() {}

    public QrcodeRecord(QrcodeRecord value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.issuerId = value.issuerId;
        this.bizScene = value.bizScene;
        this.codeContent = value.codeContent;
        this.code = value.code;
        this.expireTime = value.expireTime;
        this.status = value.status;
        this.type = value.type;
        this.maxThreshold = value.maxThreshold;
    }

    public QrcodeRecord(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long issuerId,
        String bizScene,
        JSONB codeContent,
        String code,
        ZonedDateTime expireTime,
        String status,
        String type,
        @Nullable Integer maxThreshold
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.issuerId = issuerId;
        this.bizScene = bizScene;
        this.codeContent = codeContent;
        this.code = code;
        this.expireTime = expireTime;
        this.status = status;
        this.type = type;
        this.maxThreshold = maxThreshold;
    }

    /**
     * Getter for <code>creation.qrcode_record.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.qrcode_record.id</code>. 主键
     */
    public QrcodeRecord setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.qrcode_record.created_at</code>. 创建时间
     */
    public QrcodeRecord setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.qrcode_record.updated_at</code>. 更新时间
     */
    public QrcodeRecord setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.issuer_id</code>. 任务所有者ID（1表示平台）
     */
    public Long getIssuerId() {
        return this.issuerId;
    }

    /**
     * Setter for <code>creation.qrcode_record.issuer_id</code>. 任务所有者ID（1表示平台）
     */
    public QrcodeRecord setIssuerId(Long issuerId) {
        this.issuerId = issuerId;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.biz_scene</code>. 业务场景
     */
    public String getBizScene() {
        return this.bizScene;
    }

    /**
     * Setter for <code>creation.qrcode_record.biz_scene</code>. 业务场景
     */
    public QrcodeRecord setBizScene(String bizScene) {
        this.bizScene = bizScene;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.code_content</code>. 码内容
     */
    public JSONB getCodeContent() {
        return this.codeContent;
    }

    /**
     * Setter for <code>creation.qrcode_record.code_content</code>. 码内容
     */
    public QrcodeRecord setCodeContent(JSONB codeContent) {
        this.codeContent = codeContent;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.code</code>. 码值，全局唯一
     */
    public String getCode() {
        return this.code;
    }

    /**
     * Setter for <code>creation.qrcode_record.code</code>. 码值，全局唯一
     */
    public QrcodeRecord setCode(String code) {
        this.code = code;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.expire_time</code>. 码的有效期
     */
    public ZonedDateTime getExpireTime() {
        return this.expireTime;
    }

    /**
     * Setter for <code>creation.qrcode_record.expire_time</code>. 码的有效期
     */
    public QrcodeRecord setExpireTime(ZonedDateTime expireTime) {
        this.expireTime = expireTime;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.status</code>. 状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.qrcode_record.status</code>. 状态
     */
    public QrcodeRecord setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.type</code>. 永久，一次性，时间
     */
    public String getType() {
        return this.type;
    }

    /**
     * Setter for <code>creation.qrcode_record.type</code>. 永久，一次性，时间
     */
    public QrcodeRecord setType(String type) {
        this.type = type;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.max_threshold</code>. 最大阈值
     */
    @Nullable
    public Integer getMaxThreshold() {
        return this.maxThreshold;
    }

    /**
     * Setter for <code>creation.qrcode_record.max_threshold</code>. 最大阈值
     */
    public QrcodeRecord setMaxThreshold(@Nullable Integer maxThreshold) {
        this.maxThreshold = maxThreshold;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final QrcodeRecord other = (QrcodeRecord) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.issuerId == null) {
            if (other.issuerId != null)
                return false;
        }
        else if (!this.issuerId.equals(other.issuerId))
            return false;
        if (this.bizScene == null) {
            if (other.bizScene != null)
                return false;
        }
        else if (!this.bizScene.equals(other.bizScene))
            return false;
        if (this.codeContent == null) {
            if (other.codeContent != null)
                return false;
        }
        else if (!this.codeContent.equals(other.codeContent))
            return false;
        if (this.code == null) {
            if (other.code != null)
                return false;
        }
        else if (!this.code.equals(other.code))
            return false;
        if (this.expireTime == null) {
            if (other.expireTime != null)
                return false;
        }
        else if (!this.expireTime.equals(other.expireTime))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.type == null) {
            if (other.type != null)
                return false;
        }
        else if (!this.type.equals(other.type))
            return false;
        if (this.maxThreshold == null) {
            if (other.maxThreshold != null)
                return false;
        }
        else if (!this.maxThreshold.equals(other.maxThreshold))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.issuerId == null) ? 0 : this.issuerId.hashCode());
        result = prime * result + ((this.bizScene == null) ? 0 : this.bizScene.hashCode());
        result = prime * result + ((this.codeContent == null) ? 0 : this.codeContent.hashCode());
        result = prime * result + ((this.code == null) ? 0 : this.code.hashCode());
        result = prime * result + ((this.expireTime == null) ? 0 : this.expireTime.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.type == null) ? 0 : this.type.hashCode());
        result = prime * result + ((this.maxThreshold == null) ? 0 : this.maxThreshold.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("QrcodeRecord (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(issuerId);
        sb.append(", ").append(bizScene);
        sb.append(", ").append(codeContent);
        sb.append(", ").append(code);
        sb.append(", ").append(expireTime);
        sb.append(", ").append(status);
        sb.append(", ").append(type);
        sb.append(", ").append(maxThreshold);

        sb.append(")");
        return sb.toString();
    }
}
