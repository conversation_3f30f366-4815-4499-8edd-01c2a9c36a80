/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.MaterialRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 素材记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class MaterialTable extends TableImpl<MaterialRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_material</code>
     */
    public static final MaterialTable CREATION_MATERIAL = new MaterialTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MaterialRecord> getRecordType() {
        return MaterialRecord.class;
    }

    /**
     * The column <code>creation.creation_material.id</code>. 主键
     */
    public final TableField<MaterialRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_material.created_at</code>. 创建时间
     */
    public final TableField<MaterialRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_material.updated_at</code>. 更新时间
     */
    public final TableField<MaterialRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_material.creator_type</code>. 创建者类型
     */
    public final TableField<MaterialRecord, String> CREATOR_TYPE = createField(DSL.name("creator_type"), SQLDataType.VARCHAR(32).nullable(false), this, "创建者类型");

    /**
     * The column <code>creation.creation_material.creator_id</code>. 创建者ID
     */
    public final TableField<MaterialRecord, String> CREATOR_ID = createField(DSL.name("creator_id"), SQLDataType.VARCHAR(128).nullable(false), this, "创建者ID");

    /**
     * The column <code>creation.creation_material.creator_name</code>. 创建者名称
     */
    public final TableField<MaterialRecord, String> CREATOR_NAME = createField(DSL.name("creator_name"), SQLDataType.VARCHAR(255).nullable(false), this, "创建者名称");

    /**
     * The column <code>creation.creation_material.uid</code>. 所属用户ID
     */
    public final TableField<MaterialRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "所属用户ID");

    /**
     * The column <code>creation.creation_material.name</code>. 素材名称
     */
    public final TableField<MaterialRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(128).nullable(false), this, "素材名称");

    /**
     * The column <code>creation.creation_material.type</code>.
     * 素材类型（文本/图片/音频/视频）
     */
    public final TableField<MaterialRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(32).nullable(false), this, "素材类型（文本/图片/音频/视频）");

    /**
     * The column <code>creation.creation_material.status</code>. 素材状态（有效/无效)
     */
    public final TableField<MaterialRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(16).nullable(false), this, "素材状态（有效/无效)");

    /**
     * The column <code>creation.creation_material.acl_type</code>. 访问权限（公共/私有）
     */
    public final TableField<MaterialRecord, String> ACL_TYPE = createField(DSL.name("acl_type"), SQLDataType.VARCHAR(32).nullable(false), this, "访问权限（公共/私有）");

    /**
     * The column <code>creation.creation_material.description</code>. 素材描述
     */
    public final TableField<MaterialRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(255).nullable(false), this, "素材描述");

    /**
     * The column <code>creation.creation_material.file_id</code>. 主文件ID
     */
    public final TableField<MaterialRecord, Long> FILE_ID = createField(DSL.name("file_id"), SQLDataType.BIGINT, this, "主文件ID");

    /**
     * The column <code>creation.creation_material.file_url</code>. 主文件地址（OSS协议）
     */
    public final TableField<MaterialRecord, String> FILE_URL = createField(DSL.name("file_url"), SQLDataType.VARCHAR(255), this, "主文件地址（OSS协议）");

    /**
     * The column <code>creation.creation_material.text</code>. 主文本
     */
    public final TableField<MaterialRecord, String> TEXT = createField(DSL.name("text"), SQLDataType.CLOB.nullable(false), this, "主文本");

    /**
     * The column <code>creation.creation_material.attached_resources</code>.
     * 附加资源列表（封面/缩略图d）
     */
    public final TableField<MaterialRecord, JSONB[]> ATTACHED_RESOURCES = createField(DSL.name("attached_resources"), SQLDataType.JSONB.nullable(false).array(), this, "附加资源列表（封面/缩略图d）");

    /**
     * The column <code>creation.creation_material.labels</code>. 标签列表
     */
    public final TableField<MaterialRecord, JSONB[]> LABELS = createField(DSL.name("labels"), SQLDataType.JSONB.nullable(false).array(), this, "标签列表");

    /**
     * The column <code>creation.creation_material.likes</code>. 喜欢数量
     */
    public final TableField<MaterialRecord, Long> LIKES = createField(DSL.name("likes"), SQLDataType.BIGINT.nullable(false), this, "喜欢数量");

    /**
     * The column <code>creation.creation_material.favorites</code>. 收藏数量
     */
    public final TableField<MaterialRecord, Long> FAVORITES = createField(DSL.name("favorites"), SQLDataType.BIGINT.nullable(false), this, "收藏数量");

    /**
     * The column <code>creation.creation_material.views</code>. 查看数量
     */
    public final TableField<MaterialRecord, Long> VIEWS = createField(DSL.name("views"), SQLDataType.BIGINT.nullable(false), this, "查看数量");

    private MaterialTable(Name alias, Table<MaterialRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private MaterialTable(Name alias, Table<MaterialRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("素材记录"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_material</code> table reference
     */
    public MaterialTable(String alias) {
        this(DSL.name(alias), CREATION_MATERIAL);
    }

    /**
     * Create an aliased <code>creation.creation_material</code> table reference
     */
    public MaterialTable(Name alias) {
        this(alias, CREATION_MATERIAL);
    }

    /**
     * Create a <code>creation.creation_material</code> table reference
     */
    public MaterialTable() {
        this(DSL.name("creation_material"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<MaterialRecord> getPrimaryKey() {
        return Keys.CREATION_MATERIAL_PKEY;
    }

    @Override
    public MaterialTable as(String alias) {
        return new MaterialTable(DSL.name(alias), this);
    }

    @Override
    public MaterialTable as(Name alias) {
        return new MaterialTable(alias, this);
    }

    @Override
    public MaterialTable as(Table<?> alias) {
        return new MaterialTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public MaterialTable rename(String name) {
        return new MaterialTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public MaterialTable rename(Name name) {
        return new MaterialTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public MaterialTable rename(Table<?> name) {
        return new MaterialTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public MaterialTable where(Condition condition) {
        return new MaterialTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public MaterialTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public MaterialTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public MaterialTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public MaterialTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public MaterialTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public MaterialTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public MaterialTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public MaterialTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public MaterialTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
