/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.CourseHotTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseHot;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 热门课程
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseHotRecord extends UpdatableRecordImpl<CourseHotRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.course_hot.id</code>. 热门课程id
     */
    public CourseHotRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_hot.id</code>. 热门课程id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.course_hot.created_at</code>. 创建时间
     */
    public CourseHotRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_hot.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.course_hot.updated_at</code>. 更新时间
     */
    public CourseHotRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_hot.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.course_hot.location</code>. 推荐位置
     */
    public CourseHotRecord setLocation(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_hot.location</code>. 推荐位置
     */
    public String getLocation() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.course_hot.course_id</code>. 课程ID
     */
    public CourseHotRecord setCourseId(Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_hot.course_id</code>. 课程ID
     */
    public Long getCourseId() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.course_hot.priority</code>. 推荐优先级
     */
    public CourseHotRecord setPriority(Long value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_hot.priority</code>. 推荐优先级
     */
    public Long getPriority() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>creation.course_hot.is_hot</code>. 是否推荐
     */
    public CourseHotRecord setIsHot(@Nullable Long value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_hot.is_hot</code>. 是否推荐
     */
    @Nullable
    public Long getIsHot() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>creation.course_hot.status</code>. 启用状态
     */
    public CourseHotRecord setStatus(@Nullable Short value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_hot.status</code>. 启用状态
     */
    @Nullable
    public Short getStatus() {
        return (Short) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseHotRecord
     */
    public CourseHotRecord() {
        super(CourseHotTable.COURSE_HOT);
    }

    /**
     * Create a detached, initialised CourseHotRecord
     */
    public CourseHotRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, String location, Long courseId, Long priority, @Nullable Long isHot, @Nullable Short status) {
        super(CourseHotTable.COURSE_HOT);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setLocation(location);
        setCourseId(courseId);
        setPriority(priority);
        setIsHot(isHot);
        setStatus(status);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised CourseHotRecord
     */
    public CourseHotRecord(CourseHot value) {
        super(CourseHotTable.COURSE_HOT);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setLocation(value.getLocation());
            setCourseId(value.getCourseId());
            setPriority(value.getPriority());
            setIsHot(value.getIsHot());
            setStatus(value.getStatus());
            resetChangedOnNotNull();
        }
    }
}
