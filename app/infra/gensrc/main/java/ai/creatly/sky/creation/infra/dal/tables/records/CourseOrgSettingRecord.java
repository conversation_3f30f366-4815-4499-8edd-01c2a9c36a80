/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.CourseOrgSettingTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseOrgSetting;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 机构课程购买记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseOrgSettingRecord extends UpdatableRecordImpl<CourseOrgSettingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.course_org_setting.id</code>. 机构购买记录id
     */
    public CourseOrgSettingRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_org_setting.id</code>. 机构购买记录id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.course_org_setting.created_at</code>. 创建时间
     */
    public CourseOrgSettingRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_org_setting.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.course_org_setting.updated_at</code>. 更新时间
     */
    public CourseOrgSettingRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_org_setting.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.course_org_setting.course_id</code>. 课程ID
     */
    public CourseOrgSettingRecord setCourseId(@Nullable Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_org_setting.course_id</code>. 课程ID
     */
    @Nullable
    public Long getCourseId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.course_org_setting.status</code>. 课程状态
     */
    public CourseOrgSettingRecord setStatus(@Nullable String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_org_setting.status</code>. 课程状态
     */
    @Nullable
    public String getStatus() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.course_org_setting.begin_time</code>. 开始时间
     */
    public CourseOrgSettingRecord setBeginTime(@Nullable ZonedDateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_org_setting.begin_time</code>. 开始时间
     */
    @Nullable
    public ZonedDateTime getBeginTime() {
        return (ZonedDateTime) get(5);
    }

    /**
     * Setter for <code>creation.course_org_setting.end_time</code>. 有效日期
     */
    public CourseOrgSettingRecord setEndTime(@Nullable ZonedDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_org_setting.end_time</code>. 有效日期
     */
    @Nullable
    public ZonedDateTime getEndTime() {
        return (ZonedDateTime) get(6);
    }

    /**
     * Setter for <code>creation.course_org_setting.limit_total</code>. 购买数量
     */
    public CourseOrgSettingRecord setLimitTotal(@Nullable Long value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_org_setting.limit_total</code>. 购买数量
     */
    @Nullable
    public Long getLimitTotal() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>creation.course_org_setting.learn_total</code>. 已学数量
     */
    public CourseOrgSettingRecord setLearnTotal(@Nullable Long value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_org_setting.learn_total</code>. 已学数量
     */
    @Nullable
    public Long getLearnTotal() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>creation.course_org_setting.org_code</code>. 组织机构
     */
    public CourseOrgSettingRecord setOrgCode(@Nullable String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_org_setting.org_code</code>. 组织机构
     */
    @Nullable
    public String getOrgCode() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.course_org_setting.credits</code>. 赠送元气
     */
    public CourseOrgSettingRecord setCredits(@Nullable Long value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_org_setting.credits</code>. 赠送元气
     */
    @Nullable
    public Long getCredits() {
        return (Long) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseOrgSettingRecord
     */
    public CourseOrgSettingRecord() {
        super(CourseOrgSettingTable.COURSE_ORG_SETTING);
    }

    /**
     * Create a detached, initialised CourseOrgSettingRecord
     */
    public CourseOrgSettingRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable Long courseId, @Nullable String status, @Nullable ZonedDateTime beginTime, @Nullable ZonedDateTime endTime, @Nullable Long limitTotal, @Nullable Long learnTotal, @Nullable String orgCode, @Nullable Long credits) {
        super(CourseOrgSettingTable.COURSE_ORG_SETTING);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCourseId(courseId);
        setStatus(status);
        setBeginTime(beginTime);
        setEndTime(endTime);
        setLimitTotal(limitTotal);
        setLearnTotal(learnTotal);
        setOrgCode(orgCode);
        setCredits(credits);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised CourseOrgSettingRecord
     */
    public CourseOrgSettingRecord(CourseOrgSetting value) {
        super(CourseOrgSettingTable.COURSE_ORG_SETTING);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCourseId(value.getCourseId());
            setStatus(value.getStatus());
            setBeginTime(value.getBeginTime());
            setEndTime(value.getEndTime());
            setLimitTotal(value.getLimitTotal());
            setLearnTotal(value.getLearnTotal());
            setOrgCode(value.getOrgCode());
            setCredits(value.getCredits());
            resetChangedOnNotNull();
        }
    }
}
