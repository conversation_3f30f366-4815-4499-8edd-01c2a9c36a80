/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.UserInvitationTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserInvitation;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户反馈
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserInvitationRecord extends UpdatableRecordImpl<UserInvitationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_user_invitation.id</code>. 主键
     */
    public UserInvitationRecord setId(@Nullable Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_user_invitation.created_at</code>.
     * 创建时间
     */
    public UserInvitationRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_user_invitation.updated_at</code>.
     * 更新时间
     */
    public UserInvitationRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_user_invitation.inviter_id</code>.
     * 邀请人用户ID
     */
    public UserInvitationRecord setInviterId(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.inviter_id</code>.
     * 邀请人用户ID
     */
    public Long getInviterId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_user_invitation.inviter_name</code>.
     * 邀请人用户名
     */
    public UserInvitationRecord setInviterName(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.inviter_name</code>.
     * 邀请人用户名
     */
    public String getInviterName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_user_invitation.invitee_id</code>.
     * 被邀请人用户ID
     */
    public UserInvitationRecord setInviteeId(Long value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.invitee_id</code>.
     * 被邀请人用户ID
     */
    public Long getInviteeId() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>creation.creation_user_invitation.invitee_name</code>.
     * 被邀请人用户名
     */
    public UserInvitationRecord setInviteeName(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.invitee_name</code>.
     * 被邀请人用户名
     */
    public String getInviteeName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_user_invitation.invite_code</code>.
     * 邀请码
     */
    public UserInvitationRecord setInviteCode(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.invite_code</code>.
     * 邀请码
     */
    public String getInviteCode() {
        return (String) get(7);
    }

    /**
     * Setter for
     * <code>creation.creation_user_invitation.awarded_credits</code>. 邀请人被奖励的元气
     */
    public UserInvitationRecord setAwardedCredits(Integer value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_invitation.awarded_credits</code>. 邀请人被奖励的元气
     */
    public Integer getAwardedCredits() {
        return (Integer) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserInvitationRecord
     */
    public UserInvitationRecord() {
        super(UserInvitationTable.CREATION_USER_INVITATION);
    }

    /**
     * Create a detached, initialised UserInvitationRecord
     */
    public UserInvitationRecord(@Nullable Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long inviterId, String inviterName, Long inviteeId, String inviteeName, String inviteCode, Integer awardedCredits) {
        super(UserInvitationTable.CREATION_USER_INVITATION);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setInviterId(inviterId);
        setInviterName(inviterName);
        setInviteeId(inviteeId);
        setInviteeName(inviteeName);
        setInviteCode(inviteCode);
        setAwardedCredits(awardedCredits);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserInvitationRecord
     */
    public UserInvitationRecord(CreationUserInvitation value) {
        super(UserInvitationTable.CREATION_USER_INVITATION);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setInviterId(value.getInviterId());
            setInviterName(value.getInviterName());
            setInviteeId(value.getInviteeId());
            setInviteeName(value.getInviteeName());
            setInviteCode(value.getInviteCode());
            setAwardedCredits(value.getAwardedCredits());
            resetChangedOnNotNull();
        }
    }
}
