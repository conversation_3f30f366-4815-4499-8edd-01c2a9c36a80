/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.CourseExamRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseExamRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseExamRecordRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 考试记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class CourseExamRecordDAO extends DAOImpl<CourseExamRecordRecord, CourseExamRecord, Long> {

    /**
     * Create a new CourseExamRecordDAO without any configuration
     */
    public CourseExamRecordDAO() {
        super(CourseExamRecordTable.COURSE_EXAM_RECORD, CourseExamRecord.class);
    }

    /**
     * Create a new CourseExamRecordDAO with an attached configuration
     */
    @Autowired
    public CourseExamRecordDAO(Configuration configuration) {
        super(CourseExamRecordTable.COURSE_EXAM_RECORD, CourseExamRecord.class, configuration);
    }

    @Override
    public Long getId(CourseExamRecord object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CourseExamRecord> fetchById(Long... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CourseExamRecord fetchOneById(Long value) {
        return fetchOne(CourseExamRecordTable.COURSE_EXAM_RECORD.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CourseExamRecord> fetchOptionalById(Long value) {
        return fetchOptional(CourseExamRecordTable.COURSE_EXAM_RECORD.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CourseExamRecord> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CourseExamRecord> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>exam_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfExamId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.EXAM_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>exam_id IN (values)</code>
     */
    public List<CourseExamRecord> fetchByExamId(Long... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.EXAM_ID, values);
    }

    /**
     * Fetch records that have <code>number BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfNumber(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.NUMBER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>number IN (values)</code>
     */
    public List<CourseExamRecord> fetchByNumber(Integer... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.NUMBER, values);
    }

    /**
     * Fetch records that have <code>question BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfQuestion(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.QUESTION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>question IN (values)</code>
     */
    public List<CourseExamRecord> fetchByQuestion(String... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.QUESTION, values);
    }

    /**
     * Fetch records that have <code>content BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfContent(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.CONTENT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>content IN (values)</code>
     */
    public List<CourseExamRecord> fetchByContent(String... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.CONTENT, values);
    }

    /**
     * Fetch records that have <code>answer BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfAnswer(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.ANSWER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>answer IN (values)</code>
     */
    public List<CourseExamRecord> fetchByAnswer(String... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.ANSWER, values);
    }

    /**
     * Fetch records that have <code>user_answer BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfUserAnswer(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.USER_ANSWER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>user_answer IN (values)</code>
     */
    public List<CourseExamRecord> fetchByUserAnswer(String... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.USER_ANSWER, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfType(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<CourseExamRecord> fetchByType(String... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.TYPE, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<CourseExamRecord> fetchByUid(Long... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.UID, values);
    }

    /**
     * Fetch records that have <code>org_code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfOrgCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.ORG_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_code IN (values)</code>
     */
    public List<CourseExamRecord> fetchByOrgCode(String... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.ORG_CODE, values);
    }

    /**
     * Fetch records that have <code>score BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfScore(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.SCORE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>score IN (values)</code>
     */
    public List<CourseExamRecord> fetchByScore(Long... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.SCORE, values);
    }

    /**
     * Fetch records that have <code>user_score BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseExamRecord> fetchRangeOfUserScore(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseExamRecordTable.COURSE_EXAM_RECORD.USER_SCORE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>user_score IN (values)</code>
     */
    public List<CourseExamRecord> fetchByUserScore(Long... values) {
        return fetch(CourseExamRecordTable.COURSE_EXAM_RECORD.USER_SCORE, values);
    }
}
