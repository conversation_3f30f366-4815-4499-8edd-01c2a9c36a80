/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseExamPoolRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 考试题库
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseExamPoolTable extends TableImpl<CourseExamPoolRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.course_exam_pool</code>
     */
    public static final CourseExamPoolTable COURSE_EXAM_POOL = new CourseExamPoolTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseExamPoolRecord> getRecordType() {
        return CourseExamPoolRecord.class;
    }

    /**
     * The column <code>creation.course_exam_pool.id</code>. 题库ID
     */
    public final TableField<CourseExamPoolRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "题库ID");

    /**
     * The column <code>creation.course_exam_pool.created_at</code>. 创建时间
     */
    public final TableField<CourseExamPoolRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.course_exam_pool.updated_at</code>. 更新时间
     */
    public final TableField<CourseExamPoolRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.course_exam_pool.cert_id</code>. 证书ID
     */
    public final TableField<CourseExamPoolRecord, Long> CERT_ID = createField(DSL.name("cert_id"), SQLDataType.BIGINT, this, "证书ID");

    /**
     * The column <code>creation.course_exam_pool.question</code>. 题目
     */
    public final TableField<CourseExamPoolRecord, String> QUESTION = createField(DSL.name("question"), SQLDataType.VARCHAR(512), this, "题目");

    /**
     * The column <code>creation.course_exam_pool.content</code>. 内容
     */
    public final TableField<CourseExamPoolRecord, String> CONTENT = createField(DSL.name("content"), SQLDataType.CLOB, this, "内容");

    /**
     * The column <code>creation.course_exam_pool.answer</code>. 标准答案
     */
    public final TableField<CourseExamPoolRecord, String> ANSWER = createField(DSL.name("answer"), SQLDataType.CLOB, this, "标准答案");

    /**
     * The column <code>creation.course_exam_pool.type</code>. 题目类型
     */
    public final TableField<CourseExamPoolRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(64), this, "题目类型");

    /**
     * The column <code>creation.course_exam_pool.score</code>. 标准得分
     */
    public final TableField<CourseExamPoolRecord, Long> SCORE = createField(DSL.name("score"), SQLDataType.BIGINT, this, "标准得分");

    private CourseExamPoolTable(Name alias, Table<CourseExamPoolRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private CourseExamPoolTable(Name alias, Table<CourseExamPoolRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("考试题库"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.course_exam_pool</code> table reference
     */
    public CourseExamPoolTable(String alias) {
        this(DSL.name(alias), COURSE_EXAM_POOL);
    }

    /**
     * Create an aliased <code>creation.course_exam_pool</code> table reference
     */
    public CourseExamPoolTable(Name alias) {
        this(alias, COURSE_EXAM_POOL);
    }

    /**
     * Create a <code>creation.course_exam_pool</code> table reference
     */
    public CourseExamPoolTable() {
        this(DSL.name("course_exam_pool"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<CourseExamPoolRecord> getPrimaryKey() {
        return Keys.COURSE_EXAM_POOL_PKEY;
    }

    @Override
    public CourseExamPoolTable as(String alias) {
        return new CourseExamPoolTable(DSL.name(alias), this);
    }

    @Override
    public CourseExamPoolTable as(Name alias) {
        return new CourseExamPoolTable(alias, this);
    }

    @Override
    public CourseExamPoolTable as(Table<?> alias) {
        return new CourseExamPoolTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseExamPoolTable rename(String name) {
        return new CourseExamPoolTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseExamPoolTable rename(Name name) {
        return new CourseExamPoolTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseExamPoolTable rename(Table<?> name) {
        return new CourseExamPoolTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseExamPoolTable where(Condition condition) {
        return new CourseExamPoolTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseExamPoolTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseExamPoolTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseExamPoolTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CourseExamPoolTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CourseExamPoolTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CourseExamPoolTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CourseExamPoolTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseExamPoolTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseExamPoolTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
