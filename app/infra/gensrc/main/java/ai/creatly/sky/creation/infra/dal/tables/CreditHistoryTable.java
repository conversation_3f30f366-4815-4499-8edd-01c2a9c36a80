/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Indexes;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.CreditHistoryRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 余额使用记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreditHistoryTable extends TableImpl<CreditHistoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_credit_history</code>
     */
    public static final CreditHistoryTable CREATION_CREDIT_HISTORY = new CreditHistoryTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CreditHistoryRecord> getRecordType() {
        return CreditHistoryRecord.class;
    }

    /**
     * The column <code>creation.creation_credit_history.id</code>. 主键
     */
    public final TableField<CreditHistoryRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_credit_history.created_at</code>. 创建时间
     */
    public final TableField<CreditHistoryRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_credit_history.updated_at</code>. 更新时间
     */
    public final TableField<CreditHistoryRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_credit_history.uid</code>. 用户id
     */
    public final TableField<CreditHistoryRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户id");

    /**
     * The column <code>creation.creation_credit_history.user_credit_id</code>.
     * 用户余额卡表id
     */
    public final TableField<CreditHistoryRecord, Long> USER_CREDIT_ID = createField(DSL.name("user_credit_id"), SQLDataType.BIGINT.nullable(false), this, "用户余额卡表id");

    /**
     * The column <code>creation.creation_credit_history.biz_no</code>.
     * 任务类存放任务id，消息存放消息id
     */
    public final TableField<CreditHistoryRecord, String> BIZ_NO = createField(DSL.name("biz_no"), SQLDataType.VARCHAR(255).nullable(false), this, "任务类存放任务id，消息存放消息id");

    /**
     * The column <code>creation.creation_credit_history.biz_type</code>.
     * 业务场景：马良/活图/充值
     */
    public final TableField<CreditHistoryRecord, String> BIZ_TYPE = createField(DSL.name("biz_type"), SQLDataType.VARCHAR(255).nullable(false), this, "业务场景：马良/活图/充值");

    /**
     * The column <code>creation.creation_credit_history.amount</code>. credit数量
     */
    public final TableField<CreditHistoryRecord, Integer> AMOUNT = createField(DSL.name("amount"), SQLDataType.INTEGER.nullable(false), this, "credit数量");

    /**
     * The column <code>creation.creation_credit_history.type</code>. 收入，支出
     */
    public final TableField<CreditHistoryRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(32).nullable(false), this, "收入，支出");

    /**
     * The column <code>creation.creation_credit_history.balance</code>. 信用余额
     */
    public final TableField<CreditHistoryRecord, Long> BALANCE = createField(DSL.name("balance"), SQLDataType.BIGINT.nullable(false), this, "信用余额");

    private CreditHistoryTable(Name alias, Table<CreditHistoryRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private CreditHistoryTable(Name alias, Table<CreditHistoryRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("余额使用记录"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_credit_history</code> table
     * reference
     */
    public CreditHistoryTable(String alias) {
        this(DSL.name(alias), CREATION_CREDIT_HISTORY);
    }

    /**
     * Create an aliased <code>creation.creation_credit_history</code> table
     * reference
     */
    public CreditHistoryTable(Name alias) {
        this(alias, CREATION_CREDIT_HISTORY);
    }

    /**
     * Create a <code>creation.creation_credit_history</code> table reference
     */
    public CreditHistoryTable() {
        this(DSL.name("creation_credit_history"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.CREATION_CREDIT_HISTORY_UID_BIZ_NO_BIZ_TYPE_TYPE_IDX);
    }

    @Override
    public UniqueKey<CreditHistoryRecord> getPrimaryKey() {
        return Keys.CREATION_CREDIT_HISTORY_PKEY;
    }

    @Override
    public List<UniqueKey<CreditHistoryRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CREATION_CREDIT_HISTORY_USER_CREDIT_ID_BIZ_NO_BIZ_TYPE_TYPE_KEY);
    }

    @Override
    public CreditHistoryTable as(String alias) {
        return new CreditHistoryTable(DSL.name(alias), this);
    }

    @Override
    public CreditHistoryTable as(Name alias) {
        return new CreditHistoryTable(alias, this);
    }

    @Override
    public CreditHistoryTable as(Table<?> alias) {
        return new CreditHistoryTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public CreditHistoryTable rename(String name) {
        return new CreditHistoryTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public CreditHistoryTable rename(Name name) {
        return new CreditHistoryTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public CreditHistoryTable rename(Table<?> name) {
        return new CreditHistoryTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CreditHistoryTable where(Condition condition) {
        return new CreditHistoryTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CreditHistoryTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CreditHistoryTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CreditHistoryTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CreditHistoryTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CreditHistoryTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CreditHistoryTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CreditHistoryTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CreditHistoryTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CreditHistoryTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
