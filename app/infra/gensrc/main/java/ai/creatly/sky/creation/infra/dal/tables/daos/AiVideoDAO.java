/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.AiVideoTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiVideo;
import ai.creatly.sky.creation.infra.dal.tables.records.AiVideoRecord;

import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.jooq.types.YearToSecond;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 音乐库
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class AiVideoDAO extends DAOImpl<AiVideoRecord, CreationAiVideo, Long> {

    /**
     * Create a new AiVideoDAO without any configuration
     */
    public AiVideoDAO() {
        super(AiVideoTable.CREATION_AI_VIDEO, CreationAiVideo.class);
    }

    /**
     * Create a new AiVideoDAO with an attached configuration
     */
    @Autowired
    public AiVideoDAO(Configuration configuration) {
        super(AiVideoTable.CREATION_AI_VIDEO, CreationAiVideo.class, configuration);
    }

    @Override
    public Long getId(CreationAiVideo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationAiVideo> fetchById(Long... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationAiVideo fetchOneById(Long value) {
        return fetchOne(AiVideoTable.CREATION_AI_VIDEO.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationAiVideo> fetchOptionalById(Long value) {
        return fetchOptional(AiVideoTable.CREATION_AI_VIDEO.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationAiVideo> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationAiVideo> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<CreationAiVideo> fetchByUid(Long... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.UID, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CreationAiVideo> fetchByStatus(String... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.STATUS, values);
    }

    /**
     * Fetch records that have <code>acl BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfAcl(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.ACL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>acl IN (values)</code>
     */
    public List<CreationAiVideo> fetchByAcl(String... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.ACL, values);
    }

    /**
     * Fetch records that have <code>biz_source BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfBizSource(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.BIZ_SOURCE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_source IN (values)</code>
     */
    public List<CreationAiVideo> fetchByBizSource(String... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.BIZ_SOURCE, values);
    }

    /**
     * Fetch records that have <code>biz_no BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfBizNo(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.BIZ_NO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_no IN (values)</code>
     */
    public List<CreationAiVideo> fetchByBizNo(String... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.BIZ_NO, values);
    }

    /**
     * Fetch records that have <code>title BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfTitle(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.TITLE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>title IN (values)</code>
     */
    public List<CreationAiVideo> fetchByTitle(String... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.TITLE, values);
    }

    /**
     * Fetch records that have <code>file_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfFileId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.FILE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>file_id IN (values)</code>
     */
    public List<CreationAiVideo> fetchByFileId(Long... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.FILE_ID, values);
    }

    /**
     * Fetch records that have <code>file_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfFileUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.FILE_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>file_url IN (values)</code>
     */
    public List<CreationAiVideo> fetchByFileUrl(String... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.FILE_URL, values);
    }

    /**
     * Fetch records that have <code>duration BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfDuration(Duration lowerInclusive, Duration upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.DURATION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>duration IN (values)</code>
     */
    public List<CreationAiVideo> fetchByDuration(Duration... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.DURATION, values);
    }

    /**
     * Fetch records that have <code>video_format BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfVideoFormat(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.VIDEO_FORMAT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>video_format IN (values)</code>
     */
    public List<CreationAiVideo> fetchByVideoFormat(String... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.VIDEO_FORMAT, values);
    }

    /**
     * Fetch records that have <code>resolution BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfResolution(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.RESOLUTION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>resolution IN (values)</code>
     */
    public List<CreationAiVideo> fetchByResolution(String... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.RESOLUTION, values);
    }

    /**
     * Fetch records that have <code>cover_file_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfCoverFileId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.COVER_FILE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover_file_id IN (values)</code>
     */
    public List<CreationAiVideo> fetchByCoverFileId(Long... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.COVER_FILE_ID, values);
    }

    /**
     * Fetch records that have <code>cover_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfCoverUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.COVER_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover_url IN (values)</code>
     */
    public List<CreationAiVideo> fetchByCoverUrl(String... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.COVER_URL, values);
    }

    /**
     * Fetch records that have <code>author_uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfAuthorUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.AUTHOR_UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>author_uid IN (values)</code>
     */
    public List<CreationAiVideo> fetchByAuthorUid(Long... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.AUTHOR_UID, values);
    }

    /**
     * Fetch records that have <code>author_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfAuthorName(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.AUTHOR_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>author_name IN (values)</code>
     */
    public List<CreationAiVideo> fetchByAuthorName(String... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.AUTHOR_NAME, values);
    }

    /**
     * Fetch records that have <code>author_avatar BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfAuthorAvatar(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.AUTHOR_AVATAR, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>author_avatar IN (values)</code>
     */
    public List<CreationAiVideo> fetchByAuthorAvatar(String... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.AUTHOR_AVATAR, values);
    }

    /**
     * Fetch records that have <code>tag_names BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfTagNames(String[] lowerInclusive, String[] upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.TAG_NAMES, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>tag_names IN (values)</code>
     */
    public List<CreationAiVideo> fetchByTagNames(String[]... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.TAG_NAMES, values);
    }

    /**
     * Fetch records that have <code>like_count BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiVideo> fetchRangeOfLikeCount(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(AiVideoTable.CREATION_AI_VIDEO.LIKE_COUNT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>like_count IN (values)</code>
     */
    public List<CreationAiVideo> fetchByLikeCount(Integer... values) {
        return fetch(AiVideoTable.CREATION_AI_VIDEO.LIKE_COUNT, values);
    }
}
