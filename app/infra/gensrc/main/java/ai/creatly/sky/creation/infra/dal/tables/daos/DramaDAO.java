/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.DramaTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationDrama;
import ai.creatly.sky.creation.infra.dal.tables.records.DramaRecord;

import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.JSONB;
import org.jooq.impl.DAOImpl;
import org.jooq.types.YearToSecond;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 剧情
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class DramaDAO extends DAOImpl<DramaRecord, CreationDrama, Long> {

    /**
     * Create a new DramaDAO without any configuration
     */
    public DramaDAO() {
        super(DramaTable.CREATION_DRAMA, CreationDrama.class);
    }

    /**
     * Create a new DramaDAO with an attached configuration
     */
    @Autowired
    public DramaDAO(Configuration configuration) {
        super(DramaTable.CREATION_DRAMA, CreationDrama.class, configuration);
    }

    @Override
    public Long getId(CreationDrama object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationDrama> fetchById(Long... values) {
        return fetch(DramaTable.CREATION_DRAMA.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationDrama fetchOneById(Long value) {
        return fetchOne(DramaTable.CREATION_DRAMA.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationDrama> fetchOptionalById(Long value) {
        return fetchOptional(DramaTable.CREATION_DRAMA.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationDrama> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(DramaTable.CREATION_DRAMA.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationDrama> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(DramaTable.CREATION_DRAMA.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<CreationDrama> fetchByUid(Long... values) {
        return fetch(DramaTable.CREATION_DRAMA.UID, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<CreationDrama> fetchByName(String... values) {
        return fetch(DramaTable.CREATION_DRAMA.NAME, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfType(String lowerInclusive, String upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<CreationDrama> fetchByType(String... values) {
        return fetch(DramaTable.CREATION_DRAMA.TYPE, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CreationDrama> fetchByStatus(String... values) {
        return fetch(DramaTable.CREATION_DRAMA.STATUS, values);
    }

    /**
     * Fetch records that have <code>product_categories BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfProductCategories(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.PRODUCT_CATEGORIES, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>product_categories IN (values)</code>
     */
    public List<CreationDrama> fetchByProductCategories(JSONB... values) {
        return fetch(DramaTable.CREATION_DRAMA.PRODUCT_CATEGORIES, values);
    }

    /**
     * Fetch records that have <code>crowd_categories BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfCrowdCategories(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.CROWD_CATEGORIES, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>crowd_categories IN (values)</code>
     */
    public List<CreationDrama> fetchByCrowdCategories(JSONB... values) {
        return fetch(DramaTable.CREATION_DRAMA.CROWD_CATEGORIES, values);
    }

    /**
     * Fetch records that have <code>videos BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfVideos(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.VIDEOS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>videos IN (values)</code>
     */
    public List<CreationDrama> fetchByVideos(JSONB... values) {
        return fetch(DramaTable.CREATION_DRAMA.VIDEOS, values);
    }

    /**
     * Fetch records that have <code>duration BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfDuration(Duration lowerInclusive, Duration upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.DURATION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>duration IN (values)</code>
     */
    public List<CreationDrama> fetchByDuration(Duration... values) {
        return fetch(DramaTable.CREATION_DRAMA.DURATION, values);
    }

    /**
     * Fetch records that have <code>roles BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfRoles(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.ROLES, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>roles IN (values)</code>
     */
    public List<CreationDrama> fetchByRoles(JSONB... values) {
        return fetch(DramaTable.CREATION_DRAMA.ROLES, values);
    }

    /**
     * Fetch records that have <code>cover_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfCoverUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.COVER_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover_url IN (values)</code>
     */
    public List<CreationDrama> fetchByCoverUrl(String... values) {
        return fetch(DramaTable.CREATION_DRAMA.COVER_URL, values);
    }

    /**
     * Fetch records that have <code>annotation BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfAnnotation(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.ANNOTATION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>annotation IN (values)</code>
     */
    public List<CreationDrama> fetchByAnnotation(JSONB... values) {
        return fetch(DramaTable.CREATION_DRAMA.ANNOTATION, values);
    }

    /**
     * Fetch records that have <code>stats BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfStats(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.STATS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>stats IN (values)</code>
     */
    public List<CreationDrama> fetchByStats(JSONB... values) {
        return fetch(DramaTable.CREATION_DRAMA.STATS, values);
    }

    /**
     * Fetch records that have <code>review BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfReview(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.REVIEW, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>review IN (values)</code>
     */
    public List<CreationDrama> fetchByReview(JSONB... values) {
        return fetch(DramaTable.CREATION_DRAMA.REVIEW, values);
    }

    /**
     * Fetch records that have <code>content BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfContent(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.CONTENT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>content IN (values)</code>
     */
    public List<CreationDrama> fetchByContent(JSONB... values) {
        return fetch(DramaTable.CREATION_DRAMA.CONTENT, values);
    }

    /**
     * Fetch records that have <code>local_sync_status BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<CreationDrama> fetchRangeOfLocalSyncStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(DramaTable.CREATION_DRAMA.LOCAL_SYNC_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>local_sync_status IN (values)</code>
     */
    public List<CreationDrama> fetchByLocalSyncStatus(String... values) {
        return fetch(DramaTable.CREATION_DRAMA.LOCAL_SYNC_STATUS, values);
    }
}
