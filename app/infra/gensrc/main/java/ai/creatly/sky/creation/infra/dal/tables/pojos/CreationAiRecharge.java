/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * AI工具充值
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationAiRecharge implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long priority;
    private Long price;
    private Long credit;
    private Long ownerId;
    private String ownerName;
    private ZonedDateTime paidTime;
    private String paidChannel;
    private Long orderId;
    private String codeUrl;
    private ZonedDateTime expireAt;
    private String payStatus;
    private Boolean status;
    private String orgCode;

    public CreationAiRecharge() {}

    public CreationAiRecharge(CreationAiRecharge value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.priority = value.priority;
        this.price = value.price;
        this.credit = value.credit;
        this.ownerId = value.ownerId;
        this.ownerName = value.ownerName;
        this.paidTime = value.paidTime;
        this.paidChannel = value.paidChannel;
        this.orderId = value.orderId;
        this.codeUrl = value.codeUrl;
        this.expireAt = value.expireAt;
        this.payStatus = value.payStatus;
        this.status = value.status;
        this.orgCode = value.orgCode;
    }

    public CreationAiRecharge(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        @Nullable Long priority,
        @Nullable Long price,
        @Nullable Long credit,
        @Nullable Long ownerId,
        @Nullable String ownerName,
        @Nullable ZonedDateTime paidTime,
        @Nullable String paidChannel,
        @Nullable Long orderId,
        @Nullable String codeUrl,
        @Nullable ZonedDateTime expireAt,
        @Nullable String payStatus,
        @Nullable Boolean status,
        @Nullable String orgCode
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.priority = priority;
        this.price = price;
        this.credit = credit;
        this.ownerId = ownerId;
        this.ownerName = ownerName;
        this.paidTime = paidTime;
        this.paidChannel = paidChannel;
        this.orderId = orderId;
        this.codeUrl = codeUrl;
        this.expireAt = expireAt;
        this.payStatus = payStatus;
        this.status = status;
        this.orgCode = orgCode;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.id</code>. id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.id</code>. id
     */
    public CreationAiRecharge setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.created_at</code>. 创建时间
     */
    public CreationAiRecharge setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.updated_at</code>. 更新时间
     */
    public CreationAiRecharge setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.priority</code>. 推荐优先级
     */
    @Nullable
    public Long getPriority() {
        return this.priority;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.priority</code>. 推荐优先级
     */
    public CreationAiRecharge setPriority(@Nullable Long priority) {
        this.priority = priority;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.price</code>. 价格
     */
    @Nullable
    public Long getPrice() {
        return this.price;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.price</code>. 价格
     */
    public CreationAiRecharge setPrice(@Nullable Long price) {
        this.price = price;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.credit</code>. 元气
     */
    @Nullable
    public Long getCredit() {
        return this.credit;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.credit</code>. 元气
     */
    public CreationAiRecharge setCredit(@Nullable Long credit) {
        this.credit = credit;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    @Nullable
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public CreationAiRecharge setOwnerId(@Nullable Long ownerId) {
        this.ownerId = ownerId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.owner_name</code>. 任务所有者名称
     */
    @Nullable
    public String getOwnerName() {
        return this.ownerName;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.owner_name</code>. 任务所有者名称
     */
    public CreationAiRecharge setOwnerName(@Nullable String ownerName) {
        this.ownerName = ownerName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.paid_time</code>. 支付时间
     */
    @Nullable
    public ZonedDateTime getPaidTime() {
        return this.paidTime;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.paid_time</code>. 支付时间
     */
    public CreationAiRecharge setPaidTime(@Nullable ZonedDateTime paidTime) {
        this.paidTime = paidTime;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.paid_channel</code>. 支付渠道
     */
    @Nullable
    public String getPaidChannel() {
        return this.paidChannel;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.paid_channel</code>. 支付渠道
     */
    public CreationAiRecharge setPaidChannel(@Nullable String paidChannel) {
        this.paidChannel = paidChannel;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.order_id</code>. 订单id
     */
    @Nullable
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.order_id</code>. 订单id
     */
    public CreationAiRecharge setOrderId(@Nullable Long orderId) {
        this.orderId = orderId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.code_url</code>. 支付二维码
     */
    @Nullable
    public String getCodeUrl() {
        return this.codeUrl;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.code_url</code>. 支付二维码
     */
    public CreationAiRecharge setCodeUrl(@Nullable String codeUrl) {
        this.codeUrl = codeUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.expire_at</code>. 超时时间
     */
    @Nullable
    public ZonedDateTime getExpireAt() {
        return this.expireAt;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.expire_at</code>. 超时时间
     */
    public CreationAiRecharge setExpireAt(@Nullable ZonedDateTime expireAt) {
        this.expireAt = expireAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.pay_status</code>. 支付状态
     */
    @Nullable
    public String getPayStatus() {
        return this.payStatus;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.pay_status</code>. 支付状态
     */
    public CreationAiRecharge setPayStatus(@Nullable String payStatus) {
        this.payStatus = payStatus;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.status</code>. 状态
     */
    @Nullable
    public Boolean getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.status</code>. 状态
     */
    public CreationAiRecharge setStatus(@Nullable Boolean status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.org_code</code>. 组织代码
     */
    @Nullable
    public String getOrgCode() {
        return this.orgCode;
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.org_code</code>. 组织代码
     */
    public CreationAiRecharge setOrgCode(@Nullable String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationAiRecharge other = (CreationAiRecharge) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.priority == null) {
            if (other.priority != null)
                return false;
        }
        else if (!this.priority.equals(other.priority))
            return false;
        if (this.price == null) {
            if (other.price != null)
                return false;
        }
        else if (!this.price.equals(other.price))
            return false;
        if (this.credit == null) {
            if (other.credit != null)
                return false;
        }
        else if (!this.credit.equals(other.credit))
            return false;
        if (this.ownerId == null) {
            if (other.ownerId != null)
                return false;
        }
        else if (!this.ownerId.equals(other.ownerId))
            return false;
        if (this.ownerName == null) {
            if (other.ownerName != null)
                return false;
        }
        else if (!this.ownerName.equals(other.ownerName))
            return false;
        if (this.paidTime == null) {
            if (other.paidTime != null)
                return false;
        }
        else if (!this.paidTime.equals(other.paidTime))
            return false;
        if (this.paidChannel == null) {
            if (other.paidChannel != null)
                return false;
        }
        else if (!this.paidChannel.equals(other.paidChannel))
            return false;
        if (this.orderId == null) {
            if (other.orderId != null)
                return false;
        }
        else if (!this.orderId.equals(other.orderId))
            return false;
        if (this.codeUrl == null) {
            if (other.codeUrl != null)
                return false;
        }
        else if (!this.codeUrl.equals(other.codeUrl))
            return false;
        if (this.expireAt == null) {
            if (other.expireAt != null)
                return false;
        }
        else if (!this.expireAt.equals(other.expireAt))
            return false;
        if (this.payStatus == null) {
            if (other.payStatus != null)
                return false;
        }
        else if (!this.payStatus.equals(other.payStatus))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.orgCode == null) {
            if (other.orgCode != null)
                return false;
        }
        else if (!this.orgCode.equals(other.orgCode))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.priority == null) ? 0 : this.priority.hashCode());
        result = prime * result + ((this.price == null) ? 0 : this.price.hashCode());
        result = prime * result + ((this.credit == null) ? 0 : this.credit.hashCode());
        result = prime * result + ((this.ownerId == null) ? 0 : this.ownerId.hashCode());
        result = prime * result + ((this.ownerName == null) ? 0 : this.ownerName.hashCode());
        result = prime * result + ((this.paidTime == null) ? 0 : this.paidTime.hashCode());
        result = prime * result + ((this.paidChannel == null) ? 0 : this.paidChannel.hashCode());
        result = prime * result + ((this.orderId == null) ? 0 : this.orderId.hashCode());
        result = prime * result + ((this.codeUrl == null) ? 0 : this.codeUrl.hashCode());
        result = prime * result + ((this.expireAt == null) ? 0 : this.expireAt.hashCode());
        result = prime * result + ((this.payStatus == null) ? 0 : this.payStatus.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.orgCode == null) ? 0 : this.orgCode.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationAiRecharge (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(priority);
        sb.append(", ").append(price);
        sb.append(", ").append(credit);
        sb.append(", ").append(ownerId);
        sb.append(", ").append(ownerName);
        sb.append(", ").append(paidTime);
        sb.append(", ").append(paidChannel);
        sb.append(", ").append(orderId);
        sb.append(", ").append(codeUrl);
        sb.append(", ").append(expireAt);
        sb.append(", ").append(payStatus);
        sb.append(", ").append(status);
        sb.append(", ").append(orgCode);

        sb.append(")");
        return sb.toString();
    }
}
