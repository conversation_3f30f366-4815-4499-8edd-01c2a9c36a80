/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.UserMaterialRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 用户素材中心
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserMaterialTable extends TableImpl<UserMaterialRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_user_material</code>
     */
    public static final UserMaterialTable CREATION_USER_MATERIAL = new UserMaterialTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserMaterialRecord> getRecordType() {
        return UserMaterialRecord.class;
    }

    /**
     * The column <code>creation.creation_user_material.id</code>. 素材主键
     */
    public final TableField<UserMaterialRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "素材主键");

    /**
     * The column <code>creation.creation_user_material.created_at</code>. 创建时间
     */
    public final TableField<UserMaterialRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_user_material.updated_at</code>. 更新时间
     */
    public final TableField<UserMaterialRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_user_material.uid</code>. 用户id
     */
    public final TableField<UserMaterialRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户id");

    /**
     * The column <code>creation.creation_user_material.material_id</code>.
     * 素材主表id
     */
    public final TableField<UserMaterialRecord, Long> MATERIAL_ID = createField(DSL.name("material_id"), SQLDataType.BIGINT.nullable(false), this, "素材主表id");

    /**
     * The column <code>creation.creation_user_material.file_id</code>. 文件id
     */
    public final TableField<UserMaterialRecord, Long> FILE_ID = createField(DSL.name("file_id"), SQLDataType.BIGINT.nullable(false), this, "文件id");

    /**
     * The column <code>creation.creation_user_material.file_url</code>.
     * 文件地址oss://
     */
    public final TableField<UserMaterialRecord, String> FILE_URL = createField(DSL.name("file_url"), SQLDataType.VARCHAR(255).nullable(false), this, "文件地址oss://");

    private UserMaterialTable(Name alias, Table<UserMaterialRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private UserMaterialTable(Name alias, Table<UserMaterialRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("用户素材中心"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_user_material</code> table
     * reference
     */
    public UserMaterialTable(String alias) {
        this(DSL.name(alias), CREATION_USER_MATERIAL);
    }

    /**
     * Create an aliased <code>creation.creation_user_material</code> table
     * reference
     */
    public UserMaterialTable(Name alias) {
        this(alias, CREATION_USER_MATERIAL);
    }

    /**
     * Create a <code>creation.creation_user_material</code> table reference
     */
    public UserMaterialTable() {
        this(DSL.name("creation_user_material"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<UserMaterialRecord> getPrimaryKey() {
        return Keys.CREATION_USER_MATERIAL_PKEY;
    }

    @Override
    public UserMaterialTable as(String alias) {
        return new UserMaterialTable(DSL.name(alias), this);
    }

    @Override
    public UserMaterialTable as(Name alias) {
        return new UserMaterialTable(alias, this);
    }

    @Override
    public UserMaterialTable as(Table<?> alias) {
        return new UserMaterialTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserMaterialTable rename(String name) {
        return new UserMaterialTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserMaterialTable rename(Name name) {
        return new UserMaterialTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserMaterialTable rename(Table<?> name) {
        return new UserMaterialTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserMaterialTable where(Condition condition) {
        return new UserMaterialTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserMaterialTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserMaterialTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserMaterialTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserMaterialTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserMaterialTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserMaterialTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserMaterialTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserMaterialTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserMaterialTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
