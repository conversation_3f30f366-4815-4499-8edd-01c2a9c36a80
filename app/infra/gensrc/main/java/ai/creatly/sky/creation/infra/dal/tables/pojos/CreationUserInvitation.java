/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 用户反馈
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationUserInvitation implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long inviterId;
    private String inviterName;
    private Long inviteeId;
    private String inviteeName;
    private String inviteCode;
    private Integer awardedCredits;

    public CreationUserInvitation() {}

    public CreationUserInvitation(CreationUserInvitation value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.inviterId = value.inviterId;
        this.inviterName = value.inviterName;
        this.inviteeId = value.inviteeId;
        this.inviteeName = value.inviteeName;
        this.inviteCode = value.inviteCode;
        this.awardedCredits = value.awardedCredits;
    }

    public CreationUserInvitation(
        @Nullable Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long inviterId,
        String inviterName,
        Long inviteeId,
        String inviteeName,
        String inviteCode,
        Integer awardedCredits
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.inviterId = inviterId;
        this.inviterName = inviterName;
        this.inviteeId = inviteeId;
        this.inviteeName = inviteeName;
        this.inviteCode = inviteCode;
        this.awardedCredits = awardedCredits;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_user_invitation.id</code>. 主键
     */
    public CreationUserInvitation setId(@Nullable Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_user_invitation.created_at</code>.
     * 创建时间
     */
    public CreationUserInvitation setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_user_invitation.updated_at</code>.
     * 更新时间
     */
    public CreationUserInvitation setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.inviter_id</code>.
     * 邀请人用户ID
     */
    public Long getInviterId() {
        return this.inviterId;
    }

    /**
     * Setter for <code>creation.creation_user_invitation.inviter_id</code>.
     * 邀请人用户ID
     */
    public CreationUserInvitation setInviterId(Long inviterId) {
        this.inviterId = inviterId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.inviter_name</code>.
     * 邀请人用户名
     */
    public String getInviterName() {
        return this.inviterName;
    }

    /**
     * Setter for <code>creation.creation_user_invitation.inviter_name</code>.
     * 邀请人用户名
     */
    public CreationUserInvitation setInviterName(String inviterName) {
        this.inviterName = inviterName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.invitee_id</code>.
     * 被邀请人用户ID
     */
    public Long getInviteeId() {
        return this.inviteeId;
    }

    /**
     * Setter for <code>creation.creation_user_invitation.invitee_id</code>.
     * 被邀请人用户ID
     */
    public CreationUserInvitation setInviteeId(Long inviteeId) {
        this.inviteeId = inviteeId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.invitee_name</code>.
     * 被邀请人用户名
     */
    public String getInviteeName() {
        return this.inviteeName;
    }

    /**
     * Setter for <code>creation.creation_user_invitation.invitee_name</code>.
     * 被邀请人用户名
     */
    public CreationUserInvitation setInviteeName(String inviteeName) {
        this.inviteeName = inviteeName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_invitation.invite_code</code>.
     * 邀请码
     */
    public String getInviteCode() {
        return this.inviteCode;
    }

    /**
     * Setter for <code>creation.creation_user_invitation.invite_code</code>.
     * 邀请码
     */
    public CreationUserInvitation setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_invitation.awarded_credits</code>. 邀请人被奖励的元气
     */
    public Integer getAwardedCredits() {
        return this.awardedCredits;
    }

    /**
     * Setter for
     * <code>creation.creation_user_invitation.awarded_credits</code>. 邀请人被奖励的元气
     */
    public CreationUserInvitation setAwardedCredits(Integer awardedCredits) {
        this.awardedCredits = awardedCredits;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationUserInvitation other = (CreationUserInvitation) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.inviterId == null) {
            if (other.inviterId != null)
                return false;
        }
        else if (!this.inviterId.equals(other.inviterId))
            return false;
        if (this.inviterName == null) {
            if (other.inviterName != null)
                return false;
        }
        else if (!this.inviterName.equals(other.inviterName))
            return false;
        if (this.inviteeId == null) {
            if (other.inviteeId != null)
                return false;
        }
        else if (!this.inviteeId.equals(other.inviteeId))
            return false;
        if (this.inviteeName == null) {
            if (other.inviteeName != null)
                return false;
        }
        else if (!this.inviteeName.equals(other.inviteeName))
            return false;
        if (this.inviteCode == null) {
            if (other.inviteCode != null)
                return false;
        }
        else if (!this.inviteCode.equals(other.inviteCode))
            return false;
        if (this.awardedCredits == null) {
            if (other.awardedCredits != null)
                return false;
        }
        else if (!this.awardedCredits.equals(other.awardedCredits))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.inviterId == null) ? 0 : this.inviterId.hashCode());
        result = prime * result + ((this.inviterName == null) ? 0 : this.inviterName.hashCode());
        result = prime * result + ((this.inviteeId == null) ? 0 : this.inviteeId.hashCode());
        result = prime * result + ((this.inviteeName == null) ? 0 : this.inviteeName.hashCode());
        result = prime * result + ((this.inviteCode == null) ? 0 : this.inviteCode.hashCode());
        result = prime * result + ((this.awardedCredits == null) ? 0 : this.awardedCredits.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationUserInvitation (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(inviterId);
        sb.append(", ").append(inviterName);
        sb.append(", ").append(inviteeId);
        sb.append(", ").append(inviteeName);
        sb.append(", ").append(inviteCode);
        sb.append(", ").append(awardedCredits);

        sb.append(")");
        return sb.toString();
    }
}
