/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal;


import ai.creatly.sky.creation.infra.dal.tables.AiRechargeTable;
import ai.creatly.sky.creation.infra.dal.tables.AiTalkActorTable;
import ai.creatly.sky.creation.infra.dal.tables.AiTalkEmotionTable;
import ai.creatly.sky.creation.infra.dal.tables.AiTaskTable;
import ai.creatly.sky.creation.infra.dal.tables.AiToolTable;
import ai.creatly.sky.creation.infra.dal.tables.AiVideoTable;
import ai.creatly.sky.creation.infra.dal.tables.AiVoiceCloneTable;
import ai.creatly.sky.creation.infra.dal.tables.AssetTable;
import ai.creatly.sky.creation.infra.dal.tables.BenefitLogTable;
import ai.creatly.sky.creation.infra.dal.tables.BusinessOrderTable;
import ai.creatly.sky.creation.infra.dal.tables.ContentMetricTable;
import ai.creatly.sky.creation.infra.dal.tables.ContentTable;
import ai.creatly.sky.creation.infra.dal.tables.ConversationMessageTable;
import ai.creatly.sky.creation.infra.dal.tables.ConversationMsgTable;
import ai.creatly.sky.creation.infra.dal.tables.ConversationTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseCertificateTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseContentTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseExamPoolTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseExamRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseExamTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseHotTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseLearnRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseOrderRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseOrgSettingTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseOrgUserTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseUserCertificateTable;
import ai.creatly.sky.creation.infra.dal.tables.CreditHistoryTable;
import ai.creatly.sky.creation.infra.dal.tables.CreditLogTable;
import ai.creatly.sky.creation.infra.dal.tables.DigHumanAvatarTable;
import ai.creatly.sky.creation.infra.dal.tables.DigHumanVideoTable;
import ai.creatly.sky.creation.infra.dal.tables.DramaTable;
import ai.creatly.sky.creation.infra.dal.tables.FeatureCostRuleTable;
import ai.creatly.sky.creation.infra.dal.tables.FeedbackTable;
import ai.creatly.sky.creation.infra.dal.tables.LlmConfigTable;
import ai.creatly.sky.creation.infra.dal.tables.MaterialTable;
import ai.creatly.sky.creation.infra.dal.tables.MemberTable;
import ai.creatly.sky.creation.infra.dal.tables.MenuItemTable;
import ai.creatly.sky.creation.infra.dal.tables.MenuTable;
import ai.creatly.sky.creation.infra.dal.tables.MusicTable;
import ai.creatly.sky.creation.infra.dal.tables.NotificationTable;
import ai.creatly.sky.creation.infra.dal.tables.OpenapiCredentialsTable;
import ai.creatly.sky.creation.infra.dal.tables.OrganizationTable;
import ai.creatly.sky.creation.infra.dal.tables.ProductVideoTable;
import ai.creatly.sky.creation.infra.dal.tables.PromptCategoryTable;
import ai.creatly.sky.creation.infra.dal.tables.PromptTagTable;
import ai.creatly.sky.creation.infra.dal.tables.PromptTemplateTable;
import ai.creatly.sky.creation.infra.dal.tables.QrcodeRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.QrcodeScanRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.ScenarioPromptTable;
import ai.creatly.sky.creation.infra.dal.tables.SoundTable;
import ai.creatly.sky.creation.infra.dal.tables.StoryActorTable;
import ai.creatly.sky.creation.infra.dal.tables.StoryExportTable;
import ai.creatly.sky.creation.infra.dal.tables.StorySceneShotTable;
import ai.creatly.sky.creation.infra.dal.tables.StorySceneTable;
import ai.creatly.sky.creation.infra.dal.tables.StoryTable;
import ai.creatly.sky.creation.infra.dal.tables.SystemPreferenceTable;
import ai.creatly.sky.creation.infra.dal.tables.TagTable;
import ai.creatly.sky.creation.infra.dal.tables.UserAssetTable;
import ai.creatly.sky.creation.infra.dal.tables.UserCreditTable;
import ai.creatly.sky.creation.infra.dal.tables.UserFileTable;
import ai.creatly.sky.creation.infra.dal.tables.UserInternalMessageTable;
import ai.creatly.sky.creation.infra.dal.tables.UserInvitationTable;
import ai.creatly.sky.creation.infra.dal.tables.UserMaterialTable;
import ai.creatly.sky.creation.infra.dal.tables.UserPermissionTable;
import ai.creatly.sky.creation.infra.dal.tables.UserPlanTable;
import ai.creatly.sky.creation.infra.dal.tables.UserPreferenceTable;
import ai.creatly.sky.creation.infra.dal.tables.UserProductTable;
import ai.creatly.sky.creation.infra.dal.tables.UserTable;
import ai.creatly.sky.creation.infra.dal.tables.UserVoiceLocaleTable;
import ai.creatly.sky.creation.infra.dal.tables.UserVoiceTable;
import ai.creatly.sky.creation.infra.dal.tables.UserWorkspaceTable;
import ai.creatly.sky.creation.infra.dal.tables.VideoProjectTable;
import ai.creatly.sky.creation.infra.dal.tables.VoiceLocaleTable;
import ai.creatly.sky.creation.infra.dal.tables.VoiceTable;
import ai.creatly.sky.creation.infra.dal.tables.WorkspaceTemplateTable;

import java.util.Arrays;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationSchema extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation</code>
     */
    public static final CreationSchema CREATION = new CreationSchema();

    /**
     * 课程
     */
    public final CourseTable COURSE = CourseTable.COURSE;

    /**
     * 证书
     */
    public final CourseCertificateTable COURSE_CERTIFICATE = CourseCertificateTable.COURSE_CERTIFICATE;

    /**
     * 课程目录
     */
    public final CourseContentTable COURSE_CONTENT = CourseContentTable.COURSE_CONTENT;

    /**
     * 考试记录
     */
    public final CourseExamTable COURSE_EXAM = CourseExamTable.COURSE_EXAM;

    /**
     * 考试题库
     */
    public final CourseExamPoolTable COURSE_EXAM_POOL = CourseExamPoolTable.COURSE_EXAM_POOL;

    /**
     * 考试记录
     */
    public final CourseExamRecordTable COURSE_EXAM_RECORD = CourseExamRecordTable.COURSE_EXAM_RECORD;

    /**
     * 热门课程
     */
    public final CourseHotTable COURSE_HOT = CourseHotTable.COURSE_HOT;

    /**
     * 课程学习记录
     */
    public final CourseLearnRecordTable COURSE_LEARN_RECORD = CourseLearnRecordTable.COURSE_LEARN_RECORD;

    /**
     * 课程购买记录
     */
    public final CourseOrderRecordTable COURSE_ORDER_RECORD = CourseOrderRecordTable.COURSE_ORDER_RECORD;

    /**
     * 机构课程购买记录
     */
    public final CourseOrgSettingTable COURSE_ORG_SETTING = CourseOrgSettingTable.COURSE_ORG_SETTING;

    /**
     * 机构学员课程开课记录
     */
    public final CourseOrgUserTable COURSE_ORG_USER = CourseOrgUserTable.COURSE_ORG_USER;

    /**
     * 用户证书
     */
    public final CourseUserCertificateTable COURSE_USER_CERTIFICATE = CourseUserCertificateTable.COURSE_USER_CERTIFICATE;

    /**
     * AI工具充值
     */
    public final AiRechargeTable CREATION_AI_RECHARGE = AiRechargeTable.CREATION_AI_RECHARGE;

    /**
     * AI Talk 演讲者
     */
    public final AiTalkActorTable CREATION_AI_TALK_ACTOR = AiTalkActorTable.CREATION_AI_TALK_ACTOR;

    /**
     * AI Talk 表情模板
     */
    public final AiTalkEmotionTable CREATION_AI_TALK_EMOTION = AiTalkEmotionTable.CREATION_AI_TALK_EMOTION;

    /**
     * AI创作任务
     */
    public final AiTaskTable CREATION_AI_TASK = AiTaskTable.CREATION_AI_TASK;

    /**
     * AI工具集
     */
    public final AiToolTable CREATION_AI_TOOL = AiToolTable.CREATION_AI_TOOL;

    /**
     * 音乐库
     */
    public final AiVideoTable CREATION_AI_VIDEO = AiVideoTable.CREATION_AI_VIDEO;

    /**
     * 用户声音克隆
     */
    public final AiVoiceCloneTable CREATION_AI_VOICE_CLONE = AiVoiceCloneTable.CREATION_AI_VOICE_CLONE;

    /**
     * 作品
     */
    public final AssetTable CREATION_ASSET = AssetTable.CREATION_ASSET;

    /**
     * 权益发放记录
     */
    public final BenefitLogTable CREATION_BENEFIT_LOG = BenefitLogTable.CREATION_BENEFIT_LOG;

    /**
     * 课程
     */
    public final BusinessOrderTable CREATION_BUSINESS_ORDER = BusinessOrderTable.CREATION_BUSINESS_ORDER;

    /**
     * 星耀内容大宽表
     */
    public final ContentTable CREATION_CONTENT = ContentTable.CREATION_CONTENT;

    /**
     * 内容指标明细
     */
    public final ContentMetricTable CREATION_CONTENT_METRIC = ContentMetricTable.CREATION_CONTENT_METRIC;

    /**
     * 会话
     */
    public final ConversationTable CREATION_CONVERSATION = ConversationTable.CREATION_CONVERSATION;

    /**
     * 会话消息
     */
    public final ConversationMessageTable CREATION_CONVERSATION_MESSAGE = ConversationMessageTable.CREATION_CONVERSATION_MESSAGE;

    /**
     * 场景提示词
     */
    public final ConversationMsgTable CREATION_CONVERSATION_MSG = ConversationMsgTable.CREATION_CONVERSATION_MSG;

    /**
     * 余额使用记录
     */
    public final CreditHistoryTable CREATION_CREDIT_HISTORY = CreditHistoryTable.CREATION_CREDIT_HISTORY;

    /**
     * 用户余额账户流水
     */
    public final CreditLogTable CREATION_CREDIT_LOG = CreditLogTable.CREATION_CREDIT_LOG;

    /**
     * 剧情
     */
    public final DramaTable CREATION_DRAMA = DramaTable.CREATION_DRAMA;

    /**
     * 计费规则
     */
    public final FeatureCostRuleTable CREATION_FEATURE_COST_RULE = FeatureCostRuleTable.CREATION_FEATURE_COST_RULE;

    /**
     * 用户反馈
     */
    public final FeedbackTable CREATION_FEEDBACK = FeedbackTable.CREATION_FEEDBACK;

    /**
     * LLM配置表
     */
    public final LlmConfigTable CREATION_LLM_CONFIG = LlmConfigTable.CREATION_LLM_CONFIG;

    /**
     * 素材记录
     */
    public final MaterialTable CREATION_MATERIAL = MaterialTable.CREATION_MATERIAL;

    /**
     * 会员
     */
    public final MemberTable CREATION_MEMBER = MemberTable.CREATION_MEMBER;

    /**
     * 菜单
     */
    public final MenuTable CREATION_MENU = MenuTable.CREATION_MENU;

    /**
     * 菜单项
     */
    public final MenuItemTable CREATION_MENU_ITEM = MenuItemTable.CREATION_MENU_ITEM;

    /**
     * 音乐库
     */
    public final MusicTable CREATION_MUSIC = MusicTable.CREATION_MUSIC;

    /**
     * 用户反馈
     */
    public final NotificationTable CREATION_NOTIFICATION = NotificationTable.CREATION_NOTIFICATION;

    /**
     * openapi密钥表
     */
    public final OpenapiCredentialsTable CREATION_OPENAPI_CREDENTIALS = OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS;

    /**
     * 商品视频
     */
    public final ProductVideoTable CREATION_PRODUCT_VIDEO = ProductVideoTable.CREATION_PRODUCT_VIDEO;

    /**
     * 提示词类目
     */
    public final PromptCategoryTable CREATION_PROMPT_CATEGORY = PromptCategoryTable.CREATION_PROMPT_CATEGORY;

    /**
     * 提示词标签
     */
    public final PromptTagTable CREATION_PROMPT_TAG = PromptTagTable.CREATION_PROMPT_TAG;

    /**
     * 提示词模板
     */
    public final PromptTemplateTable CREATION_PROMPT_TEMPLATE = PromptTemplateTable.CREATION_PROMPT_TEMPLATE;

    /**
     * 场景提示词
     */
    public final ScenarioPromptTable CREATION_SCENARIO_PROMPT = ScenarioPromptTable.CREATION_SCENARIO_PROMPT;

    /**
     * 音效库
     */
    public final SoundTable CREATION_SOUND = SoundTable.CREATION_SOUND;

    /**
     * 故事
     */
    public final StoryTable CREATION_STORY = StoryTable.CREATION_STORY;

    /**
     * 故事演员
     */
    public final StoryActorTable CREATION_STORY_ACTOR = StoryActorTable.CREATION_STORY_ACTOR;

    /**
     * 故事导出信息表
     */
    public final StoryExportTable CREATION_STORY_EXPORT = StoryExportTable.CREATION_STORY_EXPORT;

    /**
     * 故事场景
     */
    public final StorySceneTable CREATION_STORY_SCENE = StorySceneTable.CREATION_STORY_SCENE;

    /**
     * 场景分镜
     */
    public final StorySceneShotTable CREATION_STORY_SCENE_SHOT = StorySceneShotTable.CREATION_STORY_SCENE_SHOT;

    /**
     * 标签
     */
    public final TagTable CREATION_TAG = TagTable.CREATION_TAG;

    /**
     * 用户素材库
     */
    public final UserAssetTable CREATION_USER_ASSET = UserAssetTable.CREATION_USER_ASSET;

    /**
     * 用户文件
     */
    public final UserFileTable CREATION_USER_FILE = UserFileTable.CREATION_USER_FILE;

    /**
     * 用户站内消息
     */
    public final UserInternalMessageTable CREATION_USER_INTERNAL_MESSAGE = UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE;

    /**
     * 用户反馈
     */
    public final UserInvitationTable CREATION_USER_INVITATION = UserInvitationTable.CREATION_USER_INVITATION;

    /**
     * 用户素材中心
     */
    public final UserMaterialTable CREATION_USER_MATERIAL = UserMaterialTable.CREATION_USER_MATERIAL;

    /**
     * 用户私人定价计划
     */
    public final UserPlanTable CREATION_USER_PLAN = UserPlanTable.CREATION_USER_PLAN;

    /**
     * 用户产品
     */
    public final UserProductTable CREATION_USER_PRODUCT = UserProductTable.CREATION_USER_PRODUCT;

    /**
     * 声音演员
     */
    public final UserVoiceTable CREATION_USER_VOICE = UserVoiceTable.CREATION_USER_VOICE;

    /**
     * 声音定制的口音
     */
    public final UserVoiceLocaleTable CREATION_USER_VOICE_LOCALE = UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE;

    /**
     * 用户创作空间模版
     */
    public final UserWorkspaceTable CREATION_USER_WORKSPACE = UserWorkspaceTable.CREATION_USER_WORKSPACE;

    /**
     * 视频项目
     */
    public final VideoProjectTable CREATION_VIDEO_PROJECT = VideoProjectTable.CREATION_VIDEO_PROJECT;

    /**
     * 声音演员
     */
    public final VoiceTable CREATION_VOICE = VoiceTable.CREATION_VOICE;

    /**
     * 声音演员的口音
     */
    public final VoiceLocaleTable CREATION_VOICE_LOCALE = VoiceLocaleTable.CREATION_VOICE_LOCALE;

    /**
     * The table <code>creation.creation_workspace_template</code>.
     */
    public final WorkspaceTemplateTable CREATION_WORKSPACE_TEMPLATE = WorkspaceTemplateTable.CREATION_WORKSPACE_TEMPLATE;

    /**
     * 数字形象表
     */
    public final DigHumanAvatarTable DIG_HUMAN_AVATAR = DigHumanAvatarTable.DIG_HUMAN_AVATAR;

    /**
     * 数字人视频
     */
    public final DigHumanVideoTable DIG_HUMAN_VIDEO = DigHumanVideoTable.DIG_HUMAN_VIDEO;

    /**
     * 组织主表
     */
    public final OrganizationTable ORGANIZATION = OrganizationTable.ORGANIZATION;

    /**
     * 权益中心-码业务平台
     */
    public final QrcodeRecordTable QRCODE_RECORD = QrcodeRecordTable.QRCODE_RECORD;

    /**
     * 权益中心-用户扫码记录
     */
    public final QrcodeScanRecordTable QRCODE_SCAN_RECORD = QrcodeScanRecordTable.QRCODE_SCAN_RECORD;

    /**
     * 系统配置中心
     */
    public final SystemPreferenceTable SYSTEM_PREFERENCE = SystemPreferenceTable.SYSTEM_PREFERENCE;

    /**
     * 用户表
     */
    public final UserTable USER = UserTable.USER;

    /**
     * 用户荣誉表
     */
    public final UserCreditTable USER_CREDIT = UserCreditTable.USER_CREDIT;

    /**
     * 用户权限表
     */
    public final UserPermissionTable USER_PERMISSION = UserPermissionTable.USER_PERMISSION;

    /**
     * 用户配置中心
     */
    public final UserPreferenceTable USER_PREFERENCE = UserPreferenceTable.USER_PREFERENCE;

    /**
     * No further instances allowed
     */
    private CreationSchema() {
        super("creation", null);
    }


    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        return Arrays.asList(
            CourseTable.COURSE,
            CourseCertificateTable.COURSE_CERTIFICATE,
            CourseContentTable.COURSE_CONTENT,
            CourseExamTable.COURSE_EXAM,
            CourseExamPoolTable.COURSE_EXAM_POOL,
            CourseExamRecordTable.COURSE_EXAM_RECORD,
            CourseHotTable.COURSE_HOT,
            CourseLearnRecordTable.COURSE_LEARN_RECORD,
            CourseOrderRecordTable.COURSE_ORDER_RECORD,
            CourseOrgSettingTable.COURSE_ORG_SETTING,
            CourseOrgUserTable.COURSE_ORG_USER,
            CourseUserCertificateTable.COURSE_USER_CERTIFICATE,
            AiRechargeTable.CREATION_AI_RECHARGE,
            AiTalkActorTable.CREATION_AI_TALK_ACTOR,
            AiTalkEmotionTable.CREATION_AI_TALK_EMOTION,
            AiTaskTable.CREATION_AI_TASK,
            AiToolTable.CREATION_AI_TOOL,
            AiVideoTable.CREATION_AI_VIDEO,
            AiVoiceCloneTable.CREATION_AI_VOICE_CLONE,
            AssetTable.CREATION_ASSET,
            BenefitLogTable.CREATION_BENEFIT_LOG,
            BusinessOrderTable.CREATION_BUSINESS_ORDER,
            ContentTable.CREATION_CONTENT,
            ContentMetricTable.CREATION_CONTENT_METRIC,
            ConversationTable.CREATION_CONVERSATION,
            ConversationMessageTable.CREATION_CONVERSATION_MESSAGE,
            ConversationMsgTable.CREATION_CONVERSATION_MSG,
            CreditHistoryTable.CREATION_CREDIT_HISTORY,
            CreditLogTable.CREATION_CREDIT_LOG,
            DramaTable.CREATION_DRAMA,
            FeatureCostRuleTable.CREATION_FEATURE_COST_RULE,
            FeedbackTable.CREATION_FEEDBACK,
            LlmConfigTable.CREATION_LLM_CONFIG,
            MaterialTable.CREATION_MATERIAL,
            MemberTable.CREATION_MEMBER,
            MenuTable.CREATION_MENU,
            MenuItemTable.CREATION_MENU_ITEM,
            MusicTable.CREATION_MUSIC,
            NotificationTable.CREATION_NOTIFICATION,
            OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS,
            ProductVideoTable.CREATION_PRODUCT_VIDEO,
            PromptCategoryTable.CREATION_PROMPT_CATEGORY,
            PromptTagTable.CREATION_PROMPT_TAG,
            PromptTemplateTable.CREATION_PROMPT_TEMPLATE,
            ScenarioPromptTable.CREATION_SCENARIO_PROMPT,
            SoundTable.CREATION_SOUND,
            StoryTable.CREATION_STORY,
            StoryActorTable.CREATION_STORY_ACTOR,
            StoryExportTable.CREATION_STORY_EXPORT,
            StorySceneTable.CREATION_STORY_SCENE,
            StorySceneShotTable.CREATION_STORY_SCENE_SHOT,
            TagTable.CREATION_TAG,
            UserAssetTable.CREATION_USER_ASSET,
            UserFileTable.CREATION_USER_FILE,
            UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE,
            UserInvitationTable.CREATION_USER_INVITATION,
            UserMaterialTable.CREATION_USER_MATERIAL,
            UserPlanTable.CREATION_USER_PLAN,
            UserProductTable.CREATION_USER_PRODUCT,
            UserVoiceTable.CREATION_USER_VOICE,
            UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE,
            UserWorkspaceTable.CREATION_USER_WORKSPACE,
            VideoProjectTable.CREATION_VIDEO_PROJECT,
            VoiceTable.CREATION_VOICE,
            VoiceLocaleTable.CREATION_VOICE_LOCALE,
            WorkspaceTemplateTable.CREATION_WORKSPACE_TEMPLATE,
            DigHumanAvatarTable.DIG_HUMAN_AVATAR,
            DigHumanVideoTable.DIG_HUMAN_VIDEO,
            OrganizationTable.ORGANIZATION,
            QrcodeRecordTable.QRCODE_RECORD,
            QrcodeScanRecordTable.QRCODE_SCAN_RECORD,
            SystemPreferenceTable.SYSTEM_PREFERENCE,
            UserTable.USER,
            UserCreditTable.USER_CREDIT,
            UserPermissionTable.USER_PERMISSION,
            UserPreferenceTable.USER_PREFERENCE
        );
    }
}
