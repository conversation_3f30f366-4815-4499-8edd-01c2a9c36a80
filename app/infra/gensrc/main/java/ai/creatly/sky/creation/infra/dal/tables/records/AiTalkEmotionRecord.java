/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.AiTalkEmotionTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiTalkEmotion;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * AI Talk 表情模板
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiTalkEmotionRecord extends UpdatableRecordImpl<AiTalkEmotionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_ai_talk_emotion.id</code>. 主键
     */
    public AiTalkEmotionRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_emotion.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_emotion.created_at</code>.
     * 创建时间
     */
    public AiTalkEmotionRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_emotion.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_emotion.updated_at</code>.
     * 更新时间
     */
    public AiTalkEmotionRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_emotion.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_emotion.uid</code>. 用户ID
     */
    public AiTalkEmotionRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_emotion.uid</code>. 用户ID
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_emotion.name</code>. 表情名称
     */
    public AiTalkEmotionRecord setName(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_emotion.name</code>. 表情名称
     */
    public String getName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_emotion.code</code>.
     * 表情code（全局唯一）
     */
    public AiTalkEmotionRecord setCode(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_emotion.code</code>.
     * 表情code（全局唯一）
     */
    public String getCode() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_emotion.video_id</code>.
     * 表情视频文件id
     */
    public AiTalkEmotionRecord setVideoId(Long value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_emotion.video_id</code>.
     * 表情视频文件id
     */
    public Long getVideoId() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_emotion.video_url</code>.
     * 表情视频文件地址
     */
    public AiTalkEmotionRecord setVideoUrl(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_emotion.video_url</code>.
     * 表情视频文件地址
     */
    public String getVideoUrl() {
        return (String) get(7);
    }

    /**
     * Setter for
     * <code>creation.creation_ai_talk_emotion.template_file_id</code>. 表情模型文件ID
     */
    public AiTalkEmotionRecord setTemplateFileId(@Nullable Long value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_ai_talk_emotion.template_file_id</code>. 表情模型文件ID
     */
    @Nullable
    public Long getTemplateFileId() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_emotion.template_url</code>.
     * 表情模型文件地址
     */
    public AiTalkEmotionRecord setTemplateUrl(@Nullable String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_emotion.template_url</code>.
     * 表情模型文件地址
     */
    @Nullable
    public String getTemplateUrl() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_emotion.embedded</code>.
     * 是否系统内置
     */
    public AiTalkEmotionRecord setEmbedded(Boolean value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_emotion.embedded</code>.
     * 是否系统内置
     */
    public Boolean getEmbedded() {
        return (Boolean) get(10);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_emotion.status</code>. 当前状态
     */
    public AiTalkEmotionRecord setStatus(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_emotion.status</code>. 当前状态
     */
    public String getStatus() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_emotion.creator</code>. 创建者
     */
    public AiTalkEmotionRecord setCreator(JSONB value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_emotion.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return (JSONB) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AiTalkEmotionRecord
     */
    public AiTalkEmotionRecord() {
        super(AiTalkEmotionTable.CREATION_AI_TALK_EMOTION);
    }

    /**
     * Create a detached, initialised AiTalkEmotionRecord
     */
    public AiTalkEmotionRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String name, String code, Long videoId, String videoUrl, @Nullable Long templateFileId, @Nullable String templateUrl, Boolean embedded, String status, JSONB creator) {
        super(AiTalkEmotionTable.CREATION_AI_TALK_EMOTION);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setName(name);
        setCode(code);
        setVideoId(videoId);
        setVideoUrl(videoUrl);
        setTemplateFileId(templateFileId);
        setTemplateUrl(templateUrl);
        setEmbedded(embedded);
        setStatus(status);
        setCreator(creator);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised AiTalkEmotionRecord
     */
    public AiTalkEmotionRecord(CreationAiTalkEmotion value) {
        super(AiTalkEmotionTable.CREATION_AI_TALK_EMOTION);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setName(value.getName());
            setCode(value.getCode());
            setVideoId(value.getVideoId());
            setVideoUrl(value.getVideoUrl());
            setTemplateFileId(value.getTemplateFileId());
            setTemplateUrl(value.getTemplateUrl());
            setEmbedded(value.getEmbedded());
            setStatus(value.getStatus());
            setCreator(value.getCreator());
            resetChangedOnNotNull();
        }
    }
}
