/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Arrays;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 音效库
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationSound implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String status;
    private String sourceType;
    private String sourceNo;
    private String authorName;
    private String name;
    private String[] tagNames;
    private Long audioId;
    private String audioUrl;
    private String audioFormat;
    private Duration duration;
    private Integer bitrate;
    private Integer sampleRate;
    private JSONB creator;
    private JSONB updater;

    public CreationSound() {}

    public CreationSound(CreationSound value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.status = value.status;
        this.sourceType = value.sourceType;
        this.sourceNo = value.sourceNo;
        this.authorName = value.authorName;
        this.name = value.name;
        this.tagNames = value.tagNames;
        this.audioId = value.audioId;
        this.audioUrl = value.audioUrl;
        this.audioFormat = value.audioFormat;
        this.duration = value.duration;
        this.bitrate = value.bitrate;
        this.sampleRate = value.sampleRate;
        this.creator = value.creator;
        this.updater = value.updater;
    }

    public CreationSound(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String status,
        String sourceType,
        String sourceNo,
        String authorName,
        String name,
        String[] tagNames,
        Long audioId,
        String audioUrl,
        String audioFormat,
        Duration duration,
        Integer bitrate,
        Integer sampleRate,
        JSONB creator,
        JSONB updater
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.status = status;
        this.sourceType = sourceType;
        this.sourceNo = sourceNo;
        this.authorName = authorName;
        this.name = name;
        this.tagNames = tagNames;
        this.audioId = audioId;
        this.audioUrl = audioUrl;
        this.audioFormat = audioFormat;
        this.duration = duration;
        this.bitrate = bitrate;
        this.sampleRate = sampleRate;
        this.creator = creator;
        this.updater = updater;
    }

    /**
     * Getter for <code>creation.creation_sound.id</code>. 主键ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_sound.id</code>. 主键ID
     */
    public CreationSound setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_sound.created_at</code>. 创建时间
     */
    public CreationSound setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_sound.updated_at</code>. 更新时间
     */
    public CreationSound setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.uid</code>. 所属用户ID（1为平台）
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_sound.uid</code>. 所属用户ID（1为平台）
     */
    public CreationSound setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.status</code>. 状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_sound.status</code>. 状态
     */
    public CreationSound setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.source_type</code>. 来源方类型
     */
    public String getSourceType() {
        return this.sourceType;
    }

    /**
     * Setter for <code>creation.creation_sound.source_type</code>. 来源方类型
     */
    public CreationSound setSourceType(String sourceType) {
        this.sourceType = sourceType;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.source_no</code>. 来源方的音效编号
     */
    public String getSourceNo() {
        return this.sourceNo;
    }

    /**
     * Setter for <code>creation.creation_sound.source_no</code>. 来源方的音效编号
     */
    public CreationSound setSourceNo(String sourceNo) {
        this.sourceNo = sourceNo;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.author_name</code>. 音效作者
     */
    public String getAuthorName() {
        return this.authorName;
    }

    /**
     * Setter for <code>creation.creation_sound.author_name</code>. 音效作者
     */
    public CreationSound setAuthorName(String authorName) {
        this.authorName = authorName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.name</code>. 音效名称
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.creation_sound.name</code>. 音效名称
     */
    public CreationSound setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.tag_names</code>. 音效标签名列表
     */
    public String[] getTagNames() {
        return this.tagNames;
    }

    /**
     * Setter for <code>creation.creation_sound.tag_names</code>. 音效标签名列表
     */
    public CreationSound setTagNames(String[] tagNames) {
        this.tagNames = tagNames;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.audio_id</code>.
     */
    public Long getAudioId() {
        return this.audioId;
    }

    /**
     * Setter for <code>creation.creation_sound.audio_id</code>.
     */
    public CreationSound setAudioId(Long audioId) {
        this.audioId = audioId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.audio_url</code>. 音效文件地址（OSS格式）
     */
    public String getAudioUrl() {
        return this.audioUrl;
    }

    /**
     * Setter for <code>creation.creation_sound.audio_url</code>. 音效文件地址（OSS格式）
     */
    public CreationSound setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.audio_format</code>. 音效文件格式（小写）
     */
    public String getAudioFormat() {
        return this.audioFormat;
    }

    /**
     * Setter for <code>creation.creation_sound.audio_format</code>. 音效文件格式（小写）
     */
    public CreationSound setAudioFormat(String audioFormat) {
        this.audioFormat = audioFormat;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.duration</code>. 音效时长
     */
    public Duration getDuration() {
        return this.duration;
    }

    /**
     * Setter for <code>creation.creation_sound.duration</code>. 音效时长
     */
    public CreationSound setDuration(Duration duration) {
        this.duration = duration;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.bitrate</code>. 比特率（kps）
     */
    public Integer getBitrate() {
        return this.bitrate;
    }

    /**
     * Setter for <code>creation.creation_sound.bitrate</code>. 比特率（kps）
     */
    public CreationSound setBitrate(Integer bitrate) {
        this.bitrate = bitrate;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.sample_rate</code>. 采样率（Hz）
     */
    public Integer getSampleRate() {
        return this.sampleRate;
    }

    /**
     * Setter for <code>creation.creation_sound.sample_rate</code>. 采样率（Hz）
     */
    public CreationSound setSampleRate(Integer sampleRate) {
        this.sampleRate = sampleRate;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return this.creator;
    }

    /**
     * Setter for <code>creation.creation_sound.creator</code>. 创建者
     */
    public CreationSound setCreator(JSONB creator) {
        this.creator = creator;
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.updater</code>. 更新者
     */
    public JSONB getUpdater() {
        return this.updater;
    }

    /**
     * Setter for <code>creation.creation_sound.updater</code>. 更新者
     */
    public CreationSound setUpdater(JSONB updater) {
        this.updater = updater;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationSound other = (CreationSound) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.sourceType == null) {
            if (other.sourceType != null)
                return false;
        }
        else if (!this.sourceType.equals(other.sourceType))
            return false;
        if (this.sourceNo == null) {
            if (other.sourceNo != null)
                return false;
        }
        else if (!this.sourceNo.equals(other.sourceNo))
            return false;
        if (this.authorName == null) {
            if (other.authorName != null)
                return false;
        }
        else if (!this.authorName.equals(other.authorName))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.tagNames == null) {
            if (other.tagNames != null)
                return false;
        }
        else if (!Arrays.deepEquals(this.tagNames, other.tagNames))
            return false;
        if (this.audioId == null) {
            if (other.audioId != null)
                return false;
        }
        else if (!this.audioId.equals(other.audioId))
            return false;
        if (this.audioUrl == null) {
            if (other.audioUrl != null)
                return false;
        }
        else if (!this.audioUrl.equals(other.audioUrl))
            return false;
        if (this.audioFormat == null) {
            if (other.audioFormat != null)
                return false;
        }
        else if (!this.audioFormat.equals(other.audioFormat))
            return false;
        if (this.duration == null) {
            if (other.duration != null)
                return false;
        }
        else if (!this.duration.equals(other.duration))
            return false;
        if (this.bitrate == null) {
            if (other.bitrate != null)
                return false;
        }
        else if (!this.bitrate.equals(other.bitrate))
            return false;
        if (this.sampleRate == null) {
            if (other.sampleRate != null)
                return false;
        }
        else if (!this.sampleRate.equals(other.sampleRate))
            return false;
        if (this.creator == null) {
            if (other.creator != null)
                return false;
        }
        else if (!this.creator.equals(other.creator))
            return false;
        if (this.updater == null) {
            if (other.updater != null)
                return false;
        }
        else if (!this.updater.equals(other.updater))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.sourceType == null) ? 0 : this.sourceType.hashCode());
        result = prime * result + ((this.sourceNo == null) ? 0 : this.sourceNo.hashCode());
        result = prime * result + ((this.authorName == null) ? 0 : this.authorName.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.tagNames == null) ? 0 : Arrays.deepHashCode(this.tagNames));
        result = prime * result + ((this.audioId == null) ? 0 : this.audioId.hashCode());
        result = prime * result + ((this.audioUrl == null) ? 0 : this.audioUrl.hashCode());
        result = prime * result + ((this.audioFormat == null) ? 0 : this.audioFormat.hashCode());
        result = prime * result + ((this.duration == null) ? 0 : this.duration.hashCode());
        result = prime * result + ((this.bitrate == null) ? 0 : this.bitrate.hashCode());
        result = prime * result + ((this.sampleRate == null) ? 0 : this.sampleRate.hashCode());
        result = prime * result + ((this.creator == null) ? 0 : this.creator.hashCode());
        result = prime * result + ((this.updater == null) ? 0 : this.updater.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationSound (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(status);
        sb.append(", ").append(sourceType);
        sb.append(", ").append(sourceNo);
        sb.append(", ").append(authorName);
        sb.append(", ").append(name);
        sb.append(", ").append(Arrays.deepToString(tagNames));
        sb.append(", ").append(audioId);
        sb.append(", ").append(audioUrl);
        sb.append(", ").append(audioFormat);
        sb.append(", ").append(duration);
        sb.append(", ").append(bitrate);
        sb.append(", ").append(sampleRate);
        sb.append(", ").append(creator);
        sb.append(", ").append(updater);

        sb.append(")");
        return sb.toString();
    }
}
