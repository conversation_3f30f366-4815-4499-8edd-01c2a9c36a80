/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.WorkspaceTemplateTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationWorkspaceTemplate;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class WorkspaceTemplateRecord extends UpdatableRecordImpl<WorkspaceTemplateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_workspace_template.id</code>.
     */
    public WorkspaceTemplateRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.id</code>.
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_workspace_template.created_at</code>.
     */
    public WorkspaceTemplateRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.created_at</code>.
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_workspace_template.updated_at</code>.
     */
    public WorkspaceTemplateRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.updated_at</code>.
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_workspace_template.name</code>.
     */
    public WorkspaceTemplateRecord setName(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.name</code>.
     */
    public String getName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_workspace_template.type</code>.
     */
    public WorkspaceTemplateRecord setType(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.type</code>.
     */
    public String getType() {
        return (String) get(4);
    }

    /**
     * Setter for
     * <code>creation.creation_workspace_template.workspace_config</code>.
     */
    public WorkspaceTemplateRecord setWorkspaceConfig(JSONB value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_workspace_template.workspace_config</code>.
     */
    public JSONB getWorkspaceConfig() {
        return (JSONB) get(5);
    }

    /**
     * Setter for <code>creation.creation_workspace_template.status</code>.
     */
    public WorkspaceTemplateRecord setStatus(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.status</code>.
     */
    public String getStatus() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_workspace_template.tag</code>. 模板标签
     */
    public WorkspaceTemplateRecord setTag(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.tag</code>. 模板标签
     */
    @Nullable
    public String getTag() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached WorkspaceTemplateRecord
     */
    public WorkspaceTemplateRecord() {
        super(WorkspaceTemplateTable.CREATION_WORKSPACE_TEMPLATE);
    }

    /**
     * Create a detached, initialised WorkspaceTemplateRecord
     */
    public WorkspaceTemplateRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, String name, String type, JSONB workspaceConfig, String status, @Nullable String tag) {
        super(WorkspaceTemplateTable.CREATION_WORKSPACE_TEMPLATE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setName(name);
        setType(type);
        setWorkspaceConfig(workspaceConfig);
        setStatus(status);
        setTag(tag);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised WorkspaceTemplateRecord
     */
    public WorkspaceTemplateRecord(CreationWorkspaceTemplate value) {
        super(WorkspaceTemplateTable.CREATION_WORKSPACE_TEMPLATE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setName(value.getName());
            setType(value.getType());
            setWorkspaceConfig(value.getWorkspaceConfig());
            setStatus(value.getStatus());
            setTag(value.getTag());
            resetChangedOnNotNull();
        }
    }
}
