/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 考试题库
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseExamPool implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long certId;
    private String question;
    private String content;
    private String answer;
    private String type;
    private Long score;

    public CourseExamPool() {}

    public CourseExamPool(CourseExamPool value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.certId = value.certId;
        this.question = value.question;
        this.content = value.content;
        this.answer = value.answer;
        this.type = value.type;
        this.score = value.score;
    }

    public CourseExamPool(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        @Nullable Long certId,
        @Nullable String question,
        @Nullable String content,
        @Nullable String answer,
        @Nullable String type,
        @Nullable Long score
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.certId = certId;
        this.question = question;
        this.content = content;
        this.answer = answer;
        this.type = type;
        this.score = score;
    }

    /**
     * Getter for <code>creation.course_exam_pool.id</code>. 题库ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.course_exam_pool.id</code>. 题库ID
     */
    public CourseExamPool setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.course_exam_pool.created_at</code>. 创建时间
     */
    public CourseExamPool setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.course_exam_pool.updated_at</code>. 更新时间
     */
    public CourseExamPool setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.cert_id</code>. 证书ID
     */
    @Nullable
    public Long getCertId() {
        return this.certId;
    }

    /**
     * Setter for <code>creation.course_exam_pool.cert_id</code>. 证书ID
     */
    public CourseExamPool setCertId(@Nullable Long certId) {
        this.certId = certId;
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.question</code>. 题目
     */
    @Nullable
    public String getQuestion() {
        return this.question;
    }

    /**
     * Setter for <code>creation.course_exam_pool.question</code>. 题目
     */
    public CourseExamPool setQuestion(@Nullable String question) {
        this.question = question;
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.content</code>. 内容
     */
    @Nullable
    public String getContent() {
        return this.content;
    }

    /**
     * Setter for <code>creation.course_exam_pool.content</code>. 内容
     */
    public CourseExamPool setContent(@Nullable String content) {
        this.content = content;
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.answer</code>. 标准答案
     */
    @Nullable
    public String getAnswer() {
        return this.answer;
    }

    /**
     * Setter for <code>creation.course_exam_pool.answer</code>. 标准答案
     */
    public CourseExamPool setAnswer(@Nullable String answer) {
        this.answer = answer;
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.type</code>. 题目类型
     */
    @Nullable
    public String getType() {
        return this.type;
    }

    /**
     * Setter for <code>creation.course_exam_pool.type</code>. 题目类型
     */
    public CourseExamPool setType(@Nullable String type) {
        this.type = type;
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.score</code>. 标准得分
     */
    @Nullable
    public Long getScore() {
        return this.score;
    }

    /**
     * Setter for <code>creation.course_exam_pool.score</code>. 标准得分
     */
    public CourseExamPool setScore(@Nullable Long score) {
        this.score = score;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CourseExamPool other = (CourseExamPool) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.certId == null) {
            if (other.certId != null)
                return false;
        }
        else if (!this.certId.equals(other.certId))
            return false;
        if (this.question == null) {
            if (other.question != null)
                return false;
        }
        else if (!this.question.equals(other.question))
            return false;
        if (this.content == null) {
            if (other.content != null)
                return false;
        }
        else if (!this.content.equals(other.content))
            return false;
        if (this.answer == null) {
            if (other.answer != null)
                return false;
        }
        else if (!this.answer.equals(other.answer))
            return false;
        if (this.type == null) {
            if (other.type != null)
                return false;
        }
        else if (!this.type.equals(other.type))
            return false;
        if (this.score == null) {
            if (other.score != null)
                return false;
        }
        else if (!this.score.equals(other.score))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.certId == null) ? 0 : this.certId.hashCode());
        result = prime * result + ((this.question == null) ? 0 : this.question.hashCode());
        result = prime * result + ((this.content == null) ? 0 : this.content.hashCode());
        result = prime * result + ((this.answer == null) ? 0 : this.answer.hashCode());
        result = prime * result + ((this.type == null) ? 0 : this.type.hashCode());
        result = prime * result + ((this.score == null) ? 0 : this.score.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseExamPool (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(certId);
        sb.append(", ").append(question);
        sb.append(", ").append(content);
        sb.append(", ").append(answer);
        sb.append(", ").append(type);
        sb.append(", ").append(score);

        sb.append(")");
        return sb.toString();
    }
}
