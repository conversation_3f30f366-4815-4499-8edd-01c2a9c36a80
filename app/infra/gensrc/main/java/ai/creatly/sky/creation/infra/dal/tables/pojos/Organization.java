/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 组织主表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class Organization implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String code;
    private String name;
    private Long uid;
    private JSONB qualification;
    private String logoUrl;

    public Organization() {}

    public Organization(Organization value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.code = value.code;
        this.name = value.name;
        this.uid = value.uid;
        this.qualification = value.qualification;
        this.logoUrl = value.logoUrl;
    }

    public Organization(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        String code,
        String name,
        Long uid,
        @Nullable JSONB qualification,
        @Nullable String logoUrl
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.code = code;
        this.name = name;
        this.uid = uid;
        this.qualification = qualification;
        this.logoUrl = logoUrl;
    }

    /**
     * Getter for <code>creation.organization.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.organization.id</code>. 主键
     */
    public Organization setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.organization.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.organization.created_at</code>. 创建时间
     */
    public Organization setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.organization.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.organization.updated_at</code>. 更新时间
     */
    public Organization setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.organization.code</code>. 组织标识码
     */
    public String getCode() {
        return this.code;
    }

    /**
     * Setter for <code>creation.organization.code</code>. 组织标识码
     */
    public Organization setCode(String code) {
        this.code = code;
        return this;
    }

    /**
     * Getter for <code>creation.organization.name</code>. 组织名称
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.organization.name</code>. 组织名称
     */
    public Organization setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.organization.uid</code>. 企业法人
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.organization.uid</code>. 企业法人
     */
    public Organization setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.organization.qualification</code>. 组织资质
     */
    @Nullable
    public JSONB getQualification() {
        return this.qualification;
    }

    /**
     * Setter for <code>creation.organization.qualification</code>. 组织资质
     */
    public Organization setQualification(@Nullable JSONB qualification) {
        this.qualification = qualification;
        return this;
    }

    /**
     * Getter for <code>creation.organization.logo_url</code>. logo地址
     */
    @Nullable
    public String getLogoUrl() {
        return this.logoUrl;
    }

    /**
     * Setter for <code>creation.organization.logo_url</code>. logo地址
     */
    public Organization setLogoUrl(@Nullable String logoUrl) {
        this.logoUrl = logoUrl;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final Organization other = (Organization) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.code == null) {
            if (other.code != null)
                return false;
        }
        else if (!this.code.equals(other.code))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.qualification == null) {
            if (other.qualification != null)
                return false;
        }
        else if (!this.qualification.equals(other.qualification))
            return false;
        if (this.logoUrl == null) {
            if (other.logoUrl != null)
                return false;
        }
        else if (!this.logoUrl.equals(other.logoUrl))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.code == null) ? 0 : this.code.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.qualification == null) ? 0 : this.qualification.hashCode());
        result = prime * result + ((this.logoUrl == null) ? 0 : this.logoUrl.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Organization (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(code);
        sb.append(", ").append(name);
        sb.append(", ").append(uid);
        sb.append(", ").append(qualification);
        sb.append(", ").append(logoUrl);

        sb.append(")");
        return sb.toString();
    }
}
