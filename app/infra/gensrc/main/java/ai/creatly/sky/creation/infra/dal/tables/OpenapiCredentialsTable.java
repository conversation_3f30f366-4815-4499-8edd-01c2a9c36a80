/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.OpenapiCredentialsRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * openapi密钥表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class OpenapiCredentialsTable extends TableImpl<OpenapiCredentialsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>creation.creation_openapi_credentials</code>
     */
    public static final OpenapiCredentialsTable CREATION_OPENAPI_CREDENTIALS = new OpenapiCredentialsTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OpenapiCredentialsRecord> getRecordType() {
        return OpenapiCredentialsRecord.class;
    }

    /**
     * The column <code>creation.creation_openapi_credentials.id</code>. 主键
     */
    public final TableField<OpenapiCredentialsRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_openapi_credentials.created_at</code>.
     * 创建时间
     */
    public final TableField<OpenapiCredentialsRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_openapi_credentials.updated_at</code>.
     * 更新时间
     */
    public final TableField<OpenapiCredentialsRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_openapi_credentials.uid</code>. 用户id
     */
    public final TableField<OpenapiCredentialsRecord, String> UID = createField(DSL.name("uid"), SQLDataType.VARCHAR(64).nullable(false), this, "用户id");

    /**
     * The column <code>creation.creation_openapi_credentials.name</code>. 凭证
     */
    public final TableField<OpenapiCredentialsRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(255).nullable(false), this, "凭证");

    /**
     * The column <code>creation.creation_openapi_credentials.app_key</code>.
     * appKey
     */
    public final TableField<OpenapiCredentialsRecord, String> APP_KEY = createField(DSL.name("app_key"), SQLDataType.VARCHAR(255).nullable(false), this, "appKey");

    /**
     * The column <code>creation.creation_openapi_credentials.app_secret</code>.
     * appSecret
     */
    public final TableField<OpenapiCredentialsRecord, String> APP_SECRET = createField(DSL.name("app_secret"), SQLDataType.VARCHAR(255).nullable(false), this, "appSecret");

    /**
     * The column <code>creation.creation_openapi_credentials.status</code>. 状态
     * VALID/INVALID
     */
    public final TableField<OpenapiCredentialsRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(32).nullable(false), this, "状态 VALID/INVALID");

    /**
     * The column <code>creation.creation_openapi_credentials.expire_at</code>.
     * 到期时间
     */
    public final TableField<OpenapiCredentialsRecord, ZonedDateTime> EXPIRE_AT = createField(DSL.name("expire_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3), this, "到期时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    private OpenapiCredentialsTable(Name alias, Table<OpenapiCredentialsRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private OpenapiCredentialsTable(Name alias, Table<OpenapiCredentialsRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("openapi密钥表"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_openapi_credentials</code>
     * table reference
     */
    public OpenapiCredentialsTable(String alias) {
        this(DSL.name(alias), CREATION_OPENAPI_CREDENTIALS);
    }

    /**
     * Create an aliased <code>creation.creation_openapi_credentials</code>
     * table reference
     */
    public OpenapiCredentialsTable(Name alias) {
        this(alias, CREATION_OPENAPI_CREDENTIALS);
    }

    /**
     * Create a <code>creation.creation_openapi_credentials</code> table
     * reference
     */
    public OpenapiCredentialsTable() {
        this(DSL.name("creation_openapi_credentials"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<OpenapiCredentialsRecord> getPrimaryKey() {
        return Keys.CREATION_OPENAPI_CREDENTIALS_PKEY;
    }

    @Override
    public List<UniqueKey<OpenapiCredentialsRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CREATION_OPENAPI_CREDENTIALS_APP_KEY_KEY, Keys.CREATION_OPENAPI_CREDENTIALS_APP_KEY_KEY1, Keys.CREATION_OPENAPI_CREDENTIALS_UID_KEY, Keys.CREATION_OPENAPI_CREDENTIALS_UID_KEY1);
    }

    @Override
    public OpenapiCredentialsTable as(String alias) {
        return new OpenapiCredentialsTable(DSL.name(alias), this);
    }

    @Override
    public OpenapiCredentialsTable as(Name alias) {
        return new OpenapiCredentialsTable(alias, this);
    }

    @Override
    public OpenapiCredentialsTable as(Table<?> alias) {
        return new OpenapiCredentialsTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public OpenapiCredentialsTable rename(String name) {
        return new OpenapiCredentialsTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public OpenapiCredentialsTable rename(Name name) {
        return new OpenapiCredentialsTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public OpenapiCredentialsTable rename(Table<?> name) {
        return new OpenapiCredentialsTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OpenapiCredentialsTable where(Condition condition) {
        return new OpenapiCredentialsTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OpenapiCredentialsTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OpenapiCredentialsTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OpenapiCredentialsTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OpenapiCredentialsTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OpenapiCredentialsTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OpenapiCredentialsTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OpenapiCredentialsTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OpenapiCredentialsTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OpenapiCredentialsTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
