/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.CreditHistoryTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationCreditHistory;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 余额使用记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreditHistoryRecord extends UpdatableRecordImpl<CreditHistoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_credit_history.id</code>. 主键
     */
    public CreditHistoryRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_credit_history.created_at</code>. 创建时间
     */
    public CreditHistoryRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_credit_history.updated_at</code>. 更新时间
     */
    public CreditHistoryRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_credit_history.uid</code>. 用户id
     */
    public CreditHistoryRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.uid</code>. 用户id
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_credit_history.user_credit_id</code>.
     * 用户余额卡表id
     */
    public CreditHistoryRecord setUserCreditId(Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.user_credit_id</code>.
     * 用户余额卡表id
     */
    public Long getUserCreditId() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.creation_credit_history.biz_no</code>.
     * 任务类存放任务id，消息存放消息id
     */
    public CreditHistoryRecord setBizNo(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.biz_no</code>.
     * 任务类存放任务id，消息存放消息id
     */
    public String getBizNo() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_credit_history.biz_type</code>.
     * 业务场景：马良/活图/充值
     */
    public CreditHistoryRecord setBizType(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.biz_type</code>.
     * 业务场景：马良/活图/充值
     */
    public String getBizType() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_credit_history.amount</code>. credit数量
     */
    public CreditHistoryRecord setAmount(Integer value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.amount</code>. credit数量
     */
    public Integer getAmount() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>creation.creation_credit_history.type</code>. 收入，支出
     */
    public CreditHistoryRecord setType(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.type</code>. 收入，支出
     */
    public String getType() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_credit_history.balance</code>. 信用余额
     */
    public CreditHistoryRecord setBalance(Long value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.balance</code>. 信用余额
     */
    public Long getBalance() {
        return (Long) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CreditHistoryRecord
     */
    public CreditHistoryRecord() {
        super(CreditHistoryTable.CREATION_CREDIT_HISTORY);
    }

    /**
     * Create a detached, initialised CreditHistoryRecord
     */
    public CreditHistoryRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, Long userCreditId, String bizNo, String bizType, Integer amount, String type, Long balance) {
        super(CreditHistoryTable.CREATION_CREDIT_HISTORY);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setUserCreditId(userCreditId);
        setBizNo(bizNo);
        setBizType(bizType);
        setAmount(amount);
        setType(type);
        setBalance(balance);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised CreditHistoryRecord
     */
    public CreditHistoryRecord(CreationCreditHistory value) {
        super(CreditHistoryTable.CREATION_CREDIT_HISTORY);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setUserCreditId(value.getUserCreditId());
            setBizNo(value.getBizNo());
            setBizType(value.getBizType());
            setAmount(value.getAmount());
            setType(value.getType());
            setBalance(value.getBalance());
            resetChangedOnNotNull();
        }
    }
}
