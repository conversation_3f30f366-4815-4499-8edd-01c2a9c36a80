/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 计费规则
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationFeatureCostRule implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long cost;
    private String unitsName;
    private Integer unitsAmount;
    private String taskType;
    private String taskBizType;
    private String status;

    public CreationFeatureCostRule() {}

    public CreationFeatureCostRule(CreationFeatureCostRule value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.cost = value.cost;
        this.unitsName = value.unitsName;
        this.unitsAmount = value.unitsAmount;
        this.taskType = value.taskType;
        this.taskBizType = value.taskBizType;
        this.status = value.status;
    }

    public CreationFeatureCostRule(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long cost,
        String unitsName,
        Integer unitsAmount,
        String taskType,
        String taskBizType,
        String status
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.cost = cost;
        this.unitsName = unitsName;
        this.unitsAmount = unitsAmount;
        this.taskType = taskType;
        this.taskBizType = taskBizType;
        this.status = status;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.id</code>. 主键
     */
    public CreationFeatureCostRule setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.created_at</code>.
     * 创建时间
     */
    public CreationFeatureCostRule setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.updated_at</code>.
     * 更新时间
     */
    public CreationFeatureCostRule setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.cost</code>. 消耗值
     */
    public Long getCost() {
        return this.cost;
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.cost</code>. 消耗值
     */
    public CreationFeatureCostRule setCost(Long cost) {
        this.cost = cost;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.units_name</code>.
     * 度量单位
     */
    public String getUnitsName() {
        return this.unitsName;
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.units_name</code>.
     * 度量单位
     */
    public CreationFeatureCostRule setUnitsName(String unitsName) {
        this.unitsName = unitsName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.units_amount</code>.
     * 度量数量
     */
    public Integer getUnitsAmount() {
        return this.unitsAmount;
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.units_amount</code>.
     * 度量数量
     */
    public CreationFeatureCostRule setUnitsAmount(Integer unitsAmount) {
        this.unitsAmount = unitsAmount;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.task_type</code>.
     * 任务标识符
     */
    public String getTaskType() {
        return this.taskType;
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.task_type</code>.
     * 任务标识符
     */
    public CreationFeatureCostRule setTaskType(String taskType) {
        this.taskType = taskType;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_feature_cost_rule.task_biz_type</code>. 任务类型
     */
    public String getTaskBizType() {
        return this.taskBizType;
    }

    /**
     * Setter for
     * <code>creation.creation_feature_cost_rule.task_biz_type</code>. 任务类型
     */
    public CreationFeatureCostRule setTaskBizType(String taskBizType) {
        this.taskBizType = taskBizType;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.status</code>. 状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.status</code>. 状态
     */
    public CreationFeatureCostRule setStatus(String status) {
        this.status = status;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationFeatureCostRule other = (CreationFeatureCostRule) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.cost == null) {
            if (other.cost != null)
                return false;
        }
        else if (!this.cost.equals(other.cost))
            return false;
        if (this.unitsName == null) {
            if (other.unitsName != null)
                return false;
        }
        else if (!this.unitsName.equals(other.unitsName))
            return false;
        if (this.unitsAmount == null) {
            if (other.unitsAmount != null)
                return false;
        }
        else if (!this.unitsAmount.equals(other.unitsAmount))
            return false;
        if (this.taskType == null) {
            if (other.taskType != null)
                return false;
        }
        else if (!this.taskType.equals(other.taskType))
            return false;
        if (this.taskBizType == null) {
            if (other.taskBizType != null)
                return false;
        }
        else if (!this.taskBizType.equals(other.taskBizType))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.cost == null) ? 0 : this.cost.hashCode());
        result = prime * result + ((this.unitsName == null) ? 0 : this.unitsName.hashCode());
        result = prime * result + ((this.unitsAmount == null) ? 0 : this.unitsAmount.hashCode());
        result = prime * result + ((this.taskType == null) ? 0 : this.taskType.hashCode());
        result = prime * result + ((this.taskBizType == null) ? 0 : this.taskBizType.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationFeatureCostRule (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(cost);
        sb.append(", ").append(unitsName);
        sb.append(", ").append(unitsAmount);
        sb.append(", ").append(taskType);
        sb.append(", ").append(taskBizType);
        sb.append(", ").append(status);

        sb.append(")");
        return sb.toString();
    }
}
