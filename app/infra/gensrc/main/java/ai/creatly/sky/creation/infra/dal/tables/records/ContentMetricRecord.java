/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.ContentMetricTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationContentMetric;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 内容指标明细
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class ContentMetricRecord extends UpdatableRecordImpl<ContentMetricRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_content_metric.id</code>. 主键
     */
    public ContentMetricRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content_metric.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_content_metric.created_at</code>. 创建时间
     */
    public ContentMetricRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content_metric.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_content_metric.updated_at</code>. 更新时间
     */
    public ContentMetricRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content_metric.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_content_metric.content_id</code>. 内容id
     */
    public ContentMetricRecord setContentId(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content_metric.content_id</code>. 内容id
     */
    public Long getContentId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_content_metric.metric_code</code>.
     * 指标:每小时热点
     */
    public ContentMetricRecord setMetricCode(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content_metric.metric_code</code>.
     * 指标:每小时热点
     */
    public String getMetricCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_content_metric.metric_name</code>.
     * 指标:每小时热点
     */
    public ContentMetricRecord setMetricName(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content_metric.metric_name</code>.
     * 指标:每小时热点
     */
    public String getMetricName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_content_metric.metric_value</code>.
     * 指标值：30000
     */
    public ContentMetricRecord setMetricValue(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content_metric.metric_value</code>.
     * 指标值：30000
     */
    public String getMetricValue() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_content_metric.data_type</code>.
     * 数据类型：number,string
     */
    public ContentMetricRecord setDataType(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content_metric.data_type</code>.
     * 数据类型：number,string
     */
    public String getDataType() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ContentMetricRecord
     */
    public ContentMetricRecord() {
        super(ContentMetricTable.CREATION_CONTENT_METRIC);
    }

    /**
     * Create a detached, initialised ContentMetricRecord
     */
    public ContentMetricRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long contentId, String metricCode, String metricName, String metricValue, String dataType) {
        super(ContentMetricTable.CREATION_CONTENT_METRIC);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setContentId(contentId);
        setMetricCode(metricCode);
        setMetricName(metricName);
        setMetricValue(metricValue);
        setDataType(dataType);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised ContentMetricRecord
     */
    public ContentMetricRecord(CreationContentMetric value) {
        super(ContentMetricTable.CREATION_CONTENT_METRIC);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setContentId(value.getContentId());
            setMetricCode(value.getMetricCode());
            setMetricName(value.getMetricName());
            setMetricValue(value.getMetricValue());
            setDataType(value.getDataType());
            resetChangedOnNotNull();
        }
    }
}
