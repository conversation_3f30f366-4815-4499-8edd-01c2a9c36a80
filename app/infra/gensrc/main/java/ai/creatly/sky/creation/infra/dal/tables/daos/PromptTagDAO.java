/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.PromptTagTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationPromptTag;
import ai.creatly.sky.creation.infra.dal.tables.records.PromptTagRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 提示词标签
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class PromptTagDAO extends DAOImpl<PromptTagRecord, CreationPromptTag, Long> {

    /**
     * Create a new PromptTagDAO without any configuration
     */
    public PromptTagDAO() {
        super(PromptTagTable.CREATION_PROMPT_TAG, CreationPromptTag.class);
    }

    /**
     * Create a new PromptTagDAO with an attached configuration
     */
    @Autowired
    public PromptTagDAO(Configuration configuration) {
        super(PromptTagTable.CREATION_PROMPT_TAG, CreationPromptTag.class, configuration);
    }

    @Override
    public Long getId(CreationPromptTag object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptTag> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(PromptTagTable.CREATION_PROMPT_TAG.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationPromptTag> fetchById(Long... values) {
        return fetch(PromptTagTable.CREATION_PROMPT_TAG.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationPromptTag fetchOneById(Long value) {
        return fetchOne(PromptTagTable.CREATION_PROMPT_TAG.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationPromptTag> fetchOptionalById(Long value) {
        return fetchOptional(PromptTagTable.CREATION_PROMPT_TAG.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptTag> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(PromptTagTable.CREATION_PROMPT_TAG.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationPromptTag> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(PromptTagTable.CREATION_PROMPT_TAG.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptTag> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(PromptTagTable.CREATION_PROMPT_TAG.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationPromptTag> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(PromptTagTable.CREATION_PROMPT_TAG.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptTag> fetchRangeOfCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(PromptTagTable.CREATION_PROMPT_TAG.CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>code IN (values)</code>
     */
    public List<CreationPromptTag> fetchByCode(String... values) {
        return fetch(PromptTagTable.CREATION_PROMPT_TAG.CODE, values);
    }

    /**
     * Fetch a unique record that has <code>code = value</code>
     */
    @Nullable
    public CreationPromptTag fetchOneByCode(String value) {
        return fetchOne(PromptTagTable.CREATION_PROMPT_TAG.CODE, value);
    }

    /**
     * Fetch a unique record that has <code>code = value</code>
     */
    public Optional<CreationPromptTag> fetchOptionalByCode(String value) {
        return fetchOptional(PromptTagTable.CREATION_PROMPT_TAG.CODE, value);
    }

    /**
     * Fetch records that have <code>color BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptTag> fetchRangeOfColor(String lowerInclusive, String upperInclusive) {
        return fetchRange(PromptTagTable.CREATION_PROMPT_TAG.COLOR, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>color IN (values)</code>
     */
    public List<CreationPromptTag> fetchByColor(String... values) {
        return fetch(PromptTagTable.CREATION_PROMPT_TAG.COLOR, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptTag> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(PromptTagTable.CREATION_PROMPT_TAG.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<CreationPromptTag> fetchByName(String... values) {
        return fetch(PromptTagTable.CREATION_PROMPT_TAG.NAME, values);
    }

    /**
     * Fetch records that have <code>en_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptTag> fetchRangeOfEnName(String lowerInclusive, String upperInclusive) {
        return fetchRange(PromptTagTable.CREATION_PROMPT_TAG.EN_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>en_name IN (values)</code>
     */
    public List<CreationPromptTag> fetchByEnName(String... values) {
        return fetch(PromptTagTable.CREATION_PROMPT_TAG.EN_NAME, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptTag> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(PromptTagTable.CREATION_PROMPT_TAG.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CreationPromptTag> fetchByStatus(String... values) {
        return fetch(PromptTagTable.CREATION_PROMPT_TAG.STATUS, values);
    }
}
