/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 用户创作空间模版
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationUserWorkspace implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private JSONB creator;
    private String name;
    private String type;
    private Long templateId;
    private JSONB workspaceConfig;
    private String status;
    private Long coverFileId;
    private String coverOssUrl;

    public CreationUserWorkspace() {}

    public CreationUserWorkspace(CreationUserWorkspace value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.creator = value.creator;
        this.name = value.name;
        this.type = value.type;
        this.templateId = value.templateId;
        this.workspaceConfig = value.workspaceConfig;
        this.status = value.status;
        this.coverFileId = value.coverFileId;
        this.coverOssUrl = value.coverOssUrl;
    }

    public CreationUserWorkspace(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        JSONB creator,
        String name,
        String type,
        Long templateId,
        JSONB workspaceConfig,
        String status,
        @Nullable Long coverFileId,
        @Nullable String coverOssUrl
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.creator = creator;
        this.name = name;
        this.type = type;
        this.templateId = templateId;
        this.workspaceConfig = workspaceConfig;
        this.status = status;
        this.coverFileId = coverFileId;
        this.coverOssUrl = coverOssUrl;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_user_workspace.id</code>. 主键
     */
    public CreationUserWorkspace setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_user_workspace.created_at</code>. 创建时间
     */
    public CreationUserWorkspace setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_user_workspace.updated_at</code>. 更新时间
     */
    public CreationUserWorkspace setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.uid</code>. 用户id
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_user_workspace.uid</code>. 用户id
     */
    public CreationUserWorkspace setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return this.creator;
    }

    /**
     * Setter for <code>creation.creation_user_workspace.creator</code>. 创建者
     */
    public CreationUserWorkspace setCreator(JSONB creator) {
        this.creator = creator;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.name</code>. 工作空间名
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.creation_user_workspace.name</code>. 工作空间名
     */
    public CreationUserWorkspace setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.type</code>. 工作空间类型
     */
    public String getType() {
        return this.type;
    }

    /**
     * Setter for <code>creation.creation_user_workspace.type</code>. 工作空间类型
     */
    public CreationUserWorkspace setType(String type) {
        this.type = type;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.template_id</code>.
     * 创建模版id
     */
    public Long getTemplateId() {
        return this.templateId;
    }

    /**
     * Setter for <code>creation.creation_user_workspace.template_id</code>.
     * 创建模版id
     */
    public CreationUserWorkspace setTemplateId(Long templateId) {
        this.templateId = templateId;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_workspace.workspace_config</code>. 工作空间内部配置
     */
    public JSONB getWorkspaceConfig() {
        return this.workspaceConfig;
    }

    /**
     * Setter for
     * <code>creation.creation_user_workspace.workspace_config</code>. 工作空间内部配置
     */
    public CreationUserWorkspace setWorkspaceConfig(JSONB workspaceConfig) {
        this.workspaceConfig = workspaceConfig;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.status</code>. 工作空间状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_user_workspace.status</code>. 工作空间状态
     */
    public CreationUserWorkspace setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.cover_file_id</code>.
     */
    @Nullable
    public Long getCoverFileId() {
        return this.coverFileId;
    }

    /**
     * Setter for <code>creation.creation_user_workspace.cover_file_id</code>.
     */
    public CreationUserWorkspace setCoverFileId(@Nullable Long coverFileId) {
        this.coverFileId = coverFileId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.cover_oss_url</code>.
     */
    @Nullable
    public String getCoverOssUrl() {
        return this.coverOssUrl;
    }

    /**
     * Setter for <code>creation.creation_user_workspace.cover_oss_url</code>.
     */
    public CreationUserWorkspace setCoverOssUrl(@Nullable String coverOssUrl) {
        this.coverOssUrl = coverOssUrl;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationUserWorkspace other = (CreationUserWorkspace) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.creator == null) {
            if (other.creator != null)
                return false;
        }
        else if (!this.creator.equals(other.creator))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.type == null) {
            if (other.type != null)
                return false;
        }
        else if (!this.type.equals(other.type))
            return false;
        if (this.templateId == null) {
            if (other.templateId != null)
                return false;
        }
        else if (!this.templateId.equals(other.templateId))
            return false;
        if (this.workspaceConfig == null) {
            if (other.workspaceConfig != null)
                return false;
        }
        else if (!this.workspaceConfig.equals(other.workspaceConfig))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.coverFileId == null) {
            if (other.coverFileId != null)
                return false;
        }
        else if (!this.coverFileId.equals(other.coverFileId))
            return false;
        if (this.coverOssUrl == null) {
            if (other.coverOssUrl != null)
                return false;
        }
        else if (!this.coverOssUrl.equals(other.coverOssUrl))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.creator == null) ? 0 : this.creator.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.type == null) ? 0 : this.type.hashCode());
        result = prime * result + ((this.templateId == null) ? 0 : this.templateId.hashCode());
        result = prime * result + ((this.workspaceConfig == null) ? 0 : this.workspaceConfig.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.coverFileId == null) ? 0 : this.coverFileId.hashCode());
        result = prime * result + ((this.coverOssUrl == null) ? 0 : this.coverOssUrl.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationUserWorkspace (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(creator);
        sb.append(", ").append(name);
        sb.append(", ").append(type);
        sb.append(", ").append(templateId);
        sb.append(", ").append(workspaceConfig);
        sb.append(", ").append(status);
        sb.append(", ").append(coverFileId);
        sb.append(", ").append(coverOssUrl);

        sb.append(")");
        return sb.toString();
    }
}
