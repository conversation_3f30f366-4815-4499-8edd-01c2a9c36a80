/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.CourseLearnRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseLearnRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseLearnRecordRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 课程学习记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class CourseLearnRecordDAO extends DAOImpl<CourseLearnRecordRecord, CourseLearnRecord, Long> {

    /**
     * Create a new CourseLearnRecordDAO without any configuration
     */
    public CourseLearnRecordDAO() {
        super(CourseLearnRecordTable.COURSE_LEARN_RECORD, CourseLearnRecord.class);
    }

    /**
     * Create a new CourseLearnRecordDAO with an attached configuration
     */
    @Autowired
    public CourseLearnRecordDAO(Configuration configuration) {
        super(CourseLearnRecordTable.COURSE_LEARN_RECORD, CourseLearnRecord.class, configuration);
    }

    @Override
    public Long getId(CourseLearnRecord object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseLearnRecord> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseLearnRecordTable.COURSE_LEARN_RECORD.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CourseLearnRecord> fetchById(Long... values) {
        return fetch(CourseLearnRecordTable.COURSE_LEARN_RECORD.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CourseLearnRecord fetchOneById(Long value) {
        return fetchOne(CourseLearnRecordTable.COURSE_LEARN_RECORD.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CourseLearnRecord> fetchOptionalById(Long value) {
        return fetchOptional(CourseLearnRecordTable.COURSE_LEARN_RECORD.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseLearnRecord> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(CourseLearnRecordTable.COURSE_LEARN_RECORD.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CourseLearnRecord> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(CourseLearnRecordTable.COURSE_LEARN_RECORD.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseLearnRecord> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(CourseLearnRecordTable.COURSE_LEARN_RECORD.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CourseLearnRecord> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(CourseLearnRecordTable.COURSE_LEARN_RECORD.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>course_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseLearnRecord> fetchRangeOfCourseId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseLearnRecordTable.COURSE_LEARN_RECORD.COURSE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>course_id IN (values)</code>
     */
    public List<CourseLearnRecord> fetchByCourseId(Long... values) {
        return fetch(CourseLearnRecordTable.COURSE_LEARN_RECORD.COURSE_ID, values);
    }

    /**
     * Fetch records that have <code>content_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseLearnRecord> fetchRangeOfContentId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseLearnRecordTable.COURSE_LEARN_RECORD.CONTENT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>content_id IN (values)</code>
     */
    public List<CourseLearnRecord> fetchByContentId(Long... values) {
        return fetch(CourseLearnRecordTable.COURSE_LEARN_RECORD.CONTENT_ID, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseLearnRecord> fetchRangeOfStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(CourseLearnRecordTable.COURSE_LEARN_RECORD.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CourseLearnRecord> fetchByStatus(Integer... values) {
        return fetch(CourseLearnRecordTable.COURSE_LEARN_RECORD.STATUS, values);
    }

    /**
     * Fetch records that have <code>owner_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseLearnRecord> fetchRangeOfOwnerId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseLearnRecordTable.COURSE_LEARN_RECORD.OWNER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>owner_id IN (values)</code>
     */
    public List<CourseLearnRecord> fetchByOwnerId(Long... values) {
        return fetch(CourseLearnRecordTable.COURSE_LEARN_RECORD.OWNER_ID, values);
    }

    /**
     * Fetch records that have <code>owner_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseLearnRecord> fetchRangeOfOwnerName(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseLearnRecordTable.COURSE_LEARN_RECORD.OWNER_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>owner_name IN (values)</code>
     */
    public List<CourseLearnRecord> fetchByOwnerName(String... values) {
        return fetch(CourseLearnRecordTable.COURSE_LEARN_RECORD.OWNER_NAME, values);
    }

    /**
     * Fetch records that have <code>init_seconds BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseLearnRecord> fetchRangeOfInitSeconds(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(CourseLearnRecordTable.COURSE_LEARN_RECORD.INIT_SECONDS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>init_seconds IN (values)</code>
     */
    public List<CourseLearnRecord> fetchByInitSeconds(Integer... values) {
        return fetch(CourseLearnRecordTable.COURSE_LEARN_RECORD.INIT_SECONDS, values);
    }

    /**
     * Fetch records that have <code>org_code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseLearnRecord> fetchRangeOfOrgCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseLearnRecordTable.COURSE_LEARN_RECORD.ORG_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_code IN (values)</code>
     */
    public List<CourseLearnRecord> fetchByOrgCode(String... values) {
        return fetch(CourseLearnRecordTable.COURSE_LEARN_RECORD.ORG_CODE, values);
    }
}
