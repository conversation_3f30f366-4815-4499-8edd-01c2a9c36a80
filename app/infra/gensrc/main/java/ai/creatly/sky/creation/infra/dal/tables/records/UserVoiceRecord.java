/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.UserVoiceTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserVoice;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 声音演员
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserVoiceRecord extends UpdatableRecordImpl<UserVoiceRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_user_voice.id</code>. 主键
     */
    public UserVoiceRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_user_voice.created_at</code>. 创建时间
     */
    public UserVoiceRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_user_voice.updated_at</code>. 更新时间
     */
    public UserVoiceRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_user_voice.uid</code>. 用户ID
     */
    public UserVoiceRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.uid</code>. 用户ID
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_user_voice.code</code>. 声音编号
     */
    public UserVoiceRecord setCode(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.code</code>. 声音编号
     */
    public String getCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_user_voice.provider_type</code>. 声音提供方
     */
    public UserVoiceRecord setProviderType(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.provider_type</code>. 声音提供方
     */
    public String getProviderType() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_user_voice.category</code>. 分类
     */
    public UserVoiceRecord setCategory(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.category</code>. 分类
     */
    public String getCategory() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_user_voice.status</code>. 状态
     */
    public UserVoiceRecord setStatus(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.status</code>. 状态
     */
    public String getStatus() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_user_voice.en_name</code>. 英文名称
     */
    public UserVoiceRecord setEnName(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.en_name</code>. 英文名称
     */
    public String getEnName() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_user_voice.cn_name</code>. 中文名称
     */
    public UserVoiceRecord setCnName(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.cn_name</code>. 中文名称
     */
    public String getCnName() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_user_voice.gender</code>. 性别
     */
    public UserVoiceRecord setGender(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.gender</code>. 性别
     */
    public String getGender() {
        return (String) get(10);
    }

    /**
     * Setter for <code>creation.creation_user_voice.avatar</code>. 头像
     */
    public UserVoiceRecord setAvatar(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.avatar</code>. 头像
     */
    public String getAvatar() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_user_voice.ordinal</code>. 排序字段
     */
    public UserVoiceRecord setOrdinal(@Nullable Integer value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.ordinal</code>. 排序字段
     */
    @Nullable
    public Integer getOrdinal() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>creation.creation_user_voice.age_level</code>. 声音年龄阶层
     */
    public UserVoiceRecord setAgeLevel(@Nullable String value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.age_level</code>. 声音年龄阶层
     */
    @Nullable
    public String getAgeLevel() {
        return (String) get(13);
    }

    /**
     * Setter for <code>creation.creation_user_voice.labels</code>. 声音标签列表
     */
    public UserVoiceRecord setLabels(@Nullable JSONB value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.labels</code>. 声音标签列表
     */
    @Nullable
    public JSONB getLabels() {
        return (JSONB) get(14);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserVoiceRecord
     */
    public UserVoiceRecord() {
        super(UserVoiceTable.CREATION_USER_VOICE);
    }

    /**
     * Create a detached, initialised UserVoiceRecord
     */
    public UserVoiceRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String code, String providerType, String category, String status, String enName, String cnName, String gender, String avatar, @Nullable Integer ordinal, @Nullable String ageLevel, @Nullable JSONB labels) {
        super(UserVoiceTable.CREATION_USER_VOICE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setCode(code);
        setProviderType(providerType);
        setCategory(category);
        setStatus(status);
        setEnName(enName);
        setCnName(cnName);
        setGender(gender);
        setAvatar(avatar);
        setOrdinal(ordinal);
        setAgeLevel(ageLevel);
        setLabels(labels);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserVoiceRecord
     */
    public UserVoiceRecord(CreationUserVoice value) {
        super(UserVoiceTable.CREATION_USER_VOICE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setCode(value.getCode());
            setProviderType(value.getProviderType());
            setCategory(value.getCategory());
            setStatus(value.getStatus());
            setEnName(value.getEnName());
            setCnName(value.getCnName());
            setGender(value.getGender());
            setAvatar(value.getAvatar());
            setOrdinal(value.getOrdinal());
            setAgeLevel(value.getAgeLevel());
            setLabels(value.getLabels());
            resetChangedOnNotNull();
        }
    }
}
