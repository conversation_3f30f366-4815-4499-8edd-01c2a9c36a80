/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 课程购买记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseOrderRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long courseId;
    private Long orderPrice;
    private String orderStatus;
    private Long ownerId;
    private String ownerName;
    private ZonedDateTime paidTime;
    private String paidChannel;
    private Long orderId;
    private String codeUrl;
    private ZonedDateTime expireAt;
    private String orgCode;

    public CourseOrderRecord() {}

    public CourseOrderRecord(CourseOrderRecord value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.courseId = value.courseId;
        this.orderPrice = value.orderPrice;
        this.orderStatus = value.orderStatus;
        this.ownerId = value.ownerId;
        this.ownerName = value.ownerName;
        this.paidTime = value.paidTime;
        this.paidChannel = value.paidChannel;
        this.orderId = value.orderId;
        this.codeUrl = value.codeUrl;
        this.expireAt = value.expireAt;
        this.orgCode = value.orgCode;
    }

    public CourseOrderRecord(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        @Nullable Long courseId,
        @Nullable Long orderPrice,
        @Nullable String orderStatus,
        @Nullable Long ownerId,
        @Nullable String ownerName,
        @Nullable ZonedDateTime paidTime,
        @Nullable String paidChannel,
        @Nullable Long orderId,
        @Nullable String codeUrl,
        @Nullable ZonedDateTime expireAt,
        @Nullable String orgCode
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.courseId = courseId;
        this.orderPrice = orderPrice;
        this.orderStatus = orderStatus;
        this.ownerId = ownerId;
        this.ownerName = ownerName;
        this.paidTime = paidTime;
        this.paidChannel = paidChannel;
        this.orderId = orderId;
        this.codeUrl = codeUrl;
        this.expireAt = expireAt;
        this.orgCode = orgCode;
    }

    /**
     * Getter for <code>creation.course_order_record.id</code>. 课程购买记录id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.course_order_record.id</code>. 课程购买记录id
     */
    public CourseOrderRecord setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.course_order_record.created_at</code>. 创建时间
     */
    public CourseOrderRecord setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.course_order_record.updated_at</code>. 更新时间
     */
    public CourseOrderRecord setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.course_id</code>. 课程ID
     */
    @Nullable
    public Long getCourseId() {
        return this.courseId;
    }

    /**
     * Setter for <code>creation.course_order_record.course_id</code>. 课程ID
     */
    public CourseOrderRecord setCourseId(@Nullable Long courseId) {
        this.courseId = courseId;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.order_price</code>. 支付价格
     */
    @Nullable
    public Long getOrderPrice() {
        return this.orderPrice;
    }

    /**
     * Setter for <code>creation.course_order_record.order_price</code>. 支付价格
     */
    public CourseOrderRecord setOrderPrice(@Nullable Long orderPrice) {
        this.orderPrice = orderPrice;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.order_status</code>. 支付状态
     */
    @Nullable
    public String getOrderStatus() {
        return this.orderStatus;
    }

    /**
     * Setter for <code>creation.course_order_record.order_status</code>. 支付状态
     */
    public CourseOrderRecord setOrderStatus(@Nullable String orderStatus) {
        this.orderStatus = orderStatus;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    @Nullable
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * Setter for <code>creation.course_order_record.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public CourseOrderRecord setOwnerId(@Nullable Long ownerId) {
        this.ownerId = ownerId;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.owner_name</code>. 任务所有者名称
     */
    @Nullable
    public String getOwnerName() {
        return this.ownerName;
    }

    /**
     * Setter for <code>creation.course_order_record.owner_name</code>. 任务所有者名称
     */
    public CourseOrderRecord setOwnerName(@Nullable String ownerName) {
        this.ownerName = ownerName;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.paid_time</code>. 支付时间
     */
    @Nullable
    public ZonedDateTime getPaidTime() {
        return this.paidTime;
    }

    /**
     * Setter for <code>creation.course_order_record.paid_time</code>. 支付时间
     */
    public CourseOrderRecord setPaidTime(@Nullable ZonedDateTime paidTime) {
        this.paidTime = paidTime;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.paid_channel</code>. 支付渠道
     */
    @Nullable
    public String getPaidChannel() {
        return this.paidChannel;
    }

    /**
     * Setter for <code>creation.course_order_record.paid_channel</code>. 支付渠道
     */
    public CourseOrderRecord setPaidChannel(@Nullable String paidChannel) {
        this.paidChannel = paidChannel;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.order_id</code>. 订单id
     */
    @Nullable
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * Setter for <code>creation.course_order_record.order_id</code>. 订单id
     */
    public CourseOrderRecord setOrderId(@Nullable Long orderId) {
        this.orderId = orderId;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.code_url</code>. 支付二维码
     */
    @Nullable
    public String getCodeUrl() {
        return this.codeUrl;
    }

    /**
     * Setter for <code>creation.course_order_record.code_url</code>. 支付二维码
     */
    public CourseOrderRecord setCodeUrl(@Nullable String codeUrl) {
        this.codeUrl = codeUrl;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.expire_at</code>. 超时时间
     */
    @Nullable
    public ZonedDateTime getExpireAt() {
        return this.expireAt;
    }

    /**
     * Setter for <code>creation.course_order_record.expire_at</code>. 超时时间
     */
    public CourseOrderRecord setExpireAt(@Nullable ZonedDateTime expireAt) {
        this.expireAt = expireAt;
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.org_code</code>. 组织代码
     */
    @Nullable
    public String getOrgCode() {
        return this.orgCode;
    }

    /**
     * Setter for <code>creation.course_order_record.org_code</code>. 组织代码
     */
    public CourseOrderRecord setOrgCode(@Nullable String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CourseOrderRecord other = (CourseOrderRecord) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.courseId == null) {
            if (other.courseId != null)
                return false;
        }
        else if (!this.courseId.equals(other.courseId))
            return false;
        if (this.orderPrice == null) {
            if (other.orderPrice != null)
                return false;
        }
        else if (!this.orderPrice.equals(other.orderPrice))
            return false;
        if (this.orderStatus == null) {
            if (other.orderStatus != null)
                return false;
        }
        else if (!this.orderStatus.equals(other.orderStatus))
            return false;
        if (this.ownerId == null) {
            if (other.ownerId != null)
                return false;
        }
        else if (!this.ownerId.equals(other.ownerId))
            return false;
        if (this.ownerName == null) {
            if (other.ownerName != null)
                return false;
        }
        else if (!this.ownerName.equals(other.ownerName))
            return false;
        if (this.paidTime == null) {
            if (other.paidTime != null)
                return false;
        }
        else if (!this.paidTime.equals(other.paidTime))
            return false;
        if (this.paidChannel == null) {
            if (other.paidChannel != null)
                return false;
        }
        else if (!this.paidChannel.equals(other.paidChannel))
            return false;
        if (this.orderId == null) {
            if (other.orderId != null)
                return false;
        }
        else if (!this.orderId.equals(other.orderId))
            return false;
        if (this.codeUrl == null) {
            if (other.codeUrl != null)
                return false;
        }
        else if (!this.codeUrl.equals(other.codeUrl))
            return false;
        if (this.expireAt == null) {
            if (other.expireAt != null)
                return false;
        }
        else if (!this.expireAt.equals(other.expireAt))
            return false;
        if (this.orgCode == null) {
            if (other.orgCode != null)
                return false;
        }
        else if (!this.orgCode.equals(other.orgCode))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.courseId == null) ? 0 : this.courseId.hashCode());
        result = prime * result + ((this.orderPrice == null) ? 0 : this.orderPrice.hashCode());
        result = prime * result + ((this.orderStatus == null) ? 0 : this.orderStatus.hashCode());
        result = prime * result + ((this.ownerId == null) ? 0 : this.ownerId.hashCode());
        result = prime * result + ((this.ownerName == null) ? 0 : this.ownerName.hashCode());
        result = prime * result + ((this.paidTime == null) ? 0 : this.paidTime.hashCode());
        result = prime * result + ((this.paidChannel == null) ? 0 : this.paidChannel.hashCode());
        result = prime * result + ((this.orderId == null) ? 0 : this.orderId.hashCode());
        result = prime * result + ((this.codeUrl == null) ? 0 : this.codeUrl.hashCode());
        result = prime * result + ((this.expireAt == null) ? 0 : this.expireAt.hashCode());
        result = prime * result + ((this.orgCode == null) ? 0 : this.orgCode.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseOrderRecord (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(courseId);
        sb.append(", ").append(orderPrice);
        sb.append(", ").append(orderStatus);
        sb.append(", ").append(ownerId);
        sb.append(", ").append(ownerName);
        sb.append(", ").append(paidTime);
        sb.append(", ").append(paidChannel);
        sb.append(", ").append(orderId);
        sb.append(", ").append(codeUrl);
        sb.append(", ").append(expireAt);
        sb.append(", ").append(orgCode);

        sb.append(")");
        return sb.toString();
    }
}
