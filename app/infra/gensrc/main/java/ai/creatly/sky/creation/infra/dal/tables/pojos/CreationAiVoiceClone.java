/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 用户声音克隆
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationAiVoiceClone implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String code;
    private String status;
    private String cnName;
    private String avatar;
    private String audioUrl;
    private String language;
    private String bizType;
    private String dialect;

    public CreationAiVoiceClone() {}

    public CreationAiVoiceClone(CreationAiVoiceClone value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.code = value.code;
        this.status = value.status;
        this.cnName = value.cnName;
        this.avatar = value.avatar;
        this.audioUrl = value.audioUrl;
        this.language = value.language;
        this.bizType = value.bizType;
        this.dialect = value.dialect;
    }

    public CreationAiVoiceClone(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        @Nullable Long uid,
        @Nullable String code,
        @Nullable String status,
        @Nullable String cnName,
        @Nullable String avatar,
        @Nullable String audioUrl,
        @Nullable String language,
        @Nullable String bizType,
        @Nullable String dialect
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.code = code;
        this.status = status;
        this.cnName = cnName;
        this.avatar = avatar;
        this.audioUrl = audioUrl;
        this.language = language;
        this.bizType = bizType;
        this.dialect = dialect;
    }

    /**
     * Getter for <code>creation.creation_ai_voice_clone.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_ai_voice_clone.id</code>. 主键
     */
    public CreationAiVoiceClone setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_voice_clone.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_ai_voice_clone.created_at</code>. 创建时间
     */
    public CreationAiVoiceClone setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_voice_clone.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_ai_voice_clone.updated_at</code>. 更新时间
     */
    public CreationAiVoiceClone setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_voice_clone.uid</code>. 用户ID
     */
    @Nullable
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_ai_voice_clone.uid</code>. 用户ID
     */
    public CreationAiVoiceClone setUid(@Nullable Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_voice_clone.code</code>. 声音编号
     */
    @Nullable
    public String getCode() {
        return this.code;
    }

    /**
     * Setter for <code>creation.creation_ai_voice_clone.code</code>. 声音编号
     */
    public CreationAiVoiceClone setCode(@Nullable String code) {
        this.code = code;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_voice_clone.status</code>. 状态
     */
    @Nullable
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_ai_voice_clone.status</code>. 状态
     */
    public CreationAiVoiceClone setStatus(@Nullable String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_voice_clone.cn_name</code>. 中文名称
     */
    @Nullable
    public String getCnName() {
        return this.cnName;
    }

    /**
     * Setter for <code>creation.creation_ai_voice_clone.cn_name</code>. 中文名称
     */
    public CreationAiVoiceClone setCnName(@Nullable String cnName) {
        this.cnName = cnName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_voice_clone.avatar</code>. 头像
     */
    @Nullable
    public String getAvatar() {
        return this.avatar;
    }

    /**
     * Setter for <code>creation.creation_ai_voice_clone.avatar</code>. 头像
     */
    public CreationAiVoiceClone setAvatar(@Nullable String avatar) {
        this.avatar = avatar;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_voice_clone.audio_url</code>.
     * 声音文件地址
     */
    @Nullable
    public String getAudioUrl() {
        return this.audioUrl;
    }

    /**
     * Setter for <code>creation.creation_ai_voice_clone.audio_url</code>.
     * 声音文件地址
     */
    public CreationAiVoiceClone setAudioUrl(@Nullable String audioUrl) {
        this.audioUrl = audioUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_voice_clone.language</code>. 语言
     */
    @Nullable
    public String getLanguage() {
        return this.language;
    }

    /**
     * Setter for <code>creation.creation_ai_voice_clone.language</code>. 语言
     */
    public CreationAiVoiceClone setLanguage(@Nullable String language) {
        this.language = language;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_voice_clone.biz_type</code>. 业务类型
     */
    @Nullable
    public String getBizType() {
        return this.bizType;
    }

    /**
     * Setter for <code>creation.creation_ai_voice_clone.biz_type</code>. 业务类型
     */
    public CreationAiVoiceClone setBizType(@Nullable String bizType) {
        this.bizType = bizType;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_voice_clone.dialect</code>. 方言
     */
    @Nullable
    public String getDialect() {
        return this.dialect;
    }

    /**
     * Setter for <code>creation.creation_ai_voice_clone.dialect</code>. 方言
     */
    public CreationAiVoiceClone setDialect(@Nullable String dialect) {
        this.dialect = dialect;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationAiVoiceClone other = (CreationAiVoiceClone) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.code == null) {
            if (other.code != null)
                return false;
        }
        else if (!this.code.equals(other.code))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.cnName == null) {
            if (other.cnName != null)
                return false;
        }
        else if (!this.cnName.equals(other.cnName))
            return false;
        if (this.avatar == null) {
            if (other.avatar != null)
                return false;
        }
        else if (!this.avatar.equals(other.avatar))
            return false;
        if (this.audioUrl == null) {
            if (other.audioUrl != null)
                return false;
        }
        else if (!this.audioUrl.equals(other.audioUrl))
            return false;
        if (this.language == null) {
            if (other.language != null)
                return false;
        }
        else if (!this.language.equals(other.language))
            return false;
        if (this.bizType == null) {
            if (other.bizType != null)
                return false;
        }
        else if (!this.bizType.equals(other.bizType))
            return false;
        if (this.dialect == null) {
            if (other.dialect != null)
                return false;
        }
        else if (!this.dialect.equals(other.dialect))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.code == null) ? 0 : this.code.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.cnName == null) ? 0 : this.cnName.hashCode());
        result = prime * result + ((this.avatar == null) ? 0 : this.avatar.hashCode());
        result = prime * result + ((this.audioUrl == null) ? 0 : this.audioUrl.hashCode());
        result = prime * result + ((this.language == null) ? 0 : this.language.hashCode());
        result = prime * result + ((this.bizType == null) ? 0 : this.bizType.hashCode());
        result = prime * result + ((this.dialect == null) ? 0 : this.dialect.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationAiVoiceClone (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(code);
        sb.append(", ").append(status);
        sb.append(", ").append(cnName);
        sb.append(", ").append(avatar);
        sb.append(", ").append(audioUrl);
        sb.append(", ").append(language);
        sb.append(", ").append(bizType);
        sb.append(", ").append(dialect);

        sb.append(")");
        return sb.toString();
    }
}
