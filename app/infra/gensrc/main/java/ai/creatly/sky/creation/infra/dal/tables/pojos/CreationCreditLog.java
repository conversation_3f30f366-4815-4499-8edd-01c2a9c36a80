/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 用户余额账户流水
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationCreditLog implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String bizNo;
    private String bizType;
    private String type;
    private Long amount;
    private JSONB creditDeltas;

    public CreationCreditLog() {}

    public CreationCreditLog(CreationCreditLog value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.bizNo = value.bizNo;
        this.bizType = value.bizType;
        this.type = value.type;
        this.amount = value.amount;
        this.creditDeltas = value.creditDeltas;
    }

    public CreationCreditLog(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String bizNo,
        String bizType,
        String type,
        Long amount,
        JSONB creditDeltas
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.bizNo = bizNo;
        this.bizType = bizType;
        this.type = type;
        this.amount = amount;
        this.creditDeltas = creditDeltas;
    }

    /**
     * Getter for <code>creation.creation_credit_log.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_credit_log.id</code>. 主键
     */
    public CreationCreditLog setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_credit_log.created_at</code>. 创建时间
     */
    public CreationCreditLog setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_credit_log.updated_at</code>. 更新时间
     */
    public CreationCreditLog setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.uid</code>. 用户ID
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_credit_log.uid</code>. 用户ID
     */
    public CreationCreditLog setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.biz_no</code>. 业务标识
     */
    public String getBizNo() {
        return this.bizNo;
    }

    /**
     * Setter for <code>creation.creation_credit_log.biz_no</code>. 业务标识
     */
    public CreationCreditLog setBizNo(String bizNo) {
        this.bizNo = bizNo;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.biz_type</code>. 关联业务类型
     */
    public String getBizType() {
        return this.bizType;
    }

    /**
     * Setter for <code>creation.creation_credit_log.biz_type</code>. 关联业务类型
     */
    public CreationCreditLog setBizType(String bizType) {
        this.bizType = bizType;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.type</code>. 账户流水分类（收入/支出）
     */
    public String getType() {
        return this.type;
    }

    /**
     * Setter for <code>creation.creation_credit_log.type</code>. 账户流水分类（收入/支出）
     */
    public CreationCreditLog setType(String type) {
        this.type = type;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.amount</code>.
     * 余额变动总量（正数为收入，负数为支出）
     */
    public Long getAmount() {
        return this.amount;
    }

    /**
     * Setter for <code>creation.creation_credit_log.amount</code>.
     * 余额变动总量（正数为收入，负数为支出）
     */
    public CreationCreditLog setAmount(Long amount) {
        this.amount = amount;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.credit_deltas</code>.
     * 余额变动列表
     */
    public JSONB getCreditDeltas() {
        return this.creditDeltas;
    }

    /**
     * Setter for <code>creation.creation_credit_log.credit_deltas</code>.
     * 余额变动列表
     */
    public CreationCreditLog setCreditDeltas(JSONB creditDeltas) {
        this.creditDeltas = creditDeltas;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationCreditLog other = (CreationCreditLog) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.bizNo == null) {
            if (other.bizNo != null)
                return false;
        }
        else if (!this.bizNo.equals(other.bizNo))
            return false;
        if (this.bizType == null) {
            if (other.bizType != null)
                return false;
        }
        else if (!this.bizType.equals(other.bizType))
            return false;
        if (this.type == null) {
            if (other.type != null)
                return false;
        }
        else if (!this.type.equals(other.type))
            return false;
        if (this.amount == null) {
            if (other.amount != null)
                return false;
        }
        else if (!this.amount.equals(other.amount))
            return false;
        if (this.creditDeltas == null) {
            if (other.creditDeltas != null)
                return false;
        }
        else if (!this.creditDeltas.equals(other.creditDeltas))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.bizNo == null) ? 0 : this.bizNo.hashCode());
        result = prime * result + ((this.bizType == null) ? 0 : this.bizType.hashCode());
        result = prime * result + ((this.type == null) ? 0 : this.type.hashCode());
        result = prime * result + ((this.amount == null) ? 0 : this.amount.hashCode());
        result = prime * result + ((this.creditDeltas == null) ? 0 : this.creditDeltas.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationCreditLog (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(bizNo);
        sb.append(", ").append(bizType);
        sb.append(", ").append(type);
        sb.append(", ").append(amount);
        sb.append(", ").append(creditDeltas);

        sb.append(")");
        return sb.toString();
    }
}
