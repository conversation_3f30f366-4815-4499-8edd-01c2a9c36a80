/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.SoundTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationSound;

import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 音效库
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class SoundRecord extends UpdatableRecordImpl<SoundRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_sound.id</code>. 主键ID
     */
    public SoundRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.id</code>. 主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_sound.created_at</code>. 创建时间
     */
    public SoundRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_sound.updated_at</code>. 更新时间
     */
    public SoundRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_sound.uid</code>. 所属用户ID（1为平台）
     */
    public SoundRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.uid</code>. 所属用户ID（1为平台）
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_sound.status</code>. 状态
     */
    public SoundRecord setStatus(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.status</code>. 状态
     */
    public String getStatus() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_sound.source_type</code>. 来源方类型
     */
    public SoundRecord setSourceType(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.source_type</code>. 来源方类型
     */
    public String getSourceType() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_sound.source_no</code>. 来源方的音效编号
     */
    public SoundRecord setSourceNo(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.source_no</code>. 来源方的音效编号
     */
    public String getSourceNo() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_sound.author_name</code>. 音效作者
     */
    public SoundRecord setAuthorName(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.author_name</code>. 音效作者
     */
    public String getAuthorName() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_sound.name</code>. 音效名称
     */
    public SoundRecord setName(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.name</code>. 音效名称
     */
    public String getName() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_sound.tag_names</code>. 音效标签名列表
     */
    public SoundRecord setTagNames(String[] value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.tag_names</code>. 音效标签名列表
     */
    public String[] getTagNames() {
        return (String[]) get(9);
    }

    /**
     * Setter for <code>creation.creation_sound.audio_id</code>.
     */
    public SoundRecord setAudioId(Long value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.audio_id</code>.
     */
    public Long getAudioId() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>creation.creation_sound.audio_url</code>. 音效文件地址（OSS格式）
     */
    public SoundRecord setAudioUrl(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.audio_url</code>. 音效文件地址（OSS格式）
     */
    public String getAudioUrl() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_sound.audio_format</code>. 音效文件格式（小写）
     */
    public SoundRecord setAudioFormat(String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.audio_format</code>. 音效文件格式（小写）
     */
    public String getAudioFormat() {
        return (String) get(12);
    }

    /**
     * Setter for <code>creation.creation_sound.duration</code>. 音效时长
     */
    public SoundRecord setDuration(Duration value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.duration</code>. 音效时长
     */
    public Duration getDuration() {
        return (Duration) get(13);
    }

    /**
     * Setter for <code>creation.creation_sound.bitrate</code>. 比特率（kps）
     */
    public SoundRecord setBitrate(Integer value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.bitrate</code>. 比特率（kps）
     */
    public Integer getBitrate() {
        return (Integer) get(14);
    }

    /**
     * Setter for <code>creation.creation_sound.sample_rate</code>. 采样率（Hz）
     */
    public SoundRecord setSampleRate(Integer value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.sample_rate</code>. 采样率（Hz）
     */
    public Integer getSampleRate() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>creation.creation_sound.creator</code>. 创建者
     */
    public SoundRecord setCreator(JSONB value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return (JSONB) get(16);
    }

    /**
     * Setter for <code>creation.creation_sound.updater</code>. 更新者
     */
    public SoundRecord setUpdater(JSONB value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_sound.updater</code>. 更新者
     */
    public JSONB getUpdater() {
        return (JSONB) get(17);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SoundRecord
     */
    public SoundRecord() {
        super(SoundTable.CREATION_SOUND);
    }

    /**
     * Create a detached, initialised SoundRecord
     */
    public SoundRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String status, String sourceType, String sourceNo, String authorName, String name, String[] tagNames, Long audioId, String audioUrl, String audioFormat, Duration duration, Integer bitrate, Integer sampleRate, JSONB creator, JSONB updater) {
        super(SoundTable.CREATION_SOUND);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setStatus(status);
        setSourceType(sourceType);
        setSourceNo(sourceNo);
        setAuthorName(authorName);
        setName(name);
        setTagNames(tagNames);
        setAudioId(audioId);
        setAudioUrl(audioUrl);
        setAudioFormat(audioFormat);
        setDuration(duration);
        setBitrate(bitrate);
        setSampleRate(sampleRate);
        setCreator(creator);
        setUpdater(updater);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised SoundRecord
     */
    public SoundRecord(CreationSound value) {
        super(SoundTable.CREATION_SOUND);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setStatus(value.getStatus());
            setSourceType(value.getSourceType());
            setSourceNo(value.getSourceNo());
            setAuthorName(value.getAuthorName());
            setName(value.getName());
            setTagNames(value.getTagNames());
            setAudioId(value.getAudioId());
            setAudioUrl(value.getAudioUrl());
            setAudioFormat(value.getAudioFormat());
            setDuration(value.getDuration());
            setBitrate(value.getBitrate());
            setSampleRate(value.getSampleRate());
            setCreator(value.getCreator());
            setUpdater(value.getUpdater());
            resetChangedOnNotNull();
        }
    }
}
