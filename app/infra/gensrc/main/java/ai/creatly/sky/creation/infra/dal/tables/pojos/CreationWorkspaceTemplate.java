/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationWorkspaceTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String name;
    private String type;
    private JSONB workspaceConfig;
    private String status;
    private String tag;

    public CreationWorkspaceTemplate() {}

    public CreationWorkspaceTemplate(CreationWorkspaceTemplate value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.name = value.name;
        this.type = value.type;
        this.workspaceConfig = value.workspaceConfig;
        this.status = value.status;
        this.tag = value.tag;
    }

    public CreationWorkspaceTemplate(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        String name,
        String type,
        JSONB workspaceConfig,
        String status,
        @Nullable String tag
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.name = name;
        this.type = type;
        this.workspaceConfig = workspaceConfig;
        this.status = status;
        this.tag = tag;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.id</code>.
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_workspace_template.id</code>.
     */
    public CreationWorkspaceTemplate setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.created_at</code>.
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_workspace_template.created_at</code>.
     */
    public CreationWorkspaceTemplate setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.updated_at</code>.
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_workspace_template.updated_at</code>.
     */
    public CreationWorkspaceTemplate setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.creation_workspace_template.name</code>.
     */
    public CreationWorkspaceTemplate setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.type</code>.
     */
    public String getType() {
        return this.type;
    }

    /**
     * Setter for <code>creation.creation_workspace_template.type</code>.
     */
    public CreationWorkspaceTemplate setType(String type) {
        this.type = type;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_workspace_template.workspace_config</code>.
     */
    public JSONB getWorkspaceConfig() {
        return this.workspaceConfig;
    }

    /**
     * Setter for
     * <code>creation.creation_workspace_template.workspace_config</code>.
     */
    public CreationWorkspaceTemplate setWorkspaceConfig(JSONB workspaceConfig) {
        this.workspaceConfig = workspaceConfig;
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.status</code>.
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_workspace_template.status</code>.
     */
    public CreationWorkspaceTemplate setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_workspace_template.tag</code>. 模板标签
     */
    @Nullable
    public String getTag() {
        return this.tag;
    }

    /**
     * Setter for <code>creation.creation_workspace_template.tag</code>. 模板标签
     */
    public CreationWorkspaceTemplate setTag(@Nullable String tag) {
        this.tag = tag;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationWorkspaceTemplate other = (CreationWorkspaceTemplate) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.type == null) {
            if (other.type != null)
                return false;
        }
        else if (!this.type.equals(other.type))
            return false;
        if (this.workspaceConfig == null) {
            if (other.workspaceConfig != null)
                return false;
        }
        else if (!this.workspaceConfig.equals(other.workspaceConfig))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.tag == null) {
            if (other.tag != null)
                return false;
        }
        else if (!this.tag.equals(other.tag))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.type == null) ? 0 : this.type.hashCode());
        result = prime * result + ((this.workspaceConfig == null) ? 0 : this.workspaceConfig.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.tag == null) ? 0 : this.tag.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationWorkspaceTemplate (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(name);
        sb.append(", ").append(type);
        sb.append(", ").append(workspaceConfig);
        sb.append(", ").append(status);
        sb.append(", ").append(tag);

        sb.append(")");
        return sb.toString();
    }
}
