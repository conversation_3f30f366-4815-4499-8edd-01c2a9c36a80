/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.ConversationMessageRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 会话消息
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class ConversationMessageTable extends TableImpl<ConversationMessageRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>creation.creation_conversation_message</code>
     */
    public static final ConversationMessageTable CREATION_CONVERSATION_MESSAGE = new ConversationMessageTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ConversationMessageRecord> getRecordType() {
        return ConversationMessageRecord.class;
    }

    /**
     * The column <code>creation.creation_conversation_message.id</code>. 主键
     */
    public final TableField<ConversationMessageRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键");

    /**
     * The column
     * <code>creation.creation_conversation_message.created_at</code>. 创建时间
     */
    public final TableField<ConversationMessageRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column
     * <code>creation.creation_conversation_message.updated_at</code>. 更新时间
     */
    public final TableField<ConversationMessageRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_conversation_message.uid</code>. 用户ID
     */
    public final TableField<ConversationMessageRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户ID");

    /**
     * The column
     * <code>creation.creation_conversation_message.conversation_id</code>. 会话ID
     */
    public final TableField<ConversationMessageRecord, Long> CONVERSATION_ID = createField(DSL.name("conversation_id"), SQLDataType.BIGINT.nullable(false), this, "会话ID");

    /**
     * The column <code>creation.creation_conversation_message.role</code>. 角色
     */
    public final TableField<ConversationMessageRecord, JSONB> ROLE = createField(DSL.name("role"), SQLDataType.JSONB.nullable(false), this, "角色");

    /**
     * The column <code>creation.creation_conversation_message.content</code>.
     * 内容
     */
    public final TableField<ConversationMessageRecord, JSONB> CONTENT = createField(DSL.name("content"), SQLDataType.JSONB.nullable(false), this, "内容");

    private ConversationMessageTable(Name alias, Table<ConversationMessageRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private ConversationMessageTable(Name alias, Table<ConversationMessageRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("会话消息"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_conversation_message</code>
     * table reference
     */
    public ConversationMessageTable(String alias) {
        this(DSL.name(alias), CREATION_CONVERSATION_MESSAGE);
    }

    /**
     * Create an aliased <code>creation.creation_conversation_message</code>
     * table reference
     */
    public ConversationMessageTable(Name alias) {
        this(alias, CREATION_CONVERSATION_MESSAGE);
    }

    /**
     * Create a <code>creation.creation_conversation_message</code> table
     * reference
     */
    public ConversationMessageTable() {
        this(DSL.name("creation_conversation_message"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public Identity<ConversationMessageRecord, Long> getIdentity() {
        return (Identity<ConversationMessageRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<ConversationMessageRecord> getPrimaryKey() {
        return Keys.CREATION_CONVERSATION_MESSAGE_PKEY;
    }

    @Override
    public ConversationMessageTable as(String alias) {
        return new ConversationMessageTable(DSL.name(alias), this);
    }

    @Override
    public ConversationMessageTable as(Name alias) {
        return new ConversationMessageTable(alias, this);
    }

    @Override
    public ConversationMessageTable as(Table<?> alias) {
        return new ConversationMessageTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ConversationMessageTable rename(String name) {
        return new ConversationMessageTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ConversationMessageTable rename(Name name) {
        return new ConversationMessageTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ConversationMessageTable rename(Table<?> name) {
        return new ConversationMessageTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConversationMessageTable where(Condition condition) {
        return new ConversationMessageTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConversationMessageTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConversationMessageTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConversationMessageTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConversationMessageTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConversationMessageTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConversationMessageTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConversationMessageTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConversationMessageTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConversationMessageTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
