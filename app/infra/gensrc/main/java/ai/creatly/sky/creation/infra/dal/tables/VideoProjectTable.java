/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.VideoProjectRecord;

import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;
import org.jooq.types.YearToSecond;


/**
 * 视频项目
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class VideoProjectTable extends TableImpl<VideoProjectRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_video_project</code>
     */
    public static final VideoProjectTable CREATION_VIDEO_PROJECT = new VideoProjectTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<VideoProjectRecord> getRecordType() {
        return VideoProjectRecord.class;
    }

    /**
     * The column <code>creation.creation_video_project.id</code>. 主键
     */
    public final TableField<VideoProjectRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_video_project.created_at</code>. 创建时间
     */
    public final TableField<VideoProjectRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_video_project.updated_at</code>. 更新时间
     */
    public final TableField<VideoProjectRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_video_project.uid</code>. 用户ID
     */
    public final TableField<VideoProjectRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户ID");

    /**
     * The column <code>creation.creation_video_project.status</code>. 项目状态
     */
    public final TableField<VideoProjectRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(32).nullable(false), this, "项目状态");

    /**
     * The column <code>creation.creation_video_project.stage</code>. 项目阶段
     */
    public final TableField<VideoProjectRecord, String> STAGE = createField(DSL.name("stage"), SQLDataType.VARCHAR(32).nullable(false), this, "项目阶段");

    /**
     * The column <code>creation.creation_video_project.name</code>. 项目名
     */
    public final TableField<VideoProjectRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(128).nullable(false), this, "项目名");

    /**
     * The column <code>creation.creation_video_project.type</code>.
     */
    public final TableField<VideoProjectRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(32).nullable(false), this, "");

    /**
     * The column <code>creation.creation_video_project.assets</code>. 素材列表
     */
    public final TableField<VideoProjectRecord, JSONB> ASSETS = createField(DSL.name("assets"), SQLDataType.JSONB.nullable(false), this, "素材列表");

    /**
     * The column <code>creation.creation_video_project.script</code>. 视频脚本
     */
    public final TableField<VideoProjectRecord, JSONB> SCRIPT = createField(DSL.name("script"), SQLDataType.JSONB.nullable(false), this, "视频脚本");

    /**
     * The column <code>creation.creation_video_project.voiceover</code>. 视频配音
     */
    public final TableField<VideoProjectRecord, JSONB> VOICEOVER = createField(DSL.name("voiceover"), SQLDataType.JSONB, this, "视频配音");

    /**
     * The column <code>creation.creation_video_project.roles</code>. 视频角色列表
     */
    public final TableField<VideoProjectRecord, JSONB> ROLES = createField(DSL.name("roles"), SQLDataType.JSONB.nullable(false), this, "视频角色列表");

    /**
     * The column <code>creation.creation_video_project.subtitle_layer</code>.
     * 字幕层
     */
    public final TableField<VideoProjectRecord, JSONB> SUBTITLE_LAYER = createField(DSL.name("subtitle_layer"), SQLDataType.JSONB, this, "字幕层");

    /**
     * The column <code>creation.creation_video_project.avatar_layer</code>.
     * 数字人视频层
     */
    public final TableField<VideoProjectRecord, JSONB> AVATAR_LAYER = createField(DSL.name("avatar_layer"), SQLDataType.JSONB, this, "数字人视频层");

    /**
     * The column <code>creation.creation_video_project.background_layer</code>.
     * 背景层
     */
    public final TableField<VideoProjectRecord, JSONB> BACKGROUND_LAYER = createField(DSL.name("background_layer"), SQLDataType.JSONB, this, "背景层");

    /**
     * The column <code>creation.creation_video_project.audio_tracks</code>.
     * 音轨列表
     */
    public final TableField<VideoProjectRecord, JSONB> AUDIO_TRACKS = createField(DSL.name("audio_tracks"), SQLDataType.JSONB.nullable(false), this, "音轨列表");

    /**
     * The column <code>creation.creation_video_project.video_tracks</code>.
     * 视频轨列表
     */
    public final TableField<VideoProjectRecord, JSONB> VIDEO_TRACKS = createField(DSL.name("video_tracks"), SQLDataType.JSONB.nullable(false), this, "视频轨列表");

    /**
     * The column <code>creation.creation_video_project.aspect_ratio</code>.
     * 视频宽高比
     */
    public final TableField<VideoProjectRecord, String> ASPECT_RATIO = createField(DSL.name("aspect_ratio"), SQLDataType.VARCHAR(32).nullable(false), this, "视频宽高比");

    /**
     * The column <code>creation.creation_video_project.width</code>. 视频默认宽度（像素）
     */
    public final TableField<VideoProjectRecord, Integer> WIDTH = createField(DSL.name("width"), SQLDataType.INTEGER.nullable(false), this, "视频默认宽度（像素）");

    /**
     * The column <code>creation.creation_video_project.height</code>.
     * 视频默认高度（像素）
     */
    public final TableField<VideoProjectRecord, Integer> HEIGHT = createField(DSL.name("height"), SQLDataType.INTEGER.nullable(false), this, "视频默认高度（像素）");

    /**
     * The column <code>creation.creation_video_project.shots</code>. 视频分镜列表
     */
    public final TableField<VideoProjectRecord, JSONB> SHOTS = createField(DSL.name("shots"), SQLDataType.JSONB.nullable(false), this, "视频分镜列表");

    /**
     * The column <code>creation.creation_video_project.compose_at</code>.
     * 视频编排时间（最新一次）
     */
    public final TableField<VideoProjectRecord, ZonedDateTime> COMPOSE_AT = createField(DSL.name("compose_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3), this, "视频编排时间（最新一次）", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_video_project.compose_expired</code>.
     * 视频编排是否过期
     */
    public final TableField<VideoProjectRecord, Boolean> COMPOSE_EXPIRED = createField(DSL.name("compose_expired"), SQLDataType.BOOLEAN.nullable(false), this, "视频编排是否过期");

    /**
     * The column <code>creation.creation_video_project.compose_version</code>.
     * 视频编排版本
     */
    public final TableField<VideoProjectRecord, Integer> COMPOSE_VERSION = createField(DSL.name("compose_version"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.field(DSL.raw("1"), SQLDataType.INTEGER)), this, "视频编排版本");

    /**
     * The column <code>creation.creation_video_project.cover_file_id</code>.
     * 项目封面文件ID
     */
    public final TableField<VideoProjectRecord, Long> COVER_FILE_ID = createField(DSL.name("cover_file_id"), SQLDataType.BIGINT, this, "项目封面文件ID");

    /**
     * The column <code>creation.creation_video_project.cover_url</code>. 项目封面地址
     */
    public final TableField<VideoProjectRecord, String> COVER_URL = createField(DSL.name("cover_url"), SQLDataType.VARCHAR(128), this, "项目封面地址");

    /**
     * The column <code>creation.creation_video_project.duration</code>.
     * 视频总时长（最新一次）
     */
    public final TableField<VideoProjectRecord, Duration> DURATION = createField(DSL.name("duration"), SQLDataType.INTERVAL, this, "视频总时长（最新一次）", Converter.ofNullable(YearToSecond.class, Duration.class, YearToSecond::toDuration, YearToSecond::valueOf));

    /**
     * The column <code>creation.creation_video_project.videos</code>.
     * 成品视频（最新一批）
     */
    public final TableField<VideoProjectRecord, JSONB> VIDEOS = createField(DSL.name("videos"), SQLDataType.JSONB.nullable(false), this, "成品视频（最新一批）");

    /**
     * The column <code>creation.creation_video_project.compose_tasks</code>.
     * 最新一版编排任务
     */
    public final TableField<VideoProjectRecord, JSONB> COMPOSE_TASKS = createField(DSL.name("compose_tasks"), SQLDataType.JSONB.nullable(false).defaultValue(DSL.field(DSL.raw("'[]'::jsonb"), SQLDataType.JSONB)), this, "最新一版编排任务");

    /**
     * The column
     * <code>creation.creation_video_project.stage_failure_reason</code>.
     * 当前项目阶段的失败原因
     */
    public final TableField<VideoProjectRecord, String> STAGE_FAILURE_REASON = createField(DSL.name("stage_failure_reason"), SQLDataType.VARCHAR(200).nullable(false).defaultValue(DSL.field(DSL.raw("''::character varying"), SQLDataType.VARCHAR)), this, "当前项目阶段的失败原因");

    private VideoProjectTable(Name alias, Table<VideoProjectRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private VideoProjectTable(Name alias, Table<VideoProjectRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("视频项目"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_video_project</code> table
     * reference
     */
    public VideoProjectTable(String alias) {
        this(DSL.name(alias), CREATION_VIDEO_PROJECT);
    }

    /**
     * Create an aliased <code>creation.creation_video_project</code> table
     * reference
     */
    public VideoProjectTable(Name alias) {
        this(alias, CREATION_VIDEO_PROJECT);
    }

    /**
     * Create a <code>creation.creation_video_project</code> table reference
     */
    public VideoProjectTable() {
        this(DSL.name("creation_video_project"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<VideoProjectRecord> getPrimaryKey() {
        return Keys.CREATION_VIDEO_PROJECT_PKEY;
    }

    @Override
    public VideoProjectTable as(String alias) {
        return new VideoProjectTable(DSL.name(alias), this);
    }

    @Override
    public VideoProjectTable as(Name alias) {
        return new VideoProjectTable(alias, this);
    }

    @Override
    public VideoProjectTable as(Table<?> alias) {
        return new VideoProjectTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public VideoProjectTable rename(String name) {
        return new VideoProjectTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public VideoProjectTable rename(Name name) {
        return new VideoProjectTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public VideoProjectTable rename(Table<?> name) {
        return new VideoProjectTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public VideoProjectTable where(Condition condition) {
        return new VideoProjectTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public VideoProjectTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public VideoProjectTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public VideoProjectTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public VideoProjectTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public VideoProjectTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public VideoProjectTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public VideoProjectTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public VideoProjectTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public VideoProjectTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
