/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal;


import ai.creatly.sky.creation.infra.dal.tables.CreditHistoryTable;

import javax.annotation.processing.Generated;

import org.jooq.Index;
import org.jooq.OrderField;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling indexes of tables in creation.
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class Indexes {

    // -------------------------------------------------------------------------
    // INDEX definitions
    // -------------------------------------------------------------------------

    public static final Index CREATION_CREDIT_HISTORY_UID_BIZ_NO_BIZ_TYPE_TYPE_IDX = Internal.createIndex(DSL.name("creation_credit_history_uid_biz_no_biz_type_type_idx"), CreditHistoryTable.CREATION_CREDIT_HISTORY, new OrderField[] { CreditHistoryTable.CREATION_CREDIT_HISTORY.UID, CreditHistoryTable.CREATION_CREDIT_HISTORY.BIZ_NO, CreditHistoryTable.CREATION_CREDIT_HISTORY.BIZ_TYPE, CreditHistoryTable.CREATION_CREDIT_HISTORY.TYPE }, false);
}
