/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.DigHumanVideoTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.DigHumanVideo;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 数字人视频
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class DigHumanVideoRecord extends UpdatableRecordImpl<DigHumanVideoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.dig_human_video.id</code>. 主键ID
     */
    public DigHumanVideoRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.id</code>. 主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.dig_human_video.created_at</code>. 创建时间
     */
    public DigHumanVideoRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.dig_human_video.updated_at</code>. 更新时间
     */
    public DigHumanVideoRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.dig_human_video.uid</code>. 所属用户ID（1为平台）
     */
    public DigHumanVideoRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.uid</code>. 所属用户ID（1为平台）
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.dig_human_video.name</code>. 数字人视频名
     */
    public DigHumanVideoRecord setName(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.name</code>. 数字人视频名
     */
    public String getName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.dig_human_video.avatar_id</code>. 数字人标识
     */
    public DigHumanVideoRecord setAvatarId(Long value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.avatar_id</code>. 数字人标识
     */
    public Long getAvatarId() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>creation.dig_human_video.ext_out_avatar_id</code>.
     * 数字人形象外部标识
     */
    public DigHumanVideoRecord setExtOutAvatarId(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.ext_out_avatar_id</code>.
     * 数字人形象外部标识
     */
    public String getExtOutAvatarId() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.dig_human_video.ext_out_id</code>. 数字人视频外部标识
     */
    public DigHumanVideoRecord setExtOutId(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.ext_out_id</code>. 数字人视频外部标识
     */
    @Nullable
    public String getExtOutId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.dig_human_video.video_file</code>. 数字人视频
     */
    public DigHumanVideoRecord setVideoFile(@Nullable JSONB value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.video_file</code>. 数字人视频
     */
    @Nullable
    public JSONB getVideoFile() {
        return (JSONB) get(8);
    }

    /**
     * Setter for <code>creation.dig_human_video.status</code>. 状态
     */
    public DigHumanVideoRecord setStatus(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.status</code>. 状态
     */
    public String getStatus() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.dig_human_video.size</code>. 数字人视频尺寸
     */
    public DigHumanVideoRecord setSize(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.size</code>. 数字人视频尺寸
     */
    public String getSize() {
        return (String) get(10);
    }

    /**
     * Setter for <code>creation.dig_human_video.mask_video_file</code>.
     * 数字人mask视频
     */
    public DigHumanVideoRecord setMaskVideoFile(@Nullable JSONB value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.mask_video_file</code>.
     * 数字人mask视频
     */
    @Nullable
    public JSONB getMaskVideoFile() {
        return (JSONB) get(11);
    }

    /**
     * Setter for <code>creation.dig_human_video.audio_file</code>. 数字人audio
     */
    public DigHumanVideoRecord setAudioFile(@Nullable JSONB value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.audio_file</code>. 数字人audio
     */
    @Nullable
    public JSONB getAudioFile() {
        return (JSONB) get(12);
    }

    /**
     * Setter for <code>creation.dig_human_video.bg_file</code>. 数字人视频背景
     */
    public DigHumanVideoRecord setBgFile(@Nullable JSONB value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.bg_file</code>. 数字人视频背景
     */
    @Nullable
    public JSONB getBgFile() {
        return (JSONB) get(13);
    }

    /**
     * Setter for <code>creation.dig_human_video.green_video_file</code>. 绿幕视频
     */
    public DigHumanVideoRecord setGreenVideoFile(@Nullable JSONB value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.green_video_file</code>. 绿幕视频
     */
    @Nullable
    public JSONB getGreenVideoFile() {
        return (JSONB) get(14);
    }

    /**
     * Setter for <code>creation.dig_human_video.alpha_video_file</code>. 透明通道视频
     */
    public DigHumanVideoRecord setAlphaVideoFile(@Nullable JSONB value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_video.alpha_video_file</code>. 透明通道视频
     */
    @Nullable
    public JSONB getAlphaVideoFile() {
        return (JSONB) get(15);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DigHumanVideoRecord
     */
    public DigHumanVideoRecord() {
        super(DigHumanVideoTable.DIG_HUMAN_VIDEO);
    }

    /**
     * Create a detached, initialised DigHumanVideoRecord
     */
    public DigHumanVideoRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String name, Long avatarId, String extOutAvatarId, @Nullable String extOutId, @Nullable JSONB videoFile, String status, String size, @Nullable JSONB maskVideoFile, @Nullable JSONB audioFile, @Nullable JSONB bgFile, @Nullable JSONB greenVideoFile, @Nullable JSONB alphaVideoFile) {
        super(DigHumanVideoTable.DIG_HUMAN_VIDEO);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setName(name);
        setAvatarId(avatarId);
        setExtOutAvatarId(extOutAvatarId);
        setExtOutId(extOutId);
        setVideoFile(videoFile);
        setStatus(status);
        setSize(size);
        setMaskVideoFile(maskVideoFile);
        setAudioFile(audioFile);
        setBgFile(bgFile);
        setGreenVideoFile(greenVideoFile);
        setAlphaVideoFile(alphaVideoFile);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised DigHumanVideoRecord
     */
    public DigHumanVideoRecord(DigHumanVideo value) {
        super(DigHumanVideoTable.DIG_HUMAN_VIDEO);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setName(value.getName());
            setAvatarId(value.getAvatarId());
            setExtOutAvatarId(value.getExtOutAvatarId());
            setExtOutId(value.getExtOutId());
            setVideoFile(value.getVideoFile());
            setStatus(value.getStatus());
            setSize(value.getSize());
            setMaskVideoFile(value.getMaskVideoFile());
            setAudioFile(value.getAudioFile());
            setBgFile(value.getBgFile());
            setGreenVideoFile(value.getGreenVideoFile());
            setAlphaVideoFile(value.getAlphaVideoFile());
            resetChangedOnNotNull();
        }
    }
}
