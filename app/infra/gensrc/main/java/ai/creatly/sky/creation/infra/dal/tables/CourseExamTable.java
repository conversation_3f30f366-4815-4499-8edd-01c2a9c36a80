/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseExamRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 考试记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseExamTable extends TableImpl<CourseExamRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.course_exam</code>
     */
    public static final CourseExamTable COURSE_EXAM = new CourseExamTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseExamRecord> getRecordType() {
        return CourseExamRecord.class;
    }

    /**
     * The column <code>creation.course_exam.id</code>. 考试ID
     */
    public final TableField<CourseExamRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "考试ID");

    /**
     * The column <code>creation.course_exam.created_at</code>. 创建时间
     */
    public final TableField<CourseExamRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.course_exam.updated_at</code>. 更新时间
     */
    public final TableField<CourseExamRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.course_exam.cert_id</code>. 证书ID
     */
    public final TableField<CourseExamRecord, Long> CERT_ID = createField(DSL.name("cert_id"), SQLDataType.BIGINT, this, "证书ID");

    /**
     * The column <code>creation.course_exam.uid</code>. 用户ID
     */
    public final TableField<CourseExamRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT, this, "用户ID");

    /**
     * The column <code>creation.course_exam.org_code</code>. 组织机构
     */
    public final TableField<CourseExamRecord, String> ORG_CODE = createField(DSL.name("org_code"), SQLDataType.VARCHAR(32), this, "组织机构");

    /**
     * The column <code>creation.course_exam.score</code>. 考试得分
     */
    public final TableField<CourseExamRecord, Long> SCORE = createField(DSL.name("score"), SQLDataType.BIGINT, this, "考试得分");

    /**
     * The column <code>creation.course_exam.status</code>.
     * 考试状态，INIT，SUBMIT，FINISH
     */
    public final TableField<CourseExamRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(16), this, "考试状态，INIT，SUBMIT，FINISH");

    /**
     * The column <code>creation.course_exam.user_score</code>. 用户得分
     */
    public final TableField<CourseExamRecord, Long> USER_SCORE = createField(DSL.name("user_score"), SQLDataType.BIGINT, this, "用户得分");

    /**
     * The column <code>creation.course_exam.pass_score</code>. 通过分数
     */
    public final TableField<CourseExamRecord, Long> PASS_SCORE = createField(DSL.name("pass_score"), SQLDataType.BIGINT, this, "通过分数");

    private CourseExamTable(Name alias, Table<CourseExamRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private CourseExamTable(Name alias, Table<CourseExamRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("考试记录"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.course_exam</code> table reference
     */
    public CourseExamTable(String alias) {
        this(DSL.name(alias), COURSE_EXAM);
    }

    /**
     * Create an aliased <code>creation.course_exam</code> table reference
     */
    public CourseExamTable(Name alias) {
        this(alias, COURSE_EXAM);
    }

    /**
     * Create a <code>creation.course_exam</code> table reference
     */
    public CourseExamTable() {
        this(DSL.name("course_exam"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<CourseExamRecord> getPrimaryKey() {
        return Keys.COURSE_EXAM_PKEY;
    }

    @Override
    public CourseExamTable as(String alias) {
        return new CourseExamTable(DSL.name(alias), this);
    }

    @Override
    public CourseExamTable as(Name alias) {
        return new CourseExamTable(alias, this);
    }

    @Override
    public CourseExamTable as(Table<?> alias) {
        return new CourseExamTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseExamTable rename(String name) {
        return new CourseExamTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseExamTable rename(Name name) {
        return new CourseExamTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseExamTable rename(Table<?> name) {
        return new CourseExamTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseExamTable where(Condition condition) {
        return new CourseExamTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseExamTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseExamTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseExamTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CourseExamTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CourseExamTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CourseExamTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CourseExamTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseExamTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseExamTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
