/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 用户权限表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String permissionCode;
    private JSONB payload;
    private String status;

    public UserPermission() {}

    public UserPermission(UserPermission value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.permissionCode = value.permissionCode;
        this.payload = value.payload;
        this.status = value.status;
    }

    public UserPermission(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String permissionCode,
        @Nullable JSONB payload,
        String status
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.permissionCode = permissionCode;
        this.payload = payload;
        this.status = status;
    }

    /**
     * Getter for <code>creation.user_permission.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.user_permission.id</code>. 主键
     */
    public UserPermission setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.user_permission.created_at</code>. 创建时间
     */
    public UserPermission setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.user_permission.updated_at</code>. 更新时间
     */
    public UserPermission setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.uid</code>. 用户id
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.user_permission.uid</code>. 用户id
     */
    public UserPermission setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.permission_code</code>. 权限码
     */
    public String getPermissionCode() {
        return this.permissionCode;
    }

    /**
     * Setter for <code>creation.user_permission.permission_code</code>. 权限码
     */
    public UserPermission setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.payload</code>. 备注信息
     */
    @Nullable
    public JSONB getPayload() {
        return this.payload;
    }

    /**
     * Setter for <code>creation.user_permission.payload</code>. 备注信息
     */
    public UserPermission setPayload(@Nullable JSONB payload) {
        this.payload = payload;
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.status</code>. 有效性
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.user_permission.status</code>. 有效性
     */
    public UserPermission setStatus(String status) {
        this.status = status;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final UserPermission other = (UserPermission) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.permissionCode == null) {
            if (other.permissionCode != null)
                return false;
        }
        else if (!this.permissionCode.equals(other.permissionCode))
            return false;
        if (this.payload == null) {
            if (other.payload != null)
                return false;
        }
        else if (!this.payload.equals(other.payload))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.permissionCode == null) ? 0 : this.permissionCode.hashCode());
        result = prime * result + ((this.payload == null) ? 0 : this.payload.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("UserPermission (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(permissionCode);
        sb.append(", ").append(payload);
        sb.append(", ").append(status);

        sb.append(")");
        return sb.toString();
    }
}
