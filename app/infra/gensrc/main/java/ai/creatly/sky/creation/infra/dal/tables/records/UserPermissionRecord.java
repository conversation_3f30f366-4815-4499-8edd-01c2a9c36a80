/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.UserPermissionTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.UserPermission;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户权限表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserPermissionRecord extends UpdatableRecordImpl<UserPermissionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.user_permission.id</code>. 主键
     */
    public UserPermissionRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.user_permission.created_at</code>. 创建时间
     */
    public UserPermissionRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.user_permission.updated_at</code>. 更新时间
     */
    public UserPermissionRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.user_permission.uid</code>. 用户id
     */
    public UserPermissionRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.uid</code>. 用户id
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.user_permission.permission_code</code>. 权限码
     */
    public UserPermissionRecord setPermissionCode(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.permission_code</code>. 权限码
     */
    public String getPermissionCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.user_permission.payload</code>. 备注信息
     */
    public UserPermissionRecord setPayload(@Nullable JSONB value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.payload</code>. 备注信息
     */
    @Nullable
    public JSONB getPayload() {
        return (JSONB) get(5);
    }

    /**
     * Setter for <code>creation.user_permission.status</code>. 有效性
     */
    public UserPermissionRecord setStatus(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_permission.status</code>. 有效性
     */
    public String getStatus() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserPermissionRecord
     */
    public UserPermissionRecord() {
        super(UserPermissionTable.USER_PERMISSION);
    }

    /**
     * Create a detached, initialised UserPermissionRecord
     */
    public UserPermissionRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String permissionCode, @Nullable JSONB payload, String status) {
        super(UserPermissionTable.USER_PERMISSION);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setPermissionCode(permissionCode);
        setPayload(payload);
        setStatus(status);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserPermissionRecord
     */
    public UserPermissionRecord(UserPermission value) {
        super(UserPermissionTable.USER_PERMISSION);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setPermissionCode(value.getPermissionCode());
            setPayload(value.getPayload());
            setStatus(value.getStatus());
            resetChangedOnNotNull();
        }
    }
}
