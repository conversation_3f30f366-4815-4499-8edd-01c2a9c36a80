/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.DramaRecord;

import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;
import org.jooq.types.YearToSecond;


/**
 * 剧情
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class DramaTable extends TableImpl<DramaRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_drama</code>
     */
    public static final DramaTable CREATION_DRAMA = new DramaTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DramaRecord> getRecordType() {
        return DramaRecord.class;
    }

    /**
     * The column <code>creation.creation_drama.id</code>. 主键
     */
    public final TableField<DramaRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_drama.created_at</code>. 创建时间
     */
    public final TableField<DramaRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_drama.updated_at</code>. 更新时间
     */
    public final TableField<DramaRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_drama.uid</code>. 用户ID
     */
    public final TableField<DramaRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户ID");

    /**
     * The column <code>creation.creation_drama.name</code>. 剧情名称
     */
    public final TableField<DramaRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(128).nullable(false), this, "剧情名称");

    /**
     * The column <code>creation.creation_drama.type</code>. 剧情类型
     */
    public final TableField<DramaRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(32).nullable(false), this, "剧情类型");

    /**
     * The column <code>creation.creation_drama.status</code>. 剧情状态
     */
    public final TableField<DramaRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(32).nullable(false), this, "剧情状态");

    /**
     * The column <code>creation.creation_drama.product_categories</code>.
     * 适用的产品类目
     */
    public final TableField<DramaRecord, JSONB> PRODUCT_CATEGORIES = createField(DSL.name("product_categories"), SQLDataType.JSONB.nullable(false), this, "适用的产品类目");

    /**
     * The column <code>creation.creation_drama.crowd_categories</code>. 适用的人群类目
     */
    public final TableField<DramaRecord, JSONB> CROWD_CATEGORIES = createField(DSL.name("crowd_categories"), SQLDataType.JSONB.nullable(false), this, "适用的人群类目");

    /**
     * The column <code>creation.creation_drama.videos</code>. 剧情相关视频
     */
    public final TableField<DramaRecord, JSONB> VIDEOS = createField(DSL.name("videos"), SQLDataType.JSONB.nullable(false), this, "剧情相关视频");

    /**
     * The column <code>creation.creation_drama.duration</code>. 剧情总时长
     */
    public final TableField<DramaRecord, Duration> DURATION = createField(DSL.name("duration"), SQLDataType.INTERVAL.nullable(false), this, "剧情总时长", Converter.ofNullable(YearToSecond.class, Duration.class, YearToSecond::toDuration, YearToSecond::valueOf));

    /**
     * The column <code>creation.creation_drama.roles</code>. 剧情角色列表
     */
    public final TableField<DramaRecord, JSONB> ROLES = createField(DSL.name("roles"), SQLDataType.JSONB.nullable(false), this, "剧情角色列表");

    /**
     * The column <code>creation.creation_drama.cover_url</code>. 剧情封面图地址（OSS协议）
     */
    public final TableField<DramaRecord, String> COVER_URL = createField(DSL.name("cover_url"), SQLDataType.VARCHAR(128), this, "剧情封面图地址（OSS协议）");

    /**
     * The column <code>creation.creation_drama.annotation</code>. 剧情标注
     */
    public final TableField<DramaRecord, JSONB> ANNOTATION = createField(DSL.name("annotation"), SQLDataType.JSONB.nullable(false), this, "剧情标注");

    /**
     * The column <code>creation.creation_drama.stats</code>. 剧情统计信息
     */
    public final TableField<DramaRecord, JSONB> STATS = createField(DSL.name("stats"), SQLDataType.JSONB.nullable(false).defaultValue(DSL.field(DSL.raw("'{}'::jsonb"), SQLDataType.JSONB)), this, "剧情统计信息");

    /**
     * The column <code>creation.creation_drama.review</code>. 剧情评审说明
     */
    public final TableField<DramaRecord, JSONB> REVIEW = createField(DSL.name("review"), SQLDataType.JSONB.nullable(false).defaultValue(DSL.field(DSL.raw("'{}'::jsonb"), SQLDataType.JSONB)), this, "剧情评审说明");

    /**
     * The column <code>creation.creation_drama.content</code>. 剧情内容
     */
    public final TableField<DramaRecord, JSONB> CONTENT = createField(DSL.name("content"), SQLDataType.JSONB.nullable(false).defaultValue(DSL.field(DSL.raw("'{}'::jsonb"), SQLDataType.JSONB)), this, "剧情内容");

    /**
     * The column <code>creation.creation_drama.local_sync_status</code>.
     * 本地剧情同步状态
     */
    public final TableField<DramaRecord, String> LOCAL_SYNC_STATUS = createField(DSL.name("local_sync_status"), SQLDataType.VARCHAR(32).nullable(false).defaultValue(DSL.field(DSL.raw("'out_of_sync'::character varying"), SQLDataType.VARCHAR)), this, "本地剧情同步状态");

    private DramaTable(Name alias, Table<DramaRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private DramaTable(Name alias, Table<DramaRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("剧情"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_drama</code> table reference
     */
    public DramaTable(String alias) {
        this(DSL.name(alias), CREATION_DRAMA);
    }

    /**
     * Create an aliased <code>creation.creation_drama</code> table reference
     */
    public DramaTable(Name alias) {
        this(alias, CREATION_DRAMA);
    }

    /**
     * Create a <code>creation.creation_drama</code> table reference
     */
    public DramaTable() {
        this(DSL.name("creation_drama"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<DramaRecord> getPrimaryKey() {
        return Keys.CREATION_DRAMA_PKEY;
    }

    @Override
    public DramaTable as(String alias) {
        return new DramaTable(DSL.name(alias), this);
    }

    @Override
    public DramaTable as(Name alias) {
        return new DramaTable(alias, this);
    }

    @Override
    public DramaTable as(Table<?> alias) {
        return new DramaTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public DramaTable rename(String name) {
        return new DramaTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public DramaTable rename(Name name) {
        return new DramaTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public DramaTable rename(Table<?> name) {
        return new DramaTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public DramaTable where(Condition condition) {
        return new DramaTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public DramaTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public DramaTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public DramaTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public DramaTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public DramaTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public DramaTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public DramaTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public DramaTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public DramaTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
