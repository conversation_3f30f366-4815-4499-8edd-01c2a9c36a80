/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 声音演员的口音
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationVoiceLocale implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long voiceId;
    private String voiceCode;
    private String code;
    private String desc;
    private String langCode;
    private String langName;
    private String countryCode;
    private String countryName;
    private String ageLevel;
    private Long previewAudioId;
    private String previewAudioUrl;
    private JSONB roles;
    private JSONB emotions;
    private JSONB extInfo;
    private Integer ordinal;
    private String langFamily;
    private String voiceName;
    private Integer wordsPerMinute;

    public CreationVoiceLocale() {}

    public CreationVoiceLocale(CreationVoiceLocale value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.voiceId = value.voiceId;
        this.voiceCode = value.voiceCode;
        this.code = value.code;
        this.desc = value.desc;
        this.langCode = value.langCode;
        this.langName = value.langName;
        this.countryCode = value.countryCode;
        this.countryName = value.countryName;
        this.ageLevel = value.ageLevel;
        this.previewAudioId = value.previewAudioId;
        this.previewAudioUrl = value.previewAudioUrl;
        this.roles = value.roles;
        this.emotions = value.emotions;
        this.extInfo = value.extInfo;
        this.ordinal = value.ordinal;
        this.langFamily = value.langFamily;
        this.voiceName = value.voiceName;
        this.wordsPerMinute = value.wordsPerMinute;
    }

    public CreationVoiceLocale(
        @Nullable Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long voiceId,
        String voiceCode,
        String code,
        String desc,
        String langCode,
        String langName,
        String countryCode,
        String countryName,
        String ageLevel,
        @Nullable Long previewAudioId,
        @Nullable String previewAudioUrl,
        JSONB roles,
        JSONB emotions,
        JSONB extInfo,
        @Nullable Integer ordinal,
        @Nullable String langFamily,
        @Nullable String voiceName,
        @Nullable Integer wordsPerMinute
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.voiceId = voiceId;
        this.voiceCode = voiceCode;
        this.code = code;
        this.desc = desc;
        this.langCode = langCode;
        this.langName = langName;
        this.countryCode = countryCode;
        this.countryName = countryName;
        this.ageLevel = ageLevel;
        this.previewAudioId = previewAudioId;
        this.previewAudioUrl = previewAudioUrl;
        this.roles = roles;
        this.emotions = emotions;
        this.extInfo = extInfo;
        this.ordinal = ordinal;
        this.langFamily = langFamily;
        this.voiceName = voiceName;
        this.wordsPerMinute = wordsPerMinute;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.id</code>. 主键
     */
    public CreationVoiceLocale setId(@Nullable Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.created_at</code>. 创建时间
     */
    public CreationVoiceLocale setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.updated_at</code>. 更新时间
     */
    public CreationVoiceLocale setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.voice_id</code>. 所属声音ID
     */
    public Long getVoiceId() {
        return this.voiceId;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.voice_id</code>. 所属声音ID
     */
    public CreationVoiceLocale setVoiceId(Long voiceId) {
        this.voiceId = voiceId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.voice_code</code>. 所属声音编号
     */
    public String getVoiceCode() {
        return this.voiceCode;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.voice_code</code>. 所属声音编号
     */
    public CreationVoiceLocale setVoiceCode(String voiceCode) {
        this.voiceCode = voiceCode;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.code</code>. 本地化代号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.code</code>. 本地化代号
     */
    public CreationVoiceLocale setCode(String code) {
        this.code = code;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.desc</code>. 本地化描述（中文展示）
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.desc</code>. 本地化描述（中文展示）
     */
    public CreationVoiceLocale setDesc(String desc) {
        this.desc = desc;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.lang_code</code>. 语言代号
     */
    public String getLangCode() {
        return this.langCode;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.lang_code</code>. 语言代号
     */
    public CreationVoiceLocale setLangCode(String langCode) {
        this.langCode = langCode;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.lang_name</code>.
     * 语言名称（中文展示）
     */
    public String getLangName() {
        return this.langName;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.lang_name</code>.
     * 语言名称（中文展示）
     */
    public CreationVoiceLocale setLangName(String langName) {
        this.langName = langName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.country_code</code>. 国家代号
     */
    public String getCountryCode() {
        return this.countryCode;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.country_code</code>. 国家代号
     */
    public CreationVoiceLocale setCountryCode(String countryCode) {
        this.countryCode = countryCode;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.country_name</code>.
     * 国家名称（中文展示）
     */
    public String getCountryName() {
        return this.countryName;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.country_name</code>.
     * 国家名称（中文展示）
     */
    public CreationVoiceLocale setCountryName(String countryName) {
        this.countryName = countryName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.age_level</code>. 默认年龄层
     */
    public String getAgeLevel() {
        return this.ageLevel;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.age_level</code>. 默认年龄层
     */
    public CreationVoiceLocale setAgeLevel(String ageLevel) {
        this.ageLevel = ageLevel;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.preview_audio_id</code>.
     * 预览音频文件ID
     */
    @Nullable
    public Long getPreviewAudioId() {
        return this.previewAudioId;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.preview_audio_id</code>.
     * 预览音频文件ID
     */
    public CreationVoiceLocale setPreviewAudioId(@Nullable Long previewAudioId) {
        this.previewAudioId = previewAudioId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.preview_audio_url</code>.
     * 预览音频地址（OSS地址）
     */
    @Nullable
    public String getPreviewAudioUrl() {
        return this.previewAudioUrl;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.preview_audio_url</code>.
     * 预览音频地址（OSS地址）
     */
    public CreationVoiceLocale setPreviewAudioUrl(@Nullable String previewAudioUrl) {
        this.previewAudioUrl = previewAudioUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.roles</code>. 可扮演角色
     */
    public JSONB getRoles() {
        return this.roles;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.roles</code>. 可扮演角色
     */
    public CreationVoiceLocale setRoles(JSONB roles) {
        this.roles = roles;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.emotions</code>. 可附加情绪
     */
    public JSONB getEmotions() {
        return this.emotions;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.emotions</code>. 可附加情绪
     */
    public CreationVoiceLocale setEmotions(JSONB emotions) {
        this.emotions = emotions;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.ext_info</code>. 扩展信息
     */
    public JSONB getExtInfo() {
        return this.extInfo;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.ext_info</code>. 扩展信息
     */
    public CreationVoiceLocale setExtInfo(JSONB extInfo) {
        this.extInfo = extInfo;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.ordinal</code>. 排序字段
     */
    @Nullable
    public Integer getOrdinal() {
        return this.ordinal;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.ordinal</code>. 排序字段
     */
    public CreationVoiceLocale setOrdinal(@Nullable Integer ordinal) {
        this.ordinal = ordinal;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.lang_family</code>. 语系类型
     */
    @Nullable
    public String getLangFamily() {
        return this.langFamily;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.lang_family</code>. 语系类型
     */
    public CreationVoiceLocale setLangFamily(@Nullable String langFamily) {
        this.langFamily = langFamily;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.voice_name</code>.
     * 本地化声音名称
     */
    @Nullable
    public String getVoiceName() {
        return this.voiceName;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.voice_name</code>.
     * 本地化声音名称
     */
    public CreationVoiceLocale setVoiceName(@Nullable String voiceName) {
        this.voiceName = voiceName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_voice_locale.words_per_minute</code>.
     * 本地化口音语速
     */
    @Nullable
    public Integer getWordsPerMinute() {
        return this.wordsPerMinute;
    }

    /**
     * Setter for <code>creation.creation_voice_locale.words_per_minute</code>.
     * 本地化口音语速
     */
    public CreationVoiceLocale setWordsPerMinute(@Nullable Integer wordsPerMinute) {
        this.wordsPerMinute = wordsPerMinute;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationVoiceLocale other = (CreationVoiceLocale) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.voiceId == null) {
            if (other.voiceId != null)
                return false;
        }
        else if (!this.voiceId.equals(other.voiceId))
            return false;
        if (this.voiceCode == null) {
            if (other.voiceCode != null)
                return false;
        }
        else if (!this.voiceCode.equals(other.voiceCode))
            return false;
        if (this.code == null) {
            if (other.code != null)
                return false;
        }
        else if (!this.code.equals(other.code))
            return false;
        if (this.desc == null) {
            if (other.desc != null)
                return false;
        }
        else if (!this.desc.equals(other.desc))
            return false;
        if (this.langCode == null) {
            if (other.langCode != null)
                return false;
        }
        else if (!this.langCode.equals(other.langCode))
            return false;
        if (this.langName == null) {
            if (other.langName != null)
                return false;
        }
        else if (!this.langName.equals(other.langName))
            return false;
        if (this.countryCode == null) {
            if (other.countryCode != null)
                return false;
        }
        else if (!this.countryCode.equals(other.countryCode))
            return false;
        if (this.countryName == null) {
            if (other.countryName != null)
                return false;
        }
        else if (!this.countryName.equals(other.countryName))
            return false;
        if (this.ageLevel == null) {
            if (other.ageLevel != null)
                return false;
        }
        else if (!this.ageLevel.equals(other.ageLevel))
            return false;
        if (this.previewAudioId == null) {
            if (other.previewAudioId != null)
                return false;
        }
        else if (!this.previewAudioId.equals(other.previewAudioId))
            return false;
        if (this.previewAudioUrl == null) {
            if (other.previewAudioUrl != null)
                return false;
        }
        else if (!this.previewAudioUrl.equals(other.previewAudioUrl))
            return false;
        if (this.roles == null) {
            if (other.roles != null)
                return false;
        }
        else if (!this.roles.equals(other.roles))
            return false;
        if (this.emotions == null) {
            if (other.emotions != null)
                return false;
        }
        else if (!this.emotions.equals(other.emotions))
            return false;
        if (this.extInfo == null) {
            if (other.extInfo != null)
                return false;
        }
        else if (!this.extInfo.equals(other.extInfo))
            return false;
        if (this.ordinal == null) {
            if (other.ordinal != null)
                return false;
        }
        else if (!this.ordinal.equals(other.ordinal))
            return false;
        if (this.langFamily == null) {
            if (other.langFamily != null)
                return false;
        }
        else if (!this.langFamily.equals(other.langFamily))
            return false;
        if (this.voiceName == null) {
            if (other.voiceName != null)
                return false;
        }
        else if (!this.voiceName.equals(other.voiceName))
            return false;
        if (this.wordsPerMinute == null) {
            if (other.wordsPerMinute != null)
                return false;
        }
        else if (!this.wordsPerMinute.equals(other.wordsPerMinute))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.voiceId == null) ? 0 : this.voiceId.hashCode());
        result = prime * result + ((this.voiceCode == null) ? 0 : this.voiceCode.hashCode());
        result = prime * result + ((this.code == null) ? 0 : this.code.hashCode());
        result = prime * result + ((this.desc == null) ? 0 : this.desc.hashCode());
        result = prime * result + ((this.langCode == null) ? 0 : this.langCode.hashCode());
        result = prime * result + ((this.langName == null) ? 0 : this.langName.hashCode());
        result = prime * result + ((this.countryCode == null) ? 0 : this.countryCode.hashCode());
        result = prime * result + ((this.countryName == null) ? 0 : this.countryName.hashCode());
        result = prime * result + ((this.ageLevel == null) ? 0 : this.ageLevel.hashCode());
        result = prime * result + ((this.previewAudioId == null) ? 0 : this.previewAudioId.hashCode());
        result = prime * result + ((this.previewAudioUrl == null) ? 0 : this.previewAudioUrl.hashCode());
        result = prime * result + ((this.roles == null) ? 0 : this.roles.hashCode());
        result = prime * result + ((this.emotions == null) ? 0 : this.emotions.hashCode());
        result = prime * result + ((this.extInfo == null) ? 0 : this.extInfo.hashCode());
        result = prime * result + ((this.ordinal == null) ? 0 : this.ordinal.hashCode());
        result = prime * result + ((this.langFamily == null) ? 0 : this.langFamily.hashCode());
        result = prime * result + ((this.voiceName == null) ? 0 : this.voiceName.hashCode());
        result = prime * result + ((this.wordsPerMinute == null) ? 0 : this.wordsPerMinute.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationVoiceLocale (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(voiceId);
        sb.append(", ").append(voiceCode);
        sb.append(", ").append(code);
        sb.append(", ").append(desc);
        sb.append(", ").append(langCode);
        sb.append(", ").append(langName);
        sb.append(", ").append(countryCode);
        sb.append(", ").append(countryName);
        sb.append(", ").append(ageLevel);
        sb.append(", ").append(previewAudioId);
        sb.append(", ").append(previewAudioUrl);
        sb.append(", ").append(roles);
        sb.append(", ").append(emotions);
        sb.append(", ").append(extInfo);
        sb.append(", ").append(ordinal);
        sb.append(", ").append(langFamily);
        sb.append(", ").append(voiceName);
        sb.append(", ").append(wordsPerMinute);

        sb.append(")");
        return sb.toString();
    }
}
