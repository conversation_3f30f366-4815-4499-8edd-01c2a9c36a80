/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.Arrays;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 用户产品
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationUserProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String title;
    private String status;
    private JSONB category;
    private JSONB crowdCategories;
    private String[] features;
    private String description;
    private Long coverFileId;
    private String coverUrl;
    private JSONB labels;
    private JSONB assets;

    public CreationUserProduct() {}

    public CreationUserProduct(CreationUserProduct value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.title = value.title;
        this.status = value.status;
        this.category = value.category;
        this.crowdCategories = value.crowdCategories;
        this.features = value.features;
        this.description = value.description;
        this.coverFileId = value.coverFileId;
        this.coverUrl = value.coverUrl;
        this.labels = value.labels;
        this.assets = value.assets;
    }

    public CreationUserProduct(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String title,
        String status,
        JSONB category,
        JSONB crowdCategories,
        String[] features,
        String description,
        @Nullable Long coverFileId,
        @Nullable String coverUrl,
        JSONB labels,
        JSONB assets
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.title = title;
        this.status = status;
        this.category = category;
        this.crowdCategories = crowdCategories;
        this.features = features;
        this.description = description;
        this.coverFileId = coverFileId;
        this.coverUrl = coverUrl;
        this.labels = labels;
        this.assets = assets;
    }

    /**
     * Getter for <code>creation.creation_user_product.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_user_product.id</code>. 主键
     */
    public CreationUserProduct setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_user_product.created_at</code>. 创建时间
     */
    public CreationUserProduct setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_user_product.updated_at</code>. 更新时间
     */
    public CreationUserProduct setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.uid</code>. 用户ID
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_user_product.uid</code>. 用户ID
     */
    public CreationUserProduct setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.title</code>. 文件名
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * Setter for <code>creation.creation_user_product.title</code>. 文件名
     */
    public CreationUserProduct setTitle(String title) {
        this.title = title;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.status</code>. 产品状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_user_product.status</code>. 产品状态
     */
    public CreationUserProduct setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.category</code>. 产品类目
     */
    public JSONB getCategory() {
        return this.category;
    }

    /**
     * Setter for <code>creation.creation_user_product.category</code>. 产品类目
     */
    public CreationUserProduct setCategory(JSONB category) {
        this.category = category;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.crowd_categories</code>.
     * 产品适用人群类目
     */
    public JSONB getCrowdCategories() {
        return this.crowdCategories;
    }

    /**
     * Setter for <code>creation.creation_user_product.crowd_categories</code>.
     * 产品适用人群类目
     */
    public CreationUserProduct setCrowdCategories(JSONB crowdCategories) {
        this.crowdCategories = crowdCategories;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.features</code>. 产品卖点
     */
    public String[] getFeatures() {
        return this.features;
    }

    /**
     * Setter for <code>creation.creation_user_product.features</code>. 产品卖点
     */
    public CreationUserProduct setFeatures(String[] features) {
        this.features = features;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.description</code>.
     * 产品详细描述
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>creation.creation_user_product.description</code>.
     * 产品详细描述
     */
    public CreationUserProduct setDescription(String description) {
        this.description = description;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.cover_file_id</code>.
     * 产品封面图文件ID
     */
    @Nullable
    public Long getCoverFileId() {
        return this.coverFileId;
    }

    /**
     * Setter for <code>creation.creation_user_product.cover_file_id</code>.
     * 产品封面图文件ID
     */
    public CreationUserProduct setCoverFileId(@Nullable Long coverFileId) {
        this.coverFileId = coverFileId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.cover_url</code>.
     * 产品封面图地址（OSS地址）
     */
    @Nullable
    public String getCoverUrl() {
        return this.coverUrl;
    }

    /**
     * Setter for <code>creation.creation_user_product.cover_url</code>.
     * 产品封面图地址（OSS地址）
     */
    public CreationUserProduct setCoverUrl(@Nullable String coverUrl) {
        this.coverUrl = coverUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.labels</code>. 产品标签列表
     */
    public JSONB getLabels() {
        return this.labels;
    }

    /**
     * Setter for <code>creation.creation_user_product.labels</code>. 产品标签列表
     */
    public CreationUserProduct setLabels(JSONB labels) {
        this.labels = labels;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.assets</code>. 产品素材列表
     */
    public JSONB getAssets() {
        return this.assets;
    }

    /**
     * Setter for <code>creation.creation_user_product.assets</code>. 产品素材列表
     */
    public CreationUserProduct setAssets(JSONB assets) {
        this.assets = assets;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationUserProduct other = (CreationUserProduct) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.title == null) {
            if (other.title != null)
                return false;
        }
        else if (!this.title.equals(other.title))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.category == null) {
            if (other.category != null)
                return false;
        }
        else if (!this.category.equals(other.category))
            return false;
        if (this.crowdCategories == null) {
            if (other.crowdCategories != null)
                return false;
        }
        else if (!this.crowdCategories.equals(other.crowdCategories))
            return false;
        if (this.features == null) {
            if (other.features != null)
                return false;
        }
        else if (!Arrays.deepEquals(this.features, other.features))
            return false;
        if (this.description == null) {
            if (other.description != null)
                return false;
        }
        else if (!this.description.equals(other.description))
            return false;
        if (this.coverFileId == null) {
            if (other.coverFileId != null)
                return false;
        }
        else if (!this.coverFileId.equals(other.coverFileId))
            return false;
        if (this.coverUrl == null) {
            if (other.coverUrl != null)
                return false;
        }
        else if (!this.coverUrl.equals(other.coverUrl))
            return false;
        if (this.labels == null) {
            if (other.labels != null)
                return false;
        }
        else if (!this.labels.equals(other.labels))
            return false;
        if (this.assets == null) {
            if (other.assets != null)
                return false;
        }
        else if (!this.assets.equals(other.assets))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.title == null) ? 0 : this.title.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.category == null) ? 0 : this.category.hashCode());
        result = prime * result + ((this.crowdCategories == null) ? 0 : this.crowdCategories.hashCode());
        result = prime * result + ((this.features == null) ? 0 : Arrays.deepHashCode(this.features));
        result = prime * result + ((this.description == null) ? 0 : this.description.hashCode());
        result = prime * result + ((this.coverFileId == null) ? 0 : this.coverFileId.hashCode());
        result = prime * result + ((this.coverUrl == null) ? 0 : this.coverUrl.hashCode());
        result = prime * result + ((this.labels == null) ? 0 : this.labels.hashCode());
        result = prime * result + ((this.assets == null) ? 0 : this.assets.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationUserProduct (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(title);
        sb.append(", ").append(status);
        sb.append(", ").append(category);
        sb.append(", ").append(crowdCategories);
        sb.append(", ").append(Arrays.deepToString(features));
        sb.append(", ").append(description);
        sb.append(", ").append(coverFileId);
        sb.append(", ").append(coverUrl);
        sb.append(", ").append(labels);
        sb.append(", ").append(assets);

        sb.append(")");
        return sb.toString();
    }
}
