/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.AiTalkActorRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * AI Talk 演讲者
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiTalkActorTable extends TableImpl<AiTalkActorRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_ai_talk_actor</code>
     */
    public static final AiTalkActorTable CREATION_AI_TALK_ACTOR = new AiTalkActorTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AiTalkActorRecord> getRecordType() {
        return AiTalkActorRecord.class;
    }

    /**
     * The column <code>creation.creation_ai_talk_actor.id</code>. 主键
     */
    public final TableField<AiTalkActorRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_ai_talk_actor.created_at</code>. 创建时间
     */
    public final TableField<AiTalkActorRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_talk_actor.updated_at</code>. 更新时间
     */
    public final TableField<AiTalkActorRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_talk_actor.uid</code>.
     * 所属用户ID（1表示系统）
     */
    public final TableField<AiTalkActorRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "所属用户ID（1表示系统）");

    /**
     * The column <code>creation.creation_ai_talk_actor.name</code>. 名称
     */
    public final TableField<AiTalkActorRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(64).nullable(false), this, "名称");

    /**
     * The column <code>creation.creation_ai_talk_actor.image_id</code>. 图片文件ID
     */
    public final TableField<AiTalkActorRecord, Long> IMAGE_ID = createField(DSL.name("image_id"), SQLDataType.BIGINT.nullable(false), this, "图片文件ID");

    /**
     * The column <code>creation.creation_ai_talk_actor.image_url</code>.
     * 图片路径（冗余OSS协议的文件地址）
     */
    public final TableField<AiTalkActorRecord, String> IMAGE_URL = createField(DSL.name("image_url"), SQLDataType.VARCHAR(255).nullable(false), this, "图片路径（冗余OSS协议的文件地址）");

    /**
     * The column <code>creation.creation_ai_talk_actor.creator</code>. 创建者
     */
    public final TableField<AiTalkActorRecord, JSONB> CREATOR = createField(DSL.name("creator"), SQLDataType.JSONB.nullable(false), this, "创建者");

    private AiTalkActorTable(Name alias, Table<AiTalkActorRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private AiTalkActorTable(Name alias, Table<AiTalkActorRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("AI Talk 演讲者"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_ai_talk_actor</code> table
     * reference
     */
    public AiTalkActorTable(String alias) {
        this(DSL.name(alias), CREATION_AI_TALK_ACTOR);
    }

    /**
     * Create an aliased <code>creation.creation_ai_talk_actor</code> table
     * reference
     */
    public AiTalkActorTable(Name alias) {
        this(alias, CREATION_AI_TALK_ACTOR);
    }

    /**
     * Create a <code>creation.creation_ai_talk_actor</code> table reference
     */
    public AiTalkActorTable() {
        this(DSL.name("creation_ai_talk_actor"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<AiTalkActorRecord> getPrimaryKey() {
        return Keys.CREATION_AI_TALK_ACTOR_PKEY;
    }

    @Override
    public List<UniqueKey<AiTalkActorRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CREATION_AI_TALK_ACTOR_UID_IMAGE_ID_KEY);
    }

    @Override
    public AiTalkActorTable as(String alias) {
        return new AiTalkActorTable(DSL.name(alias), this);
    }

    @Override
    public AiTalkActorTable as(Name alias) {
        return new AiTalkActorTable(alias, this);
    }

    @Override
    public AiTalkActorTable as(Table<?> alias) {
        return new AiTalkActorTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public AiTalkActorTable rename(String name) {
        return new AiTalkActorTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public AiTalkActorTable rename(Name name) {
        return new AiTalkActorTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public AiTalkActorTable rename(Table<?> name) {
        return new AiTalkActorTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTalkActorTable where(Condition condition) {
        return new AiTalkActorTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTalkActorTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTalkActorTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTalkActorTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiTalkActorTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiTalkActorTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiTalkActorTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiTalkActorTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTalkActorTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTalkActorTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
