/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.ScenarioPromptRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 场景提示词
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class ScenarioPromptTable extends TableImpl<ScenarioPromptRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_scenario_prompt</code>
     */
    public static final ScenarioPromptTable CREATION_SCENARIO_PROMPT = new ScenarioPromptTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ScenarioPromptRecord> getRecordType() {
        return ScenarioPromptRecord.class;
    }

    /**
     * The column <code>creation.creation_scenario_prompt.id</code>. 主键
     */
    public final TableField<ScenarioPromptRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_scenario_prompt.created_at</code>.
     * 创建时间
     */
    public final TableField<ScenarioPromptRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_scenario_prompt.updated_at</code>.
     * 更新时间
     */
    public final TableField<ScenarioPromptRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column
     * <code>creation.creation_scenario_prompt.category_biz_source</code>.
     * 业务来源MAGICAL_BRUSH/INTELLIJ_WRITER
     */
    public final TableField<ScenarioPromptRecord, String> CATEGORY_BIZ_SOURCE = createField(DSL.name("category_biz_source"), SQLDataType.VARCHAR(64).nullable(false), this, "业务来源MAGICAL_BRUSH/INTELLIJ_WRITER");

    /**
     * The column <code>creation.creation_scenario_prompt.category_code</code>.
     * 分类码:E-COM/NEW-MEDIA
     */
    public final TableField<ScenarioPromptRecord, String> CATEGORY_CODE = createField(DSL.name("category_code"), SQLDataType.VARCHAR(64).nullable(false), this, "分类码:E-COM/NEW-MEDIA");

    /**
     * The column <code>creation.creation_scenario_prompt.category_name</code>.
     * 分类名:电商，新媒体
     */
    public final TableField<ScenarioPromptRecord, String> CATEGORY_NAME = createField(DSL.name("category_name"), SQLDataType.VARCHAR(64).nullable(false), this, "分类名:电商，新媒体");

    /**
     * The column <code>creation.creation_scenario_prompt.scenario_code</code>.
     * 场景码
     */
    public final TableField<ScenarioPromptRecord, String> SCENARIO_CODE = createField(DSL.name("scenario_code"), SQLDataType.VARCHAR(64).nullable(false), this, "场景码");

    /**
     * The column <code>creation.creation_scenario_prompt.scenario_name</code>.
     * 场景名字
     */
    public final TableField<ScenarioPromptRecord, String> SCENARIO_NAME = createField(DSL.name("scenario_name"), SQLDataType.VARCHAR(255).nullable(false), this, "场景名字");

    /**
     * The column <code>creation.creation_scenario_prompt.description</code>.
     * 场景描述
     */
    public final TableField<ScenarioPromptRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(255).nullable(false), this, "场景描述");

    /**
     * The column <code>creation.creation_scenario_prompt.prompt</code>. 提示词
     */
    public final TableField<ScenarioPromptRecord, String> PROMPT = createField(DSL.name("prompt"), SQLDataType.CLOB.nullable(false), this, "提示词");

    /**
     * The column <code>creation.creation_scenario_prompt.status</code>. 提示词状态
     */
    public final TableField<ScenarioPromptRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(8).nullable(false), this, "提示词状态");

    /**
     * The column <code>creation.creation_scenario_prompt.ext_info</code>. 扩展信息
     */
    public final TableField<ScenarioPromptRecord, JSONB> EXT_INFO = createField(DSL.name("ext_info"), SQLDataType.JSONB, this, "扩展信息");

    /**
     * The column <code>creation.creation_scenario_prompt.creator</code>. 创建者信息
     */
    public final TableField<ScenarioPromptRecord, JSONB> CREATOR = createField(DSL.name("creator"), SQLDataType.JSONB.nullable(false), this, "创建者信息");

    /**
     * The column <code>creation.creation_scenario_prompt.tags</code>. 标签
     */
    public final TableField<ScenarioPromptRecord, JSONB[]> TAGS = createField(DSL.name("tags"), SQLDataType.JSONB.array(), this, "标签");

    private ScenarioPromptTable(Name alias, Table<ScenarioPromptRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private ScenarioPromptTable(Name alias, Table<ScenarioPromptRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("场景提示词"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_scenario_prompt</code> table
     * reference
     */
    public ScenarioPromptTable(String alias) {
        this(DSL.name(alias), CREATION_SCENARIO_PROMPT);
    }

    /**
     * Create an aliased <code>creation.creation_scenario_prompt</code> table
     * reference
     */
    public ScenarioPromptTable(Name alias) {
        this(alias, CREATION_SCENARIO_PROMPT);
    }

    /**
     * Create a <code>creation.creation_scenario_prompt</code> table reference
     */
    public ScenarioPromptTable() {
        this(DSL.name("creation_scenario_prompt"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<ScenarioPromptRecord> getPrimaryKey() {
        return Keys.CREATION_SCENARIO_PROMPT_PKEY;
    }

    @Override
    public List<UniqueKey<ScenarioPromptRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CREATION_SCENARIO_PROMPT_SCENARIO_CODE_KEY);
    }

    @Override
    public ScenarioPromptTable as(String alias) {
        return new ScenarioPromptTable(DSL.name(alias), this);
    }

    @Override
    public ScenarioPromptTable as(Name alias) {
        return new ScenarioPromptTable(alias, this);
    }

    @Override
    public ScenarioPromptTable as(Table<?> alias) {
        return new ScenarioPromptTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ScenarioPromptTable rename(String name) {
        return new ScenarioPromptTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ScenarioPromptTable rename(Name name) {
        return new ScenarioPromptTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ScenarioPromptTable rename(Table<?> name) {
        return new ScenarioPromptTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ScenarioPromptTable where(Condition condition) {
        return new ScenarioPromptTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ScenarioPromptTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ScenarioPromptTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ScenarioPromptTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ScenarioPromptTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ScenarioPromptTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ScenarioPromptTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ScenarioPromptTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ScenarioPromptTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ScenarioPromptTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
