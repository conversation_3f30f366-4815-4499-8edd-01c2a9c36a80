/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.UserMaterialTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserMaterial;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户素材中心
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserMaterialRecord extends UpdatableRecordImpl<UserMaterialRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_user_material.id</code>. 素材主键
     */
    public UserMaterialRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.id</code>. 素材主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_user_material.created_at</code>. 创建时间
     */
    public UserMaterialRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_user_material.updated_at</code>. 更新时间
     */
    public UserMaterialRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_user_material.uid</code>. 用户id
     */
    public UserMaterialRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.uid</code>. 用户id
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_user_material.material_id</code>.
     * 素材主表id
     */
    public UserMaterialRecord setMaterialId(Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.material_id</code>.
     * 素材主表id
     */
    public Long getMaterialId() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.creation_user_material.file_id</code>. 文件id
     */
    public UserMaterialRecord setFileId(Long value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.file_id</code>. 文件id
     */
    public Long getFileId() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>creation.creation_user_material.file_url</code>.
     * 文件地址oss://
     */
    public UserMaterialRecord setFileUrl(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.file_url</code>.
     * 文件地址oss://
     */
    public String getFileUrl() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserMaterialRecord
     */
    public UserMaterialRecord() {
        super(UserMaterialTable.CREATION_USER_MATERIAL);
    }

    /**
     * Create a detached, initialised UserMaterialRecord
     */
    public UserMaterialRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, Long materialId, Long fileId, String fileUrl) {
        super(UserMaterialTable.CREATION_USER_MATERIAL);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setMaterialId(materialId);
        setFileId(fileId);
        setFileUrl(fileUrl);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserMaterialRecord
     */
    public UserMaterialRecord(CreationUserMaterial value) {
        super(UserMaterialTable.CREATION_USER_MATERIAL);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setMaterialId(value.getMaterialId());
            setFileId(value.getFileId());
            setFileUrl(value.getFileUrl());
            resetChangedOnNotNull();
        }
    }
}
