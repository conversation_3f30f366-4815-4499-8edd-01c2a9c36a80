/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.UserVoiceRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 声音演员
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserVoiceTable extends TableImpl<UserVoiceRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_user_voice</code>
     */
    public static final UserVoiceTable CREATION_USER_VOICE = new UserVoiceTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserVoiceRecord> getRecordType() {
        return UserVoiceRecord.class;
    }

    /**
     * The column <code>creation.creation_user_voice.id</code>. 主键
     */
    public final TableField<UserVoiceRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_user_voice.created_at</code>. 创建时间
     */
    public final TableField<UserVoiceRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_user_voice.updated_at</code>. 更新时间
     */
    public final TableField<UserVoiceRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_user_voice.uid</code>. 用户ID
     */
    public final TableField<UserVoiceRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户ID");

    /**
     * The column <code>creation.creation_user_voice.code</code>. 声音编号
     */
    public final TableField<UserVoiceRecord, String> CODE = createField(DSL.name("code"), SQLDataType.VARCHAR(128).nullable(false), this, "声音编号");

    /**
     * The column <code>creation.creation_user_voice.provider_type</code>. 声音提供方
     */
    public final TableField<UserVoiceRecord, String> PROVIDER_TYPE = createField(DSL.name("provider_type"), SQLDataType.VARCHAR(32).nullable(false), this, "声音提供方");

    /**
     * The column <code>creation.creation_user_voice.category</code>. 分类
     */
    public final TableField<UserVoiceRecord, String> CATEGORY = createField(DSL.name("category"), SQLDataType.VARCHAR(32).nullable(false), this, "分类");

    /**
     * The column <code>creation.creation_user_voice.status</code>. 状态
     */
    public final TableField<UserVoiceRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(16).nullable(false), this, "状态");

    /**
     * The column <code>creation.creation_user_voice.en_name</code>. 英文名称
     */
    public final TableField<UserVoiceRecord, String> EN_NAME = createField(DSL.name("en_name"), SQLDataType.VARCHAR(64).nullable(false), this, "英文名称");

    /**
     * The column <code>creation.creation_user_voice.cn_name</code>. 中文名称
     */
    public final TableField<UserVoiceRecord, String> CN_NAME = createField(DSL.name("cn_name"), SQLDataType.VARCHAR(64).nullable(false), this, "中文名称");

    /**
     * The column <code>creation.creation_user_voice.gender</code>. 性别
     */
    public final TableField<UserVoiceRecord, String> GENDER = createField(DSL.name("gender"), SQLDataType.VARCHAR(16).nullable(false), this, "性别");

    /**
     * The column <code>creation.creation_user_voice.avatar</code>. 头像
     */
    public final TableField<UserVoiceRecord, String> AVATAR = createField(DSL.name("avatar"), SQLDataType.VARCHAR(200).nullable(false), this, "头像");

    /**
     * The column <code>creation.creation_user_voice.ordinal</code>. 排序字段
     */
    public final TableField<UserVoiceRecord, Integer> ORDINAL = createField(DSL.name("ordinal"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.field(DSL.raw("0"), SQLDataType.INTEGER)), this, "排序字段");

    /**
     * The column <code>creation.creation_user_voice.age_level</code>. 声音年龄阶层
     */
    public final TableField<UserVoiceRecord, String> AGE_LEVEL = createField(DSL.name("age_level"), SQLDataType.VARCHAR(32).nullable(false).defaultValue(DSL.field(DSL.raw("'ADULT'::character varying"), SQLDataType.VARCHAR)), this, "声音年龄阶层");

    /**
     * The column <code>creation.creation_user_voice.labels</code>. 声音标签列表
     */
    public final TableField<UserVoiceRecord, JSONB> LABELS = createField(DSL.name("labels"), SQLDataType.JSONB.nullable(false).defaultValue(DSL.field(DSL.raw("'[]'::jsonb"), SQLDataType.JSONB)), this, "声音标签列表");

    private UserVoiceTable(Name alias, Table<UserVoiceRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private UserVoiceTable(Name alias, Table<UserVoiceRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("声音演员"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_user_voice</code> table
     * reference
     */
    public UserVoiceTable(String alias) {
        this(DSL.name(alias), CREATION_USER_VOICE);
    }

    /**
     * Create an aliased <code>creation.creation_user_voice</code> table
     * reference
     */
    public UserVoiceTable(Name alias) {
        this(alias, CREATION_USER_VOICE);
    }

    /**
     * Create a <code>creation.creation_user_voice</code> table reference
     */
    public UserVoiceTable() {
        this(DSL.name("creation_user_voice"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<UserVoiceRecord> getPrimaryKey() {
        return Keys.CREATION_USER_VOICE_PKEY;
    }

    @Override
    public List<UniqueKey<UserVoiceRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CREATION_USER_VOICE_UID_CODE_KEY);
    }

    @Override
    public UserVoiceTable as(String alias) {
        return new UserVoiceTable(DSL.name(alias), this);
    }

    @Override
    public UserVoiceTable as(Name alias) {
        return new UserVoiceTable(alias, this);
    }

    @Override
    public UserVoiceTable as(Table<?> alias) {
        return new UserVoiceTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserVoiceTable rename(String name) {
        return new UserVoiceTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserVoiceTable rename(Name name) {
        return new UserVoiceTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserVoiceTable rename(Table<?> name) {
        return new UserVoiceTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserVoiceTable where(Condition condition) {
        return new UserVoiceTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserVoiceTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserVoiceTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserVoiceTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserVoiceTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserVoiceTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserVoiceTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserVoiceTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserVoiceTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserVoiceTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
