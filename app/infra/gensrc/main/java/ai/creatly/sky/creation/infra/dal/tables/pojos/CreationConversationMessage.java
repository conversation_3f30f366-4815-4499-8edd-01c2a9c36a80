/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 会话消息
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationConversationMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private Long conversationId;
    private JSONB role;
    private JSONB content;

    public CreationConversationMessage() {}

    public CreationConversationMessage(CreationConversationMessage value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.conversationId = value.conversationId;
        this.role = value.role;
        this.content = value.content;
    }

    public CreationConversationMessage(
        @Nullable Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        Long conversationId,
        JSONB role,
        JSONB content
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.conversationId = conversationId;
        this.role = role;
        this.content = content;
    }

    /**
     * Getter for <code>creation.creation_conversation_message.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_conversation_message.id</code>. 主键
     */
    public CreationConversationMessage setId(@Nullable Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_conversation_message.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for
     * <code>creation.creation_conversation_message.created_at</code>. 创建时间
     */
    public CreationConversationMessage setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_conversation_message.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for
     * <code>creation.creation_conversation_message.updated_at</code>. 更新时间
     */
    public CreationConversationMessage setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_message.uid</code>. 用户ID
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_conversation_message.uid</code>. 用户ID
     */
    public CreationConversationMessage setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_conversation_message.conversation_id</code>. 会话ID
     */
    public Long getConversationId() {
        return this.conversationId;
    }

    /**
     * Setter for
     * <code>creation.creation_conversation_message.conversation_id</code>. 会话ID
     */
    public CreationConversationMessage setConversationId(Long conversationId) {
        this.conversationId = conversationId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_message.role</code>. 角色
     */
    public JSONB getRole() {
        return this.role;
    }

    /**
     * Setter for <code>creation.creation_conversation_message.role</code>. 角色
     */
    public CreationConversationMessage setRole(JSONB role) {
        this.role = role;
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_message.content</code>.
     * 内容
     */
    public JSONB getContent() {
        return this.content;
    }

    /**
     * Setter for <code>creation.creation_conversation_message.content</code>.
     * 内容
     */
    public CreationConversationMessage setContent(JSONB content) {
        this.content = content;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationConversationMessage other = (CreationConversationMessage) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.conversationId == null) {
            if (other.conversationId != null)
                return false;
        }
        else if (!this.conversationId.equals(other.conversationId))
            return false;
        if (this.role == null) {
            if (other.role != null)
                return false;
        }
        else if (!this.role.equals(other.role))
            return false;
        if (this.content == null) {
            if (other.content != null)
                return false;
        }
        else if (!this.content.equals(other.content))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.conversationId == null) ? 0 : this.conversationId.hashCode());
        result = prime * result + ((this.role == null) ? 0 : this.role.hashCode());
        result = prime * result + ((this.content == null) ? 0 : this.content.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationConversationMessage (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(conversationId);
        sb.append(", ").append(role);
        sb.append(", ").append(content);

        sb.append(")");
        return sb.toString();
    }
}
