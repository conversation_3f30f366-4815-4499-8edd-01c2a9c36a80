/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.UserInvitationRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 用户反馈
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserInvitationTable extends TableImpl<UserInvitationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_user_invitation</code>
     */
    public static final UserInvitationTable CREATION_USER_INVITATION = new UserInvitationTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserInvitationRecord> getRecordType() {
        return UserInvitationRecord.class;
    }

    /**
     * The column <code>creation.creation_user_invitation.id</code>. 主键
     */
    public final TableField<UserInvitationRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键");

    /**
     * The column <code>creation.creation_user_invitation.created_at</code>.
     * 创建时间
     */
    public final TableField<UserInvitationRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_user_invitation.updated_at</code>.
     * 更新时间
     */
    public final TableField<UserInvitationRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_user_invitation.inviter_id</code>.
     * 邀请人用户ID
     */
    public final TableField<UserInvitationRecord, Long> INVITER_ID = createField(DSL.name("inviter_id"), SQLDataType.BIGINT.nullable(false), this, "邀请人用户ID");

    /**
     * The column <code>creation.creation_user_invitation.inviter_name</code>.
     * 邀请人用户名
     */
    public final TableField<UserInvitationRecord, String> INVITER_NAME = createField(DSL.name("inviter_name"), SQLDataType.VARCHAR(128).nullable(false), this, "邀请人用户名");

    /**
     * The column <code>creation.creation_user_invitation.invitee_id</code>.
     * 被邀请人用户ID
     */
    public final TableField<UserInvitationRecord, Long> INVITEE_ID = createField(DSL.name("invitee_id"), SQLDataType.BIGINT.nullable(false), this, "被邀请人用户ID");

    /**
     * The column <code>creation.creation_user_invitation.invitee_name</code>.
     * 被邀请人用户名
     */
    public final TableField<UserInvitationRecord, String> INVITEE_NAME = createField(DSL.name("invitee_name"), SQLDataType.VARCHAR(128).nullable(false), this, "被邀请人用户名");

    /**
     * The column <code>creation.creation_user_invitation.invite_code</code>.
     * 邀请码
     */
    public final TableField<UserInvitationRecord, String> INVITE_CODE = createField(DSL.name("invite_code"), SQLDataType.VARCHAR(128).nullable(false), this, "邀请码");

    /**
     * The column
     * <code>creation.creation_user_invitation.awarded_credits</code>. 邀请人被奖励的元气
     */
    public final TableField<UserInvitationRecord, Integer> AWARDED_CREDITS = createField(DSL.name("awarded_credits"), SQLDataType.INTEGER.nullable(false), this, "邀请人被奖励的元气");

    private UserInvitationTable(Name alias, Table<UserInvitationRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private UserInvitationTable(Name alias, Table<UserInvitationRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("用户反馈"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_user_invitation</code> table
     * reference
     */
    public UserInvitationTable(String alias) {
        this(DSL.name(alias), CREATION_USER_INVITATION);
    }

    /**
     * Create an aliased <code>creation.creation_user_invitation</code> table
     * reference
     */
    public UserInvitationTable(Name alias) {
        this(alias, CREATION_USER_INVITATION);
    }

    /**
     * Create a <code>creation.creation_user_invitation</code> table reference
     */
    public UserInvitationTable() {
        this(DSL.name("creation_user_invitation"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public Identity<UserInvitationRecord, Long> getIdentity() {
        return (Identity<UserInvitationRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<UserInvitationRecord> getPrimaryKey() {
        return Keys.CREATION_USER_INVITATION_PKEY;
    }

    @Override
    public UserInvitationTable as(String alias) {
        return new UserInvitationTable(DSL.name(alias), this);
    }

    @Override
    public UserInvitationTable as(Name alias) {
        return new UserInvitationTable(alias, this);
    }

    @Override
    public UserInvitationTable as(Table<?> alias) {
        return new UserInvitationTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserInvitationTable rename(String name) {
        return new UserInvitationTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserInvitationTable rename(Name name) {
        return new UserInvitationTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserInvitationTable rename(Table<?> name) {
        return new UserInvitationTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserInvitationTable where(Condition condition) {
        return new UserInvitationTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserInvitationTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserInvitationTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserInvitationTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserInvitationTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserInvitationTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserInvitationTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserInvitationTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserInvitationTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserInvitationTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
