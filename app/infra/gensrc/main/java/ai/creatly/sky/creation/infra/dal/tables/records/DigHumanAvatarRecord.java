/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.DigHumanAvatarTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.DigHumanAvatar;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 数字形象表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class DigHumanAvatarRecord extends UpdatableRecordImpl<DigHumanAvatarRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.dig_human_avatar.id</code>. 主键ID
     */
    public DigHumanAvatarRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.id</code>. 主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.dig_human_avatar.created_at</code>. 创建时间
     */
    public DigHumanAvatarRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.dig_human_avatar.updated_at</code>. 更新时间
     */
    public DigHumanAvatarRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.dig_human_avatar.uid</code>. 用户id
     */
    public DigHumanAvatarRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.uid</code>. 用户id
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.dig_human_avatar.name</code>. 形象名
     */
    public DigHumanAvatarRecord setName(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.name</code>. 形象名
     */
    public String getName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.dig_human_avatar.gender</code>. 形象性别
     */
    public DigHumanAvatarRecord setGender(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.gender</code>. 形象性别
     */
    public String getGender() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.dig_human_avatar.ext_out_id</code>. 形象外部标识id
     */
    public DigHumanAvatarRecord setExtOutId(@Nullable String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.ext_out_id</code>. 形象外部标识id
     */
    @Nullable
    public String getExtOutId() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.dig_human_avatar.avatar_file</code>. 形象文件id
     */
    public DigHumanAvatarRecord setAvatarFile(@Nullable JSONB value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.avatar_file</code>. 形象文件id
     */
    @Nullable
    public JSONB getAvatarFile() {
        return (JSONB) get(7);
    }

    /**
     * Setter for <code>creation.dig_human_avatar.status</code>. 状态
     */
    public DigHumanAvatarRecord setStatus(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.status</code>. 状态
     */
    public String getStatus() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.dig_human_avatar.cover_file</code>. 演讲者封面
     */
    public DigHumanAvatarRecord setCoverFile(@Nullable JSONB value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.cover_file</code>. 演讲者封面
     */
    @Nullable
    public JSONB getCoverFile() {
        return (JSONB) get(9);
    }

    /**
     * Setter for <code>creation.dig_human_avatar.video_width</code>.
     * 数字人视频宽度（px）
     */
    public DigHumanAvatarRecord setVideoWidth(@Nullable Integer value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.video_width</code>.
     * 数字人视频宽度（px）
     */
    @Nullable
    public Integer getVideoWidth() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>creation.dig_human_avatar.video_height</code>.
     * 数字人视频高度（px）
     */
    public DigHumanAvatarRecord setVideoHeight(@Nullable Integer value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.video_height</code>.
     * 数字人视频高度（px）
     */
    @Nullable
    public Integer getVideoHeight() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>creation.dig_human_avatar.aspect_ratio</code>. 形象宽高比
     */
    public DigHumanAvatarRecord setAspectRatio(@Nullable String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.aspect_ratio</code>. 形象宽高比
     */
    @Nullable
    public String getAspectRatio() {
        return (String) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DigHumanAvatarRecord
     */
    public DigHumanAvatarRecord() {
        super(DigHumanAvatarTable.DIG_HUMAN_AVATAR);
    }

    /**
     * Create a detached, initialised DigHumanAvatarRecord
     */
    public DigHumanAvatarRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String name, String gender, @Nullable String extOutId, @Nullable JSONB avatarFile, String status, @Nullable JSONB coverFile, @Nullable Integer videoWidth, @Nullable Integer videoHeight, @Nullable String aspectRatio) {
        super(DigHumanAvatarTable.DIG_HUMAN_AVATAR);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setName(name);
        setGender(gender);
        setExtOutId(extOutId);
        setAvatarFile(avatarFile);
        setStatus(status);
        setCoverFile(coverFile);
        setVideoWidth(videoWidth);
        setVideoHeight(videoHeight);
        setAspectRatio(aspectRatio);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised DigHumanAvatarRecord
     */
    public DigHumanAvatarRecord(DigHumanAvatar value) {
        super(DigHumanAvatarTable.DIG_HUMAN_AVATAR);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setName(value.getName());
            setGender(value.getGender());
            setExtOutId(value.getExtOutId());
            setAvatarFile(value.getAvatarFile());
            setStatus(value.getStatus());
            setCoverFile(value.getCoverFile());
            setVideoWidth(value.getVideoWidth());
            setVideoHeight(value.getVideoHeight());
            setAspectRatio(value.getAspectRatio());
            resetChangedOnNotNull();
        }
    }
}
