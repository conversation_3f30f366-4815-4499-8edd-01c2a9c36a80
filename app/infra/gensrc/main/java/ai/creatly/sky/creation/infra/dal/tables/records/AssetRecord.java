/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.AssetTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAsset;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 作品
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AssetRecord extends UpdatableRecordImpl<AssetRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_asset.id</code>. 主键
     */
    public AssetRecord setId(@Nullable Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_asset.created_at</code>. 创建时间
     */
    public AssetRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_asset.updated_at</code>. 更新时间
     */
    public AssetRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_asset.uid</code>. 用户ID
     */
    public AssetRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.uid</code>. 用户ID
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_asset.status</code>. 状态（无效即为软删除）
     */
    public AssetRecord setStatus(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.status</code>. 状态（无效即为软删除）
     */
    public String getStatus() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_asset.source_type</code>. 来源类型
     */
    public AssetRecord setSourceType(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.source_type</code>. 来源类型
     */
    public String getSourceType() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_asset.biz_type</code>. 业务类型
     */
    public AssetRecord setBizType(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.biz_type</code>. 业务类型
     */
    public String getBizType() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_asset.cover_url</code>. 封面图
     */
    public AssetRecord setCoverUrl(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.cover_url</code>. 封面图
     */
    public String getCoverUrl() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_asset.file</code>. 文件
     */
    public AssetRecord setFile(JSONB value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.file</code>. 文件
     */
    public JSONB getFile() {
        return (JSONB) get(8);
    }

    /**
     * Setter for <code>creation.creation_asset.content</code>. 内容
     */
    public AssetRecord setContent(JSONB value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.content</code>. 内容
     */
    public JSONB getContent() {
        return (JSONB) get(9);
    }

    /**
     * Setter for <code>creation.creation_asset.metadata</code>. 元数据
     */
    public AssetRecord setMetadata(JSONB value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.metadata</code>. 元数据
     */
    public JSONB getMetadata() {
        return (JSONB) get(10);
    }

    /**
     * Setter for <code>creation.creation_asset.tags</code>. 标签列表
     */
    public AssetRecord setTags(String[] value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.tags</code>. 标签列表
     */
    public String[] getTags() {
        return (String[]) get(11);
    }

    /**
     * Setter for <code>creation.creation_asset.name</code>. 作品名称
     */
    public AssetRecord setName(@Nullable String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.name</code>. 作品名称
     */
    @Nullable
    public String getName() {
        return (String) get(12);
    }

    /**
     * Setter for <code>creation.creation_asset.org_code</code>. 组织代码
     */
    public AssetRecord setOrgCode(@Nullable String value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_asset.org_code</code>. 组织代码
     */
    @Nullable
    public String getOrgCode() {
        return (String) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AssetRecord
     */
    public AssetRecord() {
        super(AssetTable.CREATION_ASSET);
    }

    /**
     * Create a detached, initialised AssetRecord
     */
    public AssetRecord(@Nullable Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String status, String sourceType, String bizType, String coverUrl, JSONB file, JSONB content, JSONB metadata, String[] tags, @Nullable String name, @Nullable String orgCode) {
        super(AssetTable.CREATION_ASSET);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setStatus(status);
        setSourceType(sourceType);
        setBizType(bizType);
        setCoverUrl(coverUrl);
        setFile(file);
        setContent(content);
        setMetadata(metadata);
        setTags(tags);
        setName(name);
        setOrgCode(orgCode);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised AssetRecord
     */
    public AssetRecord(CreationAsset value) {
        super(AssetTable.CREATION_ASSET);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setStatus(value.getStatus());
            setSourceType(value.getSourceType());
            setBizType(value.getBizType());
            setCoverUrl(value.getCoverUrl());
            setFile(value.getFile());
            setContent(value.getContent());
            setMetadata(value.getMetadata());
            setTags(value.getTags());
            setName(value.getName());
            setOrgCode(value.getOrgCode());
            resetChangedOnNotNull();
        }
    }
}
