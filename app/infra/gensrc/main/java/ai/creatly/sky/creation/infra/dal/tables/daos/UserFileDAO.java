/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.UserFileTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserFile;
import ai.creatly.sky.creation.infra.dal.tables.records.UserFileRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.JSONB;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 用户文件
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class UserFileDAO extends DAOImpl<UserFileRecord, CreationUserFile, Long> {

    /**
     * Create a new UserFileDAO without any configuration
     */
    public UserFileDAO() {
        super(UserFileTable.CREATION_USER_FILE, CreationUserFile.class);
    }

    /**
     * Create a new UserFileDAO with an attached configuration
     */
    @Autowired
    public UserFileDAO(Configuration configuration) {
        super(UserFileTable.CREATION_USER_FILE, CreationUserFile.class, configuration);
    }

    @Override
    public Long getId(CreationUserFile object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationUserFile> fetchById(Long... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationUserFile fetchOneById(Long value) {
        return fetchOne(UserFileTable.CREATION_USER_FILE.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationUserFile> fetchOptionalById(Long value) {
        return fetchOptional(UserFileTable.CREATION_USER_FILE.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationUserFile> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationUserFile> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<CreationUserFile> fetchByUid(Long... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.UID, values);
    }

    /**
     * Fetch records that have <code>biz_source BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfBizSource(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.BIZ_SOURCE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_source IN (values)</code>
     */
    public List<CreationUserFile> fetchByBizSource(String... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.BIZ_SOURCE, values);
    }

    /**
     * Fetch records that have <code>original_filename BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfOriginalFilename(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.ORIGINAL_FILENAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>original_filename IN (values)</code>
     */
    public List<CreationUserFile> fetchByOriginalFilename(String... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.ORIGINAL_FILENAME, values);
    }

    /**
     * Fetch records that have <code>extension BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfExtension(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.EXTENSION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>extension IN (values)</code>
     */
    public List<CreationUserFile> fetchByExtension(String... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.EXTENSION, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfType(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<CreationUserFile> fetchByType(String... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.TYPE, values);
    }

    /**
     * Fetch records that have <code>storage_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfStorageType(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.STORAGE_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>storage_type IN (values)</code>
     */
    public List<CreationUserFile> fetchByStorageType(String... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.STORAGE_TYPE, values);
    }

    /**
     * Fetch records that have <code>bucket BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfBucket(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.BUCKET, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>bucket IN (values)</code>
     */
    public List<CreationUserFile> fetchByBucket(String... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.BUCKET, values);
    }

    /**
     * Fetch records that have <code>key BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfKey(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.KEY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>key IN (values)</code>
     */
    public List<CreationUserFile> fetchByKey(String... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.KEY, values);
    }

    /**
     * Fetch records that have <code>acl BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfAcl(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.ACL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>acl IN (values)</code>
     */
    public List<CreationUserFile> fetchByAcl(String... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.ACL, values);
    }

    /**
     * Fetch records that have <code>created_date BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfCreatedDate(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.CREATED_DATE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_date IN (values)</code>
     */
    public List<CreationUserFile> fetchByCreatedDate(Integer... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.CREATED_DATE, values);
    }

    /**
     * Fetch records that have <code>created_month BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfCreatedMonth(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.CREATED_MONTH, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_month IN (values)</code>
     */
    public List<CreationUserFile> fetchByCreatedMonth(Integer... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.CREATED_MONTH, values);
    }

    /**
     * Fetch records that have <code>creator BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfCreator(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.CREATOR, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creator IN (values)</code>
     */
    public List<CreationUserFile> fetchByCreator(JSONB... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.CREATOR, values);
    }

    /**
     * Fetch records that have <code>metadata BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfMetadata(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.METADATA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>metadata IN (values)</code>
     */
    public List<CreationUserFile> fetchByMetadata(JSONB... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.METADATA, values);
    }

    /**
     * Fetch records that have <code>trashed BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfTrashed(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.TRASHED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>trashed IN (values)</code>
     */
    public List<CreationUserFile> fetchByTrashed(Boolean... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.TRASHED, values);
    }

    /**
     * Fetch records that have <code>content BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfContent(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.CONTENT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>content IN (values)</code>
     */
    public List<CreationUserFile> fetchByContent(JSONB... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.CONTENT, values);
    }

    /**
     * Fetch records that have <code>md5 BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserFile> fetchRangeOfMd5(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserFileTable.CREATION_USER_FILE.MD5, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>md5 IN (values)</code>
     */
    public List<CreationUserFile> fetchByMd5(String... values) {
        return fetch(UserFileTable.CREATION_USER_FILE.MD5, values);
    }
}
