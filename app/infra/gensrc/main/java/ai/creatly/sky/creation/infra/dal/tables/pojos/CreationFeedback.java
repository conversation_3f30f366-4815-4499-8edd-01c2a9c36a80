/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 用户反馈
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationFeedback implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String status;
    private Long uid;
    private String username;
    private String phone;
    private String email;
    private String title;
    private String detail;
    private JSONB attachments;

    public CreationFeedback() {}

    public CreationFeedback(CreationFeedback value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.status = value.status;
        this.uid = value.uid;
        this.username = value.username;
        this.phone = value.phone;
        this.email = value.email;
        this.title = value.title;
        this.detail = value.detail;
        this.attachments = value.attachments;
    }

    public CreationFeedback(
        @Nullable Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        @Nullable String status,
        Long uid,
        String username,
        String phone,
        @Nullable String email,
        String title,
        @Nullable String detail,
        JSONB attachments
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.status = status;
        this.uid = uid;
        this.username = username;
        this.phone = phone;
        this.email = email;
        this.title = title;
        this.detail = detail;
        this.attachments = attachments;
    }

    /**
     * Getter for <code>creation.creation_feedback.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_feedback.id</code>. 主键
     */
    public CreationFeedback setId(@Nullable Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_feedback.created_at</code>. 创建时间
     */
    public CreationFeedback setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_feedback.updated_at</code>. 更新时间
     */
    public CreationFeedback setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.status</code>. 状态
     */
    @Nullable
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_feedback.status</code>. 状态
     */
    public CreationFeedback setStatus(@Nullable String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.uid</code>. 用户ID
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_feedback.uid</code>. 用户ID
     */
    public CreationFeedback setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.username</code>. 用户名称
     */
    public String getUsername() {
        return this.username;
    }

    /**
     * Setter for <code>creation.creation_feedback.username</code>. 用户名称
     */
    public CreationFeedback setUsername(String username) {
        this.username = username;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.phone</code>. 手机号
     */
    public String getPhone() {
        return this.phone;
    }

    /**
     * Setter for <code>creation.creation_feedback.phone</code>. 手机号
     */
    public CreationFeedback setPhone(String phone) {
        this.phone = phone;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.email</code>. 邮箱
     */
    @Nullable
    public String getEmail() {
        return this.email;
    }

    /**
     * Setter for <code>creation.creation_feedback.email</code>. 邮箱
     */
    public CreationFeedback setEmail(@Nullable String email) {
        this.email = email;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.title</code>. 标题
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * Setter for <code>creation.creation_feedback.title</code>. 标题
     */
    public CreationFeedback setTitle(String title) {
        this.title = title;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.detail</code>. 明细
     */
    @Nullable
    public String getDetail() {
        return this.detail;
    }

    /**
     * Setter for <code>creation.creation_feedback.detail</code>. 明细
     */
    public CreationFeedback setDetail(@Nullable String detail) {
        this.detail = detail;
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.attachments</code>. 附件列表
     */
    public JSONB getAttachments() {
        return this.attachments;
    }

    /**
     * Setter for <code>creation.creation_feedback.attachments</code>. 附件列表
     */
    public CreationFeedback setAttachments(JSONB attachments) {
        this.attachments = attachments;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationFeedback other = (CreationFeedback) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.username == null) {
            if (other.username != null)
                return false;
        }
        else if (!this.username.equals(other.username))
            return false;
        if (this.phone == null) {
            if (other.phone != null)
                return false;
        }
        else if (!this.phone.equals(other.phone))
            return false;
        if (this.email == null) {
            if (other.email != null)
                return false;
        }
        else if (!this.email.equals(other.email))
            return false;
        if (this.title == null) {
            if (other.title != null)
                return false;
        }
        else if (!this.title.equals(other.title))
            return false;
        if (this.detail == null) {
            if (other.detail != null)
                return false;
        }
        else if (!this.detail.equals(other.detail))
            return false;
        if (this.attachments == null) {
            if (other.attachments != null)
                return false;
        }
        else if (!this.attachments.equals(other.attachments))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.username == null) ? 0 : this.username.hashCode());
        result = prime * result + ((this.phone == null) ? 0 : this.phone.hashCode());
        result = prime * result + ((this.email == null) ? 0 : this.email.hashCode());
        result = prime * result + ((this.title == null) ? 0 : this.title.hashCode());
        result = prime * result + ((this.detail == null) ? 0 : this.detail.hashCode());
        result = prime * result + ((this.attachments == null) ? 0 : this.attachments.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationFeedback (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(status);
        sb.append(", ").append(uid);
        sb.append(", ").append(username);
        sb.append(", ").append(phone);
        sb.append(", ").append(email);
        sb.append(", ").append(title);
        sb.append(", ").append(detail);
        sb.append(", ").append(attachments);

        sb.append(")");
        return sb.toString();
    }
}
