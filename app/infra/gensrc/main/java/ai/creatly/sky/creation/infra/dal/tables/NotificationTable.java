/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.NotificationRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 用户反馈
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class NotificationTable extends TableImpl<NotificationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_notification</code>
     */
    public static final NotificationTable CREATION_NOTIFICATION = new NotificationTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<NotificationRecord> getRecordType() {
        return NotificationRecord.class;
    }

    /**
     * The column <code>creation.creation_notification.id</code>. 主键
     */
    public final TableField<NotificationRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键");

    /**
     * The column <code>creation.creation_notification.created_at</code>. 创建时间
     */
    public final TableField<NotificationRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_notification.updated_at</code>. 更新时间
     */
    public final TableField<NotificationRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_notification.status</code>. 通知状态/已读/未读
     */
    public final TableField<NotificationRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(20).nullable(false).defaultValue(DSL.field(DSL.raw("'UNREAD/READ'::character varying"), SQLDataType.VARCHAR)), this, "通知状态/已读/未读");

    /**
     * The column <code>creation.creation_notification.sender</code>. 通知发送者id
     */
    public final TableField<NotificationRecord, Long> SENDER = createField(DSL.name("sender"), SQLDataType.BIGINT.nullable(false), this, "通知发送者id");

    /**
     * The column <code>creation.creation_notification.sender_name</code>. 通知发送者
     */
    public final TableField<NotificationRecord, String> SENDER_NAME = createField(DSL.name("sender_name"), SQLDataType.VARCHAR(64).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "通知发送者");

    /**
     * The column <code>creation.creation_notification.receiver</code>. 接受者id
     */
    public final TableField<NotificationRecord, Long> RECEIVER = createField(DSL.name("receiver"), SQLDataType.BIGINT.nullable(false), this, "接受者id");

    /**
     * The column <code>creation.creation_notification.receiver_name</code>. 接受者
     */
    public final TableField<NotificationRecord, String> RECEIVER_NAME = createField(DSL.name("receiver_name"), SQLDataType.VARCHAR(64).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "接受者");

    /**
     * The column <code>creation.creation_notification.biz_type</code>. 业务类型
     */
    public final TableField<NotificationRecord, String> BIZ_TYPE = createField(DSL.name("biz_type"), SQLDataType.VARCHAR(64).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "业务类型");

    /**
     * The column <code>creation.creation_notification.task_type</code>. 任务类型
     */
    public final TableField<NotificationRecord, String> TASK_TYPE = createField(DSL.name("task_type"), SQLDataType.VARCHAR(64).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "任务类型");

    /**
     * The column <code>creation.creation_notification.task_name</code>. 任务名
     */
    public final TableField<NotificationRecord, String> TASK_NAME = createField(DSL.name("task_name"), SQLDataType.VARCHAR(64).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "任务名");

    /**
     * The column <code>creation.creation_notification.task_id</code>. 任务id
     */
    public final TableField<NotificationRecord, String> TASK_ID = createField(DSL.name("task_id"), SQLDataType.VARCHAR(64).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "任务id");

    /**
     * The column <code>creation.creation_notification.msg_type</code>.
     * 通知类型:系统/业务
     */
    public final TableField<NotificationRecord, String> MSG_TYPE = createField(DSL.name("msg_type"), SQLDataType.VARCHAR(64).nullable(false), this, "通知类型:系统/业务");

    /**
     * The column <code>creation.creation_notification.msg_content</code>. 消息体内容
     */
    public final TableField<NotificationRecord, JSONB> MSG_CONTENT = createField(DSL.name("msg_content"), SQLDataType.JSONB.nullable(false), this, "消息体内容");

    private NotificationTable(Name alias, Table<NotificationRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private NotificationTable(Name alias, Table<NotificationRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("用户反馈"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_notification</code> table
     * reference
     */
    public NotificationTable(String alias) {
        this(DSL.name(alias), CREATION_NOTIFICATION);
    }

    /**
     * Create an aliased <code>creation.creation_notification</code> table
     * reference
     */
    public NotificationTable(Name alias) {
        this(alias, CREATION_NOTIFICATION);
    }

    /**
     * Create a <code>creation.creation_notification</code> table reference
     */
    public NotificationTable() {
        this(DSL.name("creation_notification"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public Identity<NotificationRecord, Long> getIdentity() {
        return (Identity<NotificationRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<NotificationRecord> getPrimaryKey() {
        return Keys.CREATION_NOTIFICATION_PKEY;
    }

    @Override
    public NotificationTable as(String alias) {
        return new NotificationTable(DSL.name(alias), this);
    }

    @Override
    public NotificationTable as(Name alias) {
        return new NotificationTable(alias, this);
    }

    @Override
    public NotificationTable as(Table<?> alias) {
        return new NotificationTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public NotificationTable rename(String name) {
        return new NotificationTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public NotificationTable rename(Name name) {
        return new NotificationTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public NotificationTable rename(Table<?> name) {
        return new NotificationTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public NotificationTable where(Condition condition) {
        return new NotificationTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public NotificationTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public NotificationTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public NotificationTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public NotificationTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public NotificationTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public NotificationTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public NotificationTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public NotificationTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public NotificationTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
