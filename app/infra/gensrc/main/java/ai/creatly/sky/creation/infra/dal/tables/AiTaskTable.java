/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.AiTaskRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * AI创作任务
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiTaskTable extends TableImpl<AiTaskRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_ai_task</code>
     */
    public static final AiTaskTable CREATION_AI_TASK = new AiTaskTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AiTaskRecord> getRecordType() {
        return AiTaskRecord.class;
    }

    /**
     * The column <code>creation.creation_ai_task.id</code>. 主键
     */
    public final TableField<AiTaskRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_ai_task.created_at</code>. 创建时间
     */
    public final TableField<AiTaskRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_task.updated_at</code>. 更新时间
     */
    public final TableField<AiTaskRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_task.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public final TableField<AiTaskRecord, Long> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.BIGINT.nullable(false), this, "任务所有者ID（1表示系统）");

    /**
     * The column <code>creation.creation_ai_task.owner_name</code>. 任务所有者名称
     */
    public final TableField<AiTaskRecord, String> OWNER_NAME = createField(DSL.name("owner_name"), SQLDataType.VARCHAR(128).nullable(false), this, "任务所有者名称");

    /**
     * The column <code>creation.creation_ai_task.task_type</code>. 任务类型
     */
    public final TableField<AiTaskRecord, String> TASK_TYPE = createField(DSL.name("task_type"), SQLDataType.VARCHAR(64).nullable(false), this, "任务类型");

    /**
     * The column <code>creation.creation_ai_task.task_name</code>. 任务名称
     */
    public final TableField<AiTaskRecord, String> TASK_NAME = createField(DSL.name("task_name"), SQLDataType.VARCHAR(128).nullable(false), this, "任务名称");

    /**
     * The column <code>creation.creation_ai_task.status</code>. 状态
     */
    public final TableField<AiTaskRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(16).nullable(false), this, "状态");

    /**
     * The column <code>creation.creation_ai_task.biz_status</code>. 关联外部业务状态
     */
    public final TableField<AiTaskRecord, String> BIZ_STATUS = createField(DSL.name("biz_status"), SQLDataType.VARCHAR(64).nullable(false), this, "关联外部业务状态");

    /**
     * The column <code>creation.creation_ai_task.biz_type</code>. 关联外部业务类型
     */
    public final TableField<AiTaskRecord, String> BIZ_TYPE = createField(DSL.name("biz_type"), SQLDataType.VARCHAR(32).nullable(false), this, "关联外部业务类型");

    /**
     * The column <code>creation.creation_ai_task.biz_no</code>. 关联外部业务单号
     */
    public final TableField<AiTaskRecord, String> BIZ_NO = createField(DSL.name("biz_no"), SQLDataType.VARCHAR(255).nullable(false), this, "关联外部业务单号");

    /**
     * The column <code>creation.creation_ai_task.sub_biz_no</code>. 关联外部业务子单号
     */
    public final TableField<AiTaskRecord, String> SUB_BIZ_NO = createField(DSL.name("sub_biz_no"), SQLDataType.VARCHAR(255).nullable(false), this, "关联外部业务子单号");

    /**
     * The column <code>creation.creation_ai_task.exec_status</code>. 执行状态
     */
    public final TableField<AiTaskRecord, String> EXEC_STATUS = createField(DSL.name("exec_status"), SQLDataType.VARCHAR(20).nullable(false), this, "执行状态");

    /**
     * The column <code>creation.creation_ai_task.auto_exec</code>. 是否自动执行
     */
    public final TableField<AiTaskRecord, Boolean> AUTO_EXEC = createField(DSL.name("auto_exec"), SQLDataType.BOOLEAN.nullable(false), this, "是否自动执行");

    /**
     * The column <code>creation.creation_ai_task.priority</code>. 任务优先级（默认100）
     */
    public final TableField<AiTaskRecord, Integer> PRIORITY = createField(DSL.name("priority"), SQLDataType.INTEGER.nullable(false), this, "任务优先级（默认100）");

    /**
     * The column <code>creation.creation_ai_task.sharding</code>. 任务随机分片号
     */
    public final TableField<AiTaskRecord, Integer> SHARDING = createField(DSL.name("sharding"), SQLDataType.INTEGER.nullable(false), this, "任务随机分片号");

    /**
     * The column <code>creation.creation_ai_task.started_at</code>. 开始运行时间
     */
    public final TableField<AiTaskRecord, ZonedDateTime> STARTED_AT = createField(DSL.name("started_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3), this, "开始运行时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_task.finished_at</code>. 运行完成时间
     */
    public final TableField<AiTaskRecord, ZonedDateTime> FINISHED_AT = createField(DSL.name("finished_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3), this, "运行完成时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_task.shared_users</code>. 附加用户列表
     */
    public final TableField<AiTaskRecord, JSONB[]> SHARED_USERS = createField(DSL.name("shared_users"), SQLDataType.JSONB.nullable(false).array(), this, "附加用户列表");

    /**
     * The column <code>creation.creation_ai_task.biz_params</code>. 业务参数
     */
    public final TableField<AiTaskRecord, JSONB> BIZ_PARAMS = createField(DSL.name("biz_params"), SQLDataType.JSONB.nullable(false), this, "业务参数");

    /**
     * The column <code>creation.creation_ai_task.biz_result</code>. 业务结果
     */
    public final TableField<AiTaskRecord, JSONB> BIZ_RESULT = createField(DSL.name("biz_result"), SQLDataType.JSONB.nullable(false), this, "业务结果");

    /**
     * The column <code>creation.creation_ai_task.sys_params</code>. 系统参数
     */
    public final TableField<AiTaskRecord, JSONB> SYS_PARAMS = createField(DSL.name("sys_params"), SQLDataType.JSONB.nullable(false), this, "系统参数");

    /**
     * The column <code>creation.creation_ai_task.sys_result</code>. 系统结果
     */
    public final TableField<AiTaskRecord, JSONB> SYS_RESULT = createField(DSL.name("sys_result"), SQLDataType.JSONB.nullable(false), this, "系统结果");

    /**
     * The column <code>creation.creation_ai_task.exec_info</code>. 执行过程信息
     */
    public final TableField<AiTaskRecord, JSONB> EXEC_INFO = createField(DSL.name("exec_info"), SQLDataType.JSONB.nullable(false), this, "执行过程信息");

    /**
     * The column <code>creation.creation_ai_task.creator</code>. 创建人
     */
    public final TableField<AiTaskRecord, JSONB> CREATOR = createField(DSL.name("creator"), SQLDataType.JSONB.nullable(false), this, "创建人");

    /**
     * The column <code>creation.creation_ai_task.biz_exec_info</code>. 业务执行过程信息
     */
    public final TableField<AiTaskRecord, JSONB> BIZ_EXEC_INFO = createField(DSL.name("biz_exec_info"), SQLDataType.JSONB.nullable(false).defaultValue(DSL.field(DSL.raw("'{}'::jsonb"), SQLDataType.JSONB)), this, "业务执行过程信息");

    /**
     * The column <code>creation.creation_ai_task.delayed_at</code>. 延迟到指定时间执行
     */
    public final TableField<AiTaskRecord, ZonedDateTime> DELAYED_AT = createField(DSL.name("delayed_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3), this, "延迟到指定时间执行", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_task.org_code</code>. 组织代码
     */
    public final TableField<AiTaskRecord, String> ORG_CODE = createField(DSL.name("org_code"), SQLDataType.VARCHAR(64), this, "组织代码");

    private AiTaskTable(Name alias, Table<AiTaskRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private AiTaskTable(Name alias, Table<AiTaskRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("AI创作任务"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_ai_task</code> table reference
     */
    public AiTaskTable(String alias) {
        this(DSL.name(alias), CREATION_AI_TASK);
    }

    /**
     * Create an aliased <code>creation.creation_ai_task</code> table reference
     */
    public AiTaskTable(Name alias) {
        this(alias, CREATION_AI_TASK);
    }

    /**
     * Create a <code>creation.creation_ai_task</code> table reference
     */
    public AiTaskTable() {
        this(DSL.name("creation_ai_task"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<AiTaskRecord> getPrimaryKey() {
        return Keys.CREATION_AI_TASK_PKEY;
    }

    @Override
    public List<UniqueKey<AiTaskRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CREATION_AI_TASK_TASK_TYPE_BIZ_TYPE_BIZ_NO_SUB_BIZ_NO_KEY);
    }

    @Override
    public AiTaskTable as(String alias) {
        return new AiTaskTable(DSL.name(alias), this);
    }

    @Override
    public AiTaskTable as(Name alias) {
        return new AiTaskTable(alias, this);
    }

    @Override
    public AiTaskTable as(Table<?> alias) {
        return new AiTaskTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public AiTaskTable rename(String name) {
        return new AiTaskTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public AiTaskTable rename(Name name) {
        return new AiTaskTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public AiTaskTable rename(Table<?> name) {
        return new AiTaskTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTaskTable where(Condition condition) {
        return new AiTaskTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTaskTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTaskTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTaskTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiTaskTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiTaskTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiTaskTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiTaskTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTaskTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTaskTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
