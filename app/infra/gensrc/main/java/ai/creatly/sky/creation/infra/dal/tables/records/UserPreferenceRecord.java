/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.UserPreferenceTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.UserPreference;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户配置中心
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserPreferenceRecord extends UpdatableRecordImpl<UserPreferenceRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.user_preference.id</code>. 主键
     */
    public UserPreferenceRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.user_preference.created_at</code>. 创建时间
     */
    public UserPreferenceRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.user_preference.updated_at</code>. 更新时间
     */
    public UserPreferenceRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.user_preference.uid</code>. 用户id
     */
    public UserPreferenceRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.uid</code>. 用户id
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.user_preference.config_key</code>. 配置项
     */
    public UserPreferenceRecord setConfigKey(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.config_key</code>. 配置项
     */
    public String getConfigKey() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.user_preference.config_value</code>. 配置值
     */
    public UserPreferenceRecord setConfigValue(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.config_value</code>. 配置值
     */
    public String getConfigValue() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.user_preference.operator</code>. 操作人
     */
    public UserPreferenceRecord setOperator(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.operator</code>. 操作人
     */
    public String getOperator() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.user_preference.status</code>. 状态 VALID/INVALID
     */
    public UserPreferenceRecord setStatus(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.status</code>. 状态 VALID/INVALID
     */
    public String getStatus() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserPreferenceRecord
     */
    public UserPreferenceRecord() {
        super(UserPreferenceTable.USER_PREFERENCE);
    }

    /**
     * Create a detached, initialised UserPreferenceRecord
     */
    public UserPreferenceRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String configKey, String configValue, String operator, String status) {
        super(UserPreferenceTable.USER_PREFERENCE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setConfigKey(configKey);
        setConfigValue(configValue);
        setOperator(operator);
        setStatus(status);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserPreferenceRecord
     */
    public UserPreferenceRecord(UserPreference value) {
        super(UserPreferenceTable.USER_PREFERENCE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setConfigKey(value.getConfigKey());
            setConfigValue(value.getConfigValue());
            setOperator(value.getOperator());
            setStatus(value.getStatus());
            resetChangedOnNotNull();
        }
    }
}
