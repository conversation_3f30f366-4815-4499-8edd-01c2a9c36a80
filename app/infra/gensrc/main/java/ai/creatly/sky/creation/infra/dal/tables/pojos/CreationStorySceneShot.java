/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.Duration;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 场景分镜
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationStorySceneShot implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long storyId;
    private Long sceneId;
    private String shotName;
    private Integer index;
    private String description;
    private String enDescription;
    private JSONB asset;
    private JSONB dialogues;
    private JSONB sounds;
    private String soundsDesc;
    private JSONB creator;
    private String status;
    private String movement;
    private Duration duration;

    public CreationStorySceneShot() {}

    public CreationStorySceneShot(CreationStorySceneShot value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.storyId = value.storyId;
        this.sceneId = value.sceneId;
        this.shotName = value.shotName;
        this.index = value.index;
        this.description = value.description;
        this.enDescription = value.enDescription;
        this.asset = value.asset;
        this.dialogues = value.dialogues;
        this.sounds = value.sounds;
        this.soundsDesc = value.soundsDesc;
        this.creator = value.creator;
        this.status = value.status;
        this.movement = value.movement;
        this.duration = value.duration;
    }

    public CreationStorySceneShot(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long storyId,
        Long sceneId,
        String shotName,
        Integer index,
        String description,
        String enDescription,
        JSONB asset,
        JSONB dialogues,
        JSONB sounds,
        String soundsDesc,
        JSONB creator,
        String status,
        @Nullable String movement,
        Duration duration
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.storyId = storyId;
        this.sceneId = sceneId;
        this.shotName = shotName;
        this.index = index;
        this.description = description;
        this.enDescription = enDescription;
        this.asset = asset;
        this.dialogues = dialogues;
        this.sounds = sounds;
        this.soundsDesc = soundsDesc;
        this.creator = creator;
        this.status = status;
        this.movement = movement;
        this.duration = duration;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.id</code>. 分镜id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.id</code>. 分镜id
     */
    public CreationStorySceneShot setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.created_at</code>.
     * 创建时间
     */
    public CreationStorySceneShot setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.updated_at</code>.
     * 更新时间
     */
    public CreationStorySceneShot setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.story_id</code>. 故事id
     */
    public Long getStoryId() {
        return this.storyId;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.story_id</code>. 故事id
     */
    public CreationStorySceneShot setStoryId(Long storyId) {
        this.storyId = storyId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.scene_id</code>. 场景id
     */
    public Long getSceneId() {
        return this.sceneId;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.scene_id</code>. 场景id
     */
    public CreationStorySceneShot setSceneId(Long sceneId) {
        this.sceneId = sceneId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.shot_name</code>.
     * 舞台演出
     */
    public String getShotName() {
        return this.shotName;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.shot_name</code>.
     * 舞台演出
     */
    public CreationStorySceneShot setShotName(String shotName) {
        this.shotName = shotName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.index</code>. 分镜下标
     */
    public Integer getIndex() {
        return this.index;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.index</code>. 分镜下标
     */
    public CreationStorySceneShot setIndex(Integer index) {
        this.index = index;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.description</code>.
     * 分镜描述
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.description</code>.
     * 分镜描述
     */
    public CreationStorySceneShot setDescription(String description) {
        this.description = description;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_story_scene_shot.en_description</code>. 英文分镜描述
     */
    public String getEnDescription() {
        return this.enDescription;
    }

    /**
     * Setter for
     * <code>creation.creation_story_scene_shot.en_description</code>. 英文分镜描述
     */
    public CreationStorySceneShot setEnDescription(String enDescription) {
        this.enDescription = enDescription;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.asset</code>. 分镜资产
     */
    public JSONB getAsset() {
        return this.asset;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.asset</code>. 分镜资产
     */
    public CreationStorySceneShot setAsset(JSONB asset) {
        this.asset = asset;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.dialogues</code>. 对白
     */
    public JSONB getDialogues() {
        return this.dialogues;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.dialogues</code>. 对白
     */
    public CreationStorySceneShot setDialogues(JSONB dialogues) {
        this.dialogues = dialogues;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.sounds</code>. 音效
     */
    public JSONB getSounds() {
        return this.sounds;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.sounds</code>. 音效
     */
    public CreationStorySceneShot setSounds(JSONB sounds) {
        this.sounds = sounds;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.sounds_desc</code>.
     * 音效描述
     */
    public String getSoundsDesc() {
        return this.soundsDesc;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.sounds_desc</code>.
     * 音效描述
     */
    public CreationStorySceneShot setSoundsDesc(String soundsDesc) {
        this.soundsDesc = soundsDesc;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return this.creator;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.creator</code>. 创建者
     */
    public CreationStorySceneShot setCreator(JSONB creator) {
        this.creator = creator;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.status</code>. 状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.status</code>. 状态
     */
    public CreationStorySceneShot setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.movement</code>.
     */
    @Nullable
    public String getMovement() {
        return this.movement;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.movement</code>.
     */
    public CreationStorySceneShot setMovement(@Nullable String movement) {
        this.movement = movement;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.duration</code>. 分镜时长
     */
    public Duration getDuration() {
        return this.duration;
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.duration</code>. 分镜时长
     */
    public CreationStorySceneShot setDuration(Duration duration) {
        this.duration = duration;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationStorySceneShot other = (CreationStorySceneShot) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.storyId == null) {
            if (other.storyId != null)
                return false;
        }
        else if (!this.storyId.equals(other.storyId))
            return false;
        if (this.sceneId == null) {
            if (other.sceneId != null)
                return false;
        }
        else if (!this.sceneId.equals(other.sceneId))
            return false;
        if (this.shotName == null) {
            if (other.shotName != null)
                return false;
        }
        else if (!this.shotName.equals(other.shotName))
            return false;
        if (this.index == null) {
            if (other.index != null)
                return false;
        }
        else if (!this.index.equals(other.index))
            return false;
        if (this.description == null) {
            if (other.description != null)
                return false;
        }
        else if (!this.description.equals(other.description))
            return false;
        if (this.enDescription == null) {
            if (other.enDescription != null)
                return false;
        }
        else if (!this.enDescription.equals(other.enDescription))
            return false;
        if (this.asset == null) {
            if (other.asset != null)
                return false;
        }
        else if (!this.asset.equals(other.asset))
            return false;
        if (this.dialogues == null) {
            if (other.dialogues != null)
                return false;
        }
        else if (!this.dialogues.equals(other.dialogues))
            return false;
        if (this.sounds == null) {
            if (other.sounds != null)
                return false;
        }
        else if (!this.sounds.equals(other.sounds))
            return false;
        if (this.soundsDesc == null) {
            if (other.soundsDesc != null)
                return false;
        }
        else if (!this.soundsDesc.equals(other.soundsDesc))
            return false;
        if (this.creator == null) {
            if (other.creator != null)
                return false;
        }
        else if (!this.creator.equals(other.creator))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.movement == null) {
            if (other.movement != null)
                return false;
        }
        else if (!this.movement.equals(other.movement))
            return false;
        if (this.duration == null) {
            if (other.duration != null)
                return false;
        }
        else if (!this.duration.equals(other.duration))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.storyId == null) ? 0 : this.storyId.hashCode());
        result = prime * result + ((this.sceneId == null) ? 0 : this.sceneId.hashCode());
        result = prime * result + ((this.shotName == null) ? 0 : this.shotName.hashCode());
        result = prime * result + ((this.index == null) ? 0 : this.index.hashCode());
        result = prime * result + ((this.description == null) ? 0 : this.description.hashCode());
        result = prime * result + ((this.enDescription == null) ? 0 : this.enDescription.hashCode());
        result = prime * result + ((this.asset == null) ? 0 : this.asset.hashCode());
        result = prime * result + ((this.dialogues == null) ? 0 : this.dialogues.hashCode());
        result = prime * result + ((this.sounds == null) ? 0 : this.sounds.hashCode());
        result = prime * result + ((this.soundsDesc == null) ? 0 : this.soundsDesc.hashCode());
        result = prime * result + ((this.creator == null) ? 0 : this.creator.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.movement == null) ? 0 : this.movement.hashCode());
        result = prime * result + ((this.duration == null) ? 0 : this.duration.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationStorySceneShot (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(storyId);
        sb.append(", ").append(sceneId);
        sb.append(", ").append(shotName);
        sb.append(", ").append(index);
        sb.append(", ").append(description);
        sb.append(", ").append(enDescription);
        sb.append(", ").append(asset);
        sb.append(", ").append(dialogues);
        sb.append(", ").append(sounds);
        sb.append(", ").append(soundsDesc);
        sb.append(", ").append(creator);
        sb.append(", ").append(status);
        sb.append(", ").append(movement);
        sb.append(", ").append(duration);

        sb.append(")");
        return sb.toString();
    }
}
