/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.CourseExamRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseExamRecord;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 考试记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseExamRecordRecord extends UpdatableRecordImpl<CourseExamRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.course_exam_record.id</code>. 考试记录ID
     */
    public CourseExamRecordRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.id</code>. 考试记录ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.course_exam_record.created_at</code>. 创建时间
     */
    public CourseExamRecordRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.course_exam_record.updated_at</code>. 更新时间
     */
    public CourseExamRecordRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.course_exam_record.exam_id</code>. 考试ID
     */
    public CourseExamRecordRecord setExamId(@Nullable Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.exam_id</code>. 考试ID
     */
    @Nullable
    public Long getExamId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.course_exam_record.number</code>. 问题序号
     */
    public CourseExamRecordRecord setNumber(@Nullable Integer value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.number</code>. 问题序号
     */
    @Nullable
    public Integer getNumber() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>creation.course_exam_record.question</code>. 题目
     */
    public CourseExamRecordRecord setQuestion(@Nullable String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.question</code>. 题目
     */
    @Nullable
    public String getQuestion() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.course_exam_record.content</code>. 内容
     */
    public CourseExamRecordRecord setContent(@Nullable String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.content</code>. 内容
     */
    @Nullable
    public String getContent() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.course_exam_record.answer</code>. 正确答案
     */
    public CourseExamRecordRecord setAnswer(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.answer</code>. 正确答案
     */
    @Nullable
    public String getAnswer() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.course_exam_record.user_answer</code>. 用户答案
     */
    public CourseExamRecordRecord setUserAnswer(@Nullable String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.user_answer</code>. 用户答案
     */
    @Nullable
    public String getUserAnswer() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.course_exam_record.type</code>. 题目类型
     */
    public CourseExamRecordRecord setType(@Nullable String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.type</code>. 题目类型
     */
    @Nullable
    public String getType() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.course_exam_record.uid</code>. 用户id
     */
    public CourseExamRecordRecord setUid(@Nullable Long value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.uid</code>. 用户id
     */
    @Nullable
    public Long getUid() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>creation.course_exam_record.org_code</code>. 组织机构
     */
    public CourseExamRecordRecord setOrgCode(@Nullable String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.org_code</code>. 组织机构
     */
    @Nullable
    public String getOrgCode() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.course_exam_record.score</code>. 本题分值
     */
    public CourseExamRecordRecord setScore(@Nullable Long value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.score</code>. 本题分值
     */
    @Nullable
    public Long getScore() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>creation.course_exam_record.user_score</code>. 用户得分
     */
    public CourseExamRecordRecord setUserScore(@Nullable Long value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_record.user_score</code>. 用户得分
     */
    @Nullable
    public Long getUserScore() {
        return (Long) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseExamRecordRecord
     */
    public CourseExamRecordRecord() {
        super(CourseExamRecordTable.COURSE_EXAM_RECORD);
    }

    /**
     * Create a detached, initialised CourseExamRecordRecord
     */
    public CourseExamRecordRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable Long examId, @Nullable Integer number, @Nullable String question, @Nullable String content, @Nullable String answer, @Nullable String userAnswer, @Nullable String type, @Nullable Long uid, @Nullable String orgCode, @Nullable Long score, @Nullable Long userScore) {
        super(CourseExamRecordTable.COURSE_EXAM_RECORD);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setExamId(examId);
        setNumber(number);
        setQuestion(question);
        setContent(content);
        setAnswer(answer);
        setUserAnswer(userAnswer);
        setType(type);
        setUid(uid);
        setOrgCode(orgCode);
        setScore(score);
        setUserScore(userScore);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised CourseExamRecordRecord
     */
    public CourseExamRecordRecord(CourseExamRecord value) {
        super(CourseExamRecordTable.COURSE_EXAM_RECORD);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setExamId(value.getExamId());
            setNumber(value.getNumber());
            setQuestion(value.getQuestion());
            setContent(value.getContent());
            setAnswer(value.getAnswer());
            setUserAnswer(value.getUserAnswer());
            setType(value.getType());
            setUid(value.getUid());
            setOrgCode(value.getOrgCode());
            setScore(value.getScore());
            setUserScore(value.getUserScore());
            resetChangedOnNotNull();
        }
    }
}
