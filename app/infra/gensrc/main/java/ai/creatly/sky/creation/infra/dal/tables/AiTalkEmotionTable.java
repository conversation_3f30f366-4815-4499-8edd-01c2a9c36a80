/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.AiTalkEmotionRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * AI Talk 表情模板
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiTalkEmotionTable extends TableImpl<AiTalkEmotionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_ai_talk_emotion</code>
     */
    public static final AiTalkEmotionTable CREATION_AI_TALK_EMOTION = new AiTalkEmotionTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AiTalkEmotionRecord> getRecordType() {
        return AiTalkEmotionRecord.class;
    }

    /**
     * The column <code>creation.creation_ai_talk_emotion.id</code>. 主键
     */
    public final TableField<AiTalkEmotionRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_ai_talk_emotion.created_at</code>.
     * 创建时间
     */
    public final TableField<AiTalkEmotionRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_talk_emotion.updated_at</code>.
     * 更新时间
     */
    public final TableField<AiTalkEmotionRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_talk_emotion.uid</code>. 用户ID
     */
    public final TableField<AiTalkEmotionRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户ID");

    /**
     * The column <code>creation.creation_ai_talk_emotion.name</code>. 表情名称
     */
    public final TableField<AiTalkEmotionRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(255).nullable(false), this, "表情名称");

    /**
     * The column <code>creation.creation_ai_talk_emotion.code</code>.
     * 表情code（全局唯一）
     */
    public final TableField<AiTalkEmotionRecord, String> CODE = createField(DSL.name("code"), SQLDataType.VARCHAR(64).nullable(false), this, "表情code（全局唯一）");

    /**
     * The column <code>creation.creation_ai_talk_emotion.video_id</code>.
     * 表情视频文件id
     */
    public final TableField<AiTalkEmotionRecord, Long> VIDEO_ID = createField(DSL.name("video_id"), SQLDataType.BIGINT.nullable(false), this, "表情视频文件id");

    /**
     * The column <code>creation.creation_ai_talk_emotion.video_url</code>.
     * 表情视频文件地址
     */
    public final TableField<AiTalkEmotionRecord, String> VIDEO_URL = createField(DSL.name("video_url"), SQLDataType.VARCHAR(255).nullable(false), this, "表情视频文件地址");

    /**
     * The column
     * <code>creation.creation_ai_talk_emotion.template_file_id</code>. 表情模型文件ID
     */
    public final TableField<AiTalkEmotionRecord, Long> TEMPLATE_FILE_ID = createField(DSL.name("template_file_id"), SQLDataType.BIGINT, this, "表情模型文件ID");

    /**
     * The column <code>creation.creation_ai_talk_emotion.template_url</code>.
     * 表情模型文件地址
     */
    public final TableField<AiTalkEmotionRecord, String> TEMPLATE_URL = createField(DSL.name("template_url"), SQLDataType.VARCHAR(255), this, "表情模型文件地址");

    /**
     * The column <code>creation.creation_ai_talk_emotion.embedded</code>.
     * 是否系统内置
     */
    public final TableField<AiTalkEmotionRecord, Boolean> EMBEDDED = createField(DSL.name("embedded"), SQLDataType.BOOLEAN.nullable(false), this, "是否系统内置");

    /**
     * The column <code>creation.creation_ai_talk_emotion.status</code>. 当前状态
     */
    public final TableField<AiTalkEmotionRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(16).nullable(false), this, "当前状态");

    /**
     * The column <code>creation.creation_ai_talk_emotion.creator</code>. 创建者
     */
    public final TableField<AiTalkEmotionRecord, JSONB> CREATOR = createField(DSL.name("creator"), SQLDataType.JSONB.nullable(false), this, "创建者");

    private AiTalkEmotionTable(Name alias, Table<AiTalkEmotionRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private AiTalkEmotionTable(Name alias, Table<AiTalkEmotionRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("AI Talk 表情模板"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_ai_talk_emotion</code> table
     * reference
     */
    public AiTalkEmotionTable(String alias) {
        this(DSL.name(alias), CREATION_AI_TALK_EMOTION);
    }

    /**
     * Create an aliased <code>creation.creation_ai_talk_emotion</code> table
     * reference
     */
    public AiTalkEmotionTable(Name alias) {
        this(alias, CREATION_AI_TALK_EMOTION);
    }

    /**
     * Create a <code>creation.creation_ai_talk_emotion</code> table reference
     */
    public AiTalkEmotionTable() {
        this(DSL.name("creation_ai_talk_emotion"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<AiTalkEmotionRecord> getPrimaryKey() {
        return Keys.CREATION_AI_TALK_EMOTION_PKEY;
    }

    @Override
    public List<UniqueKey<AiTalkEmotionRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CREATION_AI_TALK_EMOTION_CODE_KEY);
    }

    @Override
    public AiTalkEmotionTable as(String alias) {
        return new AiTalkEmotionTable(DSL.name(alias), this);
    }

    @Override
    public AiTalkEmotionTable as(Name alias) {
        return new AiTalkEmotionTable(alias, this);
    }

    @Override
    public AiTalkEmotionTable as(Table<?> alias) {
        return new AiTalkEmotionTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public AiTalkEmotionTable rename(String name) {
        return new AiTalkEmotionTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public AiTalkEmotionTable rename(Name name) {
        return new AiTalkEmotionTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public AiTalkEmotionTable rename(Table<?> name) {
        return new AiTalkEmotionTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTalkEmotionTable where(Condition condition) {
        return new AiTalkEmotionTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTalkEmotionTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTalkEmotionTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTalkEmotionTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiTalkEmotionTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiTalkEmotionTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiTalkEmotionTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiTalkEmotionTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTalkEmotionTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiTalkEmotionTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
