/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 数字形象表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class DigHumanAvatar implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String name;
    private String gender;
    private String extOutId;
    private JSONB avatarFile;
    private String status;
    private JSONB coverFile;
    private Integer videoWidth;
    private Integer videoHeight;
    private String aspectRatio;

    public DigHumanAvatar() {}

    public DigHumanAvatar(DigHumanAvatar value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.name = value.name;
        this.gender = value.gender;
        this.extOutId = value.extOutId;
        this.avatarFile = value.avatarFile;
        this.status = value.status;
        this.coverFile = value.coverFile;
        this.videoWidth = value.videoWidth;
        this.videoHeight = value.videoHeight;
        this.aspectRatio = value.aspectRatio;
    }

    public DigHumanAvatar(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String name,
        String gender,
        @Nullable String extOutId,
        @Nullable JSONB avatarFile,
        String status,
        @Nullable JSONB coverFile,
        @Nullable Integer videoWidth,
        @Nullable Integer videoHeight,
        @Nullable String aspectRatio
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.name = name;
        this.gender = gender;
        this.extOutId = extOutId;
        this.avatarFile = avatarFile;
        this.status = status;
        this.coverFile = coverFile;
        this.videoWidth = videoWidth;
        this.videoHeight = videoHeight;
        this.aspectRatio = aspectRatio;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.id</code>. 主键ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.id</code>. 主键ID
     */
    public DigHumanAvatar setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.created_at</code>. 创建时间
     */
    public DigHumanAvatar setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.updated_at</code>. 更新时间
     */
    public DigHumanAvatar setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.uid</code>. 用户id
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.uid</code>. 用户id
     */
    public DigHumanAvatar setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.name</code>. 形象名
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.name</code>. 形象名
     */
    public DigHumanAvatar setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.gender</code>. 形象性别
     */
    public String getGender() {
        return this.gender;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.gender</code>. 形象性别
     */
    public DigHumanAvatar setGender(String gender) {
        this.gender = gender;
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.ext_out_id</code>. 形象外部标识id
     */
    @Nullable
    public String getExtOutId() {
        return this.extOutId;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.ext_out_id</code>. 形象外部标识id
     */
    public DigHumanAvatar setExtOutId(@Nullable String extOutId) {
        this.extOutId = extOutId;
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.avatar_file</code>. 形象文件id
     */
    @Nullable
    public JSONB getAvatarFile() {
        return this.avatarFile;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.avatar_file</code>. 形象文件id
     */
    public DigHumanAvatar setAvatarFile(@Nullable JSONB avatarFile) {
        this.avatarFile = avatarFile;
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.status</code>. 状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.status</code>. 状态
     */
    public DigHumanAvatar setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.cover_file</code>. 演讲者封面
     */
    @Nullable
    public JSONB getCoverFile() {
        return this.coverFile;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.cover_file</code>. 演讲者封面
     */
    public DigHumanAvatar setCoverFile(@Nullable JSONB coverFile) {
        this.coverFile = coverFile;
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.video_width</code>.
     * 数字人视频宽度（px）
     */
    @Nullable
    public Integer getVideoWidth() {
        return this.videoWidth;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.video_width</code>.
     * 数字人视频宽度（px）
     */
    public DigHumanAvatar setVideoWidth(@Nullable Integer videoWidth) {
        this.videoWidth = videoWidth;
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.video_height</code>.
     * 数字人视频高度（px）
     */
    @Nullable
    public Integer getVideoHeight() {
        return this.videoHeight;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.video_height</code>.
     * 数字人视频高度（px）
     */
    public DigHumanAvatar setVideoHeight(@Nullable Integer videoHeight) {
        this.videoHeight = videoHeight;
        return this;
    }

    /**
     * Getter for <code>creation.dig_human_avatar.aspect_ratio</code>. 形象宽高比
     */
    @Nullable
    public String getAspectRatio() {
        return this.aspectRatio;
    }

    /**
     * Setter for <code>creation.dig_human_avatar.aspect_ratio</code>. 形象宽高比
     */
    public DigHumanAvatar setAspectRatio(@Nullable String aspectRatio) {
        this.aspectRatio = aspectRatio;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final DigHumanAvatar other = (DigHumanAvatar) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.gender == null) {
            if (other.gender != null)
                return false;
        }
        else if (!this.gender.equals(other.gender))
            return false;
        if (this.extOutId == null) {
            if (other.extOutId != null)
                return false;
        }
        else if (!this.extOutId.equals(other.extOutId))
            return false;
        if (this.avatarFile == null) {
            if (other.avatarFile != null)
                return false;
        }
        else if (!this.avatarFile.equals(other.avatarFile))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.coverFile == null) {
            if (other.coverFile != null)
                return false;
        }
        else if (!this.coverFile.equals(other.coverFile))
            return false;
        if (this.videoWidth == null) {
            if (other.videoWidth != null)
                return false;
        }
        else if (!this.videoWidth.equals(other.videoWidth))
            return false;
        if (this.videoHeight == null) {
            if (other.videoHeight != null)
                return false;
        }
        else if (!this.videoHeight.equals(other.videoHeight))
            return false;
        if (this.aspectRatio == null) {
            if (other.aspectRatio != null)
                return false;
        }
        else if (!this.aspectRatio.equals(other.aspectRatio))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.gender == null) ? 0 : this.gender.hashCode());
        result = prime * result + ((this.extOutId == null) ? 0 : this.extOutId.hashCode());
        result = prime * result + ((this.avatarFile == null) ? 0 : this.avatarFile.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.coverFile == null) ? 0 : this.coverFile.hashCode());
        result = prime * result + ((this.videoWidth == null) ? 0 : this.videoWidth.hashCode());
        result = prime * result + ((this.videoHeight == null) ? 0 : this.videoHeight.hashCode());
        result = prime * result + ((this.aspectRatio == null) ? 0 : this.aspectRatio.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("DigHumanAvatar (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(name);
        sb.append(", ").append(gender);
        sb.append(", ").append(extOutId);
        sb.append(", ").append(avatarFile);
        sb.append(", ").append(status);
        sb.append(", ").append(coverFile);
        sb.append(", ").append(videoWidth);
        sb.append(", ").append(videoHeight);
        sb.append(", ").append(aspectRatio);

        sb.append(")");
        return sb.toString();
    }
}
