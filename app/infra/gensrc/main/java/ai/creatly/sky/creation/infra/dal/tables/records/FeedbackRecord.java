/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.FeedbackTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationFeedback;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户反馈
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class FeedbackRecord extends UpdatableRecordImpl<FeedbackRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_feedback.id</code>. 主键
     */
    public FeedbackRecord setId(@Nullable Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_feedback.created_at</code>. 创建时间
     */
    public FeedbackRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_feedback.updated_at</code>. 更新时间
     */
    public FeedbackRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_feedback.status</code>. 状态
     */
    public FeedbackRecord setStatus(@Nullable String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.status</code>. 状态
     */
    @Nullable
    public String getStatus() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_feedback.uid</code>. 用户ID
     */
    public FeedbackRecord setUid(Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.uid</code>. 用户ID
     */
    public Long getUid() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.creation_feedback.username</code>. 用户名称
     */
    public FeedbackRecord setUsername(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.username</code>. 用户名称
     */
    public String getUsername() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_feedback.phone</code>. 手机号
     */
    public FeedbackRecord setPhone(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.phone</code>. 手机号
     */
    public String getPhone() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_feedback.email</code>. 邮箱
     */
    public FeedbackRecord setEmail(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.email</code>. 邮箱
     */
    @Nullable
    public String getEmail() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_feedback.title</code>. 标题
     */
    public FeedbackRecord setTitle(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.title</code>. 标题
     */
    public String getTitle() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_feedback.detail</code>. 明细
     */
    public FeedbackRecord setDetail(@Nullable String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.detail</code>. 明细
     */
    @Nullable
    public String getDetail() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_feedback.attachments</code>. 附件列表
     */
    public FeedbackRecord setAttachments(JSONB value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feedback.attachments</code>. 附件列表
     */
    public JSONB getAttachments() {
        return (JSONB) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached FeedbackRecord
     */
    public FeedbackRecord() {
        super(FeedbackTable.CREATION_FEEDBACK);
    }

    /**
     * Create a detached, initialised FeedbackRecord
     */
    public FeedbackRecord(@Nullable Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable String status, Long uid, String username, String phone, @Nullable String email, String title, @Nullable String detail, JSONB attachments) {
        super(FeedbackTable.CREATION_FEEDBACK);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setStatus(status);
        setUid(uid);
        setUsername(username);
        setPhone(phone);
        setEmail(email);
        setTitle(title);
        setDetail(detail);
        setAttachments(attachments);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised FeedbackRecord
     */
    public FeedbackRecord(CreationFeedback value) {
        super(FeedbackTable.CREATION_FEEDBACK);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setStatus(value.getStatus());
            setUid(value.getUid());
            setUsername(value.getUsername());
            setPhone(value.getPhone());
            setEmail(value.getEmail());
            setTitle(value.getTitle());
            setDetail(value.getDetail());
            setAttachments(value.getAttachments());
            resetChangedOnNotNull();
        }
    }
}
