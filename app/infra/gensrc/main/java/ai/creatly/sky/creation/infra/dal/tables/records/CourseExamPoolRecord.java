/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.CourseExamPoolTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseExamPool;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 考试题库
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseExamPoolRecord extends UpdatableRecordImpl<CourseExamPoolRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.course_exam_pool.id</code>. 题库ID
     */
    public CourseExamPoolRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.id</code>. 题库ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.course_exam_pool.created_at</code>. 创建时间
     */
    public CourseExamPoolRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.course_exam_pool.updated_at</code>. 更新时间
     */
    public CourseExamPoolRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.course_exam_pool.cert_id</code>. 证书ID
     */
    public CourseExamPoolRecord setCertId(@Nullable Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.cert_id</code>. 证书ID
     */
    @Nullable
    public Long getCertId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.course_exam_pool.question</code>. 题目
     */
    public CourseExamPoolRecord setQuestion(@Nullable String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.question</code>. 题目
     */
    @Nullable
    public String getQuestion() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.course_exam_pool.content</code>. 内容
     */
    public CourseExamPoolRecord setContent(@Nullable String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.content</code>. 内容
     */
    @Nullable
    public String getContent() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.course_exam_pool.answer</code>. 标准答案
     */
    public CourseExamPoolRecord setAnswer(@Nullable String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.answer</code>. 标准答案
     */
    @Nullable
    public String getAnswer() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.course_exam_pool.type</code>. 题目类型
     */
    public CourseExamPoolRecord setType(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.type</code>. 题目类型
     */
    @Nullable
    public String getType() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.course_exam_pool.score</code>. 标准得分
     */
    public CourseExamPoolRecord setScore(@Nullable Long value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam_pool.score</code>. 标准得分
     */
    @Nullable
    public Long getScore() {
        return (Long) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseExamPoolRecord
     */
    public CourseExamPoolRecord() {
        super(CourseExamPoolTable.COURSE_EXAM_POOL);
    }

    /**
     * Create a detached, initialised CourseExamPoolRecord
     */
    public CourseExamPoolRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable Long certId, @Nullable String question, @Nullable String content, @Nullable String answer, @Nullable String type, @Nullable Long score) {
        super(CourseExamPoolTable.COURSE_EXAM_POOL);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCertId(certId);
        setQuestion(question);
        setContent(content);
        setAnswer(answer);
        setType(type);
        setScore(score);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised CourseExamPoolRecord
     */
    public CourseExamPoolRecord(CourseExamPool value) {
        super(CourseExamPoolTable.COURSE_EXAM_POOL);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCertId(value.getCertId());
            setQuestion(value.getQuestion());
            setContent(value.getContent());
            setAnswer(value.getAnswer());
            setType(value.getType());
            setScore(value.getScore());
            resetChangedOnNotNull();
        }
    }
}
