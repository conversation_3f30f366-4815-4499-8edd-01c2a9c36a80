/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.StoryRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 故事
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class StoryTable extends TableImpl<StoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_story</code>
     */
    public static final StoryTable CREATION_STORY = new StoryTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StoryRecord> getRecordType() {
        return StoryRecord.class;
    }

    /**
     * The column <code>creation.creation_story.id</code>. 故事id
     */
    public final TableField<StoryRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "故事id");

    /**
     * The column <code>creation.creation_story.created_at</code>. 创建时间
     */
    public final TableField<StoryRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_story.updated_at</code>. 更新时间
     */
    public final TableField<StoryRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_story.name</code>. 故事名
     */
    public final TableField<StoryRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(64).nullable(false), this, "故事名");

    /**
     * The column <code>creation.creation_story.content</code>. 故事内容
     */
    public final TableField<StoryRecord, String> CONTENT = createField(DSL.name("content"), SQLDataType.VARCHAR(8000).nullable(false), this, "故事内容");

    /**
     * The column <code>creation.creation_story.story_type</code>. 故事类型
     */
    public final TableField<StoryRecord, String> STORY_TYPE = createField(DSL.name("story_type"), SQLDataType.VARCHAR(20).nullable(false), this, "故事类型");

    /**
     * The column <code>creation.creation_story.style_config</code>. 样式配置
     */
    public final TableField<StoryRecord, JSONB> STYLE_CONFIG = createField(DSL.name("style_config"), SQLDataType.JSONB.nullable(false), this, "样式配置");

    /**
     * The column <code>creation.creation_story.uid</code>. 用户id
     */
    public final TableField<StoryRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户id");

    /**
     * The column <code>creation.creation_story.roles</code>. 故事角色
     */
    public final TableField<StoryRecord, JSONB> ROLES = createField(DSL.name("roles"), SQLDataType.JSONB.nullable(false), this, "故事角色");

    /**
     * The column <code>creation.creation_story.creator</code>. 创建者
     */
    public final TableField<StoryRecord, JSONB> CREATOR = createField(DSL.name("creator"), SQLDataType.JSONB.nullable(false), this, "创建者");

    /**
     * The column <code>creation.creation_story.status</code>. 状态
     */
    public final TableField<StoryRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(20).defaultValue(DSL.field(DSL.raw("'VALID/INVALID'::character varying"), SQLDataType.VARCHAR)), this, "状态");

    /**
     * The column <code>creation.creation_story.idea</code>. 故事灵感
     */
    public final TableField<StoryRecord, String> IDEA = createField(DSL.name("idea"), SQLDataType.VARCHAR(600), this, "故事灵感");

    /**
     * The column <code>creation.creation_story.source_type</code>. 来源类型
     */
    public final TableField<StoryRecord, String> SOURCE_TYPE = createField(DSL.name("source_type"), SQLDataType.VARCHAR(32), this, "来源类型");

    private StoryTable(Name alias, Table<StoryRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private StoryTable(Name alias, Table<StoryRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("故事"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_story</code> table reference
     */
    public StoryTable(String alias) {
        this(DSL.name(alias), CREATION_STORY);
    }

    /**
     * Create an aliased <code>creation.creation_story</code> table reference
     */
    public StoryTable(Name alias) {
        this(alias, CREATION_STORY);
    }

    /**
     * Create a <code>creation.creation_story</code> table reference
     */
    public StoryTable() {
        this(DSL.name("creation_story"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<StoryRecord> getPrimaryKey() {
        return Keys.CREATION_STORY_PKEY;
    }

    @Override
    public StoryTable as(String alias) {
        return new StoryTable(DSL.name(alias), this);
    }

    @Override
    public StoryTable as(Name alias) {
        return new StoryTable(alias, this);
    }

    @Override
    public StoryTable as(Table<?> alias) {
        return new StoryTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public StoryTable rename(String name) {
        return new StoryTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public StoryTable rename(Name name) {
        return new StoryTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public StoryTable rename(Table<?> name) {
        return new StoryTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StoryTable where(Condition condition) {
        return new StoryTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StoryTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StoryTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StoryTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public StoryTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public StoryTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public StoryTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public StoryTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StoryTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StoryTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
