/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.UserAssetTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserAsset;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户素材库
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserAssetRecord extends UpdatableRecordImpl<UserAssetRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_user_asset.id</code>. 主键
     */
    public UserAssetRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_user_asset.created_at</code>. 创建时间
     */
    public UserAssetRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_user_asset.updated_at</code>. 更新时间
     */
    public UserAssetRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_user_asset.uid</code>. 用户ID
     */
    public UserAssetRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.uid</code>. 用户ID
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_user_asset.status</code>. 素材状态
     */
    public UserAssetRecord setStatus(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.status</code>. 素材状态
     */
    public String getStatus() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_user_asset.name</code>. 素材名称
     */
    public UserAssetRecord setName(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.name</code>. 素材名称
     */
    public String getName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_user_asset.type</code>. 素材类型
     */
    public UserAssetRecord setType(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.type</code>. 素材类型
     */
    public String getType() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_user_asset.file</code>. 素材文件
     */
    public UserAssetRecord setFile(JSONB value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.file</code>. 素材文件
     */
    public JSONB getFile() {
        return (JSONB) get(7);
    }

    /**
     * Setter for <code>creation.creation_user_asset.content</code>. 素材内容
     */
    public UserAssetRecord setContent(JSONB value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.content</code>. 素材内容
     */
    public JSONB getContent() {
        return (JSONB) get(8);
    }

    /**
     * Setter for <code>creation.creation_user_asset.cover_file_id</code>.
     * 素材封面图文件ID
     */
    public UserAssetRecord setCoverFileId(@Nullable Long value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.cover_file_id</code>.
     * 素材封面图文件ID
     */
    @Nullable
    public Long getCoverFileId() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>creation.creation_user_asset.cover_url</code>. 素材封面图地址
     */
    public UserAssetRecord setCoverUrl(@Nullable String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.cover_url</code>. 素材封面图地址
     */
    @Nullable
    public String getCoverUrl() {
        return (String) get(10);
    }

    /**
     * Setter for <code>creation.creation_user_asset.description</code>. 素材描述
     */
    public UserAssetRecord setDescription(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.description</code>. 素材描述
     */
    public String getDescription() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_user_asset.metadata</code>. 素材元数据
     */
    public UserAssetRecord setMetadata(JSONB value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.metadata</code>. 素材元数据
     */
    public JSONB getMetadata() {
        return (JSONB) get(12);
    }

    /**
     * Setter for <code>creation.creation_user_asset.keywords</code>. 素材关键词列表
     */
    public UserAssetRecord setKeywords(String[] value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.keywords</code>. 素材关键词列表
     */
    public String[] getKeywords() {
        return (String[]) get(13);
    }

    /**
     * Setter for <code>creation.creation_user_asset.labels</code>. 素材标签列表
     */
    public UserAssetRecord setLabels(JSONB value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.labels</code>. 素材标签列表
     */
    public JSONB getLabels() {
        return (JSONB) get(14);
    }

    /**
     * Setter for <code>creation.creation_user_asset.video_slices</code>.
     * 视频素材切片列表
     */
    public UserAssetRecord setVideoSlices(@Nullable JSONB value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.video_slices</code>.
     * 视频素材切片列表
     */
    @Nullable
    public JSONB getVideoSlices() {
        return (JSONB) get(15);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserAssetRecord
     */
    public UserAssetRecord() {
        super(UserAssetTable.CREATION_USER_ASSET);
    }

    /**
     * Create a detached, initialised UserAssetRecord
     */
    public UserAssetRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String status, String name, String type, JSONB file, JSONB content, @Nullable Long coverFileId, @Nullable String coverUrl, String description, JSONB metadata, String[] keywords, JSONB labels, @Nullable JSONB videoSlices) {
        super(UserAssetTable.CREATION_USER_ASSET);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setStatus(status);
        setName(name);
        setType(type);
        setFile(file);
        setContent(content);
        setCoverFileId(coverFileId);
        setCoverUrl(coverUrl);
        setDescription(description);
        setMetadata(metadata);
        setKeywords(keywords);
        setLabels(labels);
        setVideoSlices(videoSlices);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserAssetRecord
     */
    public UserAssetRecord(CreationUserAsset value) {
        super(UserAssetTable.CREATION_USER_ASSET);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setStatus(value.getStatus());
            setName(value.getName());
            setType(value.getType());
            setFile(value.getFile());
            setContent(value.getContent());
            setCoverFileId(value.getCoverFileId());
            setCoverUrl(value.getCoverUrl());
            setDescription(value.getDescription());
            setMetadata(value.getMetadata());
            setKeywords(value.getKeywords());
            setLabels(value.getLabels());
            setVideoSlices(value.getVideoSlices());
            resetChangedOnNotNull();
        }
    }
}
