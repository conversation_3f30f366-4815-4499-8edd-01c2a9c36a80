/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 提示词标签
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationPromptTag implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String code;
    private String color;
    private String name;
    private String enName;
    private String status;

    public CreationPromptTag() {}

    public CreationPromptTag(CreationPromptTag value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.code = value.code;
        this.color = value.color;
        this.name = value.name;
        this.enName = value.enName;
        this.status = value.status;
    }

    public CreationPromptTag(
        @Nullable Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        String code,
        String color,
        String name,
        String enName,
        String status
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.code = code;
        this.color = color;
        this.name = name;
        this.enName = enName;
        this.status = status;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.id</code>. 主键
     */
    public CreationPromptTag setId(@Nullable Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.created_at</code>. 创建时间
     */
    public CreationPromptTag setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.updated_at</code>. 更新时间
     */
    public CreationPromptTag setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.code</code>. 标签码
     */
    public String getCode() {
        return this.code;
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.code</code>. 标签码
     */
    public CreationPromptTag setCode(String code) {
        this.code = code;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.color</code>. 标签颜色
     */
    public String getColor() {
        return this.color;
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.color</code>. 标签颜色
     */
    public CreationPromptTag setColor(String color) {
        this.color = color;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.name</code>. 标签英文名
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.name</code>. 标签英文名
     */
    public CreationPromptTag setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.en_name</code>.
     */
    public String getEnName() {
        return this.enName;
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.en_name</code>.
     */
    public CreationPromptTag setEnName(String enName) {
        this.enName = enName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.status</code>. 状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.status</code>. 状态
     */
    public CreationPromptTag setStatus(String status) {
        this.status = status;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationPromptTag other = (CreationPromptTag) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.code == null) {
            if (other.code != null)
                return false;
        }
        else if (!this.code.equals(other.code))
            return false;
        if (this.color == null) {
            if (other.color != null)
                return false;
        }
        else if (!this.color.equals(other.color))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.enName == null) {
            if (other.enName != null)
                return false;
        }
        else if (!this.enName.equals(other.enName))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.code == null) ? 0 : this.code.hashCode());
        result = prime * result + ((this.color == null) ? 0 : this.color.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.enName == null) ? 0 : this.enName.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationPromptTag (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(code);
        sb.append(", ").append(color);
        sb.append(", ").append(name);
        sb.append(", ").append(enName);
        sb.append(", ").append(status);

        sb.append(")");
        return sb.toString();
    }
}
