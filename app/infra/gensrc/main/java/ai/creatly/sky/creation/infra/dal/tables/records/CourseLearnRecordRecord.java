/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.CourseLearnRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseLearnRecord;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 课程学习记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseLearnRecordRecord extends UpdatableRecordImpl<CourseLearnRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.course_learn_record.id</code>. 课程学习记录id
     */
    public CourseLearnRecordRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_learn_record.id</code>. 课程学习记录id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.course_learn_record.created_at</code>. 创建时间
     */
    public CourseLearnRecordRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_learn_record.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.course_learn_record.updated_at</code>. 更新时间
     */
    public CourseLearnRecordRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_learn_record.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.course_learn_record.course_id</code>. 课程ID
     */
    public CourseLearnRecordRecord setCourseId(@Nullable Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_learn_record.course_id</code>. 课程ID
     */
    @Nullable
    public Long getCourseId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.course_learn_record.content_id</code>. 课件ID
     */
    public CourseLearnRecordRecord setContentId(@Nullable Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_learn_record.content_id</code>. 课件ID
     */
    @Nullable
    public Long getContentId() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.course_learn_record.status</code>. 学习状态
     */
    public CourseLearnRecordRecord setStatus(@Nullable Integer value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_learn_record.status</code>. 学习状态
     */
    @Nullable
    public Integer getStatus() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>creation.course_learn_record.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public CourseLearnRecordRecord setOwnerId(@Nullable Long value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_learn_record.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    @Nullable
    public Long getOwnerId() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>creation.course_learn_record.owner_name</code>. 任务所有者名称
     */
    public CourseLearnRecordRecord setOwnerName(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_learn_record.owner_name</code>. 任务所有者名称
     */
    @Nullable
    public String getOwnerName() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.course_learn_record.init_seconds</code>. 初始秒数
     */
    public CourseLearnRecordRecord setInitSeconds(@Nullable Integer value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_learn_record.init_seconds</code>. 初始秒数
     */
    @Nullable
    public Integer getInitSeconds() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>creation.course_learn_record.org_code</code>. 组织代码
     */
    public CourseLearnRecordRecord setOrgCode(@Nullable String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_learn_record.org_code</code>. 组织代码
     */
    @Nullable
    public String getOrgCode() {
        return (String) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseLearnRecordRecord
     */
    public CourseLearnRecordRecord() {
        super(CourseLearnRecordTable.COURSE_LEARN_RECORD);
    }

    /**
     * Create a detached, initialised CourseLearnRecordRecord
     */
    public CourseLearnRecordRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable Long courseId, @Nullable Long contentId, @Nullable Integer status, @Nullable Long ownerId, @Nullable String ownerName, @Nullable Integer initSeconds, @Nullable String orgCode) {
        super(CourseLearnRecordTable.COURSE_LEARN_RECORD);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCourseId(courseId);
        setContentId(contentId);
        setStatus(status);
        setOwnerId(ownerId);
        setOwnerName(ownerName);
        setInitSeconds(initSeconds);
        setOrgCode(orgCode);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised CourseLearnRecordRecord
     */
    public CourseLearnRecordRecord(CourseLearnRecord value) {
        super(CourseLearnRecordTable.COURSE_LEARN_RECORD);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCourseId(value.getCourseId());
            setContentId(value.getContentId());
            setStatus(value.getStatus());
            setOwnerId(value.getOwnerId());
            setOwnerName(value.getOwnerName());
            setInitSeconds(value.getInitSeconds());
            setOrgCode(value.getOrgCode());
            resetChangedOnNotNull();
        }
    }
}
