/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.AssetTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAsset;
import ai.creatly.sky.creation.infra.dal.tables.records.AssetRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.JSONB;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 作品
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class AssetDAO extends DAOImpl<AssetRecord, CreationAsset, Long> {

    /**
     * Create a new AssetDAO without any configuration
     */
    public AssetDAO() {
        super(AssetTable.CREATION_ASSET, CreationAsset.class);
    }

    /**
     * Create a new AssetDAO with an attached configuration
     */
    @Autowired
    public AssetDAO(Configuration configuration) {
        super(AssetTable.CREATION_ASSET, CreationAsset.class, configuration);
    }

    @Override
    public Long getId(CreationAsset object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationAsset> fetchById(Long... values) {
        return fetch(AssetTable.CREATION_ASSET.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationAsset fetchOneById(Long value) {
        return fetchOne(AssetTable.CREATION_ASSET.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationAsset> fetchOptionalById(Long value) {
        return fetchOptional(AssetTable.CREATION_ASSET.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationAsset> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(AssetTable.CREATION_ASSET.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationAsset> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(AssetTable.CREATION_ASSET.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<CreationAsset> fetchByUid(Long... values) {
        return fetch(AssetTable.CREATION_ASSET.UID, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CreationAsset> fetchByStatus(String... values) {
        return fetch(AssetTable.CREATION_ASSET.STATUS, values);
    }

    /**
     * Fetch records that have <code>source_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfSourceType(String lowerInclusive, String upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.SOURCE_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>source_type IN (values)</code>
     */
    public List<CreationAsset> fetchBySourceType(String... values) {
        return fetch(AssetTable.CREATION_ASSET.SOURCE_TYPE, values);
    }

    /**
     * Fetch records that have <code>biz_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfBizType(String lowerInclusive, String upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.BIZ_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_type IN (values)</code>
     */
    public List<CreationAsset> fetchByBizType(String... values) {
        return fetch(AssetTable.CREATION_ASSET.BIZ_TYPE, values);
    }

    /**
     * Fetch records that have <code>cover_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfCoverUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.COVER_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover_url IN (values)</code>
     */
    public List<CreationAsset> fetchByCoverUrl(String... values) {
        return fetch(AssetTable.CREATION_ASSET.COVER_URL, values);
    }

    /**
     * Fetch records that have <code>file BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfFile(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.FILE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>file IN (values)</code>
     */
    public List<CreationAsset> fetchByFile(JSONB... values) {
        return fetch(AssetTable.CREATION_ASSET.FILE, values);
    }

    /**
     * Fetch records that have <code>content BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfContent(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.CONTENT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>content IN (values)</code>
     */
    public List<CreationAsset> fetchByContent(JSONB... values) {
        return fetch(AssetTable.CREATION_ASSET.CONTENT, values);
    }

    /**
     * Fetch records that have <code>metadata BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfMetadata(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.METADATA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>metadata IN (values)</code>
     */
    public List<CreationAsset> fetchByMetadata(JSONB... values) {
        return fetch(AssetTable.CREATION_ASSET.METADATA, values);
    }

    /**
     * Fetch records that have <code>tags BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfTags(String[] lowerInclusive, String[] upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.TAGS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>tags IN (values)</code>
     */
    public List<CreationAsset> fetchByTags(String[]... values) {
        return fetch(AssetTable.CREATION_ASSET.TAGS, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<CreationAsset> fetchByName(String... values) {
        return fetch(AssetTable.CREATION_ASSET.NAME, values);
    }

    /**
     * Fetch records that have <code>org_code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAsset> fetchRangeOfOrgCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(AssetTable.CREATION_ASSET.ORG_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_code IN (values)</code>
     */
    public List<CreationAsset> fetchByOrgCode(String... values) {
        return fetch(AssetTable.CREATION_ASSET.ORG_CODE, values);
    }
}
