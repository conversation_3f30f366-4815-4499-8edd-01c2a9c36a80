/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.BusinessOrderRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 课程
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class BusinessOrderTable extends TableImpl<BusinessOrderRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_business_order</code>
     */
    public static final BusinessOrderTable CREATION_BUSINESS_ORDER = new BusinessOrderTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<BusinessOrderRecord> getRecordType() {
        return BusinessOrderRecord.class;
    }

    /**
     * The column <code>creation.creation_business_order.id</code>. 商单id
     */
    public final TableField<BusinessOrderRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "商单id");

    /**
     * The column <code>creation.creation_business_order.created_at</code>. 创建时间
     */
    public final TableField<BusinessOrderRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_business_order.updated_at</code>. 更新时间
     */
    public final TableField<BusinessOrderRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_business_order.cover_url</code>. 商单封面
     */
    public final TableField<BusinessOrderRecord, String> COVER_URL = createField(DSL.name("cover_url"), SQLDataType.VARCHAR(128), this, "商单封面");

    /**
     * The column <code>creation.creation_business_order.video_url</code>. 视频链接
     */
    public final TableField<BusinessOrderRecord, String> VIDEO_URL = createField(DSL.name("video_url"), SQLDataType.VARCHAR(255), this, "视频链接");

    /**
     * The column <code>creation.creation_business_order.title</code>. 商单名称
     */
    public final TableField<BusinessOrderRecord, String> TITLE = createField(DSL.name("title"), SQLDataType.VARCHAR(20), this, "商单名称");

    /**
     * The column <code>creation.creation_business_order.order_intro</code>.
     * 商单介绍
     */
    public final TableField<BusinessOrderRecord, String> ORDER_INTRO = createField(DSL.name("order_intro"), SQLDataType.CLOB, this, "商单介绍");

    /**
     * The column <code>creation.creation_business_order.order_status</code>.
     * 商单状态
     */
    public final TableField<BusinessOrderRecord, String> ORDER_STATUS = createField(DSL.name("order_status"), SQLDataType.VARCHAR(20), this, "商单状态");

    /**
     * The column <code>creation.creation_business_order.order_type</code>. 商单类型
     */
    public final TableField<BusinessOrderRecord, String> ORDER_TYPE = createField(DSL.name("order_type"), SQLDataType.VARCHAR(32), this, "商单类型");

    /**
     * The column <code>creation.creation_business_order.subtitle</code>. 副标题
     */
    public final TableField<BusinessOrderRecord, String> SUBTITLE = createField(DSL.name("subtitle"), SQLDataType.VARCHAR(64), this, "副标题");

    /**
     * The column <code>creation.creation_business_order.order_price</code>.
     * 商单价格
     */
    public final TableField<BusinessOrderRecord, Long> ORDER_PRICE = createField(DSL.name("order_price"), SQLDataType.BIGINT, this, "商单价格");

    /**
     * The column <code>creation.creation_business_order.begin_time</code>. 开始时间
     */
    public final TableField<BusinessOrderRecord, ZonedDateTime> BEGIN_TIME = createField(DSL.name("begin_time"), SQLDataType.TIMESTAMPWITHTIMEZONE(3), this, "开始时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_business_order.end_time</code>. 结束时间
     */
    public final TableField<BusinessOrderRecord, ZonedDateTime> END_TIME = createField(DSL.name("end_time"), SQLDataType.TIMESTAMPWITHTIMEZONE(3), this, "结束时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_business_order.priority</code>. 优先级
     */
    public final TableField<BusinessOrderRecord, Integer> PRIORITY = createField(DSL.name("priority"), SQLDataType.INTEGER, this, "优先级");

    /**
     * The column <code>creation.creation_business_order.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public final TableField<BusinessOrderRecord, Long> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.BIGINT, this, "任务所有者ID（1表示系统）");

    /**
     * The column <code>creation.creation_business_order.owner_name</code>.
     * 任务所有者名称
     */
    public final TableField<BusinessOrderRecord, String> OWNER_NAME = createField(DSL.name("owner_name"), SQLDataType.VARCHAR(128), this, "任务所有者名称");

    private BusinessOrderTable(Name alias, Table<BusinessOrderRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private BusinessOrderTable(Name alias, Table<BusinessOrderRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("课程"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_business_order</code> table
     * reference
     */
    public BusinessOrderTable(String alias) {
        this(DSL.name(alias), CREATION_BUSINESS_ORDER);
    }

    /**
     * Create an aliased <code>creation.creation_business_order</code> table
     * reference
     */
    public BusinessOrderTable(Name alias) {
        this(alias, CREATION_BUSINESS_ORDER);
    }

    /**
     * Create a <code>creation.creation_business_order</code> table reference
     */
    public BusinessOrderTable() {
        this(DSL.name("creation_business_order"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<BusinessOrderRecord> getPrimaryKey() {
        return Keys.BUSINESS_ORDER_PKEY;
    }

    @Override
    public BusinessOrderTable as(String alias) {
        return new BusinessOrderTable(DSL.name(alias), this);
    }

    @Override
    public BusinessOrderTable as(Name alias) {
        return new BusinessOrderTable(alias, this);
    }

    @Override
    public BusinessOrderTable as(Table<?> alias) {
        return new BusinessOrderTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public BusinessOrderTable rename(String name) {
        return new BusinessOrderTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public BusinessOrderTable rename(Name name) {
        return new BusinessOrderTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public BusinessOrderTable rename(Table<?> name) {
        return new BusinessOrderTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public BusinessOrderTable where(Condition condition) {
        return new BusinessOrderTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public BusinessOrderTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public BusinessOrderTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public BusinessOrderTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public BusinessOrderTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public BusinessOrderTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public BusinessOrderTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public BusinessOrderTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public BusinessOrderTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public BusinessOrderTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
