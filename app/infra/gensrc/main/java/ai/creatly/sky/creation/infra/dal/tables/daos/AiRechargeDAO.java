/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.AiRechargeTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiRecharge;
import ai.creatly.sky.creation.infra.dal.tables.records.AiRechargeRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * AI工具充值
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class AiRechargeDAO extends DAOImpl<AiRechargeRecord, CreationAiRecharge, Long> {

    /**
     * Create a new AiRechargeDAO without any configuration
     */
    public AiRechargeDAO() {
        super(AiRechargeTable.CREATION_AI_RECHARGE, CreationAiRecharge.class);
    }

    /**
     * Create a new AiRechargeDAO with an attached configuration
     */
    @Autowired
    public AiRechargeDAO(Configuration configuration) {
        super(AiRechargeTable.CREATION_AI_RECHARGE, CreationAiRecharge.class, configuration);
    }

    @Override
    public Long getId(CreationAiRecharge object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationAiRecharge> fetchById(Long... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationAiRecharge fetchOneById(Long value) {
        return fetchOne(AiRechargeTable.CREATION_AI_RECHARGE.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationAiRecharge> fetchOptionalById(Long value) {
        return fetchOptional(AiRechargeTable.CREATION_AI_RECHARGE.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>priority BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfPriority(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.PRIORITY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>priority IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByPriority(Long... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.PRIORITY, values);
    }

    /**
     * Fetch records that have <code>price BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfPrice(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.PRICE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>price IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByPrice(Long... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.PRICE, values);
    }

    /**
     * Fetch records that have <code>credit BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfCredit(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.CREDIT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>credit IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByCredit(Long... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.CREDIT, values);
    }

    /**
     * Fetch records that have <code>owner_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfOwnerId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.OWNER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>owner_id IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByOwnerId(Long... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.OWNER_ID, values);
    }

    /**
     * Fetch records that have <code>owner_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfOwnerName(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.OWNER_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>owner_name IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByOwnerName(String... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.OWNER_NAME, values);
    }

    /**
     * Fetch records that have <code>paid_time BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfPaidTime(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.PAID_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>paid_time IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByPaidTime(ZonedDateTime... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.PAID_TIME, values);
    }

    /**
     * Fetch records that have <code>paid_channel BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfPaidChannel(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.PAID_CHANNEL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>paid_channel IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByPaidChannel(String... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.PAID_CHANNEL, values);
    }

    /**
     * Fetch records that have <code>order_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfOrderId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.ORDER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>order_id IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByOrderId(Long... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.ORDER_ID, values);
    }

    /**
     * Fetch records that have <code>code_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfCodeUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.CODE_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>code_url IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByCodeUrl(String... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.CODE_URL, values);
    }

    /**
     * Fetch records that have <code>expire_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfExpireAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.EXPIRE_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>expire_at IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByExpireAt(ZonedDateTime... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.EXPIRE_AT, values);
    }

    /**
     * Fetch records that have <code>pay_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfPayStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.PAY_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>pay_status IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByPayStatus(String... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.PAY_STATUS, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfStatus(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByStatus(Boolean... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.STATUS, values);
    }

    /**
     * Fetch records that have <code>org_code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiRecharge> fetchRangeOfOrgCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiRechargeTable.CREATION_AI_RECHARGE.ORG_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_code IN (values)</code>
     */
    public List<CreationAiRecharge> fetchByOrgCode(String... values) {
        return fetch(AiRechargeTable.CREATION_AI_RECHARGE.ORG_CODE, values);
    }
}
