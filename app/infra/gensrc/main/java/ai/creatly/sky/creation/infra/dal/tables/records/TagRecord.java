/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.TagTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationTag;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 标签
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class TagRecord extends UpdatableRecordImpl<TagRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_tag.id</code>. 主键
     */
    public TagRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_tag.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_tag.created_at</code>. 创建时间
     */
    public TagRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_tag.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_tag.updated_at</code>. 更新时间
     */
    public TagRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_tag.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_tag.creator_id</code>. 创建者id
     */
    public TagRecord setCreatorId(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_tag.creator_id</code>. 创建者id
     */
    public Long getCreatorId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_tag.status</code>. 状态
     */
    public TagRecord setStatus(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_tag.status</code>. 状态
     */
    public String getStatus() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_tag.tag_code</code>. 标签码
     */
    public TagRecord setTagCode(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_tag.tag_code</code>. 标签码
     */
    public String getTagCode() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_tag.tag_name</code>. 标签名
     */
    public TagRecord setTagName(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_tag.tag_name</code>. 标签名
     */
    public String getTagName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_tag.tag_color</code>. 颜色 16进制
     */
    public TagRecord setTagColor(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_tag.tag_color</code>. 颜色 16进制
     */
    public String getTagColor() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached TagRecord
     */
    public TagRecord() {
        super(TagTable.CREATION_TAG);
    }

    /**
     * Create a detached, initialised TagRecord
     */
    public TagRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long creatorId, String status, String tagCode, String tagName, String tagColor) {
        super(TagTable.CREATION_TAG);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCreatorId(creatorId);
        setStatus(status);
        setTagCode(tagCode);
        setTagName(tagName);
        setTagColor(tagColor);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised TagRecord
     */
    public TagRecord(CreationTag value) {
        super(TagTable.CREATION_TAG);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCreatorId(value.getCreatorId());
            setStatus(value.getStatus());
            setTagCode(value.getTagCode());
            setTagName(value.getTagName());
            setTagColor(value.getTagColor());
            resetChangedOnNotNull();
        }
    }
}
