/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.QrcodeScanRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.QrcodeScanRecord;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 权益中心-用户扫码记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class QrcodeScanRecordRecord extends UpdatableRecordImpl<QrcodeScanRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.qrcode_scan_record.id</code>. 主键
     */
    public QrcodeScanRecordRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.created_at</code>. 创建时间
     */
    public QrcodeScanRecordRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.updated_at</code>. 更新时间
     */
    public QrcodeScanRecordRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.issuing_party</code>. 发码方
     */
    public QrcodeScanRecordRecord setIssuingParty(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.issuing_party</code>. 发码方
     */
    public Long getIssuingParty() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.scanning_party</code>. 扫码人
     */
    public QrcodeScanRecordRecord setScanningParty(Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.scanning_party</code>. 扫码人
     */
    public Long getScanningParty() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.code</code>. 码值
     */
    public QrcodeScanRecordRecord setCode(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.code</code>. 码值
     */
    public String getCode() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.type</code>. 二维码
     */
    public QrcodeScanRecordRecord setType(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.type</code>. 二维码
     */
    public String getType() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached QrcodeScanRecordRecord
     */
    public QrcodeScanRecordRecord() {
        super(QrcodeScanRecordTable.QRCODE_SCAN_RECORD);
    }

    /**
     * Create a detached, initialised QrcodeScanRecordRecord
     */
    public QrcodeScanRecordRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long issuingParty, Long scanningParty, String code, String type) {
        super(QrcodeScanRecordTable.QRCODE_SCAN_RECORD);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setIssuingParty(issuingParty);
        setScanningParty(scanningParty);
        setCode(code);
        setType(type);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised QrcodeScanRecordRecord
     */
    public QrcodeScanRecordRecord(QrcodeScanRecord value) {
        super(QrcodeScanRecordTable.QRCODE_SCAN_RECORD);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setIssuingParty(value.getIssuingParty());
            setScanningParty(value.getScanningParty());
            setCode(value.getCode());
            setType(value.getType());
            resetChangedOnNotNull();
        }
    }
}
