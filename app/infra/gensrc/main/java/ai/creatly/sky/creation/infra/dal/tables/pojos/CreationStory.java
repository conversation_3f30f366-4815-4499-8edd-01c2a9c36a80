/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 故事
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationStory implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String name;
    private String content;
    private String storyType;
    private JSONB styleConfig;
    private Long uid;
    private JSONB roles;
    private JSONB creator;
    private String status;
    private String idea;
    private String sourceType;

    public CreationStory() {}

    public CreationStory(CreationStory value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.name = value.name;
        this.content = value.content;
        this.storyType = value.storyType;
        this.styleConfig = value.styleConfig;
        this.uid = value.uid;
        this.roles = value.roles;
        this.creator = value.creator;
        this.status = value.status;
        this.idea = value.idea;
        this.sourceType = value.sourceType;
    }

    public CreationStory(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        String name,
        String content,
        String storyType,
        JSONB styleConfig,
        Long uid,
        JSONB roles,
        JSONB creator,
        @Nullable String status,
        @Nullable String idea,
        @Nullable String sourceType
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.name = name;
        this.content = content;
        this.storyType = storyType;
        this.styleConfig = styleConfig;
        this.uid = uid;
        this.roles = roles;
        this.creator = creator;
        this.status = status;
        this.idea = idea;
        this.sourceType = sourceType;
    }

    /**
     * Getter for <code>creation.creation_story.id</code>. 故事id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_story.id</code>. 故事id
     */
    public CreationStory setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_story.created_at</code>. 创建时间
     */
    public CreationStory setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_story.updated_at</code>. 更新时间
     */
    public CreationStory setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story.name</code>. 故事名
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.creation_story.name</code>. 故事名
     */
    public CreationStory setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story.content</code>. 故事内容
     */
    public String getContent() {
        return this.content;
    }

    /**
     * Setter for <code>creation.creation_story.content</code>. 故事内容
     */
    public CreationStory setContent(String content) {
        this.content = content;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story.story_type</code>. 故事类型
     */
    public String getStoryType() {
        return this.storyType;
    }

    /**
     * Setter for <code>creation.creation_story.story_type</code>. 故事类型
     */
    public CreationStory setStoryType(String storyType) {
        this.storyType = storyType;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story.style_config</code>. 样式配置
     */
    public JSONB getStyleConfig() {
        return this.styleConfig;
    }

    /**
     * Setter for <code>creation.creation_story.style_config</code>. 样式配置
     */
    public CreationStory setStyleConfig(JSONB styleConfig) {
        this.styleConfig = styleConfig;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story.uid</code>. 用户id
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_story.uid</code>. 用户id
     */
    public CreationStory setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story.roles</code>. 故事角色
     */
    public JSONB getRoles() {
        return this.roles;
    }

    /**
     * Setter for <code>creation.creation_story.roles</code>. 故事角色
     */
    public CreationStory setRoles(JSONB roles) {
        this.roles = roles;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return this.creator;
    }

    /**
     * Setter for <code>creation.creation_story.creator</code>. 创建者
     */
    public CreationStory setCreator(JSONB creator) {
        this.creator = creator;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story.status</code>. 状态
     */
    @Nullable
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_story.status</code>. 状态
     */
    public CreationStory setStatus(@Nullable String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story.idea</code>. 故事灵感
     */
    @Nullable
    public String getIdea() {
        return this.idea;
    }

    /**
     * Setter for <code>creation.creation_story.idea</code>. 故事灵感
     */
    public CreationStory setIdea(@Nullable String idea) {
        this.idea = idea;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story.source_type</code>. 来源类型
     */
    @Nullable
    public String getSourceType() {
        return this.sourceType;
    }

    /**
     * Setter for <code>creation.creation_story.source_type</code>. 来源类型
     */
    public CreationStory setSourceType(@Nullable String sourceType) {
        this.sourceType = sourceType;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationStory other = (CreationStory) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.content == null) {
            if (other.content != null)
                return false;
        }
        else if (!this.content.equals(other.content))
            return false;
        if (this.storyType == null) {
            if (other.storyType != null)
                return false;
        }
        else if (!this.storyType.equals(other.storyType))
            return false;
        if (this.styleConfig == null) {
            if (other.styleConfig != null)
                return false;
        }
        else if (!this.styleConfig.equals(other.styleConfig))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.roles == null) {
            if (other.roles != null)
                return false;
        }
        else if (!this.roles.equals(other.roles))
            return false;
        if (this.creator == null) {
            if (other.creator != null)
                return false;
        }
        else if (!this.creator.equals(other.creator))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.idea == null) {
            if (other.idea != null)
                return false;
        }
        else if (!this.idea.equals(other.idea))
            return false;
        if (this.sourceType == null) {
            if (other.sourceType != null)
                return false;
        }
        else if (!this.sourceType.equals(other.sourceType))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.content == null) ? 0 : this.content.hashCode());
        result = prime * result + ((this.storyType == null) ? 0 : this.storyType.hashCode());
        result = prime * result + ((this.styleConfig == null) ? 0 : this.styleConfig.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.roles == null) ? 0 : this.roles.hashCode());
        result = prime * result + ((this.creator == null) ? 0 : this.creator.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.idea == null) ? 0 : this.idea.hashCode());
        result = prime * result + ((this.sourceType == null) ? 0 : this.sourceType.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationStory (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(name);
        sb.append(", ").append(content);
        sb.append(", ").append(storyType);
        sb.append(", ").append(styleConfig);
        sb.append(", ").append(uid);
        sb.append(", ").append(roles);
        sb.append(", ").append(creator);
        sb.append(", ").append(status);
        sb.append(", ").append(idea);
        sb.append(", ").append(sourceType);

        sb.append(")");
        return sb.toString();
    }
}
