/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.QrcodeRecordRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 权益中心-码业务平台
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class QrcodeRecordTable extends TableImpl<QrcodeRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.qrcode_record</code>
     */
    public static final QrcodeRecordTable QRCODE_RECORD = new QrcodeRecordTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<QrcodeRecordRecord> getRecordType() {
        return QrcodeRecordRecord.class;
    }

    /**
     * The column <code>creation.qrcode_record.id</code>. 主键
     */
    public final TableField<QrcodeRecordRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.qrcode_record.created_at</code>. 创建时间
     */
    public final TableField<QrcodeRecordRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.qrcode_record.updated_at</code>. 更新时间
     */
    public final TableField<QrcodeRecordRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.qrcode_record.issuer_id</code>. 任务所有者ID（1表示平台）
     */
    public final TableField<QrcodeRecordRecord, Long> ISSUER_ID = createField(DSL.name("issuer_id"), SQLDataType.BIGINT.nullable(false), this, "任务所有者ID（1表示平台）");

    /**
     * The column <code>creation.qrcode_record.biz_scene</code>. 业务场景
     */
    public final TableField<QrcodeRecordRecord, String> BIZ_SCENE = createField(DSL.name("biz_scene"), SQLDataType.VARCHAR(64).nullable(false), this, "业务场景");

    /**
     * The column <code>creation.qrcode_record.code_content</code>. 码内容
     */
    public final TableField<QrcodeRecordRecord, JSONB> CODE_CONTENT = createField(DSL.name("code_content"), SQLDataType.JSONB.nullable(false), this, "码内容");

    /**
     * The column <code>creation.qrcode_record.code</code>. 码值，全局唯一
     */
    public final TableField<QrcodeRecordRecord, String> CODE = createField(DSL.name("code"), SQLDataType.VARCHAR(255).nullable(false), this, "码值，全局唯一");

    /**
     * The column <code>creation.qrcode_record.expire_time</code>. 码的有效期
     */
    public final TableField<QrcodeRecordRecord, ZonedDateTime> EXPIRE_TIME = createField(DSL.name("expire_time"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false), this, "码的有效期", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.qrcode_record.status</code>. 状态
     */
    public final TableField<QrcodeRecordRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(64).nullable(false), this, "状态");

    /**
     * The column <code>creation.qrcode_record.type</code>. 永久，一次性，时间
     */
    public final TableField<QrcodeRecordRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(64).nullable(false), this, "永久，一次性，时间");

    /**
     * The column <code>creation.qrcode_record.max_threshold</code>. 最大阈值
     */
    public final TableField<QrcodeRecordRecord, Integer> MAX_THRESHOLD = createField(DSL.name("max_threshold"), SQLDataType.INTEGER, this, "最大阈值");

    private QrcodeRecordTable(Name alias, Table<QrcodeRecordRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private QrcodeRecordTable(Name alias, Table<QrcodeRecordRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("权益中心-码业务平台"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.qrcode_record</code> table reference
     */
    public QrcodeRecordTable(String alias) {
        this(DSL.name(alias), QRCODE_RECORD);
    }

    /**
     * Create an aliased <code>creation.qrcode_record</code> table reference
     */
    public QrcodeRecordTable(Name alias) {
        this(alias, QRCODE_RECORD);
    }

    /**
     * Create a <code>creation.qrcode_record</code> table reference
     */
    public QrcodeRecordTable() {
        this(DSL.name("qrcode_record"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<QrcodeRecordRecord> getPrimaryKey() {
        return Keys.QRCODE_RECORD_PKEY;
    }

    @Override
    public List<UniqueKey<QrcodeRecordRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.QRCODE_RECORD_CODE_KEY);
    }

    @Override
    public QrcodeRecordTable as(String alias) {
        return new QrcodeRecordTable(DSL.name(alias), this);
    }

    @Override
    public QrcodeRecordTable as(Name alias) {
        return new QrcodeRecordTable(alias, this);
    }

    @Override
    public QrcodeRecordTable as(Table<?> alias) {
        return new QrcodeRecordTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public QrcodeRecordTable rename(String name) {
        return new QrcodeRecordTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public QrcodeRecordTable rename(Name name) {
        return new QrcodeRecordTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public QrcodeRecordTable rename(Table<?> name) {
        return new QrcodeRecordTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public QrcodeRecordTable where(Condition condition) {
        return new QrcodeRecordTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public QrcodeRecordTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public QrcodeRecordTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public QrcodeRecordTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public QrcodeRecordTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public QrcodeRecordTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public QrcodeRecordTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public QrcodeRecordTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public QrcodeRecordTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public QrcodeRecordTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
