/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.DramaTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationDrama;

import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 剧情
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class DramaRecord extends UpdatableRecordImpl<DramaRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_drama.id</code>. 主键
     */
    public DramaRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_drama.created_at</code>. 创建时间
     */
    public DramaRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_drama.updated_at</code>. 更新时间
     */
    public DramaRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_drama.uid</code>. 用户ID
     */
    public DramaRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.uid</code>. 用户ID
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_drama.name</code>. 剧情名称
     */
    public DramaRecord setName(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.name</code>. 剧情名称
     */
    public String getName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_drama.type</code>. 剧情类型
     */
    public DramaRecord setType(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.type</code>. 剧情类型
     */
    public String getType() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_drama.status</code>. 剧情状态
     */
    public DramaRecord setStatus(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.status</code>. 剧情状态
     */
    public String getStatus() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_drama.product_categories</code>.
     * 适用的产品类目
     */
    public DramaRecord setProductCategories(JSONB value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.product_categories</code>.
     * 适用的产品类目
     */
    public JSONB getProductCategories() {
        return (JSONB) get(7);
    }

    /**
     * Setter for <code>creation.creation_drama.crowd_categories</code>. 适用的人群类目
     */
    public DramaRecord setCrowdCategories(JSONB value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.crowd_categories</code>. 适用的人群类目
     */
    public JSONB getCrowdCategories() {
        return (JSONB) get(8);
    }

    /**
     * Setter for <code>creation.creation_drama.videos</code>. 剧情相关视频
     */
    public DramaRecord setVideos(JSONB value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.videos</code>. 剧情相关视频
     */
    public JSONB getVideos() {
        return (JSONB) get(9);
    }

    /**
     * Setter for <code>creation.creation_drama.duration</code>. 剧情总时长
     */
    public DramaRecord setDuration(Duration value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.duration</code>. 剧情总时长
     */
    public Duration getDuration() {
        return (Duration) get(10);
    }

    /**
     * Setter for <code>creation.creation_drama.roles</code>. 剧情角色列表
     */
    public DramaRecord setRoles(JSONB value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.roles</code>. 剧情角色列表
     */
    public JSONB getRoles() {
        return (JSONB) get(11);
    }

    /**
     * Setter for <code>creation.creation_drama.cover_url</code>. 剧情封面图地址（OSS协议）
     */
    public DramaRecord setCoverUrl(@Nullable String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.cover_url</code>. 剧情封面图地址（OSS协议）
     */
    @Nullable
    public String getCoverUrl() {
        return (String) get(12);
    }

    /**
     * Setter for <code>creation.creation_drama.annotation</code>. 剧情标注
     */
    public DramaRecord setAnnotation(JSONB value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.annotation</code>. 剧情标注
     */
    public JSONB getAnnotation() {
        return (JSONB) get(13);
    }

    /**
     * Setter for <code>creation.creation_drama.stats</code>. 剧情统计信息
     */
    public DramaRecord setStats(@Nullable JSONB value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.stats</code>. 剧情统计信息
     */
    @Nullable
    public JSONB getStats() {
        return (JSONB) get(14);
    }

    /**
     * Setter for <code>creation.creation_drama.review</code>. 剧情评审说明
     */
    public DramaRecord setReview(@Nullable JSONB value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.review</code>. 剧情评审说明
     */
    @Nullable
    public JSONB getReview() {
        return (JSONB) get(15);
    }

    /**
     * Setter for <code>creation.creation_drama.content</code>. 剧情内容
     */
    public DramaRecord setContent(@Nullable JSONB value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.content</code>. 剧情内容
     */
    @Nullable
    public JSONB getContent() {
        return (JSONB) get(16);
    }

    /**
     * Setter for <code>creation.creation_drama.local_sync_status</code>.
     * 本地剧情同步状态
     */
    public DramaRecord setLocalSyncStatus(@Nullable String value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_drama.local_sync_status</code>.
     * 本地剧情同步状态
     */
    @Nullable
    public String getLocalSyncStatus() {
        return (String) get(17);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DramaRecord
     */
    public DramaRecord() {
        super(DramaTable.CREATION_DRAMA);
    }

    /**
     * Create a detached, initialised DramaRecord
     */
    public DramaRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String name, String type, String status, JSONB productCategories, JSONB crowdCategories, JSONB videos, Duration duration, JSONB roles, @Nullable String coverUrl, JSONB annotation, @Nullable JSONB stats, @Nullable JSONB review, @Nullable JSONB content, @Nullable String localSyncStatus) {
        super(DramaTable.CREATION_DRAMA);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setName(name);
        setType(type);
        setStatus(status);
        setProductCategories(productCategories);
        setCrowdCategories(crowdCategories);
        setVideos(videos);
        setDuration(duration);
        setRoles(roles);
        setCoverUrl(coverUrl);
        setAnnotation(annotation);
        setStats(stats);
        setReview(review);
        setContent(content);
        setLocalSyncStatus(localSyncStatus);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised DramaRecord
     */
    public DramaRecord(CreationDrama value) {
        super(DramaTable.CREATION_DRAMA);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setName(value.getName());
            setType(value.getType());
            setStatus(value.getStatus());
            setProductCategories(value.getProductCategories());
            setCrowdCategories(value.getCrowdCategories());
            setVideos(value.getVideos());
            setDuration(value.getDuration());
            setRoles(value.getRoles());
            setCoverUrl(value.getCoverUrl());
            setAnnotation(value.getAnnotation());
            setStats(value.getStats());
            setReview(value.getReview());
            setContent(value.getContent());
            setLocalSyncStatus(value.getLocalSyncStatus());
            resetChangedOnNotNull();
        }
    }
}
