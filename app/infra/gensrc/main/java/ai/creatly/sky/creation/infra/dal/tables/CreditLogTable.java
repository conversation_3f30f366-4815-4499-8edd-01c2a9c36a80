/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.CreditLogRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 用户余额账户流水
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreditLogTable extends TableImpl<CreditLogRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_credit_log</code>
     */
    public static final CreditLogTable CREATION_CREDIT_LOG = new CreditLogTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CreditLogRecord> getRecordType() {
        return CreditLogRecord.class;
    }

    /**
     * The column <code>creation.creation_credit_log.id</code>. 主键
     */
    public final TableField<CreditLogRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_credit_log.created_at</code>. 创建时间
     */
    public final TableField<CreditLogRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_credit_log.updated_at</code>. 更新时间
     */
    public final TableField<CreditLogRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_credit_log.uid</code>. 用户ID
     */
    public final TableField<CreditLogRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户ID");

    /**
     * The column <code>creation.creation_credit_log.biz_no</code>. 业务标识
     */
    public final TableField<CreditLogRecord, String> BIZ_NO = createField(DSL.name("biz_no"), SQLDataType.VARCHAR(64).nullable(false), this, "业务标识");

    /**
     * The column <code>creation.creation_credit_log.biz_type</code>. 关联业务类型
     */
    public final TableField<CreditLogRecord, String> BIZ_TYPE = createField(DSL.name("biz_type"), SQLDataType.VARCHAR(32).nullable(false), this, "关联业务类型");

    /**
     * The column <code>creation.creation_credit_log.type</code>. 账户流水分类（收入/支出）
     */
    public final TableField<CreditLogRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(16).nullable(false), this, "账户流水分类（收入/支出）");

    /**
     * The column <code>creation.creation_credit_log.amount</code>.
     * 余额变动总量（正数为收入，负数为支出）
     */
    public final TableField<CreditLogRecord, Long> AMOUNT = createField(DSL.name("amount"), SQLDataType.BIGINT.nullable(false), this, "余额变动总量（正数为收入，负数为支出）");

    /**
     * The column <code>creation.creation_credit_log.credit_deltas</code>.
     * 余额变动列表
     */
    public final TableField<CreditLogRecord, JSONB> CREDIT_DELTAS = createField(DSL.name("credit_deltas"), SQLDataType.JSONB.nullable(false), this, "余额变动列表");

    private CreditLogTable(Name alias, Table<CreditLogRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private CreditLogTable(Name alias, Table<CreditLogRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("用户余额账户流水"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_credit_log</code> table
     * reference
     */
    public CreditLogTable(String alias) {
        this(DSL.name(alias), CREATION_CREDIT_LOG);
    }

    /**
     * Create an aliased <code>creation.creation_credit_log</code> table
     * reference
     */
    public CreditLogTable(Name alias) {
        this(alias, CREATION_CREDIT_LOG);
    }

    /**
     * Create a <code>creation.creation_credit_log</code> table reference
     */
    public CreditLogTable() {
        this(DSL.name("creation_credit_log"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<CreditLogRecord> getPrimaryKey() {
        return Keys.CREATION_CREDIT_LOG_PKEY;
    }

    @Override
    public List<UniqueKey<CreditLogRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CREATION_CREDIT_LOG_UID_BIZ_NO_BIZ_TYPE_TYPE_KEY);
    }

    @Override
    public CreditLogTable as(String alias) {
        return new CreditLogTable(DSL.name(alias), this);
    }

    @Override
    public CreditLogTable as(Name alias) {
        return new CreditLogTable(alias, this);
    }

    @Override
    public CreditLogTable as(Table<?> alias) {
        return new CreditLogTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public CreditLogTable rename(String name) {
        return new CreditLogTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public CreditLogTable rename(Name name) {
        return new CreditLogTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public CreditLogTable rename(Table<?> name) {
        return new CreditLogTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CreditLogTable where(Condition condition) {
        return new CreditLogTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CreditLogTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CreditLogTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CreditLogTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CreditLogTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CreditLogTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CreditLogTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CreditLogTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CreditLogTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CreditLogTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
