/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.CreditLogTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationCreditLog;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户余额账户流水
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreditLogRecord extends UpdatableRecordImpl<CreditLogRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_credit_log.id</code>. 主键
     */
    public CreditLogRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_credit_log.created_at</code>. 创建时间
     */
    public CreditLogRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_credit_log.updated_at</code>. 更新时间
     */
    public CreditLogRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_credit_log.uid</code>. 用户ID
     */
    public CreditLogRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.uid</code>. 用户ID
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_credit_log.biz_no</code>. 业务标识
     */
    public CreditLogRecord setBizNo(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.biz_no</code>. 业务标识
     */
    public String getBizNo() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_credit_log.biz_type</code>. 关联业务类型
     */
    public CreditLogRecord setBizType(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.biz_type</code>. 关联业务类型
     */
    public String getBizType() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_credit_log.type</code>. 账户流水分类（收入/支出）
     */
    public CreditLogRecord setType(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.type</code>. 账户流水分类（收入/支出）
     */
    public String getType() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_credit_log.amount</code>.
     * 余额变动总量（正数为收入，负数为支出）
     */
    public CreditLogRecord setAmount(Long value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.amount</code>.
     * 余额变动总量（正数为收入，负数为支出）
     */
    public Long getAmount() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>creation.creation_credit_log.credit_deltas</code>.
     * 余额变动列表
     */
    public CreditLogRecord setCreditDeltas(JSONB value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_log.credit_deltas</code>.
     * 余额变动列表
     */
    public JSONB getCreditDeltas() {
        return (JSONB) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CreditLogRecord
     */
    public CreditLogRecord() {
        super(CreditLogTable.CREATION_CREDIT_LOG);
    }

    /**
     * Create a detached, initialised CreditLogRecord
     */
    public CreditLogRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String bizNo, String bizType, String type, Long amount, JSONB creditDeltas) {
        super(CreditLogTable.CREATION_CREDIT_LOG);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setBizNo(bizNo);
        setBizType(bizType);
        setType(type);
        setAmount(amount);
        setCreditDeltas(creditDeltas);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised CreditLogRecord
     */
    public CreditLogRecord(CreationCreditLog value) {
        super(CreditLogTable.CREATION_CREDIT_LOG);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setBizNo(value.getBizNo());
            setBizType(value.getBizType());
            setType(value.getType());
            setAmount(value.getAmount());
            setCreditDeltas(value.getCreditDeltas());
            resetChangedOnNotNull();
        }
    }
}
