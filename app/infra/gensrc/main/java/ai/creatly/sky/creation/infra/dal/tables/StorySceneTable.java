/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.StorySceneRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 故事场景
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class StorySceneTable extends TableImpl<StorySceneRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_story_scene</code>
     */
    public static final StorySceneTable CREATION_STORY_SCENE = new StorySceneTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StorySceneRecord> getRecordType() {
        return StorySceneRecord.class;
    }

    /**
     * The column <code>creation.creation_story_scene.id</code>. 场景id
     */
    public final TableField<StorySceneRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "场景id");

    /**
     * The column <code>creation.creation_story_scene.created_at</code>. 创建时间
     */
    public final TableField<StorySceneRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_story_scene.updated_at</code>. 更新时间
     */
    public final TableField<StorySceneRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_story_scene.story_id</code>. 故事id
     */
    public final TableField<StorySceneRecord, Long> STORY_ID = createField(DSL.name("story_id"), SQLDataType.BIGINT.nullable(false), this, "故事id");

    /**
     * The column <code>creation.creation_story_scene.name</code>. 场景名
     */
    public final TableField<StorySceneRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false), this, "场景名");

    /**
     * The column <code>creation.creation_story_scene.index</code>. 场景序号
     */
    public final TableField<StorySceneRecord, Integer> INDEX = createField(DSL.name("index"), SQLDataType.INTEGER.nullable(false), this, "场景序号");

    /**
     * The column <code>creation.creation_story_scene.title</code>. 场景标题
     */
    public final TableField<StorySceneRecord, String> TITLE = createField(DSL.name("title"), SQLDataType.VARCHAR(512).nullable(false), this, "场景标题");

    /**
     * The column <code>creation.creation_story_scene.config</code>. 场景配置
     */
    public final TableField<StorySceneRecord, JSONB> CONFIG = createField(DSL.name("config"), SQLDataType.JSONB.nullable(false), this, "场景配置");

    /**
     * The column <code>creation.creation_story_scene.creator</code>. 创建者
     */
    public final TableField<StorySceneRecord, JSONB> CREATOR = createField(DSL.name("creator"), SQLDataType.JSONB.nullable(false), this, "创建者");

    /**
     * The column <code>creation.creation_story_scene.status</code>. 状态
     */
    public final TableField<StorySceneRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(20).nullable(false), this, "状态");

    private StorySceneTable(Name alias, Table<StorySceneRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private StorySceneTable(Name alias, Table<StorySceneRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("故事场景"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_story_scene</code> table
     * reference
     */
    public StorySceneTable(String alias) {
        this(DSL.name(alias), CREATION_STORY_SCENE);
    }

    /**
     * Create an aliased <code>creation.creation_story_scene</code> table
     * reference
     */
    public StorySceneTable(Name alias) {
        this(alias, CREATION_STORY_SCENE);
    }

    /**
     * Create a <code>creation.creation_story_scene</code> table reference
     */
    public StorySceneTable() {
        this(DSL.name("creation_story_scene"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<StorySceneRecord> getPrimaryKey() {
        return Keys.CREATION_STORY_SCENE_PKEY;
    }

    @Override
    public StorySceneTable as(String alias) {
        return new StorySceneTable(DSL.name(alias), this);
    }

    @Override
    public StorySceneTable as(Name alias) {
        return new StorySceneTable(alias, this);
    }

    @Override
    public StorySceneTable as(Table<?> alias) {
        return new StorySceneTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public StorySceneTable rename(String name) {
        return new StorySceneTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public StorySceneTable rename(Name name) {
        return new StorySceneTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public StorySceneTable rename(Table<?> name) {
        return new StorySceneTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StorySceneTable where(Condition condition) {
        return new StorySceneTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StorySceneTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StorySceneTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StorySceneTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public StorySceneTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public StorySceneTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public StorySceneTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public StorySceneTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StorySceneTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StorySceneTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
