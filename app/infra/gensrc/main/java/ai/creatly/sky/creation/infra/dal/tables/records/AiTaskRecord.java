/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.AiTaskTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiTask;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * AI创作任务
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiTaskRecord extends UpdatableRecordImpl<AiTaskRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_ai_task.id</code>. 主键
     */
    public AiTaskRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_ai_task.created_at</code>. 创建时间
     */
    public AiTaskRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_ai_task.updated_at</code>. 更新时间
     */
    public AiTaskRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_ai_task.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public AiTaskRecord setOwnerId(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public Long getOwnerId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_ai_task.owner_name</code>. 任务所有者名称
     */
    public AiTaskRecord setOwnerName(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.owner_name</code>. 任务所有者名称
     */
    public String getOwnerName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_ai_task.task_type</code>. 任务类型
     */
    public AiTaskRecord setTaskType(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.task_type</code>. 任务类型
     */
    public String getTaskType() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_ai_task.task_name</code>. 任务名称
     */
    public AiTaskRecord setTaskName(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.task_name</code>. 任务名称
     */
    public String getTaskName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_ai_task.status</code>. 状态
     */
    public AiTaskRecord setStatus(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.status</code>. 状态
     */
    public String getStatus() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_ai_task.biz_status</code>. 关联外部业务状态
     */
    public AiTaskRecord setBizStatus(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.biz_status</code>. 关联外部业务状态
     */
    public String getBizStatus() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_ai_task.biz_type</code>. 关联外部业务类型
     */
    public AiTaskRecord setBizType(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.biz_type</code>. 关联外部业务类型
     */
    public String getBizType() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_ai_task.biz_no</code>. 关联外部业务单号
     */
    public AiTaskRecord setBizNo(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.biz_no</code>. 关联外部业务单号
     */
    public String getBizNo() {
        return (String) get(10);
    }

    /**
     * Setter for <code>creation.creation_ai_task.sub_biz_no</code>. 关联外部业务子单号
     */
    public AiTaskRecord setSubBizNo(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.sub_biz_no</code>. 关联外部业务子单号
     */
    public String getSubBizNo() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_ai_task.exec_status</code>. 执行状态
     */
    public AiTaskRecord setExecStatus(String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.exec_status</code>. 执行状态
     */
    public String getExecStatus() {
        return (String) get(12);
    }

    /**
     * Setter for <code>creation.creation_ai_task.auto_exec</code>. 是否自动执行
     */
    public AiTaskRecord setAutoExec(Boolean value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.auto_exec</code>. 是否自动执行
     */
    public Boolean getAutoExec() {
        return (Boolean) get(13);
    }

    /**
     * Setter for <code>creation.creation_ai_task.priority</code>. 任务优先级（默认100）
     */
    public AiTaskRecord setPriority(Integer value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.priority</code>. 任务优先级（默认100）
     */
    public Integer getPriority() {
        return (Integer) get(14);
    }

    /**
     * Setter for <code>creation.creation_ai_task.sharding</code>. 任务随机分片号
     */
    public AiTaskRecord setSharding(Integer value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.sharding</code>. 任务随机分片号
     */
    public Integer getSharding() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>creation.creation_ai_task.started_at</code>. 开始运行时间
     */
    public AiTaskRecord setStartedAt(@Nullable ZonedDateTime value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.started_at</code>. 开始运行时间
     */
    @Nullable
    public ZonedDateTime getStartedAt() {
        return (ZonedDateTime) get(16);
    }

    /**
     * Setter for <code>creation.creation_ai_task.finished_at</code>. 运行完成时间
     */
    public AiTaskRecord setFinishedAt(@Nullable ZonedDateTime value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.finished_at</code>. 运行完成时间
     */
    @Nullable
    public ZonedDateTime getFinishedAt() {
        return (ZonedDateTime) get(17);
    }

    /**
     * Setter for <code>creation.creation_ai_task.shared_users</code>. 附加用户列表
     */
    public AiTaskRecord setSharedUsers(JSONB[] value) {
        set(18, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.shared_users</code>. 附加用户列表
     */
    public JSONB[] getSharedUsers() {
        return (JSONB[]) get(18);
    }

    /**
     * Setter for <code>creation.creation_ai_task.biz_params</code>. 业务参数
     */
    public AiTaskRecord setBizParams(JSONB value) {
        set(19, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.biz_params</code>. 业务参数
     */
    public JSONB getBizParams() {
        return (JSONB) get(19);
    }

    /**
     * Setter for <code>creation.creation_ai_task.biz_result</code>. 业务结果
     */
    public AiTaskRecord setBizResult(JSONB value) {
        set(20, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.biz_result</code>. 业务结果
     */
    public JSONB getBizResult() {
        return (JSONB) get(20);
    }

    /**
     * Setter for <code>creation.creation_ai_task.sys_params</code>. 系统参数
     */
    public AiTaskRecord setSysParams(JSONB value) {
        set(21, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.sys_params</code>. 系统参数
     */
    public JSONB getSysParams() {
        return (JSONB) get(21);
    }

    /**
     * Setter for <code>creation.creation_ai_task.sys_result</code>. 系统结果
     */
    public AiTaskRecord setSysResult(JSONB value) {
        set(22, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.sys_result</code>. 系统结果
     */
    public JSONB getSysResult() {
        return (JSONB) get(22);
    }

    /**
     * Setter for <code>creation.creation_ai_task.exec_info</code>. 执行过程信息
     */
    public AiTaskRecord setExecInfo(JSONB value) {
        set(23, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.exec_info</code>. 执行过程信息
     */
    public JSONB getExecInfo() {
        return (JSONB) get(23);
    }

    /**
     * Setter for <code>creation.creation_ai_task.creator</code>. 创建人
     */
    public AiTaskRecord setCreator(JSONB value) {
        set(24, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.creator</code>. 创建人
     */
    public JSONB getCreator() {
        return (JSONB) get(24);
    }

    /**
     * Setter for <code>creation.creation_ai_task.biz_exec_info</code>. 业务执行过程信息
     */
    public AiTaskRecord setBizExecInfo(@Nullable JSONB value) {
        set(25, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.biz_exec_info</code>. 业务执行过程信息
     */
    @Nullable
    public JSONB getBizExecInfo() {
        return (JSONB) get(25);
    }

    /**
     * Setter for <code>creation.creation_ai_task.delayed_at</code>. 延迟到指定时间执行
     */
    public AiTaskRecord setDelayedAt(@Nullable ZonedDateTime value) {
        set(26, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.delayed_at</code>. 延迟到指定时间执行
     */
    @Nullable
    public ZonedDateTime getDelayedAt() {
        return (ZonedDateTime) get(26);
    }

    /**
     * Setter for <code>creation.creation_ai_task.org_code</code>. 组织代码
     */
    public AiTaskRecord setOrgCode(@Nullable String value) {
        set(27, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_task.org_code</code>. 组织代码
     */
    @Nullable
    public String getOrgCode() {
        return (String) get(27);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AiTaskRecord
     */
    public AiTaskRecord() {
        super(AiTaskTable.CREATION_AI_TASK);
    }

    /**
     * Create a detached, initialised AiTaskRecord
     */
    public AiTaskRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long ownerId, String ownerName, String taskType, String taskName, String status, String bizStatus, String bizType, String bizNo, String subBizNo, String execStatus, Boolean autoExec, Integer priority, Integer sharding, @Nullable ZonedDateTime startedAt, @Nullable ZonedDateTime finishedAt, JSONB[] sharedUsers, JSONB bizParams, JSONB bizResult, JSONB sysParams, JSONB sysResult, JSONB execInfo, JSONB creator, @Nullable JSONB bizExecInfo, @Nullable ZonedDateTime delayedAt, @Nullable String orgCode) {
        super(AiTaskTable.CREATION_AI_TASK);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setOwnerId(ownerId);
        setOwnerName(ownerName);
        setTaskType(taskType);
        setTaskName(taskName);
        setStatus(status);
        setBizStatus(bizStatus);
        setBizType(bizType);
        setBizNo(bizNo);
        setSubBizNo(subBizNo);
        setExecStatus(execStatus);
        setAutoExec(autoExec);
        setPriority(priority);
        setSharding(sharding);
        setStartedAt(startedAt);
        setFinishedAt(finishedAt);
        setSharedUsers(sharedUsers);
        setBizParams(bizParams);
        setBizResult(bizResult);
        setSysParams(sysParams);
        setSysResult(sysResult);
        setExecInfo(execInfo);
        setCreator(creator);
        setBizExecInfo(bizExecInfo);
        setDelayedAt(delayedAt);
        setOrgCode(orgCode);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised AiTaskRecord
     */
    public AiTaskRecord(CreationAiTask value) {
        super(AiTaskTable.CREATION_AI_TASK);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setOwnerId(value.getOwnerId());
            setOwnerName(value.getOwnerName());
            setTaskType(value.getTaskType());
            setTaskName(value.getTaskName());
            setStatus(value.getStatus());
            setBizStatus(value.getBizStatus());
            setBizType(value.getBizType());
            setBizNo(value.getBizNo());
            setSubBizNo(value.getSubBizNo());
            setExecStatus(value.getExecStatus());
            setAutoExec(value.getAutoExec());
            setPriority(value.getPriority());
            setSharding(value.getSharding());
            setStartedAt(value.getStartedAt());
            setFinishedAt(value.getFinishedAt());
            setSharedUsers(value.getSharedUsers());
            setBizParams(value.getBizParams());
            setBizResult(value.getBizResult());
            setSysParams(value.getSysParams());
            setSysResult(value.getSysResult());
            setExecInfo(value.getExecInfo());
            setCreator(value.getCreator());
            setBizExecInfo(value.getBizExecInfo());
            setDelayedAt(value.getDelayedAt());
            setOrgCode(value.getOrgCode());
            resetChangedOnNotNull();
        }
    }
}
