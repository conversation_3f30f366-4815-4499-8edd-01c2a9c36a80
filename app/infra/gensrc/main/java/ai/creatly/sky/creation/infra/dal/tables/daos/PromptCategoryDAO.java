/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.PromptCategoryTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationPromptCategory;
import ai.creatly.sky.creation.infra.dal.tables.records.PromptCategoryRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 提示词类目
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class PromptCategoryDAO extends DAOImpl<PromptCategoryRecord, CreationPromptCategory, Long> {

    /**
     * Create a new PromptCategoryDAO without any configuration
     */
    public PromptCategoryDAO() {
        super(PromptCategoryTable.CREATION_PROMPT_CATEGORY, CreationPromptCategory.class);
    }

    /**
     * Create a new PromptCategoryDAO with an attached configuration
     */
    @Autowired
    public PromptCategoryDAO(Configuration configuration) {
        super(PromptCategoryTable.CREATION_PROMPT_CATEGORY, CreationPromptCategory.class, configuration);
    }

    @Override
    public Long getId(CreationPromptCategory object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptCategory> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(PromptCategoryTable.CREATION_PROMPT_CATEGORY.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationPromptCategory> fetchById(Long... values) {
        return fetch(PromptCategoryTable.CREATION_PROMPT_CATEGORY.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationPromptCategory fetchOneById(Long value) {
        return fetchOne(PromptCategoryTable.CREATION_PROMPT_CATEGORY.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationPromptCategory> fetchOptionalById(Long value) {
        return fetchOptional(PromptCategoryTable.CREATION_PROMPT_CATEGORY.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptCategory> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(PromptCategoryTable.CREATION_PROMPT_CATEGORY.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationPromptCategory> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(PromptCategoryTable.CREATION_PROMPT_CATEGORY.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptCategory> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(PromptCategoryTable.CREATION_PROMPT_CATEGORY.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationPromptCategory> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(PromptCategoryTable.CREATION_PROMPT_CATEGORY.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>biz_source BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptCategory> fetchRangeOfBizSource(String lowerInclusive, String upperInclusive) {
        return fetchRange(PromptCategoryTable.CREATION_PROMPT_CATEGORY.BIZ_SOURCE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_source IN (values)</code>
     */
    public List<CreationPromptCategory> fetchByBizSource(String... values) {
        return fetch(PromptCategoryTable.CREATION_PROMPT_CATEGORY.BIZ_SOURCE, values);
    }

    /**
     * Fetch records that have <code>code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptCategory> fetchRangeOfCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(PromptCategoryTable.CREATION_PROMPT_CATEGORY.CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>code IN (values)</code>
     */
    public List<CreationPromptCategory> fetchByCode(String... values) {
        return fetch(PromptCategoryTable.CREATION_PROMPT_CATEGORY.CODE, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptCategory> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(PromptCategoryTable.CREATION_PROMPT_CATEGORY.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<CreationPromptCategory> fetchByName(String... values) {
        return fetch(PromptCategoryTable.CREATION_PROMPT_CATEGORY.NAME, values);
    }

    /**
     * Fetch records that have <code>en_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptCategory> fetchRangeOfEnName(String lowerInclusive, String upperInclusive) {
        return fetchRange(PromptCategoryTable.CREATION_PROMPT_CATEGORY.EN_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>en_name IN (values)</code>
     */
    public List<CreationPromptCategory> fetchByEnName(String... values) {
        return fetch(PromptCategoryTable.CREATION_PROMPT_CATEGORY.EN_NAME, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationPromptCategory> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(PromptCategoryTable.CREATION_PROMPT_CATEGORY.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CreationPromptCategory> fetchByStatus(String... values) {
        return fetch(PromptCategoryTable.CREATION_PROMPT_CATEGORY.STATUS, values);
    }
}
