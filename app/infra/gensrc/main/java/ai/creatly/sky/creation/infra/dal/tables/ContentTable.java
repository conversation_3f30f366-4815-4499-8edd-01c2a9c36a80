/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.ContentRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 星耀内容大宽表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class ContentTable extends TableImpl<ContentRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_content</code>
     */
    public static final ContentTable CREATION_CONTENT = new ContentTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ContentRecord> getRecordType() {
        return ContentRecord.class;
    }

    /**
     * The column <code>creation.creation_content.id</code>. 主键
     */
    public final TableField<ContentRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_content.created_at</code>. 创建时间
     */
    public final TableField<ContentRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_content.updated_at</code>. 更新时间
     */
    public final TableField<ContentRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_content.source</code>. 三方平台方
     */
    public final TableField<ContentRecord, String> SOURCE = createField(DSL.name("source"), SQLDataType.VARCHAR(255).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "三方平台方");

    /**
     * The column <code>creation.creation_content.source_content_url</code>.
     * 三方平台内容链接
     */
    public final TableField<ContentRecord, String> SOURCE_CONTENT_URL = createField(DSL.name("source_content_url"), SQLDataType.VARCHAR(255).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "三方平台内容链接");

    /**
     * The column <code>creation.creation_content.source_category</code>. 三方平台行业
     */
    public final TableField<ContentRecord, String> SOURCE_CATEGORY = createField(DSL.name("source_category"), SQLDataType.VARCHAR(255).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "三方平台行业");

    /**
     * The column <code>creation.creation_content.title</code>. 标题
     */
    public final TableField<ContentRecord, String> TITLE = createField(DSL.name("title"), SQLDataType.VARCHAR(255).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "标题");

    /**
     * The column <code>creation.creation_content.content</code>. 内容
     */
    public final TableField<ContentRecord, String> CONTENT = createField(DSL.name("content"), SQLDataType.CLOB, this, "内容");

    /**
     * The column <code>creation.creation_content.category</code>. 行业
     */
    public final TableField<ContentRecord, String> CATEGORY = createField(DSL.name("category"), SQLDataType.VARCHAR(255).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "行业");

    /**
     * The column <code>creation.creation_content.author</code>. 作者
     */
    public final TableField<ContentRecord, JSONB> AUTHOR = createField(DSL.name("author"), SQLDataType.JSONB, this, "作者");

    /**
     * The column <code>creation.creation_content.publish_time</code>. 发布时间
     */
    public final TableField<ContentRecord, String> PUBLISH_TIME = createField(DSL.name("publish_time"), SQLDataType.VARCHAR(255).nullable(false), this, "发布时间");

    /**
     * The column <code>creation.creation_content.status</code>. 当前状态
     */
    public final TableField<ContentRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(16).nullable(false), this, "当前状态");

    /**
     * The column <code>creation.creation_content.tags</code>. 标签
     */
    public final TableField<ContentRecord, JSONB[]> TAGS = createField(DSL.name("tags"), SQLDataType.JSONB.array(), this, "标签");

    /**
     * The column <code>creation.creation_content.views</code>. 总观看量
     */
    public final TableField<ContentRecord, Long> VIEWS = createField(DSL.name("views"), SQLDataType.BIGINT.nullable(false), this, "总观看量");

    private ContentTable(Name alias, Table<ContentRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private ContentTable(Name alias, Table<ContentRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("星耀内容大宽表"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_content</code> table reference
     */
    public ContentTable(String alias) {
        this(DSL.name(alias), CREATION_CONTENT);
    }

    /**
     * Create an aliased <code>creation.creation_content</code> table reference
     */
    public ContentTable(Name alias) {
        this(alias, CREATION_CONTENT);
    }

    /**
     * Create a <code>creation.creation_content</code> table reference
     */
    public ContentTable() {
        this(DSL.name("creation_content"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<ContentRecord> getPrimaryKey() {
        return Keys.CREATION_CONTENT_PKEY;
    }

    @Override
    public ContentTable as(String alias) {
        return new ContentTable(DSL.name(alias), this);
    }

    @Override
    public ContentTable as(Name alias) {
        return new ContentTable(alias, this);
    }

    @Override
    public ContentTable as(Table<?> alias) {
        return new ContentTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ContentTable rename(String name) {
        return new ContentTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ContentTable rename(Name name) {
        return new ContentTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ContentTable rename(Table<?> name) {
        return new ContentTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ContentTable where(Condition condition) {
        return new ContentTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ContentTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ContentTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ContentTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ContentTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ContentTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ContentTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ContentTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ContentTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ContentTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
