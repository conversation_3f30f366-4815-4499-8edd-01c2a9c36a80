/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.MaterialTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationMaterial;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 素材记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class MaterialRecord extends UpdatableRecordImpl<MaterialRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_material.id</code>. 主键
     */
    public MaterialRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_material.created_at</code>. 创建时间
     */
    public MaterialRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_material.updated_at</code>. 更新时间
     */
    public MaterialRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_material.creator_type</code>. 创建者类型
     */
    public MaterialRecord setCreatorType(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.creator_type</code>. 创建者类型
     */
    public String getCreatorType() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_material.creator_id</code>. 创建者ID
     */
    public MaterialRecord setCreatorId(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.creator_id</code>. 创建者ID
     */
    public String getCreatorId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_material.creator_name</code>. 创建者名称
     */
    public MaterialRecord setCreatorName(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.creator_name</code>. 创建者名称
     */
    public String getCreatorName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_material.uid</code>. 所属用户ID
     */
    public MaterialRecord setUid(Long value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.uid</code>. 所属用户ID
     */
    public Long getUid() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>creation.creation_material.name</code>. 素材名称
     */
    public MaterialRecord setName(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.name</code>. 素材名称
     */
    public String getName() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_material.type</code>.
     * 素材类型（文本/图片/音频/视频）
     */
    public MaterialRecord setType(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.type</code>.
     * 素材类型（文本/图片/音频/视频）
     */
    public String getType() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_material.status</code>. 素材状态（有效/无效)
     */
    public MaterialRecord setStatus(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.status</code>. 素材状态（有效/无效)
     */
    public String getStatus() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_material.acl_type</code>. 访问权限（公共/私有）
     */
    public MaterialRecord setAclType(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.acl_type</code>. 访问权限（公共/私有）
     */
    public String getAclType() {
        return (String) get(10);
    }

    /**
     * Setter for <code>creation.creation_material.description</code>. 素材描述
     */
    public MaterialRecord setDescription(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.description</code>. 素材描述
     */
    public String getDescription() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_material.file_id</code>. 主文件ID
     */
    public MaterialRecord setFileId(@Nullable Long value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.file_id</code>. 主文件ID
     */
    @Nullable
    public Long getFileId() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>creation.creation_material.file_url</code>. 主文件地址（OSS协议）
     */
    public MaterialRecord setFileUrl(@Nullable String value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.file_url</code>. 主文件地址（OSS协议）
     */
    @Nullable
    public String getFileUrl() {
        return (String) get(13);
    }

    /**
     * Setter for <code>creation.creation_material.text</code>. 主文本
     */
    public MaterialRecord setText(String value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.text</code>. 主文本
     */
    public String getText() {
        return (String) get(14);
    }

    /**
     * Setter for <code>creation.creation_material.attached_resources</code>.
     * 附加资源列表（封面/缩略图d）
     */
    public MaterialRecord setAttachedResources(JSONB[] value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.attached_resources</code>.
     * 附加资源列表（封面/缩略图d）
     */
    public JSONB[] getAttachedResources() {
        return (JSONB[]) get(15);
    }

    /**
     * Setter for <code>creation.creation_material.labels</code>. 标签列表
     */
    public MaterialRecord setLabels(JSONB[] value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.labels</code>. 标签列表
     */
    public JSONB[] getLabels() {
        return (JSONB[]) get(16);
    }

    /**
     * Setter for <code>creation.creation_material.likes</code>. 喜欢数量
     */
    public MaterialRecord setLikes(Long value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.likes</code>. 喜欢数量
     */
    public Long getLikes() {
        return (Long) get(17);
    }

    /**
     * Setter for <code>creation.creation_material.favorites</code>. 收藏数量
     */
    public MaterialRecord setFavorites(Long value) {
        set(18, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.favorites</code>. 收藏数量
     */
    public Long getFavorites() {
        return (Long) get(18);
    }

    /**
     * Setter for <code>creation.creation_material.views</code>. 查看数量
     */
    public MaterialRecord setViews(Long value) {
        set(19, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_material.views</code>. 查看数量
     */
    public Long getViews() {
        return (Long) get(19);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached MaterialRecord
     */
    public MaterialRecord() {
        super(MaterialTable.CREATION_MATERIAL);
    }

    /**
     * Create a detached, initialised MaterialRecord
     */
    public MaterialRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, String creatorType, String creatorId, String creatorName, Long uid, String name, String type, String status, String aclType, String description, @Nullable Long fileId, @Nullable String fileUrl, String text, JSONB[] attachedResources, JSONB[] labels, Long likes, Long favorites, Long views) {
        super(MaterialTable.CREATION_MATERIAL);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCreatorType(creatorType);
        setCreatorId(creatorId);
        setCreatorName(creatorName);
        setUid(uid);
        setName(name);
        setType(type);
        setStatus(status);
        setAclType(aclType);
        setDescription(description);
        setFileId(fileId);
        setFileUrl(fileUrl);
        setText(text);
        setAttachedResources(attachedResources);
        setLabels(labels);
        setLikes(likes);
        setFavorites(favorites);
        setViews(views);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised MaterialRecord
     */
    public MaterialRecord(CreationMaterial value) {
        super(MaterialTable.CREATION_MATERIAL);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCreatorType(value.getCreatorType());
            setCreatorId(value.getCreatorId());
            setCreatorName(value.getCreatorName());
            setUid(value.getUid());
            setName(value.getName());
            setType(value.getType());
            setStatus(value.getStatus());
            setAclType(value.getAclType());
            setDescription(value.getDescription());
            setFileId(value.getFileId());
            setFileUrl(value.getFileUrl());
            setText(value.getText());
            setAttachedResources(value.getAttachedResources());
            setLabels(value.getLabels());
            setLikes(value.getLikes());
            setFavorites(value.getFavorites());
            setViews(value.getViews());
            resetChangedOnNotNull();
        }
    }
}
