/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Arrays;

import javax.annotation.processing.Generated;


/**
 * 音乐库
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationAiVideo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String status;
    private String acl;
    private String bizSource;
    private String bizNo;
    private String title;
    private Long fileId;
    private String fileUrl;
    private Duration duration;
    private String videoFormat;
    private String resolution;
    private Long coverFileId;
    private String coverUrl;
    private Long authorUid;
    private String authorName;
    private String authorAvatar;
    private String[] tagNames;
    private Integer likeCount;

    public CreationAiVideo() {}

    public CreationAiVideo(CreationAiVideo value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.status = value.status;
        this.acl = value.acl;
        this.bizSource = value.bizSource;
        this.bizNo = value.bizNo;
        this.title = value.title;
        this.fileId = value.fileId;
        this.fileUrl = value.fileUrl;
        this.duration = value.duration;
        this.videoFormat = value.videoFormat;
        this.resolution = value.resolution;
        this.coverFileId = value.coverFileId;
        this.coverUrl = value.coverUrl;
        this.authorUid = value.authorUid;
        this.authorName = value.authorName;
        this.authorAvatar = value.authorAvatar;
        this.tagNames = value.tagNames;
        this.likeCount = value.likeCount;
    }

    public CreationAiVideo(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        @Nullable Long uid,
        @Nullable String status,
        @Nullable String acl,
        @Nullable String bizSource,
        @Nullable String bizNo,
        @Nullable String title,
        @Nullable Long fileId,
        @Nullable String fileUrl,
        @Nullable Duration duration,
        @Nullable String videoFormat,
        @Nullable String resolution,
        @Nullable Long coverFileId,
        @Nullable String coverUrl,
        @Nullable Long authorUid,
        @Nullable String authorName,
        @Nullable String authorAvatar,
        @Nullable String[] tagNames,
        @Nullable Integer likeCount
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.status = status;
        this.acl = acl;
        this.bizSource = bizSource;
        this.bizNo = bizNo;
        this.title = title;
        this.fileId = fileId;
        this.fileUrl = fileUrl;
        this.duration = duration;
        this.videoFormat = videoFormat;
        this.resolution = resolution;
        this.coverFileId = coverFileId;
        this.coverUrl = coverUrl;
        this.authorUid = authorUid;
        this.authorName = authorName;
        this.authorAvatar = authorAvatar;
        this.tagNames = tagNames;
        this.likeCount = likeCount;
    }

    /**
     * Getter for <code>creation.creation_ai_video.id</code>. 主键ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_ai_video.id</code>. 主键ID
     */
    public CreationAiVideo setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_ai_video.created_at</code>. 创建时间
     */
    public CreationAiVideo setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_ai_video.updated_at</code>. 更新时间
     */
    public CreationAiVideo setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.uid</code>. 所属用户ID（1为平台）
     */
    @Nullable
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_ai_video.uid</code>. 所属用户ID（1为平台）
     */
    public CreationAiVideo setUid(@Nullable Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.status</code>. 状态
     */
    @Nullable
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_ai_video.status</code>. 状态
     */
    public CreationAiVideo setStatus(@Nullable String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.acl</code>. 访问权限
     */
    @Nullable
    public String getAcl() {
        return this.acl;
    }

    /**
     * Setter for <code>creation.creation_ai_video.acl</code>. 访问权限
     */
    public CreationAiVideo setAcl(@Nullable String acl) {
        this.acl = acl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.biz_source</code>. 业务来源
     */
    @Nullable
    public String getBizSource() {
        return this.bizSource;
    }

    /**
     * Setter for <code>creation.creation_ai_video.biz_source</code>. 业务来源
     */
    public CreationAiVideo setBizSource(@Nullable String bizSource) {
        this.bizSource = bizSource;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.biz_no</code>. 业务编号
     */
    @Nullable
    public String getBizNo() {
        return this.bizNo;
    }

    /**
     * Setter for <code>creation.creation_ai_video.biz_no</code>. 业务编号
     */
    public CreationAiVideo setBizNo(@Nullable String bizNo) {
        this.bizNo = bizNo;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.title</code>. 视频标题
     */
    @Nullable
    public String getTitle() {
        return this.title;
    }

    /**
     * Setter for <code>creation.creation_ai_video.title</code>. 视频标题
     */
    public CreationAiVideo setTitle(@Nullable String title) {
        this.title = title;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.file_id</code>. 视频文件ID
     */
    @Nullable
    public Long getFileId() {
        return this.fileId;
    }

    /**
     * Setter for <code>creation.creation_ai_video.file_id</code>. 视频文件ID
     */
    public CreationAiVideo setFileId(@Nullable Long fileId) {
        this.fileId = fileId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.file_url</code>.
     * 视频文件地址（OSS格式）
     */
    @Nullable
    public String getFileUrl() {
        return this.fileUrl;
    }

    /**
     * Setter for <code>creation.creation_ai_video.file_url</code>.
     * 视频文件地址（OSS格式）
     */
    public CreationAiVideo setFileUrl(@Nullable String fileUrl) {
        this.fileUrl = fileUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.duration</code>. 视频时长
     */
    @Nullable
    public Duration getDuration() {
        return this.duration;
    }

    /**
     * Setter for <code>creation.creation_ai_video.duration</code>. 视频时长
     */
    public CreationAiVideo setDuration(@Nullable Duration duration) {
        this.duration = duration;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.video_format</code>. 视频格式（小写）
     */
    @Nullable
    public String getVideoFormat() {
        return this.videoFormat;
    }

    /**
     * Setter for <code>creation.creation_ai_video.video_format</code>. 视频格式（小写）
     */
    public CreationAiVideo setVideoFormat(@Nullable String videoFormat) {
        this.videoFormat = videoFormat;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.resolution</code>.
     * 视频分辨率（宽度x高度）
     */
    @Nullable
    public String getResolution() {
        return this.resolution;
    }

    /**
     * Setter for <code>creation.creation_ai_video.resolution</code>.
     * 视频分辨率（宽度x高度）
     */
    public CreationAiVideo setResolution(@Nullable String resolution) {
        this.resolution = resolution;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.cover_file_id</code>. 封面图文件ID
     */
    @Nullable
    public Long getCoverFileId() {
        return this.coverFileId;
    }

    /**
     * Setter for <code>creation.creation_ai_video.cover_file_id</code>. 封面图文件ID
     */
    public CreationAiVideo setCoverFileId(@Nullable Long coverFileId) {
        this.coverFileId = coverFileId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.cover_url</code>.
     * 封面图地址（OSS格式）
     */
    @Nullable
    public String getCoverUrl() {
        return this.coverUrl;
    }

    /**
     * Setter for <code>creation.creation_ai_video.cover_url</code>.
     * 封面图地址（OSS格式）
     */
    public CreationAiVideo setCoverUrl(@Nullable String coverUrl) {
        this.coverUrl = coverUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.author_uid</code>. 作者的用户ID
     */
    @Nullable
    public Long getAuthorUid() {
        return this.authorUid;
    }

    /**
     * Setter for <code>creation.creation_ai_video.author_uid</code>. 作者的用户ID
     */
    public CreationAiVideo setAuthorUid(@Nullable Long authorUid) {
        this.authorUid = authorUid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.author_name</code>. 作者名
     */
    @Nullable
    public String getAuthorName() {
        return this.authorName;
    }

    /**
     * Setter for <code>creation.creation_ai_video.author_name</code>. 作者名
     */
    public CreationAiVideo setAuthorName(@Nullable String authorName) {
        this.authorName = authorName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.author_avatar</code>.
     */
    @Nullable
    public String getAuthorAvatar() {
        return this.authorAvatar;
    }

    /**
     * Setter for <code>creation.creation_ai_video.author_avatar</code>.
     */
    public CreationAiVideo setAuthorAvatar(@Nullable String authorAvatar) {
        this.authorAvatar = authorAvatar;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.tag_names</code>. 标签列表
     */
    @Nullable
    public String[] getTagNames() {
        return this.tagNames;
    }

    /**
     * Setter for <code>creation.creation_ai_video.tag_names</code>. 标签列表
     */
    public CreationAiVideo setTagNames(@Nullable String[] tagNames) {
        this.tagNames = tagNames;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_video.like_count</code>. 点赞数量
     */
    @Nullable
    public Integer getLikeCount() {
        return this.likeCount;
    }

    /**
     * Setter for <code>creation.creation_ai_video.like_count</code>. 点赞数量
     */
    public CreationAiVideo setLikeCount(@Nullable Integer likeCount) {
        this.likeCount = likeCount;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationAiVideo other = (CreationAiVideo) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.acl == null) {
            if (other.acl != null)
                return false;
        }
        else if (!this.acl.equals(other.acl))
            return false;
        if (this.bizSource == null) {
            if (other.bizSource != null)
                return false;
        }
        else if (!this.bizSource.equals(other.bizSource))
            return false;
        if (this.bizNo == null) {
            if (other.bizNo != null)
                return false;
        }
        else if (!this.bizNo.equals(other.bizNo))
            return false;
        if (this.title == null) {
            if (other.title != null)
                return false;
        }
        else if (!this.title.equals(other.title))
            return false;
        if (this.fileId == null) {
            if (other.fileId != null)
                return false;
        }
        else if (!this.fileId.equals(other.fileId))
            return false;
        if (this.fileUrl == null) {
            if (other.fileUrl != null)
                return false;
        }
        else if (!this.fileUrl.equals(other.fileUrl))
            return false;
        if (this.duration == null) {
            if (other.duration != null)
                return false;
        }
        else if (!this.duration.equals(other.duration))
            return false;
        if (this.videoFormat == null) {
            if (other.videoFormat != null)
                return false;
        }
        else if (!this.videoFormat.equals(other.videoFormat))
            return false;
        if (this.resolution == null) {
            if (other.resolution != null)
                return false;
        }
        else if (!this.resolution.equals(other.resolution))
            return false;
        if (this.coverFileId == null) {
            if (other.coverFileId != null)
                return false;
        }
        else if (!this.coverFileId.equals(other.coverFileId))
            return false;
        if (this.coverUrl == null) {
            if (other.coverUrl != null)
                return false;
        }
        else if (!this.coverUrl.equals(other.coverUrl))
            return false;
        if (this.authorUid == null) {
            if (other.authorUid != null)
                return false;
        }
        else if (!this.authorUid.equals(other.authorUid))
            return false;
        if (this.authorName == null) {
            if (other.authorName != null)
                return false;
        }
        else if (!this.authorName.equals(other.authorName))
            return false;
        if (this.authorAvatar == null) {
            if (other.authorAvatar != null)
                return false;
        }
        else if (!this.authorAvatar.equals(other.authorAvatar))
            return false;
        if (this.tagNames == null) {
            if (other.tagNames != null)
                return false;
        }
        else if (!Arrays.deepEquals(this.tagNames, other.tagNames))
            return false;
        if (this.likeCount == null) {
            if (other.likeCount != null)
                return false;
        }
        else if (!this.likeCount.equals(other.likeCount))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.acl == null) ? 0 : this.acl.hashCode());
        result = prime * result + ((this.bizSource == null) ? 0 : this.bizSource.hashCode());
        result = prime * result + ((this.bizNo == null) ? 0 : this.bizNo.hashCode());
        result = prime * result + ((this.title == null) ? 0 : this.title.hashCode());
        result = prime * result + ((this.fileId == null) ? 0 : this.fileId.hashCode());
        result = prime * result + ((this.fileUrl == null) ? 0 : this.fileUrl.hashCode());
        result = prime * result + ((this.duration == null) ? 0 : this.duration.hashCode());
        result = prime * result + ((this.videoFormat == null) ? 0 : this.videoFormat.hashCode());
        result = prime * result + ((this.resolution == null) ? 0 : this.resolution.hashCode());
        result = prime * result + ((this.coverFileId == null) ? 0 : this.coverFileId.hashCode());
        result = prime * result + ((this.coverUrl == null) ? 0 : this.coverUrl.hashCode());
        result = prime * result + ((this.authorUid == null) ? 0 : this.authorUid.hashCode());
        result = prime * result + ((this.authorName == null) ? 0 : this.authorName.hashCode());
        result = prime * result + ((this.authorAvatar == null) ? 0 : this.authorAvatar.hashCode());
        result = prime * result + ((this.tagNames == null) ? 0 : Arrays.deepHashCode(this.tagNames));
        result = prime * result + ((this.likeCount == null) ? 0 : this.likeCount.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationAiVideo (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(status);
        sb.append(", ").append(acl);
        sb.append(", ").append(bizSource);
        sb.append(", ").append(bizNo);
        sb.append(", ").append(title);
        sb.append(", ").append(fileId);
        sb.append(", ").append(fileUrl);
        sb.append(", ").append(duration);
        sb.append(", ").append(videoFormat);
        sb.append(", ").append(resolution);
        sb.append(", ").append(coverFileId);
        sb.append(", ").append(coverUrl);
        sb.append(", ").append(authorUid);
        sb.append(", ").append(authorName);
        sb.append(", ").append(authorAvatar);
        sb.append(", ").append(Arrays.deepToString(tagNames));
        sb.append(", ").append(likeCount);

        sb.append(")");
        return sb.toString();
    }
}
