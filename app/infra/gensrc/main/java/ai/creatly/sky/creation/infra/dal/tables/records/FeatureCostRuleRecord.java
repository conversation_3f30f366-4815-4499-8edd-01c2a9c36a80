/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.FeatureCostRuleTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationFeatureCostRule;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 计费规则
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class FeatureCostRuleRecord extends UpdatableRecordImpl<FeatureCostRuleRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_feature_cost_rule.id</code>. 主键
     */
    public FeatureCostRuleRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.created_at</code>.
     * 创建时间
     */
    public FeatureCostRuleRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.updated_at</code>.
     * 更新时间
     */
    public FeatureCostRuleRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.cost</code>. 消耗值
     */
    public FeatureCostRuleRecord setCost(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.cost</code>. 消耗值
     */
    public Long getCost() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.units_name</code>.
     * 度量单位
     */
    public FeatureCostRuleRecord setUnitsName(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.units_name</code>.
     * 度量单位
     */
    public String getUnitsName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.units_amount</code>.
     * 度量数量
     */
    public FeatureCostRuleRecord setUnitsAmount(Integer value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.units_amount</code>.
     * 度量数量
     */
    public Integer getUnitsAmount() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.task_type</code>.
     * 任务标识符
     */
    public FeatureCostRuleRecord setTaskType(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.task_type</code>.
     * 任务标识符
     */
    public String getTaskType() {
        return (String) get(6);
    }

    /**
     * Setter for
     * <code>creation.creation_feature_cost_rule.task_biz_type</code>. 任务类型
     */
    public FeatureCostRuleRecord setTaskBizType(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_feature_cost_rule.task_biz_type</code>. 任务类型
     */
    public String getTaskBizType() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_feature_cost_rule.status</code>. 状态
     */
    public FeatureCostRuleRecord setStatus(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_feature_cost_rule.status</code>. 状态
     */
    public String getStatus() {
        return (String) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached FeatureCostRuleRecord
     */
    public FeatureCostRuleRecord() {
        super(FeatureCostRuleTable.CREATION_FEATURE_COST_RULE);
    }

    /**
     * Create a detached, initialised FeatureCostRuleRecord
     */
    public FeatureCostRuleRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long cost, String unitsName, Integer unitsAmount, String taskType, String taskBizType, String status) {
        super(FeatureCostRuleTable.CREATION_FEATURE_COST_RULE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCost(cost);
        setUnitsName(unitsName);
        setUnitsAmount(unitsAmount);
        setTaskType(taskType);
        setTaskBizType(taskBizType);
        setStatus(status);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised FeatureCostRuleRecord
     */
    public FeatureCostRuleRecord(CreationFeatureCostRule value) {
        super(FeatureCostRuleTable.CREATION_FEATURE_COST_RULE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCost(value.getCost());
            setUnitsName(value.getUnitsName());
            setUnitsAmount(value.getUnitsAmount());
            setTaskType(value.getTaskType());
            setTaskBizType(value.getTaskBizType());
            setStatus(value.getStatus());
            resetChangedOnNotNull();
        }
    }
}
