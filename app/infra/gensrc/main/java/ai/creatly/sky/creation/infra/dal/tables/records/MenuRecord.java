/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.MenuTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationMenu;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 菜单
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class MenuRecord extends UpdatableRecordImpl<MenuRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_menu.id</code>. 主键
     */
    public MenuRecord setId(@Nullable Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_menu.created_at</code>. 创建时间
     */
    public MenuRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_menu.updated_at</code>. 更新时间
     */
    public MenuRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_menu.code</code>. 唯一标识
     */
    public MenuRecord setCode(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu.code</code>. 唯一标识
     */
    public String getCode() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_menu.version</code>. 版本
     */
    public MenuRecord setVersion(Integer value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu.version</code>. 版本
     */
    public Integer getVersion() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>creation.creation_menu.desc</code>. 描述
     */
    public MenuRecord setDesc(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu.desc</code>. 描述
     */
    public String getDesc() {
        return (String) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached MenuRecord
     */
    public MenuRecord() {
        super(MenuTable.CREATION_MENU);
    }

    /**
     * Create a detached, initialised MenuRecord
     */
    public MenuRecord(@Nullable Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, String code, Integer version, String desc) {
        super(MenuTable.CREATION_MENU);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCode(code);
        setVersion(version);
        setDesc(desc);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised MenuRecord
     */
    public MenuRecord(CreationMenu value) {
        super(MenuTable.CREATION_MENU);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCode(value.getCode());
            setVersion(value.getVersion());
            setDesc(value.getDesc());
            resetChangedOnNotNull();
        }
    }
}
