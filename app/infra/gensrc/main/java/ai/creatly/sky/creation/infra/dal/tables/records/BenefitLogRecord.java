/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.BenefitLogTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationBenefitLog;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 权益发放记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class BenefitLogRecord extends UpdatableRecordImpl<BenefitLogRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_benefit_log.id</code>. 主键
     */
    public BenefitLogRecord setId(@Nullable Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.created_at</code>. 创建时间
     */
    public BenefitLogRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.updated_at</code>. 更新时间
     */
    public BenefitLogRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.uid</code>. 权益所属用户ID
     */
    public BenefitLogRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.uid</code>. 权益所属用户ID
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.source_type</code>. 权益来源类型
     */
    public BenefitLogRecord setSourceType(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.source_type</code>. 权益来源类型
     */
    public String getSourceType() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.source_id</code>. 权益来源唯一标识
     */
    public BenefitLogRecord setSourceId(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.source_id</code>. 权益来源唯一标识
     */
    public String getSourceId() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.benefit_type</code>. 权益类型
     */
    public BenefitLogRecord setBenefitType(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.benefit_type</code>. 权益类型
     */
    public String getBenefitType() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.benefit_id</code>. 权益实例ID
     */
    public BenefitLogRecord setBenefitId(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.benefit_id</code>. 权益实例ID
     */
    public String getBenefitId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.benefit_code</code>. 权益编号
     */
    public BenefitLogRecord setBenefitCode(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.benefit_code</code>. 权益编号
     */
    public String getBenefitCode() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.benefit_name</code>. 权益名称
     */
    public BenefitLogRecord setBenefitName(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.benefit_name</code>. 权益名称
     */
    public String getBenefitName() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.benefit_value</code>. 权益内容
     */
    public BenefitLogRecord setBenefitValue(JSONB value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.benefit_value</code>. 权益内容
     */
    public JSONB getBenefitValue() {
        return (JSONB) get(10);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.plan_id</code>. 订阅计划ID
     */
    public BenefitLogRecord setPlanId(@Nullable String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.plan_id</code>. 订阅计划ID
     */
    @Nullable
    public String getPlanId() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.paid_fee</code>. 支付金额
     */
    public BenefitLogRecord setPaidFee(Long value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.paid_fee</code>. 支付金额
     */
    public Long getPaidFee() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.receive_at</code>. 权益获得时间
     */
    public BenefitLogRecord setReceiveAt(ZonedDateTime value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.receive_at</code>. 权益获得时间
     */
    public ZonedDateTime getReceiveAt() {
        return (ZonedDateTime) get(13);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.effect_at</code>. 权益生效时间
     */
    public BenefitLogRecord setEffectAt(ZonedDateTime value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.effect_at</code>. 权益生效时间
     */
    public ZonedDateTime getEffectAt() {
        return (ZonedDateTime) get(14);
    }

    /**
     * Setter for <code>creation.creation_benefit_log.expire_at</code>. 权益到期时间
     */
    public BenefitLogRecord setExpireAt(@Nullable ZonedDateTime value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_benefit_log.expire_at</code>. 权益到期时间
     */
    @Nullable
    public ZonedDateTime getExpireAt() {
        return (ZonedDateTime) get(15);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached BenefitLogRecord
     */
    public BenefitLogRecord() {
        super(BenefitLogTable.CREATION_BENEFIT_LOG);
    }

    /**
     * Create a detached, initialised BenefitLogRecord
     */
    public BenefitLogRecord(@Nullable Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String sourceType, String sourceId, String benefitType, String benefitId, String benefitCode, String benefitName, JSONB benefitValue, @Nullable String planId, Long paidFee, ZonedDateTime receiveAt, ZonedDateTime effectAt, @Nullable ZonedDateTime expireAt) {
        super(BenefitLogTable.CREATION_BENEFIT_LOG);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setSourceType(sourceType);
        setSourceId(sourceId);
        setBenefitType(benefitType);
        setBenefitId(benefitId);
        setBenefitCode(benefitCode);
        setBenefitName(benefitName);
        setBenefitValue(benefitValue);
        setPlanId(planId);
        setPaidFee(paidFee);
        setReceiveAt(receiveAt);
        setEffectAt(effectAt);
        setExpireAt(expireAt);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised BenefitLogRecord
     */
    public BenefitLogRecord(CreationBenefitLog value) {
        super(BenefitLogTable.CREATION_BENEFIT_LOG);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setSourceType(value.getSourceType());
            setSourceId(value.getSourceId());
            setBenefitType(value.getBenefitType());
            setBenefitId(value.getBenefitId());
            setBenefitCode(value.getBenefitCode());
            setBenefitName(value.getBenefitName());
            setBenefitValue(value.getBenefitValue());
            setPlanId(value.getPlanId());
            setPaidFee(value.getPaidFee());
            setReceiveAt(value.getReceiveAt());
            setEffectAt(value.getEffectAt());
            setExpireAt(value.getExpireAt());
            resetChangedOnNotNull();
        }
    }
}
