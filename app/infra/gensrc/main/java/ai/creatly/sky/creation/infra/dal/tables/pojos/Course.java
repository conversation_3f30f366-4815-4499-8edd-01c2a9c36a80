/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 课程
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class Course implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String category;
    private String coverUrl;
    private String courseIntro;
    private String courseStatus;
    private String courseType;
    private String courseTitle;
    private String videoUrl;
    private String courseSubtitle;
    private Long realPrice;
    private Long linePrice;
    private String duration;

    public Course() {}

    public Course(Course value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.category = value.category;
        this.coverUrl = value.coverUrl;
        this.courseIntro = value.courseIntro;
        this.courseStatus = value.courseStatus;
        this.courseType = value.courseType;
        this.courseTitle = value.courseTitle;
        this.videoUrl = value.videoUrl;
        this.courseSubtitle = value.courseSubtitle;
        this.realPrice = value.realPrice;
        this.linePrice = value.linePrice;
        this.duration = value.duration;
    }

    public Course(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        @Nullable String category,
        @Nullable String coverUrl,
        @Nullable String courseIntro,
        @Nullable String courseStatus,
        @Nullable String courseType,
        @Nullable String courseTitle,
        @Nullable String videoUrl,
        @Nullable String courseSubtitle,
        @Nullable Long realPrice,
        @Nullable Long linePrice,
        @Nullable String duration
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.category = category;
        this.coverUrl = coverUrl;
        this.courseIntro = courseIntro;
        this.courseStatus = courseStatus;
        this.courseType = courseType;
        this.courseTitle = courseTitle;
        this.videoUrl = videoUrl;
        this.courseSubtitle = courseSubtitle;
        this.realPrice = realPrice;
        this.linePrice = linePrice;
        this.duration = duration;
    }

    /**
     * Getter for <code>creation.course.id</code>. 课程id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.course.id</code>. 课程id
     */
    public Course setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.course.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.course.created_at</code>. 创建时间
     */
    public Course setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.course.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.course.updated_at</code>. 更新时间
     */
    public Course setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.course.category</code>. 课程类目
     */
    @Nullable
    public String getCategory() {
        return this.category;
    }

    /**
     * Setter for <code>creation.course.category</code>. 课程类目
     */
    public Course setCategory(@Nullable String category) {
        this.category = category;
        return this;
    }

    /**
     * Getter for <code>creation.course.cover_url</code>. 课程封面
     */
    @Nullable
    public String getCoverUrl() {
        return this.coverUrl;
    }

    /**
     * Setter for <code>creation.course.cover_url</code>. 课程封面
     */
    public Course setCoverUrl(@Nullable String coverUrl) {
        this.coverUrl = coverUrl;
        return this;
    }

    /**
     * Getter for <code>creation.course.course_intro</code>. 课程介绍
     */
    @Nullable
    public String getCourseIntro() {
        return this.courseIntro;
    }

    /**
     * Setter for <code>creation.course.course_intro</code>. 课程介绍
     */
    public Course setCourseIntro(@Nullable String courseIntro) {
        this.courseIntro = courseIntro;
        return this;
    }

    /**
     * Getter for <code>creation.course.course_status</code>. 状态
     */
    @Nullable
    public String getCourseStatus() {
        return this.courseStatus;
    }

    /**
     * Setter for <code>creation.course.course_status</code>. 状态
     */
    public Course setCourseStatus(@Nullable String courseStatus) {
        this.courseStatus = courseStatus;
        return this;
    }

    /**
     * Getter for <code>creation.course.course_type</code>. 课程类型
     */
    @Nullable
    public String getCourseType() {
        return this.courseType;
    }

    /**
     * Setter for <code>creation.course.course_type</code>. 课程类型
     */
    public Course setCourseType(@Nullable String courseType) {
        this.courseType = courseType;
        return this;
    }

    /**
     * Getter for <code>creation.course.course_title</code>. 课程名称
     */
    @Nullable
    public String getCourseTitle() {
        return this.courseTitle;
    }

    /**
     * Setter for <code>creation.course.course_title</code>. 课程名称
     */
    public Course setCourseTitle(@Nullable String courseTitle) {
        this.courseTitle = courseTitle;
        return this;
    }

    /**
     * Getter for <code>creation.course.video_url</code>. 视频链接
     */
    @Nullable
    public String getVideoUrl() {
        return this.videoUrl;
    }

    /**
     * Setter for <code>creation.course.video_url</code>. 视频链接
     */
    public Course setVideoUrl(@Nullable String videoUrl) {
        this.videoUrl = videoUrl;
        return this;
    }

    /**
     * Getter for <code>creation.course.course_subtitle</code>. 课程副标题
     */
    @Nullable
    public String getCourseSubtitle() {
        return this.courseSubtitle;
    }

    /**
     * Setter for <code>creation.course.course_subtitle</code>. 课程副标题
     */
    public Course setCourseSubtitle(@Nullable String courseSubtitle) {
        this.courseSubtitle = courseSubtitle;
        return this;
    }

    /**
     * Getter for <code>creation.course.real_price</code>. 课程价格
     */
    @Nullable
    public Long getRealPrice() {
        return this.realPrice;
    }

    /**
     * Setter for <code>creation.course.real_price</code>. 课程价格
     */
    public Course setRealPrice(@Nullable Long realPrice) {
        this.realPrice = realPrice;
        return this;
    }

    /**
     * Getter for <code>creation.course.line_price</code>. 划线价格
     */
    @Nullable
    public Long getLinePrice() {
        return this.linePrice;
    }

    /**
     * Setter for <code>creation.course.line_price</code>. 划线价格
     */
    public Course setLinePrice(@Nullable Long linePrice) {
        this.linePrice = linePrice;
        return this;
    }

    /**
     * Getter for <code>creation.course.duration</code>. 总课时
     */
    @Nullable
    public String getDuration() {
        return this.duration;
    }

    /**
     * Setter for <code>creation.course.duration</code>. 总课时
     */
    public Course setDuration(@Nullable String duration) {
        this.duration = duration;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final Course other = (Course) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.category == null) {
            if (other.category != null)
                return false;
        }
        else if (!this.category.equals(other.category))
            return false;
        if (this.coverUrl == null) {
            if (other.coverUrl != null)
                return false;
        }
        else if (!this.coverUrl.equals(other.coverUrl))
            return false;
        if (this.courseIntro == null) {
            if (other.courseIntro != null)
                return false;
        }
        else if (!this.courseIntro.equals(other.courseIntro))
            return false;
        if (this.courseStatus == null) {
            if (other.courseStatus != null)
                return false;
        }
        else if (!this.courseStatus.equals(other.courseStatus))
            return false;
        if (this.courseType == null) {
            if (other.courseType != null)
                return false;
        }
        else if (!this.courseType.equals(other.courseType))
            return false;
        if (this.courseTitle == null) {
            if (other.courseTitle != null)
                return false;
        }
        else if (!this.courseTitle.equals(other.courseTitle))
            return false;
        if (this.videoUrl == null) {
            if (other.videoUrl != null)
                return false;
        }
        else if (!this.videoUrl.equals(other.videoUrl))
            return false;
        if (this.courseSubtitle == null) {
            if (other.courseSubtitle != null)
                return false;
        }
        else if (!this.courseSubtitle.equals(other.courseSubtitle))
            return false;
        if (this.realPrice == null) {
            if (other.realPrice != null)
                return false;
        }
        else if (!this.realPrice.equals(other.realPrice))
            return false;
        if (this.linePrice == null) {
            if (other.linePrice != null)
                return false;
        }
        else if (!this.linePrice.equals(other.linePrice))
            return false;
        if (this.duration == null) {
            if (other.duration != null)
                return false;
        }
        else if (!this.duration.equals(other.duration))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.category == null) ? 0 : this.category.hashCode());
        result = prime * result + ((this.coverUrl == null) ? 0 : this.coverUrl.hashCode());
        result = prime * result + ((this.courseIntro == null) ? 0 : this.courseIntro.hashCode());
        result = prime * result + ((this.courseStatus == null) ? 0 : this.courseStatus.hashCode());
        result = prime * result + ((this.courseType == null) ? 0 : this.courseType.hashCode());
        result = prime * result + ((this.courseTitle == null) ? 0 : this.courseTitle.hashCode());
        result = prime * result + ((this.videoUrl == null) ? 0 : this.videoUrl.hashCode());
        result = prime * result + ((this.courseSubtitle == null) ? 0 : this.courseSubtitle.hashCode());
        result = prime * result + ((this.realPrice == null) ? 0 : this.realPrice.hashCode());
        result = prime * result + ((this.linePrice == null) ? 0 : this.linePrice.hashCode());
        result = prime * result + ((this.duration == null) ? 0 : this.duration.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Course (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(category);
        sb.append(", ").append(coverUrl);
        sb.append(", ").append(courseIntro);
        sb.append(", ").append(courseStatus);
        sb.append(", ").append(courseType);
        sb.append(", ").append(courseTitle);
        sb.append(", ").append(videoUrl);
        sb.append(", ").append(courseSubtitle);
        sb.append(", ").append(realPrice);
        sb.append(", ").append(linePrice);
        sb.append(", ").append(duration);

        sb.append(")");
        return sb.toString();
    }
}
