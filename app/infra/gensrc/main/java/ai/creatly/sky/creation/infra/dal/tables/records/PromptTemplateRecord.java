/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.PromptTemplateTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationPromptTemplate;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 提示词模板
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class PromptTemplateRecord extends UpdatableRecordImpl<PromptTemplateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_prompt_template.id</code>. 主键
     */
    public PromptTemplateRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_prompt_template.created_at</code>.
     * 创建时间
     */
    public PromptTemplateRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_prompt_template.updated_at</code>.
     * 更新时间
     */
    public PromptTemplateRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_prompt_template.biz_type</code>. 业务分类
     */
    public PromptTemplateRecord setBizType(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.biz_type</code>. 业务分类
     */
    public String getBizType() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_prompt_template.code</code>. 模板编号
     */
    public PromptTemplateRecord setCode(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.code</code>. 模板编号
     */
    public String getCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_prompt_template.title</code>. 标题
     */
    public PromptTemplateRecord setTitle(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.title</code>. 标题
     */
    public String getTitle() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_prompt_template.category</code>. 类目编号
     */
    public PromptTemplateRecord setCategory(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.category</code>. 类目编号
     */
    public String getCategory() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_prompt_template.prompt</code>. 提示词
     */
    public PromptTemplateRecord setPrompt(JSONB value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.prompt</code>. 提示词
     */
    public JSONB getPrompt() {
        return (JSONB) get(7);
    }

    /**
     * Setter for <code>creation.creation_prompt_template.description</code>. 描述
     */
    public PromptTemplateRecord setDescription(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.description</code>. 描述
     */
    public String getDescription() {
        return (String) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached PromptTemplateRecord
     */
    public PromptTemplateRecord() {
        super(PromptTemplateTable.CREATION_PROMPT_TEMPLATE);
    }

    /**
     * Create a detached, initialised PromptTemplateRecord
     */
    public PromptTemplateRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, String bizType, String code, String title, String category, JSONB prompt, String description) {
        super(PromptTemplateTable.CREATION_PROMPT_TEMPLATE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setBizType(bizType);
        setCode(code);
        setTitle(title);
        setCategory(category);
        setPrompt(prompt);
        setDescription(description);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised PromptTemplateRecord
     */
    public PromptTemplateRecord(CreationPromptTemplate value) {
        super(PromptTemplateTable.CREATION_PROMPT_TEMPLATE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setBizType(value.getBizType());
            setCode(value.getCode());
            setTitle(value.getTitle());
            setCategory(value.getCategory());
            setPrompt(value.getPrompt());
            setDescription(value.getDescription());
            resetChangedOnNotNull();
        }
    }
}
