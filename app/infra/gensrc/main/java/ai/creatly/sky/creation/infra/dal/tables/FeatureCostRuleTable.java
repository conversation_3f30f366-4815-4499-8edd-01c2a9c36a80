/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.FeatureCostRuleRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 计费规则
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class FeatureCostRuleTable extends TableImpl<FeatureCostRuleRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>creation.creation_feature_cost_rule</code>
     */
    public static final FeatureCostRuleTable CREATION_FEATURE_COST_RULE = new FeatureCostRuleTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<FeatureCostRuleRecord> getRecordType() {
        return FeatureCostRuleRecord.class;
    }

    /**
     * The column <code>creation.creation_feature_cost_rule.id</code>. 主键
     */
    public final TableField<FeatureCostRuleRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_feature_cost_rule.created_at</code>.
     * 创建时间
     */
    public final TableField<FeatureCostRuleRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_feature_cost_rule.updated_at</code>.
     * 更新时间
     */
    public final TableField<FeatureCostRuleRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_feature_cost_rule.cost</code>. 消耗值
     */
    public final TableField<FeatureCostRuleRecord, Long> COST = createField(DSL.name("cost"), SQLDataType.BIGINT.nullable(false), this, "消耗值");

    /**
     * The column <code>creation.creation_feature_cost_rule.units_name</code>.
     * 度量单位
     */
    public final TableField<FeatureCostRuleRecord, String> UNITS_NAME = createField(DSL.name("units_name"), SQLDataType.VARCHAR(64).nullable(false), this, "度量单位");

    /**
     * The column <code>creation.creation_feature_cost_rule.units_amount</code>.
     * 度量数量
     */
    public final TableField<FeatureCostRuleRecord, Integer> UNITS_AMOUNT = createField(DSL.name("units_amount"), SQLDataType.INTEGER.nullable(false), this, "度量数量");

    /**
     * The column <code>creation.creation_feature_cost_rule.task_type</code>.
     * 任务标识符
     */
    public final TableField<FeatureCostRuleRecord, String> TASK_TYPE = createField(DSL.name("task_type"), SQLDataType.VARCHAR(64).nullable(false), this, "任务标识符");

    /**
     * The column
     * <code>creation.creation_feature_cost_rule.task_biz_type</code>. 任务类型
     */
    public final TableField<FeatureCostRuleRecord, String> TASK_BIZ_TYPE = createField(DSL.name("task_biz_type"), SQLDataType.VARCHAR(64).nullable(false), this, "任务类型");

    /**
     * The column <code>creation.creation_feature_cost_rule.status</code>. 状态
     */
    public final TableField<FeatureCostRuleRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(16).nullable(false), this, "状态");

    private FeatureCostRuleTable(Name alias, Table<FeatureCostRuleRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private FeatureCostRuleTable(Name alias, Table<FeatureCostRuleRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("计费规则"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_feature_cost_rule</code> table
     * reference
     */
    public FeatureCostRuleTable(String alias) {
        this(DSL.name(alias), CREATION_FEATURE_COST_RULE);
    }

    /**
     * Create an aliased <code>creation.creation_feature_cost_rule</code> table
     * reference
     */
    public FeatureCostRuleTable(Name alias) {
        this(alias, CREATION_FEATURE_COST_RULE);
    }

    /**
     * Create a <code>creation.creation_feature_cost_rule</code> table reference
     */
    public FeatureCostRuleTable() {
        this(DSL.name("creation_feature_cost_rule"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<FeatureCostRuleRecord> getPrimaryKey() {
        return Keys.CREATION_FEATURE_COST_RULE_PKEY;
    }

    @Override
    public FeatureCostRuleTable as(String alias) {
        return new FeatureCostRuleTable(DSL.name(alias), this);
    }

    @Override
    public FeatureCostRuleTable as(Name alias) {
        return new FeatureCostRuleTable(alias, this);
    }

    @Override
    public FeatureCostRuleTable as(Table<?> alias) {
        return new FeatureCostRuleTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public FeatureCostRuleTable rename(String name) {
        return new FeatureCostRuleTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public FeatureCostRuleTable rename(Name name) {
        return new FeatureCostRuleTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public FeatureCostRuleTable rename(Table<?> name) {
        return new FeatureCostRuleTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public FeatureCostRuleTable where(Condition condition) {
        return new FeatureCostRuleTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public FeatureCostRuleTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public FeatureCostRuleTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public FeatureCostRuleTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public FeatureCostRuleTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public FeatureCostRuleTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public FeatureCostRuleTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public FeatureCostRuleTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public FeatureCostRuleTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public FeatureCostRuleTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
