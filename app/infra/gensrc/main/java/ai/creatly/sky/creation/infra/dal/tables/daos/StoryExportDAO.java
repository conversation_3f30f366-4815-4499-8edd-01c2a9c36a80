/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.StoryExportTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationStoryExport;
import ai.creatly.sky.creation.infra.dal.tables.records.StoryExportRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 故事导出信息表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class StoryExportDAO extends DAOImpl<StoryExportRecord, CreationStoryExport, Long> {

    /**
     * Create a new StoryExportDAO without any configuration
     */
    public StoryExportDAO() {
        super(StoryExportTable.CREATION_STORY_EXPORT, CreationStoryExport.class);
    }

    /**
     * Create a new StoryExportDAO with an attached configuration
     */
    @Autowired
    public StoryExportDAO(Configuration configuration) {
        super(StoryExportTable.CREATION_STORY_EXPORT, CreationStoryExport.class, configuration);
    }

    @Override
    public Long getId(CreationStoryExport object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryExport> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(StoryExportTable.CREATION_STORY_EXPORT.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationStoryExport> fetchById(Long... values) {
        return fetch(StoryExportTable.CREATION_STORY_EXPORT.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationStoryExport fetchOneById(Long value) {
        return fetchOne(StoryExportTable.CREATION_STORY_EXPORT.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationStoryExport> fetchOptionalById(Long value) {
        return fetchOptional(StoryExportTable.CREATION_STORY_EXPORT.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryExport> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(StoryExportTable.CREATION_STORY_EXPORT.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationStoryExport> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(StoryExportTable.CREATION_STORY_EXPORT.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryExport> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(StoryExportTable.CREATION_STORY_EXPORT.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationStoryExport> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(StoryExportTable.CREATION_STORY_EXPORT.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryExport> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(StoryExportTable.CREATION_STORY_EXPORT.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<CreationStoryExport> fetchByUid(Long... values) {
        return fetch(StoryExportTable.CREATION_STORY_EXPORT.UID, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryExport> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(StoryExportTable.CREATION_STORY_EXPORT.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CreationStoryExport> fetchByStatus(String... values) {
        return fetch(StoryExportTable.CREATION_STORY_EXPORT.STATUS, values);
    }

    /**
     * Fetch records that have <code>down_file_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryExport> fetchRangeOfDownFileId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(StoryExportTable.CREATION_STORY_EXPORT.DOWN_FILE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>down_file_id IN (values)</code>
     */
    public List<CreationStoryExport> fetchByDownFileId(Long... values) {
        return fetch(StoryExportTable.CREATION_STORY_EXPORT.DOWN_FILE_ID, values);
    }

    /**
     * Fetch records that have <code>down_file_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryExport> fetchRangeOfDownFileUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(StoryExportTable.CREATION_STORY_EXPORT.DOWN_FILE_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>down_file_url IN (values)</code>
     */
    public List<CreationStoryExport> fetchByDownFileUrl(String... values) {
        return fetch(StoryExportTable.CREATION_STORY_EXPORT.DOWN_FILE_URL, values);
    }

    /**
     * Fetch records that have <code>story_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryExport> fetchRangeOfStoryId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(StoryExportTable.CREATION_STORY_EXPORT.STORY_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>story_id IN (values)</code>
     */
    public List<CreationStoryExport> fetchByStoryId(Long... values) {
        return fetch(StoryExportTable.CREATION_STORY_EXPORT.STORY_ID, values);
    }

    /**
     * Fetch records that have <code>export_format BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryExport> fetchRangeOfExportFormat(String lowerInclusive, String upperInclusive) {
        return fetchRange(StoryExportTable.CREATION_STORY_EXPORT.EXPORT_FORMAT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>export_format IN (values)</code>
     */
    public List<CreationStoryExport> fetchByExportFormat(String... values) {
        return fetch(StoryExportTable.CREATION_STORY_EXPORT.EXPORT_FORMAT, values);
    }
}
