/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.ContentMetricRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 内容指标明细
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class ContentMetricTable extends TableImpl<ContentMetricRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_content_metric</code>
     */
    public static final ContentMetricTable CREATION_CONTENT_METRIC = new ContentMetricTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ContentMetricRecord> getRecordType() {
        return ContentMetricRecord.class;
    }

    /**
     * The column <code>creation.creation_content_metric.id</code>. 主键
     */
    public final TableField<ContentMetricRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_content_metric.created_at</code>. 创建时间
     */
    public final TableField<ContentMetricRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_content_metric.updated_at</code>. 更新时间
     */
    public final TableField<ContentMetricRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_content_metric.content_id</code>. 内容id
     */
    public final TableField<ContentMetricRecord, Long> CONTENT_ID = createField(DSL.name("content_id"), SQLDataType.BIGINT.nullable(false), this, "内容id");

    /**
     * The column <code>creation.creation_content_metric.metric_code</code>.
     * 指标:每小时热点
     */
    public final TableField<ContentMetricRecord, String> METRIC_CODE = createField(DSL.name("metric_code"), SQLDataType.VARCHAR(255).nullable(false), this, "指标:每小时热点");

    /**
     * The column <code>creation.creation_content_metric.metric_name</code>.
     * 指标:每小时热点
     */
    public final TableField<ContentMetricRecord, String> METRIC_NAME = createField(DSL.name("metric_name"), SQLDataType.VARCHAR(255).nullable(false), this, "指标:每小时热点");

    /**
     * The column <code>creation.creation_content_metric.metric_value</code>.
     * 指标值：30000
     */
    public final TableField<ContentMetricRecord, String> METRIC_VALUE = createField(DSL.name("metric_value"), SQLDataType.VARCHAR(255).nullable(false), this, "指标值：30000");

    /**
     * The column <code>creation.creation_content_metric.data_type</code>.
     * 数据类型：number,string
     */
    public final TableField<ContentMetricRecord, String> DATA_TYPE = createField(DSL.name("data_type"), SQLDataType.VARCHAR(255).nullable(false), this, "数据类型：number,string");

    private ContentMetricTable(Name alias, Table<ContentMetricRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private ContentMetricTable(Name alias, Table<ContentMetricRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("内容指标明细"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_content_metric</code> table
     * reference
     */
    public ContentMetricTable(String alias) {
        this(DSL.name(alias), CREATION_CONTENT_METRIC);
    }

    /**
     * Create an aliased <code>creation.creation_content_metric</code> table
     * reference
     */
    public ContentMetricTable(Name alias) {
        this(alias, CREATION_CONTENT_METRIC);
    }

    /**
     * Create a <code>creation.creation_content_metric</code> table reference
     */
    public ContentMetricTable() {
        this(DSL.name("creation_content_metric"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<ContentMetricRecord> getPrimaryKey() {
        return Keys.CREATION_CONTENT_METRIC_PKEY;
    }

    @Override
    public List<UniqueKey<ContentMetricRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.UK_METRIC_CODE);
    }

    @Override
    public ContentMetricTable as(String alias) {
        return new ContentMetricTable(DSL.name(alias), this);
    }

    @Override
    public ContentMetricTable as(Name alias) {
        return new ContentMetricTable(alias, this);
    }

    @Override
    public ContentMetricTable as(Table<?> alias) {
        return new ContentMetricTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ContentMetricTable rename(String name) {
        return new ContentMetricTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ContentMetricTable rename(Name name) {
        return new ContentMetricTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ContentMetricTable rename(Table<?> name) {
        return new ContentMetricTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ContentMetricTable where(Condition condition) {
        return new ContentMetricTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ContentMetricTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ContentMetricTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ContentMetricTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ContentMetricTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ContentMetricTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ContentMetricTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ContentMetricTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ContentMetricTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ContentMetricTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
