/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.UserPermissionRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 用户权限表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserPermissionTable extends TableImpl<UserPermissionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.user_permission</code>
     */
    public static final UserPermissionTable USER_PERMISSION = new UserPermissionTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserPermissionRecord> getRecordType() {
        return UserPermissionRecord.class;
    }

    /**
     * The column <code>creation.user_permission.id</code>. 主键
     */
    public final TableField<UserPermissionRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.user_permission.created_at</code>. 创建时间
     */
    public final TableField<UserPermissionRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.user_permission.updated_at</code>. 更新时间
     */
    public final TableField<UserPermissionRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.user_permission.uid</code>. 用户id
     */
    public final TableField<UserPermissionRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户id");

    /**
     * The column <code>creation.user_permission.permission_code</code>. 权限码
     */
    public final TableField<UserPermissionRecord, String> PERMISSION_CODE = createField(DSL.name("permission_code"), SQLDataType.VARCHAR(255).nullable(false), this, "权限码");

    /**
     * The column <code>creation.user_permission.payload</code>. 备注信息
     */
    public final TableField<UserPermissionRecord, JSONB> PAYLOAD = createField(DSL.name("payload"), SQLDataType.JSONB, this, "备注信息");

    /**
     * The column <code>creation.user_permission.status</code>. 有效性
     */
    public final TableField<UserPermissionRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(64).nullable(false), this, "有效性");

    private UserPermissionTable(Name alias, Table<UserPermissionRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private UserPermissionTable(Name alias, Table<UserPermissionRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("用户权限表"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.user_permission</code> table reference
     */
    public UserPermissionTable(String alias) {
        this(DSL.name(alias), USER_PERMISSION);
    }

    /**
     * Create an aliased <code>creation.user_permission</code> table reference
     */
    public UserPermissionTable(Name alias) {
        this(alias, USER_PERMISSION);
    }

    /**
     * Create a <code>creation.user_permission</code> table reference
     */
    public UserPermissionTable() {
        this(DSL.name("user_permission"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<UserPermissionRecord> getPrimaryKey() {
        return Keys.USER_PERMISSION_PKEY;
    }

    @Override
    public UserPermissionTable as(String alias) {
        return new UserPermissionTable(DSL.name(alias), this);
    }

    @Override
    public UserPermissionTable as(Name alias) {
        return new UserPermissionTable(alias, this);
    }

    @Override
    public UserPermissionTable as(Table<?> alias) {
        return new UserPermissionTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserPermissionTable rename(String name) {
        return new UserPermissionTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserPermissionTable rename(Name name) {
        return new UserPermissionTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserPermissionTable rename(Table<?> name) {
        return new UserPermissionTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPermissionTable where(Condition condition) {
        return new UserPermissionTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPermissionTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPermissionTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPermissionTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserPermissionTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserPermissionTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserPermissionTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserPermissionTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPermissionTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPermissionTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
