/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.UserVoiceLocaleTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserVoiceLocale;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 声音定制的口音
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserVoiceLocaleRecord extends UpdatableRecordImpl<UserVoiceLocaleRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_user_voice_locale.id</code>. 主键
     */
    public UserVoiceLocaleRecord setId(@Nullable Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.created_at</code>.
     * 创建时间
     */
    public UserVoiceLocaleRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.updated_at</code>.
     * 更新时间
     */
    public UserVoiceLocaleRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.voice_id</code>.
     * 所属声音ID
     */
    public UserVoiceLocaleRecord setVoiceId(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.voice_id</code>.
     * 所属声音ID
     */
    public Long getVoiceId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.voice_code</code>.
     * 所属声音编号
     */
    public UserVoiceLocaleRecord setVoiceCode(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.voice_code</code>.
     * 所属声音编号
     */
    public String getVoiceCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.code</code>. 本地化代号
     */
    public UserVoiceLocaleRecord setCode(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.code</code>. 本地化代号
     */
    public String getCode() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.desc</code>.
     * 本地化描述（中文展示）
     */
    public UserVoiceLocaleRecord setDesc(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.desc</code>.
     * 本地化描述（中文展示）
     */
    public String getDesc() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.lang_code</code>.
     * 语言代号
     */
    public UserVoiceLocaleRecord setLangCode(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.lang_code</code>.
     * 语言代号
     */
    public String getLangCode() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.lang_name</code>.
     * 语言名称（中文展示）
     */
    public UserVoiceLocaleRecord setLangName(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.lang_name</code>.
     * 语言名称（中文展示）
     */
    public String getLangName() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.country_code</code>.
     * 国家代号
     */
    public UserVoiceLocaleRecord setCountryCode(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.country_code</code>.
     * 国家代号
     */
    public String getCountryCode() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.country_name</code>.
     * 国家名称（中文展示）
     */
    public UserVoiceLocaleRecord setCountryName(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.country_name</code>.
     * 国家名称（中文展示）
     */
    public String getCountryName() {
        return (String) get(10);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.age_level</code>.
     * 默认年龄层
     */
    public UserVoiceLocaleRecord setAgeLevel(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.age_level</code>.
     * 默认年龄层
     */
    public String getAgeLevel() {
        return (String) get(11);
    }

    /**
     * Setter for
     * <code>creation.creation_user_voice_locale.preview_audio_id</code>.
     * 预览音频文件ID
     */
    public UserVoiceLocaleRecord setPreviewAudioId(@Nullable Long value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_voice_locale.preview_audio_id</code>.
     * 预览音频文件ID
     */
    @Nullable
    public Long getPreviewAudioId() {
        return (Long) get(12);
    }

    /**
     * Setter for
     * <code>creation.creation_user_voice_locale.preview_audio_url</code>.
     * 预览音频地址（OSS地址）
     */
    public UserVoiceLocaleRecord setPreviewAudioUrl(@Nullable String value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_voice_locale.preview_audio_url</code>.
     * 预览音频地址（OSS地址）
     */
    @Nullable
    public String getPreviewAudioUrl() {
        return (String) get(13);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.roles</code>. 可扮演角色
     */
    public UserVoiceLocaleRecord setRoles(JSONB value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.roles</code>. 可扮演角色
     */
    public JSONB getRoles() {
        return (JSONB) get(14);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.emotions</code>.
     * 可附加情绪
     */
    public UserVoiceLocaleRecord setEmotions(JSONB value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.emotions</code>.
     * 可附加情绪
     */
    public JSONB getEmotions() {
        return (JSONB) get(15);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.ext_info</code>.
     * 扩展信息
     */
    public UserVoiceLocaleRecord setExtInfo(JSONB value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.ext_info</code>.
     * 扩展信息
     */
    public JSONB getExtInfo() {
        return (JSONB) get(16);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.ordinal</code>. 排序字段
     */
    public UserVoiceLocaleRecord setOrdinal(@Nullable Integer value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.ordinal</code>. 排序字段
     */
    @Nullable
    public Integer getOrdinal() {
        return (Integer) get(17);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.lang_family</code>.
     * 语系类型
     */
    public UserVoiceLocaleRecord setLangFamily(@Nullable String value) {
        set(18, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.lang_family</code>.
     * 语系类型
     */
    @Nullable
    public String getLangFamily() {
        return (String) get(18);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.uid</code>. 用户ID
     */
    public UserVoiceLocaleRecord setUid(@Nullable Long value) {
        set(19, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.uid</code>. 用户ID
     */
    @Nullable
    public Long getUid() {
        return (Long) get(19);
    }

    /**
     * Setter for <code>creation.creation_user_voice_locale.voice_name</code>.
     * 本地化声音名称
     */
    public UserVoiceLocaleRecord setVoiceName(@Nullable String value) {
        set(20, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice_locale.voice_name</code>.
     * 本地化声音名称
     */
    @Nullable
    public String getVoiceName() {
        return (String) get(20);
    }

    /**
     * Setter for
     * <code>creation.creation_user_voice_locale.words_per_minute</code>.
     * 本地化口音语速
     */
    public UserVoiceLocaleRecord setWordsPerMinute(@Nullable Integer value) {
        set(21, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_voice_locale.words_per_minute</code>.
     * 本地化口音语速
     */
    @Nullable
    public Integer getWordsPerMinute() {
        return (Integer) get(21);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserVoiceLocaleRecord
     */
    public UserVoiceLocaleRecord() {
        super(UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE);
    }

    /**
     * Create a detached, initialised UserVoiceLocaleRecord
     */
    public UserVoiceLocaleRecord(@Nullable Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long voiceId, String voiceCode, String code, String desc, String langCode, String langName, String countryCode, String countryName, String ageLevel, @Nullable Long previewAudioId, @Nullable String previewAudioUrl, JSONB roles, JSONB emotions, JSONB extInfo, @Nullable Integer ordinal, @Nullable String langFamily, @Nullable Long uid, @Nullable String voiceName, @Nullable Integer wordsPerMinute) {
        super(UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setVoiceId(voiceId);
        setVoiceCode(voiceCode);
        setCode(code);
        setDesc(desc);
        setLangCode(langCode);
        setLangName(langName);
        setCountryCode(countryCode);
        setCountryName(countryName);
        setAgeLevel(ageLevel);
        setPreviewAudioId(previewAudioId);
        setPreviewAudioUrl(previewAudioUrl);
        setRoles(roles);
        setEmotions(emotions);
        setExtInfo(extInfo);
        setOrdinal(ordinal);
        setLangFamily(langFamily);
        setUid(uid);
        setVoiceName(voiceName);
        setWordsPerMinute(wordsPerMinute);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserVoiceLocaleRecord
     */
    public UserVoiceLocaleRecord(CreationUserVoiceLocale value) {
        super(UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setVoiceId(value.getVoiceId());
            setVoiceCode(value.getVoiceCode());
            setCode(value.getCode());
            setDesc(value.getDesc());
            setLangCode(value.getLangCode());
            setLangName(value.getLangName());
            setCountryCode(value.getCountryCode());
            setCountryName(value.getCountryName());
            setAgeLevel(value.getAgeLevel());
            setPreviewAudioId(value.getPreviewAudioId());
            setPreviewAudioUrl(value.getPreviewAudioUrl());
            setRoles(value.getRoles());
            setEmotions(value.getEmotions());
            setExtInfo(value.getExtInfo());
            setOrdinal(value.getOrdinal());
            setLangFamily(value.getLangFamily());
            setUid(value.getUid());
            setVoiceName(value.getVoiceName());
            setWordsPerMinute(value.getWordsPerMinute());
            resetChangedOnNotNull();
        }
    }
}
