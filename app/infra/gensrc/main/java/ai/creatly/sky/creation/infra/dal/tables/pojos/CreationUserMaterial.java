/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 用户素材中心
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationUserMaterial implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private Long materialId;
    private Long fileId;
    private String fileUrl;

    public CreationUserMaterial() {}

    public CreationUserMaterial(CreationUserMaterial value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.materialId = value.materialId;
        this.fileId = value.fileId;
        this.fileUrl = value.fileUrl;
    }

    public CreationUserMaterial(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        Long materialId,
        Long fileId,
        String fileUrl
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.materialId = materialId;
        this.fileId = fileId;
        this.fileUrl = fileUrl;
    }

    /**
     * Getter for <code>creation.creation_user_material.id</code>. 素材主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_user_material.id</code>. 素材主键
     */
    public CreationUserMaterial setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_user_material.created_at</code>. 创建时间
     */
    public CreationUserMaterial setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_user_material.updated_at</code>. 更新时间
     */
    public CreationUserMaterial setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.uid</code>. 用户id
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_user_material.uid</code>. 用户id
     */
    public CreationUserMaterial setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.material_id</code>.
     * 素材主表id
     */
    public Long getMaterialId() {
        return this.materialId;
    }

    /**
     * Setter for <code>creation.creation_user_material.material_id</code>.
     * 素材主表id
     */
    public CreationUserMaterial setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.file_id</code>. 文件id
     */
    public Long getFileId() {
        return this.fileId;
    }

    /**
     * Setter for <code>creation.creation_user_material.file_id</code>. 文件id
     */
    public CreationUserMaterial setFileId(Long fileId) {
        this.fileId = fileId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_material.file_url</code>.
     * 文件地址oss://
     */
    public String getFileUrl() {
        return this.fileUrl;
    }

    /**
     * Setter for <code>creation.creation_user_material.file_url</code>.
     * 文件地址oss://
     */
    public CreationUserMaterial setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationUserMaterial other = (CreationUserMaterial) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.materialId == null) {
            if (other.materialId != null)
                return false;
        }
        else if (!this.materialId.equals(other.materialId))
            return false;
        if (this.fileId == null) {
            if (other.fileId != null)
                return false;
        }
        else if (!this.fileId.equals(other.fileId))
            return false;
        if (this.fileUrl == null) {
            if (other.fileUrl != null)
                return false;
        }
        else if (!this.fileUrl.equals(other.fileUrl))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.materialId == null) ? 0 : this.materialId.hashCode());
        result = prime * result + ((this.fileId == null) ? 0 : this.fileId.hashCode());
        result = prime * result + ((this.fileUrl == null) ? 0 : this.fileUrl.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationUserMaterial (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(materialId);
        sb.append(", ").append(fileId);
        sb.append(", ").append(fileUrl);

        sb.append(")");
        return sb.toString();
    }
}
