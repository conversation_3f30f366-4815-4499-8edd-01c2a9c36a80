/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.UserPlanRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 用户私人定价计划
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserPlanTable extends TableImpl<UserPlanRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_user_plan</code>
     */
    public static final UserPlanTable CREATION_USER_PLAN = new UserPlanTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserPlanRecord> getRecordType() {
        return UserPlanRecord.class;
    }

    /**
     * The column <code>creation.creation_user_plan.id</code>. 主键
     */
    public final TableField<UserPlanRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_user_plan.created_at</code>. 创建时间
     */
    public final TableField<UserPlanRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_user_plan.updated_at</code>. 更新时间
     */
    public final TableField<UserPlanRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_user_plan.created_by</code>. 创建者
     */
    public final TableField<UserPlanRecord, JSONB> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.JSONB.nullable(false), this, "创建者");

    /**
     * The column <code>creation.creation_user_plan.updated_by</code>. 更新者
     */
    public final TableField<UserPlanRecord, JSONB> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.JSONB.nullable(false), this, "更新者");

    /**
     * The column <code>creation.creation_user_plan.uid</code>. 用户ID
     */
    public final TableField<UserPlanRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户ID");

    /**
     * The column <code>creation.creation_user_plan.platform_plan_id</code>.
     * 平台统一定价计划ID
     */
    public final TableField<UserPlanRecord, String> PLATFORM_PLAN_ID = createField(DSL.name("platform_plan_id"), SQLDataType.VARCHAR(64).nullable(false), this, "平台统一定价计划ID");

    /**
     * The column <code>creation.creation_user_plan.type</code>. 计划类型
     */
    public final TableField<UserPlanRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(32).nullable(false), this, "计划类型");

    /**
     * The column <code>creation.creation_user_plan.name</code>. 计划名称
     */
    public final TableField<UserPlanRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(64).nullable(false), this, "计划名称");

    /**
     * The column <code>creation.creation_user_plan.description</code>. 分类
     */
    public final TableField<UserPlanRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(128).nullable(false), this, "分类");

    /**
     * The column <code>creation.creation_user_plan.original_fee</code>. 原价（分）
     */
    public final TableField<UserPlanRecord, Long> ORIGINAL_FEE = createField(DSL.name("original_fee"), SQLDataType.BIGINT.nullable(false), this, "原价（分）");

    /**
     * The column <code>creation.creation_user_plan.real_fee</code>. 实价（分）
     */
    public final TableField<UserPlanRecord, Long> REAL_FEE = createField(DSL.name("real_fee"), SQLDataType.BIGINT.nullable(false), this, "实价（分）");

    /**
     * The column <code>creation.creation_user_plan.order_title</code>. 订单标题
     */
    public final TableField<UserPlanRecord, String> ORDER_TITLE = createField(DSL.name("order_title"), SQLDataType.VARCHAR(64).nullable(false), this, "订单标题");

    /**
     * The column <code>creation.creation_user_plan.period_type</code>. 计划周期类型
     */
    public final TableField<UserPlanRecord, String> PERIOD_TYPE = createField(DSL.name("period_type"), SQLDataType.VARCHAR(32).nullable(false), this, "计划周期类型");

    /**
     * The column <code>creation.creation_user_plan.auto_renewed</code>.
     * 到期是否自动续费（如果为空，则取决于用户选择）
     */
    public final TableField<UserPlanRecord, Boolean> AUTO_RENEWED = createField(DSL.name("auto_renewed"), SQLDataType.BOOLEAN, this, "到期是否自动续费（如果为空，则取决于用户选择）");

    /**
     * The column <code>creation.creation_user_plan.level</code>.
     * 阶梯档位（用于排序、有序展示）
     */
    public final TableField<UserPlanRecord, Integer> LEVEL = createField(DSL.name("level"), SQLDataType.INTEGER.nullable(false), this, "阶梯档位（用于排序、有序展示）");

    /**
     * The column <code>creation.creation_user_plan.benefit_duration</code>.
     * 权益有效期
     */
    public final TableField<UserPlanRecord, String> BENEFIT_DURATION = createField(DSL.name("benefit_duration"), SQLDataType.VARCHAR(32), this, "权益有效期");

    /**
     * The column <code>creation.creation_user_plan.benefits</code>. 权益列表
     */
    public final TableField<UserPlanRecord, JSONB> BENEFITS = createField(DSL.name("benefits"), SQLDataType.JSONB.nullable(false), this, "权益列表");

    private UserPlanTable(Name alias, Table<UserPlanRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private UserPlanTable(Name alias, Table<UserPlanRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("用户私人定价计划"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_user_plan</code> table
     * reference
     */
    public UserPlanTable(String alias) {
        this(DSL.name(alias), CREATION_USER_PLAN);
    }

    /**
     * Create an aliased <code>creation.creation_user_plan</code> table
     * reference
     */
    public UserPlanTable(Name alias) {
        this(alias, CREATION_USER_PLAN);
    }

    /**
     * Create a <code>creation.creation_user_plan</code> table reference
     */
    public UserPlanTable() {
        this(DSL.name("creation_user_plan"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<UserPlanRecord> getPrimaryKey() {
        return Keys.CREATION_USER_PLAN_PKEY;
    }

    @Override
    public List<UniqueKey<UserPlanRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CREATION_USER_PLAN_UID_PLATFORM_PLAN_ID_KEY);
    }

    @Override
    public UserPlanTable as(String alias) {
        return new UserPlanTable(DSL.name(alias), this);
    }

    @Override
    public UserPlanTable as(Name alias) {
        return new UserPlanTable(alias, this);
    }

    @Override
    public UserPlanTable as(Table<?> alias) {
        return new UserPlanTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserPlanTable rename(String name) {
        return new UserPlanTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserPlanTable rename(Name name) {
        return new UserPlanTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserPlanTable rename(Table<?> name) {
        return new UserPlanTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPlanTable where(Condition condition) {
        return new UserPlanTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPlanTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPlanTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPlanTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserPlanTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserPlanTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserPlanTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserPlanTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPlanTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPlanTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
