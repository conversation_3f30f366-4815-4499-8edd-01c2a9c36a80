/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 证书
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseCertificate implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String coverUrl;
    private Long courseId;
    private String name;
    private Long totalScore;
    private Long passScore;
    private Long limitExam;
    private String orgCode;

    public CourseCertificate() {}

    public CourseCertificate(CourseCertificate value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.coverUrl = value.coverUrl;
        this.courseId = value.courseId;
        this.name = value.name;
        this.totalScore = value.totalScore;
        this.passScore = value.passScore;
        this.limitExam = value.limitExam;
        this.orgCode = value.orgCode;
    }

    public CourseCertificate(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        @Nullable String coverUrl,
        @Nullable Long courseId,
        @Nullable String name,
        @Nullable Long totalScore,
        @Nullable Long passScore,
        @Nullable Long limitExam,
        @Nullable String orgCode
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.coverUrl = coverUrl;
        this.courseId = courseId;
        this.name = name;
        this.totalScore = totalScore;
        this.passScore = passScore;
        this.limitExam = limitExam;
        this.orgCode = orgCode;
    }

    /**
     * Getter for <code>creation.course_certificate.id</code>. 证书id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.course_certificate.id</code>. 证书id
     */
    public CourseCertificate setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.course_certificate.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.course_certificate.created_at</code>. 创建时间
     */
    public CourseCertificate setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.course_certificate.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.course_certificate.updated_at</code>. 更新时间
     */
    public CourseCertificate setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.course_certificate.cover_url</code>. 证书封面
     */
    @Nullable
    public String getCoverUrl() {
        return this.coverUrl;
    }

    /**
     * Setter for <code>creation.course_certificate.cover_url</code>. 证书封面
     */
    public CourseCertificate setCoverUrl(@Nullable String coverUrl) {
        this.coverUrl = coverUrl;
        return this;
    }

    /**
     * Getter for <code>creation.course_certificate.course_id</code>. 关联课程id
     */
    @Nullable
    public Long getCourseId() {
        return this.courseId;
    }

    /**
     * Setter for <code>creation.course_certificate.course_id</code>. 关联课程id
     */
    public CourseCertificate setCourseId(@Nullable Long courseId) {
        this.courseId = courseId;
        return this;
    }

    /**
     * Getter for <code>creation.course_certificate.name</code>. 证书名称
     */
    @Nullable
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.course_certificate.name</code>. 证书名称
     */
    public CourseCertificate setName(@Nullable String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.course_certificate.total_score</code>. 总分
     */
    @Nullable
    public Long getTotalScore() {
        return this.totalScore;
    }

    /**
     * Setter for <code>creation.course_certificate.total_score</code>. 总分
     */
    public CourseCertificate setTotalScore(@Nullable Long totalScore) {
        this.totalScore = totalScore;
        return this;
    }

    /**
     * Getter for <code>creation.course_certificate.pass_score</code>. 最低分
     */
    @Nullable
    public Long getPassScore() {
        return this.passScore;
    }

    /**
     * Setter for <code>creation.course_certificate.pass_score</code>. 最低分
     */
    public CourseCertificate setPassScore(@Nullable Long passScore) {
        this.passScore = passScore;
        return this;
    }

    /**
     * Getter for <code>creation.course_certificate.limit_exam</code>. 限制次数
     */
    @Nullable
    public Long getLimitExam() {
        return this.limitExam;
    }

    /**
     * Setter for <code>creation.course_certificate.limit_exam</code>. 限制次数
     */
    public CourseCertificate setLimitExam(@Nullable Long limitExam) {
        this.limitExam = limitExam;
        return this;
    }

    /**
     * Getter for <code>creation.course_certificate.org_code</code>. 组织机构
     */
    @Nullable
    public String getOrgCode() {
        return this.orgCode;
    }

    /**
     * Setter for <code>creation.course_certificate.org_code</code>. 组织机构
     */
    public CourseCertificate setOrgCode(@Nullable String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CourseCertificate other = (CourseCertificate) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.coverUrl == null) {
            if (other.coverUrl != null)
                return false;
        }
        else if (!this.coverUrl.equals(other.coverUrl))
            return false;
        if (this.courseId == null) {
            if (other.courseId != null)
                return false;
        }
        else if (!this.courseId.equals(other.courseId))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.totalScore == null) {
            if (other.totalScore != null)
                return false;
        }
        else if (!this.totalScore.equals(other.totalScore))
            return false;
        if (this.passScore == null) {
            if (other.passScore != null)
                return false;
        }
        else if (!this.passScore.equals(other.passScore))
            return false;
        if (this.limitExam == null) {
            if (other.limitExam != null)
                return false;
        }
        else if (!this.limitExam.equals(other.limitExam))
            return false;
        if (this.orgCode == null) {
            if (other.orgCode != null)
                return false;
        }
        else if (!this.orgCode.equals(other.orgCode))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.coverUrl == null) ? 0 : this.coverUrl.hashCode());
        result = prime * result + ((this.courseId == null) ? 0 : this.courseId.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.totalScore == null) ? 0 : this.totalScore.hashCode());
        result = prime * result + ((this.passScore == null) ? 0 : this.passScore.hashCode());
        result = prime * result + ((this.limitExam == null) ? 0 : this.limitExam.hashCode());
        result = prime * result + ((this.orgCode == null) ? 0 : this.orgCode.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseCertificate (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(coverUrl);
        sb.append(", ").append(courseId);
        sb.append(", ").append(name);
        sb.append(", ").append(totalScore);
        sb.append(", ").append(passScore);
        sb.append(", ").append(limitExam);
        sb.append(", ").append(orgCode);

        sb.append(")");
        return sb.toString();
    }
}
