/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.OrganizationTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.Organization;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 组织主表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class OrganizationRecord extends UpdatableRecordImpl<OrganizationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.organization.id</code>. 主键
     */
    public OrganizationRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.organization.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.organization.created_at</code>. 创建时间
     */
    public OrganizationRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.organization.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.organization.updated_at</code>. 更新时间
     */
    public OrganizationRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.organization.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.organization.code</code>. 组织标识码
     */
    public OrganizationRecord setCode(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.organization.code</code>. 组织标识码
     */
    public String getCode() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.organization.name</code>. 组织名称
     */
    public OrganizationRecord setName(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.organization.name</code>. 组织名称
     */
    public String getName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.organization.uid</code>. 企业法人
     */
    public OrganizationRecord setUid(Long value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.organization.uid</code>. 企业法人
     */
    public Long getUid() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>creation.organization.qualification</code>. 组织资质
     */
    public OrganizationRecord setQualification(@Nullable JSONB value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.organization.qualification</code>. 组织资质
     */
    @Nullable
    public JSONB getQualification() {
        return (JSONB) get(6);
    }

    /**
     * Setter for <code>creation.organization.logo_url</code>. logo地址
     */
    public OrganizationRecord setLogoUrl(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.organization.logo_url</code>. logo地址
     */
    @Nullable
    public String getLogoUrl() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached OrganizationRecord
     */
    public OrganizationRecord() {
        super(OrganizationTable.ORGANIZATION);
    }

    /**
     * Create a detached, initialised OrganizationRecord
     */
    public OrganizationRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, String code, String name, Long uid, @Nullable JSONB qualification, @Nullable String logoUrl) {
        super(OrganizationTable.ORGANIZATION);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCode(code);
        setName(name);
        setUid(uid);
        setQualification(qualification);
        setLogoUrl(logoUrl);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised OrganizationRecord
     */
    public OrganizationRecord(Organization value) {
        super(OrganizationTable.ORGANIZATION);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCode(value.getCode());
            setName(value.getName());
            setUid(value.getUid());
            setQualification(value.getQualification());
            setLogoUrl(value.getLogoUrl());
            resetChangedOnNotNull();
        }
    }
}
