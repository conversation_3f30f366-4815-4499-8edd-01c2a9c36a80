/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 故事导出信息表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationStoryExport implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String status;
    private Long downFileId;
    private String downFileUrl;
    private Long storyId;
    private String exportFormat;

    public CreationStoryExport() {}

    public CreationStoryExport(CreationStoryExport value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.status = value.status;
        this.downFileId = value.downFileId;
        this.downFileUrl = value.downFileUrl;
        this.storyId = value.storyId;
        this.exportFormat = value.exportFormat;
    }

    public CreationStoryExport(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String status,
        Long downFileId,
        String downFileUrl,
        Long storyId,
        String exportFormat
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.status = status;
        this.downFileId = downFileId;
        this.downFileUrl = downFileUrl;
        this.storyId = storyId;
        this.exportFormat = exportFormat;
    }

    /**
     * Getter for <code>creation.creation_story_export.id</code>. 主键ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_story_export.id</code>. 主键ID
     */
    public CreationStoryExport setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_story_export.created_at</code>. 创建时间
     */
    public CreationStoryExport setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_story_export.updated_at</code>. 更新时间
     */
    public CreationStoryExport setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.uid</code>. 所属用户ID（1为平台）
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_story_export.uid</code>. 所属用户ID（1为平台）
     */
    public CreationStoryExport setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.status</code>. 状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_story_export.status</code>. 状态
     */
    public CreationStoryExport setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.down_file_id</code>.
     * 下载故事文件ID
     */
    public Long getDownFileId() {
        return this.downFileId;
    }

    /**
     * Setter for <code>creation.creation_story_export.down_file_id</code>.
     * 下载故事文件ID
     */
    public CreationStoryExport setDownFileId(Long downFileId) {
        this.downFileId = downFileId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.down_file_url</code>.
     * 下载故事文件地址（OSS格式）
     */
    public String getDownFileUrl() {
        return this.downFileUrl;
    }

    /**
     * Setter for <code>creation.creation_story_export.down_file_url</code>.
     * 下载故事文件地址（OSS格式）
     */
    public CreationStoryExport setDownFileUrl(String downFileUrl) {
        this.downFileUrl = downFileUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.story_id</code>. 故事ID
     */
    public Long getStoryId() {
        return this.storyId;
    }

    /**
     * Setter for <code>creation.creation_story_export.story_id</code>. 故事ID
     */
    public CreationStoryExport setStoryId(Long storyId) {
        this.storyId = storyId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.export_format</code>.
     * 导出格式
     */
    public String getExportFormat() {
        return this.exportFormat;
    }

    /**
     * Setter for <code>creation.creation_story_export.export_format</code>.
     * 导出格式
     */
    public CreationStoryExport setExportFormat(String exportFormat) {
        this.exportFormat = exportFormat;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationStoryExport other = (CreationStoryExport) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.downFileId == null) {
            if (other.downFileId != null)
                return false;
        }
        else if (!this.downFileId.equals(other.downFileId))
            return false;
        if (this.downFileUrl == null) {
            if (other.downFileUrl != null)
                return false;
        }
        else if (!this.downFileUrl.equals(other.downFileUrl))
            return false;
        if (this.storyId == null) {
            if (other.storyId != null)
                return false;
        }
        else if (!this.storyId.equals(other.storyId))
            return false;
        if (this.exportFormat == null) {
            if (other.exportFormat != null)
                return false;
        }
        else if (!this.exportFormat.equals(other.exportFormat))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.downFileId == null) ? 0 : this.downFileId.hashCode());
        result = prime * result + ((this.downFileUrl == null) ? 0 : this.downFileUrl.hashCode());
        result = prime * result + ((this.storyId == null) ? 0 : this.storyId.hashCode());
        result = prime * result + ((this.exportFormat == null) ? 0 : this.exportFormat.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationStoryExport (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(status);
        sb.append(", ").append(downFileId);
        sb.append(", ").append(downFileUrl);
        sb.append(", ").append(storyId);
        sb.append(", ").append(exportFormat);

        sb.append(")");
        return sb.toString();
    }
}
