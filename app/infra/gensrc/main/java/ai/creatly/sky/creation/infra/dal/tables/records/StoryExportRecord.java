/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.StoryExportTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationStoryExport;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 故事导出信息表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class StoryExportRecord extends UpdatableRecordImpl<StoryExportRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_story_export.id</code>. 主键ID
     */
    public StoryExportRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.id</code>. 主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_story_export.created_at</code>. 创建时间
     */
    public StoryExportRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_story_export.updated_at</code>. 更新时间
     */
    public StoryExportRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_story_export.uid</code>. 所属用户ID（1为平台）
     */
    public StoryExportRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.uid</code>. 所属用户ID（1为平台）
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_story_export.status</code>. 状态
     */
    public StoryExportRecord setStatus(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.status</code>. 状态
     */
    public String getStatus() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_story_export.down_file_id</code>.
     * 下载故事文件ID
     */
    public StoryExportRecord setDownFileId(Long value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.down_file_id</code>.
     * 下载故事文件ID
     */
    public Long getDownFileId() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>creation.creation_story_export.down_file_url</code>.
     * 下载故事文件地址（OSS格式）
     */
    public StoryExportRecord setDownFileUrl(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.down_file_url</code>.
     * 下载故事文件地址（OSS格式）
     */
    public String getDownFileUrl() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_story_export.story_id</code>. 故事ID
     */
    public StoryExportRecord setStoryId(Long value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.story_id</code>. 故事ID
     */
    public Long getStoryId() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>creation.creation_story_export.export_format</code>.
     * 导出格式
     */
    public StoryExportRecord setExportFormat(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_export.export_format</code>.
     * 导出格式
     */
    public String getExportFormat() {
        return (String) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StoryExportRecord
     */
    public StoryExportRecord() {
        super(StoryExportTable.CREATION_STORY_EXPORT);
    }

    /**
     * Create a detached, initialised StoryExportRecord
     */
    public StoryExportRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String status, Long downFileId, String downFileUrl, Long storyId, String exportFormat) {
        super(StoryExportTable.CREATION_STORY_EXPORT);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setStatus(status);
        setDownFileId(downFileId);
        setDownFileUrl(downFileUrl);
        setStoryId(storyId);
        setExportFormat(exportFormat);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised StoryExportRecord
     */
    public StoryExportRecord(CreationStoryExport value) {
        super(StoryExportTable.CREATION_STORY_EXPORT);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setStatus(value.getStatus());
            setDownFileId(value.getDownFileId());
            setDownFileUrl(value.getDownFileUrl());
            setStoryId(value.getStoryId());
            setExportFormat(value.getExportFormat());
            resetChangedOnNotNull();
        }
    }
}
