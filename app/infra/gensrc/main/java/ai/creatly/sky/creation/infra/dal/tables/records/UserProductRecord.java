/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.UserProductTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserProduct;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户产品
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserProductRecord extends UpdatableRecordImpl<UserProductRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_user_product.id</code>. 主键
     */
    public UserProductRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_user_product.created_at</code>. 创建时间
     */
    public UserProductRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_user_product.updated_at</code>. 更新时间
     */
    public UserProductRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_user_product.uid</code>. 用户ID
     */
    public UserProductRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.uid</code>. 用户ID
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_user_product.title</code>. 文件名
     */
    public UserProductRecord setTitle(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.title</code>. 文件名
     */
    public String getTitle() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_user_product.status</code>. 产品状态
     */
    public UserProductRecord setStatus(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.status</code>. 产品状态
     */
    public String getStatus() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_user_product.category</code>. 产品类目
     */
    public UserProductRecord setCategory(JSONB value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.category</code>. 产品类目
     */
    public JSONB getCategory() {
        return (JSONB) get(6);
    }

    /**
     * Setter for <code>creation.creation_user_product.crowd_categories</code>.
     * 产品适用人群类目
     */
    public UserProductRecord setCrowdCategories(JSONB value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.crowd_categories</code>.
     * 产品适用人群类目
     */
    public JSONB getCrowdCategories() {
        return (JSONB) get(7);
    }

    /**
     * Setter for <code>creation.creation_user_product.features</code>. 产品卖点
     */
    public UserProductRecord setFeatures(String[] value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.features</code>. 产品卖点
     */
    public String[] getFeatures() {
        return (String[]) get(8);
    }

    /**
     * Setter for <code>creation.creation_user_product.description</code>.
     * 产品详细描述
     */
    public UserProductRecord setDescription(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.description</code>.
     * 产品详细描述
     */
    public String getDescription() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_user_product.cover_file_id</code>.
     * 产品封面图文件ID
     */
    public UserProductRecord setCoverFileId(@Nullable Long value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.cover_file_id</code>.
     * 产品封面图文件ID
     */
    @Nullable
    public Long getCoverFileId() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>creation.creation_user_product.cover_url</code>.
     * 产品封面图地址（OSS地址）
     */
    public UserProductRecord setCoverUrl(@Nullable String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.cover_url</code>.
     * 产品封面图地址（OSS地址）
     */
    @Nullable
    public String getCoverUrl() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_user_product.labels</code>. 产品标签列表
     */
    public UserProductRecord setLabels(JSONB value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.labels</code>. 产品标签列表
     */
    public JSONB getLabels() {
        return (JSONB) get(12);
    }

    /**
     * Setter for <code>creation.creation_user_product.assets</code>. 产品素材列表
     */
    public UserProductRecord setAssets(JSONB value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_product.assets</code>. 产品素材列表
     */
    public JSONB getAssets() {
        return (JSONB) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserProductRecord
     */
    public UserProductRecord() {
        super(UserProductTable.CREATION_USER_PRODUCT);
    }

    /**
     * Create a detached, initialised UserProductRecord
     */
    public UserProductRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String title, String status, JSONB category, JSONB crowdCategories, String[] features, String description, @Nullable Long coverFileId, @Nullable String coverUrl, JSONB labels, JSONB assets) {
        super(UserProductTable.CREATION_USER_PRODUCT);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setTitle(title);
        setStatus(status);
        setCategory(category);
        setCrowdCategories(crowdCategories);
        setFeatures(features);
        setDescription(description);
        setCoverFileId(coverFileId);
        setCoverUrl(coverUrl);
        setLabels(labels);
        setAssets(assets);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserProductRecord
     */
    public UserProductRecord(CreationUserProduct value) {
        super(UserProductTable.CREATION_USER_PRODUCT);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setTitle(value.getTitle());
            setStatus(value.getStatus());
            setCategory(value.getCategory());
            setCrowdCategories(value.getCrowdCategories());
            setFeatures(value.getFeatures());
            setDescription(value.getDescription());
            setCoverFileId(value.getCoverFileId());
            setCoverUrl(value.getCoverUrl());
            setLabels(value.getLabels());
            setAssets(value.getAssets());
            resetChangedOnNotNull();
        }
    }
}
