/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.UserProductTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserProduct;
import ai.creatly.sky.creation.infra.dal.tables.records.UserProductRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.JSONB;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 用户产品
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class UserProductDAO extends DAOImpl<UserProductRecord, CreationUserProduct, Long> {

    /**
     * Create a new UserProductDAO without any configuration
     */
    public UserProductDAO() {
        super(UserProductTable.CREATION_USER_PRODUCT, CreationUserProduct.class);
    }

    /**
     * Create a new UserProductDAO with an attached configuration
     */
    @Autowired
    public UserProductDAO(Configuration configuration) {
        super(UserProductTable.CREATION_USER_PRODUCT, CreationUserProduct.class, configuration);
    }

    @Override
    public Long getId(CreationUserProduct object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationUserProduct> fetchById(Long... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationUserProduct fetchOneById(Long value) {
        return fetchOne(UserProductTable.CREATION_USER_PRODUCT.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationUserProduct> fetchOptionalById(Long value) {
        return fetchOptional(UserProductTable.CREATION_USER_PRODUCT.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationUserProduct> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationUserProduct> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<CreationUserProduct> fetchByUid(Long... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.UID, values);
    }

    /**
     * Fetch records that have <code>title BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfTitle(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.TITLE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>title IN (values)</code>
     */
    public List<CreationUserProduct> fetchByTitle(String... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.TITLE, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CreationUserProduct> fetchByStatus(String... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.STATUS, values);
    }

    /**
     * Fetch records that have <code>category BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfCategory(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.CATEGORY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>category IN (values)</code>
     */
    public List<CreationUserProduct> fetchByCategory(JSONB... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.CATEGORY, values);
    }

    /**
     * Fetch records that have <code>crowd_categories BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfCrowdCategories(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.CROWD_CATEGORIES, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>crowd_categories IN (values)</code>
     */
    public List<CreationUserProduct> fetchByCrowdCategories(JSONB... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.CROWD_CATEGORIES, values);
    }

    /**
     * Fetch records that have <code>features BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfFeatures(String[] lowerInclusive, String[] upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.FEATURES, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>features IN (values)</code>
     */
    public List<CreationUserProduct> fetchByFeatures(String[]... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.FEATURES, values);
    }

    /**
     * Fetch records that have <code>description BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfDescription(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.DESCRIPTION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>description IN (values)</code>
     */
    public List<CreationUserProduct> fetchByDescription(String... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.DESCRIPTION, values);
    }

    /**
     * Fetch records that have <code>cover_file_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfCoverFileId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.COVER_FILE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover_file_id IN (values)</code>
     */
    public List<CreationUserProduct> fetchByCoverFileId(Long... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.COVER_FILE_ID, values);
    }

    /**
     * Fetch records that have <code>cover_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfCoverUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.COVER_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover_url IN (values)</code>
     */
    public List<CreationUserProduct> fetchByCoverUrl(String... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.COVER_URL, values);
    }

    /**
     * Fetch records that have <code>labels BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfLabels(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.LABELS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>labels IN (values)</code>
     */
    public List<CreationUserProduct> fetchByLabels(JSONB... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.LABELS, values);
    }

    /**
     * Fetch records that have <code>assets BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserProduct> fetchRangeOfAssets(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(UserProductTable.CREATION_USER_PRODUCT.ASSETS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>assets IN (values)</code>
     */
    public List<CreationUserProduct> fetchByAssets(JSONB... values) {
        return fetch(UserProductTable.CREATION_USER_PRODUCT.ASSETS, values);
    }
}
