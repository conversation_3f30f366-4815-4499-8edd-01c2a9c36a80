/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.PromptCategoryTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationPromptCategory;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 提示词类目
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class PromptCategoryRecord extends UpdatableRecordImpl<PromptCategoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_prompt_category.id</code>. 主键
     */
    public PromptCategoryRecord setId(@Nullable Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_prompt_category.created_at</code>.
     * 创建时间
     */
    public PromptCategoryRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_prompt_category.updated_at</code>.
     * 更新时间
     */
    public PromptCategoryRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_prompt_category.biz_source</code>.
     */
    public PromptCategoryRecord setBizSource(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.biz_source</code>.
     */
    public String getBizSource() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_prompt_category.code</code>. 用户ID
     */
    public PromptCategoryRecord setCode(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.code</code>. 用户ID
     */
    public String getCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_prompt_category.name</code>. 用户ID
     */
    public PromptCategoryRecord setName(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.name</code>. 用户ID
     */
    public String getName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_prompt_category.en_name</code>. 用户ID
     */
    public PromptCategoryRecord setEnName(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.en_name</code>. 用户ID
     */
    public String getEnName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_prompt_category.status</code>. 用户ID
     */
    public PromptCategoryRecord setStatus(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.status</code>. 用户ID
     */
    public String getStatus() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached PromptCategoryRecord
     */
    public PromptCategoryRecord() {
        super(PromptCategoryTable.CREATION_PROMPT_CATEGORY);
    }

    /**
     * Create a detached, initialised PromptCategoryRecord
     */
    public PromptCategoryRecord(@Nullable Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, String bizSource, String code, String name, String enName, String status) {
        super(PromptCategoryTable.CREATION_PROMPT_CATEGORY);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setBizSource(bizSource);
        setCode(code);
        setName(name);
        setEnName(enName);
        setStatus(status);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised PromptCategoryRecord
     */
    public PromptCategoryRecord(CreationPromptCategory value) {
        super(PromptCategoryTable.CREATION_PROMPT_CATEGORY);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setBizSource(value.getBizSource());
            setCode(value.getCode());
            setName(value.getName());
            setEnName(value.getEnName());
            setStatus(value.getStatus());
            resetChangedOnNotNull();
        }
    }
}
