/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.ConversationMessageTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationConversationMessage;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 会话消息
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class ConversationMessageRecord extends UpdatableRecordImpl<ConversationMessageRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_conversation_message.id</code>. 主键
     */
    public ConversationMessageRecord setId(@Nullable Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_message.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for
     * <code>creation.creation_conversation_message.created_at</code>. 创建时间
     */
    public ConversationMessageRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_conversation_message.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for
     * <code>creation.creation_conversation_message.updated_at</code>. 更新时间
     */
    public ConversationMessageRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_conversation_message.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_conversation_message.uid</code>. 用户ID
     */
    public ConversationMessageRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_message.uid</code>. 用户ID
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for
     * <code>creation.creation_conversation_message.conversation_id</code>. 会话ID
     */
    public ConversationMessageRecord setConversationId(Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_conversation_message.conversation_id</code>. 会话ID
     */
    public Long getConversationId() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.creation_conversation_message.role</code>. 角色
     */
    public ConversationMessageRecord setRole(JSONB value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_message.role</code>. 角色
     */
    public JSONB getRole() {
        return (JSONB) get(5);
    }

    /**
     * Setter for <code>creation.creation_conversation_message.content</code>.
     * 内容
     */
    public ConversationMessageRecord setContent(JSONB value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_message.content</code>.
     * 内容
     */
    public JSONB getContent() {
        return (JSONB) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ConversationMessageRecord
     */
    public ConversationMessageRecord() {
        super(ConversationMessageTable.CREATION_CONVERSATION_MESSAGE);
    }

    /**
     * Create a detached, initialised ConversationMessageRecord
     */
    public ConversationMessageRecord(@Nullable Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, Long conversationId, JSONB role, JSONB content) {
        super(ConversationMessageTable.CREATION_CONVERSATION_MESSAGE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setConversationId(conversationId);
        setRole(role);
        setContent(content);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised ConversationMessageRecord
     */
    public ConversationMessageRecord(CreationConversationMessage value) {
        super(ConversationMessageTable.CREATION_CONVERSATION_MESSAGE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setConversationId(value.getConversationId());
            setRole(value.getRole());
            setContent(value.getContent());
            resetChangedOnNotNull();
        }
    }
}
