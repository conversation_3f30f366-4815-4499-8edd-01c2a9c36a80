/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.Arrays;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 用户素材库
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationUserAsset implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String status;
    private String name;
    private String type;
    private JSONB file;
    private JSONB content;
    private Long coverFileId;
    private String coverUrl;
    private String description;
    private JSONB metadata;
    private String[] keywords;
    private JSONB labels;
    private JSONB videoSlices;

    public CreationUserAsset() {}

    public CreationUserAsset(CreationUserAsset value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.status = value.status;
        this.name = value.name;
        this.type = value.type;
        this.file = value.file;
        this.content = value.content;
        this.coverFileId = value.coverFileId;
        this.coverUrl = value.coverUrl;
        this.description = value.description;
        this.metadata = value.metadata;
        this.keywords = value.keywords;
        this.labels = value.labels;
        this.videoSlices = value.videoSlices;
    }

    public CreationUserAsset(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String status,
        String name,
        String type,
        JSONB file,
        JSONB content,
        @Nullable Long coverFileId,
        @Nullable String coverUrl,
        String description,
        JSONB metadata,
        String[] keywords,
        JSONB labels,
        @Nullable JSONB videoSlices
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.status = status;
        this.name = name;
        this.type = type;
        this.file = file;
        this.content = content;
        this.coverFileId = coverFileId;
        this.coverUrl = coverUrl;
        this.description = description;
        this.metadata = metadata;
        this.keywords = keywords;
        this.labels = labels;
        this.videoSlices = videoSlices;
    }

    /**
     * Getter for <code>creation.creation_user_asset.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_user_asset.id</code>. 主键
     */
    public CreationUserAsset setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_user_asset.created_at</code>. 创建时间
     */
    public CreationUserAsset setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_user_asset.updated_at</code>. 更新时间
     */
    public CreationUserAsset setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.uid</code>. 用户ID
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_user_asset.uid</code>. 用户ID
     */
    public CreationUserAsset setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.status</code>. 素材状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_user_asset.status</code>. 素材状态
     */
    public CreationUserAsset setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.name</code>. 素材名称
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.creation_user_asset.name</code>. 素材名称
     */
    public CreationUserAsset setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.type</code>. 素材类型
     */
    public String getType() {
        return this.type;
    }

    /**
     * Setter for <code>creation.creation_user_asset.type</code>. 素材类型
     */
    public CreationUserAsset setType(String type) {
        this.type = type;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.file</code>. 素材文件
     */
    public JSONB getFile() {
        return this.file;
    }

    /**
     * Setter for <code>creation.creation_user_asset.file</code>. 素材文件
     */
    public CreationUserAsset setFile(JSONB file) {
        this.file = file;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.content</code>. 素材内容
     */
    public JSONB getContent() {
        return this.content;
    }

    /**
     * Setter for <code>creation.creation_user_asset.content</code>. 素材内容
     */
    public CreationUserAsset setContent(JSONB content) {
        this.content = content;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.cover_file_id</code>.
     * 素材封面图文件ID
     */
    @Nullable
    public Long getCoverFileId() {
        return this.coverFileId;
    }

    /**
     * Setter for <code>creation.creation_user_asset.cover_file_id</code>.
     * 素材封面图文件ID
     */
    public CreationUserAsset setCoverFileId(@Nullable Long coverFileId) {
        this.coverFileId = coverFileId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.cover_url</code>. 素材封面图地址
     */
    @Nullable
    public String getCoverUrl() {
        return this.coverUrl;
    }

    /**
     * Setter for <code>creation.creation_user_asset.cover_url</code>. 素材封面图地址
     */
    public CreationUserAsset setCoverUrl(@Nullable String coverUrl) {
        this.coverUrl = coverUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.description</code>. 素材描述
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>creation.creation_user_asset.description</code>. 素材描述
     */
    public CreationUserAsset setDescription(String description) {
        this.description = description;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.metadata</code>. 素材元数据
     */
    public JSONB getMetadata() {
        return this.metadata;
    }

    /**
     * Setter for <code>creation.creation_user_asset.metadata</code>. 素材元数据
     */
    public CreationUserAsset setMetadata(JSONB metadata) {
        this.metadata = metadata;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.keywords</code>. 素材关键词列表
     */
    public String[] getKeywords() {
        return this.keywords;
    }

    /**
     * Setter for <code>creation.creation_user_asset.keywords</code>. 素材关键词列表
     */
    public CreationUserAsset setKeywords(String[] keywords) {
        this.keywords = keywords;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.labels</code>. 素材标签列表
     */
    public JSONB getLabels() {
        return this.labels;
    }

    /**
     * Setter for <code>creation.creation_user_asset.labels</code>. 素材标签列表
     */
    public CreationUserAsset setLabels(JSONB labels) {
        this.labels = labels;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_asset.video_slices</code>.
     * 视频素材切片列表
     */
    @Nullable
    public JSONB getVideoSlices() {
        return this.videoSlices;
    }

    /**
     * Setter for <code>creation.creation_user_asset.video_slices</code>.
     * 视频素材切片列表
     */
    public CreationUserAsset setVideoSlices(@Nullable JSONB videoSlices) {
        this.videoSlices = videoSlices;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationUserAsset other = (CreationUserAsset) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.type == null) {
            if (other.type != null)
                return false;
        }
        else if (!this.type.equals(other.type))
            return false;
        if (this.file == null) {
            if (other.file != null)
                return false;
        }
        else if (!this.file.equals(other.file))
            return false;
        if (this.content == null) {
            if (other.content != null)
                return false;
        }
        else if (!this.content.equals(other.content))
            return false;
        if (this.coverFileId == null) {
            if (other.coverFileId != null)
                return false;
        }
        else if (!this.coverFileId.equals(other.coverFileId))
            return false;
        if (this.coverUrl == null) {
            if (other.coverUrl != null)
                return false;
        }
        else if (!this.coverUrl.equals(other.coverUrl))
            return false;
        if (this.description == null) {
            if (other.description != null)
                return false;
        }
        else if (!this.description.equals(other.description))
            return false;
        if (this.metadata == null) {
            if (other.metadata != null)
                return false;
        }
        else if (!this.metadata.equals(other.metadata))
            return false;
        if (this.keywords == null) {
            if (other.keywords != null)
                return false;
        }
        else if (!Arrays.deepEquals(this.keywords, other.keywords))
            return false;
        if (this.labels == null) {
            if (other.labels != null)
                return false;
        }
        else if (!this.labels.equals(other.labels))
            return false;
        if (this.videoSlices == null) {
            if (other.videoSlices != null)
                return false;
        }
        else if (!this.videoSlices.equals(other.videoSlices))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.type == null) ? 0 : this.type.hashCode());
        result = prime * result + ((this.file == null) ? 0 : this.file.hashCode());
        result = prime * result + ((this.content == null) ? 0 : this.content.hashCode());
        result = prime * result + ((this.coverFileId == null) ? 0 : this.coverFileId.hashCode());
        result = prime * result + ((this.coverUrl == null) ? 0 : this.coverUrl.hashCode());
        result = prime * result + ((this.description == null) ? 0 : this.description.hashCode());
        result = prime * result + ((this.metadata == null) ? 0 : this.metadata.hashCode());
        result = prime * result + ((this.keywords == null) ? 0 : Arrays.deepHashCode(this.keywords));
        result = prime * result + ((this.labels == null) ? 0 : this.labels.hashCode());
        result = prime * result + ((this.videoSlices == null) ? 0 : this.videoSlices.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationUserAsset (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(status);
        sb.append(", ").append(name);
        sb.append(", ").append(type);
        sb.append(", ").append(file);
        sb.append(", ").append(content);
        sb.append(", ").append(coverFileId);
        sb.append(", ").append(coverUrl);
        sb.append(", ").append(description);
        sb.append(", ").append(metadata);
        sb.append(", ").append(Arrays.deepToString(keywords));
        sb.append(", ").append(labels);
        sb.append(", ").append(videoSlices);

        sb.append(")");
        return sb.toString();
    }
}
