/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 提示词类目
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationPromptCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String bizSource;
    private String code;
    private String name;
    private String enName;
    private String status;

    public CreationPromptCategory() {}

    public CreationPromptCategory(CreationPromptCategory value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.bizSource = value.bizSource;
        this.code = value.code;
        this.name = value.name;
        this.enName = value.enName;
        this.status = value.status;
    }

    public CreationPromptCategory(
        @Nullable Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        String bizSource,
        String code,
        String name,
        String enName,
        String status
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.bizSource = bizSource;
        this.code = code;
        this.name = name;
        this.enName = enName;
        this.status = status;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_prompt_category.id</code>. 主键
     */
    public CreationPromptCategory setId(@Nullable Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_prompt_category.created_at</code>.
     * 创建时间
     */
    public CreationPromptCategory setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_prompt_category.updated_at</code>.
     * 更新时间
     */
    public CreationPromptCategory setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.biz_source</code>.
     */
    public String getBizSource() {
        return this.bizSource;
    }

    /**
     * Setter for <code>creation.creation_prompt_category.biz_source</code>.
     */
    public CreationPromptCategory setBizSource(String bizSource) {
        this.bizSource = bizSource;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.code</code>. 用户ID
     */
    public String getCode() {
        return this.code;
    }

    /**
     * Setter for <code>creation.creation_prompt_category.code</code>. 用户ID
     */
    public CreationPromptCategory setCode(String code) {
        this.code = code;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.name</code>. 用户ID
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.creation_prompt_category.name</code>. 用户ID
     */
    public CreationPromptCategory setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.en_name</code>. 用户ID
     */
    public String getEnName() {
        return this.enName;
    }

    /**
     * Setter for <code>creation.creation_prompt_category.en_name</code>. 用户ID
     */
    public CreationPromptCategory setEnName(String enName) {
        this.enName = enName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_category.status</code>. 用户ID
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_prompt_category.status</code>. 用户ID
     */
    public CreationPromptCategory setStatus(String status) {
        this.status = status;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationPromptCategory other = (CreationPromptCategory) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.bizSource == null) {
            if (other.bizSource != null)
                return false;
        }
        else if (!this.bizSource.equals(other.bizSource))
            return false;
        if (this.code == null) {
            if (other.code != null)
                return false;
        }
        else if (!this.code.equals(other.code))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.enName == null) {
            if (other.enName != null)
                return false;
        }
        else if (!this.enName.equals(other.enName))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.bizSource == null) ? 0 : this.bizSource.hashCode());
        result = prime * result + ((this.code == null) ? 0 : this.code.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.enName == null) ? 0 : this.enName.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationPromptCategory (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(bizSource);
        sb.append(", ").append(code);
        sb.append(", ").append(name);
        sb.append(", ").append(enName);
        sb.append(", ").append(status);

        sb.append(")");
        return sb.toString();
    }
}
