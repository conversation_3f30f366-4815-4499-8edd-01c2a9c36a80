/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.UserInvitationTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserInvitation;
import ai.creatly.sky.creation.infra.dal.tables.records.UserInvitationRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 用户反馈
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class UserInvitationDAO extends DAOImpl<UserInvitationRecord, CreationUserInvitation, Long> {

    /**
     * Create a new UserInvitationDAO without any configuration
     */
    public UserInvitationDAO() {
        super(UserInvitationTable.CREATION_USER_INVITATION, CreationUserInvitation.class);
    }

    /**
     * Create a new UserInvitationDAO with an attached configuration
     */
    @Autowired
    public UserInvitationDAO(Configuration configuration) {
        super(UserInvitationTable.CREATION_USER_INVITATION, CreationUserInvitation.class, configuration);
    }

    @Override
    public Long getId(CreationUserInvitation object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInvitation> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserInvitationTable.CREATION_USER_INVITATION.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationUserInvitation> fetchById(Long... values) {
        return fetch(UserInvitationTable.CREATION_USER_INVITATION.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationUserInvitation fetchOneById(Long value) {
        return fetchOne(UserInvitationTable.CREATION_USER_INVITATION.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationUserInvitation> fetchOptionalById(Long value) {
        return fetchOptional(UserInvitationTable.CREATION_USER_INVITATION.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInvitation> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserInvitationTable.CREATION_USER_INVITATION.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationUserInvitation> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(UserInvitationTable.CREATION_USER_INVITATION.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInvitation> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserInvitationTable.CREATION_USER_INVITATION.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationUserInvitation> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(UserInvitationTable.CREATION_USER_INVITATION.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>inviter_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInvitation> fetchRangeOfInviterId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserInvitationTable.CREATION_USER_INVITATION.INVITER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>inviter_id IN (values)</code>
     */
    public List<CreationUserInvitation> fetchByInviterId(Long... values) {
        return fetch(UserInvitationTable.CREATION_USER_INVITATION.INVITER_ID, values);
    }

    /**
     * Fetch records that have <code>inviter_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInvitation> fetchRangeOfInviterName(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserInvitationTable.CREATION_USER_INVITATION.INVITER_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>inviter_name IN (values)</code>
     */
    public List<CreationUserInvitation> fetchByInviterName(String... values) {
        return fetch(UserInvitationTable.CREATION_USER_INVITATION.INVITER_NAME, values);
    }

    /**
     * Fetch records that have <code>invitee_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInvitation> fetchRangeOfInviteeId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserInvitationTable.CREATION_USER_INVITATION.INVITEE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>invitee_id IN (values)</code>
     */
    public List<CreationUserInvitation> fetchByInviteeId(Long... values) {
        return fetch(UserInvitationTable.CREATION_USER_INVITATION.INVITEE_ID, values);
    }

    /**
     * Fetch records that have <code>invitee_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInvitation> fetchRangeOfInviteeName(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserInvitationTable.CREATION_USER_INVITATION.INVITEE_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>invitee_name IN (values)</code>
     */
    public List<CreationUserInvitation> fetchByInviteeName(String... values) {
        return fetch(UserInvitationTable.CREATION_USER_INVITATION.INVITEE_NAME, values);
    }

    /**
     * Fetch records that have <code>invite_code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInvitation> fetchRangeOfInviteCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserInvitationTable.CREATION_USER_INVITATION.INVITE_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>invite_code IN (values)</code>
     */
    public List<CreationUserInvitation> fetchByInviteCode(String... values) {
        return fetch(UserInvitationTable.CREATION_USER_INVITATION.INVITE_CODE, values);
    }

    /**
     * Fetch records that have <code>awarded_credits BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInvitation> fetchRangeOfAwardedCredits(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(UserInvitationTable.CREATION_USER_INVITATION.AWARDED_CREDITS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>awarded_credits IN (values)</code>
     */
    public List<CreationUserInvitation> fetchByAwardedCredits(Integer... values) {
        return fetch(UserInvitationTable.CREATION_USER_INVITATION.AWARDED_CREDITS, values);
    }
}
