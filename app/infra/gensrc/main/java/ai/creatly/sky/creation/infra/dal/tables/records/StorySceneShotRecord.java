/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.StorySceneShotTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationStorySceneShot;

import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 场景分镜
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class StorySceneShotRecord extends UpdatableRecordImpl<StorySceneShotRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_story_scene_shot.id</code>. 分镜id
     */
    public StorySceneShotRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.id</code>. 分镜id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.created_at</code>.
     * 创建时间
     */
    public StorySceneShotRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.updated_at</code>.
     * 更新时间
     */
    public StorySceneShotRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.story_id</code>. 故事id
     */
    public StorySceneShotRecord setStoryId(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.story_id</code>. 故事id
     */
    public Long getStoryId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.scene_id</code>. 场景id
     */
    public StorySceneShotRecord setSceneId(Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.scene_id</code>. 场景id
     */
    public Long getSceneId() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.shot_name</code>.
     * 舞台演出
     */
    public StorySceneShotRecord setShotName(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.shot_name</code>.
     * 舞台演出
     */
    public String getShotName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.index</code>. 分镜下标
     */
    public StorySceneShotRecord setIndex(Integer value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.index</code>. 分镜下标
     */
    public Integer getIndex() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.description</code>.
     * 分镜描述
     */
    public StorySceneShotRecord setDescription(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.description</code>.
     * 分镜描述
     */
    public String getDescription() {
        return (String) get(7);
    }

    /**
     * Setter for
     * <code>creation.creation_story_scene_shot.en_description</code>. 英文分镜描述
     */
    public StorySceneShotRecord setEnDescription(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_story_scene_shot.en_description</code>. 英文分镜描述
     */
    public String getEnDescription() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.asset</code>. 分镜资产
     */
    public StorySceneShotRecord setAsset(JSONB value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.asset</code>. 分镜资产
     */
    public JSONB getAsset() {
        return (JSONB) get(9);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.dialogues</code>. 对白
     */
    public StorySceneShotRecord setDialogues(JSONB value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.dialogues</code>. 对白
     */
    public JSONB getDialogues() {
        return (JSONB) get(10);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.sounds</code>. 音效
     */
    public StorySceneShotRecord setSounds(JSONB value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.sounds</code>. 音效
     */
    public JSONB getSounds() {
        return (JSONB) get(11);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.sounds_desc</code>.
     * 音效描述
     */
    public StorySceneShotRecord setSoundsDesc(String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.sounds_desc</code>.
     * 音效描述
     */
    public String getSoundsDesc() {
        return (String) get(12);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.creator</code>. 创建者
     */
    public StorySceneShotRecord setCreator(JSONB value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return (JSONB) get(13);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.status</code>. 状态
     */
    public StorySceneShotRecord setStatus(String value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.status</code>. 状态
     */
    public String getStatus() {
        return (String) get(14);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.movement</code>.
     */
    public StorySceneShotRecord setMovement(@Nullable String value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.movement</code>.
     */
    @Nullable
    public String getMovement() {
        return (String) get(15);
    }

    /**
     * Setter for <code>creation.creation_story_scene_shot.duration</code>. 分镜时长
     */
    public StorySceneShotRecord setDuration(Duration value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_story_scene_shot.duration</code>. 分镜时长
     */
    public Duration getDuration() {
        return (Duration) get(16);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StorySceneShotRecord
     */
    public StorySceneShotRecord() {
        super(StorySceneShotTable.CREATION_STORY_SCENE_SHOT);
    }

    /**
     * Create a detached, initialised StorySceneShotRecord
     */
    public StorySceneShotRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long storyId, Long sceneId, String shotName, Integer index, String description, String enDescription, JSONB asset, JSONB dialogues, JSONB sounds, String soundsDesc, JSONB creator, String status, @Nullable String movement, Duration duration) {
        super(StorySceneShotTable.CREATION_STORY_SCENE_SHOT);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setStoryId(storyId);
        setSceneId(sceneId);
        setShotName(shotName);
        setIndex(index);
        setDescription(description);
        setEnDescription(enDescription);
        setAsset(asset);
        setDialogues(dialogues);
        setSounds(sounds);
        setSoundsDesc(soundsDesc);
        setCreator(creator);
        setStatus(status);
        setMovement(movement);
        setDuration(duration);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised StorySceneShotRecord
     */
    public StorySceneShotRecord(CreationStorySceneShot value) {
        super(StorySceneShotTable.CREATION_STORY_SCENE_SHOT);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setStoryId(value.getStoryId());
            setSceneId(value.getSceneId());
            setShotName(value.getShotName());
            setIndex(value.getIndex());
            setDescription(value.getDescription());
            setEnDescription(value.getEnDescription());
            setAsset(value.getAsset());
            setDialogues(value.getDialogues());
            setSounds(value.getSounds());
            setSoundsDesc(value.getSoundsDesc());
            setCreator(value.getCreator());
            setStatus(value.getStatus());
            setMovement(value.getMovement());
            setDuration(value.getDuration());
            resetChangedOnNotNull();
        }
    }
}
