/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 余额使用记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationCreditHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private Long userCreditId;
    private String bizNo;
    private String bizType;
    private Integer amount;
    private String type;
    private Long balance;

    public CreationCreditHistory() {}

    public CreationCreditHistory(CreationCreditHistory value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.userCreditId = value.userCreditId;
        this.bizNo = value.bizNo;
        this.bizType = value.bizType;
        this.amount = value.amount;
        this.type = value.type;
        this.balance = value.balance;
    }

    public CreationCreditHistory(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        Long userCreditId,
        String bizNo,
        String bizType,
        Integer amount,
        String type,
        Long balance
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.userCreditId = userCreditId;
        this.bizNo = bizNo;
        this.bizType = bizType;
        this.amount = amount;
        this.type = type;
        this.balance = balance;
    }

    /**
     * Getter for <code>creation.creation_credit_history.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_credit_history.id</code>. 主键
     */
    public CreationCreditHistory setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_credit_history.created_at</code>. 创建时间
     */
    public CreationCreditHistory setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_credit_history.updated_at</code>. 更新时间
     */
    public CreationCreditHistory setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.uid</code>. 用户id
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_credit_history.uid</code>. 用户id
     */
    public CreationCreditHistory setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.user_credit_id</code>.
     * 用户余额卡表id
     */
    public Long getUserCreditId() {
        return this.userCreditId;
    }

    /**
     * Setter for <code>creation.creation_credit_history.user_credit_id</code>.
     * 用户余额卡表id
     */
    public CreationCreditHistory setUserCreditId(Long userCreditId) {
        this.userCreditId = userCreditId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.biz_no</code>.
     * 任务类存放任务id，消息存放消息id
     */
    public String getBizNo() {
        return this.bizNo;
    }

    /**
     * Setter for <code>creation.creation_credit_history.biz_no</code>.
     * 任务类存放任务id，消息存放消息id
     */
    public CreationCreditHistory setBizNo(String bizNo) {
        this.bizNo = bizNo;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.biz_type</code>.
     * 业务场景：马良/活图/充值
     */
    public String getBizType() {
        return this.bizType;
    }

    /**
     * Setter for <code>creation.creation_credit_history.biz_type</code>.
     * 业务场景：马良/活图/充值
     */
    public CreationCreditHistory setBizType(String bizType) {
        this.bizType = bizType;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.amount</code>. credit数量
     */
    public Integer getAmount() {
        return this.amount;
    }

    /**
     * Setter for <code>creation.creation_credit_history.amount</code>. credit数量
     */
    public CreationCreditHistory setAmount(Integer amount) {
        this.amount = amount;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.type</code>. 收入，支出
     */
    public String getType() {
        return this.type;
    }

    /**
     * Setter for <code>creation.creation_credit_history.type</code>. 收入，支出
     */
    public CreationCreditHistory setType(String type) {
        this.type = type;
        return this;
    }

    /**
     * Getter for <code>creation.creation_credit_history.balance</code>. 信用余额
     */
    public Long getBalance() {
        return this.balance;
    }

    /**
     * Setter for <code>creation.creation_credit_history.balance</code>. 信用余额
     */
    public CreationCreditHistory setBalance(Long balance) {
        this.balance = balance;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationCreditHistory other = (CreationCreditHistory) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.userCreditId == null) {
            if (other.userCreditId != null)
                return false;
        }
        else if (!this.userCreditId.equals(other.userCreditId))
            return false;
        if (this.bizNo == null) {
            if (other.bizNo != null)
                return false;
        }
        else if (!this.bizNo.equals(other.bizNo))
            return false;
        if (this.bizType == null) {
            if (other.bizType != null)
                return false;
        }
        else if (!this.bizType.equals(other.bizType))
            return false;
        if (this.amount == null) {
            if (other.amount != null)
                return false;
        }
        else if (!this.amount.equals(other.amount))
            return false;
        if (this.type == null) {
            if (other.type != null)
                return false;
        }
        else if (!this.type.equals(other.type))
            return false;
        if (this.balance == null) {
            if (other.balance != null)
                return false;
        }
        else if (!this.balance.equals(other.balance))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.userCreditId == null) ? 0 : this.userCreditId.hashCode());
        result = prime * result + ((this.bizNo == null) ? 0 : this.bizNo.hashCode());
        result = prime * result + ((this.bizType == null) ? 0 : this.bizType.hashCode());
        result = prime * result + ((this.amount == null) ? 0 : this.amount.hashCode());
        result = prime * result + ((this.type == null) ? 0 : this.type.hashCode());
        result = prime * result + ((this.balance == null) ? 0 : this.balance.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationCreditHistory (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(userCreditId);
        sb.append(", ").append(bizNo);
        sb.append(", ").append(bizType);
        sb.append(", ").append(amount);
        sb.append(", ").append(type);
        sb.append(", ").append(balance);

        sb.append(")");
        return sb.toString();
    }
}
