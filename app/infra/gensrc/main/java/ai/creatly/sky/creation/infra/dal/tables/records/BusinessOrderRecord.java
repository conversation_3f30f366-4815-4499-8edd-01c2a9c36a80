/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.BusinessOrderTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationBusinessOrder;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 课程
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class BusinessOrderRecord extends UpdatableRecordImpl<BusinessOrderRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_business_order.id</code>. 商单id
     */
    public BusinessOrderRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.id</code>. 商单id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_business_order.created_at</code>. 创建时间
     */
    public BusinessOrderRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_business_order.updated_at</code>. 更新时间
     */
    public BusinessOrderRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_business_order.cover_url</code>. 商单封面
     */
    public BusinessOrderRecord setCoverUrl(@Nullable String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.cover_url</code>. 商单封面
     */
    @Nullable
    public String getCoverUrl() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_business_order.video_url</code>. 视频链接
     */
    public BusinessOrderRecord setVideoUrl(@Nullable String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.video_url</code>. 视频链接
     */
    @Nullable
    public String getVideoUrl() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_business_order.title</code>. 商单名称
     */
    public BusinessOrderRecord setTitle(@Nullable String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.title</code>. 商单名称
     */
    @Nullable
    public String getTitle() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_business_order.order_intro</code>.
     * 商单介绍
     */
    public BusinessOrderRecord setOrderIntro(@Nullable String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.order_intro</code>.
     * 商单介绍
     */
    @Nullable
    public String getOrderIntro() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_business_order.order_status</code>.
     * 商单状态
     */
    public BusinessOrderRecord setOrderStatus(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.order_status</code>.
     * 商单状态
     */
    @Nullable
    public String getOrderStatus() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_business_order.order_type</code>. 商单类型
     */
    public BusinessOrderRecord setOrderType(@Nullable String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.order_type</code>. 商单类型
     */
    @Nullable
    public String getOrderType() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_business_order.subtitle</code>. 副标题
     */
    public BusinessOrderRecord setSubtitle(@Nullable String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.subtitle</code>. 副标题
     */
    @Nullable
    public String getSubtitle() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_business_order.order_price</code>.
     * 商单价格
     */
    public BusinessOrderRecord setOrderPrice(@Nullable Long value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.order_price</code>.
     * 商单价格
     */
    @Nullable
    public Long getOrderPrice() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>creation.creation_business_order.begin_time</code>. 开始时间
     */
    public BusinessOrderRecord setBeginTime(@Nullable ZonedDateTime value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.begin_time</code>. 开始时间
     */
    @Nullable
    public ZonedDateTime getBeginTime() {
        return (ZonedDateTime) get(11);
    }

    /**
     * Setter for <code>creation.creation_business_order.end_time</code>. 结束时间
     */
    public BusinessOrderRecord setEndTime(@Nullable ZonedDateTime value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.end_time</code>. 结束时间
     */
    @Nullable
    public ZonedDateTime getEndTime() {
        return (ZonedDateTime) get(12);
    }

    /**
     * Setter for <code>creation.creation_business_order.priority</code>. 优先级
     */
    public BusinessOrderRecord setPriority(@Nullable Integer value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.priority</code>. 优先级
     */
    @Nullable
    public Integer getPriority() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>creation.creation_business_order.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public BusinessOrderRecord setOwnerId(@Nullable Long value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    @Nullable
    public Long getOwnerId() {
        return (Long) get(14);
    }

    /**
     * Setter for <code>creation.creation_business_order.owner_name</code>.
     * 任务所有者名称
     */
    public BusinessOrderRecord setOwnerName(@Nullable String value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.owner_name</code>.
     * 任务所有者名称
     */
    @Nullable
    public String getOwnerName() {
        return (String) get(15);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached BusinessOrderRecord
     */
    public BusinessOrderRecord() {
        super(BusinessOrderTable.CREATION_BUSINESS_ORDER);
    }

    /**
     * Create a detached, initialised BusinessOrderRecord
     */
    public BusinessOrderRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable String coverUrl, @Nullable String videoUrl, @Nullable String title, @Nullable String orderIntro, @Nullable String orderStatus, @Nullable String orderType, @Nullable String subtitle, @Nullable Long orderPrice, @Nullable ZonedDateTime beginTime, @Nullable ZonedDateTime endTime, @Nullable Integer priority, @Nullable Long ownerId, @Nullable String ownerName) {
        super(BusinessOrderTable.CREATION_BUSINESS_ORDER);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCoverUrl(coverUrl);
        setVideoUrl(videoUrl);
        setTitle(title);
        setOrderIntro(orderIntro);
        setOrderStatus(orderStatus);
        setOrderType(orderType);
        setSubtitle(subtitle);
        setOrderPrice(orderPrice);
        setBeginTime(beginTime);
        setEndTime(endTime);
        setPriority(priority);
        setOwnerId(ownerId);
        setOwnerName(ownerName);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised BusinessOrderRecord
     */
    public BusinessOrderRecord(CreationBusinessOrder value) {
        super(BusinessOrderTable.CREATION_BUSINESS_ORDER);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCoverUrl(value.getCoverUrl());
            setVideoUrl(value.getVideoUrl());
            setTitle(value.getTitle());
            setOrderIntro(value.getOrderIntro());
            setOrderStatus(value.getOrderStatus());
            setOrderType(value.getOrderType());
            setSubtitle(value.getSubtitle());
            setOrderPrice(value.getOrderPrice());
            setBeginTime(value.getBeginTime());
            setEndTime(value.getEndTime());
            setPriority(value.getPriority());
            setOwnerId(value.getOwnerId());
            setOwnerName(value.getOwnerName());
            resetChangedOnNotNull();
        }
    }
}
