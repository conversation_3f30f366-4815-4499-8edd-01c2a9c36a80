/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseLearnRecordRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 课程学习记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseLearnRecordTable extends TableImpl<CourseLearnRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.course_learn_record</code>
     */
    public static final CourseLearnRecordTable COURSE_LEARN_RECORD = new CourseLearnRecordTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseLearnRecordRecord> getRecordType() {
        return CourseLearnRecordRecord.class;
    }

    /**
     * The column <code>creation.course_learn_record.id</code>. 课程学习记录id
     */
    public final TableField<CourseLearnRecordRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "课程学习记录id");

    /**
     * The column <code>creation.course_learn_record.created_at</code>. 创建时间
     */
    public final TableField<CourseLearnRecordRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.course_learn_record.updated_at</code>. 更新时间
     */
    public final TableField<CourseLearnRecordRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.course_learn_record.course_id</code>. 课程ID
     */
    public final TableField<CourseLearnRecordRecord, Long> COURSE_ID = createField(DSL.name("course_id"), SQLDataType.BIGINT, this, "课程ID");

    /**
     * The column <code>creation.course_learn_record.content_id</code>. 课件ID
     */
    public final TableField<CourseLearnRecordRecord, Long> CONTENT_ID = createField(DSL.name("content_id"), SQLDataType.BIGINT, this, "课件ID");

    /**
     * The column <code>creation.course_learn_record.status</code>. 学习状态
     */
    public final TableField<CourseLearnRecordRecord, Integer> STATUS = createField(DSL.name("status"), SQLDataType.INTEGER, this, "学习状态");

    /**
     * The column <code>creation.course_learn_record.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public final TableField<CourseLearnRecordRecord, Long> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.BIGINT, this, "任务所有者ID（1表示系统）");

    /**
     * The column <code>creation.course_learn_record.owner_name</code>. 任务所有者名称
     */
    public final TableField<CourseLearnRecordRecord, String> OWNER_NAME = createField(DSL.name("owner_name"), SQLDataType.VARCHAR(128), this, "任务所有者名称");

    /**
     * The column <code>creation.course_learn_record.init_seconds</code>. 初始秒数
     */
    public final TableField<CourseLearnRecordRecord, Integer> INIT_SECONDS = createField(DSL.name("init_seconds"), SQLDataType.INTEGER, this, "初始秒数");

    /**
     * The column <code>creation.course_learn_record.org_code</code>. 组织代码
     */
    public final TableField<CourseLearnRecordRecord, String> ORG_CODE = createField(DSL.name("org_code"), SQLDataType.VARCHAR(64), this, "组织代码");

    private CourseLearnRecordTable(Name alias, Table<CourseLearnRecordRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private CourseLearnRecordTable(Name alias, Table<CourseLearnRecordRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("课程学习记录"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.course_learn_record</code> table
     * reference
     */
    public CourseLearnRecordTable(String alias) {
        this(DSL.name(alias), COURSE_LEARN_RECORD);
    }

    /**
     * Create an aliased <code>creation.course_learn_record</code> table
     * reference
     */
    public CourseLearnRecordTable(Name alias) {
        this(alias, COURSE_LEARN_RECORD);
    }

    /**
     * Create a <code>creation.course_learn_record</code> table reference
     */
    public CourseLearnRecordTable() {
        this(DSL.name("course_learn_record"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<CourseLearnRecordRecord> getPrimaryKey() {
        return Keys.COURSE_LEARN_RECORD_PKEY;
    }

    @Override
    public CourseLearnRecordTable as(String alias) {
        return new CourseLearnRecordTable(DSL.name(alias), this);
    }

    @Override
    public CourseLearnRecordTable as(Name alias) {
        return new CourseLearnRecordTable(alias, this);
    }

    @Override
    public CourseLearnRecordTable as(Table<?> alias) {
        return new CourseLearnRecordTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseLearnRecordTable rename(String name) {
        return new CourseLearnRecordTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseLearnRecordTable rename(Name name) {
        return new CourseLearnRecordTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseLearnRecordTable rename(Table<?> name) {
        return new CourseLearnRecordTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseLearnRecordTable where(Condition condition) {
        return new CourseLearnRecordTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseLearnRecordTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseLearnRecordTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseLearnRecordTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CourseLearnRecordTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CourseLearnRecordTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CourseLearnRecordTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public CourseLearnRecordTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseLearnRecordTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public CourseLearnRecordTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
