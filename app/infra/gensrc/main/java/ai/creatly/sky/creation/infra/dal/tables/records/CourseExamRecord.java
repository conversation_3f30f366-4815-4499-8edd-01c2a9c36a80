/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.CourseExamTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseExam;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 考试记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseExamRecord extends UpdatableRecordImpl<CourseExamRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.course_exam.id</code>. 考试ID
     */
    public CourseExamRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam.id</code>. 考试ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.course_exam.created_at</code>. 创建时间
     */
    public CourseExamRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.course_exam.updated_at</code>. 更新时间
     */
    public CourseExamRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.course_exam.cert_id</code>. 证书ID
     */
    public CourseExamRecord setCertId(@Nullable Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam.cert_id</code>. 证书ID
     */
    @Nullable
    public Long getCertId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.course_exam.uid</code>. 用户ID
     */
    public CourseExamRecord setUid(@Nullable Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam.uid</code>. 用户ID
     */
    @Nullable
    public Long getUid() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.course_exam.org_code</code>. 组织机构
     */
    public CourseExamRecord setOrgCode(@Nullable String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam.org_code</code>. 组织机构
     */
    @Nullable
    public String getOrgCode() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.course_exam.score</code>. 考试得分
     */
    public CourseExamRecord setScore(@Nullable Long value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam.score</code>. 考试得分
     */
    @Nullable
    public Long getScore() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>creation.course_exam.status</code>.
     * 考试状态，INIT，SUBMIT，FINISH
     */
    public CourseExamRecord setStatus(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam.status</code>.
     * 考试状态，INIT，SUBMIT，FINISH
     */
    @Nullable
    public String getStatus() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.course_exam.user_score</code>. 用户得分
     */
    public CourseExamRecord setUserScore(@Nullable Long value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam.user_score</code>. 用户得分
     */
    @Nullable
    public Long getUserScore() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>creation.course_exam.pass_score</code>. 通过分数
     */
    public CourseExamRecord setPassScore(@Nullable Long value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_exam.pass_score</code>. 通过分数
     */
    @Nullable
    public Long getPassScore() {
        return (Long) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseExamRecord
     */
    public CourseExamRecord() {
        super(CourseExamTable.COURSE_EXAM);
    }

    /**
     * Create a detached, initialised CourseExamRecord
     */
    public CourseExamRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable Long certId, @Nullable Long uid, @Nullable String orgCode, @Nullable Long score, @Nullable String status, @Nullable Long userScore, @Nullable Long passScore) {
        super(CourseExamTable.COURSE_EXAM);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCertId(certId);
        setUid(uid);
        setOrgCode(orgCode);
        setScore(score);
        setStatus(status);
        setUserScore(userScore);
        setPassScore(passScore);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised CourseExamRecord
     */
    public CourseExamRecord(CourseExam value) {
        super(CourseExamTable.COURSE_EXAM);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCertId(value.getCertId());
            setUid(value.getUid());
            setOrgCode(value.getOrgCode());
            setScore(value.getScore());
            setStatus(value.getStatus());
            setUserScore(value.getUserScore());
            setPassScore(value.getPassScore());
            resetChangedOnNotNull();
        }
    }
}
