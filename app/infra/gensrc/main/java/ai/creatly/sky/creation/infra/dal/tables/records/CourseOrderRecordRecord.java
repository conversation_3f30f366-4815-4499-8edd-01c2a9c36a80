/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.CourseOrderRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseOrderRecord;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 课程购买记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseOrderRecordRecord extends UpdatableRecordImpl<CourseOrderRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.course_order_record.id</code>. 课程购买记录id
     */
    public CourseOrderRecordRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.id</code>. 课程购买记录id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.course_order_record.created_at</code>. 创建时间
     */
    public CourseOrderRecordRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.course_order_record.updated_at</code>. 更新时间
     */
    public CourseOrderRecordRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.course_order_record.course_id</code>. 课程ID
     */
    public CourseOrderRecordRecord setCourseId(@Nullable Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.course_id</code>. 课程ID
     */
    @Nullable
    public Long getCourseId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.course_order_record.order_price</code>. 支付价格
     */
    public CourseOrderRecordRecord setOrderPrice(@Nullable Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.order_price</code>. 支付价格
     */
    @Nullable
    public Long getOrderPrice() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.course_order_record.order_status</code>. 支付状态
     */
    public CourseOrderRecordRecord setOrderStatus(@Nullable String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.order_status</code>. 支付状态
     */
    @Nullable
    public String getOrderStatus() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.course_order_record.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public CourseOrderRecordRecord setOwnerId(@Nullable Long value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    @Nullable
    public Long getOwnerId() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>creation.course_order_record.owner_name</code>. 任务所有者名称
     */
    public CourseOrderRecordRecord setOwnerName(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.owner_name</code>. 任务所有者名称
     */
    @Nullable
    public String getOwnerName() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.course_order_record.paid_time</code>. 支付时间
     */
    public CourseOrderRecordRecord setPaidTime(@Nullable ZonedDateTime value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.paid_time</code>. 支付时间
     */
    @Nullable
    public ZonedDateTime getPaidTime() {
        return (ZonedDateTime) get(8);
    }

    /**
     * Setter for <code>creation.course_order_record.paid_channel</code>. 支付渠道
     */
    public CourseOrderRecordRecord setPaidChannel(@Nullable String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.paid_channel</code>. 支付渠道
     */
    @Nullable
    public String getPaidChannel() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.course_order_record.order_id</code>. 订单id
     */
    public CourseOrderRecordRecord setOrderId(@Nullable Long value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.order_id</code>. 订单id
     */
    @Nullable
    public Long getOrderId() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>creation.course_order_record.code_url</code>. 支付二维码
     */
    public CourseOrderRecordRecord setCodeUrl(@Nullable String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.code_url</code>. 支付二维码
     */
    @Nullable
    public String getCodeUrl() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.course_order_record.expire_at</code>. 超时时间
     */
    public CourseOrderRecordRecord setExpireAt(@Nullable ZonedDateTime value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.expire_at</code>. 超时时间
     */
    @Nullable
    public ZonedDateTime getExpireAt() {
        return (ZonedDateTime) get(12);
    }

    /**
     * Setter for <code>creation.course_order_record.org_code</code>. 组织代码
     */
    public CourseOrderRecordRecord setOrgCode(@Nullable String value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_order_record.org_code</code>. 组织代码
     */
    @Nullable
    public String getOrgCode() {
        return (String) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseOrderRecordRecord
     */
    public CourseOrderRecordRecord() {
        super(CourseOrderRecordTable.COURSE_ORDER_RECORD);
    }

    /**
     * Create a detached, initialised CourseOrderRecordRecord
     */
    public CourseOrderRecordRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable Long courseId, @Nullable Long orderPrice, @Nullable String orderStatus, @Nullable Long ownerId, @Nullable String ownerName, @Nullable ZonedDateTime paidTime, @Nullable String paidChannel, @Nullable Long orderId, @Nullable String codeUrl, @Nullable ZonedDateTime expireAt, @Nullable String orgCode) {
        super(CourseOrderRecordTable.COURSE_ORDER_RECORD);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCourseId(courseId);
        setOrderPrice(orderPrice);
        setOrderStatus(orderStatus);
        setOwnerId(ownerId);
        setOwnerName(ownerName);
        setPaidTime(paidTime);
        setPaidChannel(paidChannel);
        setOrderId(orderId);
        setCodeUrl(codeUrl);
        setExpireAt(expireAt);
        setOrgCode(orgCode);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised CourseOrderRecordRecord
     */
    public CourseOrderRecordRecord(CourseOrderRecord value) {
        super(CourseOrderRecordTable.COURSE_ORDER_RECORD);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCourseId(value.getCourseId());
            setOrderPrice(value.getOrderPrice());
            setOrderStatus(value.getOrderStatus());
            setOwnerId(value.getOwnerId());
            setOwnerName(value.getOwnerName());
            setPaidTime(value.getPaidTime());
            setPaidChannel(value.getPaidChannel());
            setOrderId(value.getOrderId());
            setCodeUrl(value.getCodeUrl());
            setExpireAt(value.getExpireAt());
            setOrgCode(value.getOrgCode());
            resetChangedOnNotNull();
        }
    }
}
