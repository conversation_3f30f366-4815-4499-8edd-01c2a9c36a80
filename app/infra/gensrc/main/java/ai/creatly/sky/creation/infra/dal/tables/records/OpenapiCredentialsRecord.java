/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.OpenapiCredentialsTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationOpenapiCredentials;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * openapi密钥表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class OpenapiCredentialsRecord extends UpdatableRecordImpl<OpenapiCredentialsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_openapi_credentials.id</code>. 主键
     */
    public OpenapiCredentialsRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.created_at</code>.
     * 创建时间
     */
    public OpenapiCredentialsRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.updated_at</code>.
     * 更新时间
     */
    public OpenapiCredentialsRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.uid</code>. 用户id
     */
    public OpenapiCredentialsRecord setUid(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.uid</code>. 用户id
     */
    public String getUid() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.name</code>. 凭证
     */
    public OpenapiCredentialsRecord setName(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.name</code>. 凭证
     */
    public String getName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.app_key</code>.
     * appKey
     */
    public OpenapiCredentialsRecord setAppKey(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.app_key</code>.
     * appKey
     */
    public String getAppKey() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.app_secret</code>.
     * appSecret
     */
    public OpenapiCredentialsRecord setAppSecret(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.app_secret</code>.
     * appSecret
     */
    public String getAppSecret() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.status</code>. 状态
     * VALID/INVALID
     */
    public OpenapiCredentialsRecord setStatus(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.status</code>. 状态
     * VALID/INVALID
     */
    public String getStatus() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.expire_at</code>.
     * 到期时间
     */
    public OpenapiCredentialsRecord setExpireAt(@Nullable ZonedDateTime value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.expire_at</code>.
     * 到期时间
     */
    @Nullable
    public ZonedDateTime getExpireAt() {
        return (ZonedDateTime) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached OpenapiCredentialsRecord
     */
    public OpenapiCredentialsRecord() {
        super(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS);
    }

    /**
     * Create a detached, initialised OpenapiCredentialsRecord
     */
    public OpenapiCredentialsRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, String uid, String name, String appKey, String appSecret, String status, @Nullable ZonedDateTime expireAt) {
        super(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setName(name);
        setAppKey(appKey);
        setAppSecret(appSecret);
        setStatus(status);
        setExpireAt(expireAt);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised OpenapiCredentialsRecord
     */
    public OpenapiCredentialsRecord(CreationOpenapiCredentials value) {
        super(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setName(value.getName());
            setAppKey(value.getAppKey());
            setAppSecret(value.getAppSecret());
            setStatus(value.getStatus());
            setExpireAt(value.getExpireAt());
            resetChangedOnNotNull();
        }
    }
}
