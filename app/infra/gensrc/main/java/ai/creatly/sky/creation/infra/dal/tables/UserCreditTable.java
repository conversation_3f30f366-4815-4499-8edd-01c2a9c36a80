/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.UserCreditRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 用户荣誉表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserCreditTable extends TableImpl<UserCreditRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.user_credit</code>
     */
    public static final UserCreditTable USER_CREDIT = new UserCreditTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserCreditRecord> getRecordType() {
        return UserCreditRecord.class;
    }

    /**
     * The column <code>creation.user_credit.id</code>. 主键（自增长）
     */
    public final TableField<UserCreditRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键（自增长）");

    /**
     * The column <code>creation.user_credit.created_at</code>. 创建时间
     */
    public final TableField<UserCreditRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.user_credit.updated_at</code>.
     */
    public final TableField<UserCreditRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.user_credit.uid</code>. 用户ID
     */
    public final TableField<UserCreditRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户ID");

    /**
     * The column <code>creation.user_credit.balance</code>. 余额
     */
    public final TableField<UserCreditRecord, Long> BALANCE = createField(DSL.name("balance"), SQLDataType.BIGINT.nullable(false), this, "余额");

    /**
     * The column <code>creation.user_credit.credit_type</code>.
     */
    public final TableField<UserCreditRecord, String> CREDIT_TYPE = createField(DSL.name("credit_type"), SQLDataType.VARCHAR(64).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>creation.user_credit.expire_at</code>. 到期时间
     */
    public final TableField<UserCreditRecord, ZonedDateTime> EXPIRE_AT = createField(DSL.name("expire_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3), this, "到期时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.user_credit.effect_at</code>. 生效时间
     */
    public final TableField<UserCreditRecord, ZonedDateTime> EFFECT_AT = createField(DSL.name("effect_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3), this, "生效时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.user_credit.org_code</code>. 组织代码
     */
    public final TableField<UserCreditRecord, String> ORG_CODE = createField(DSL.name("org_code"), SQLDataType.VARCHAR(64), this, "组织代码");

    private UserCreditTable(Name alias, Table<UserCreditRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private UserCreditTable(Name alias, Table<UserCreditRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("用户荣誉表"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.user_credit</code> table reference
     */
    public UserCreditTable(String alias) {
        this(DSL.name(alias), USER_CREDIT);
    }

    /**
     * Create an aliased <code>creation.user_credit</code> table reference
     */
    public UserCreditTable(Name alias) {
        this(alias, USER_CREDIT);
    }

    /**
     * Create a <code>creation.user_credit</code> table reference
     */
    public UserCreditTable() {
        this(DSL.name("user_credit"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<UserCreditRecord> getPrimaryKey() {
        return Keys.USER_CREDIT_PKEY;
    }

    @Override
    public UserCreditTable as(String alias) {
        return new UserCreditTable(DSL.name(alias), this);
    }

    @Override
    public UserCreditTable as(Name alias) {
        return new UserCreditTable(alias, this);
    }

    @Override
    public UserCreditTable as(Table<?> alias) {
        return new UserCreditTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserCreditTable rename(String name) {
        return new UserCreditTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserCreditTable rename(Name name) {
        return new UserCreditTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserCreditTable rename(Table<?> name) {
        return new UserCreditTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserCreditTable where(Condition condition) {
        return new UserCreditTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserCreditTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserCreditTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserCreditTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserCreditTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserCreditTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserCreditTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserCreditTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserCreditTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserCreditTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
