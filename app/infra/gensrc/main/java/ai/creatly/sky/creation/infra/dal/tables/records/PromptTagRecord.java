/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.PromptTagTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationPromptTag;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 提示词标签
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class PromptTagRecord extends UpdatableRecordImpl<PromptTagRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_prompt_tag.id</code>. 主键
     */
    public PromptTagRecord setId(@Nullable Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.created_at</code>. 创建时间
     */
    public PromptTagRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.updated_at</code>. 更新时间
     */
    public PromptTagRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.code</code>. 标签码
     */
    public PromptTagRecord setCode(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.code</code>. 标签码
     */
    public String getCode() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.color</code>. 标签颜色
     */
    public PromptTagRecord setColor(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.color</code>. 标签颜色
     */
    public String getColor() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.name</code>. 标签英文名
     */
    public PromptTagRecord setName(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.name</code>. 标签英文名
     */
    public String getName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.en_name</code>.
     */
    public PromptTagRecord setEnName(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.en_name</code>.
     */
    public String getEnName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_prompt_tag.status</code>. 状态
     */
    public PromptTagRecord setStatus(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_tag.status</code>. 状态
     */
    public String getStatus() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached PromptTagRecord
     */
    public PromptTagRecord() {
        super(PromptTagTable.CREATION_PROMPT_TAG);
    }

    /**
     * Create a detached, initialised PromptTagRecord
     */
    public PromptTagRecord(@Nullable Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, String code, String color, String name, String enName, String status) {
        super(PromptTagTable.CREATION_PROMPT_TAG);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCode(code);
        setColor(color);
        setName(name);
        setEnName(enName);
        setStatus(status);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised PromptTagRecord
     */
    public PromptTagRecord(CreationPromptTag value) {
        super(PromptTagTable.CREATION_PROMPT_TAG);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCode(value.getCode());
            setColor(value.getColor());
            setName(value.getName());
            setEnName(value.getEnName());
            setStatus(value.getStatus());
            resetChangedOnNotNull();
        }
    }
}
