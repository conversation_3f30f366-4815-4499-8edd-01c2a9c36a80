/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.QrcodeScanRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.QrcodeScanRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.QrcodeScanRecordRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 权益中心-用户扫码记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class QrcodeScanRecordDAO extends DAOImpl<QrcodeScanRecordRecord, QrcodeScanRecord, Long> {

    /**
     * Create a new QrcodeScanRecordDAO without any configuration
     */
    public QrcodeScanRecordDAO() {
        super(QrcodeScanRecordTable.QRCODE_SCAN_RECORD, QrcodeScanRecord.class);
    }

    /**
     * Create a new QrcodeScanRecordDAO with an attached configuration
     */
    @Autowired
    public QrcodeScanRecordDAO(Configuration configuration) {
        super(QrcodeScanRecordTable.QRCODE_SCAN_RECORD, QrcodeScanRecord.class, configuration);
    }

    @Override
    public Long getId(QrcodeScanRecord object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeScanRecord> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<QrcodeScanRecord> fetchById(Long... values) {
        return fetch(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public QrcodeScanRecord fetchOneById(Long value) {
        return fetchOne(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<QrcodeScanRecord> fetchOptionalById(Long value) {
        return fetchOptional(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeScanRecord> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<QrcodeScanRecord> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeScanRecord> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<QrcodeScanRecord> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>issuing_party BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeScanRecord> fetchRangeOfIssuingParty(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.ISSUING_PARTY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>issuing_party IN (values)</code>
     */
    public List<QrcodeScanRecord> fetchByIssuingParty(Long... values) {
        return fetch(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.ISSUING_PARTY, values);
    }

    /**
     * Fetch records that have <code>scanning_party BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeScanRecord> fetchRangeOfScanningParty(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.SCANNING_PARTY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>scanning_party IN (values)</code>
     */
    public List<QrcodeScanRecord> fetchByScanningParty(Long... values) {
        return fetch(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.SCANNING_PARTY, values);
    }

    /**
     * Fetch records that have <code>code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeScanRecord> fetchRangeOfCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>code IN (values)</code>
     */
    public List<QrcodeScanRecord> fetchByCode(String... values) {
        return fetch(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.CODE, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeScanRecord> fetchRangeOfType(String lowerInclusive, String upperInclusive) {
        return fetchRange(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<QrcodeScanRecord> fetchByType(String... values) {
        return fetch(QrcodeScanRecordTable.QRCODE_SCAN_RECORD.TYPE, values);
    }
}
