/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.CourseTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.Course;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 课程
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseRecord extends UpdatableRecordImpl<CourseRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.course.id</code>. 课程id
     */
    public CourseRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.id</code>. 课程id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.course.created_at</code>. 创建时间
     */
    public CourseRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.course.updated_at</code>. 更新时间
     */
    public CourseRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.course.category</code>. 课程类目
     */
    public CourseRecord setCategory(@Nullable String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.category</code>. 课程类目
     */
    @Nullable
    public String getCategory() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.course.cover_url</code>. 课程封面
     */
    public CourseRecord setCoverUrl(@Nullable String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.cover_url</code>. 课程封面
     */
    @Nullable
    public String getCoverUrl() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.course.course_intro</code>. 课程介绍
     */
    public CourseRecord setCourseIntro(@Nullable String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.course_intro</code>. 课程介绍
     */
    @Nullable
    public String getCourseIntro() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.course.course_status</code>. 状态
     */
    public CourseRecord setCourseStatus(@Nullable String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.course_status</code>. 状态
     */
    @Nullable
    public String getCourseStatus() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.course.course_type</code>. 课程类型
     */
    public CourseRecord setCourseType(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.course_type</code>. 课程类型
     */
    @Nullable
    public String getCourseType() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.course.course_title</code>. 课程名称
     */
    public CourseRecord setCourseTitle(@Nullable String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.course_title</code>. 课程名称
     */
    @Nullable
    public String getCourseTitle() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.course.video_url</code>. 视频链接
     */
    public CourseRecord setVideoUrl(@Nullable String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.video_url</code>. 视频链接
     */
    @Nullable
    public String getVideoUrl() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.course.course_subtitle</code>. 课程副标题
     */
    public CourseRecord setCourseSubtitle(@Nullable String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.course_subtitle</code>. 课程副标题
     */
    @Nullable
    public String getCourseSubtitle() {
        return (String) get(10);
    }

    /**
     * Setter for <code>creation.course.real_price</code>. 课程价格
     */
    public CourseRecord setRealPrice(@Nullable Long value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.real_price</code>. 课程价格
     */
    @Nullable
    public Long getRealPrice() {
        return (Long) get(11);
    }

    /**
     * Setter for <code>creation.course.line_price</code>. 划线价格
     */
    public CourseRecord setLinePrice(@Nullable Long value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.line_price</code>. 划线价格
     */
    @Nullable
    public Long getLinePrice() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>creation.course.duration</code>. 总课时
     */
    public CourseRecord setDuration(@Nullable String value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.course.duration</code>. 总课时
     */
    @Nullable
    public String getDuration() {
        return (String) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseRecord
     */
    public CourseRecord() {
        super(CourseTable.COURSE);
    }

    /**
     * Create a detached, initialised CourseRecord
     */
    public CourseRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable String category, @Nullable String coverUrl, @Nullable String courseIntro, @Nullable String courseStatus, @Nullable String courseType, @Nullable String courseTitle, @Nullable String videoUrl, @Nullable String courseSubtitle, @Nullable Long realPrice, @Nullable Long linePrice, @Nullable String duration) {
        super(CourseTable.COURSE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCategory(category);
        setCoverUrl(coverUrl);
        setCourseIntro(courseIntro);
        setCourseStatus(courseStatus);
        setCourseType(courseType);
        setCourseTitle(courseTitle);
        setVideoUrl(videoUrl);
        setCourseSubtitle(courseSubtitle);
        setRealPrice(realPrice);
        setLinePrice(linePrice);
        setDuration(duration);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised CourseRecord
     */
    public CourseRecord(Course value) {
        super(CourseTable.COURSE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCategory(value.getCategory());
            setCoverUrl(value.getCoverUrl());
            setCourseIntro(value.getCourseIntro());
            setCourseStatus(value.getCourseStatus());
            setCourseType(value.getCourseType());
            setCourseTitle(value.getCourseTitle());
            setVideoUrl(value.getVideoUrl());
            setCourseSubtitle(value.getCourseSubtitle());
            setRealPrice(value.getRealPrice());
            setLinePrice(value.getLinePrice());
            setDuration(value.getDuration());
            resetChangedOnNotNull();
        }
    }
}
