/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.AiTalkActorTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiTalkActor;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * AI Talk 演讲者
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiTalkActorRecord extends UpdatableRecordImpl<AiTalkActorRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_ai_talk_actor.id</code>. 主键
     */
    public AiTalkActorRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.created_at</code>. 创建时间
     */
    public AiTalkActorRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.updated_at</code>. 更新时间
     */
    public AiTalkActorRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.uid</code>.
     * 所属用户ID（1表示系统）
     */
    public AiTalkActorRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.uid</code>.
     * 所属用户ID（1表示系统）
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.name</code>. 名称
     */
    public AiTalkActorRecord setName(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.name</code>. 名称
     */
    public String getName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.image_id</code>. 图片文件ID
     */
    public AiTalkActorRecord setImageId(Long value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.image_id</code>. 图片文件ID
     */
    public Long getImageId() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.image_url</code>.
     * 图片路径（冗余OSS协议的文件地址）
     */
    public AiTalkActorRecord setImageUrl(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.image_url</code>.
     * 图片路径（冗余OSS协议的文件地址）
     */
    public String getImageUrl() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.creator</code>. 创建者
     */
    public AiTalkActorRecord setCreator(JSONB value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return (JSONB) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AiTalkActorRecord
     */
    public AiTalkActorRecord() {
        super(AiTalkActorTable.CREATION_AI_TALK_ACTOR);
    }

    /**
     * Create a detached, initialised AiTalkActorRecord
     */
    public AiTalkActorRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String name, Long imageId, String imageUrl, JSONB creator) {
        super(AiTalkActorTable.CREATION_AI_TALK_ACTOR);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setName(name);
        setImageId(imageId);
        setImageUrl(imageUrl);
        setCreator(creator);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised AiTalkActorRecord
     */
    public AiTalkActorRecord(CreationAiTalkActor value) {
        super(AiTalkActorTable.CREATION_AI_TALK_ACTOR);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setName(value.getName());
            setImageId(value.getImageId());
            setImageUrl(value.getImageUrl());
            setCreator(value.getCreator());
            resetChangedOnNotNull();
        }
    }
}
