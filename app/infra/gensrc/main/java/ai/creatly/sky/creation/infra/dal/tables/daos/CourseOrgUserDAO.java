/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.CourseOrgUserTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseOrgUser;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseOrgUserRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 机构学员课程开课记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class CourseOrgUserDAO extends DAOImpl<CourseOrgUserRecord, CourseOrgUser, Long> {

    /**
     * Create a new CourseOrgUserDAO without any configuration
     */
    public CourseOrgUserDAO() {
        super(CourseOrgUserTable.COURSE_ORG_USER, CourseOrgUser.class);
    }

    /**
     * Create a new CourseOrgUserDAO with an attached configuration
     */
    @Autowired
    public CourseOrgUserDAO(Configuration configuration) {
        super(CourseOrgUserTable.COURSE_ORG_USER, CourseOrgUser.class, configuration);
    }

    @Override
    public Long getId(CourseOrgUser object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseOrgUser> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseOrgUserTable.COURSE_ORG_USER.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CourseOrgUser> fetchById(Long... values) {
        return fetch(CourseOrgUserTable.COURSE_ORG_USER.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CourseOrgUser fetchOneById(Long value) {
        return fetchOne(CourseOrgUserTable.COURSE_ORG_USER.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CourseOrgUser> fetchOptionalById(Long value) {
        return fetchOptional(CourseOrgUserTable.COURSE_ORG_USER.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseOrgUser> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(CourseOrgUserTable.COURSE_ORG_USER.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CourseOrgUser> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(CourseOrgUserTable.COURSE_ORG_USER.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseOrgUser> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(CourseOrgUserTable.COURSE_ORG_USER.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CourseOrgUser> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(CourseOrgUserTable.COURSE_ORG_USER.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>course_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseOrgUser> fetchRangeOfCourseId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseOrgUserTable.COURSE_ORG_USER.COURSE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>course_id IN (values)</code>
     */
    public List<CourseOrgUser> fetchByCourseId(Long... values) {
        return fetch(CourseOrgUserTable.COURSE_ORG_USER.COURSE_ID, values);
    }

    /**
     * Fetch records that have <code>begin_time BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseOrgUser> fetchRangeOfBeginTime(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(CourseOrgUserTable.COURSE_ORG_USER.BEGIN_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>begin_time IN (values)</code>
     */
    public List<CourseOrgUser> fetchByBeginTime(ZonedDateTime... values) {
        return fetch(CourseOrgUserTable.COURSE_ORG_USER.BEGIN_TIME, values);
    }

    /**
     * Fetch records that have <code>end_time BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseOrgUser> fetchRangeOfEndTime(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(CourseOrgUserTable.COURSE_ORG_USER.END_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>end_time IN (values)</code>
     */
    public List<CourseOrgUser> fetchByEndTime(ZonedDateTime... values) {
        return fetch(CourseOrgUserTable.COURSE_ORG_USER.END_TIME, values);
    }

    /**
     * Fetch records that have <code>class_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseOrgUser> fetchRangeOfClassName(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseOrgUserTable.COURSE_ORG_USER.CLASS_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>class_name IN (values)</code>
     */
    public List<CourseOrgUser> fetchByClassName(String... values) {
        return fetch(CourseOrgUserTable.COURSE_ORG_USER.CLASS_NAME, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseOrgUser> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseOrgUserTable.COURSE_ORG_USER.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<CourseOrgUser> fetchByUid(Long... values) {
        return fetch(CourseOrgUserTable.COURSE_ORG_USER.UID, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseOrgUser> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseOrgUserTable.COURSE_ORG_USER.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CourseOrgUser> fetchByStatus(String... values) {
        return fetch(CourseOrgUserTable.COURSE_ORG_USER.STATUS, values);
    }

    /**
     * Fetch records that have <code>org_code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseOrgUser> fetchRangeOfOrgCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseOrgUserTable.COURSE_ORG_USER.ORG_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_code IN (values)</code>
     */
    public List<CourseOrgUser> fetchByOrgCode(String... values) {
        return fetch(CourseOrgUserTable.COURSE_ORG_USER.ORG_CODE, values);
    }
}
