/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.UserInternalMessageTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserInternalMessage;
import ai.creatly.sky.creation.infra.dal.tables.records.UserInternalMessageRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 用户站内消息
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class UserInternalMessageDAO extends DAOImpl<UserInternalMessageRecord, CreationUserInternalMessage, Long> {

    /**
     * Create a new UserInternalMessageDAO without any configuration
     */
    public UserInternalMessageDAO() {
        super(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE, CreationUserInternalMessage.class);
    }

    /**
     * Create a new UserInternalMessageDAO with an attached configuration
     */
    @Autowired
    public UserInternalMessageDAO(Configuration configuration) {
        super(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE, CreationUserInternalMessage.class, configuration);
    }

    @Override
    public Long getId(CreationUserInternalMessage object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInternalMessage> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationUserInternalMessage> fetchById(Long... values) {
        return fetch(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationUserInternalMessage fetchOneById(Long value) {
        return fetchOne(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationUserInternalMessage> fetchOptionalById(Long value) {
        return fetchOptional(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInternalMessage> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationUserInternalMessage> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInternalMessage> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationUserInternalMessage> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInternalMessage> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<CreationUserInternalMessage> fetchByUid(Long... values) {
        return fetch(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.UID, values);
    }

    /**
     * Fetch records that have <code>source_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInternalMessage> fetchRangeOfSourceId(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.SOURCE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>source_id IN (values)</code>
     */
    public List<CreationUserInternalMessage> fetchBySourceId(String... values) {
        return fetch(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.SOURCE_ID, values);
    }

    /**
     * Fetch records that have <code>source_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInternalMessage> fetchRangeOfSourceType(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.SOURCE_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>source_type IN (values)</code>
     */
    public List<CreationUserInternalMessage> fetchBySourceType(String... values) {
        return fetch(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.SOURCE_TYPE, values);
    }

    /**
     * Fetch records that have <code>message_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInternalMessage> fetchRangeOfMessageType(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.MESSAGE_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>message_type IN (values)</code>
     */
    public List<CreationUserInternalMessage> fetchByMessageType(String... values) {
        return fetch(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.MESSAGE_TYPE, values);
    }

    /**
     * Fetch records that have <code>biz_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInternalMessage> fetchRangeOfBizType(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.BIZ_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_type IN (values)</code>
     */
    public List<CreationUserInternalMessage> fetchByBizType(String... values) {
        return fetch(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.BIZ_TYPE, values);
    }

    /**
     * Fetch records that have <code>content BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInternalMessage> fetchRangeOfContent(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.CONTENT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>content IN (values)</code>
     */
    public List<CreationUserInternalMessage> fetchByContent(String... values) {
        return fetch(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.CONTENT, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInternalMessage> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CreationUserInternalMessage> fetchByStatus(String... values) {
        return fetch(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.STATUS, values);
    }

    /**
     * Fetch records that have <code>read_time BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserInternalMessage> fetchRangeOfReadTime(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.READ_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>read_time IN (values)</code>
     */
    public List<CreationUserInternalMessage> fetchByReadTime(ZonedDateTime... values) {
        return fetch(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.READ_TIME, values);
    }
}
