/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.NotificationTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationNotification;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户反馈
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class NotificationRecord extends UpdatableRecordImpl<NotificationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_notification.id</code>. 主键
     */
    public NotificationRecord setId(@Nullable Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_notification.created_at</code>. 创建时间
     */
    public NotificationRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_notification.updated_at</code>. 更新时间
     */
    public NotificationRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_notification.status</code>. 通知状态/已读/未读
     */
    public NotificationRecord setStatus(@Nullable String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.status</code>. 通知状态/已读/未读
     */
    @Nullable
    public String getStatus() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_notification.sender</code>. 通知发送者id
     */
    public NotificationRecord setSender(Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.sender</code>. 通知发送者id
     */
    public Long getSender() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.creation_notification.sender_name</code>. 通知发送者
     */
    public NotificationRecord setSenderName(@Nullable String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.sender_name</code>. 通知发送者
     */
    @Nullable
    public String getSenderName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_notification.receiver</code>. 接受者id
     */
    public NotificationRecord setReceiver(Long value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.receiver</code>. 接受者id
     */
    public Long getReceiver() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>creation.creation_notification.receiver_name</code>. 接受者
     */
    public NotificationRecord setReceiverName(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.receiver_name</code>. 接受者
     */
    @Nullable
    public String getReceiverName() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_notification.biz_type</code>. 业务类型
     */
    public NotificationRecord setBizType(@Nullable String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.biz_type</code>. 业务类型
     */
    @Nullable
    public String getBizType() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_notification.task_type</code>. 任务类型
     */
    public NotificationRecord setTaskType(@Nullable String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.task_type</code>. 任务类型
     */
    @Nullable
    public String getTaskType() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_notification.task_name</code>. 任务名
     */
    public NotificationRecord setTaskName(@Nullable String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.task_name</code>. 任务名
     */
    @Nullable
    public String getTaskName() {
        return (String) get(10);
    }

    /**
     * Setter for <code>creation.creation_notification.task_id</code>. 任务id
     */
    public NotificationRecord setTaskId(@Nullable String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.task_id</code>. 任务id
     */
    @Nullable
    public String getTaskId() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_notification.msg_type</code>.
     * 通知类型:系统/业务
     */
    public NotificationRecord setMsgType(String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.msg_type</code>.
     * 通知类型:系统/业务
     */
    public String getMsgType() {
        return (String) get(12);
    }

    /**
     * Setter for <code>creation.creation_notification.msg_content</code>. 消息体内容
     */
    public NotificationRecord setMsgContent(JSONB value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_notification.msg_content</code>. 消息体内容
     */
    public JSONB getMsgContent() {
        return (JSONB) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached NotificationRecord
     */
    public NotificationRecord() {
        super(NotificationTable.CREATION_NOTIFICATION);
    }

    /**
     * Create a detached, initialised NotificationRecord
     */
    public NotificationRecord(@Nullable Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable String status, Long sender, @Nullable String senderName, Long receiver, @Nullable String receiverName, @Nullable String bizType, @Nullable String taskType, @Nullable String taskName, @Nullable String taskId, String msgType, JSONB msgContent) {
        super(NotificationTable.CREATION_NOTIFICATION);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setStatus(status);
        setSender(sender);
        setSenderName(senderName);
        setReceiver(receiver);
        setReceiverName(receiverName);
        setBizType(bizType);
        setTaskType(taskType);
        setTaskName(taskName);
        setTaskId(taskId);
        setMsgType(msgType);
        setMsgContent(msgContent);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised NotificationRecord
     */
    public NotificationRecord(CreationNotification value) {
        super(NotificationTable.CREATION_NOTIFICATION);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setStatus(value.getStatus());
            setSender(value.getSender());
            setSenderName(value.getSenderName());
            setReceiver(value.getReceiver());
            setReceiverName(value.getReceiverName());
            setBizType(value.getBizType());
            setTaskType(value.getTaskType());
            setTaskName(value.getTaskName());
            setTaskId(value.getTaskId());
            setMsgType(value.getMsgType());
            setMsgContent(value.getMsgContent());
            resetChangedOnNotNull();
        }
    }
}
