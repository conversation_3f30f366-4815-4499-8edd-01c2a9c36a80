/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.VideoProjectTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationVideoProject;

import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 视频项目
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class VideoProjectRecord extends UpdatableRecordImpl<VideoProjectRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_video_project.id</code>. 主键
     */
    public VideoProjectRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_video_project.created_at</code>. 创建时间
     */
    public VideoProjectRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_video_project.updated_at</code>. 更新时间
     */
    public VideoProjectRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_video_project.uid</code>. 用户ID
     */
    public VideoProjectRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.uid</code>. 用户ID
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_video_project.status</code>. 项目状态
     */
    public VideoProjectRecord setStatus(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.status</code>. 项目状态
     */
    public String getStatus() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_video_project.stage</code>. 项目阶段
     */
    public VideoProjectRecord setStage(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.stage</code>. 项目阶段
     */
    public String getStage() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_video_project.name</code>. 项目名
     */
    public VideoProjectRecord setName(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.name</code>. 项目名
     */
    public String getName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_video_project.type</code>.
     */
    public VideoProjectRecord setType(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.type</code>.
     */
    public String getType() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_video_project.assets</code>. 素材列表
     */
    public VideoProjectRecord setAssets(JSONB value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.assets</code>. 素材列表
     */
    public JSONB getAssets() {
        return (JSONB) get(8);
    }

    /**
     * Setter for <code>creation.creation_video_project.script</code>. 视频脚本
     */
    public VideoProjectRecord setScript(JSONB value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.script</code>. 视频脚本
     */
    public JSONB getScript() {
        return (JSONB) get(9);
    }

    /**
     * Setter for <code>creation.creation_video_project.voiceover</code>. 视频配音
     */
    public VideoProjectRecord setVoiceover(@Nullable JSONB value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.voiceover</code>. 视频配音
     */
    @Nullable
    public JSONB getVoiceover() {
        return (JSONB) get(10);
    }

    /**
     * Setter for <code>creation.creation_video_project.roles</code>. 视频角色列表
     */
    public VideoProjectRecord setRoles(JSONB value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.roles</code>. 视频角色列表
     */
    public JSONB getRoles() {
        return (JSONB) get(11);
    }

    /**
     * Setter for <code>creation.creation_video_project.subtitle_layer</code>.
     * 字幕层
     */
    public VideoProjectRecord setSubtitleLayer(@Nullable JSONB value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.subtitle_layer</code>.
     * 字幕层
     */
    @Nullable
    public JSONB getSubtitleLayer() {
        return (JSONB) get(12);
    }

    /**
     * Setter for <code>creation.creation_video_project.avatar_layer</code>.
     * 数字人视频层
     */
    public VideoProjectRecord setAvatarLayer(@Nullable JSONB value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.avatar_layer</code>.
     * 数字人视频层
     */
    @Nullable
    public JSONB getAvatarLayer() {
        return (JSONB) get(13);
    }

    /**
     * Setter for <code>creation.creation_video_project.background_layer</code>.
     * 背景层
     */
    public VideoProjectRecord setBackgroundLayer(@Nullable JSONB value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.background_layer</code>.
     * 背景层
     */
    @Nullable
    public JSONB getBackgroundLayer() {
        return (JSONB) get(14);
    }

    /**
     * Setter for <code>creation.creation_video_project.audio_tracks</code>.
     * 音轨列表
     */
    public VideoProjectRecord setAudioTracks(JSONB value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.audio_tracks</code>.
     * 音轨列表
     */
    public JSONB getAudioTracks() {
        return (JSONB) get(15);
    }

    /**
     * Setter for <code>creation.creation_video_project.video_tracks</code>.
     * 视频轨列表
     */
    public VideoProjectRecord setVideoTracks(JSONB value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.video_tracks</code>.
     * 视频轨列表
     */
    public JSONB getVideoTracks() {
        return (JSONB) get(16);
    }

    /**
     * Setter for <code>creation.creation_video_project.aspect_ratio</code>.
     * 视频宽高比
     */
    public VideoProjectRecord setAspectRatio(String value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.aspect_ratio</code>.
     * 视频宽高比
     */
    public String getAspectRatio() {
        return (String) get(17);
    }

    /**
     * Setter for <code>creation.creation_video_project.width</code>. 视频默认宽度（像素）
     */
    public VideoProjectRecord setWidth(Integer value) {
        set(18, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.width</code>. 视频默认宽度（像素）
     */
    public Integer getWidth() {
        return (Integer) get(18);
    }

    /**
     * Setter for <code>creation.creation_video_project.height</code>.
     * 视频默认高度（像素）
     */
    public VideoProjectRecord setHeight(Integer value) {
        set(19, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.height</code>.
     * 视频默认高度（像素）
     */
    public Integer getHeight() {
        return (Integer) get(19);
    }

    /**
     * Setter for <code>creation.creation_video_project.shots</code>. 视频分镜列表
     */
    public VideoProjectRecord setShots(JSONB value) {
        set(20, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.shots</code>. 视频分镜列表
     */
    public JSONB getShots() {
        return (JSONB) get(20);
    }

    /**
     * Setter for <code>creation.creation_video_project.compose_at</code>.
     * 视频编排时间（最新一次）
     */
    public VideoProjectRecord setComposeAt(@Nullable ZonedDateTime value) {
        set(21, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.compose_at</code>.
     * 视频编排时间（最新一次）
     */
    @Nullable
    public ZonedDateTime getComposeAt() {
        return (ZonedDateTime) get(21);
    }

    /**
     * Setter for <code>creation.creation_video_project.compose_expired</code>.
     * 视频编排是否过期
     */
    public VideoProjectRecord setComposeExpired(Boolean value) {
        set(22, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.compose_expired</code>.
     * 视频编排是否过期
     */
    public Boolean getComposeExpired() {
        return (Boolean) get(22);
    }

    /**
     * Setter for <code>creation.creation_video_project.compose_version</code>.
     * 视频编排版本
     */
    public VideoProjectRecord setComposeVersion(@Nullable Integer value) {
        set(23, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.compose_version</code>.
     * 视频编排版本
     */
    @Nullable
    public Integer getComposeVersion() {
        return (Integer) get(23);
    }

    /**
     * Setter for <code>creation.creation_video_project.cover_file_id</code>.
     * 项目封面文件ID
     */
    public VideoProjectRecord setCoverFileId(@Nullable Long value) {
        set(24, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.cover_file_id</code>.
     * 项目封面文件ID
     */
    @Nullable
    public Long getCoverFileId() {
        return (Long) get(24);
    }

    /**
     * Setter for <code>creation.creation_video_project.cover_url</code>. 项目封面地址
     */
    public VideoProjectRecord setCoverUrl(@Nullable String value) {
        set(25, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.cover_url</code>. 项目封面地址
     */
    @Nullable
    public String getCoverUrl() {
        return (String) get(25);
    }

    /**
     * Setter for <code>creation.creation_video_project.duration</code>.
     * 视频总时长（最新一次）
     */
    public VideoProjectRecord setDuration(@Nullable Duration value) {
        set(26, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.duration</code>.
     * 视频总时长（最新一次）
     */
    @Nullable
    public Duration getDuration() {
        return (Duration) get(26);
    }

    /**
     * Setter for <code>creation.creation_video_project.videos</code>.
     * 成品视频（最新一批）
     */
    public VideoProjectRecord setVideos(JSONB value) {
        set(27, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.videos</code>.
     * 成品视频（最新一批）
     */
    public JSONB getVideos() {
        return (JSONB) get(27);
    }

    /**
     * Setter for <code>creation.creation_video_project.compose_tasks</code>.
     * 最新一版编排任务
     */
    public VideoProjectRecord setComposeTasks(@Nullable JSONB value) {
        set(28, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.compose_tasks</code>.
     * 最新一版编排任务
     */
    @Nullable
    public JSONB getComposeTasks() {
        return (JSONB) get(28);
    }

    /**
     * Setter for
     * <code>creation.creation_video_project.stage_failure_reason</code>.
     * 当前项目阶段的失败原因
     */
    public VideoProjectRecord setStageFailureReason(@Nullable String value) {
        set(29, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_video_project.stage_failure_reason</code>.
     * 当前项目阶段的失败原因
     */
    @Nullable
    public String getStageFailureReason() {
        return (String) get(29);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached VideoProjectRecord
     */
    public VideoProjectRecord() {
        super(VideoProjectTable.CREATION_VIDEO_PROJECT);
    }

    /**
     * Create a detached, initialised VideoProjectRecord
     */
    public VideoProjectRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String status, String stage, String name, String type, JSONB assets, JSONB script, @Nullable JSONB voiceover, JSONB roles, @Nullable JSONB subtitleLayer, @Nullable JSONB avatarLayer, @Nullable JSONB backgroundLayer, JSONB audioTracks, JSONB videoTracks, String aspectRatio, Integer width, Integer height, JSONB shots, @Nullable ZonedDateTime composeAt, Boolean composeExpired, @Nullable Integer composeVersion, @Nullable Long coverFileId, @Nullable String coverUrl, @Nullable Duration duration, JSONB videos, @Nullable JSONB composeTasks, @Nullable String stageFailureReason) {
        super(VideoProjectTable.CREATION_VIDEO_PROJECT);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setStatus(status);
        setStage(stage);
        setName(name);
        setType(type);
        setAssets(assets);
        setScript(script);
        setVoiceover(voiceover);
        setRoles(roles);
        setSubtitleLayer(subtitleLayer);
        setAvatarLayer(avatarLayer);
        setBackgroundLayer(backgroundLayer);
        setAudioTracks(audioTracks);
        setVideoTracks(videoTracks);
        setAspectRatio(aspectRatio);
        setWidth(width);
        setHeight(height);
        setShots(shots);
        setComposeAt(composeAt);
        setComposeExpired(composeExpired);
        setComposeVersion(composeVersion);
        setCoverFileId(coverFileId);
        setCoverUrl(coverUrl);
        setDuration(duration);
        setVideos(videos);
        setComposeTasks(composeTasks);
        setStageFailureReason(stageFailureReason);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised VideoProjectRecord
     */
    public VideoProjectRecord(CreationVideoProject value) {
        super(VideoProjectTable.CREATION_VIDEO_PROJECT);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setStatus(value.getStatus());
            setStage(value.getStage());
            setName(value.getName());
            setType(value.getType());
            setAssets(value.getAssets());
            setScript(value.getScript());
            setVoiceover(value.getVoiceover());
            setRoles(value.getRoles());
            setSubtitleLayer(value.getSubtitleLayer());
            setAvatarLayer(value.getAvatarLayer());
            setBackgroundLayer(value.getBackgroundLayer());
            setAudioTracks(value.getAudioTracks());
            setVideoTracks(value.getVideoTracks());
            setAspectRatio(value.getAspectRatio());
            setWidth(value.getWidth());
            setHeight(value.getHeight());
            setShots(value.getShots());
            setComposeAt(value.getComposeAt());
            setComposeExpired(value.getComposeExpired());
            setComposeVersion(value.getComposeVersion());
            setCoverFileId(value.getCoverFileId());
            setCoverUrl(value.getCoverUrl());
            setDuration(value.getDuration());
            setVideos(value.getVideos());
            setComposeTasks(value.getComposeTasks());
            setStageFailureReason(value.getStageFailureReason());
            resetChangedOnNotNull();
        }
    }
}
