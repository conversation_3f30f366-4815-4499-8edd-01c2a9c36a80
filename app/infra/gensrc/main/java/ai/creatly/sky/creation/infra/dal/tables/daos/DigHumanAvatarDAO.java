/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.DigHumanAvatarTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.DigHumanAvatar;
import ai.creatly.sky.creation.infra.dal.tables.records.DigHumanAvatarRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.JSONB;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 数字形象表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class DigHumanAvatarDAO extends DAOImpl<DigHumanAvatarRecord, DigHumanAvatar, Long> {

    /**
     * Create a new DigHumanAvatarDAO without any configuration
     */
    public DigHumanAvatarDAO() {
        super(DigHumanAvatarTable.DIG_HUMAN_AVATAR, DigHumanAvatar.class);
    }

    /**
     * Create a new DigHumanAvatarDAO with an attached configuration
     */
    @Autowired
    public DigHumanAvatarDAO(Configuration configuration) {
        super(DigHumanAvatarTable.DIG_HUMAN_AVATAR, DigHumanAvatar.class, configuration);
    }

    @Override
    public Long getId(DigHumanAvatar object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<DigHumanAvatar> fetchById(Long... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public DigHumanAvatar fetchOneById(Long value) {
        return fetchOne(DigHumanAvatarTable.DIG_HUMAN_AVATAR.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<DigHumanAvatar> fetchOptionalById(Long value) {
        return fetchOptional(DigHumanAvatarTable.DIG_HUMAN_AVATAR.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<DigHumanAvatar> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<DigHumanAvatar> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<DigHumanAvatar> fetchByUid(Long... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.UID, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<DigHumanAvatar> fetchByName(String... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.NAME, values);
    }

    /**
     * Fetch records that have <code>gender BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfGender(String lowerInclusive, String upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.GENDER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>gender IN (values)</code>
     */
    public List<DigHumanAvatar> fetchByGender(String... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.GENDER, values);
    }

    /**
     * Fetch records that have <code>ext_out_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfExtOutId(String lowerInclusive, String upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.EXT_OUT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ext_out_id IN (values)</code>
     */
    public List<DigHumanAvatar> fetchByExtOutId(String... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.EXT_OUT_ID, values);
    }

    /**
     * Fetch records that have <code>avatar_file BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfAvatarFile(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.AVATAR_FILE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>avatar_file IN (values)</code>
     */
    public List<DigHumanAvatar> fetchByAvatarFile(JSONB... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.AVATAR_FILE, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<DigHumanAvatar> fetchByStatus(String... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.STATUS, values);
    }

    /**
     * Fetch records that have <code>cover_file BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfCoverFile(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.COVER_FILE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover_file IN (values)</code>
     */
    public List<DigHumanAvatar> fetchByCoverFile(JSONB... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.COVER_FILE, values);
    }

    /**
     * Fetch records that have <code>video_width BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfVideoWidth(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.VIDEO_WIDTH, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>video_width IN (values)</code>
     */
    public List<DigHumanAvatar> fetchByVideoWidth(Integer... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.VIDEO_WIDTH, values);
    }

    /**
     * Fetch records that have <code>video_height BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfVideoHeight(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.VIDEO_HEIGHT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>video_height IN (values)</code>
     */
    public List<DigHumanAvatar> fetchByVideoHeight(Integer... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.VIDEO_HEIGHT, values);
    }

    /**
     * Fetch records that have <code>aspect_ratio BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<DigHumanAvatar> fetchRangeOfAspectRatio(String lowerInclusive, String upperInclusive) {
        return fetchRange(DigHumanAvatarTable.DIG_HUMAN_AVATAR.ASPECT_RATIO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>aspect_ratio IN (values)</code>
     */
    public List<DigHumanAvatar> fetchByAspectRatio(String... values) {
        return fetch(DigHumanAvatarTable.DIG_HUMAN_AVATAR.ASPECT_RATIO, values);
    }
}
