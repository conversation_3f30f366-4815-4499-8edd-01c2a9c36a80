/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.UserPlanTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserPlan;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户私人定价计划
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserPlanRecord extends UpdatableRecordImpl<UserPlanRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_user_plan.id</code>. 主键
     */
    public UserPlanRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_user_plan.created_at</code>. 创建时间
     */
    public UserPlanRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_user_plan.updated_at</code>. 更新时间
     */
    public UserPlanRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_user_plan.created_by</code>. 创建者
     */
    public UserPlanRecord setCreatedBy(JSONB value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.created_by</code>. 创建者
     */
    public JSONB getCreatedBy() {
        return (JSONB) get(3);
    }

    /**
     * Setter for <code>creation.creation_user_plan.updated_by</code>. 更新者
     */
    public UserPlanRecord setUpdatedBy(JSONB value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.updated_by</code>. 更新者
     */
    public JSONB getUpdatedBy() {
        return (JSONB) get(4);
    }

    /**
     * Setter for <code>creation.creation_user_plan.uid</code>. 用户ID
     */
    public UserPlanRecord setUid(Long value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.uid</code>. 用户ID
     */
    public Long getUid() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>creation.creation_user_plan.platform_plan_id</code>.
     * 平台统一定价计划ID
     */
    public UserPlanRecord setPlatformPlanId(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.platform_plan_id</code>.
     * 平台统一定价计划ID
     */
    public String getPlatformPlanId() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_user_plan.type</code>. 计划类型
     */
    public UserPlanRecord setType(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.type</code>. 计划类型
     */
    public String getType() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_user_plan.name</code>. 计划名称
     */
    public UserPlanRecord setName(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.name</code>. 计划名称
     */
    public String getName() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_user_plan.description</code>. 分类
     */
    public UserPlanRecord setDescription(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.description</code>. 分类
     */
    public String getDescription() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_user_plan.original_fee</code>. 原价（分）
     */
    public UserPlanRecord setOriginalFee(Long value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.original_fee</code>. 原价（分）
     */
    public Long getOriginalFee() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>creation.creation_user_plan.real_fee</code>. 实价（分）
     */
    public UserPlanRecord setRealFee(Long value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.real_fee</code>. 实价（分）
     */
    public Long getRealFee() {
        return (Long) get(11);
    }

    /**
     * Setter for <code>creation.creation_user_plan.order_title</code>. 订单标题
     */
    public UserPlanRecord setOrderTitle(String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.order_title</code>. 订单标题
     */
    public String getOrderTitle() {
        return (String) get(12);
    }

    /**
     * Setter for <code>creation.creation_user_plan.period_type</code>. 计划周期类型
     */
    public UserPlanRecord setPeriodType(String value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.period_type</code>. 计划周期类型
     */
    public String getPeriodType() {
        return (String) get(13);
    }

    /**
     * Setter for <code>creation.creation_user_plan.auto_renewed</code>.
     * 到期是否自动续费（如果为空，则取决于用户选择）
     */
    public UserPlanRecord setAutoRenewed(@Nullable Boolean value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.auto_renewed</code>.
     * 到期是否自动续费（如果为空，则取决于用户选择）
     */
    @Nullable
    public Boolean getAutoRenewed() {
        return (Boolean) get(14);
    }

    /**
     * Setter for <code>creation.creation_user_plan.level</code>.
     * 阶梯档位（用于排序、有序展示）
     */
    public UserPlanRecord setLevel(Integer value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.level</code>.
     * 阶梯档位（用于排序、有序展示）
     */
    public Integer getLevel() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>creation.creation_user_plan.benefit_duration</code>.
     * 权益有效期
     */
    public UserPlanRecord setBenefitDuration(@Nullable String value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.benefit_duration</code>.
     * 权益有效期
     */
    @Nullable
    public String getBenefitDuration() {
        return (String) get(16);
    }

    /**
     * Setter for <code>creation.creation_user_plan.benefits</code>. 权益列表
     */
    public UserPlanRecord setBenefits(JSONB value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.benefits</code>. 权益列表
     */
    public JSONB getBenefits() {
        return (JSONB) get(17);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserPlanRecord
     */
    public UserPlanRecord() {
        super(UserPlanTable.CREATION_USER_PLAN);
    }

    /**
     * Create a detached, initialised UserPlanRecord
     */
    public UserPlanRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, JSONB createdBy, JSONB updatedBy, Long uid, String platformPlanId, String type, String name, String description, Long originalFee, Long realFee, String orderTitle, String periodType, @Nullable Boolean autoRenewed, Integer level, @Nullable String benefitDuration, JSONB benefits) {
        super(UserPlanTable.CREATION_USER_PLAN);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setUid(uid);
        setPlatformPlanId(platformPlanId);
        setType(type);
        setName(name);
        setDescription(description);
        setOriginalFee(originalFee);
        setRealFee(realFee);
        setOrderTitle(orderTitle);
        setPeriodType(periodType);
        setAutoRenewed(autoRenewed);
        setLevel(level);
        setBenefitDuration(benefitDuration);
        setBenefits(benefits);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserPlanRecord
     */
    public UserPlanRecord(CreationUserPlan value) {
        super(UserPlanTable.CREATION_USER_PLAN);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCreatedBy(value.getCreatedBy());
            setUpdatedBy(value.getUpdatedBy());
            setUid(value.getUid());
            setPlatformPlanId(value.getPlatformPlanId());
            setType(value.getType());
            setName(value.getName());
            setDescription(value.getDescription());
            setOriginalFee(value.getOriginalFee());
            setRealFee(value.getRealFee());
            setOrderTitle(value.getOrderTitle());
            setPeriodType(value.getPeriodType());
            setAutoRenewed(value.getAutoRenewed());
            setLevel(value.getLevel());
            setBenefitDuration(value.getBenefitDuration());
            setBenefits(value.getBenefits());
            resetChangedOnNotNull();
        }
    }
}
