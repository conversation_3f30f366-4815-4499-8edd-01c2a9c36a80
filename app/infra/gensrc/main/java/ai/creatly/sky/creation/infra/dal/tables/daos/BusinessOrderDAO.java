/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.BusinessOrderTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationBusinessOrder;
import ai.creatly.sky.creation.infra.dal.tables.records.BusinessOrderRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 课程
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class BusinessOrderDAO extends DAOImpl<BusinessOrderRecord, CreationBusinessOrder, Long> {

    /**
     * Create a new BusinessOrderDAO without any configuration
     */
    public BusinessOrderDAO() {
        super(BusinessOrderTable.CREATION_BUSINESS_ORDER, CreationBusinessOrder.class);
    }

    /**
     * Create a new BusinessOrderDAO with an attached configuration
     */
    @Autowired
    public BusinessOrderDAO(Configuration configuration) {
        super(BusinessOrderTable.CREATION_BUSINESS_ORDER, CreationBusinessOrder.class, configuration);
    }

    @Override
    public Long getId(CreationBusinessOrder object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchById(Long... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationBusinessOrder fetchOneById(Long value) {
        return fetchOne(BusinessOrderTable.CREATION_BUSINESS_ORDER.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationBusinessOrder> fetchOptionalById(Long value) {
        return fetchOptional(BusinessOrderTable.CREATION_BUSINESS_ORDER.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>cover_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfCoverUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.COVER_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover_url IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByCoverUrl(String... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.COVER_URL, values);
    }

    /**
     * Fetch records that have <code>video_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfVideoUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.VIDEO_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>video_url IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByVideoUrl(String... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.VIDEO_URL, values);
    }

    /**
     * Fetch records that have <code>title BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfTitle(String lowerInclusive, String upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.TITLE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>title IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByTitle(String... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.TITLE, values);
    }

    /**
     * Fetch records that have <code>order_intro BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfOrderIntro(String lowerInclusive, String upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.ORDER_INTRO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>order_intro IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByOrderIntro(String... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.ORDER_INTRO, values);
    }

    /**
     * Fetch records that have <code>order_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfOrderStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.ORDER_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>order_status IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByOrderStatus(String... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.ORDER_STATUS, values);
    }

    /**
     * Fetch records that have <code>order_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfOrderType(String lowerInclusive, String upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.ORDER_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>order_type IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByOrderType(String... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.ORDER_TYPE, values);
    }

    /**
     * Fetch records that have <code>subtitle BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfSubtitle(String lowerInclusive, String upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.SUBTITLE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>subtitle IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchBySubtitle(String... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.SUBTITLE, values);
    }

    /**
     * Fetch records that have <code>order_price BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfOrderPrice(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.ORDER_PRICE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>order_price IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByOrderPrice(Long... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.ORDER_PRICE, values);
    }

    /**
     * Fetch records that have <code>begin_time BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfBeginTime(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.BEGIN_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>begin_time IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByBeginTime(ZonedDateTime... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.BEGIN_TIME, values);
    }

    /**
     * Fetch records that have <code>end_time BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfEndTime(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.END_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>end_time IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByEndTime(ZonedDateTime... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.END_TIME, values);
    }

    /**
     * Fetch records that have <code>priority BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfPriority(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.PRIORITY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>priority IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByPriority(Integer... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.PRIORITY, values);
    }

    /**
     * Fetch records that have <code>owner_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfOwnerId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.OWNER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>owner_id IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByOwnerId(Long... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.OWNER_ID, values);
    }

    /**
     * Fetch records that have <code>owner_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationBusinessOrder> fetchRangeOfOwnerName(String lowerInclusive, String upperInclusive) {
        return fetchRange(BusinessOrderTable.CREATION_BUSINESS_ORDER.OWNER_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>owner_name IN (values)</code>
     */
    public List<CreationBusinessOrder> fetchByOwnerName(String... values) {
        return fetch(BusinessOrderTable.CREATION_BUSINESS_ORDER.OWNER_NAME, values);
    }
}
