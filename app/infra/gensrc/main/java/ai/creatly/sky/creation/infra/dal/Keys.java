/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal;


import ai.creatly.sky.creation.infra.dal.tables.AiRechargeTable;
import ai.creatly.sky.creation.infra.dal.tables.AiTalkActorTable;
import ai.creatly.sky.creation.infra.dal.tables.AiTalkEmotionTable;
import ai.creatly.sky.creation.infra.dal.tables.AiTaskTable;
import ai.creatly.sky.creation.infra.dal.tables.AiToolTable;
import ai.creatly.sky.creation.infra.dal.tables.AiVideoTable;
import ai.creatly.sky.creation.infra.dal.tables.AiVoiceCloneTable;
import ai.creatly.sky.creation.infra.dal.tables.AssetTable;
import ai.creatly.sky.creation.infra.dal.tables.BenefitLogTable;
import ai.creatly.sky.creation.infra.dal.tables.BusinessOrderTable;
import ai.creatly.sky.creation.infra.dal.tables.ContentMetricTable;
import ai.creatly.sky.creation.infra.dal.tables.ContentTable;
import ai.creatly.sky.creation.infra.dal.tables.ConversationMessageTable;
import ai.creatly.sky.creation.infra.dal.tables.ConversationMsgTable;
import ai.creatly.sky.creation.infra.dal.tables.ConversationTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseCertificateTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseContentTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseExamPoolTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseExamRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseExamTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseHotTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseLearnRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseOrderRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseOrgSettingTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseOrgUserTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseTable;
import ai.creatly.sky.creation.infra.dal.tables.CourseUserCertificateTable;
import ai.creatly.sky.creation.infra.dal.tables.CreditHistoryTable;
import ai.creatly.sky.creation.infra.dal.tables.CreditLogTable;
import ai.creatly.sky.creation.infra.dal.tables.DigHumanAvatarTable;
import ai.creatly.sky.creation.infra.dal.tables.DigHumanVideoTable;
import ai.creatly.sky.creation.infra.dal.tables.DramaTable;
import ai.creatly.sky.creation.infra.dal.tables.FeatureCostRuleTable;
import ai.creatly.sky.creation.infra.dal.tables.FeedbackTable;
import ai.creatly.sky.creation.infra.dal.tables.LlmConfigTable;
import ai.creatly.sky.creation.infra.dal.tables.MaterialTable;
import ai.creatly.sky.creation.infra.dal.tables.MemberTable;
import ai.creatly.sky.creation.infra.dal.tables.MenuItemTable;
import ai.creatly.sky.creation.infra.dal.tables.MenuTable;
import ai.creatly.sky.creation.infra.dal.tables.MusicTable;
import ai.creatly.sky.creation.infra.dal.tables.NotificationTable;
import ai.creatly.sky.creation.infra.dal.tables.OpenapiCredentialsTable;
import ai.creatly.sky.creation.infra.dal.tables.OrganizationTable;
import ai.creatly.sky.creation.infra.dal.tables.ProductVideoTable;
import ai.creatly.sky.creation.infra.dal.tables.PromptCategoryTable;
import ai.creatly.sky.creation.infra.dal.tables.PromptTagTable;
import ai.creatly.sky.creation.infra.dal.tables.PromptTemplateTable;
import ai.creatly.sky.creation.infra.dal.tables.QrcodeRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.QrcodeScanRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.ScenarioPromptTable;
import ai.creatly.sky.creation.infra.dal.tables.SoundTable;
import ai.creatly.sky.creation.infra.dal.tables.StoryActorTable;
import ai.creatly.sky.creation.infra.dal.tables.StoryExportTable;
import ai.creatly.sky.creation.infra.dal.tables.StorySceneShotTable;
import ai.creatly.sky.creation.infra.dal.tables.StorySceneTable;
import ai.creatly.sky.creation.infra.dal.tables.StoryTable;
import ai.creatly.sky.creation.infra.dal.tables.SystemPreferenceTable;
import ai.creatly.sky.creation.infra.dal.tables.TagTable;
import ai.creatly.sky.creation.infra.dal.tables.UserAssetTable;
import ai.creatly.sky.creation.infra.dal.tables.UserCreditTable;
import ai.creatly.sky.creation.infra.dal.tables.UserFileTable;
import ai.creatly.sky.creation.infra.dal.tables.UserInternalMessageTable;
import ai.creatly.sky.creation.infra.dal.tables.UserInvitationTable;
import ai.creatly.sky.creation.infra.dal.tables.UserMaterialTable;
import ai.creatly.sky.creation.infra.dal.tables.UserPermissionTable;
import ai.creatly.sky.creation.infra.dal.tables.UserPlanTable;
import ai.creatly.sky.creation.infra.dal.tables.UserPreferenceTable;
import ai.creatly.sky.creation.infra.dal.tables.UserProductTable;
import ai.creatly.sky.creation.infra.dal.tables.UserTable;
import ai.creatly.sky.creation.infra.dal.tables.UserVoiceLocaleTable;
import ai.creatly.sky.creation.infra.dal.tables.UserVoiceTable;
import ai.creatly.sky.creation.infra.dal.tables.UserWorkspaceTable;
import ai.creatly.sky.creation.infra.dal.tables.VideoProjectTable;
import ai.creatly.sky.creation.infra.dal.tables.VoiceLocaleTable;
import ai.creatly.sky.creation.infra.dal.tables.VoiceTable;
import ai.creatly.sky.creation.infra.dal.tables.WorkspaceTemplateTable;
import ai.creatly.sky.creation.infra.dal.tables.records.AiRechargeRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.AiTalkActorRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.AiTalkEmotionRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.AiTaskRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.AiToolRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.AiVideoRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.AiVoiceCloneRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.AssetRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.BenefitLogRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.BusinessOrderRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.ContentMetricRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.ContentRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.ConversationMessageRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.ConversationMsgRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.ConversationRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseCertificateRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseContentRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseExamPoolRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseExamRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseExamRecordRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseHotRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseLearnRecordRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseOrderRecordRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseOrgSettingRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseOrgUserRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseUserCertificateRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CreditHistoryRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.CreditLogRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.DigHumanAvatarRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.DigHumanVideoRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.DramaRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.FeatureCostRuleRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.FeedbackRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.LlmConfigRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.MaterialRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.MemberRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.MenuItemRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.MenuRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.MusicRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.NotificationRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.OpenapiCredentialsRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.OrganizationRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.ProductVideoRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.PromptCategoryRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.PromptTagRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.PromptTemplateRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.QrcodeRecordRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.QrcodeScanRecordRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.ScenarioPromptRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.SoundRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.StoryActorRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.StoryExportRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.StoryRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.StorySceneRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.StorySceneShotRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.SystemPreferenceRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.TagRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserAssetRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserCreditRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserFileRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserInternalMessageRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserInvitationRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserMaterialRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserPermissionRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserPlanRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserPreferenceRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserProductRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserVoiceLocaleRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserVoiceRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.UserWorkspaceRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.VideoProjectRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.VoiceLocaleRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.VoiceRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.WorkspaceTemplateRecord;

import javax.annotation.processing.Generated;

import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling foreign key relationships and constraints of tables in
 * creation.
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class Keys {

    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------

    public static final UniqueKey<CourseRecord> COURSE_PKEY = Internal.createUniqueKey(CourseTable.COURSE, DSL.name("course_pkey"), new TableField[] { CourseTable.COURSE.ID }, true);
    public static final UniqueKey<CourseCertificateRecord> COURSE_CERT_PKEY = Internal.createUniqueKey(CourseCertificateTable.COURSE_CERTIFICATE, DSL.name("course_cert_pkey"), new TableField[] { CourseCertificateTable.COURSE_CERTIFICATE.ID }, true);
    public static final UniqueKey<CourseContentRecord> COURSE_CONTENT_PKEY = Internal.createUniqueKey(CourseContentTable.COURSE_CONTENT, DSL.name("course_content_pkey"), new TableField[] { CourseContentTable.COURSE_CONTENT.ID }, true);
    public static final UniqueKey<CourseExamRecord> COURSE_EXAM_PKEY = Internal.createUniqueKey(CourseExamTable.COURSE_EXAM, DSL.name("course_exam_pkey"), new TableField[] { CourseExamTable.COURSE_EXAM.ID }, true);
    public static final UniqueKey<CourseExamPoolRecord> COURSE_EXAM_POOL_PKEY = Internal.createUniqueKey(CourseExamPoolTable.COURSE_EXAM_POOL, DSL.name("course_exam_pool_pkey"), new TableField[] { CourseExamPoolTable.COURSE_EXAM_POOL.ID }, true);
    public static final UniqueKey<CourseExamRecordRecord> COURSE_EXAM_RECORD_PKEY = Internal.createUniqueKey(CourseExamRecordTable.COURSE_EXAM_RECORD, DSL.name("course_exam_record_pkey"), new TableField[] { CourseExamRecordTable.COURSE_EXAM_RECORD.ID }, true);
    public static final UniqueKey<CourseHotRecord> COURSE_HOT_PKEY = Internal.createUniqueKey(CourseHotTable.COURSE_HOT, DSL.name("course_hot_pkey"), new TableField[] { CourseHotTable.COURSE_HOT.ID }, true);
    public static final UniqueKey<CourseLearnRecordRecord> COURSE_LEARN_RECORD_PKEY = Internal.createUniqueKey(CourseLearnRecordTable.COURSE_LEARN_RECORD, DSL.name("course_learn_record_pkey"), new TableField[] { CourseLearnRecordTable.COURSE_LEARN_RECORD.ID }, true);
    public static final UniqueKey<CourseOrderRecordRecord> COURSE_ORDER_RECORD_PKEY = Internal.createUniqueKey(CourseOrderRecordTable.COURSE_ORDER_RECORD, DSL.name("course_order_record_pkey"), new TableField[] { CourseOrderRecordTable.COURSE_ORDER_RECORD.ID }, true);
    public static final UniqueKey<CourseOrgSettingRecord> COURSE_ORG_SETTING_PKEY = Internal.createUniqueKey(CourseOrgSettingTable.COURSE_ORG_SETTING, DSL.name("course_org_setting_pkey"), new TableField[] { CourseOrgSettingTable.COURSE_ORG_SETTING.ID }, true);
    public static final UniqueKey<CourseOrgUserRecord> COURSE_ORG_USER_PKEY = Internal.createUniqueKey(CourseOrgUserTable.COURSE_ORG_USER, DSL.name("course_org_user_pkey"), new TableField[] { CourseOrgUserTable.COURSE_ORG_USER.ID }, true);
    public static final UniqueKey<CourseUserCertificateRecord> COURSE_USER_CERT_PKEY = Internal.createUniqueKey(CourseUserCertificateTable.COURSE_USER_CERTIFICATE, DSL.name("course_user_cert_pkey"), new TableField[] { CourseUserCertificateTable.COURSE_USER_CERTIFICATE.ID }, true);
    public static final UniqueKey<AiRechargeRecord> CREATION_AI_RECHARGE_PKEY = Internal.createUniqueKey(AiRechargeTable.CREATION_AI_RECHARGE, DSL.name("creation_ai_recharge_pkey"), new TableField[] { AiRechargeTable.CREATION_AI_RECHARGE.ID }, true);
    public static final UniqueKey<AiTalkActorRecord> CREATION_AI_TALK_ACTOR_PKEY = Internal.createUniqueKey(AiTalkActorTable.CREATION_AI_TALK_ACTOR, DSL.name("creation_ai_talk_actor_pkey"), new TableField[] { AiTalkActorTable.CREATION_AI_TALK_ACTOR.ID }, true);
    public static final UniqueKey<AiTalkActorRecord> CREATION_AI_TALK_ACTOR_UID_IMAGE_ID_KEY = Internal.createUniqueKey(AiTalkActorTable.CREATION_AI_TALK_ACTOR, DSL.name("creation_ai_talk_actor_uid_image_id_key"), new TableField[] { AiTalkActorTable.CREATION_AI_TALK_ACTOR.UID, AiTalkActorTable.CREATION_AI_TALK_ACTOR.IMAGE_ID }, true);
    public static final UniqueKey<AiTalkEmotionRecord> CREATION_AI_TALK_EMOTION_CODE_KEY = Internal.createUniqueKey(AiTalkEmotionTable.CREATION_AI_TALK_EMOTION, DSL.name("creation_ai_talk_emotion_code_key"), new TableField[] { AiTalkEmotionTable.CREATION_AI_TALK_EMOTION.CODE }, true);
    public static final UniqueKey<AiTalkEmotionRecord> CREATION_AI_TALK_EMOTION_PKEY = Internal.createUniqueKey(AiTalkEmotionTable.CREATION_AI_TALK_EMOTION, DSL.name("creation_ai_talk_emotion_pkey"), new TableField[] { AiTalkEmotionTable.CREATION_AI_TALK_EMOTION.ID }, true);
    public static final UniqueKey<AiTaskRecord> CREATION_AI_TASK_PKEY = Internal.createUniqueKey(AiTaskTable.CREATION_AI_TASK, DSL.name("creation_ai_task_pkey"), new TableField[] { AiTaskTable.CREATION_AI_TASK.ID }, true);
    public static final UniqueKey<AiTaskRecord> CREATION_AI_TASK_TASK_TYPE_BIZ_TYPE_BIZ_NO_SUB_BIZ_NO_KEY = Internal.createUniqueKey(AiTaskTable.CREATION_AI_TASK, DSL.name("creation_ai_task_task_type_biz_type_biz_no_sub_biz_no_key"), new TableField[] { AiTaskTable.CREATION_AI_TASK.TASK_TYPE, AiTaskTable.CREATION_AI_TASK.BIZ_TYPE, AiTaskTable.CREATION_AI_TASK.BIZ_NO, AiTaskTable.CREATION_AI_TASK.SUB_BIZ_NO }, true);
    public static final UniqueKey<AiToolRecord> CREATION_AI_TOOL_PKEY = Internal.createUniqueKey(AiToolTable.CREATION_AI_TOOL, DSL.name("creation_ai_tool_pkey"), new TableField[] { AiToolTable.CREATION_AI_TOOL.ID }, true);
    public static final UniqueKey<AiVideoRecord> CREATION_AI_VIDEO_BIZ_SOURCE_BIZ_NO_KEY = Internal.createUniqueKey(AiVideoTable.CREATION_AI_VIDEO, DSL.name("creation_ai_video_biz_source_biz_no_key"), new TableField[] { AiVideoTable.CREATION_AI_VIDEO.BIZ_SOURCE, AiVideoTable.CREATION_AI_VIDEO.BIZ_NO }, true);
    public static final UniqueKey<AiVideoRecord> CREATION_AI_VIDEO_PKEY = Internal.createUniqueKey(AiVideoTable.CREATION_AI_VIDEO, DSL.name("creation_ai_video_pkey"), new TableField[] { AiVideoTable.CREATION_AI_VIDEO.ID }, true);
    public static final UniqueKey<AiVoiceCloneRecord> CREATION_USER_VOICE_CLONE_PKEY = Internal.createUniqueKey(AiVoiceCloneTable.CREATION_AI_VOICE_CLONE, DSL.name("creation_user_voice_clone_pkey"), new TableField[] { AiVoiceCloneTable.CREATION_AI_VOICE_CLONE.ID }, true);
    public static final UniqueKey<AssetRecord> CREATION_ASSET_PKEY = Internal.createUniqueKey(AssetTable.CREATION_ASSET, DSL.name("creation_asset_pkey"), new TableField[] { AssetTable.CREATION_ASSET.ID }, true);
    public static final UniqueKey<BenefitLogRecord> CREATION_BENEFIT_LOG_PKEY = Internal.createUniqueKey(BenefitLogTable.CREATION_BENEFIT_LOG, DSL.name("creation_benefit_log_pkey"), new TableField[] { BenefitLogTable.CREATION_BENEFIT_LOG.ID }, true);
    public static final UniqueKey<BenefitLogRecord> CREATION_BENEFIT_LOG_UID_SOURCE_TYPE_SOURCE_ID_KEY = Internal.createUniqueKey(BenefitLogTable.CREATION_BENEFIT_LOG, DSL.name("creation_benefit_log_uid_source_type_source_id_key"), new TableField[] { BenefitLogTable.CREATION_BENEFIT_LOG.UID, BenefitLogTable.CREATION_BENEFIT_LOG.SOURCE_TYPE, BenefitLogTable.CREATION_BENEFIT_LOG.SOURCE_ID }, true);
    public static final UniqueKey<BusinessOrderRecord> BUSINESS_ORDER_PKEY = Internal.createUniqueKey(BusinessOrderTable.CREATION_BUSINESS_ORDER, DSL.name("business_order_pkey"), new TableField[] { BusinessOrderTable.CREATION_BUSINESS_ORDER.ID }, true);
    public static final UniqueKey<ContentRecord> CREATION_CONTENT_PKEY = Internal.createUniqueKey(ContentTable.CREATION_CONTENT, DSL.name("creation_content_pkey"), new TableField[] { ContentTable.CREATION_CONTENT.ID }, true);
    public static final UniqueKey<ContentMetricRecord> CREATION_CONTENT_METRIC_PKEY = Internal.createUniqueKey(ContentMetricTable.CREATION_CONTENT_METRIC, DSL.name("creation_content_metric_pkey"), new TableField[] { ContentMetricTable.CREATION_CONTENT_METRIC.ID }, true);
    public static final UniqueKey<ContentMetricRecord> UK_METRIC_CODE = Internal.createUniqueKey(ContentMetricTable.CREATION_CONTENT_METRIC, DSL.name("uk_metric_code"), new TableField[] { ContentMetricTable.CREATION_CONTENT_METRIC.METRIC_CODE }, true);
    public static final UniqueKey<ConversationRecord> CREATION_CONVERSATION_PKEY = Internal.createUniqueKey(ConversationTable.CREATION_CONVERSATION, DSL.name("creation_conversation_pkey"), new TableField[] { ConversationTable.CREATION_CONVERSATION.ID }, true);
    public static final UniqueKey<ConversationMessageRecord> CREATION_CONVERSATION_MESSAGE_PKEY = Internal.createUniqueKey(ConversationMessageTable.CREATION_CONVERSATION_MESSAGE, DSL.name("creation_conversation_message_pkey"), new TableField[] { ConversationMessageTable.CREATION_CONVERSATION_MESSAGE.ID }, true);
    public static final UniqueKey<ConversationMsgRecord> CREATION_CONVERSATION_MSG_PKEY = Internal.createUniqueKey(ConversationMsgTable.CREATION_CONVERSATION_MSG, DSL.name("creation_conversation_msg_pkey"), new TableField[] { ConversationMsgTable.CREATION_CONVERSATION_MSG.ID }, true);
    public static final UniqueKey<CreditHistoryRecord> CREATION_CREDIT_HISTORY_PKEY = Internal.createUniqueKey(CreditHistoryTable.CREATION_CREDIT_HISTORY, DSL.name("creation_credit_history_pkey"), new TableField[] { CreditHistoryTable.CREATION_CREDIT_HISTORY.ID }, true);
    public static final UniqueKey<CreditHistoryRecord> CREATION_CREDIT_HISTORY_USER_CREDIT_ID_BIZ_NO_BIZ_TYPE_TYPE_KEY = Internal.createUniqueKey(CreditHistoryTable.CREATION_CREDIT_HISTORY, DSL.name("creation_credit_history_user_credit_id_biz_no_biz_type_type_key"), new TableField[] { CreditHistoryTable.CREATION_CREDIT_HISTORY.USER_CREDIT_ID, CreditHistoryTable.CREATION_CREDIT_HISTORY.BIZ_NO, CreditHistoryTable.CREATION_CREDIT_HISTORY.BIZ_TYPE, CreditHistoryTable.CREATION_CREDIT_HISTORY.TYPE }, true);
    public static final UniqueKey<CreditLogRecord> CREATION_CREDIT_LOG_PKEY = Internal.createUniqueKey(CreditLogTable.CREATION_CREDIT_LOG, DSL.name("creation_credit_log_pkey"), new TableField[] { CreditLogTable.CREATION_CREDIT_LOG.ID }, true);
    public static final UniqueKey<CreditLogRecord> CREATION_CREDIT_LOG_UID_BIZ_NO_BIZ_TYPE_TYPE_KEY = Internal.createUniqueKey(CreditLogTable.CREATION_CREDIT_LOG, DSL.name("creation_credit_log_uid_biz_no_biz_type_type_key"), new TableField[] { CreditLogTable.CREATION_CREDIT_LOG.UID, CreditLogTable.CREATION_CREDIT_LOG.BIZ_NO, CreditLogTable.CREATION_CREDIT_LOG.BIZ_TYPE, CreditLogTable.CREATION_CREDIT_LOG.TYPE }, true);
    public static final UniqueKey<DramaRecord> CREATION_DRAMA_PKEY = Internal.createUniqueKey(DramaTable.CREATION_DRAMA, DSL.name("creation_drama_pkey"), new TableField[] { DramaTable.CREATION_DRAMA.ID }, true);
    public static final UniqueKey<FeatureCostRuleRecord> CREATION_FEATURE_COST_RULE_PKEY = Internal.createUniqueKey(FeatureCostRuleTable.CREATION_FEATURE_COST_RULE, DSL.name("creation_feature_cost_rule_pkey"), new TableField[] { FeatureCostRuleTable.CREATION_FEATURE_COST_RULE.ID }, true);
    public static final UniqueKey<FeedbackRecord> CREATION_FEEDBACK_PKEY = Internal.createUniqueKey(FeedbackTable.CREATION_FEEDBACK, DSL.name("creation_feedback_pkey"), new TableField[] { FeedbackTable.CREATION_FEEDBACK.ID }, true);
    public static final UniqueKey<LlmConfigRecord> CREATION_LLM_CONFIG_PKEY = Internal.createUniqueKey(LlmConfigTable.CREATION_LLM_CONFIG, DSL.name("creation_llm_config_pkey"), new TableField[] { LlmConfigTable.CREATION_LLM_CONFIG.ID }, true);
    public static final UniqueKey<MaterialRecord> CREATION_MATERIAL_PKEY = Internal.createUniqueKey(MaterialTable.CREATION_MATERIAL, DSL.name("creation_material_pkey"), new TableField[] { MaterialTable.CREATION_MATERIAL.ID }, true);
    public static final UniqueKey<MemberRecord> CREATION_MEMBER_PKEY = Internal.createUniqueKey(MemberTable.CREATION_MEMBER, DSL.name("creation_member_pkey"), new TableField[] { MemberTable.CREATION_MEMBER.ID }, true);
    public static final UniqueKey<MemberRecord> CREATION_MEMBER_UID_TYPE_KEY = Internal.createUniqueKey(MemberTable.CREATION_MEMBER, DSL.name("creation_member_uid_type_key"), new TableField[] { MemberTable.CREATION_MEMBER.UID, MemberTable.CREATION_MEMBER.TYPE }, true);
    public static final UniqueKey<MenuRecord> CREATION_MENU_CODE_VERSION_KEY = Internal.createUniqueKey(MenuTable.CREATION_MENU, DSL.name("creation_menu_code_version_key"), new TableField[] { MenuTable.CREATION_MENU.CODE, MenuTable.CREATION_MENU.VERSION }, true);
    public static final UniqueKey<MenuRecord> CREATION_MENU_PKEY = Internal.createUniqueKey(MenuTable.CREATION_MENU, DSL.name("creation_menu_pkey"), new TableField[] { MenuTable.CREATION_MENU.ID }, true);
    public static final UniqueKey<MenuItemRecord> CREATION_MENU_ITEM_MENU_ID_CODE_KEY = Internal.createUniqueKey(MenuItemTable.CREATION_MENU_ITEM, DSL.name("creation_menu_item_menu_id_code_key"), new TableField[] { MenuItemTable.CREATION_MENU_ITEM.MENU_ID, MenuItemTable.CREATION_MENU_ITEM.CODE }, true);
    public static final UniqueKey<MenuItemRecord> CREATION_MENU_ITEM_PKEY = Internal.createUniqueKey(MenuItemTable.CREATION_MENU_ITEM, DSL.name("creation_menu_item_pkey"), new TableField[] { MenuItemTable.CREATION_MENU_ITEM.ID }, true);
    public static final UniqueKey<MusicRecord> CREATION_MUSIC_PKEY = Internal.createUniqueKey(MusicTable.CREATION_MUSIC, DSL.name("creation_music_pkey"), new TableField[] { MusicTable.CREATION_MUSIC.ID }, true);
    public static final UniqueKey<MusicRecord> CREATION_MUSIC_SOURCE_TYPE_SOURCE_NO_KEY = Internal.createUniqueKey(MusicTable.CREATION_MUSIC, DSL.name("creation_music_source_type_source_no_key"), new TableField[] { MusicTable.CREATION_MUSIC.SOURCE_TYPE, MusicTable.CREATION_MUSIC.SOURCE_NO }, true);
    public static final UniqueKey<NotificationRecord> CREATION_NOTIFICATION_PKEY = Internal.createUniqueKey(NotificationTable.CREATION_NOTIFICATION, DSL.name("creation_notification_pkey"), new TableField[] { NotificationTable.CREATION_NOTIFICATION.ID }, true);
    public static final UniqueKey<OpenapiCredentialsRecord> CREATION_OPENAPI_CREDENTIALS_APP_KEY_KEY = Internal.createUniqueKey(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS, DSL.name("creation_openapi_credentials_app_key_key"), new TableField[] { OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.APP_KEY }, true);
    public static final UniqueKey<OpenapiCredentialsRecord> CREATION_OPENAPI_CREDENTIALS_APP_KEY_KEY1 = Internal.createUniqueKey(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS, DSL.name("creation_openapi_credentials_app_key_key1"), new TableField[] { OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.APP_KEY }, true);
    public static final UniqueKey<OpenapiCredentialsRecord> CREATION_OPENAPI_CREDENTIALS_PKEY = Internal.createUniqueKey(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS, DSL.name("creation_openapi_credentials_pkey"), new TableField[] { OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.ID }, true);
    public static final UniqueKey<OpenapiCredentialsRecord> CREATION_OPENAPI_CREDENTIALS_UID_KEY = Internal.createUniqueKey(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS, DSL.name("creation_openapi_credentials_uid_key"), new TableField[] { OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.UID }, true);
    public static final UniqueKey<OpenapiCredentialsRecord> CREATION_OPENAPI_CREDENTIALS_UID_KEY1 = Internal.createUniqueKey(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS, DSL.name("creation_openapi_credentials_uid_key1"), new TableField[] { OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.UID }, true);
    public static final UniqueKey<ProductVideoRecord> CREATION_PRODUCT_VIDEO_PKEY = Internal.createUniqueKey(ProductVideoTable.CREATION_PRODUCT_VIDEO, DSL.name("creation_product_video_pkey"), new TableField[] { ProductVideoTable.CREATION_PRODUCT_VIDEO.ID }, true);
    public static final UniqueKey<PromptCategoryRecord> CREATION_PROMPT_CATEGORY_PKEY = Internal.createUniqueKey(PromptCategoryTable.CREATION_PROMPT_CATEGORY, DSL.name("creation_prompt_category_pkey"), new TableField[] { PromptCategoryTable.CREATION_PROMPT_CATEGORY.ID }, true);
    public static final UniqueKey<PromptTagRecord> CREATION_PROMPT_TAG_CODE_KEY = Internal.createUniqueKey(PromptTagTable.CREATION_PROMPT_TAG, DSL.name("creation_prompt_tag_code_key"), new TableField[] { PromptTagTable.CREATION_PROMPT_TAG.CODE }, true);
    public static final UniqueKey<PromptTagRecord> CREATION_PROMPT_TAG_PKEY = Internal.createUniqueKey(PromptTagTable.CREATION_PROMPT_TAG, DSL.name("creation_prompt_tag_pkey"), new TableField[] { PromptTagTable.CREATION_PROMPT_TAG.ID }, true);
    public static final UniqueKey<PromptTemplateRecord> CREATION_PROMPT_TEMPLATE_CODE_KEY = Internal.createUniqueKey(PromptTemplateTable.CREATION_PROMPT_TEMPLATE, DSL.name("creation_prompt_template_code_key"), new TableField[] { PromptTemplateTable.CREATION_PROMPT_TEMPLATE.CODE }, true);
    public static final UniqueKey<PromptTemplateRecord> CREATION_PROMPT_TEMPLATE_PKEY = Internal.createUniqueKey(PromptTemplateTable.CREATION_PROMPT_TEMPLATE, DSL.name("creation_prompt_template_pkey"), new TableField[] { PromptTemplateTable.CREATION_PROMPT_TEMPLATE.ID }, true);
    public static final UniqueKey<ScenarioPromptRecord> CREATION_SCENARIO_PROMPT_PKEY = Internal.createUniqueKey(ScenarioPromptTable.CREATION_SCENARIO_PROMPT, DSL.name("creation_scenario_prompt_pkey"), new TableField[] { ScenarioPromptTable.CREATION_SCENARIO_PROMPT.ID }, true);
    public static final UniqueKey<ScenarioPromptRecord> CREATION_SCENARIO_PROMPT_SCENARIO_CODE_KEY = Internal.createUniqueKey(ScenarioPromptTable.CREATION_SCENARIO_PROMPT, DSL.name("creation_scenario_prompt_scenario_code_key"), new TableField[] { ScenarioPromptTable.CREATION_SCENARIO_PROMPT.SCENARIO_CODE }, true);
    public static final UniqueKey<SoundRecord> CREATION_SOUND_PKEY = Internal.createUniqueKey(SoundTable.CREATION_SOUND, DSL.name("creation_sound_pkey"), new TableField[] { SoundTable.CREATION_SOUND.ID }, true);
    public static final UniqueKey<SoundRecord> CREATION_SOUND_SOURCE_TYPE_SOURCE_NO_KEY = Internal.createUniqueKey(SoundTable.CREATION_SOUND, DSL.name("creation_sound_source_type_source_no_key"), new TableField[] { SoundTable.CREATION_SOUND.SOURCE_TYPE, SoundTable.CREATION_SOUND.SOURCE_NO }, true);
    public static final UniqueKey<StoryRecord> CREATION_STORY_PKEY = Internal.createUniqueKey(StoryTable.CREATION_STORY, DSL.name("creation_story_pkey"), new TableField[] { StoryTable.CREATION_STORY.ID }, true);
    public static final UniqueKey<StoryActorRecord> CREATION_STORY_ACTORS_PKEY = Internal.createUniqueKey(StoryActorTable.CREATION_STORY_ACTOR, DSL.name("creation_story_actors_pkey"), new TableField[] { StoryActorTable.CREATION_STORY_ACTOR.ID }, true);
    public static final UniqueKey<StoryExportRecord> CREATION_STORY_EXPORT_PKEY = Internal.createUniqueKey(StoryExportTable.CREATION_STORY_EXPORT, DSL.name("creation_story_export_pkey"), new TableField[] { StoryExportTable.CREATION_STORY_EXPORT.ID }, true);
    public static final UniqueKey<StorySceneRecord> CREATION_STORY_SCENE_PKEY = Internal.createUniqueKey(StorySceneTable.CREATION_STORY_SCENE, DSL.name("creation_story_scene_pkey"), new TableField[] { StorySceneTable.CREATION_STORY_SCENE.ID }, true);
    public static final UniqueKey<StorySceneShotRecord> CREATION_STORY_SCENE_SHOT_PKEY = Internal.createUniqueKey(StorySceneShotTable.CREATION_STORY_SCENE_SHOT, DSL.name("creation_story_scene_shot_pkey"), new TableField[] { StorySceneShotTable.CREATION_STORY_SCENE_SHOT.ID }, true);
    public static final UniqueKey<TagRecord> CREATION_TAG_PKEY = Internal.createUniqueKey(TagTable.CREATION_TAG, DSL.name("creation_tag_pkey"), new TableField[] { TagTable.CREATION_TAG.ID }, true);
    public static final UniqueKey<TagRecord> UK_TAG_CODE = Internal.createUniqueKey(TagTable.CREATION_TAG, DSL.name("uk_tag_code"), new TableField[] { TagTable.CREATION_TAG.TAG_CODE }, true);
    public static final UniqueKey<UserAssetRecord> CREATION_USER_ASSET_PKEY = Internal.createUniqueKey(UserAssetTable.CREATION_USER_ASSET, DSL.name("creation_user_asset_pkey"), new TableField[] { UserAssetTable.CREATION_USER_ASSET.ID }, true);
    public static final UniqueKey<UserFileRecord> CREATION_USER_FILE_PKEY = Internal.createUniqueKey(UserFileTable.CREATION_USER_FILE, DSL.name("creation_user_file_pkey"), new TableField[] { UserFileTable.CREATION_USER_FILE.ID }, true);
    public static final UniqueKey<UserInternalMessageRecord> CREATION_USER_INTERNAL_MESSAGE_PKEY = Internal.createUniqueKey(UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE, DSL.name("creation_user_internal_message_pkey"), new TableField[] { UserInternalMessageTable.CREATION_USER_INTERNAL_MESSAGE.ID }, true);
    public static final UniqueKey<UserInvitationRecord> CREATION_USER_INVITATION_PKEY = Internal.createUniqueKey(UserInvitationTable.CREATION_USER_INVITATION, DSL.name("creation_user_invitation_pkey"), new TableField[] { UserInvitationTable.CREATION_USER_INVITATION.ID }, true);
    public static final UniqueKey<UserMaterialRecord> CREATION_USER_MATERIAL_PKEY = Internal.createUniqueKey(UserMaterialTable.CREATION_USER_MATERIAL, DSL.name("creation_user_material_pkey"), new TableField[] { UserMaterialTable.CREATION_USER_MATERIAL.ID }, true);
    public static final UniqueKey<UserPlanRecord> CREATION_USER_PLAN_PKEY = Internal.createUniqueKey(UserPlanTable.CREATION_USER_PLAN, DSL.name("creation_user_plan_pkey"), new TableField[] { UserPlanTable.CREATION_USER_PLAN.ID }, true);
    public static final UniqueKey<UserPlanRecord> CREATION_USER_PLAN_UID_PLATFORM_PLAN_ID_KEY = Internal.createUniqueKey(UserPlanTable.CREATION_USER_PLAN, DSL.name("creation_user_plan_uid_platform_plan_id_key"), new TableField[] { UserPlanTable.CREATION_USER_PLAN.UID, UserPlanTable.CREATION_USER_PLAN.PLATFORM_PLAN_ID }, true);
    public static final UniqueKey<UserProductRecord> CREATION_USER_PRODUCT_PKEY = Internal.createUniqueKey(UserProductTable.CREATION_USER_PRODUCT, DSL.name("creation_user_product_pkey"), new TableField[] { UserProductTable.CREATION_USER_PRODUCT.ID }, true);
    public static final UniqueKey<UserVoiceRecord> CREATION_USER_VOICE_PKEY = Internal.createUniqueKey(UserVoiceTable.CREATION_USER_VOICE, DSL.name("creation_user_voice_pkey"), new TableField[] { UserVoiceTable.CREATION_USER_VOICE.ID }, true);
    public static final UniqueKey<UserVoiceRecord> CREATION_USER_VOICE_UID_CODE_KEY = Internal.createUniqueKey(UserVoiceTable.CREATION_USER_VOICE, DSL.name("creation_user_voice_uid_code_key"), new TableField[] { UserVoiceTable.CREATION_USER_VOICE.UID, UserVoiceTable.CREATION_USER_VOICE.CODE }, true);
    public static final UniqueKey<UserVoiceLocaleRecord> CREATION_USER_VOICE_LOCALE_PKEY = Internal.createUniqueKey(UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE, DSL.name("creation_user_voice_locale_pkey"), new TableField[] { UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE.ID }, true);
    public static final UniqueKey<UserVoiceLocaleRecord> CREATION_USER_VOICE_LOCALE_UID_VOICE_CODE_CODE_KEY = Internal.createUniqueKey(UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE, DSL.name("creation_user_voice_locale_uid_voice_code_code_key"), new TableField[] { UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE.UID, UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE.VOICE_CODE, UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE.CODE }, true);
    public static final UniqueKey<UserVoiceLocaleRecord> CREATION_USER_VOICE_LOCALE_VOICE_ID_CODE_KEY = Internal.createUniqueKey(UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE, DSL.name("creation_user_voice_locale_voice_id_code_key"), new TableField[] { UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE.VOICE_ID, UserVoiceLocaleTable.CREATION_USER_VOICE_LOCALE.CODE }, true);
    public static final UniqueKey<UserWorkspaceRecord> CREATION_USER_WORKSPACE_PKEY = Internal.createUniqueKey(UserWorkspaceTable.CREATION_USER_WORKSPACE, DSL.name("creation_user_workspace_pkey"), new TableField[] { UserWorkspaceTable.CREATION_USER_WORKSPACE.ID }, true);
    public static final UniqueKey<VideoProjectRecord> CREATION_VIDEO_PROJECT_PKEY = Internal.createUniqueKey(VideoProjectTable.CREATION_VIDEO_PROJECT, DSL.name("creation_video_project_pkey"), new TableField[] { VideoProjectTable.CREATION_VIDEO_PROJECT.ID }, true);
    public static final UniqueKey<VoiceRecord> CREATION_VOICE_CODE_KEY = Internal.createUniqueKey(VoiceTable.CREATION_VOICE, DSL.name("creation_voice_code_key"), new TableField[] { VoiceTable.CREATION_VOICE.CODE }, true);
    public static final UniqueKey<VoiceRecord> CREATION_VOICE_PKEY = Internal.createUniqueKey(VoiceTable.CREATION_VOICE, DSL.name("creation_voice_pkey"), new TableField[] { VoiceTable.CREATION_VOICE.ID }, true);
    public static final UniqueKey<VoiceLocaleRecord> CREATION_VOICE_LOCALE_PKEY = Internal.createUniqueKey(VoiceLocaleTable.CREATION_VOICE_LOCALE, DSL.name("creation_voice_locale_pkey"), new TableField[] { VoiceLocaleTable.CREATION_VOICE_LOCALE.ID }, true);
    public static final UniqueKey<VoiceLocaleRecord> CREATION_VOICE_LOCALE_VOICE_CODE_CODE_KEY = Internal.createUniqueKey(VoiceLocaleTable.CREATION_VOICE_LOCALE, DSL.name("creation_voice_locale_voice_code_code_key"), new TableField[] { VoiceLocaleTable.CREATION_VOICE_LOCALE.VOICE_CODE, VoiceLocaleTable.CREATION_VOICE_LOCALE.CODE }, true);
    public static final UniqueKey<VoiceLocaleRecord> CREATION_VOICE_LOCALE_VOICE_ID_CODE_KEY = Internal.createUniqueKey(VoiceLocaleTable.CREATION_VOICE_LOCALE, DSL.name("creation_voice_locale_voice_id_code_key"), new TableField[] { VoiceLocaleTable.CREATION_VOICE_LOCALE.VOICE_ID, VoiceLocaleTable.CREATION_VOICE_LOCALE.CODE }, true);
    public static final UniqueKey<WorkspaceTemplateRecord> CREATION_WORKSPACE_TEMPLATE_PKEY = Internal.createUniqueKey(WorkspaceTemplateTable.CREATION_WORKSPACE_TEMPLATE, DSL.name("creation_workspace_template_pkey"), new TableField[] { WorkspaceTemplateTable.CREATION_WORKSPACE_TEMPLATE.ID }, true);
    public static final UniqueKey<DigHumanAvatarRecord> DIG_HUMAN_AVATAR_PKEY = Internal.createUniqueKey(DigHumanAvatarTable.DIG_HUMAN_AVATAR, DSL.name("dig_human_avatar_pkey"), new TableField[] { DigHumanAvatarTable.DIG_HUMAN_AVATAR.ID }, true);
    public static final UniqueKey<DigHumanVideoRecord> DIG_HUMAN_VIDEO_PKEY = Internal.createUniqueKey(DigHumanVideoTable.DIG_HUMAN_VIDEO, DSL.name("dig_human_video_pkey"), new TableField[] { DigHumanVideoTable.DIG_HUMAN_VIDEO.ID }, true);
    public static final UniqueKey<OrganizationRecord> ORGANIZATION_CODE_KEY = Internal.createUniqueKey(OrganizationTable.ORGANIZATION, DSL.name("organization_code_key"), new TableField[] { OrganizationTable.ORGANIZATION.CODE }, true);
    public static final UniqueKey<OrganizationRecord> ORGANIZATION_PKEY = Internal.createUniqueKey(OrganizationTable.ORGANIZATION, DSL.name("organization_pkey"), new TableField[] { OrganizationTable.ORGANIZATION.ID }, true);
    public static final UniqueKey<QrcodeRecordRecord> QRCODE_RECORD_CODE_KEY = Internal.createUniqueKey(QrcodeRecordTable.QRCODE_RECORD, DSL.name("qrcode_record_code_key"), new TableField[] { QrcodeRecordTable.QRCODE_RECORD.CODE }, true);
    public static final UniqueKey<QrcodeRecordRecord> QRCODE_RECORD_PKEY = Internal.createUniqueKey(QrcodeRecordTable.QRCODE_RECORD, DSL.name("qrcode_record_pkey"), new TableField[] { QrcodeRecordTable.QRCODE_RECORD.ID }, true);
    public static final UniqueKey<QrcodeScanRecordRecord> QRCODE_SCAN_RECORD_PKEY = Internal.createUniqueKey(QrcodeScanRecordTable.QRCODE_SCAN_RECORD, DSL.name("qrcode_scan_record_pkey"), new TableField[] { QrcodeScanRecordTable.QRCODE_SCAN_RECORD.ID }, true);
    public static final UniqueKey<SystemPreferenceRecord> SYSTEM_PREFERENCE_PKEY = Internal.createUniqueKey(SystemPreferenceTable.SYSTEM_PREFERENCE, DSL.name("system_preference_pkey"), new TableField[] { SystemPreferenceTable.SYSTEM_PREFERENCE.ID }, true);
    public static final UniqueKey<UserRecord> UK_PHONE = Internal.createUniqueKey(UserTable.USER, DSL.name("uk_phone"), new TableField[] { UserTable.USER.PHONE }, true);
    public static final UniqueKey<UserRecord> UK_USERNAME = Internal.createUniqueKey(UserTable.USER, DSL.name("uk_username"), new TableField[] { UserTable.USER.USERNAME }, true);
    public static final UniqueKey<UserRecord> USER_PKEY = Internal.createUniqueKey(UserTable.USER, DSL.name("user_pkey"), new TableField[] { UserTable.USER.ID }, true);
    public static final UniqueKey<UserCreditRecord> USER_CREDIT_PKEY = Internal.createUniqueKey(UserCreditTable.USER_CREDIT, DSL.name("user_credit_pkey"), new TableField[] { UserCreditTable.USER_CREDIT.ID }, true);
    public static final UniqueKey<UserPermissionRecord> USER_PERMISSION_PKEY = Internal.createUniqueKey(UserPermissionTable.USER_PERMISSION, DSL.name("user_permission_pkey"), new TableField[] { UserPermissionTable.USER_PERMISSION.ID }, true);
    public static final UniqueKey<UserPreferenceRecord> USER_PREFERENCE_PKEY = Internal.createUniqueKey(UserPreferenceTable.USER_PREFERENCE, DSL.name("user_preference_pkey"), new TableField[] { UserPreferenceTable.USER_PREFERENCE.ID }, true);
}
