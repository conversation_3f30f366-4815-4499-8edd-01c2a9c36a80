/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.Arrays;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 场景提示词
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationScenarioPrompt implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String categoryBizSource;
    private String categoryCode;
    private String categoryName;
    private String scenarioCode;
    private String scenarioName;
    private String description;
    private String prompt;
    private String status;
    private JSONB extInfo;
    private JSONB creator;
    private JSONB[] tags;

    public CreationScenarioPrompt() {}

    public CreationScenarioPrompt(CreationScenarioPrompt value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.categoryBizSource = value.categoryBizSource;
        this.categoryCode = value.categoryCode;
        this.categoryName = value.categoryName;
        this.scenarioCode = value.scenarioCode;
        this.scenarioName = value.scenarioName;
        this.description = value.description;
        this.prompt = value.prompt;
        this.status = value.status;
        this.extInfo = value.extInfo;
        this.creator = value.creator;
        this.tags = value.tags;
    }

    public CreationScenarioPrompt(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        String categoryBizSource,
        String categoryCode,
        String categoryName,
        String scenarioCode,
        String scenarioName,
        String description,
        String prompt,
        String status,
        @Nullable JSONB extInfo,
        JSONB creator,
        @Nullable JSONB[] tags
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.categoryBizSource = categoryBizSource;
        this.categoryCode = categoryCode;
        this.categoryName = categoryName;
        this.scenarioCode = scenarioCode;
        this.scenarioName = scenarioName;
        this.description = description;
        this.prompt = prompt;
        this.status = status;
        this.extInfo = extInfo;
        this.creator = creator;
        this.tags = tags;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.id</code>. 主键
     */
    public CreationScenarioPrompt setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.created_at</code>.
     * 创建时间
     */
    public CreationScenarioPrompt setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.updated_at</code>.
     * 更新时间
     */
    public CreationScenarioPrompt setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_scenario_prompt.category_biz_source</code>.
     * 业务来源MAGICAL_BRUSH/INTELLIJ_WRITER
     */
    public String getCategoryBizSource() {
        return this.categoryBizSource;
    }

    /**
     * Setter for
     * <code>creation.creation_scenario_prompt.category_biz_source</code>.
     * 业务来源MAGICAL_BRUSH/INTELLIJ_WRITER
     */
    public CreationScenarioPrompt setCategoryBizSource(String categoryBizSource) {
        this.categoryBizSource = categoryBizSource;
        return this;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.category_code</code>.
     * 分类码:E-COM/NEW-MEDIA
     */
    public String getCategoryCode() {
        return this.categoryCode;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.category_code</code>.
     * 分类码:E-COM/NEW-MEDIA
     */
    public CreationScenarioPrompt setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
        return this;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.category_name</code>.
     * 分类名:电商，新媒体
     */
    public String getCategoryName() {
        return this.categoryName;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.category_name</code>.
     * 分类名:电商，新媒体
     */
    public CreationScenarioPrompt setCategoryName(String categoryName) {
        this.categoryName = categoryName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.scenario_code</code>.
     * 场景码
     */
    public String getScenarioCode() {
        return this.scenarioCode;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.scenario_code</code>.
     * 场景码
     */
    public CreationScenarioPrompt setScenarioCode(String scenarioCode) {
        this.scenarioCode = scenarioCode;
        return this;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.scenario_name</code>.
     * 场景名字
     */
    public String getScenarioName() {
        return this.scenarioName;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.scenario_name</code>.
     * 场景名字
     */
    public CreationScenarioPrompt setScenarioName(String scenarioName) {
        this.scenarioName = scenarioName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.description</code>.
     * 场景描述
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.description</code>.
     * 场景描述
     */
    public CreationScenarioPrompt setDescription(String description) {
        this.description = description;
        return this;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.prompt</code>. 提示词
     */
    public String getPrompt() {
        return this.prompt;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.prompt</code>. 提示词
     */
    public CreationScenarioPrompt setPrompt(String prompt) {
        this.prompt = prompt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.status</code>. 提示词状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.status</code>. 提示词状态
     */
    public CreationScenarioPrompt setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.ext_info</code>. 扩展信息
     */
    @Nullable
    public JSONB getExtInfo() {
        return this.extInfo;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.ext_info</code>. 扩展信息
     */
    public CreationScenarioPrompt setExtInfo(@Nullable JSONB extInfo) {
        this.extInfo = extInfo;
        return this;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.creator</code>. 创建者信息
     */
    public JSONB getCreator() {
        return this.creator;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.creator</code>. 创建者信息
     */
    public CreationScenarioPrompt setCreator(JSONB creator) {
        this.creator = creator;
        return this;
    }

    /**
     * Getter for <code>creation.creation_scenario_prompt.tags</code>. 标签
     */
    @Nullable
    public JSONB[] getTags() {
        return this.tags;
    }

    /**
     * Setter for <code>creation.creation_scenario_prompt.tags</code>. 标签
     */
    public CreationScenarioPrompt setTags(@Nullable JSONB[] tags) {
        this.tags = tags;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationScenarioPrompt other = (CreationScenarioPrompt) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.categoryBizSource == null) {
            if (other.categoryBizSource != null)
                return false;
        }
        else if (!this.categoryBizSource.equals(other.categoryBizSource))
            return false;
        if (this.categoryCode == null) {
            if (other.categoryCode != null)
                return false;
        }
        else if (!this.categoryCode.equals(other.categoryCode))
            return false;
        if (this.categoryName == null) {
            if (other.categoryName != null)
                return false;
        }
        else if (!this.categoryName.equals(other.categoryName))
            return false;
        if (this.scenarioCode == null) {
            if (other.scenarioCode != null)
                return false;
        }
        else if (!this.scenarioCode.equals(other.scenarioCode))
            return false;
        if (this.scenarioName == null) {
            if (other.scenarioName != null)
                return false;
        }
        else if (!this.scenarioName.equals(other.scenarioName))
            return false;
        if (this.description == null) {
            if (other.description != null)
                return false;
        }
        else if (!this.description.equals(other.description))
            return false;
        if (this.prompt == null) {
            if (other.prompt != null)
                return false;
        }
        else if (!this.prompt.equals(other.prompt))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.extInfo == null) {
            if (other.extInfo != null)
                return false;
        }
        else if (!this.extInfo.equals(other.extInfo))
            return false;
        if (this.creator == null) {
            if (other.creator != null)
                return false;
        }
        else if (!this.creator.equals(other.creator))
            return false;
        if (this.tags == null) {
            if (other.tags != null)
                return false;
        }
        else if (!Arrays.deepEquals(this.tags, other.tags))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.categoryBizSource == null) ? 0 : this.categoryBizSource.hashCode());
        result = prime * result + ((this.categoryCode == null) ? 0 : this.categoryCode.hashCode());
        result = prime * result + ((this.categoryName == null) ? 0 : this.categoryName.hashCode());
        result = prime * result + ((this.scenarioCode == null) ? 0 : this.scenarioCode.hashCode());
        result = prime * result + ((this.scenarioName == null) ? 0 : this.scenarioName.hashCode());
        result = prime * result + ((this.description == null) ? 0 : this.description.hashCode());
        result = prime * result + ((this.prompt == null) ? 0 : this.prompt.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.extInfo == null) ? 0 : this.extInfo.hashCode());
        result = prime * result + ((this.creator == null) ? 0 : this.creator.hashCode());
        result = prime * result + ((this.tags == null) ? 0 : Arrays.deepHashCode(this.tags));
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationScenarioPrompt (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(categoryBizSource);
        sb.append(", ").append(categoryCode);
        sb.append(", ").append(categoryName);
        sb.append(", ").append(scenarioCode);
        sb.append(", ").append(scenarioName);
        sb.append(", ").append(description);
        sb.append(", ").append(prompt);
        sb.append(", ").append(status);
        sb.append(", ").append(extInfo);
        sb.append(", ").append(creator);
        sb.append(", ").append(Arrays.deepToString(tags));

        sb.append(")");
        return sb.toString();
    }
}
