/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.QrcodeRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.QrcodeRecord;
import ai.creatly.sky.creation.infra.dal.tables.records.QrcodeRecordRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.JSONB;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 权益中心-码业务平台
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class QrcodeRecordDAO extends DAOImpl<QrcodeRecordRecord, QrcodeRecord, Long> {

    /**
     * Create a new QrcodeRecordDAO without any configuration
     */
    public QrcodeRecordDAO() {
        super(QrcodeRecordTable.QRCODE_RECORD, QrcodeRecord.class);
    }

    /**
     * Create a new QrcodeRecordDAO with an attached configuration
     */
    @Autowired
    public QrcodeRecordDAO(Configuration configuration) {
        super(QrcodeRecordTable.QRCODE_RECORD, QrcodeRecord.class, configuration);
    }

    @Override
    public Long getId(QrcodeRecord object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeRecord> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(QrcodeRecordTable.QRCODE_RECORD.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<QrcodeRecord> fetchById(Long... values) {
        return fetch(QrcodeRecordTable.QRCODE_RECORD.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public QrcodeRecord fetchOneById(Long value) {
        return fetchOne(QrcodeRecordTable.QRCODE_RECORD.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<QrcodeRecord> fetchOptionalById(Long value) {
        return fetchOptional(QrcodeRecordTable.QRCODE_RECORD.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeRecord> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(QrcodeRecordTable.QRCODE_RECORD.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<QrcodeRecord> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(QrcodeRecordTable.QRCODE_RECORD.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeRecord> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(QrcodeRecordTable.QRCODE_RECORD.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<QrcodeRecord> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(QrcodeRecordTable.QRCODE_RECORD.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>issuer_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeRecord> fetchRangeOfIssuerId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(QrcodeRecordTable.QRCODE_RECORD.ISSUER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>issuer_id IN (values)</code>
     */
    public List<QrcodeRecord> fetchByIssuerId(Long... values) {
        return fetch(QrcodeRecordTable.QRCODE_RECORD.ISSUER_ID, values);
    }

    /**
     * Fetch records that have <code>biz_scene BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeRecord> fetchRangeOfBizScene(String lowerInclusive, String upperInclusive) {
        return fetchRange(QrcodeRecordTable.QRCODE_RECORD.BIZ_SCENE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_scene IN (values)</code>
     */
    public List<QrcodeRecord> fetchByBizScene(String... values) {
        return fetch(QrcodeRecordTable.QRCODE_RECORD.BIZ_SCENE, values);
    }

    /**
     * Fetch records that have <code>code_content BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeRecord> fetchRangeOfCodeContent(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(QrcodeRecordTable.QRCODE_RECORD.CODE_CONTENT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>code_content IN (values)</code>
     */
    public List<QrcodeRecord> fetchByCodeContent(JSONB... values) {
        return fetch(QrcodeRecordTable.QRCODE_RECORD.CODE_CONTENT, values);
    }

    /**
     * Fetch records that have <code>code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeRecord> fetchRangeOfCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(QrcodeRecordTable.QRCODE_RECORD.CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>code IN (values)</code>
     */
    public List<QrcodeRecord> fetchByCode(String... values) {
        return fetch(QrcodeRecordTable.QRCODE_RECORD.CODE, values);
    }

    /**
     * Fetch a unique record that has <code>code = value</code>
     */
    @Nullable
    public QrcodeRecord fetchOneByCode(String value) {
        return fetchOne(QrcodeRecordTable.QRCODE_RECORD.CODE, value);
    }

    /**
     * Fetch a unique record that has <code>code = value</code>
     */
    public Optional<QrcodeRecord> fetchOptionalByCode(String value) {
        return fetchOptional(QrcodeRecordTable.QRCODE_RECORD.CODE, value);
    }

    /**
     * Fetch records that have <code>expire_time BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeRecord> fetchRangeOfExpireTime(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(QrcodeRecordTable.QRCODE_RECORD.EXPIRE_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>expire_time IN (values)</code>
     */
    public List<QrcodeRecord> fetchByExpireTime(ZonedDateTime... values) {
        return fetch(QrcodeRecordTable.QRCODE_RECORD.EXPIRE_TIME, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeRecord> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(QrcodeRecordTable.QRCODE_RECORD.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<QrcodeRecord> fetchByStatus(String... values) {
        return fetch(QrcodeRecordTable.QRCODE_RECORD.STATUS, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeRecord> fetchRangeOfType(String lowerInclusive, String upperInclusive) {
        return fetchRange(QrcodeRecordTable.QRCODE_RECORD.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<QrcodeRecord> fetchByType(String... values) {
        return fetch(QrcodeRecordTable.QRCODE_RECORD.TYPE, values);
    }

    /**
     * Fetch records that have <code>max_threshold BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<QrcodeRecord> fetchRangeOfMaxThreshold(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(QrcodeRecordTable.QRCODE_RECORD.MAX_THRESHOLD, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>max_threshold IN (values)</code>
     */
    public List<QrcodeRecord> fetchByMaxThreshold(Integer... values) {
        return fetch(QrcodeRecordTable.QRCODE_RECORD.MAX_THRESHOLD, values);
    }
}
