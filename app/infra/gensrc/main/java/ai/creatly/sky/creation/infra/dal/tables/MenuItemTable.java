/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.MenuItemRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 菜单项
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class MenuItemTable extends TableImpl<MenuItemRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_menu_item</code>
     */
    public static final MenuItemTable CREATION_MENU_ITEM = new MenuItemTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MenuItemRecord> getRecordType() {
        return MenuItemRecord.class;
    }

    /**
     * The column <code>creation.creation_menu_item.id</code>. 主键
     */
    public final TableField<MenuItemRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键");

    /**
     * The column <code>creation.creation_menu_item.created_at</code>.
     */
    public final TableField<MenuItemRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_menu_item.updated_at</code>.
     */
    public final TableField<MenuItemRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_menu_item.menu_id</code>. 所属菜单
     */
    public final TableField<MenuItemRecord, Long> MENU_ID = createField(DSL.name("menu_id"), SQLDataType.BIGINT.nullable(false), this, "所属菜单");

    /**
     * The column <code>creation.creation_menu_item.code</code>. 唯一标识
     */
    public final TableField<MenuItemRecord, String> CODE = createField(DSL.name("code"), SQLDataType.VARCHAR(64).nullable(false), this, "唯一标识");

    /**
     * The column <code>creation.creation_menu_item.label</code>. 显示名称
     */
    public final TableField<MenuItemRecord, String> LABEL = createField(DSL.name("label"), SQLDataType.VARCHAR(64).nullable(false), this, "显示名称");

    /**
     * The column <code>creation.creation_menu_item.icon</code>. 图标
     */
    public final TableField<MenuItemRecord, String> ICON = createField(DSL.name("icon"), SQLDataType.VARCHAR(128).nullable(false), this, "图标");

    /**
     * The column <code>creation.creation_menu_item.path</code>. 路径（以/开头）
     */
    public final TableField<MenuItemRecord, String> PATH = createField(DSL.name("path"), SQLDataType.VARCHAR(128).nullable(false), this, "路径（以/开头）");

    /**
     * The column <code>creation.creation_menu_item.parent_code</code>. 父标识
     */
    public final TableField<MenuItemRecord, String> PARENT_CODE = createField(DSL.name("parent_code"), SQLDataType.VARCHAR(64), this, "父标识");

    /**
     * The column <code>creation.creation_menu_item.status</code>.
     */
    public final TableField<MenuItemRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(16).nullable(false), this, "");

    /**
     * The column <code>creation.creation_menu_item.index</code>.
     */
    public final TableField<MenuItemRecord, Integer> INDEX = createField(DSL.name("index"), SQLDataType.INTEGER.defaultValue(DSL.field(DSL.raw("999"), SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>creation.creation_menu_item.item_tag</code>.
     */
    public final TableField<MenuItemRecord, String> ITEM_TAG = createField(DSL.name("item_tag"), SQLDataType.VARCHAR(64).defaultValue(DSL.field(DSL.raw("'formal'::character varying"), SQLDataType.VARCHAR)), this, "");

    private MenuItemTable(Name alias, Table<MenuItemRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private MenuItemTable(Name alias, Table<MenuItemRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("菜单项"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_menu_item</code> table
     * reference
     */
    public MenuItemTable(String alias) {
        this(DSL.name(alias), CREATION_MENU_ITEM);
    }

    /**
     * Create an aliased <code>creation.creation_menu_item</code> table
     * reference
     */
    public MenuItemTable(Name alias) {
        this(alias, CREATION_MENU_ITEM);
    }

    /**
     * Create a <code>creation.creation_menu_item</code> table reference
     */
    public MenuItemTable() {
        this(DSL.name("creation_menu_item"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public Identity<MenuItemRecord, Long> getIdentity() {
        return (Identity<MenuItemRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<MenuItemRecord> getPrimaryKey() {
        return Keys.CREATION_MENU_ITEM_PKEY;
    }

    @Override
    public List<UniqueKey<MenuItemRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CREATION_MENU_ITEM_MENU_ID_CODE_KEY);
    }

    @Override
    public MenuItemTable as(String alias) {
        return new MenuItemTable(DSL.name(alias), this);
    }

    @Override
    public MenuItemTable as(Name alias) {
        return new MenuItemTable(alias, this);
    }

    @Override
    public MenuItemTable as(Table<?> alias) {
        return new MenuItemTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public MenuItemTable rename(String name) {
        return new MenuItemTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public MenuItemTable rename(Name name) {
        return new MenuItemTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public MenuItemTable rename(Table<?> name) {
        return new MenuItemTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public MenuItemTable where(Condition condition) {
        return new MenuItemTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public MenuItemTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public MenuItemTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public MenuItemTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public MenuItemTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public MenuItemTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public MenuItemTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public MenuItemTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public MenuItemTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public MenuItemTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
