/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 机构学员课程开课记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseOrgUser implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long courseId;
    private ZonedDateTime beginTime;
    private ZonedDateTime endTime;
    private String className;
    private Long uid;
    private String status;
    private String orgCode;

    public CourseOrgUser() {}

    public CourseOrgUser(CourseOrgUser value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.courseId = value.courseId;
        this.beginTime = value.beginTime;
        this.endTime = value.endTime;
        this.className = value.className;
        this.uid = value.uid;
        this.status = value.status;
        this.orgCode = value.orgCode;
    }

    public CourseOrgUser(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        @Nullable Long courseId,
        @Nullable ZonedDateTime beginTime,
        @Nullable ZonedDateTime endTime,
        @Nullable String className,
        @Nullable Long uid,
        @Nullable String status,
        @Nullable String orgCode
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.courseId = courseId;
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.className = className;
        this.uid = uid;
        this.status = status;
        this.orgCode = orgCode;
    }

    /**
     * Getter for <code>creation.course_org_user.id</code>. 学员课程记录id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.course_org_user.id</code>. 学员课程记录id
     */
    public CourseOrgUser setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.course_org_user.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.course_org_user.created_at</code>. 创建时间
     */
    public CourseOrgUser setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.course_org_user.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.course_org_user.updated_at</code>. 更新时间
     */
    public CourseOrgUser setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.course_org_user.course_id</code>. 课程ID
     */
    @Nullable
    public Long getCourseId() {
        return this.courseId;
    }

    /**
     * Setter for <code>creation.course_org_user.course_id</code>. 课程ID
     */
    public CourseOrgUser setCourseId(@Nullable Long courseId) {
        this.courseId = courseId;
        return this;
    }

    /**
     * Getter for <code>creation.course_org_user.begin_time</code>. 开始时间
     */
    @Nullable
    public ZonedDateTime getBeginTime() {
        return this.beginTime;
    }

    /**
     * Setter for <code>creation.course_org_user.begin_time</code>. 开始时间
     */
    public CourseOrgUser setBeginTime(@Nullable ZonedDateTime beginTime) {
        this.beginTime = beginTime;
        return this;
    }

    /**
     * Getter for <code>creation.course_org_user.end_time</code>. 有效日期
     */
    @Nullable
    public ZonedDateTime getEndTime() {
        return this.endTime;
    }

    /**
     * Setter for <code>creation.course_org_user.end_time</code>. 有效日期
     */
    public CourseOrgUser setEndTime(@Nullable ZonedDateTime endTime) {
        this.endTime = endTime;
        return this;
    }

    /**
     * Getter for <code>creation.course_org_user.class_name</code>. 班级名称
     */
    @Nullable
    public String getClassName() {
        return this.className;
    }

    /**
     * Setter for <code>creation.course_org_user.class_name</code>. 班级名称
     */
    public CourseOrgUser setClassName(@Nullable String className) {
        this.className = className;
        return this;
    }

    /**
     * Getter for <code>creation.course_org_user.uid</code>. 用户id
     */
    @Nullable
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.course_org_user.uid</code>. 用户id
     */
    public CourseOrgUser setUid(@Nullable Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.course_org_user.status</code>. 开课状态
     */
    @Nullable
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.course_org_user.status</code>. 开课状态
     */
    public CourseOrgUser setStatus(@Nullable String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.course_org_user.org_code</code>. 组织机构
     */
    @Nullable
    public String getOrgCode() {
        return this.orgCode;
    }

    /**
     * Setter for <code>creation.course_org_user.org_code</code>. 组织机构
     */
    public CourseOrgUser setOrgCode(@Nullable String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CourseOrgUser other = (CourseOrgUser) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.courseId == null) {
            if (other.courseId != null)
                return false;
        }
        else if (!this.courseId.equals(other.courseId))
            return false;
        if (this.beginTime == null) {
            if (other.beginTime != null)
                return false;
        }
        else if (!this.beginTime.equals(other.beginTime))
            return false;
        if (this.endTime == null) {
            if (other.endTime != null)
                return false;
        }
        else if (!this.endTime.equals(other.endTime))
            return false;
        if (this.className == null) {
            if (other.className != null)
                return false;
        }
        else if (!this.className.equals(other.className))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.orgCode == null) {
            if (other.orgCode != null)
                return false;
        }
        else if (!this.orgCode.equals(other.orgCode))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.courseId == null) ? 0 : this.courseId.hashCode());
        result = prime * result + ((this.beginTime == null) ? 0 : this.beginTime.hashCode());
        result = prime * result + ((this.endTime == null) ? 0 : this.endTime.hashCode());
        result = prime * result + ((this.className == null) ? 0 : this.className.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.orgCode == null) ? 0 : this.orgCode.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseOrgUser (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(courseId);
        sb.append(", ").append(beginTime);
        sb.append(", ").append(endTime);
        sb.append(", ").append(className);
        sb.append(", ").append(uid);
        sb.append(", ").append(status);
        sb.append(", ").append(orgCode);

        sb.append(")");
        return sb.toString();
    }
}
