/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.UserPreferenceRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 用户配置中心
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserPreferenceTable extends TableImpl<UserPreferenceRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.user_preference</code>
     */
    public static final UserPreferenceTable USER_PREFERENCE = new UserPreferenceTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserPreferenceRecord> getRecordType() {
        return UserPreferenceRecord.class;
    }

    /**
     * The column <code>creation.user_preference.id</code>. 主键
     */
    public final TableField<UserPreferenceRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.user_preference.created_at</code>. 创建时间
     */
    public final TableField<UserPreferenceRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.user_preference.updated_at</code>. 更新时间
     */
    public final TableField<UserPreferenceRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.user_preference.uid</code>. 用户id
     */
    public final TableField<UserPreferenceRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户id");

    /**
     * The column <code>creation.user_preference.config_key</code>. 配置项
     */
    public final TableField<UserPreferenceRecord, String> CONFIG_KEY = createField(DSL.name("config_key"), SQLDataType.VARCHAR(255).nullable(false), this, "配置项");

    /**
     * The column <code>creation.user_preference.config_value</code>. 配置值
     */
    public final TableField<UserPreferenceRecord, String> CONFIG_VALUE = createField(DSL.name("config_value"), SQLDataType.VARCHAR(255).nullable(false), this, "配置值");

    /**
     * The column <code>creation.user_preference.operator</code>. 操作人
     */
    public final TableField<UserPreferenceRecord, String> OPERATOR = createField(DSL.name("operator"), SQLDataType.VARCHAR(64).nullable(false), this, "操作人");

    /**
     * The column <code>creation.user_preference.status</code>. 状态 VALID/INVALID
     */
    public final TableField<UserPreferenceRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(32).nullable(false), this, "状态 VALID/INVALID");

    private UserPreferenceTable(Name alias, Table<UserPreferenceRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private UserPreferenceTable(Name alias, Table<UserPreferenceRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("用户配置中心"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.user_preference</code> table reference
     */
    public UserPreferenceTable(String alias) {
        this(DSL.name(alias), USER_PREFERENCE);
    }

    /**
     * Create an aliased <code>creation.user_preference</code> table reference
     */
    public UserPreferenceTable(Name alias) {
        this(alias, USER_PREFERENCE);
    }

    /**
     * Create a <code>creation.user_preference</code> table reference
     */
    public UserPreferenceTable() {
        this(DSL.name("user_preference"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<UserPreferenceRecord> getPrimaryKey() {
        return Keys.USER_PREFERENCE_PKEY;
    }

    @Override
    public UserPreferenceTable as(String alias) {
        return new UserPreferenceTable(DSL.name(alias), this);
    }

    @Override
    public UserPreferenceTable as(Name alias) {
        return new UserPreferenceTable(alias, this);
    }

    @Override
    public UserPreferenceTable as(Table<?> alias) {
        return new UserPreferenceTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserPreferenceTable rename(String name) {
        return new UserPreferenceTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserPreferenceTable rename(Name name) {
        return new UserPreferenceTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserPreferenceTable rename(Table<?> name) {
        return new UserPreferenceTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPreferenceTable where(Condition condition) {
        return new UserPreferenceTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPreferenceTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPreferenceTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPreferenceTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserPreferenceTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserPreferenceTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserPreferenceTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserPreferenceTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPreferenceTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserPreferenceTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
