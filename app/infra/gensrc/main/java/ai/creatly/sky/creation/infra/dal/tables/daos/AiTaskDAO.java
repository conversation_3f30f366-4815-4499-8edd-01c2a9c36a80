/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.AiTaskTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiTask;
import ai.creatly.sky.creation.infra.dal.tables.records.AiTaskRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.JSONB;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * AI创作任务
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class AiTaskDAO extends DAOImpl<AiTaskRecord, CreationAiTask, Long> {

    /**
     * Create a new AiTaskDAO without any configuration
     */
    public AiTaskDAO() {
        super(AiTaskTable.CREATION_AI_TASK, CreationAiTask.class);
    }

    /**
     * Create a new AiTaskDAO with an attached configuration
     */
    @Autowired
    public AiTaskDAO(Configuration configuration) {
        super(AiTaskTable.CREATION_AI_TASK, CreationAiTask.class, configuration);
    }

    @Override
    public Long getId(CreationAiTask object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationAiTask> fetchById(Long... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationAiTask fetchOneById(Long value) {
        return fetchOne(AiTaskTable.CREATION_AI_TASK.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationAiTask> fetchOptionalById(Long value) {
        return fetchOptional(AiTaskTable.CREATION_AI_TASK.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationAiTask> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationAiTask> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>owner_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfOwnerId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.OWNER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>owner_id IN (values)</code>
     */
    public List<CreationAiTask> fetchByOwnerId(Long... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.OWNER_ID, values);
    }

    /**
     * Fetch records that have <code>owner_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfOwnerName(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.OWNER_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>owner_name IN (values)</code>
     */
    public List<CreationAiTask> fetchByOwnerName(String... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.OWNER_NAME, values);
    }

    /**
     * Fetch records that have <code>task_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfTaskType(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.TASK_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>task_type IN (values)</code>
     */
    public List<CreationAiTask> fetchByTaskType(String... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.TASK_TYPE, values);
    }

    /**
     * Fetch records that have <code>task_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfTaskName(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.TASK_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>task_name IN (values)</code>
     */
    public List<CreationAiTask> fetchByTaskName(String... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.TASK_NAME, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CreationAiTask> fetchByStatus(String... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.STATUS, values);
    }

    /**
     * Fetch records that have <code>biz_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfBizStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.BIZ_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_status IN (values)</code>
     */
    public List<CreationAiTask> fetchByBizStatus(String... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.BIZ_STATUS, values);
    }

    /**
     * Fetch records that have <code>biz_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfBizType(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.BIZ_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_type IN (values)</code>
     */
    public List<CreationAiTask> fetchByBizType(String... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.BIZ_TYPE, values);
    }

    /**
     * Fetch records that have <code>biz_no BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfBizNo(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.BIZ_NO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_no IN (values)</code>
     */
    public List<CreationAiTask> fetchByBizNo(String... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.BIZ_NO, values);
    }

    /**
     * Fetch records that have <code>sub_biz_no BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfSubBizNo(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.SUB_BIZ_NO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>sub_biz_no IN (values)</code>
     */
    public List<CreationAiTask> fetchBySubBizNo(String... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.SUB_BIZ_NO, values);
    }

    /**
     * Fetch records that have <code>exec_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfExecStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.EXEC_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>exec_status IN (values)</code>
     */
    public List<CreationAiTask> fetchByExecStatus(String... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.EXEC_STATUS, values);
    }

    /**
     * Fetch records that have <code>auto_exec BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfAutoExec(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.AUTO_EXEC, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>auto_exec IN (values)</code>
     */
    public List<CreationAiTask> fetchByAutoExec(Boolean... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.AUTO_EXEC, values);
    }

    /**
     * Fetch records that have <code>priority BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfPriority(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.PRIORITY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>priority IN (values)</code>
     */
    public List<CreationAiTask> fetchByPriority(Integer... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.PRIORITY, values);
    }

    /**
     * Fetch records that have <code>sharding BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfSharding(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.SHARDING, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>sharding IN (values)</code>
     */
    public List<CreationAiTask> fetchBySharding(Integer... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.SHARDING, values);
    }

    /**
     * Fetch records that have <code>started_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfStartedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.STARTED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>started_at IN (values)</code>
     */
    public List<CreationAiTask> fetchByStartedAt(ZonedDateTime... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.STARTED_AT, values);
    }

    /**
     * Fetch records that have <code>finished_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfFinishedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.FINISHED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>finished_at IN (values)</code>
     */
    public List<CreationAiTask> fetchByFinishedAt(ZonedDateTime... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.FINISHED_AT, values);
    }

    /**
     * Fetch records that have <code>shared_users BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfSharedUsers(JSONB[] lowerInclusive, JSONB[] upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.SHARED_USERS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>shared_users IN (values)</code>
     */
    public List<CreationAiTask> fetchBySharedUsers(JSONB[]... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.SHARED_USERS, values);
    }

    /**
     * Fetch records that have <code>biz_params BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfBizParams(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.BIZ_PARAMS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_params IN (values)</code>
     */
    public List<CreationAiTask> fetchByBizParams(JSONB... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.BIZ_PARAMS, values);
    }

    /**
     * Fetch records that have <code>biz_result BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfBizResult(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.BIZ_RESULT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_result IN (values)</code>
     */
    public List<CreationAiTask> fetchByBizResult(JSONB... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.BIZ_RESULT, values);
    }

    /**
     * Fetch records that have <code>sys_params BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfSysParams(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.SYS_PARAMS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>sys_params IN (values)</code>
     */
    public List<CreationAiTask> fetchBySysParams(JSONB... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.SYS_PARAMS, values);
    }

    /**
     * Fetch records that have <code>sys_result BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfSysResult(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.SYS_RESULT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>sys_result IN (values)</code>
     */
    public List<CreationAiTask> fetchBySysResult(JSONB... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.SYS_RESULT, values);
    }

    /**
     * Fetch records that have <code>exec_info BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfExecInfo(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.EXEC_INFO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>exec_info IN (values)</code>
     */
    public List<CreationAiTask> fetchByExecInfo(JSONB... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.EXEC_INFO, values);
    }

    /**
     * Fetch records that have <code>creator BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfCreator(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.CREATOR, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creator IN (values)</code>
     */
    public List<CreationAiTask> fetchByCreator(JSONB... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.CREATOR, values);
    }

    /**
     * Fetch records that have <code>biz_exec_info BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfBizExecInfo(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.BIZ_EXEC_INFO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>biz_exec_info IN (values)</code>
     */
    public List<CreationAiTask> fetchByBizExecInfo(JSONB... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.BIZ_EXEC_INFO, values);
    }

    /**
     * Fetch records that have <code>delayed_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfDelayedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.DELAYED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>delayed_at IN (values)</code>
     */
    public List<CreationAiTask> fetchByDelayedAt(ZonedDateTime... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.DELAYED_AT, values);
    }

    /**
     * Fetch records that have <code>org_code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationAiTask> fetchRangeOfOrgCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(AiTaskTable.CREATION_AI_TASK.ORG_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_code IN (values)</code>
     */
    public List<CreationAiTask> fetchByOrgCode(String... values) {
        return fetch(AiTaskTable.CREATION_AI_TASK.ORG_CODE, values);
    }
}
