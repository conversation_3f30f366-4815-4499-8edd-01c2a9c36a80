/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.UserTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.User;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserRecord extends UpdatableRecordImpl<UserRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.user.id</code>. 主键
     */
    public UserRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.user.created_at</code>. 创建时间
     */
    public UserRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.user.updated_at</code>. 更新时间
     */
    public UserRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.user.username</code>. 用户名
     */
    public UserRecord setUsername(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.username</code>. 用户名
     */
    public String getUsername() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.user.nickname</code>. 昵称
     */
    public UserRecord setNickname(@Nullable String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.nickname</code>. 昵称
     */
    @Nullable
    public String getNickname() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.user.password</code>. 密码
     */
    public UserRecord setPassword(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.password</code>. 密码
     */
    public String getPassword() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.user.email</code>. 邮箱
     */
    public UserRecord setEmail(@Nullable String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.email</code>. 邮箱
     */
    @Nullable
    public String getEmail() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.user.phone</code>. 用户手机好
     */
    public UserRecord setPhone(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.phone</code>. 用户手机好
     */
    public String getPhone() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.user.avatar</code>. 用户头像
     */
    public UserRecord setAvatar(@Nullable String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.avatar</code>. 用户头像
     */
    @Nullable
    public String getAvatar() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.user.status</code>. 用户状态
     */
    public UserRecord setStatus(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.status</code>. 用户状态
     */
    public String getStatus() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.user.organizations</code>. 组织信息
     */
    public UserRecord setOrganizations(@Nullable JSONB value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.organizations</code>. 组织信息
     */
    @Nullable
    public JSONB getOrganizations() {
        return (JSONB) get(10);
    }

    /**
     * Setter for <code>creation.user.invite_code</code>. 专属邀请码
     */
    public UserRecord setInviteCode(@Nullable String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.invite_code</code>. 专属邀请码
     */
    @Nullable
    public String getInviteCode() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.user.wechat_union_id</code>. 微信统一用户标识
     */
    public UserRecord setWechatUnionId(@Nullable String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.wechat_union_id</code>. 微信统一用户标识
     */
    @Nullable
    public String getWechatUnionId() {
        return (String) get(12);
    }

    /**
     * Setter for <code>creation.user.wechat_open_ids</code>. 微信多应用端用户标识
     */
    public UserRecord setWechatOpenIds(@Nullable JSONB value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.wechat_open_ids</code>. 微信多应用端用户标识
     */
    @Nullable
    public JSONB getWechatOpenIds() {
        return (JSONB) get(13);
    }

    /**
     * Setter for <code>creation.user.roles</code>. 用户角色列表
     */
    public UserRecord setRoles(@Nullable JSONB value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.roles</code>. 用户角色列表
     */
    @Nullable
    public JSONB getRoles() {
        return (JSONB) get(14);
    }

    /**
     * Setter for <code>creation.user.last_org_code</code>. 最后一次登录的组织
     */
    public UserRecord setLastOrgCode(@Nullable String value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.last_org_code</code>. 最后一次登录的组织
     */
    @Nullable
    public String getLastOrgCode() {
        return (String) get(15);
    }

    /**
     * Setter for <code>creation.user.full_name</code>. 姓名
     */
    public UserRecord setFullName(@Nullable String value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.full_name</code>. 姓名
     */
    @Nullable
    public String getFullName() {
        return (String) get(16);
    }

    /**
     * Setter for <code>creation.user.id_card</code>. 身份证
     */
    public UserRecord setIdCard(@Nullable String value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>creation.user.id_card</code>. 身份证
     */
    @Nullable
    public String getIdCard() {
        return (String) get(17);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserRecord
     */
    public UserRecord() {
        super(UserTable.USER);
    }

    /**
     * Create a detached, initialised UserRecord
     */
    public UserRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, String username, @Nullable String nickname, String password, @Nullable String email, String phone, @Nullable String avatar, String status, @Nullable JSONB organizations, @Nullable String inviteCode, @Nullable String wechatUnionId, @Nullable JSONB wechatOpenIds, @Nullable JSONB roles, @Nullable String lastOrgCode, @Nullable String fullName, @Nullable String idCard) {
        super(UserTable.USER);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUsername(username);
        setNickname(nickname);
        setPassword(password);
        setEmail(email);
        setPhone(phone);
        setAvatar(avatar);
        setStatus(status);
        setOrganizations(organizations);
        setInviteCode(inviteCode);
        setWechatUnionId(wechatUnionId);
        setWechatOpenIds(wechatOpenIds);
        setRoles(roles);
        setLastOrgCode(lastOrgCode);
        setFullName(fullName);
        setIdCard(idCard);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserRecord
     */
    public UserRecord(User value) {
        super(UserTable.USER);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUsername(value.getUsername());
            setNickname(value.getNickname());
            setPassword(value.getPassword());
            setEmail(value.getEmail());
            setPhone(value.getPhone());
            setAvatar(value.getAvatar());
            setStatus(value.getStatus());
            setOrganizations(value.getOrganizations());
            setInviteCode(value.getInviteCode());
            setWechatUnionId(value.getWechatUnionId());
            setWechatOpenIds(value.getWechatOpenIds());
            setRoles(value.getRoles());
            setLastOrgCode(value.getLastOrgCode());
            setFullName(value.getFullName());
            setIdCard(value.getIdCard());
            resetChangedOnNotNull();
        }
    }
}
