/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 权益中心-用户扫码记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class QrcodeScanRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long issuingParty;
    private Long scanningParty;
    private String code;
    private String type;

    public QrcodeScanRecord() {}

    public QrcodeScanRecord(QrcodeScanRecord value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.issuingParty = value.issuingParty;
        this.scanningParty = value.scanningParty;
        this.code = value.code;
        this.type = value.type;
    }

    public QrcodeScanRecord(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long issuingParty,
        Long scanningParty,
        String code,
        String type
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.issuingParty = issuingParty;
        this.scanningParty = scanningParty;
        this.code = code;
        this.type = type;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.id</code>. 主键
     */
    public QrcodeScanRecord setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.created_at</code>. 创建时间
     */
    public QrcodeScanRecord setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.updated_at</code>. 更新时间
     */
    public QrcodeScanRecord setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.issuing_party</code>. 发码方
     */
    public Long getIssuingParty() {
        return this.issuingParty;
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.issuing_party</code>. 发码方
     */
    public QrcodeScanRecord setIssuingParty(Long issuingParty) {
        this.issuingParty = issuingParty;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.scanning_party</code>. 扫码人
     */
    public Long getScanningParty() {
        return this.scanningParty;
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.scanning_party</code>. 扫码人
     */
    public QrcodeScanRecord setScanningParty(Long scanningParty) {
        this.scanningParty = scanningParty;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.code</code>. 码值
     */
    public String getCode() {
        return this.code;
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.code</code>. 码值
     */
    public QrcodeScanRecord setCode(String code) {
        this.code = code;
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_scan_record.type</code>. 二维码
     */
    public String getType() {
        return this.type;
    }

    /**
     * Setter for <code>creation.qrcode_scan_record.type</code>. 二维码
     */
    public QrcodeScanRecord setType(String type) {
        this.type = type;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final QrcodeScanRecord other = (QrcodeScanRecord) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.issuingParty == null) {
            if (other.issuingParty != null)
                return false;
        }
        else if (!this.issuingParty.equals(other.issuingParty))
            return false;
        if (this.scanningParty == null) {
            if (other.scanningParty != null)
                return false;
        }
        else if (!this.scanningParty.equals(other.scanningParty))
            return false;
        if (this.code == null) {
            if (other.code != null)
                return false;
        }
        else if (!this.code.equals(other.code))
            return false;
        if (this.type == null) {
            if (other.type != null)
                return false;
        }
        else if (!this.type.equals(other.type))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.issuingParty == null) ? 0 : this.issuingParty.hashCode());
        result = prime * result + ((this.scanningParty == null) ? 0 : this.scanningParty.hashCode());
        result = prime * result + ((this.code == null) ? 0 : this.code.hashCode());
        result = prime * result + ((this.type == null) ? 0 : this.type.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("QrcodeScanRecord (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(issuingParty);
        sb.append(", ").append(scanningParty);
        sb.append(", ").append(code);
        sb.append(", ").append(type);

        sb.append(")");
        return sb.toString();
    }
}
