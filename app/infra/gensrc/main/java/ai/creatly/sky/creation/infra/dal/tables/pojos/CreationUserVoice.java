/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 声音演员
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationUserVoice implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String code;
    private String providerType;
    private String category;
    private String status;
    private String enName;
    private String cnName;
    private String gender;
    private String avatar;
    private Integer ordinal;
    private String ageLevel;
    private JSONB labels;

    public CreationUserVoice() {}

    public CreationUserVoice(CreationUserVoice value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.code = value.code;
        this.providerType = value.providerType;
        this.category = value.category;
        this.status = value.status;
        this.enName = value.enName;
        this.cnName = value.cnName;
        this.gender = value.gender;
        this.avatar = value.avatar;
        this.ordinal = value.ordinal;
        this.ageLevel = value.ageLevel;
        this.labels = value.labels;
    }

    public CreationUserVoice(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String code,
        String providerType,
        String category,
        String status,
        String enName,
        String cnName,
        String gender,
        String avatar,
        @Nullable Integer ordinal,
        @Nullable String ageLevel,
        @Nullable JSONB labels
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.code = code;
        this.providerType = providerType;
        this.category = category;
        this.status = status;
        this.enName = enName;
        this.cnName = cnName;
        this.gender = gender;
        this.avatar = avatar;
        this.ordinal = ordinal;
        this.ageLevel = ageLevel;
        this.labels = labels;
    }

    /**
     * Getter for <code>creation.creation_user_voice.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_user_voice.id</code>. 主键
     */
    public CreationUserVoice setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_user_voice.created_at</code>. 创建时间
     */
    public CreationUserVoice setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_user_voice.updated_at</code>. 更新时间
     */
    public CreationUserVoice setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.uid</code>. 用户ID
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_user_voice.uid</code>. 用户ID
     */
    public CreationUserVoice setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.code</code>. 声音编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * Setter for <code>creation.creation_user_voice.code</code>. 声音编号
     */
    public CreationUserVoice setCode(String code) {
        this.code = code;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.provider_type</code>. 声音提供方
     */
    public String getProviderType() {
        return this.providerType;
    }

    /**
     * Setter for <code>creation.creation_user_voice.provider_type</code>. 声音提供方
     */
    public CreationUserVoice setProviderType(String providerType) {
        this.providerType = providerType;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.category</code>. 分类
     */
    public String getCategory() {
        return this.category;
    }

    /**
     * Setter for <code>creation.creation_user_voice.category</code>. 分类
     */
    public CreationUserVoice setCategory(String category) {
        this.category = category;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.status</code>. 状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_user_voice.status</code>. 状态
     */
    public CreationUserVoice setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.en_name</code>. 英文名称
     */
    public String getEnName() {
        return this.enName;
    }

    /**
     * Setter for <code>creation.creation_user_voice.en_name</code>. 英文名称
     */
    public CreationUserVoice setEnName(String enName) {
        this.enName = enName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.cn_name</code>. 中文名称
     */
    public String getCnName() {
        return this.cnName;
    }

    /**
     * Setter for <code>creation.creation_user_voice.cn_name</code>. 中文名称
     */
    public CreationUserVoice setCnName(String cnName) {
        this.cnName = cnName;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.gender</code>. 性别
     */
    public String getGender() {
        return this.gender;
    }

    /**
     * Setter for <code>creation.creation_user_voice.gender</code>. 性别
     */
    public CreationUserVoice setGender(String gender) {
        this.gender = gender;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.avatar</code>. 头像
     */
    public String getAvatar() {
        return this.avatar;
    }

    /**
     * Setter for <code>creation.creation_user_voice.avatar</code>. 头像
     */
    public CreationUserVoice setAvatar(String avatar) {
        this.avatar = avatar;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.ordinal</code>. 排序字段
     */
    @Nullable
    public Integer getOrdinal() {
        return this.ordinal;
    }

    /**
     * Setter for <code>creation.creation_user_voice.ordinal</code>. 排序字段
     */
    public CreationUserVoice setOrdinal(@Nullable Integer ordinal) {
        this.ordinal = ordinal;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.age_level</code>. 声音年龄阶层
     */
    @Nullable
    public String getAgeLevel() {
        return this.ageLevel;
    }

    /**
     * Setter for <code>creation.creation_user_voice.age_level</code>. 声音年龄阶层
     */
    public CreationUserVoice setAgeLevel(@Nullable String ageLevel) {
        this.ageLevel = ageLevel;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_voice.labels</code>. 声音标签列表
     */
    @Nullable
    public JSONB getLabels() {
        return this.labels;
    }

    /**
     * Setter for <code>creation.creation_user_voice.labels</code>. 声音标签列表
     */
    public CreationUserVoice setLabels(@Nullable JSONB labels) {
        this.labels = labels;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationUserVoice other = (CreationUserVoice) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.code == null) {
            if (other.code != null)
                return false;
        }
        else if (!this.code.equals(other.code))
            return false;
        if (this.providerType == null) {
            if (other.providerType != null)
                return false;
        }
        else if (!this.providerType.equals(other.providerType))
            return false;
        if (this.category == null) {
            if (other.category != null)
                return false;
        }
        else if (!this.category.equals(other.category))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.enName == null) {
            if (other.enName != null)
                return false;
        }
        else if (!this.enName.equals(other.enName))
            return false;
        if (this.cnName == null) {
            if (other.cnName != null)
                return false;
        }
        else if (!this.cnName.equals(other.cnName))
            return false;
        if (this.gender == null) {
            if (other.gender != null)
                return false;
        }
        else if (!this.gender.equals(other.gender))
            return false;
        if (this.avatar == null) {
            if (other.avatar != null)
                return false;
        }
        else if (!this.avatar.equals(other.avatar))
            return false;
        if (this.ordinal == null) {
            if (other.ordinal != null)
                return false;
        }
        else if (!this.ordinal.equals(other.ordinal))
            return false;
        if (this.ageLevel == null) {
            if (other.ageLevel != null)
                return false;
        }
        else if (!this.ageLevel.equals(other.ageLevel))
            return false;
        if (this.labels == null) {
            if (other.labels != null)
                return false;
        }
        else if (!this.labels.equals(other.labels))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.code == null) ? 0 : this.code.hashCode());
        result = prime * result + ((this.providerType == null) ? 0 : this.providerType.hashCode());
        result = prime * result + ((this.category == null) ? 0 : this.category.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.enName == null) ? 0 : this.enName.hashCode());
        result = prime * result + ((this.cnName == null) ? 0 : this.cnName.hashCode());
        result = prime * result + ((this.gender == null) ? 0 : this.gender.hashCode());
        result = prime * result + ((this.avatar == null) ? 0 : this.avatar.hashCode());
        result = prime * result + ((this.ordinal == null) ? 0 : this.ordinal.hashCode());
        result = prime * result + ((this.ageLevel == null) ? 0 : this.ageLevel.hashCode());
        result = prime * result + ((this.labels == null) ? 0 : this.labels.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationUserVoice (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(code);
        sb.append(", ").append(providerType);
        sb.append(", ").append(category);
        sb.append(", ").append(status);
        sb.append(", ").append(enName);
        sb.append(", ").append(cnName);
        sb.append(", ").append(gender);
        sb.append(", ").append(avatar);
        sb.append(", ").append(ordinal);
        sb.append(", ").append(ageLevel);
        sb.append(", ").append(labels);

        sb.append(")");
        return sb.toString();
    }
}
