/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.UserFileTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserFile;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户文件
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserFileRecord extends UpdatableRecordImpl<UserFileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_user_file.id</code>. 主键
     */
    public UserFileRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_user_file.created_at</code>. 创建时间
     */
    public UserFileRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_user_file.updated_at</code>. 更新时间
     */
    public UserFileRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_user_file.uid</code>. 所属用户（1表示系统）
     */
    public UserFileRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.uid</code>. 所属用户（1表示系统）
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_user_file.biz_source</code>. 业务来源
     */
    public UserFileRecord setBizSource(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.biz_source</code>. 业务来源
     */
    public String getBizSource() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_user_file.original_filename</code>.
     * 原文件名
     */
    public UserFileRecord setOriginalFilename(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.original_filename</code>.
     * 原文件名
     */
    public String getOriginalFilename() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_user_file.extension</code>.
     * 文件扩展名（不带.号）
     */
    public UserFileRecord setExtension(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.extension</code>.
     * 文件扩展名（不带.号）
     */
    public String getExtension() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_user_file.type</code>. 文件类型
     */
    public UserFileRecord setType(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.type</code>. 文件类型
     */
    public String getType() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_user_file.storage_type</code>. 文件存储类型
     */
    public UserFileRecord setStorageType(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.storage_type</code>. 文件存储类型
     */
    public String getStorageType() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_user_file.bucket</code>. 文件存储空间
     */
    public UserFileRecord setBucket(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.bucket</code>. 文件存储空间
     */
    public String getBucket() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_user_file.key</code>.
     * 文件唯一标识（在存储空间下，包含目录+文件名）
     */
    public UserFileRecord setKey(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.key</code>.
     * 文件唯一标识（在存储空间下，包含目录+文件名）
     */
    public String getKey() {
        return (String) get(10);
    }

    /**
     * Setter for <code>creation.creation_user_file.acl</code>. 文件访问控制
     */
    public UserFileRecord setAcl(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.acl</code>. 文件访问控制
     */
    public String getAcl() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_user_file.created_date</code>.
     * 创建日期（yyyyMMdd）
     */
    public UserFileRecord setCreatedDate(Integer value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.created_date</code>.
     * 创建日期（yyyyMMdd）
     */
    public Integer getCreatedDate() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>creation.creation_user_file.created_month</code>.
     * 创建月份（yyyyMM）
     */
    public UserFileRecord setCreatedMonth(Integer value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.created_month</code>.
     * 创建月份（yyyyMM）
     */
    public Integer getCreatedMonth() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>creation.creation_user_file.creator</code>. 创建者
     */
    public UserFileRecord setCreator(JSONB value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return (JSONB) get(14);
    }

    /**
     * Setter for <code>creation.creation_user_file.metadata</code>. 文件元数据
     */
    public UserFileRecord setMetadata(JSONB value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.metadata</code>. 文件元数据
     */
    public JSONB getMetadata() {
        return (JSONB) get(15);
    }

    /**
     * Setter for <code>creation.creation_user_file.trashed</code>. 软删除标记
     */
    public UserFileRecord setTrashed(@Nullable Boolean value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.trashed</code>. 软删除标记
     */
    @Nullable
    public Boolean getTrashed() {
        return (Boolean) get(16);
    }

    /**
     * Setter for <code>creation.creation_user_file.content</code>. 文件内容
     */
    public UserFileRecord setContent(@Nullable JSONB value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.content</code>. 文件内容
     */
    @Nullable
    public JSONB getContent() {
        return (JSONB) get(17);
    }

    /**
     * Setter for <code>creation.creation_user_file.md5</code>. 文件内容MD5值（base64）
     */
    public UserFileRecord setMd5(@Nullable String value) {
        set(18, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_file.md5</code>. 文件内容MD5值（base64）
     */
    @Nullable
    public String getMd5() {
        return (String) get(18);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserFileRecord
     */
    public UserFileRecord() {
        super(UserFileTable.CREATION_USER_FILE);
    }

    /**
     * Create a detached, initialised UserFileRecord
     */
    public UserFileRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String bizSource, String originalFilename, String extension, String type, String storageType, String bucket, String key, String acl, Integer createdDate, Integer createdMonth, JSONB creator, JSONB metadata, @Nullable Boolean trashed, @Nullable JSONB content, @Nullable String md5) {
        super(UserFileTable.CREATION_USER_FILE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setBizSource(bizSource);
        setOriginalFilename(originalFilename);
        setExtension(extension);
        setType(type);
        setStorageType(storageType);
        setBucket(bucket);
        setKey(key);
        setAcl(acl);
        setCreatedDate(createdDate);
        setCreatedMonth(createdMonth);
        setCreator(creator);
        setMetadata(metadata);
        setTrashed(trashed);
        setContent(content);
        setMd5(md5);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserFileRecord
     */
    public UserFileRecord(CreationUserFile value) {
        super(UserFileTable.CREATION_USER_FILE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setBizSource(value.getBizSource());
            setOriginalFilename(value.getOriginalFilename());
            setExtension(value.getExtension());
            setType(value.getType());
            setStorageType(value.getStorageType());
            setBucket(value.getBucket());
            setKey(value.getKey());
            setAcl(value.getAcl());
            setCreatedDate(value.getCreatedDate());
            setCreatedMonth(value.getCreatedMonth());
            setCreator(value.getCreator());
            setMetadata(value.getMetadata());
            setTrashed(value.getTrashed());
            setContent(value.getContent());
            setMd5(value.getMd5());
            resetChangedOnNotNull();
        }
    }
}
