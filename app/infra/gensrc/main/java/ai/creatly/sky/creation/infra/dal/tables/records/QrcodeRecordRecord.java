/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.QrcodeRecordTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.QrcodeRecord;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 权益中心-码业务平台
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class QrcodeRecordRecord extends UpdatableRecordImpl<QrcodeRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.qrcode_record.id</code>. 主键
     */
    public QrcodeRecordRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.qrcode_record.created_at</code>. 创建时间
     */
    public QrcodeRecordRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.qrcode_record.updated_at</code>. 更新时间
     */
    public QrcodeRecordRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.qrcode_record.issuer_id</code>. 任务所有者ID（1表示平台）
     */
    public QrcodeRecordRecord setIssuerId(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.issuer_id</code>. 任务所有者ID（1表示平台）
     */
    public Long getIssuerId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.qrcode_record.biz_scene</code>. 业务场景
     */
    public QrcodeRecordRecord setBizScene(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.biz_scene</code>. 业务场景
     */
    public String getBizScene() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.qrcode_record.code_content</code>. 码内容
     */
    public QrcodeRecordRecord setCodeContent(JSONB value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.code_content</code>. 码内容
     */
    public JSONB getCodeContent() {
        return (JSONB) get(5);
    }

    /**
     * Setter for <code>creation.qrcode_record.code</code>. 码值，全局唯一
     */
    public QrcodeRecordRecord setCode(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.code</code>. 码值，全局唯一
     */
    public String getCode() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.qrcode_record.expire_time</code>. 码的有效期
     */
    public QrcodeRecordRecord setExpireTime(ZonedDateTime value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.expire_time</code>. 码的有效期
     */
    public ZonedDateTime getExpireTime() {
        return (ZonedDateTime) get(7);
    }

    /**
     * Setter for <code>creation.qrcode_record.status</code>. 状态
     */
    public QrcodeRecordRecord setStatus(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.status</code>. 状态
     */
    public String getStatus() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.qrcode_record.type</code>. 永久，一次性，时间
     */
    public QrcodeRecordRecord setType(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.type</code>. 永久，一次性，时间
     */
    public String getType() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.qrcode_record.max_threshold</code>. 最大阈值
     */
    public QrcodeRecordRecord setMaxThreshold(@Nullable Integer value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.qrcode_record.max_threshold</code>. 最大阈值
     */
    @Nullable
    public Integer getMaxThreshold() {
        return (Integer) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached QrcodeRecordRecord
     */
    public QrcodeRecordRecord() {
        super(QrcodeRecordTable.QRCODE_RECORD);
    }

    /**
     * Create a detached, initialised QrcodeRecordRecord
     */
    public QrcodeRecordRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long issuerId, String bizScene, JSONB codeContent, String code, ZonedDateTime expireTime, String status, String type, @Nullable Integer maxThreshold) {
        super(QrcodeRecordTable.QRCODE_RECORD);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setIssuerId(issuerId);
        setBizScene(bizScene);
        setCodeContent(codeContent);
        setCode(code);
        setExpireTime(expireTime);
        setStatus(status);
        setType(type);
        setMaxThreshold(maxThreshold);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised QrcodeRecordRecord
     */
    public QrcodeRecordRecord(QrcodeRecord value) {
        super(QrcodeRecordTable.QRCODE_RECORD);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setIssuerId(value.getIssuerId());
            setBizScene(value.getBizScene());
            setCodeContent(value.getCodeContent());
            setCode(value.getCode());
            setExpireTime(value.getExpireTime());
            setStatus(value.getStatus());
            setType(value.getType());
            setMaxThreshold(value.getMaxThreshold());
            resetChangedOnNotNull();
        }
    }
}
