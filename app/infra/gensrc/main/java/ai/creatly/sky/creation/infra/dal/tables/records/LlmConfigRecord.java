/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.LlmConfigTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationLlmConfig;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * LLM配置表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class LlmConfigRecord extends UpdatableRecordImpl<LlmConfigRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_llm_config.id</code>. 主键
     */
    public LlmConfigRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_llm_config.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_llm_config.created_at</code>. 创建时间
     */
    public LlmConfigRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_llm_config.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_llm_config.updated_at</code>. 更新时间
     */
    public LlmConfigRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_llm_config.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_llm_config.name</code>. 模型名称
     */
    public LlmConfigRecord setName(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_llm_config.name</code>. 模型名称
     */
    public String getName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_llm_config.description</code>. 模型描述
     */
    public LlmConfigRecord setDescription(@Nullable String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_llm_config.description</code>. 模型描述
     */
    @Nullable
    public String getDescription() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_llm_config.status</code>. 当前状态
     */
    public LlmConfigRecord setStatus(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_llm_config.status</code>. 当前状态
     */
    public String getStatus() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_llm_config.config</code>. 配置
     */
    public LlmConfigRecord setConfig(JSONB value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_llm_config.config</code>. 配置
     */
    public JSONB getConfig() {
        return (JSONB) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LlmConfigRecord
     */
    public LlmConfigRecord() {
        super(LlmConfigTable.CREATION_LLM_CONFIG);
    }

    /**
     * Create a detached, initialised LlmConfigRecord
     */
    public LlmConfigRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, String name, @Nullable String description, String status, JSONB config) {
        super(LlmConfigTable.CREATION_LLM_CONFIG);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setName(name);
        setDescription(description);
        setStatus(status);
        setConfig(config);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised LlmConfigRecord
     */
    public LlmConfigRecord(CreationLlmConfig value) {
        super(LlmConfigTable.CREATION_LLM_CONFIG);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setName(value.getName());
            setDescription(value.getDescription());
            setStatus(value.getStatus());
            setConfig(value.getConfig());
            resetChangedOnNotNull();
        }
    }
}
