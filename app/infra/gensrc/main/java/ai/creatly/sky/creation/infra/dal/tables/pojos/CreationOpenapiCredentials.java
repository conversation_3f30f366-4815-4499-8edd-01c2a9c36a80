/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * openapi密钥表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationOpenapiCredentials implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String uid;
    private String name;
    private String appKey;
    private String appSecret;
    private String status;
    private ZonedDateTime expireAt;

    public CreationOpenapiCredentials() {}

    public CreationOpenapiCredentials(CreationOpenapiCredentials value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.name = value.name;
        this.appKey = value.appKey;
        this.appSecret = value.appSecret;
        this.status = value.status;
        this.expireAt = value.expireAt;
    }

    public CreationOpenapiCredentials(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        String uid,
        String name,
        String appKey,
        String appSecret,
        String status,
        @Nullable ZonedDateTime expireAt
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.name = name;
        this.appKey = appKey;
        this.appSecret = appSecret;
        this.status = status;
        this.expireAt = expireAt;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.id</code>. 主键
     */
    public CreationOpenapiCredentials setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.created_at</code>.
     * 创建时间
     */
    public CreationOpenapiCredentials setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.updated_at</code>.
     * 更新时间
     */
    public CreationOpenapiCredentials setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.uid</code>. 用户id
     */
    public String getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.uid</code>. 用户id
     */
    public CreationOpenapiCredentials setUid(String uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.name</code>. 凭证
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.name</code>. 凭证
     */
    public CreationOpenapiCredentials setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.app_key</code>.
     * appKey
     */
    public String getAppKey() {
        return this.appKey;
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.app_key</code>.
     * appKey
     */
    public CreationOpenapiCredentials setAppKey(String appKey) {
        this.appKey = appKey;
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.app_secret</code>.
     * appSecret
     */
    public String getAppSecret() {
        return this.appSecret;
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.app_secret</code>.
     * appSecret
     */
    public CreationOpenapiCredentials setAppSecret(String appSecret) {
        this.appSecret = appSecret;
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.status</code>. 状态
     * VALID/INVALID
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.status</code>. 状态
     * VALID/INVALID
     */
    public CreationOpenapiCredentials setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_openapi_credentials.expire_at</code>.
     * 到期时间
     */
    @Nullable
    public ZonedDateTime getExpireAt() {
        return this.expireAt;
    }

    /**
     * Setter for <code>creation.creation_openapi_credentials.expire_at</code>.
     * 到期时间
     */
    public CreationOpenapiCredentials setExpireAt(@Nullable ZonedDateTime expireAt) {
        this.expireAt = expireAt;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationOpenapiCredentials other = (CreationOpenapiCredentials) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.appKey == null) {
            if (other.appKey != null)
                return false;
        }
        else if (!this.appKey.equals(other.appKey))
            return false;
        if (this.appSecret == null) {
            if (other.appSecret != null)
                return false;
        }
        else if (!this.appSecret.equals(other.appSecret))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.expireAt == null) {
            if (other.expireAt != null)
                return false;
        }
        else if (!this.expireAt.equals(other.expireAt))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.appKey == null) ? 0 : this.appKey.hashCode());
        result = prime * result + ((this.appSecret == null) ? 0 : this.appSecret.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.expireAt == null) ? 0 : this.expireAt.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationOpenapiCredentials (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(name);
        sb.append(", ").append(appKey);
        sb.append(", ").append(appSecret);
        sb.append(", ").append(status);
        sb.append(", ").append(expireAt);

        sb.append(")");
        return sb.toString();
    }
}
