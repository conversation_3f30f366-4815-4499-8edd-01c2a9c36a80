/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.AiRechargeTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiRecharge;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * AI工具充值
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiRechargeRecord extends UpdatableRecordImpl<AiRechargeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_ai_recharge.id</code>. id
     */
    public AiRechargeRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.id</code>. id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.created_at</code>. 创建时间
     */
    public AiRechargeRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.updated_at</code>. 更新时间
     */
    public AiRechargeRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.priority</code>. 推荐优先级
     */
    public AiRechargeRecord setPriority(@Nullable Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.priority</code>. 推荐优先级
     */
    @Nullable
    public Long getPriority() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.price</code>. 价格
     */
    public AiRechargeRecord setPrice(@Nullable Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.price</code>. 价格
     */
    @Nullable
    public Long getPrice() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.credit</code>. 元气
     */
    public AiRechargeRecord setCredit(@Nullable Long value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.credit</code>. 元气
     */
    @Nullable
    public Long getCredit() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public AiRechargeRecord setOwnerId(@Nullable Long value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    @Nullable
    public Long getOwnerId() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.owner_name</code>. 任务所有者名称
     */
    public AiRechargeRecord setOwnerName(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.owner_name</code>. 任务所有者名称
     */
    @Nullable
    public String getOwnerName() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.paid_time</code>. 支付时间
     */
    public AiRechargeRecord setPaidTime(@Nullable ZonedDateTime value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.paid_time</code>. 支付时间
     */
    @Nullable
    public ZonedDateTime getPaidTime() {
        return (ZonedDateTime) get(8);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.paid_channel</code>. 支付渠道
     */
    public AiRechargeRecord setPaidChannel(@Nullable String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.paid_channel</code>. 支付渠道
     */
    @Nullable
    public String getPaidChannel() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.order_id</code>. 订单id
     */
    public AiRechargeRecord setOrderId(@Nullable Long value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.order_id</code>. 订单id
     */
    @Nullable
    public Long getOrderId() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.code_url</code>. 支付二维码
     */
    public AiRechargeRecord setCodeUrl(@Nullable String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.code_url</code>. 支付二维码
     */
    @Nullable
    public String getCodeUrl() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.expire_at</code>. 超时时间
     */
    public AiRechargeRecord setExpireAt(@Nullable ZonedDateTime value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.expire_at</code>. 超时时间
     */
    @Nullable
    public ZonedDateTime getExpireAt() {
        return (ZonedDateTime) get(12);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.pay_status</code>. 支付状态
     */
    public AiRechargeRecord setPayStatus(@Nullable String value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.pay_status</code>. 支付状态
     */
    @Nullable
    public String getPayStatus() {
        return (String) get(13);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.status</code>. 状态
     */
    public AiRechargeRecord setStatus(@Nullable Boolean value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.status</code>. 状态
     */
    @Nullable
    public Boolean getStatus() {
        return (Boolean) get(14);
    }

    /**
     * Setter for <code>creation.creation_ai_recharge.org_code</code>. 组织代码
     */
    public AiRechargeRecord setOrgCode(@Nullable String value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_recharge.org_code</code>. 组织代码
     */
    @Nullable
    public String getOrgCode() {
        return (String) get(15);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AiRechargeRecord
     */
    public AiRechargeRecord() {
        super(AiRechargeTable.CREATION_AI_RECHARGE);
    }

    /**
     * Create a detached, initialised AiRechargeRecord
     */
    public AiRechargeRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable Long priority, @Nullable Long price, @Nullable Long credit, @Nullable Long ownerId, @Nullable String ownerName, @Nullable ZonedDateTime paidTime, @Nullable String paidChannel, @Nullable Long orderId, @Nullable String codeUrl, @Nullable ZonedDateTime expireAt, @Nullable String payStatus, @Nullable Boolean status, @Nullable String orgCode) {
        super(AiRechargeTable.CREATION_AI_RECHARGE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setPriority(priority);
        setPrice(price);
        setCredit(credit);
        setOwnerId(ownerId);
        setOwnerName(ownerName);
        setPaidTime(paidTime);
        setPaidChannel(paidChannel);
        setOrderId(orderId);
        setCodeUrl(codeUrl);
        setExpireAt(expireAt);
        setPayStatus(payStatus);
        setStatus(status);
        setOrgCode(orgCode);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised AiRechargeRecord
     */
    public AiRechargeRecord(CreationAiRecharge value) {
        super(AiRechargeTable.CREATION_AI_RECHARGE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setPriority(value.getPriority());
            setPrice(value.getPrice());
            setCredit(value.getCredit());
            setOwnerId(value.getOwnerId());
            setOwnerName(value.getOwnerName());
            setPaidTime(value.getPaidTime());
            setPaidChannel(value.getPaidChannel());
            setOrderId(value.getOrderId());
            setCodeUrl(value.getCodeUrl());
            setExpireAt(value.getExpireAt());
            setPayStatus(value.getPayStatus());
            setStatus(value.getStatus());
            setOrgCode(value.getOrgCode());
            resetChangedOnNotNull();
        }
    }
}
