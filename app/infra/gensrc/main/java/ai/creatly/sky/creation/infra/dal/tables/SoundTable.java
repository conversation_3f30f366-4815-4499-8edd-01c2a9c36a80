/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.SoundRecord;

import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;
import org.jooq.types.YearToSecond;


/**
 * 音效库
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class SoundTable extends TableImpl<SoundRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_sound</code>
     */
    public static final SoundTable CREATION_SOUND = new SoundTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SoundRecord> getRecordType() {
        return SoundRecord.class;
    }

    /**
     * The column <code>creation.creation_sound.id</code>. 主键ID
     */
    public final TableField<SoundRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键ID");

    /**
     * The column <code>creation.creation_sound.created_at</code>. 创建时间
     */
    public final TableField<SoundRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_sound.updated_at</code>. 更新时间
     */
    public final TableField<SoundRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_sound.uid</code>. 所属用户ID（1为平台）
     */
    public final TableField<SoundRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "所属用户ID（1为平台）");

    /**
     * The column <code>creation.creation_sound.status</code>. 状态
     */
    public final TableField<SoundRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(32).nullable(false), this, "状态");

    /**
     * The column <code>creation.creation_sound.source_type</code>. 来源方类型
     */
    public final TableField<SoundRecord, String> SOURCE_TYPE = createField(DSL.name("source_type"), SQLDataType.VARCHAR(32).nullable(false), this, "来源方类型");

    /**
     * The column <code>creation.creation_sound.source_no</code>. 来源方的音效编号
     */
    public final TableField<SoundRecord, String> SOURCE_NO = createField(DSL.name("source_no"), SQLDataType.VARCHAR(64).nullable(false), this, "来源方的音效编号");

    /**
     * The column <code>creation.creation_sound.author_name</code>. 音效作者
     */
    public final TableField<SoundRecord, String> AUTHOR_NAME = createField(DSL.name("author_name"), SQLDataType.VARCHAR(64).nullable(false), this, "音效作者");

    /**
     * The column <code>creation.creation_sound.name</code>. 音效名称
     */
    public final TableField<SoundRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false), this, "音效名称");

    /**
     * The column <code>creation.creation_sound.tag_names</code>. 音效标签名列表
     */
    public final TableField<SoundRecord, String[]> TAG_NAMES = createField(DSL.name("tag_names"), SQLDataType.VARCHAR(32).nullable(false).array(), this, "音效标签名列表");

    /**
     * The column <code>creation.creation_sound.audio_id</code>.
     */
    public final TableField<SoundRecord, Long> AUDIO_ID = createField(DSL.name("audio_id"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>creation.creation_sound.audio_url</code>. 音效文件地址（OSS格式）
     */
    public final TableField<SoundRecord, String> AUDIO_URL = createField(DSL.name("audio_url"), SQLDataType.VARCHAR(128).nullable(false), this, "音效文件地址（OSS格式）");

    /**
     * The column <code>creation.creation_sound.audio_format</code>. 音效文件格式（小写）
     */
    public final TableField<SoundRecord, String> AUDIO_FORMAT = createField(DSL.name("audio_format"), SQLDataType.VARCHAR(32).nullable(false), this, "音效文件格式（小写）");

    /**
     * The column <code>creation.creation_sound.duration</code>. 音效时长
     */
    public final TableField<SoundRecord, Duration> DURATION = createField(DSL.name("duration"), SQLDataType.INTERVAL.nullable(false), this, "音效时长", Converter.ofNullable(YearToSecond.class, Duration.class, YearToSecond::toDuration, YearToSecond::valueOf));

    /**
     * The column <code>creation.creation_sound.bitrate</code>. 比特率（kps）
     */
    public final TableField<SoundRecord, Integer> BITRATE = createField(DSL.name("bitrate"), SQLDataType.INTEGER.nullable(false), this, "比特率（kps）");

    /**
     * The column <code>creation.creation_sound.sample_rate</code>. 采样率（Hz）
     */
    public final TableField<SoundRecord, Integer> SAMPLE_RATE = createField(DSL.name("sample_rate"), SQLDataType.INTEGER.nullable(false), this, "采样率（Hz）");

    /**
     * The column <code>creation.creation_sound.creator</code>. 创建者
     */
    public final TableField<SoundRecord, JSONB> CREATOR = createField(DSL.name("creator"), SQLDataType.JSONB.nullable(false), this, "创建者");

    /**
     * The column <code>creation.creation_sound.updater</code>. 更新者
     */
    public final TableField<SoundRecord, JSONB> UPDATER = createField(DSL.name("updater"), SQLDataType.JSONB.nullable(false), this, "更新者");

    private SoundTable(Name alias, Table<SoundRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private SoundTable(Name alias, Table<SoundRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("音效库"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_sound</code> table reference
     */
    public SoundTable(String alias) {
        this(DSL.name(alias), CREATION_SOUND);
    }

    /**
     * Create an aliased <code>creation.creation_sound</code> table reference
     */
    public SoundTable(Name alias) {
        this(alias, CREATION_SOUND);
    }

    /**
     * Create a <code>creation.creation_sound</code> table reference
     */
    public SoundTable() {
        this(DSL.name("creation_sound"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<SoundRecord> getPrimaryKey() {
        return Keys.CREATION_SOUND_PKEY;
    }

    @Override
    public List<UniqueKey<SoundRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CREATION_SOUND_SOURCE_TYPE_SOURCE_NO_KEY);
    }

    @Override
    public SoundTable as(String alias) {
        return new SoundTable(DSL.name(alias), this);
    }

    @Override
    public SoundTable as(Name alias) {
        return new SoundTable(alias, this);
    }

    @Override
    public SoundTable as(Table<?> alias) {
        return new SoundTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public SoundTable rename(String name) {
        return new SoundTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SoundTable rename(Name name) {
        return new SoundTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public SoundTable rename(Table<?> name) {
        return new SoundTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public SoundTable where(Condition condition) {
        return new SoundTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public SoundTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public SoundTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public SoundTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public SoundTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public SoundTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public SoundTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public SoundTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public SoundTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public SoundTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
