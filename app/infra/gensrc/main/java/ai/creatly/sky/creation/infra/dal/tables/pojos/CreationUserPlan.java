/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 用户私人定价计划
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationUserPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private JSONB createdBy;
    private JSONB updatedBy;
    private Long uid;
    private String platformPlanId;
    private String type;
    private String name;
    private String description;
    private Long originalFee;
    private Long realFee;
    private String orderTitle;
    private String periodType;
    private Boolean autoRenewed;
    private Integer level;
    private String benefitDuration;
    private JSONB benefits;

    public CreationUserPlan() {}

    public CreationUserPlan(CreationUserPlan value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.uid = value.uid;
        this.platformPlanId = value.platformPlanId;
        this.type = value.type;
        this.name = value.name;
        this.description = value.description;
        this.originalFee = value.originalFee;
        this.realFee = value.realFee;
        this.orderTitle = value.orderTitle;
        this.periodType = value.periodType;
        this.autoRenewed = value.autoRenewed;
        this.level = value.level;
        this.benefitDuration = value.benefitDuration;
        this.benefits = value.benefits;
    }

    public CreationUserPlan(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        JSONB createdBy,
        JSONB updatedBy,
        Long uid,
        String platformPlanId,
        String type,
        String name,
        String description,
        Long originalFee,
        Long realFee,
        String orderTitle,
        String periodType,
        @Nullable Boolean autoRenewed,
        Integer level,
        @Nullable String benefitDuration,
        JSONB benefits
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.uid = uid;
        this.platformPlanId = platformPlanId;
        this.type = type;
        this.name = name;
        this.description = description;
        this.originalFee = originalFee;
        this.realFee = realFee;
        this.orderTitle = orderTitle;
        this.periodType = periodType;
        this.autoRenewed = autoRenewed;
        this.level = level;
        this.benefitDuration = benefitDuration;
        this.benefits = benefits;
    }

    /**
     * Getter for <code>creation.creation_user_plan.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_user_plan.id</code>. 主键
     */
    public CreationUserPlan setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_user_plan.created_at</code>. 创建时间
     */
    public CreationUserPlan setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_user_plan.updated_at</code>. 更新时间
     */
    public CreationUserPlan setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.created_by</code>. 创建者
     */
    public JSONB getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>creation.creation_user_plan.created_by</code>. 创建者
     */
    public CreationUserPlan setCreatedBy(JSONB createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.updated_by</code>. 更新者
     */
    public JSONB getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>creation.creation_user_plan.updated_by</code>. 更新者
     */
    public CreationUserPlan setUpdatedBy(JSONB updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.uid</code>. 用户ID
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_user_plan.uid</code>. 用户ID
     */
    public CreationUserPlan setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.platform_plan_id</code>.
     * 平台统一定价计划ID
     */
    public String getPlatformPlanId() {
        return this.platformPlanId;
    }

    /**
     * Setter for <code>creation.creation_user_plan.platform_plan_id</code>.
     * 平台统一定价计划ID
     */
    public CreationUserPlan setPlatformPlanId(String platformPlanId) {
        this.platformPlanId = platformPlanId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.type</code>. 计划类型
     */
    public String getType() {
        return this.type;
    }

    /**
     * Setter for <code>creation.creation_user_plan.type</code>. 计划类型
     */
    public CreationUserPlan setType(String type) {
        this.type = type;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.name</code>. 计划名称
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.creation_user_plan.name</code>. 计划名称
     */
    public CreationUserPlan setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.description</code>. 分类
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>creation.creation_user_plan.description</code>. 分类
     */
    public CreationUserPlan setDescription(String description) {
        this.description = description;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.original_fee</code>. 原价（分）
     */
    public Long getOriginalFee() {
        return this.originalFee;
    }

    /**
     * Setter for <code>creation.creation_user_plan.original_fee</code>. 原价（分）
     */
    public CreationUserPlan setOriginalFee(Long originalFee) {
        this.originalFee = originalFee;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.real_fee</code>. 实价（分）
     */
    public Long getRealFee() {
        return this.realFee;
    }

    /**
     * Setter for <code>creation.creation_user_plan.real_fee</code>. 实价（分）
     */
    public CreationUserPlan setRealFee(Long realFee) {
        this.realFee = realFee;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.order_title</code>. 订单标题
     */
    public String getOrderTitle() {
        return this.orderTitle;
    }

    /**
     * Setter for <code>creation.creation_user_plan.order_title</code>. 订单标题
     */
    public CreationUserPlan setOrderTitle(String orderTitle) {
        this.orderTitle = orderTitle;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.period_type</code>. 计划周期类型
     */
    public String getPeriodType() {
        return this.periodType;
    }

    /**
     * Setter for <code>creation.creation_user_plan.period_type</code>. 计划周期类型
     */
    public CreationUserPlan setPeriodType(String periodType) {
        this.periodType = periodType;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.auto_renewed</code>.
     * 到期是否自动续费（如果为空，则取决于用户选择）
     */
    @Nullable
    public Boolean getAutoRenewed() {
        return this.autoRenewed;
    }

    /**
     * Setter for <code>creation.creation_user_plan.auto_renewed</code>.
     * 到期是否自动续费（如果为空，则取决于用户选择）
     */
    public CreationUserPlan setAutoRenewed(@Nullable Boolean autoRenewed) {
        this.autoRenewed = autoRenewed;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.level</code>.
     * 阶梯档位（用于排序、有序展示）
     */
    public Integer getLevel() {
        return this.level;
    }

    /**
     * Setter for <code>creation.creation_user_plan.level</code>.
     * 阶梯档位（用于排序、有序展示）
     */
    public CreationUserPlan setLevel(Integer level) {
        this.level = level;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.benefit_duration</code>.
     * 权益有效期
     */
    @Nullable
    public String getBenefitDuration() {
        return this.benefitDuration;
    }

    /**
     * Setter for <code>creation.creation_user_plan.benefit_duration</code>.
     * 权益有效期
     */
    public CreationUserPlan setBenefitDuration(@Nullable String benefitDuration) {
        this.benefitDuration = benefitDuration;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_plan.benefits</code>. 权益列表
     */
    public JSONB getBenefits() {
        return this.benefits;
    }

    /**
     * Setter for <code>creation.creation_user_plan.benefits</code>. 权益列表
     */
    public CreationUserPlan setBenefits(JSONB benefits) {
        this.benefits = benefits;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationUserPlan other = (CreationUserPlan) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!this.createdBy.equals(other.createdBy))
            return false;
        if (this.updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!this.updatedBy.equals(other.updatedBy))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.platformPlanId == null) {
            if (other.platformPlanId != null)
                return false;
        }
        else if (!this.platformPlanId.equals(other.platformPlanId))
            return false;
        if (this.type == null) {
            if (other.type != null)
                return false;
        }
        else if (!this.type.equals(other.type))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.description == null) {
            if (other.description != null)
                return false;
        }
        else if (!this.description.equals(other.description))
            return false;
        if (this.originalFee == null) {
            if (other.originalFee != null)
                return false;
        }
        else if (!this.originalFee.equals(other.originalFee))
            return false;
        if (this.realFee == null) {
            if (other.realFee != null)
                return false;
        }
        else if (!this.realFee.equals(other.realFee))
            return false;
        if (this.orderTitle == null) {
            if (other.orderTitle != null)
                return false;
        }
        else if (!this.orderTitle.equals(other.orderTitle))
            return false;
        if (this.periodType == null) {
            if (other.periodType != null)
                return false;
        }
        else if (!this.periodType.equals(other.periodType))
            return false;
        if (this.autoRenewed == null) {
            if (other.autoRenewed != null)
                return false;
        }
        else if (!this.autoRenewed.equals(other.autoRenewed))
            return false;
        if (this.level == null) {
            if (other.level != null)
                return false;
        }
        else if (!this.level.equals(other.level))
            return false;
        if (this.benefitDuration == null) {
            if (other.benefitDuration != null)
                return false;
        }
        else if (!this.benefitDuration.equals(other.benefitDuration))
            return false;
        if (this.benefits == null) {
            if (other.benefits != null)
                return false;
        }
        else if (!this.benefits.equals(other.benefits))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.platformPlanId == null) ? 0 : this.platformPlanId.hashCode());
        result = prime * result + ((this.type == null) ? 0 : this.type.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.description == null) ? 0 : this.description.hashCode());
        result = prime * result + ((this.originalFee == null) ? 0 : this.originalFee.hashCode());
        result = prime * result + ((this.realFee == null) ? 0 : this.realFee.hashCode());
        result = prime * result + ((this.orderTitle == null) ? 0 : this.orderTitle.hashCode());
        result = prime * result + ((this.periodType == null) ? 0 : this.periodType.hashCode());
        result = prime * result + ((this.autoRenewed == null) ? 0 : this.autoRenewed.hashCode());
        result = prime * result + ((this.level == null) ? 0 : this.level.hashCode());
        result = prime * result + ((this.benefitDuration == null) ? 0 : this.benefitDuration.hashCode());
        result = prime * result + ((this.benefits == null) ? 0 : this.benefits.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationUserPlan (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(uid);
        sb.append(", ").append(platformPlanId);
        sb.append(", ").append(type);
        sb.append(", ").append(name);
        sb.append(", ").append(description);
        sb.append(", ").append(originalFee);
        sb.append(", ").append(realFee);
        sb.append(", ").append(orderTitle);
        sb.append(", ").append(periodType);
        sb.append(", ").append(autoRenewed);
        sb.append(", ").append(level);
        sb.append(", ").append(benefitDuration);
        sb.append(", ").append(benefits);

        sb.append(")");
        return sb.toString();
    }
}
