/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.Duration;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 商品视频
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationProductVideo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String status;
    private String title;
    private String description;
    private Long coverFileId;
    private String coverUrl;
    private Long videoFileId;
    private String videoUrl;
    private Duration videoDuration;
    private String videoFormat;
    private String videoResolution;
    private Long audioFileId;
    private String audioUrl;
    private JSONB audioMetadata;
    private JSONB creator;

    public CreationProductVideo() {}

    public CreationProductVideo(CreationProductVideo value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.status = value.status;
        this.title = value.title;
        this.description = value.description;
        this.coverFileId = value.coverFileId;
        this.coverUrl = value.coverUrl;
        this.videoFileId = value.videoFileId;
        this.videoUrl = value.videoUrl;
        this.videoDuration = value.videoDuration;
        this.videoFormat = value.videoFormat;
        this.videoResolution = value.videoResolution;
        this.audioFileId = value.audioFileId;
        this.audioUrl = value.audioUrl;
        this.audioMetadata = value.audioMetadata;
        this.creator = value.creator;
    }

    public CreationProductVideo(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String status,
        String title,
        String description,
        @Nullable Long coverFileId,
        @Nullable String coverUrl,
        Long videoFileId,
        String videoUrl,
        Duration videoDuration,
        String videoFormat,
        @Nullable String videoResolution,
        @Nullable Long audioFileId,
        @Nullable String audioUrl,
        @Nullable JSONB audioMetadata,
        JSONB creator
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.status = status;
        this.title = title;
        this.description = description;
        this.coverFileId = coverFileId;
        this.coverUrl = coverUrl;
        this.videoFileId = videoFileId;
        this.videoUrl = videoUrl;
        this.videoDuration = videoDuration;
        this.videoFormat = videoFormat;
        this.videoResolution = videoResolution;
        this.audioFileId = audioFileId;
        this.audioUrl = audioUrl;
        this.audioMetadata = audioMetadata;
        this.creator = creator;
    }

    /**
     * Getter for <code>creation.creation_product_video.id</code>. 主键ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_product_video.id</code>. 主键ID
     */
    public CreationProductVideo setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_product_video.created_at</code>. 创建时间
     */
    public CreationProductVideo setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_product_video.updated_at</code>. 更新时间
     */
    public CreationProductVideo setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.uid</code>. 所属用户ID（1为平台）
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_product_video.uid</code>. 所属用户ID（1为平台）
     */
    public CreationProductVideo setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.status</code>. 状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_product_video.status</code>. 状态
     */
    public CreationProductVideo setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.title</code>. 标题
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * Setter for <code>creation.creation_product_video.title</code>. 标题
     */
    public CreationProductVideo setTitle(String title) {
        this.title = title;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.description</code>. 描述
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>creation.creation_product_video.description</code>. 描述
     */
    public CreationProductVideo setDescription(String description) {
        this.description = description;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.cover_file_id</code>.
     * 封面图文件ID
     */
    @Nullable
    public Long getCoverFileId() {
        return this.coverFileId;
    }

    /**
     * Setter for <code>creation.creation_product_video.cover_file_id</code>.
     * 封面图文件ID
     */
    public CreationProductVideo setCoverFileId(@Nullable Long coverFileId) {
        this.coverFileId = coverFileId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.cover_url</code>. 封面图地址
     */
    @Nullable
    public String getCoverUrl() {
        return this.coverUrl;
    }

    /**
     * Setter for <code>creation.creation_product_video.cover_url</code>. 封面图地址
     */
    public CreationProductVideo setCoverUrl(@Nullable String coverUrl) {
        this.coverUrl = coverUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.video_file_id</code>.
     * 视频文件ID
     */
    public Long getVideoFileId() {
        return this.videoFileId;
    }

    /**
     * Setter for <code>creation.creation_product_video.video_file_id</code>.
     * 视频文件ID
     */
    public CreationProductVideo setVideoFileId(Long videoFileId) {
        this.videoFileId = videoFileId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.video_url</code>.
     * 视频地址（OSS格式）
     */
    public String getVideoUrl() {
        return this.videoUrl;
    }

    /**
     * Setter for <code>creation.creation_product_video.video_url</code>.
     * 视频地址（OSS格式）
     */
    public CreationProductVideo setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.video_duration</code>.
     * 视频时长
     */
    public Duration getVideoDuration() {
        return this.videoDuration;
    }

    /**
     * Setter for <code>creation.creation_product_video.video_duration</code>.
     * 视频时长
     */
    public CreationProductVideo setVideoDuration(Duration videoDuration) {
        this.videoDuration = videoDuration;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.video_format</code>.
     * 视频格式（小写）
     */
    public String getVideoFormat() {
        return this.videoFormat;
    }

    /**
     * Setter for <code>creation.creation_product_video.video_format</code>.
     * 视频格式（小写）
     */
    public CreationProductVideo setVideoFormat(String videoFormat) {
        this.videoFormat = videoFormat;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.video_resolution</code>.
     * 视频分辨率（宽度x高度）
     */
    @Nullable
    public String getVideoResolution() {
        return this.videoResolution;
    }

    /**
     * Setter for <code>creation.creation_product_video.video_resolution</code>.
     * 视频分辨率（宽度x高度）
     */
    public CreationProductVideo setVideoResolution(@Nullable String videoResolution) {
        this.videoResolution = videoResolution;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.audio_file_id</code>.
     * 音频文件ID
     */
    @Nullable
    public Long getAudioFileId() {
        return this.audioFileId;
    }

    /**
     * Setter for <code>creation.creation_product_video.audio_file_id</code>.
     * 音频文件ID
     */
    public CreationProductVideo setAudioFileId(@Nullable Long audioFileId) {
        this.audioFileId = audioFileId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.audio_url</code>.
     * 音频地址（OSS格式）
     */
    @Nullable
    public String getAudioUrl() {
        return this.audioUrl;
    }

    /**
     * Setter for <code>creation.creation_product_video.audio_url</code>.
     * 音频地址（OSS格式）
     */
    public CreationProductVideo setAudioUrl(@Nullable String audioUrl) {
        this.audioUrl = audioUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.audio_metadata</code>.
     * 音频元信息
     */
    @Nullable
    public JSONB getAudioMetadata() {
        return this.audioMetadata;
    }

    /**
     * Setter for <code>creation.creation_product_video.audio_metadata</code>.
     * 音频元信息
     */
    public CreationProductVideo setAudioMetadata(@Nullable JSONB audioMetadata) {
        this.audioMetadata = audioMetadata;
        return this;
    }

    /**
     * Getter for <code>creation.creation_product_video.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return this.creator;
    }

    /**
     * Setter for <code>creation.creation_product_video.creator</code>. 创建者
     */
    public CreationProductVideo setCreator(JSONB creator) {
        this.creator = creator;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationProductVideo other = (CreationProductVideo) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.title == null) {
            if (other.title != null)
                return false;
        }
        else if (!this.title.equals(other.title))
            return false;
        if (this.description == null) {
            if (other.description != null)
                return false;
        }
        else if (!this.description.equals(other.description))
            return false;
        if (this.coverFileId == null) {
            if (other.coverFileId != null)
                return false;
        }
        else if (!this.coverFileId.equals(other.coverFileId))
            return false;
        if (this.coverUrl == null) {
            if (other.coverUrl != null)
                return false;
        }
        else if (!this.coverUrl.equals(other.coverUrl))
            return false;
        if (this.videoFileId == null) {
            if (other.videoFileId != null)
                return false;
        }
        else if (!this.videoFileId.equals(other.videoFileId))
            return false;
        if (this.videoUrl == null) {
            if (other.videoUrl != null)
                return false;
        }
        else if (!this.videoUrl.equals(other.videoUrl))
            return false;
        if (this.videoDuration == null) {
            if (other.videoDuration != null)
                return false;
        }
        else if (!this.videoDuration.equals(other.videoDuration))
            return false;
        if (this.videoFormat == null) {
            if (other.videoFormat != null)
                return false;
        }
        else if (!this.videoFormat.equals(other.videoFormat))
            return false;
        if (this.videoResolution == null) {
            if (other.videoResolution != null)
                return false;
        }
        else if (!this.videoResolution.equals(other.videoResolution))
            return false;
        if (this.audioFileId == null) {
            if (other.audioFileId != null)
                return false;
        }
        else if (!this.audioFileId.equals(other.audioFileId))
            return false;
        if (this.audioUrl == null) {
            if (other.audioUrl != null)
                return false;
        }
        else if (!this.audioUrl.equals(other.audioUrl))
            return false;
        if (this.audioMetadata == null) {
            if (other.audioMetadata != null)
                return false;
        }
        else if (!this.audioMetadata.equals(other.audioMetadata))
            return false;
        if (this.creator == null) {
            if (other.creator != null)
                return false;
        }
        else if (!this.creator.equals(other.creator))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.title == null) ? 0 : this.title.hashCode());
        result = prime * result + ((this.description == null) ? 0 : this.description.hashCode());
        result = prime * result + ((this.coverFileId == null) ? 0 : this.coverFileId.hashCode());
        result = prime * result + ((this.coverUrl == null) ? 0 : this.coverUrl.hashCode());
        result = prime * result + ((this.videoFileId == null) ? 0 : this.videoFileId.hashCode());
        result = prime * result + ((this.videoUrl == null) ? 0 : this.videoUrl.hashCode());
        result = prime * result + ((this.videoDuration == null) ? 0 : this.videoDuration.hashCode());
        result = prime * result + ((this.videoFormat == null) ? 0 : this.videoFormat.hashCode());
        result = prime * result + ((this.videoResolution == null) ? 0 : this.videoResolution.hashCode());
        result = prime * result + ((this.audioFileId == null) ? 0 : this.audioFileId.hashCode());
        result = prime * result + ((this.audioUrl == null) ? 0 : this.audioUrl.hashCode());
        result = prime * result + ((this.audioMetadata == null) ? 0 : this.audioMetadata.hashCode());
        result = prime * result + ((this.creator == null) ? 0 : this.creator.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationProductVideo (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(status);
        sb.append(", ").append(title);
        sb.append(", ").append(description);
        sb.append(", ").append(coverFileId);
        sb.append(", ").append(coverUrl);
        sb.append(", ").append(videoFileId);
        sb.append(", ").append(videoUrl);
        sb.append(", ").append(videoDuration);
        sb.append(", ").append(videoFormat);
        sb.append(", ").append(videoResolution);
        sb.append(", ").append(audioFileId);
        sb.append(", ").append(audioUrl);
        sb.append(", ").append(audioMetadata);
        sb.append(", ").append(creator);

        sb.append(")");
        return sb.toString();
    }
}
