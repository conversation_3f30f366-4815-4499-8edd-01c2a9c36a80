/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.AiRechargeRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * AI工具充值
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiRechargeTable extends TableImpl<AiRechargeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_ai_recharge</code>
     */
    public static final AiRechargeTable CREATION_AI_RECHARGE = new AiRechargeTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AiRechargeRecord> getRecordType() {
        return AiRechargeRecord.class;
    }

    /**
     * The column <code>creation.creation_ai_recharge.id</code>. id
     */
    public final TableField<AiRechargeRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "id");

    /**
     * The column <code>creation.creation_ai_recharge.created_at</code>. 创建时间
     */
    public final TableField<AiRechargeRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_recharge.updated_at</code>. 更新时间
     */
    public final TableField<AiRechargeRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_recharge.priority</code>. 推荐优先级
     */
    public final TableField<AiRechargeRecord, Long> PRIORITY = createField(DSL.name("priority"), SQLDataType.BIGINT, this, "推荐优先级");

    /**
     * The column <code>creation.creation_ai_recharge.price</code>. 价格
     */
    public final TableField<AiRechargeRecord, Long> PRICE = createField(DSL.name("price"), SQLDataType.BIGINT, this, "价格");

    /**
     * The column <code>creation.creation_ai_recharge.credit</code>. 元气
     */
    public final TableField<AiRechargeRecord, Long> CREDIT = createField(DSL.name("credit"), SQLDataType.BIGINT, this, "元气");

    /**
     * The column <code>creation.creation_ai_recharge.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public final TableField<AiRechargeRecord, Long> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.BIGINT, this, "任务所有者ID（1表示系统）");

    /**
     * The column <code>creation.creation_ai_recharge.owner_name</code>. 任务所有者名称
     */
    public final TableField<AiRechargeRecord, String> OWNER_NAME = createField(DSL.name("owner_name"), SQLDataType.VARCHAR(128), this, "任务所有者名称");

    /**
     * The column <code>creation.creation_ai_recharge.paid_time</code>. 支付时间
     */
    public final TableField<AiRechargeRecord, ZonedDateTime> PAID_TIME = createField(DSL.name("paid_time"), SQLDataType.TIMESTAMPWITHTIMEZONE(3), this, "支付时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_recharge.paid_channel</code>. 支付渠道
     */
    public final TableField<AiRechargeRecord, String> PAID_CHANNEL = createField(DSL.name("paid_channel"), SQLDataType.VARCHAR(255), this, "支付渠道");

    /**
     * The column <code>creation.creation_ai_recharge.order_id</code>. 订单id
     */
    public final TableField<AiRechargeRecord, Long> ORDER_ID = createField(DSL.name("order_id"), SQLDataType.BIGINT, this, "订单id");

    /**
     * The column <code>creation.creation_ai_recharge.code_url</code>. 支付二维码
     */
    public final TableField<AiRechargeRecord, String> CODE_URL = createField(DSL.name("code_url"), SQLDataType.VARCHAR(255), this, "支付二维码");

    /**
     * The column <code>creation.creation_ai_recharge.expire_at</code>. 超时时间
     */
    public final TableField<AiRechargeRecord, ZonedDateTime> EXPIRE_AT = createField(DSL.name("expire_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3), this, "超时时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_recharge.pay_status</code>. 支付状态
     */
    public final TableField<AiRechargeRecord, String> PAY_STATUS = createField(DSL.name("pay_status"), SQLDataType.VARCHAR(64), this, "支付状态");

    /**
     * The column <code>creation.creation_ai_recharge.status</code>. 状态
     */
    public final TableField<AiRechargeRecord, Boolean> STATUS = createField(DSL.name("status"), SQLDataType.BOOLEAN, this, "状态");

    /**
     * The column <code>creation.creation_ai_recharge.org_code</code>. 组织代码
     */
    public final TableField<AiRechargeRecord, String> ORG_CODE = createField(DSL.name("org_code"), SQLDataType.VARCHAR(64), this, "组织代码");

    private AiRechargeTable(Name alias, Table<AiRechargeRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private AiRechargeTable(Name alias, Table<AiRechargeRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("AI工具充值"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_ai_recharge</code> table
     * reference
     */
    public AiRechargeTable(String alias) {
        this(DSL.name(alias), CREATION_AI_RECHARGE);
    }

    /**
     * Create an aliased <code>creation.creation_ai_recharge</code> table
     * reference
     */
    public AiRechargeTable(Name alias) {
        this(alias, CREATION_AI_RECHARGE);
    }

    /**
     * Create a <code>creation.creation_ai_recharge</code> table reference
     */
    public AiRechargeTable() {
        this(DSL.name("creation_ai_recharge"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<AiRechargeRecord> getPrimaryKey() {
        return Keys.CREATION_AI_RECHARGE_PKEY;
    }

    @Override
    public AiRechargeTable as(String alias) {
        return new AiRechargeTable(DSL.name(alias), this);
    }

    @Override
    public AiRechargeTable as(Name alias) {
        return new AiRechargeTable(alias, this);
    }

    @Override
    public AiRechargeTable as(Table<?> alias) {
        return new AiRechargeTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public AiRechargeTable rename(String name) {
        return new AiRechargeTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public AiRechargeTable rename(Name name) {
        return new AiRechargeTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public AiRechargeTable rename(Table<?> name) {
        return new AiRechargeTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiRechargeTable where(Condition condition) {
        return new AiRechargeTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiRechargeTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiRechargeTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiRechargeTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiRechargeTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiRechargeTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiRechargeTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiRechargeTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiRechargeTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiRechargeTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
