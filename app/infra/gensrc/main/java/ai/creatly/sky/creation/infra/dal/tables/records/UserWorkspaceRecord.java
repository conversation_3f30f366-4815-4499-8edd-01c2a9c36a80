/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.UserWorkspaceTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserWorkspace;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户创作空间模版
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserWorkspaceRecord extends UpdatableRecordImpl<UserWorkspaceRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_user_workspace.id</code>. 主键
     */
    public UserWorkspaceRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_user_workspace.created_at</code>. 创建时间
     */
    public UserWorkspaceRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_user_workspace.updated_at</code>. 更新时间
     */
    public UserWorkspaceRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_user_workspace.uid</code>. 用户id
     */
    public UserWorkspaceRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.uid</code>. 用户id
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_user_workspace.creator</code>. 创建者
     */
    public UserWorkspaceRecord setCreator(JSONB value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return (JSONB) get(4);
    }

    /**
     * Setter for <code>creation.creation_user_workspace.name</code>. 工作空间名
     */
    public UserWorkspaceRecord setName(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.name</code>. 工作空间名
     */
    public String getName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_user_workspace.type</code>. 工作空间类型
     */
    public UserWorkspaceRecord setType(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.type</code>. 工作空间类型
     */
    public String getType() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_user_workspace.template_id</code>.
     * 创建模版id
     */
    public UserWorkspaceRecord setTemplateId(Long value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.template_id</code>.
     * 创建模版id
     */
    public Long getTemplateId() {
        return (Long) get(7);
    }

    /**
     * Setter for
     * <code>creation.creation_user_workspace.workspace_config</code>. 工作空间内部配置
     */
    public UserWorkspaceRecord setWorkspaceConfig(JSONB value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_workspace.workspace_config</code>. 工作空间内部配置
     */
    public JSONB getWorkspaceConfig() {
        return (JSONB) get(8);
    }

    /**
     * Setter for <code>creation.creation_user_workspace.status</code>. 工作空间状态
     */
    public UserWorkspaceRecord setStatus(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.status</code>. 工作空间状态
     */
    public String getStatus() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_user_workspace.cover_file_id</code>.
     */
    public UserWorkspaceRecord setCoverFileId(@Nullable Long value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.cover_file_id</code>.
     */
    @Nullable
    public Long getCoverFileId() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>creation.creation_user_workspace.cover_oss_url</code>.
     */
    public UserWorkspaceRecord setCoverOssUrl(@Nullable String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_workspace.cover_oss_url</code>.
     */
    @Nullable
    public String getCoverOssUrl() {
        return (String) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserWorkspaceRecord
     */
    public UserWorkspaceRecord() {
        super(UserWorkspaceTable.CREATION_USER_WORKSPACE);
    }

    /**
     * Create a detached, initialised UserWorkspaceRecord
     */
    public UserWorkspaceRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, JSONB creator, String name, String type, Long templateId, JSONB workspaceConfig, String status, @Nullable Long coverFileId, @Nullable String coverOssUrl) {
        super(UserWorkspaceTable.CREATION_USER_WORKSPACE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setCreator(creator);
        setName(name);
        setType(type);
        setTemplateId(templateId);
        setWorkspaceConfig(workspaceConfig);
        setStatus(status);
        setCoverFileId(coverFileId);
        setCoverOssUrl(coverOssUrl);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserWorkspaceRecord
     */
    public UserWorkspaceRecord(CreationUserWorkspace value) {
        super(UserWorkspaceTable.CREATION_USER_WORKSPACE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setCreator(value.getCreator());
            setName(value.getName());
            setType(value.getType());
            setTemplateId(value.getTemplateId());
            setWorkspaceConfig(value.getWorkspaceConfig());
            setStatus(value.getStatus());
            setCoverFileId(value.getCoverFileId());
            setCoverOssUrl(value.getCoverOssUrl());
            resetChangedOnNotNull();
        }
    }
}
