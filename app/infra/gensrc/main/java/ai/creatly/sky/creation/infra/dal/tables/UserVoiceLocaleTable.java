/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.UserVoiceLocaleRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 声音定制的口音
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserVoiceLocaleTable extends TableImpl<UserVoiceLocaleRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>creation.creation_user_voice_locale</code>
     */
    public static final UserVoiceLocaleTable CREATION_USER_VOICE_LOCALE = new UserVoiceLocaleTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserVoiceLocaleRecord> getRecordType() {
        return UserVoiceLocaleRecord.class;
    }

    /**
     * The column <code>creation.creation_user_voice_locale.id</code>. 主键
     */
    public final TableField<UserVoiceLocaleRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键");

    /**
     * The column <code>creation.creation_user_voice_locale.created_at</code>.
     * 创建时间
     */
    public final TableField<UserVoiceLocaleRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_user_voice_locale.updated_at</code>.
     * 更新时间
     */
    public final TableField<UserVoiceLocaleRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_user_voice_locale.voice_id</code>.
     * 所属声音ID
     */
    public final TableField<UserVoiceLocaleRecord, Long> VOICE_ID = createField(DSL.name("voice_id"), SQLDataType.BIGINT.nullable(false), this, "所属声音ID");

    /**
     * The column <code>creation.creation_user_voice_locale.voice_code</code>.
     * 所属声音编号
     */
    public final TableField<UserVoiceLocaleRecord, String> VOICE_CODE = createField(DSL.name("voice_code"), SQLDataType.VARCHAR(128).nullable(false), this, "所属声音编号");

    /**
     * The column <code>creation.creation_user_voice_locale.code</code>. 本地化代号
     */
    public final TableField<UserVoiceLocaleRecord, String> CODE = createField(DSL.name("code"), SQLDataType.VARCHAR(32).nullable(false), this, "本地化代号");

    /**
     * The column <code>creation.creation_user_voice_locale.desc</code>.
     * 本地化描述（中文展示）
     */
    public final TableField<UserVoiceLocaleRecord, String> DESC = createField(DSL.name("desc"), SQLDataType.VARCHAR(64).nullable(false), this, "本地化描述（中文展示）");

    /**
     * The column <code>creation.creation_user_voice_locale.lang_code</code>.
     * 语言代号
     */
    public final TableField<UserVoiceLocaleRecord, String> LANG_CODE = createField(DSL.name("lang_code"), SQLDataType.VARCHAR(32).nullable(false), this, "语言代号");

    /**
     * The column <code>creation.creation_user_voice_locale.lang_name</code>.
     * 语言名称（中文展示）
     */
    public final TableField<UserVoiceLocaleRecord, String> LANG_NAME = createField(DSL.name("lang_name"), SQLDataType.VARCHAR(32).nullable(false), this, "语言名称（中文展示）");

    /**
     * The column <code>creation.creation_user_voice_locale.country_code</code>.
     * 国家代号
     */
    public final TableField<UserVoiceLocaleRecord, String> COUNTRY_CODE = createField(DSL.name("country_code"), SQLDataType.VARCHAR(32).nullable(false), this, "国家代号");

    /**
     * The column <code>creation.creation_user_voice_locale.country_name</code>.
     * 国家名称（中文展示）
     */
    public final TableField<UserVoiceLocaleRecord, String> COUNTRY_NAME = createField(DSL.name("country_name"), SQLDataType.VARCHAR(32).nullable(false), this, "国家名称（中文展示）");

    /**
     * The column <code>creation.creation_user_voice_locale.age_level</code>.
     * 默认年龄层
     */
    public final TableField<UserVoiceLocaleRecord, String> AGE_LEVEL = createField(DSL.name("age_level"), SQLDataType.VARCHAR(32).nullable(false), this, "默认年龄层");

    /**
     * The column
     * <code>creation.creation_user_voice_locale.preview_audio_id</code>.
     * 预览音频文件ID
     */
    public final TableField<UserVoiceLocaleRecord, Long> PREVIEW_AUDIO_ID = createField(DSL.name("preview_audio_id"), SQLDataType.BIGINT, this, "预览音频文件ID");

    /**
     * The column
     * <code>creation.creation_user_voice_locale.preview_audio_url</code>.
     * 预览音频地址（OSS地址）
     */
    public final TableField<UserVoiceLocaleRecord, String> PREVIEW_AUDIO_URL = createField(DSL.name("preview_audio_url"), SQLDataType.VARCHAR(200), this, "预览音频地址（OSS地址）");

    /**
     * The column <code>creation.creation_user_voice_locale.roles</code>. 可扮演角色
     */
    public final TableField<UserVoiceLocaleRecord, JSONB> ROLES = createField(DSL.name("roles"), SQLDataType.JSONB.nullable(false), this, "可扮演角色");

    /**
     * The column <code>creation.creation_user_voice_locale.emotions</code>.
     * 可附加情绪
     */
    public final TableField<UserVoiceLocaleRecord, JSONB> EMOTIONS = createField(DSL.name("emotions"), SQLDataType.JSONB.nullable(false), this, "可附加情绪");

    /**
     * The column <code>creation.creation_user_voice_locale.ext_info</code>.
     * 扩展信息
     */
    public final TableField<UserVoiceLocaleRecord, JSONB> EXT_INFO = createField(DSL.name("ext_info"), SQLDataType.JSONB.nullable(false), this, "扩展信息");

    /**
     * The column <code>creation.creation_user_voice_locale.ordinal</code>. 排序字段
     */
    public final TableField<UserVoiceLocaleRecord, Integer> ORDINAL = createField(DSL.name("ordinal"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.field(DSL.raw("0"), SQLDataType.INTEGER)), this, "排序字段");

    /**
     * The column <code>creation.creation_user_voice_locale.lang_family</code>.
     * 语系类型
     */
    public final TableField<UserVoiceLocaleRecord, String> LANG_FAMILY = createField(DSL.name("lang_family"), SQLDataType.VARCHAR(32), this, "语系类型");

    /**
     * The column <code>creation.creation_user_voice_locale.uid</code>. 用户ID
     */
    public final TableField<UserVoiceLocaleRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT, this, "用户ID");

    /**
     * The column <code>creation.creation_user_voice_locale.voice_name</code>.
     * 本地化声音名称
     */
    public final TableField<UserVoiceLocaleRecord, String> VOICE_NAME = createField(DSL.name("voice_name"), SQLDataType.VARCHAR(64), this, "本地化声音名称");

    /**
     * The column
     * <code>creation.creation_user_voice_locale.words_per_minute</code>.
     * 本地化口音语速
     */
    public final TableField<UserVoiceLocaleRecord, Integer> WORDS_PER_MINUTE = createField(DSL.name("words_per_minute"), SQLDataType.INTEGER, this, "本地化口音语速");

    private UserVoiceLocaleTable(Name alias, Table<UserVoiceLocaleRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private UserVoiceLocaleTable(Name alias, Table<UserVoiceLocaleRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("声音定制的口音"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_user_voice_locale</code> table
     * reference
     */
    public UserVoiceLocaleTable(String alias) {
        this(DSL.name(alias), CREATION_USER_VOICE_LOCALE);
    }

    /**
     * Create an aliased <code>creation.creation_user_voice_locale</code> table
     * reference
     */
    public UserVoiceLocaleTable(Name alias) {
        this(alias, CREATION_USER_VOICE_LOCALE);
    }

    /**
     * Create a <code>creation.creation_user_voice_locale</code> table reference
     */
    public UserVoiceLocaleTable() {
        this(DSL.name("creation_user_voice_locale"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public Identity<UserVoiceLocaleRecord, Long> getIdentity() {
        return (Identity<UserVoiceLocaleRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<UserVoiceLocaleRecord> getPrimaryKey() {
        return Keys.CREATION_USER_VOICE_LOCALE_PKEY;
    }

    @Override
    public List<UniqueKey<UserVoiceLocaleRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CREATION_USER_VOICE_LOCALE_UID_VOICE_CODE_CODE_KEY, Keys.CREATION_USER_VOICE_LOCALE_VOICE_ID_CODE_KEY);
    }

    @Override
    public UserVoiceLocaleTable as(String alias) {
        return new UserVoiceLocaleTable(DSL.name(alias), this);
    }

    @Override
    public UserVoiceLocaleTable as(Name alias) {
        return new UserVoiceLocaleTable(alias, this);
    }

    @Override
    public UserVoiceLocaleTable as(Table<?> alias) {
        return new UserVoiceLocaleTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserVoiceLocaleTable rename(String name) {
        return new UserVoiceLocaleTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserVoiceLocaleTable rename(Name name) {
        return new UserVoiceLocaleTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserVoiceLocaleTable rename(Table<?> name) {
        return new UserVoiceLocaleTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserVoiceLocaleTable where(Condition condition) {
        return new UserVoiceLocaleTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserVoiceLocaleTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserVoiceLocaleTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserVoiceLocaleTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserVoiceLocaleTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserVoiceLocaleTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserVoiceLocaleTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public UserVoiceLocaleTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserVoiceLocaleTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public UserVoiceLocaleTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
