/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.ContentTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationContent;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 星耀内容大宽表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class ContentRecord extends UpdatableRecordImpl<ContentRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_content.id</code>. 主键
     */
    public ContentRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_content.created_at</code>. 创建时间
     */
    public ContentRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_content.updated_at</code>. 更新时间
     */
    public ContentRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_content.source</code>. 三方平台方
     */
    public ContentRecord setSource(@Nullable String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.source</code>. 三方平台方
     */
    @Nullable
    public String getSource() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_content.source_content_url</code>.
     * 三方平台内容链接
     */
    public ContentRecord setSourceContentUrl(@Nullable String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.source_content_url</code>.
     * 三方平台内容链接
     */
    @Nullable
    public String getSourceContentUrl() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_content.source_category</code>. 三方平台行业
     */
    public ContentRecord setSourceCategory(@Nullable String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.source_category</code>. 三方平台行业
     */
    @Nullable
    public String getSourceCategory() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_content.title</code>. 标题
     */
    public ContentRecord setTitle(@Nullable String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.title</code>. 标题
     */
    @Nullable
    public String getTitle() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_content.content</code>. 内容
     */
    public ContentRecord setContent(@Nullable String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.content</code>. 内容
     */
    @Nullable
    public String getContent() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_content.category</code>. 行业
     */
    public ContentRecord setCategory(@Nullable String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.category</code>. 行业
     */
    @Nullable
    public String getCategory() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_content.author</code>. 作者
     */
    public ContentRecord setAuthor(@Nullable JSONB value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.author</code>. 作者
     */
    @Nullable
    public JSONB getAuthor() {
        return (JSONB) get(9);
    }

    /**
     * Setter for <code>creation.creation_content.publish_time</code>. 发布时间
     */
    public ContentRecord setPublishTime(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.publish_time</code>. 发布时间
     */
    public String getPublishTime() {
        return (String) get(10);
    }

    /**
     * Setter for <code>creation.creation_content.status</code>. 当前状态
     */
    public ContentRecord setStatus(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.status</code>. 当前状态
     */
    public String getStatus() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_content.tags</code>. 标签
     */
    public ContentRecord setTags(@Nullable JSONB[] value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.tags</code>. 标签
     */
    @Nullable
    public JSONB[] getTags() {
        return (JSONB[]) get(12);
    }

    /**
     * Setter for <code>creation.creation_content.views</code>. 总观看量
     */
    public ContentRecord setViews(Long value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_content.views</code>. 总观看量
     */
    public Long getViews() {
        return (Long) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ContentRecord
     */
    public ContentRecord() {
        super(ContentTable.CREATION_CONTENT);
    }

    /**
     * Create a detached, initialised ContentRecord
     */
    public ContentRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable String source, @Nullable String sourceContentUrl, @Nullable String sourceCategory, @Nullable String title, @Nullable String content, @Nullable String category, @Nullable JSONB author, String publishTime, String status, @Nullable JSONB[] tags, Long views) {
        super(ContentTable.CREATION_CONTENT);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setSource(source);
        setSourceContentUrl(sourceContentUrl);
        setSourceCategory(sourceCategory);
        setTitle(title);
        setContent(content);
        setCategory(category);
        setAuthor(author);
        setPublishTime(publishTime);
        setStatus(status);
        setTags(tags);
        setViews(views);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised ContentRecord
     */
    public ContentRecord(CreationContent value) {
        super(ContentTable.CREATION_CONTENT);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setSource(value.getSource());
            setSourceContentUrl(value.getSourceContentUrl());
            setSourceCategory(value.getSourceCategory());
            setTitle(value.getTitle());
            setContent(value.getContent());
            setCategory(value.getCategory());
            setAuthor(value.getAuthor());
            setPublishTime(value.getPublishTime());
            setStatus(value.getStatus());
            setTags(value.getTags());
            setViews(value.getViews());
            resetChangedOnNotNull();
        }
    }
}
