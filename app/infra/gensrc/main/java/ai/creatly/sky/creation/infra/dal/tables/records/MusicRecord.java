/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.MusicTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationMusic;

import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 音乐库
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class MusicRecord extends UpdatableRecordImpl<MusicRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_music.id</code>. 主键ID
     */
    public MusicRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.id</code>. 主键ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_music.created_at</code>. 创建时间
     */
    public MusicRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_music.updated_at</code>. 更新时间
     */
    public MusicRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_music.uid</code>. 所属用户ID（1为平台）
     */
    public MusicRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.uid</code>. 所属用户ID（1为平台）
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_music.status</code>. 状态
     */
    public MusicRecord setStatus(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.status</code>. 状态
     */
    public String getStatus() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_music.source_type</code>. 来源方类型
     */
    public MusicRecord setSourceType(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.source_type</code>. 来源方类型
     */
    public String getSourceType() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_music.source_no</code>. 来源方的音乐编号
     */
    public MusicRecord setSourceNo(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.source_no</code>. 来源方的音乐编号
     */
    public String getSourceNo() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_music.author_name</code>. 音乐作者
     */
    public MusicRecord setAuthorName(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.author_name</code>. 音乐作者
     */
    public String getAuthorName() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_music.name</code>. 音乐名称
     */
    public MusicRecord setName(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.name</code>. 音乐名称
     */
    public String getName() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_music.tag_names</code>. 音乐标签名列表
     */
    public MusicRecord setTagNames(String[] value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.tag_names</code>. 音乐标签名列表
     */
    public String[] getTagNames() {
        return (String[]) get(9);
    }

    /**
     * Setter for <code>creation.creation_music.audio_id</code>. 音效文件ID
     */
    public MusicRecord setAudioId(Long value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.audio_id</code>. 音效文件ID
     */
    public Long getAudioId() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>creation.creation_music.audio_url</code>. 音乐文件地址（OSS格式）
     */
    public MusicRecord setAudioUrl(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.audio_url</code>. 音乐文件地址（OSS格式）
     */
    public String getAudioUrl() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_music.audio_format</code>. 音乐文件格式（小写）
     */
    public MusicRecord setAudioFormat(String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.audio_format</code>. 音乐文件格式（小写）
     */
    public String getAudioFormat() {
        return (String) get(12);
    }

    /**
     * Setter for <code>creation.creation_music.cover_image_id</code>. 封面图文件ID
     */
    public MusicRecord setCoverImageId(@Nullable Long value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.cover_image_id</code>. 封面图文件ID
     */
    @Nullable
    public Long getCoverImageId() {
        return (Long) get(13);
    }

    /**
     * Setter for <code>creation.creation_music.cover_url</code>. 封面图地址（OSS格式）
     */
    public MusicRecord setCoverUrl(@Nullable String value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.cover_url</code>. 封面图地址（OSS格式）
     */
    @Nullable
    public String getCoverUrl() {
        return (String) get(14);
    }

    /**
     * Setter for <code>creation.creation_music.duration</code>. 音乐时长
     */
    public MusicRecord setDuration(Duration value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.duration</code>. 音乐时长
     */
    public Duration getDuration() {
        return (Duration) get(15);
    }

    /**
     * Setter for <code>creation.creation_music.bitrate</code>. 比特率
     */
    public MusicRecord setBitrate(Integer value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.bitrate</code>. 比特率
     */
    public Integer getBitrate() {
        return (Integer) get(16);
    }

    /**
     * Setter for <code>creation.creation_music.sample_rate</code>. 采样率
     */
    public MusicRecord setSampleRate(Integer value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.sample_rate</code>. 采样率
     */
    public Integer getSampleRate() {
        return (Integer) get(17);
    }

    /**
     * Setter for <code>creation.creation_music.bpm</code>. 每分钟节拍
     */
    public MusicRecord setBpm(Integer value) {
        set(18, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.bpm</code>. 每分钟节拍
     */
    public Integer getBpm() {
        return (Integer) get(18);
    }

    /**
     * Setter for <code>creation.creation_music.creator</code>. 创建者
     */
    public MusicRecord setCreator(JSONB value) {
        set(19, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return (JSONB) get(19);
    }

    /**
     * Setter for <code>creation.creation_music.updater</code>. 更新者
     */
    public MusicRecord setUpdater(JSONB value) {
        set(20, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_music.updater</code>. 更新者
     */
    public JSONB getUpdater() {
        return (JSONB) get(20);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached MusicRecord
     */
    public MusicRecord() {
        super(MusicTable.CREATION_MUSIC);
    }

    /**
     * Create a detached, initialised MusicRecord
     */
    public MusicRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, String status, String sourceType, String sourceNo, String authorName, String name, String[] tagNames, Long audioId, String audioUrl, String audioFormat, @Nullable Long coverImageId, @Nullable String coverUrl, Duration duration, Integer bitrate, Integer sampleRate, Integer bpm, JSONB creator, JSONB updater) {
        super(MusicTable.CREATION_MUSIC);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setStatus(status);
        setSourceType(sourceType);
        setSourceNo(sourceNo);
        setAuthorName(authorName);
        setName(name);
        setTagNames(tagNames);
        setAudioId(audioId);
        setAudioUrl(audioUrl);
        setAudioFormat(audioFormat);
        setCoverImageId(coverImageId);
        setCoverUrl(coverUrl);
        setDuration(duration);
        setBitrate(bitrate);
        setSampleRate(sampleRate);
        setBpm(bpm);
        setCreator(creator);
        setUpdater(updater);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised MusicRecord
     */
    public MusicRecord(CreationMusic value) {
        super(MusicTable.CREATION_MUSIC);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setStatus(value.getStatus());
            setSourceType(value.getSourceType());
            setSourceNo(value.getSourceNo());
            setAuthorName(value.getAuthorName());
            setName(value.getName());
            setTagNames(value.getTagNames());
            setAudioId(value.getAudioId());
            setAudioUrl(value.getAudioUrl());
            setAudioFormat(value.getAudioFormat());
            setCoverImageId(value.getCoverImageId());
            setCoverUrl(value.getCoverUrl());
            setDuration(value.getDuration());
            setBitrate(value.getBitrate());
            setSampleRate(value.getSampleRate());
            setBpm(value.getBpm());
            setCreator(value.getCreator());
            setUpdater(value.getUpdater());
            resetChangedOnNotNull();
        }
    }
}
