/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.AiToolRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * AI工具集
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiToolTable extends TableImpl<AiToolRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_ai_tool</code>
     */
    public static final AiToolTable CREATION_AI_TOOL = new AiToolTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AiToolRecord> getRecordType() {
        return AiToolRecord.class;
    }

    /**
     * The column <code>creation.creation_ai_tool.id</code>. id
     */
    public final TableField<AiToolRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "id");

    /**
     * The column <code>creation.creation_ai_tool.created_at</code>. 创建时间
     */
    public final TableField<AiToolRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_tool.updated_at</code>. 更新时间
     */
    public final TableField<AiToolRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_ai_tool.title</code>. 工具名称
     */
    public final TableField<AiToolRecord, String> TITLE = createField(DSL.name("title"), SQLDataType.VARCHAR(255), this, "工具名称");

    /**
     * The column <code>creation.creation_ai_tool.cover_url</code>. 工具封面
     */
    public final TableField<AiToolRecord, String> COVER_URL = createField(DSL.name("cover_url"), SQLDataType.VARCHAR(255), this, "工具封面");

    /**
     * The column <code>creation.creation_ai_tool.priority</code>. 推荐优先级
     */
    public final TableField<AiToolRecord, Long> PRIORITY = createField(DSL.name("priority"), SQLDataType.BIGINT, this, "推荐优先级");

    /**
     * The column <code>creation.creation_ai_tool.is_hot</code>. 是否推荐
     */
    public final TableField<AiToolRecord, Short> IS_HOT = createField(DSL.name("is_hot"), SQLDataType.SMALLINT, this, "是否推荐");

    /**
     * The column <code>creation.creation_ai_tool.status</code>. 启用状态
     */
    public final TableField<AiToolRecord, Short> STATUS = createField(DSL.name("status"), SQLDataType.SMALLINT, this, "启用状态");

    /**
     * The column <code>creation.creation_ai_tool.hidden</code>. 是否显示
     */
    public final TableField<AiToolRecord, Short> HIDDEN = createField(DSL.name("hidden"), SQLDataType.SMALLINT, this, "是否显示");

    /**
     * The column <code>creation.creation_ai_tool.path</code>. 跳转路径
     */
    public final TableField<AiToolRecord, String> PATH = createField(DSL.name("path"), SQLDataType.VARCHAR(255), this, "跳转路径");

    /**
     * The column <code>creation.creation_ai_tool.sub_title</code>. 副标题
     */
    public final TableField<AiToolRecord, String> SUB_TITLE = createField(DSL.name("sub_title"), SQLDataType.VARCHAR(255), this, "副标题");

    /**
     * The column <code>creation.creation_ai_tool.init_param</code>. 初始化参数
     */
    public final TableField<AiToolRecord, String> INIT_PARAM = createField(DSL.name("init_param"), SQLDataType.CLOB, this, "初始化参数");

    /**
     * The column <code>creation.creation_ai_tool.category</code>. 工具类目
     */
    public final TableField<AiToolRecord, String> CATEGORY = createField(DSL.name("category"), SQLDataType.VARCHAR(255), this, "工具类目");

    /**
     * The column <code>creation.creation_ai_tool.credits</code>. 消耗元气
     */
    public final TableField<AiToolRecord, Long> CREDITS = createField(DSL.name("credits"), SQLDataType.BIGINT, this, "消耗元气");

    /**
     * The column <code>creation.creation_ai_tool.model</code>. 模型
     */
    public final TableField<AiToolRecord, String> MODEL = createField(DSL.name("model"), SQLDataType.VARCHAR(255), this, "模型");

    /**
     * The column <code>creation.creation_ai_tool.biz_type</code>. 类型
     */
    public final TableField<AiToolRecord, String> BIZ_TYPE = createField(DSL.name("biz_type"), SQLDataType.VARCHAR(255), this, "类型");

    /**
     * The column <code>creation.creation_ai_tool.function</code>. 功能
     */
    public final TableField<AiToolRecord, String> FUNCTION = createField(DSL.name("function"), SQLDataType.VARCHAR(255), this, "功能");

    private AiToolTable(Name alias, Table<AiToolRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private AiToolTable(Name alias, Table<AiToolRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("AI工具集"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_ai_tool</code> table reference
     */
    public AiToolTable(String alias) {
        this(DSL.name(alias), CREATION_AI_TOOL);
    }

    /**
     * Create an aliased <code>creation.creation_ai_tool</code> table reference
     */
    public AiToolTable(Name alias) {
        this(alias, CREATION_AI_TOOL);
    }

    /**
     * Create a <code>creation.creation_ai_tool</code> table reference
     */
    public AiToolTable() {
        this(DSL.name("creation_ai_tool"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<AiToolRecord> getPrimaryKey() {
        return Keys.CREATION_AI_TOOL_PKEY;
    }

    @Override
    public AiToolTable as(String alias) {
        return new AiToolTable(DSL.name(alias), this);
    }

    @Override
    public AiToolTable as(Name alias) {
        return new AiToolTable(alias, this);
    }

    @Override
    public AiToolTable as(Table<?> alias) {
        return new AiToolTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public AiToolTable rename(String name) {
        return new AiToolTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public AiToolTable rename(Name name) {
        return new AiToolTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public AiToolTable rename(Table<?> name) {
        return new AiToolTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiToolTable where(Condition condition) {
        return new AiToolTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiToolTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiToolTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiToolTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiToolTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiToolTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiToolTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AiToolTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiToolTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AiToolTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
