/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 课程
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationBusinessOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String coverUrl;
    private String videoUrl;
    private String title;
    private String orderIntro;
    private String orderStatus;
    private String orderType;
    private String subtitle;
    private Long orderPrice;
    private ZonedDateTime beginTime;
    private ZonedDateTime endTime;
    private Integer priority;
    private Long ownerId;
    private String ownerName;

    public CreationBusinessOrder() {}

    public CreationBusinessOrder(CreationBusinessOrder value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.coverUrl = value.coverUrl;
        this.videoUrl = value.videoUrl;
        this.title = value.title;
        this.orderIntro = value.orderIntro;
        this.orderStatus = value.orderStatus;
        this.orderType = value.orderType;
        this.subtitle = value.subtitle;
        this.orderPrice = value.orderPrice;
        this.beginTime = value.beginTime;
        this.endTime = value.endTime;
        this.priority = value.priority;
        this.ownerId = value.ownerId;
        this.ownerName = value.ownerName;
    }

    public CreationBusinessOrder(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        @Nullable String coverUrl,
        @Nullable String videoUrl,
        @Nullable String title,
        @Nullable String orderIntro,
        @Nullable String orderStatus,
        @Nullable String orderType,
        @Nullable String subtitle,
        @Nullable Long orderPrice,
        @Nullable ZonedDateTime beginTime,
        @Nullable ZonedDateTime endTime,
        @Nullable Integer priority,
        @Nullable Long ownerId,
        @Nullable String ownerName
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.coverUrl = coverUrl;
        this.videoUrl = videoUrl;
        this.title = title;
        this.orderIntro = orderIntro;
        this.orderStatus = orderStatus;
        this.orderType = orderType;
        this.subtitle = subtitle;
        this.orderPrice = orderPrice;
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.priority = priority;
        this.ownerId = ownerId;
        this.ownerName = ownerName;
    }

    /**
     * Getter for <code>creation.creation_business_order.id</code>. 商单id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_business_order.id</code>. 商单id
     */
    public CreationBusinessOrder setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_business_order.created_at</code>. 创建时间
     */
    public CreationBusinessOrder setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_business_order.updated_at</code>. 更新时间
     */
    public CreationBusinessOrder setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.cover_url</code>. 商单封面
     */
    @Nullable
    public String getCoverUrl() {
        return this.coverUrl;
    }

    /**
     * Setter for <code>creation.creation_business_order.cover_url</code>. 商单封面
     */
    public CreationBusinessOrder setCoverUrl(@Nullable String coverUrl) {
        this.coverUrl = coverUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.video_url</code>. 视频链接
     */
    @Nullable
    public String getVideoUrl() {
        return this.videoUrl;
    }

    /**
     * Setter for <code>creation.creation_business_order.video_url</code>. 视频链接
     */
    public CreationBusinessOrder setVideoUrl(@Nullable String videoUrl) {
        this.videoUrl = videoUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.title</code>. 商单名称
     */
    @Nullable
    public String getTitle() {
        return this.title;
    }

    /**
     * Setter for <code>creation.creation_business_order.title</code>. 商单名称
     */
    public CreationBusinessOrder setTitle(@Nullable String title) {
        this.title = title;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.order_intro</code>.
     * 商单介绍
     */
    @Nullable
    public String getOrderIntro() {
        return this.orderIntro;
    }

    /**
     * Setter for <code>creation.creation_business_order.order_intro</code>.
     * 商单介绍
     */
    public CreationBusinessOrder setOrderIntro(@Nullable String orderIntro) {
        this.orderIntro = orderIntro;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.order_status</code>.
     * 商单状态
     */
    @Nullable
    public String getOrderStatus() {
        return this.orderStatus;
    }

    /**
     * Setter for <code>creation.creation_business_order.order_status</code>.
     * 商单状态
     */
    public CreationBusinessOrder setOrderStatus(@Nullable String orderStatus) {
        this.orderStatus = orderStatus;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.order_type</code>. 商单类型
     */
    @Nullable
    public String getOrderType() {
        return this.orderType;
    }

    /**
     * Setter for <code>creation.creation_business_order.order_type</code>. 商单类型
     */
    public CreationBusinessOrder setOrderType(@Nullable String orderType) {
        this.orderType = orderType;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.subtitle</code>. 副标题
     */
    @Nullable
    public String getSubtitle() {
        return this.subtitle;
    }

    /**
     * Setter for <code>creation.creation_business_order.subtitle</code>. 副标题
     */
    public CreationBusinessOrder setSubtitle(@Nullable String subtitle) {
        this.subtitle = subtitle;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.order_price</code>.
     * 商单价格
     */
    @Nullable
    public Long getOrderPrice() {
        return this.orderPrice;
    }

    /**
     * Setter for <code>creation.creation_business_order.order_price</code>.
     * 商单价格
     */
    public CreationBusinessOrder setOrderPrice(@Nullable Long orderPrice) {
        this.orderPrice = orderPrice;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.begin_time</code>. 开始时间
     */
    @Nullable
    public ZonedDateTime getBeginTime() {
        return this.beginTime;
    }

    /**
     * Setter for <code>creation.creation_business_order.begin_time</code>. 开始时间
     */
    public CreationBusinessOrder setBeginTime(@Nullable ZonedDateTime beginTime) {
        this.beginTime = beginTime;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.end_time</code>. 结束时间
     */
    @Nullable
    public ZonedDateTime getEndTime() {
        return this.endTime;
    }

    /**
     * Setter for <code>creation.creation_business_order.end_time</code>. 结束时间
     */
    public CreationBusinessOrder setEndTime(@Nullable ZonedDateTime endTime) {
        this.endTime = endTime;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.priority</code>. 优先级
     */
    @Nullable
    public Integer getPriority() {
        return this.priority;
    }

    /**
     * Setter for <code>creation.creation_business_order.priority</code>. 优先级
     */
    public CreationBusinessOrder setPriority(@Nullable Integer priority) {
        this.priority = priority;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    @Nullable
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * Setter for <code>creation.creation_business_order.owner_id</code>.
     * 任务所有者ID（1表示系统）
     */
    public CreationBusinessOrder setOwnerId(@Nullable Long ownerId) {
        this.ownerId = ownerId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_business_order.owner_name</code>.
     * 任务所有者名称
     */
    @Nullable
    public String getOwnerName() {
        return this.ownerName;
    }

    /**
     * Setter for <code>creation.creation_business_order.owner_name</code>.
     * 任务所有者名称
     */
    public CreationBusinessOrder setOwnerName(@Nullable String ownerName) {
        this.ownerName = ownerName;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationBusinessOrder other = (CreationBusinessOrder) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.coverUrl == null) {
            if (other.coverUrl != null)
                return false;
        }
        else if (!this.coverUrl.equals(other.coverUrl))
            return false;
        if (this.videoUrl == null) {
            if (other.videoUrl != null)
                return false;
        }
        else if (!this.videoUrl.equals(other.videoUrl))
            return false;
        if (this.title == null) {
            if (other.title != null)
                return false;
        }
        else if (!this.title.equals(other.title))
            return false;
        if (this.orderIntro == null) {
            if (other.orderIntro != null)
                return false;
        }
        else if (!this.orderIntro.equals(other.orderIntro))
            return false;
        if (this.orderStatus == null) {
            if (other.orderStatus != null)
                return false;
        }
        else if (!this.orderStatus.equals(other.orderStatus))
            return false;
        if (this.orderType == null) {
            if (other.orderType != null)
                return false;
        }
        else if (!this.orderType.equals(other.orderType))
            return false;
        if (this.subtitle == null) {
            if (other.subtitle != null)
                return false;
        }
        else if (!this.subtitle.equals(other.subtitle))
            return false;
        if (this.orderPrice == null) {
            if (other.orderPrice != null)
                return false;
        }
        else if (!this.orderPrice.equals(other.orderPrice))
            return false;
        if (this.beginTime == null) {
            if (other.beginTime != null)
                return false;
        }
        else if (!this.beginTime.equals(other.beginTime))
            return false;
        if (this.endTime == null) {
            if (other.endTime != null)
                return false;
        }
        else if (!this.endTime.equals(other.endTime))
            return false;
        if (this.priority == null) {
            if (other.priority != null)
                return false;
        }
        else if (!this.priority.equals(other.priority))
            return false;
        if (this.ownerId == null) {
            if (other.ownerId != null)
                return false;
        }
        else if (!this.ownerId.equals(other.ownerId))
            return false;
        if (this.ownerName == null) {
            if (other.ownerName != null)
                return false;
        }
        else if (!this.ownerName.equals(other.ownerName))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.coverUrl == null) ? 0 : this.coverUrl.hashCode());
        result = prime * result + ((this.videoUrl == null) ? 0 : this.videoUrl.hashCode());
        result = prime * result + ((this.title == null) ? 0 : this.title.hashCode());
        result = prime * result + ((this.orderIntro == null) ? 0 : this.orderIntro.hashCode());
        result = prime * result + ((this.orderStatus == null) ? 0 : this.orderStatus.hashCode());
        result = prime * result + ((this.orderType == null) ? 0 : this.orderType.hashCode());
        result = prime * result + ((this.subtitle == null) ? 0 : this.subtitle.hashCode());
        result = prime * result + ((this.orderPrice == null) ? 0 : this.orderPrice.hashCode());
        result = prime * result + ((this.beginTime == null) ? 0 : this.beginTime.hashCode());
        result = prime * result + ((this.endTime == null) ? 0 : this.endTime.hashCode());
        result = prime * result + ((this.priority == null) ? 0 : this.priority.hashCode());
        result = prime * result + ((this.ownerId == null) ? 0 : this.ownerId.hashCode());
        result = prime * result + ((this.ownerName == null) ? 0 : this.ownerName.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationBusinessOrder (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(coverUrl);
        sb.append(", ").append(videoUrl);
        sb.append(", ").append(title);
        sb.append(", ").append(orderIntro);
        sb.append(", ").append(orderStatus);
        sb.append(", ").append(orderType);
        sb.append(", ").append(subtitle);
        sb.append(", ").append(orderPrice);
        sb.append(", ").append(beginTime);
        sb.append(", ").append(endTime);
        sb.append(", ").append(priority);
        sb.append(", ").append(ownerId);
        sb.append(", ").append(ownerName);

        sb.append(")");
        return sb.toString();
    }
}
