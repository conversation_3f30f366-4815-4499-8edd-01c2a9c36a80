/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.LlmConfigRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * LLM配置表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class LlmConfigTable extends TableImpl<LlmConfigRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_llm_config</code>
     */
    public static final LlmConfigTable CREATION_LLM_CONFIG = new LlmConfigTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LlmConfigRecord> getRecordType() {
        return LlmConfigRecord.class;
    }

    /**
     * The column <code>creation.creation_llm_config.id</code>. 主键
     */
    public final TableField<LlmConfigRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_llm_config.created_at</code>. 创建时间
     */
    public final TableField<LlmConfigRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_llm_config.updated_at</code>. 更新时间
     */
    public final TableField<LlmConfigRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_llm_config.name</code>. 模型名称
     */
    public final TableField<LlmConfigRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(255).nullable(false), this, "模型名称");

    /**
     * The column <code>creation.creation_llm_config.description</code>. 模型描述
     */
    public final TableField<LlmConfigRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(255).defaultValue(DSL.field(DSL.raw("NULL::character varying"), SQLDataType.VARCHAR)), this, "模型描述");

    /**
     * The column <code>creation.creation_llm_config.status</code>. 当前状态
     */
    public final TableField<LlmConfigRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(16).nullable(false), this, "当前状态");

    /**
     * The column <code>creation.creation_llm_config.config</code>. 配置
     */
    public final TableField<LlmConfigRecord, JSONB> CONFIG = createField(DSL.name("config"), SQLDataType.JSONB.nullable(false), this, "配置");

    private LlmConfigTable(Name alias, Table<LlmConfigRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private LlmConfigTable(Name alias, Table<LlmConfigRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("LLM配置表"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_llm_config</code> table
     * reference
     */
    public LlmConfigTable(String alias) {
        this(DSL.name(alias), CREATION_LLM_CONFIG);
    }

    /**
     * Create an aliased <code>creation.creation_llm_config</code> table
     * reference
     */
    public LlmConfigTable(Name alias) {
        this(alias, CREATION_LLM_CONFIG);
    }

    /**
     * Create a <code>creation.creation_llm_config</code> table reference
     */
    public LlmConfigTable() {
        this(DSL.name("creation_llm_config"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<LlmConfigRecord> getPrimaryKey() {
        return Keys.CREATION_LLM_CONFIG_PKEY;
    }

    @Override
    public LlmConfigTable as(String alias) {
        return new LlmConfigTable(DSL.name(alias), this);
    }

    @Override
    public LlmConfigTable as(Name alias) {
        return new LlmConfigTable(alias, this);
    }

    @Override
    public LlmConfigTable as(Table<?> alias) {
        return new LlmConfigTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public LlmConfigTable rename(String name) {
        return new LlmConfigTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public LlmConfigTable rename(Name name) {
        return new LlmConfigTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public LlmConfigTable rename(Table<?> name) {
        return new LlmConfigTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public LlmConfigTable where(Condition condition) {
        return new LlmConfigTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public LlmConfigTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public LlmConfigTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public LlmConfigTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public LlmConfigTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public LlmConfigTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public LlmConfigTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public LlmConfigTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public LlmConfigTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public LlmConfigTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
