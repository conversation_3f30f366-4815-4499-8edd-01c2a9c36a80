/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * AI Talk 演讲者
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationAiTalkActor implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String name;
    private Long imageId;
    private String imageUrl;
    private JSONB creator;

    public CreationAiTalkActor() {}

    public CreationAiTalkActor(CreationAiTalkActor value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.name = value.name;
        this.imageId = value.imageId;
        this.imageUrl = value.imageUrl;
        this.creator = value.creator;
    }

    public CreationAiTalkActor(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String name,
        Long imageId,
        String imageUrl,
        JSONB creator
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.name = name;
        this.imageId = imageId;
        this.imageUrl = imageUrl;
        this.creator = creator;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.id</code>. 主键
     */
    public CreationAiTalkActor setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.created_at</code>. 创建时间
     */
    public CreationAiTalkActor setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.updated_at</code>. 更新时间
     */
    public CreationAiTalkActor setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.uid</code>.
     * 所属用户ID（1表示系统）
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.uid</code>.
     * 所属用户ID（1表示系统）
     */
    public CreationAiTalkActor setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.name</code>. 名称
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.name</code>. 名称
     */
    public CreationAiTalkActor setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.image_id</code>. 图片文件ID
     */
    public Long getImageId() {
        return this.imageId;
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.image_id</code>. 图片文件ID
     */
    public CreationAiTalkActor setImageId(Long imageId) {
        this.imageId = imageId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.image_url</code>.
     * 图片路径（冗余OSS协议的文件地址）
     */
    public String getImageUrl() {
        return this.imageUrl;
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.image_url</code>.
     * 图片路径（冗余OSS协议的文件地址）
     */
    public CreationAiTalkActor setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_talk_actor.creator</code>. 创建者
     */
    public JSONB getCreator() {
        return this.creator;
    }

    /**
     * Setter for <code>creation.creation_ai_talk_actor.creator</code>. 创建者
     */
    public CreationAiTalkActor setCreator(JSONB creator) {
        this.creator = creator;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationAiTalkActor other = (CreationAiTalkActor) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.imageId == null) {
            if (other.imageId != null)
                return false;
        }
        else if (!this.imageId.equals(other.imageId))
            return false;
        if (this.imageUrl == null) {
            if (other.imageUrl != null)
                return false;
        }
        else if (!this.imageUrl.equals(other.imageUrl))
            return false;
        if (this.creator == null) {
            if (other.creator != null)
                return false;
        }
        else if (!this.creator.equals(other.creator))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.imageId == null) ? 0 : this.imageId.hashCode());
        result = prime * result + ((this.imageUrl == null) ? 0 : this.imageUrl.hashCode());
        result = prime * result + ((this.creator == null) ? 0 : this.creator.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationAiTalkActor (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(name);
        sb.append(", ").append(imageId);
        sb.append(", ").append(imageUrl);
        sb.append(", ").append(creator);

        sb.append(")");
        return sb.toString();
    }
}
