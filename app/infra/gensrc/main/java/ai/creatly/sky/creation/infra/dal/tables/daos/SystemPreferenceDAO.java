/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.SystemPreferenceTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.SystemPreference;
import ai.creatly.sky.creation.infra.dal.tables.records.SystemPreferenceRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 系统配置中心
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class SystemPreferenceDAO extends DAOImpl<SystemPreferenceRecord, SystemPreference, Long> {

    /**
     * Create a new SystemPreferenceDAO without any configuration
     */
    public SystemPreferenceDAO() {
        super(SystemPreferenceTable.SYSTEM_PREFERENCE, SystemPreference.class);
    }

    /**
     * Create a new SystemPreferenceDAO with an attached configuration
     */
    @Autowired
    public SystemPreferenceDAO(Configuration configuration) {
        super(SystemPreferenceTable.SYSTEM_PREFERENCE, SystemPreference.class, configuration);
    }

    @Override
    public Long getId(SystemPreference object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<SystemPreference> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(SystemPreferenceTable.SYSTEM_PREFERENCE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<SystemPreference> fetchById(Long... values) {
        return fetch(SystemPreferenceTable.SYSTEM_PREFERENCE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public SystemPreference fetchOneById(Long value) {
        return fetchOne(SystemPreferenceTable.SYSTEM_PREFERENCE.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<SystemPreference> fetchOptionalById(Long value) {
        return fetchOptional(SystemPreferenceTable.SYSTEM_PREFERENCE.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<SystemPreference> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(SystemPreferenceTable.SYSTEM_PREFERENCE.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<SystemPreference> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(SystemPreferenceTable.SYSTEM_PREFERENCE.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<SystemPreference> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(SystemPreferenceTable.SYSTEM_PREFERENCE.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<SystemPreference> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(SystemPreferenceTable.SYSTEM_PREFERENCE.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>config_key BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<SystemPreference> fetchRangeOfConfigKey(String lowerInclusive, String upperInclusive) {
        return fetchRange(SystemPreferenceTable.SYSTEM_PREFERENCE.CONFIG_KEY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>config_key IN (values)</code>
     */
    public List<SystemPreference> fetchByConfigKey(String... values) {
        return fetch(SystemPreferenceTable.SYSTEM_PREFERENCE.CONFIG_KEY, values);
    }

    /**
     * Fetch records that have <code>config_value BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<SystemPreference> fetchRangeOfConfigValue(String lowerInclusive, String upperInclusive) {
        return fetchRange(SystemPreferenceTable.SYSTEM_PREFERENCE.CONFIG_VALUE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>config_value IN (values)</code>
     */
    public List<SystemPreference> fetchByConfigValue(String... values) {
        return fetch(SystemPreferenceTable.SYSTEM_PREFERENCE.CONFIG_VALUE, values);
    }

    /**
     * Fetch records that have <code>operator BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<SystemPreference> fetchRangeOfOperator(String lowerInclusive, String upperInclusive) {
        return fetchRange(SystemPreferenceTable.SYSTEM_PREFERENCE.OPERATOR, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>operator IN (values)</code>
     */
    public List<SystemPreference> fetchByOperator(String... values) {
        return fetch(SystemPreferenceTable.SYSTEM_PREFERENCE.OPERATOR, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<SystemPreference> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(SystemPreferenceTable.SYSTEM_PREFERENCE.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<SystemPreference> fetchByStatus(String... values) {
        return fetch(SystemPreferenceTable.SYSTEM_PREFERENCE.STATUS, values);
    }
}
