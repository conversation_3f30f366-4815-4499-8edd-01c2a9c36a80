/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.CourseUserCertificateTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseUserCertificate;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户证书
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseUserCertificateRecord extends UpdatableRecordImpl<CourseUserCertificateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.course_user_certificate.id</code>. 用户证书id
     */
    public CourseUserCertificateRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.id</code>. 用户证书id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.course_user_certificate.created_at</code>. 创建时间
     */
    public CourseUserCertificateRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.course_user_certificate.updated_at</code>. 更新时间
     */
    public CourseUserCertificateRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.course_user_certificate.cover_url</code>. 证书封面
     */
    public CourseUserCertificateRecord setCoverUrl(@Nullable String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.cover_url</code>. 证书封面
     */
    @Nullable
    public String getCoverUrl() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.course_user_certificate.cert_id</code>. 证书id
     */
    public CourseUserCertificateRecord setCertId(@Nullable Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.cert_id</code>. 证书id
     */
    @Nullable
    public Long getCertId() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.course_user_certificate.name</code>. 证书名称
     */
    public CourseUserCertificateRecord setName(@Nullable String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.name</code>. 证书名称
     */
    @Nullable
    public String getName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.course_user_certificate.score</code>. 总分
     */
    public CourseUserCertificateRecord setScore(@Nullable Long value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.score</code>. 总分
     */
    @Nullable
    public Long getScore() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>creation.course_user_certificate.uid</code>. 用户id
     */
    public CourseUserCertificateRecord setUid(@Nullable Long value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.uid</code>. 用户id
     */
    @Nullable
    public Long getUid() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>creation.course_user_certificate.org_code</code>. 组织机构
     */
    public CourseUserCertificateRecord setOrgCode(@Nullable String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.org_code</code>. 组织机构
     */
    @Nullable
    public String getOrgCode() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.course_user_certificate.limit_exam</code>. 限制次数
     */
    public CourseUserCertificateRecord setLimitExam(@Nullable Long value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.limit_exam</code>. 限制次数
     */
    @Nullable
    public Long getLimitExam() {
        return (Long) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseUserCertificateRecord
     */
    public CourseUserCertificateRecord() {
        super(CourseUserCertificateTable.COURSE_USER_CERTIFICATE);
    }

    /**
     * Create a detached, initialised CourseUserCertificateRecord
     */
    public CourseUserCertificateRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable String coverUrl, @Nullable Long certId, @Nullable String name, @Nullable Long score, @Nullable Long uid, @Nullable String orgCode, @Nullable Long limitExam) {
        super(CourseUserCertificateTable.COURSE_USER_CERTIFICATE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setCoverUrl(coverUrl);
        setCertId(certId);
        setName(name);
        setScore(score);
        setUid(uid);
        setOrgCode(orgCode);
        setLimitExam(limitExam);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised CourseUserCertificateRecord
     */
    public CourseUserCertificateRecord(CourseUserCertificate value) {
        super(CourseUserCertificateTable.COURSE_USER_CERTIFICATE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setCoverUrl(value.getCoverUrl());
            setCertId(value.getCertId());
            setName(value.getName());
            setScore(value.getScore());
            setUid(value.getUid());
            setOrgCode(value.getOrgCode());
            setLimitExam(value.getLimitExam());
            resetChangedOnNotNull();
        }
    }
}
