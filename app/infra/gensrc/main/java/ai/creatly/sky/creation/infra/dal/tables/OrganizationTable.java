/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.OrganizationRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 组织主表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class OrganizationTable extends TableImpl<OrganizationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.organization</code>
     */
    public static final OrganizationTable ORGANIZATION = new OrganizationTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OrganizationRecord> getRecordType() {
        return OrganizationRecord.class;
    }

    /**
     * The column <code>creation.organization.id</code>. 主键
     */
    public final TableField<OrganizationRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.organization.created_at</code>. 创建时间
     */
    public final TableField<OrganizationRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.organization.updated_at</code>. 更新时间
     */
    public final TableField<OrganizationRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.organization.code</code>. 组织标识码
     */
    public final TableField<OrganizationRecord, String> CODE = createField(DSL.name("code"), SQLDataType.VARCHAR(64).nullable(false), this, "组织标识码");

    /**
     * The column <code>creation.organization.name</code>. 组织名称
     */
    public final TableField<OrganizationRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(128).nullable(false), this, "组织名称");

    /**
     * The column <code>creation.organization.uid</code>. 企业法人
     */
    public final TableField<OrganizationRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "企业法人");

    /**
     * The column <code>creation.organization.qualification</code>. 组织资质
     */
    public final TableField<OrganizationRecord, JSONB> QUALIFICATION = createField(DSL.name("qualification"), SQLDataType.JSONB, this, "组织资质");

    /**
     * The column <code>creation.organization.logo_url</code>. logo地址
     */
    public final TableField<OrganizationRecord, String> LOGO_URL = createField(DSL.name("logo_url"), SQLDataType.VARCHAR(256), this, "logo地址");

    private OrganizationTable(Name alias, Table<OrganizationRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private OrganizationTable(Name alias, Table<OrganizationRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("组织主表"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.organization</code> table reference
     */
    public OrganizationTable(String alias) {
        this(DSL.name(alias), ORGANIZATION);
    }

    /**
     * Create an aliased <code>creation.organization</code> table reference
     */
    public OrganizationTable(Name alias) {
        this(alias, ORGANIZATION);
    }

    /**
     * Create a <code>creation.organization</code> table reference
     */
    public OrganizationTable() {
        this(DSL.name("organization"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<OrganizationRecord> getPrimaryKey() {
        return Keys.ORGANIZATION_PKEY;
    }

    @Override
    public List<UniqueKey<OrganizationRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.ORGANIZATION_CODE_KEY);
    }

    @Override
    public OrganizationTable as(String alias) {
        return new OrganizationTable(DSL.name(alias), this);
    }

    @Override
    public OrganizationTable as(Name alias) {
        return new OrganizationTable(alias, this);
    }

    @Override
    public OrganizationTable as(Table<?> alias) {
        return new OrganizationTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public OrganizationTable rename(String name) {
        return new OrganizationTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public OrganizationTable rename(Name name) {
        return new OrganizationTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public OrganizationTable rename(Table<?> name) {
        return new OrganizationTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OrganizationTable where(Condition condition) {
        return new OrganizationTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OrganizationTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OrganizationTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OrganizationTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OrganizationTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OrganizationTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OrganizationTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OrganizationTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OrganizationTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OrganizationTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
