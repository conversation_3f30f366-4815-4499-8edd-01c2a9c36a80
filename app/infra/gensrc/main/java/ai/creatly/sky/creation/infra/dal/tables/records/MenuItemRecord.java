/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.MenuItemTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationMenuItem;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 菜单项
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class MenuItemRecord extends UpdatableRecordImpl<MenuItemRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_menu_item.id</code>. 主键
     */
    public MenuItemRecord setId(@Nullable Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu_item.id</code>. 主键
     */
    @Nullable
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_menu_item.created_at</code>.
     */
    public MenuItemRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu_item.created_at</code>.
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_menu_item.updated_at</code>.
     */
    public MenuItemRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu_item.updated_at</code>.
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_menu_item.menu_id</code>. 所属菜单
     */
    public MenuItemRecord setMenuId(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu_item.menu_id</code>. 所属菜单
     */
    public Long getMenuId() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>creation.creation_menu_item.code</code>. 唯一标识
     */
    public MenuItemRecord setCode(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu_item.code</code>. 唯一标识
     */
    public String getCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_menu_item.label</code>. 显示名称
     */
    public MenuItemRecord setLabel(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu_item.label</code>. 显示名称
     */
    public String getLabel() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.creation_menu_item.icon</code>. 图标
     */
    public MenuItemRecord setIcon(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu_item.icon</code>. 图标
     */
    public String getIcon() {
        return (String) get(6);
    }

    /**
     * Setter for <code>creation.creation_menu_item.path</code>. 路径（以/开头）
     */
    public MenuItemRecord setPath(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu_item.path</code>. 路径（以/开头）
     */
    public String getPath() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_menu_item.parent_code</code>. 父标识
     */
    public MenuItemRecord setParentCode(@Nullable String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu_item.parent_code</code>. 父标识
     */
    @Nullable
    public String getParentCode() {
        return (String) get(8);
    }

    /**
     * Setter for <code>creation.creation_menu_item.status</code>.
     */
    public MenuItemRecord setStatus(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu_item.status</code>.
     */
    public String getStatus() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_menu_item.index</code>.
     */
    public MenuItemRecord setIndex(@Nullable Integer value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu_item.index</code>.
     */
    @Nullable
    public Integer getIndex() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>creation.creation_menu_item.item_tag</code>.
     */
    public MenuItemRecord setItemTag(@Nullable String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_menu_item.item_tag</code>.
     */
    @Nullable
    public String getItemTag() {
        return (String) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached MenuItemRecord
     */
    public MenuItemRecord() {
        super(MenuItemTable.CREATION_MENU_ITEM);
    }

    /**
     * Create a detached, initialised MenuItemRecord
     */
    public MenuItemRecord(@Nullable Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long menuId, String code, String label, String icon, String path, @Nullable String parentCode, String status, @Nullable Integer index, @Nullable String itemTag) {
        super(MenuItemTable.CREATION_MENU_ITEM);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setMenuId(menuId);
        setCode(code);
        setLabel(label);
        setIcon(icon);
        setPath(path);
        setParentCode(parentCode);
        setStatus(status);
        setIndex(index);
        setItemTag(itemTag);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised MenuItemRecord
     */
    public MenuItemRecord(CreationMenuItem value) {
        super(MenuItemTable.CREATION_MENU_ITEM);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setMenuId(value.getMenuId());
            setCode(value.getCode());
            setLabel(value.getLabel());
            setIcon(value.getIcon());
            setPath(value.getPath());
            setParentCode(value.getParentCode());
            setStatus(value.getStatus());
            setIndex(value.getIndex());
            setItemTag(value.getItemTag());
            resetChangedOnNotNull();
        }
    }
}
