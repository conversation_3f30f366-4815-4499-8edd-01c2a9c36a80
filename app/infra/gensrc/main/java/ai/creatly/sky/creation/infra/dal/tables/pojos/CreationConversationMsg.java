/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 场景提示词
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationConversationMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private Long conversationId;
    private Long scenarioId;
    private Long scenarioCtxNo;
    private String role;
    private String prompt;

    public CreationConversationMsg() {}

    public CreationConversationMsg(CreationConversationMsg value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.conversationId = value.conversationId;
        this.scenarioId = value.scenarioId;
        this.scenarioCtxNo = value.scenarioCtxNo;
        this.role = value.role;
        this.prompt = value.prompt;
    }

    public CreationConversationMsg(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        Long conversationId,
        Long scenarioId,
        Long scenarioCtxNo,
        String role,
        String prompt
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.conversationId = conversationId;
        this.scenarioId = scenarioId;
        this.scenarioCtxNo = scenarioCtxNo;
        this.role = role;
        this.prompt = prompt;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.id</code>. 主键
     */
    public CreationConversationMsg setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.created_at</code>.
     * 创建时间
     */
    public CreationConversationMsg setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.updated_at</code>.
     * 更新时间
     */
    public CreationConversationMsg setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.uid</code>. 用户id
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.uid</code>. 用户id
     */
    public CreationConversationMsg setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_conversation_msg.conversation_id</code>. 会话id
     */
    public Long getConversationId() {
        return this.conversationId;
    }

    /**
     * Setter for
     * <code>creation.creation_conversation_msg.conversation_id</code>. 会话id
     */
    public CreationConversationMsg setConversationId(Long conversationId) {
        this.conversationId = conversationId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.scenario_id</code>.
     * 场景码
     */
    public Long getScenarioId() {
        return this.scenarioId;
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.scenario_id</code>.
     * 场景码
     */
    public CreationConversationMsg setScenarioId(Long scenarioId) {
        this.scenarioId = scenarioId;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_conversation_msg.scenario_ctx_no</code>. 场景码关联的no
     */
    public Long getScenarioCtxNo() {
        return this.scenarioCtxNo;
    }

    /**
     * Setter for
     * <code>creation.creation_conversation_msg.scenario_ctx_no</code>. 场景码关联的no
     */
    public CreationConversationMsg setScenarioCtxNo(Long scenarioCtxNo) {
        this.scenarioCtxNo = scenarioCtxNo;
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.role</code>.
     * 提示词角色（user/ai）
     */
    public String getRole() {
        return this.role;
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.role</code>.
     * 提示词角色（user/ai）
     */
    public CreationConversationMsg setRole(String role) {
        this.role = role;
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.prompt</code>. 提示词
     */
    public String getPrompt() {
        return this.prompt;
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.prompt</code>. 提示词
     */
    public CreationConversationMsg setPrompt(String prompt) {
        this.prompt = prompt;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationConversationMsg other = (CreationConversationMsg) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.conversationId == null) {
            if (other.conversationId != null)
                return false;
        }
        else if (!this.conversationId.equals(other.conversationId))
            return false;
        if (this.scenarioId == null) {
            if (other.scenarioId != null)
                return false;
        }
        else if (!this.scenarioId.equals(other.scenarioId))
            return false;
        if (this.scenarioCtxNo == null) {
            if (other.scenarioCtxNo != null)
                return false;
        }
        else if (!this.scenarioCtxNo.equals(other.scenarioCtxNo))
            return false;
        if (this.role == null) {
            if (other.role != null)
                return false;
        }
        else if (!this.role.equals(other.role))
            return false;
        if (this.prompt == null) {
            if (other.prompt != null)
                return false;
        }
        else if (!this.prompt.equals(other.prompt))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.conversationId == null) ? 0 : this.conversationId.hashCode());
        result = prime * result + ((this.scenarioId == null) ? 0 : this.scenarioId.hashCode());
        result = prime * result + ((this.scenarioCtxNo == null) ? 0 : this.scenarioCtxNo.hashCode());
        result = prime * result + ((this.role == null) ? 0 : this.role.hashCode());
        result = prime * result + ((this.prompt == null) ? 0 : this.prompt.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationConversationMsg (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(conversationId);
        sb.append(", ").append(scenarioId);
        sb.append(", ").append(scenarioCtxNo);
        sb.append(", ").append(role);
        sb.append(", ").append(prompt);

        sb.append(")");
        return sb.toString();
    }
}
