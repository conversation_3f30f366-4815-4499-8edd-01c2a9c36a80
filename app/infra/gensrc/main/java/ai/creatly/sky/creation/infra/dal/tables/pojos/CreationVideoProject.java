/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.Duration;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 视频项目
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationVideoProject implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String status;
    private String stage;
    private String name;
    private String type;
    private JSONB assets;
    private JSONB script;
    private JSONB voiceover;
    private JSONB roles;
    private JSONB subtitleLayer;
    private JSONB avatarLayer;
    private JSONB backgroundLayer;
    private JSONB audioTracks;
    private JSONB videoTracks;
    private String aspectRatio;
    private Integer width;
    private Integer height;
    private JSONB shots;
    private ZonedDateTime composeAt;
    private Boolean composeExpired;
    private Integer composeVersion;
    private Long coverFileId;
    private String coverUrl;
    private Duration duration;
    private JSONB videos;
    private JSONB composeTasks;
    private String stageFailureReason;

    public CreationVideoProject() {}

    public CreationVideoProject(CreationVideoProject value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.status = value.status;
        this.stage = value.stage;
        this.name = value.name;
        this.type = value.type;
        this.assets = value.assets;
        this.script = value.script;
        this.voiceover = value.voiceover;
        this.roles = value.roles;
        this.subtitleLayer = value.subtitleLayer;
        this.avatarLayer = value.avatarLayer;
        this.backgroundLayer = value.backgroundLayer;
        this.audioTracks = value.audioTracks;
        this.videoTracks = value.videoTracks;
        this.aspectRatio = value.aspectRatio;
        this.width = value.width;
        this.height = value.height;
        this.shots = value.shots;
        this.composeAt = value.composeAt;
        this.composeExpired = value.composeExpired;
        this.composeVersion = value.composeVersion;
        this.coverFileId = value.coverFileId;
        this.coverUrl = value.coverUrl;
        this.duration = value.duration;
        this.videos = value.videos;
        this.composeTasks = value.composeTasks;
        this.stageFailureReason = value.stageFailureReason;
    }

    public CreationVideoProject(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String status,
        String stage,
        String name,
        String type,
        JSONB assets,
        JSONB script,
        @Nullable JSONB voiceover,
        JSONB roles,
        @Nullable JSONB subtitleLayer,
        @Nullable JSONB avatarLayer,
        @Nullable JSONB backgroundLayer,
        JSONB audioTracks,
        JSONB videoTracks,
        String aspectRatio,
        Integer width,
        Integer height,
        JSONB shots,
        @Nullable ZonedDateTime composeAt,
        Boolean composeExpired,
        @Nullable Integer composeVersion,
        @Nullable Long coverFileId,
        @Nullable String coverUrl,
        @Nullable Duration duration,
        JSONB videos,
        @Nullable JSONB composeTasks,
        @Nullable String stageFailureReason
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.status = status;
        this.stage = stage;
        this.name = name;
        this.type = type;
        this.assets = assets;
        this.script = script;
        this.voiceover = voiceover;
        this.roles = roles;
        this.subtitleLayer = subtitleLayer;
        this.avatarLayer = avatarLayer;
        this.backgroundLayer = backgroundLayer;
        this.audioTracks = audioTracks;
        this.videoTracks = videoTracks;
        this.aspectRatio = aspectRatio;
        this.width = width;
        this.height = height;
        this.shots = shots;
        this.composeAt = composeAt;
        this.composeExpired = composeExpired;
        this.composeVersion = composeVersion;
        this.coverFileId = coverFileId;
        this.coverUrl = coverUrl;
        this.duration = duration;
        this.videos = videos;
        this.composeTasks = composeTasks;
        this.stageFailureReason = stageFailureReason;
    }

    /**
     * Getter for <code>creation.creation_video_project.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_video_project.id</code>. 主键
     */
    public CreationVideoProject setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_video_project.created_at</code>. 创建时间
     */
    public CreationVideoProject setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_video_project.updated_at</code>. 更新时间
     */
    public CreationVideoProject setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.uid</code>. 用户ID
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_video_project.uid</code>. 用户ID
     */
    public CreationVideoProject setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.status</code>. 项目状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_video_project.status</code>. 项目状态
     */
    public CreationVideoProject setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.stage</code>. 项目阶段
     */
    public String getStage() {
        return this.stage;
    }

    /**
     * Setter for <code>creation.creation_video_project.stage</code>. 项目阶段
     */
    public CreationVideoProject setStage(String stage) {
        this.stage = stage;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.name</code>. 项目名
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.creation_video_project.name</code>. 项目名
     */
    public CreationVideoProject setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.type</code>.
     */
    public String getType() {
        return this.type;
    }

    /**
     * Setter for <code>creation.creation_video_project.type</code>.
     */
    public CreationVideoProject setType(String type) {
        this.type = type;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.assets</code>. 素材列表
     */
    public JSONB getAssets() {
        return this.assets;
    }

    /**
     * Setter for <code>creation.creation_video_project.assets</code>. 素材列表
     */
    public CreationVideoProject setAssets(JSONB assets) {
        this.assets = assets;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.script</code>. 视频脚本
     */
    public JSONB getScript() {
        return this.script;
    }

    /**
     * Setter for <code>creation.creation_video_project.script</code>. 视频脚本
     */
    public CreationVideoProject setScript(JSONB script) {
        this.script = script;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.voiceover</code>. 视频配音
     */
    @Nullable
    public JSONB getVoiceover() {
        return this.voiceover;
    }

    /**
     * Setter for <code>creation.creation_video_project.voiceover</code>. 视频配音
     */
    public CreationVideoProject setVoiceover(@Nullable JSONB voiceover) {
        this.voiceover = voiceover;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.roles</code>. 视频角色列表
     */
    public JSONB getRoles() {
        return this.roles;
    }

    /**
     * Setter for <code>creation.creation_video_project.roles</code>. 视频角色列表
     */
    public CreationVideoProject setRoles(JSONB roles) {
        this.roles = roles;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.subtitle_layer</code>.
     * 字幕层
     */
    @Nullable
    public JSONB getSubtitleLayer() {
        return this.subtitleLayer;
    }

    /**
     * Setter for <code>creation.creation_video_project.subtitle_layer</code>.
     * 字幕层
     */
    public CreationVideoProject setSubtitleLayer(@Nullable JSONB subtitleLayer) {
        this.subtitleLayer = subtitleLayer;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.avatar_layer</code>.
     * 数字人视频层
     */
    @Nullable
    public JSONB getAvatarLayer() {
        return this.avatarLayer;
    }

    /**
     * Setter for <code>creation.creation_video_project.avatar_layer</code>.
     * 数字人视频层
     */
    public CreationVideoProject setAvatarLayer(@Nullable JSONB avatarLayer) {
        this.avatarLayer = avatarLayer;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.background_layer</code>.
     * 背景层
     */
    @Nullable
    public JSONB getBackgroundLayer() {
        return this.backgroundLayer;
    }

    /**
     * Setter for <code>creation.creation_video_project.background_layer</code>.
     * 背景层
     */
    public CreationVideoProject setBackgroundLayer(@Nullable JSONB backgroundLayer) {
        this.backgroundLayer = backgroundLayer;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.audio_tracks</code>.
     * 音轨列表
     */
    public JSONB getAudioTracks() {
        return this.audioTracks;
    }

    /**
     * Setter for <code>creation.creation_video_project.audio_tracks</code>.
     * 音轨列表
     */
    public CreationVideoProject setAudioTracks(JSONB audioTracks) {
        this.audioTracks = audioTracks;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.video_tracks</code>.
     * 视频轨列表
     */
    public JSONB getVideoTracks() {
        return this.videoTracks;
    }

    /**
     * Setter for <code>creation.creation_video_project.video_tracks</code>.
     * 视频轨列表
     */
    public CreationVideoProject setVideoTracks(JSONB videoTracks) {
        this.videoTracks = videoTracks;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.aspect_ratio</code>.
     * 视频宽高比
     */
    public String getAspectRatio() {
        return this.aspectRatio;
    }

    /**
     * Setter for <code>creation.creation_video_project.aspect_ratio</code>.
     * 视频宽高比
     */
    public CreationVideoProject setAspectRatio(String aspectRatio) {
        this.aspectRatio = aspectRatio;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.width</code>. 视频默认宽度（像素）
     */
    public Integer getWidth() {
        return this.width;
    }

    /**
     * Setter for <code>creation.creation_video_project.width</code>. 视频默认宽度（像素）
     */
    public CreationVideoProject setWidth(Integer width) {
        this.width = width;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.height</code>.
     * 视频默认高度（像素）
     */
    public Integer getHeight() {
        return this.height;
    }

    /**
     * Setter for <code>creation.creation_video_project.height</code>.
     * 视频默认高度（像素）
     */
    public CreationVideoProject setHeight(Integer height) {
        this.height = height;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.shots</code>. 视频分镜列表
     */
    public JSONB getShots() {
        return this.shots;
    }

    /**
     * Setter for <code>creation.creation_video_project.shots</code>. 视频分镜列表
     */
    public CreationVideoProject setShots(JSONB shots) {
        this.shots = shots;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.compose_at</code>.
     * 视频编排时间（最新一次）
     */
    @Nullable
    public ZonedDateTime getComposeAt() {
        return this.composeAt;
    }

    /**
     * Setter for <code>creation.creation_video_project.compose_at</code>.
     * 视频编排时间（最新一次）
     */
    public CreationVideoProject setComposeAt(@Nullable ZonedDateTime composeAt) {
        this.composeAt = composeAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.compose_expired</code>.
     * 视频编排是否过期
     */
    public Boolean getComposeExpired() {
        return this.composeExpired;
    }

    /**
     * Setter for <code>creation.creation_video_project.compose_expired</code>.
     * 视频编排是否过期
     */
    public CreationVideoProject setComposeExpired(Boolean composeExpired) {
        this.composeExpired = composeExpired;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.compose_version</code>.
     * 视频编排版本
     */
    @Nullable
    public Integer getComposeVersion() {
        return this.composeVersion;
    }

    /**
     * Setter for <code>creation.creation_video_project.compose_version</code>.
     * 视频编排版本
     */
    public CreationVideoProject setComposeVersion(@Nullable Integer composeVersion) {
        this.composeVersion = composeVersion;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.cover_file_id</code>.
     * 项目封面文件ID
     */
    @Nullable
    public Long getCoverFileId() {
        return this.coverFileId;
    }

    /**
     * Setter for <code>creation.creation_video_project.cover_file_id</code>.
     * 项目封面文件ID
     */
    public CreationVideoProject setCoverFileId(@Nullable Long coverFileId) {
        this.coverFileId = coverFileId;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.cover_url</code>. 项目封面地址
     */
    @Nullable
    public String getCoverUrl() {
        return this.coverUrl;
    }

    /**
     * Setter for <code>creation.creation_video_project.cover_url</code>. 项目封面地址
     */
    public CreationVideoProject setCoverUrl(@Nullable String coverUrl) {
        this.coverUrl = coverUrl;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.duration</code>.
     * 视频总时长（最新一次）
     */
    @Nullable
    public Duration getDuration() {
        return this.duration;
    }

    /**
     * Setter for <code>creation.creation_video_project.duration</code>.
     * 视频总时长（最新一次）
     */
    public CreationVideoProject setDuration(@Nullable Duration duration) {
        this.duration = duration;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.videos</code>.
     * 成品视频（最新一批）
     */
    public JSONB getVideos() {
        return this.videos;
    }

    /**
     * Setter for <code>creation.creation_video_project.videos</code>.
     * 成品视频（最新一批）
     */
    public CreationVideoProject setVideos(JSONB videos) {
        this.videos = videos;
        return this;
    }

    /**
     * Getter for <code>creation.creation_video_project.compose_tasks</code>.
     * 最新一版编排任务
     */
    @Nullable
    public JSONB getComposeTasks() {
        return this.composeTasks;
    }

    /**
     * Setter for <code>creation.creation_video_project.compose_tasks</code>.
     * 最新一版编排任务
     */
    public CreationVideoProject setComposeTasks(@Nullable JSONB composeTasks) {
        this.composeTasks = composeTasks;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_video_project.stage_failure_reason</code>.
     * 当前项目阶段的失败原因
     */
    @Nullable
    public String getStageFailureReason() {
        return this.stageFailureReason;
    }

    /**
     * Setter for
     * <code>creation.creation_video_project.stage_failure_reason</code>.
     * 当前项目阶段的失败原因
     */
    public CreationVideoProject setStageFailureReason(@Nullable String stageFailureReason) {
        this.stageFailureReason = stageFailureReason;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationVideoProject other = (CreationVideoProject) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.stage == null) {
            if (other.stage != null)
                return false;
        }
        else if (!this.stage.equals(other.stage))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.type == null) {
            if (other.type != null)
                return false;
        }
        else if (!this.type.equals(other.type))
            return false;
        if (this.assets == null) {
            if (other.assets != null)
                return false;
        }
        else if (!this.assets.equals(other.assets))
            return false;
        if (this.script == null) {
            if (other.script != null)
                return false;
        }
        else if (!this.script.equals(other.script))
            return false;
        if (this.voiceover == null) {
            if (other.voiceover != null)
                return false;
        }
        else if (!this.voiceover.equals(other.voiceover))
            return false;
        if (this.roles == null) {
            if (other.roles != null)
                return false;
        }
        else if (!this.roles.equals(other.roles))
            return false;
        if (this.subtitleLayer == null) {
            if (other.subtitleLayer != null)
                return false;
        }
        else if (!this.subtitleLayer.equals(other.subtitleLayer))
            return false;
        if (this.avatarLayer == null) {
            if (other.avatarLayer != null)
                return false;
        }
        else if (!this.avatarLayer.equals(other.avatarLayer))
            return false;
        if (this.backgroundLayer == null) {
            if (other.backgroundLayer != null)
                return false;
        }
        else if (!this.backgroundLayer.equals(other.backgroundLayer))
            return false;
        if (this.audioTracks == null) {
            if (other.audioTracks != null)
                return false;
        }
        else if (!this.audioTracks.equals(other.audioTracks))
            return false;
        if (this.videoTracks == null) {
            if (other.videoTracks != null)
                return false;
        }
        else if (!this.videoTracks.equals(other.videoTracks))
            return false;
        if (this.aspectRatio == null) {
            if (other.aspectRatio != null)
                return false;
        }
        else if (!this.aspectRatio.equals(other.aspectRatio))
            return false;
        if (this.width == null) {
            if (other.width != null)
                return false;
        }
        else if (!this.width.equals(other.width))
            return false;
        if (this.height == null) {
            if (other.height != null)
                return false;
        }
        else if (!this.height.equals(other.height))
            return false;
        if (this.shots == null) {
            if (other.shots != null)
                return false;
        }
        else if (!this.shots.equals(other.shots))
            return false;
        if (this.composeAt == null) {
            if (other.composeAt != null)
                return false;
        }
        else if (!this.composeAt.equals(other.composeAt))
            return false;
        if (this.composeExpired == null) {
            if (other.composeExpired != null)
                return false;
        }
        else if (!this.composeExpired.equals(other.composeExpired))
            return false;
        if (this.composeVersion == null) {
            if (other.composeVersion != null)
                return false;
        }
        else if (!this.composeVersion.equals(other.composeVersion))
            return false;
        if (this.coverFileId == null) {
            if (other.coverFileId != null)
                return false;
        }
        else if (!this.coverFileId.equals(other.coverFileId))
            return false;
        if (this.coverUrl == null) {
            if (other.coverUrl != null)
                return false;
        }
        else if (!this.coverUrl.equals(other.coverUrl))
            return false;
        if (this.duration == null) {
            if (other.duration != null)
                return false;
        }
        else if (!this.duration.equals(other.duration))
            return false;
        if (this.videos == null) {
            if (other.videos != null)
                return false;
        }
        else if (!this.videos.equals(other.videos))
            return false;
        if (this.composeTasks == null) {
            if (other.composeTasks != null)
                return false;
        }
        else if (!this.composeTasks.equals(other.composeTasks))
            return false;
        if (this.stageFailureReason == null) {
            if (other.stageFailureReason != null)
                return false;
        }
        else if (!this.stageFailureReason.equals(other.stageFailureReason))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.stage == null) ? 0 : this.stage.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.type == null) ? 0 : this.type.hashCode());
        result = prime * result + ((this.assets == null) ? 0 : this.assets.hashCode());
        result = prime * result + ((this.script == null) ? 0 : this.script.hashCode());
        result = prime * result + ((this.voiceover == null) ? 0 : this.voiceover.hashCode());
        result = prime * result + ((this.roles == null) ? 0 : this.roles.hashCode());
        result = prime * result + ((this.subtitleLayer == null) ? 0 : this.subtitleLayer.hashCode());
        result = prime * result + ((this.avatarLayer == null) ? 0 : this.avatarLayer.hashCode());
        result = prime * result + ((this.backgroundLayer == null) ? 0 : this.backgroundLayer.hashCode());
        result = prime * result + ((this.audioTracks == null) ? 0 : this.audioTracks.hashCode());
        result = prime * result + ((this.videoTracks == null) ? 0 : this.videoTracks.hashCode());
        result = prime * result + ((this.aspectRatio == null) ? 0 : this.aspectRatio.hashCode());
        result = prime * result + ((this.width == null) ? 0 : this.width.hashCode());
        result = prime * result + ((this.height == null) ? 0 : this.height.hashCode());
        result = prime * result + ((this.shots == null) ? 0 : this.shots.hashCode());
        result = prime * result + ((this.composeAt == null) ? 0 : this.composeAt.hashCode());
        result = prime * result + ((this.composeExpired == null) ? 0 : this.composeExpired.hashCode());
        result = prime * result + ((this.composeVersion == null) ? 0 : this.composeVersion.hashCode());
        result = prime * result + ((this.coverFileId == null) ? 0 : this.coverFileId.hashCode());
        result = prime * result + ((this.coverUrl == null) ? 0 : this.coverUrl.hashCode());
        result = prime * result + ((this.duration == null) ? 0 : this.duration.hashCode());
        result = prime * result + ((this.videos == null) ? 0 : this.videos.hashCode());
        result = prime * result + ((this.composeTasks == null) ? 0 : this.composeTasks.hashCode());
        result = prime * result + ((this.stageFailureReason == null) ? 0 : this.stageFailureReason.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationVideoProject (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(status);
        sb.append(", ").append(stage);
        sb.append(", ").append(name);
        sb.append(", ").append(type);
        sb.append(", ").append(assets);
        sb.append(", ").append(script);
        sb.append(", ").append(voiceover);
        sb.append(", ").append(roles);
        sb.append(", ").append(subtitleLayer);
        sb.append(", ").append(avatarLayer);
        sb.append(", ").append(backgroundLayer);
        sb.append(", ").append(audioTracks);
        sb.append(", ").append(videoTracks);
        sb.append(", ").append(aspectRatio);
        sb.append(", ").append(width);
        sb.append(", ").append(height);
        sb.append(", ").append(shots);
        sb.append(", ").append(composeAt);
        sb.append(", ").append(composeExpired);
        sb.append(", ").append(composeVersion);
        sb.append(", ").append(coverFileId);
        sb.append(", ").append(coverUrl);
        sb.append(", ").append(duration);
        sb.append(", ").append(videos);
        sb.append(", ").append(composeTasks);
        sb.append(", ").append(stageFailureReason);

        sb.append(")");
        return sb.toString();
    }
}
