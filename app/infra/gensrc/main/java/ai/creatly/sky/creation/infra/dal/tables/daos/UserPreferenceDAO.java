/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.UserPreferenceTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.UserPreference;
import ai.creatly.sky.creation.infra.dal.tables.records.UserPreferenceRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 用户配置中心
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class UserPreferenceDAO extends DAOImpl<UserPreferenceRecord, UserPreference, Long> {

    /**
     * Create a new UserPreferenceDAO without any configuration
     */
    public UserPreferenceDAO() {
        super(UserPreferenceTable.USER_PREFERENCE, UserPreference.class);
    }

    /**
     * Create a new UserPreferenceDAO with an attached configuration
     */
    @Autowired
    public UserPreferenceDAO(Configuration configuration) {
        super(UserPreferenceTable.USER_PREFERENCE, UserPreference.class, configuration);
    }

    @Override
    public Long getId(UserPreference object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<UserPreference> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserPreferenceTable.USER_PREFERENCE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<UserPreference> fetchById(Long... values) {
        return fetch(UserPreferenceTable.USER_PREFERENCE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public UserPreference fetchOneById(Long value) {
        return fetchOne(UserPreferenceTable.USER_PREFERENCE.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<UserPreference> fetchOptionalById(Long value) {
        return fetchOptional(UserPreferenceTable.USER_PREFERENCE.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<UserPreference> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserPreferenceTable.USER_PREFERENCE.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<UserPreference> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(UserPreferenceTable.USER_PREFERENCE.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<UserPreference> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserPreferenceTable.USER_PREFERENCE.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<UserPreference> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(UserPreferenceTable.USER_PREFERENCE.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<UserPreference> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserPreferenceTable.USER_PREFERENCE.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<UserPreference> fetchByUid(Long... values) {
        return fetch(UserPreferenceTable.USER_PREFERENCE.UID, values);
    }

    /**
     * Fetch records that have <code>config_key BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<UserPreference> fetchRangeOfConfigKey(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserPreferenceTable.USER_PREFERENCE.CONFIG_KEY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>config_key IN (values)</code>
     */
    public List<UserPreference> fetchByConfigKey(String... values) {
        return fetch(UserPreferenceTable.USER_PREFERENCE.CONFIG_KEY, values);
    }

    /**
     * Fetch records that have <code>config_value BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<UserPreference> fetchRangeOfConfigValue(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserPreferenceTable.USER_PREFERENCE.CONFIG_VALUE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>config_value IN (values)</code>
     */
    public List<UserPreference> fetchByConfigValue(String... values) {
        return fetch(UserPreferenceTable.USER_PREFERENCE.CONFIG_VALUE, values);
    }

    /**
     * Fetch records that have <code>operator BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<UserPreference> fetchRangeOfOperator(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserPreferenceTable.USER_PREFERENCE.OPERATOR, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>operator IN (values)</code>
     */
    public List<UserPreference> fetchByOperator(String... values) {
        return fetch(UserPreferenceTable.USER_PREFERENCE.OPERATOR, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<UserPreference> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserPreferenceTable.USER_PREFERENCE.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<UserPreference> fetchByStatus(String... values) {
        return fetch(UserPreferenceTable.USER_PREFERENCE.STATUS, values);
    }
}
