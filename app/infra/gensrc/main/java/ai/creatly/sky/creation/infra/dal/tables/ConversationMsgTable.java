/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.ConversationMsgRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 场景提示词
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class ConversationMsgTable extends TableImpl<ConversationMsgRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_conversation_msg</code>
     */
    public static final ConversationMsgTable CREATION_CONVERSATION_MSG = new ConversationMsgTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ConversationMsgRecord> getRecordType() {
        return ConversationMsgRecord.class;
    }

    /**
     * The column <code>creation.creation_conversation_msg.id</code>. 主键
     */
    public final TableField<ConversationMsgRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.creation_conversation_msg.created_at</code>.
     * 创建时间
     */
    public final TableField<ConversationMsgRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_conversation_msg.updated_at</code>.
     * 更新时间
     */
    public final TableField<ConversationMsgRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_conversation_msg.uid</code>. 用户id
     */
    public final TableField<ConversationMsgRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户id");

    /**
     * The column
     * <code>creation.creation_conversation_msg.conversation_id</code>. 会话id
     */
    public final TableField<ConversationMsgRecord, Long> CONVERSATION_ID = createField(DSL.name("conversation_id"), SQLDataType.BIGINT.nullable(false), this, "会话id");

    /**
     * The column <code>creation.creation_conversation_msg.scenario_id</code>.
     * 场景码
     */
    public final TableField<ConversationMsgRecord, Long> SCENARIO_ID = createField(DSL.name("scenario_id"), SQLDataType.BIGINT.nullable(false), this, "场景码");

    /**
     * The column
     * <code>creation.creation_conversation_msg.scenario_ctx_no</code>. 场景码关联的no
     */
    public final TableField<ConversationMsgRecord, Long> SCENARIO_CTX_NO = createField(DSL.name("scenario_ctx_no"), SQLDataType.BIGINT.nullable(false), this, "场景码关联的no");

    /**
     * The column <code>creation.creation_conversation_msg.role</code>.
     * 提示词角色（user/ai）
     */
    public final TableField<ConversationMsgRecord, String> ROLE = createField(DSL.name("role"), SQLDataType.VARCHAR(32).nullable(false), this, "提示词角色（user/ai）");

    /**
     * The column <code>creation.creation_conversation_msg.prompt</code>. 提示词
     */
    public final TableField<ConversationMsgRecord, String> PROMPT = createField(DSL.name("prompt"), SQLDataType.CLOB.nullable(false), this, "提示词");

    private ConversationMsgTable(Name alias, Table<ConversationMsgRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private ConversationMsgTable(Name alias, Table<ConversationMsgRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("场景提示词"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_conversation_msg</code> table
     * reference
     */
    public ConversationMsgTable(String alias) {
        this(DSL.name(alias), CREATION_CONVERSATION_MSG);
    }

    /**
     * Create an aliased <code>creation.creation_conversation_msg</code> table
     * reference
     */
    public ConversationMsgTable(Name alias) {
        this(alias, CREATION_CONVERSATION_MSG);
    }

    /**
     * Create a <code>creation.creation_conversation_msg</code> table reference
     */
    public ConversationMsgTable() {
        this(DSL.name("creation_conversation_msg"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<ConversationMsgRecord> getPrimaryKey() {
        return Keys.CREATION_CONVERSATION_MSG_PKEY;
    }

    @Override
    public ConversationMsgTable as(String alias) {
        return new ConversationMsgTable(DSL.name(alias), this);
    }

    @Override
    public ConversationMsgTable as(Name alias) {
        return new ConversationMsgTable(alias, this);
    }

    @Override
    public ConversationMsgTable as(Table<?> alias) {
        return new ConversationMsgTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ConversationMsgTable rename(String name) {
        return new ConversationMsgTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ConversationMsgTable rename(Name name) {
        return new ConversationMsgTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ConversationMsgTable rename(Table<?> name) {
        return new ConversationMsgTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConversationMsgTable where(Condition condition) {
        return new ConversationMsgTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConversationMsgTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConversationMsgTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConversationMsgTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConversationMsgTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConversationMsgTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConversationMsgTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConversationMsgTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConversationMsgTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConversationMsgTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
