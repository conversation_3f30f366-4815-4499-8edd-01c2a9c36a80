/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.UserAssetTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationUserAsset;
import ai.creatly.sky.creation.infra.dal.tables.records.UserAssetRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.JSONB;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 用户素材库
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class UserAssetDAO extends DAOImpl<UserAssetRecord, CreationUserAsset, Long> {

    /**
     * Create a new UserAssetDAO without any configuration
     */
    public UserAssetDAO() {
        super(UserAssetTable.CREATION_USER_ASSET, CreationUserAsset.class);
    }

    /**
     * Create a new UserAssetDAO with an attached configuration
     */
    @Autowired
    public UserAssetDAO(Configuration configuration) {
        super(UserAssetTable.CREATION_USER_ASSET, CreationUserAsset.class, configuration);
    }

    @Override
    public Long getId(CreationUserAsset object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationUserAsset> fetchById(Long... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationUserAsset fetchOneById(Long value) {
        return fetchOne(UserAssetTable.CREATION_USER_ASSET.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationUserAsset> fetchOptionalById(Long value) {
        return fetchOptional(UserAssetTable.CREATION_USER_ASSET.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationUserAsset> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationUserAsset> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<CreationUserAsset> fetchByUid(Long... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.UID, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CreationUserAsset> fetchByStatus(String... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.STATUS, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<CreationUserAsset> fetchByName(String... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.NAME, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfType(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<CreationUserAsset> fetchByType(String... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.TYPE, values);
    }

    /**
     * Fetch records that have <code>file BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfFile(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.FILE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>file IN (values)</code>
     */
    public List<CreationUserAsset> fetchByFile(JSONB... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.FILE, values);
    }

    /**
     * Fetch records that have <code>content BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfContent(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.CONTENT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>content IN (values)</code>
     */
    public List<CreationUserAsset> fetchByContent(JSONB... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.CONTENT, values);
    }

    /**
     * Fetch records that have <code>cover_file_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfCoverFileId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.COVER_FILE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover_file_id IN (values)</code>
     */
    public List<CreationUserAsset> fetchByCoverFileId(Long... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.COVER_FILE_ID, values);
    }

    /**
     * Fetch records that have <code>cover_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfCoverUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.COVER_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover_url IN (values)</code>
     */
    public List<CreationUserAsset> fetchByCoverUrl(String... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.COVER_URL, values);
    }

    /**
     * Fetch records that have <code>description BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfDescription(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.DESCRIPTION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>description IN (values)</code>
     */
    public List<CreationUserAsset> fetchByDescription(String... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.DESCRIPTION, values);
    }

    /**
     * Fetch records that have <code>metadata BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfMetadata(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.METADATA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>metadata IN (values)</code>
     */
    public List<CreationUserAsset> fetchByMetadata(JSONB... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.METADATA, values);
    }

    /**
     * Fetch records that have <code>keywords BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfKeywords(String[] lowerInclusive, String[] upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.KEYWORDS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>keywords IN (values)</code>
     */
    public List<CreationUserAsset> fetchByKeywords(String[]... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.KEYWORDS, values);
    }

    /**
     * Fetch records that have <code>labels BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfLabels(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.LABELS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>labels IN (values)</code>
     */
    public List<CreationUserAsset> fetchByLabels(JSONB... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.LABELS, values);
    }

    /**
     * Fetch records that have <code>video_slices BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationUserAsset> fetchRangeOfVideoSlices(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(UserAssetTable.CREATION_USER_ASSET.VIDEO_SLICES, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>video_slices IN (values)</code>
     */
    public List<CreationUserAsset> fetchByVideoSlices(JSONB... values) {
        return fetch(UserAssetTable.CREATION_USER_ASSET.VIDEO_SLICES, values);
    }
}
