/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.OpenapiCredentialsTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationOpenapiCredentials;
import ai.creatly.sky.creation.infra.dal.tables.records.OpenapiCredentialsRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * openapi密钥表
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class OpenapiCredentialsDAO extends DAOImpl<OpenapiCredentialsRecord, CreationOpenapiCredentials, Long> {

    /**
     * Create a new OpenapiCredentialsDAO without any configuration
     */
    public OpenapiCredentialsDAO() {
        super(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS, CreationOpenapiCredentials.class);
    }

    /**
     * Create a new OpenapiCredentialsDAO with an attached configuration
     */
    @Autowired
    public OpenapiCredentialsDAO(Configuration configuration) {
        super(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS, CreationOpenapiCredentials.class, configuration);
    }

    @Override
    public Long getId(CreationOpenapiCredentials object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationOpenapiCredentials> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationOpenapiCredentials> fetchById(Long... values) {
        return fetch(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationOpenapiCredentials fetchOneById(Long value) {
        return fetchOne(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationOpenapiCredentials> fetchOptionalById(Long value) {
        return fetchOptional(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationOpenapiCredentials> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationOpenapiCredentials> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationOpenapiCredentials> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationOpenapiCredentials> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationOpenapiCredentials> fetchRangeOfUid(String lowerInclusive, String upperInclusive) {
        return fetchRange(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<CreationOpenapiCredentials> fetchByUid(String... values) {
        return fetch(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.UID, values);
    }

    /**
     * Fetch a unique record that has <code>uid = value</code>
     */
    @Nullable
    public CreationOpenapiCredentials fetchOneByUid(String value) {
        return fetchOne(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.UID, value);
    }

    /**
     * Fetch a unique record that has <code>uid = value</code>
     */
    public Optional<CreationOpenapiCredentials> fetchOptionalByUid(String value) {
        return fetchOptional(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.UID, value);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationOpenapiCredentials> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<CreationOpenapiCredentials> fetchByName(String... values) {
        return fetch(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.NAME, values);
    }

    /**
     * Fetch records that have <code>app_key BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationOpenapiCredentials> fetchRangeOfAppKey(String lowerInclusive, String upperInclusive) {
        return fetchRange(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.APP_KEY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>app_key IN (values)</code>
     */
    public List<CreationOpenapiCredentials> fetchByAppKey(String... values) {
        return fetch(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.APP_KEY, values);
    }

    /**
     * Fetch a unique record that has <code>app_key = value</code>
     */
    @Nullable
    public CreationOpenapiCredentials fetchOneByAppKey(String value) {
        return fetchOne(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.APP_KEY, value);
    }

    /**
     * Fetch a unique record that has <code>app_key = value</code>
     */
    public Optional<CreationOpenapiCredentials> fetchOptionalByAppKey(String value) {
        return fetchOptional(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.APP_KEY, value);
    }

    /**
     * Fetch records that have <code>app_secret BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationOpenapiCredentials> fetchRangeOfAppSecret(String lowerInclusive, String upperInclusive) {
        return fetchRange(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.APP_SECRET, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>app_secret IN (values)</code>
     */
    public List<CreationOpenapiCredentials> fetchByAppSecret(String... values) {
        return fetch(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.APP_SECRET, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationOpenapiCredentials> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<CreationOpenapiCredentials> fetchByStatus(String... values) {
        return fetch(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.STATUS, values);
    }

    /**
     * Fetch records that have <code>expire_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationOpenapiCredentials> fetchRangeOfExpireAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.EXPIRE_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>expire_at IN (values)</code>
     */
    public List<CreationOpenapiCredentials> fetchByExpireAt(ZonedDateTime... values) {
        return fetch(OpenapiCredentialsTable.CREATION_OPENAPI_CREDENTIALS.EXPIRE_AT, values);
    }
}
