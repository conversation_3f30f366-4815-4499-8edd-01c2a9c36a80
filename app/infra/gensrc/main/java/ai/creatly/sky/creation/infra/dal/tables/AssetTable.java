/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.AssetRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 作品
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AssetTable extends TableImpl<AssetRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_asset</code>
     */
    public static final AssetTable CREATION_ASSET = new AssetTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AssetRecord> getRecordType() {
        return AssetRecord.class;
    }

    /**
     * The column <code>creation.creation_asset.id</code>. 主键
     */
    public final TableField<AssetRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键");

    /**
     * The column <code>creation.creation_asset.created_at</code>. 创建时间
     */
    public final TableField<AssetRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_asset.updated_at</code>. 更新时间
     */
    public final TableField<AssetRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_asset.uid</code>. 用户ID
     */
    public final TableField<AssetRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "用户ID");

    /**
     * The column <code>creation.creation_asset.status</code>. 状态（无效即为软删除）
     */
    public final TableField<AssetRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(16).nullable(false), this, "状态（无效即为软删除）");

    /**
     * The column <code>creation.creation_asset.source_type</code>. 来源类型
     */
    public final TableField<AssetRecord, String> SOURCE_TYPE = createField(DSL.name("source_type"), SQLDataType.VARCHAR(16).nullable(false), this, "来源类型");

    /**
     * The column <code>creation.creation_asset.biz_type</code>. 业务类型
     */
    public final TableField<AssetRecord, String> BIZ_TYPE = createField(DSL.name("biz_type"), SQLDataType.VARCHAR(32).nullable(false), this, "业务类型");

    /**
     * The column <code>creation.creation_asset.cover_url</code>. 封面图
     */
    public final TableField<AssetRecord, String> COVER_URL = createField(DSL.name("cover_url"), SQLDataType.VARCHAR(200).nullable(false), this, "封面图");

    /**
     * The column <code>creation.creation_asset.file</code>. 文件
     */
    public final TableField<AssetRecord, JSONB> FILE = createField(DSL.name("file"), SQLDataType.JSONB.nullable(false), this, "文件");

    /**
     * The column <code>creation.creation_asset.content</code>. 内容
     */
    public final TableField<AssetRecord, JSONB> CONTENT = createField(DSL.name("content"), SQLDataType.JSONB.nullable(false), this, "内容");

    /**
     * The column <code>creation.creation_asset.metadata</code>. 元数据
     */
    public final TableField<AssetRecord, JSONB> METADATA = createField(DSL.name("metadata"), SQLDataType.JSONB.nullable(false), this, "元数据");

    /**
     * The column <code>creation.creation_asset.tags</code>. 标签列表
     */
    public final TableField<AssetRecord, String[]> TAGS = createField(DSL.name("tags"), SQLDataType.VARCHAR(32).nullable(false).array(), this, "标签列表");

    /**
     * The column <code>creation.creation_asset.name</code>. 作品名称
     */
    public final TableField<AssetRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false).defaultValue(DSL.field(DSL.raw("'未命名'::character varying"), SQLDataType.VARCHAR)), this, "作品名称");

    /**
     * The column <code>creation.creation_asset.org_code</code>. 组织代码
     */
    public final TableField<AssetRecord, String> ORG_CODE = createField(DSL.name("org_code"), SQLDataType.VARCHAR(64), this, "组织代码");

    private AssetTable(Name alias, Table<AssetRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private AssetTable(Name alias, Table<AssetRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("作品"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_asset</code> table reference
     */
    public AssetTable(String alias) {
        this(DSL.name(alias), CREATION_ASSET);
    }

    /**
     * Create an aliased <code>creation.creation_asset</code> table reference
     */
    public AssetTable(Name alias) {
        this(alias, CREATION_ASSET);
    }

    /**
     * Create a <code>creation.creation_asset</code> table reference
     */
    public AssetTable() {
        this(DSL.name("creation_asset"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public Identity<AssetRecord, Long> getIdentity() {
        return (Identity<AssetRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<AssetRecord> getPrimaryKey() {
        return Keys.CREATION_ASSET_PKEY;
    }

    @Override
    public AssetTable as(String alias) {
        return new AssetTable(DSL.name(alias), this);
    }

    @Override
    public AssetTable as(Name alias) {
        return new AssetTable(alias, this);
    }

    @Override
    public AssetTable as(Table<?> alias) {
        return new AssetTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public AssetTable rename(String name) {
        return new AssetTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public AssetTable rename(Name name) {
        return new AssetTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public AssetTable rename(Table<?> name) {
        return new AssetTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AssetTable where(Condition condition) {
        return new AssetTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AssetTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AssetTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AssetTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AssetTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AssetTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AssetTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AssetTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AssetTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AssetTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
