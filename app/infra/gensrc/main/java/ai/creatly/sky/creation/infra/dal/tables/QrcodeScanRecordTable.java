/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.QrcodeScanRecordRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 权益中心-用户扫码记录
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class QrcodeScanRecordTable extends TableImpl<QrcodeScanRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.qrcode_scan_record</code>
     */
    public static final QrcodeScanRecordTable QRCODE_SCAN_RECORD = new QrcodeScanRecordTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<QrcodeScanRecordRecord> getRecordType() {
        return QrcodeScanRecordRecord.class;
    }

    /**
     * The column <code>creation.qrcode_scan_record.id</code>. 主键
     */
    public final TableField<QrcodeScanRecordRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>creation.qrcode_scan_record.created_at</code>. 创建时间
     */
    public final TableField<QrcodeScanRecordRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.qrcode_scan_record.updated_at</code>. 更新时间
     */
    public final TableField<QrcodeScanRecordRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.qrcode_scan_record.issuing_party</code>. 发码方
     */
    public final TableField<QrcodeScanRecordRecord, Long> ISSUING_PARTY = createField(DSL.name("issuing_party"), SQLDataType.BIGINT.nullable(false), this, "发码方");

    /**
     * The column <code>creation.qrcode_scan_record.scanning_party</code>. 扫码人
     */
    public final TableField<QrcodeScanRecordRecord, Long> SCANNING_PARTY = createField(DSL.name("scanning_party"), SQLDataType.BIGINT.nullable(false), this, "扫码人");

    /**
     * The column <code>creation.qrcode_scan_record.code</code>. 码值
     */
    public final TableField<QrcodeScanRecordRecord, String> CODE = createField(DSL.name("code"), SQLDataType.VARCHAR(255).nullable(false), this, "码值");

    /**
     * The column <code>creation.qrcode_scan_record.type</code>. 二维码
     */
    public final TableField<QrcodeScanRecordRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(255).nullable(false), this, "二维码");

    private QrcodeScanRecordTable(Name alias, Table<QrcodeScanRecordRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private QrcodeScanRecordTable(Name alias, Table<QrcodeScanRecordRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("权益中心-用户扫码记录"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.qrcode_scan_record</code> table
     * reference
     */
    public QrcodeScanRecordTable(String alias) {
        this(DSL.name(alias), QRCODE_SCAN_RECORD);
    }

    /**
     * Create an aliased <code>creation.qrcode_scan_record</code> table
     * reference
     */
    public QrcodeScanRecordTable(Name alias) {
        this(alias, QRCODE_SCAN_RECORD);
    }

    /**
     * Create a <code>creation.qrcode_scan_record</code> table reference
     */
    public QrcodeScanRecordTable() {
        this(DSL.name("qrcode_scan_record"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<QrcodeScanRecordRecord> getPrimaryKey() {
        return Keys.QRCODE_SCAN_RECORD_PKEY;
    }

    @Override
    public QrcodeScanRecordTable as(String alias) {
        return new QrcodeScanRecordTable(DSL.name(alias), this);
    }

    @Override
    public QrcodeScanRecordTable as(Name alias) {
        return new QrcodeScanRecordTable(alias, this);
    }

    @Override
    public QrcodeScanRecordTable as(Table<?> alias) {
        return new QrcodeScanRecordTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public QrcodeScanRecordTable rename(String name) {
        return new QrcodeScanRecordTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public QrcodeScanRecordTable rename(Name name) {
        return new QrcodeScanRecordTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public QrcodeScanRecordTable rename(Table<?> name) {
        return new QrcodeScanRecordTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public QrcodeScanRecordTable where(Condition condition) {
        return new QrcodeScanRecordTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public QrcodeScanRecordTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public QrcodeScanRecordTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public QrcodeScanRecordTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public QrcodeScanRecordTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public QrcodeScanRecordTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public QrcodeScanRecordTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public QrcodeScanRecordTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public QrcodeScanRecordTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public QrcodeScanRecordTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
