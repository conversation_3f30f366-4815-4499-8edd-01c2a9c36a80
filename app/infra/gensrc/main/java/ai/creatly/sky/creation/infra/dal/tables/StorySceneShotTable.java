/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.StorySceneShotRecord;

import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;
import org.jooq.types.YearToSecond;


/**
 * 场景分镜
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class StorySceneShotTable extends TableImpl<StorySceneShotRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_story_scene_shot</code>
     */
    public static final StorySceneShotTable CREATION_STORY_SCENE_SHOT = new StorySceneShotTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StorySceneShotRecord> getRecordType() {
        return StorySceneShotRecord.class;
    }

    /**
     * The column <code>creation.creation_story_scene_shot.id</code>. 分镜id
     */
    public final TableField<StorySceneShotRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "分镜id");

    /**
     * The column <code>creation.creation_story_scene_shot.created_at</code>.
     * 创建时间
     */
    public final TableField<StorySceneShotRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_story_scene_shot.updated_at</code>.
     * 更新时间
     */
    public final TableField<StorySceneShotRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_story_scene_shot.story_id</code>. 故事id
     */
    public final TableField<StorySceneShotRecord, Long> STORY_ID = createField(DSL.name("story_id"), SQLDataType.BIGINT.nullable(false), this, "故事id");

    /**
     * The column <code>creation.creation_story_scene_shot.scene_id</code>. 场景id
     */
    public final TableField<StorySceneShotRecord, Long> SCENE_ID = createField(DSL.name("scene_id"), SQLDataType.BIGINT.nullable(false), this, "场景id");

    /**
     * The column <code>creation.creation_story_scene_shot.shot_name</code>.
     * 舞台演出
     */
    public final TableField<StorySceneShotRecord, String> SHOT_NAME = createField(DSL.name("shot_name"), SQLDataType.VARCHAR(100).nullable(false), this, "舞台演出");

    /**
     * The column <code>creation.creation_story_scene_shot.index</code>. 分镜下标
     */
    public final TableField<StorySceneShotRecord, Integer> INDEX = createField(DSL.name("index"), SQLDataType.INTEGER.nullable(false), this, "分镜下标");

    /**
     * The column <code>creation.creation_story_scene_shot.description</code>.
     * 分镜描述
     */
    public final TableField<StorySceneShotRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(512).nullable(false), this, "分镜描述");

    /**
     * The column
     * <code>creation.creation_story_scene_shot.en_description</code>. 英文分镜描述
     */
    public final TableField<StorySceneShotRecord, String> EN_DESCRIPTION = createField(DSL.name("en_description"), SQLDataType.VARCHAR(512).nullable(false), this, "英文分镜描述");

    /**
     * The column <code>creation.creation_story_scene_shot.asset</code>. 分镜资产
     */
    public final TableField<StorySceneShotRecord, JSONB> ASSET = createField(DSL.name("asset"), SQLDataType.JSONB.nullable(false), this, "分镜资产");

    /**
     * The column <code>creation.creation_story_scene_shot.dialogues</code>. 对白
     */
    public final TableField<StorySceneShotRecord, JSONB> DIALOGUES = createField(DSL.name("dialogues"), SQLDataType.JSONB.nullable(false), this, "对白");

    /**
     * The column <code>creation.creation_story_scene_shot.sounds</code>. 音效
     */
    public final TableField<StorySceneShotRecord, JSONB> SOUNDS = createField(DSL.name("sounds"), SQLDataType.JSONB.nullable(false), this, "音效");

    /**
     * The column <code>creation.creation_story_scene_shot.sounds_desc</code>.
     * 音效描述
     */
    public final TableField<StorySceneShotRecord, String> SOUNDS_DESC = createField(DSL.name("sounds_desc"), SQLDataType.VARCHAR(512).nullable(false), this, "音效描述");

    /**
     * The column <code>creation.creation_story_scene_shot.creator</code>. 创建者
     */
    public final TableField<StorySceneShotRecord, JSONB> CREATOR = createField(DSL.name("creator"), SQLDataType.JSONB.nullable(false), this, "创建者");

    /**
     * The column <code>creation.creation_story_scene_shot.status</code>. 状态
     */
    public final TableField<StorySceneShotRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(20).nullable(false), this, "状态");

    /**
     * The column <code>creation.creation_story_scene_shot.movement</code>.
     */
    public final TableField<StorySceneShotRecord, String> MOVEMENT = createField(DSL.name("movement"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>creation.creation_story_scene_shot.duration</code>. 分镜时长
     */
    public final TableField<StorySceneShotRecord, Duration> DURATION = createField(DSL.name("duration"), SQLDataType.INTERVAL.nullable(false), this, "分镜时长", Converter.ofNullable(YearToSecond.class, Duration.class, YearToSecond::toDuration, YearToSecond::valueOf));

    private StorySceneShotTable(Name alias, Table<StorySceneShotRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private StorySceneShotTable(Name alias, Table<StorySceneShotRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("场景分镜"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_story_scene_shot</code> table
     * reference
     */
    public StorySceneShotTable(String alias) {
        this(DSL.name(alias), CREATION_STORY_SCENE_SHOT);
    }

    /**
     * Create an aliased <code>creation.creation_story_scene_shot</code> table
     * reference
     */
    public StorySceneShotTable(Name alias) {
        this(alias, CREATION_STORY_SCENE_SHOT);
    }

    /**
     * Create a <code>creation.creation_story_scene_shot</code> table reference
     */
    public StorySceneShotTable() {
        this(DSL.name("creation_story_scene_shot"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<StorySceneShotRecord> getPrimaryKey() {
        return Keys.CREATION_STORY_SCENE_SHOT_PKEY;
    }

    @Override
    public StorySceneShotTable as(String alias) {
        return new StorySceneShotTable(DSL.name(alias), this);
    }

    @Override
    public StorySceneShotTable as(Name alias) {
        return new StorySceneShotTable(alias, this);
    }

    @Override
    public StorySceneShotTable as(Table<?> alias) {
        return new StorySceneShotTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public StorySceneShotTable rename(String name) {
        return new StorySceneShotTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public StorySceneShotTable rename(Name name) {
        return new StorySceneShotTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public StorySceneShotTable rename(Table<?> name) {
        return new StorySceneShotTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StorySceneShotTable where(Condition condition) {
        return new StorySceneShotTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StorySceneShotTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StorySceneShotTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StorySceneShotTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public StorySceneShotTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public StorySceneShotTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public StorySceneShotTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public StorySceneShotTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StorySceneShotTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public StorySceneShotTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
