/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.ConversationMsgTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationConversationMsg;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 场景提示词
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class ConversationMsgRecord extends UpdatableRecordImpl<ConversationMsgRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_conversation_msg.id</code>. 主键
     */
    public ConversationMsgRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.created_at</code>.
     * 创建时间
     */
    public ConversationMsgRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.updated_at</code>.
     * 更新时间
     */
    public ConversationMsgRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.uid</code>. 用户id
     */
    public ConversationMsgRecord setUid(Long value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.uid</code>. 用户id
     */
    public Long getUid() {
        return (Long) get(3);
    }

    /**
     * Setter for
     * <code>creation.creation_conversation_msg.conversation_id</code>. 会话id
     */
    public ConversationMsgRecord setConversationId(Long value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_conversation_msg.conversation_id</code>. 会话id
     */
    public Long getConversationId() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.scenario_id</code>.
     * 场景码
     */
    public ConversationMsgRecord setScenarioId(Long value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.scenario_id</code>.
     * 场景码
     */
    public Long getScenarioId() {
        return (Long) get(5);
    }

    /**
     * Setter for
     * <code>creation.creation_conversation_msg.scenario_ctx_no</code>. 场景码关联的no
     */
    public ConversationMsgRecord setScenarioCtxNo(Long value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_conversation_msg.scenario_ctx_no</code>. 场景码关联的no
     */
    public Long getScenarioCtxNo() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.role</code>.
     * 提示词角色（user/ai）
     */
    public ConversationMsgRecord setRole(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.role</code>.
     * 提示词角色（user/ai）
     */
    public String getRole() {
        return (String) get(7);
    }

    /**
     * Setter for <code>creation.creation_conversation_msg.prompt</code>. 提示词
     */
    public ConversationMsgRecord setPrompt(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_conversation_msg.prompt</code>. 提示词
     */
    public String getPrompt() {
        return (String) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ConversationMsgRecord
     */
    public ConversationMsgRecord() {
        super(ConversationMsgTable.CREATION_CONVERSATION_MSG);
    }

    /**
     * Create a detached, initialised ConversationMsgRecord
     */
    public ConversationMsgRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, Long uid, Long conversationId, Long scenarioId, Long scenarioCtxNo, String role, String prompt) {
        super(ConversationMsgTable.CREATION_CONVERSATION_MSG);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setUid(uid);
        setConversationId(conversationId);
        setScenarioId(scenarioId);
        setScenarioCtxNo(scenarioCtxNo);
        setRole(role);
        setPrompt(prompt);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised ConversationMsgRecord
     */
    public ConversationMsgRecord(CreationConversationMsg value) {
        super(ConversationMsgTable.CREATION_CONVERSATION_MSG);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setUid(value.getUid());
            setConversationId(value.getConversationId());
            setScenarioId(value.getScenarioId());
            setScenarioCtxNo(value.getScenarioCtxNo());
            setRole(value.getRole());
            setPrompt(value.getPrompt());
            resetChangedOnNotNull();
        }
    }
}
