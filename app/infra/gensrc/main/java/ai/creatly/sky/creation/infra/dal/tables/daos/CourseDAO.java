/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.CourseTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.Course;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 课程
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class CourseDAO extends DAOImpl<CourseRecord, Course, Long> {

    /**
     * Create a new CourseDAO without any configuration
     */
    public CourseDAO() {
        super(CourseTable.COURSE, Course.class);
    }

    /**
     * Create a new CourseDAO with an attached configuration
     */
    @Autowired
    public CourseDAO(Configuration configuration) {
        super(CourseTable.COURSE, Course.class, configuration);
    }

    @Override
    public Long getId(Course object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseTable.COURSE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<Course> fetchById(Long... values) {
        return fetch(CourseTable.COURSE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public Course fetchOneById(Long value) {
        return fetchOne(CourseTable.COURSE.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<Course> fetchOptionalById(Long value) {
        return fetchOptional(CourseTable.COURSE.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(CourseTable.COURSE.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<Course> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(CourseTable.COURSE.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(CourseTable.COURSE.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<Course> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(CourseTable.COURSE.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>category BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfCategory(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseTable.COURSE.CATEGORY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>category IN (values)</code>
     */
    public List<Course> fetchByCategory(String... values) {
        return fetch(CourseTable.COURSE.CATEGORY, values);
    }

    /**
     * Fetch records that have <code>cover_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfCoverUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseTable.COURSE.COVER_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover_url IN (values)</code>
     */
    public List<Course> fetchByCoverUrl(String... values) {
        return fetch(CourseTable.COURSE.COVER_URL, values);
    }

    /**
     * Fetch records that have <code>course_intro BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfCourseIntro(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseTable.COURSE.COURSE_INTRO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>course_intro IN (values)</code>
     */
    public List<Course> fetchByCourseIntro(String... values) {
        return fetch(CourseTable.COURSE.COURSE_INTRO, values);
    }

    /**
     * Fetch records that have <code>course_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfCourseStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseTable.COURSE.COURSE_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>course_status IN (values)</code>
     */
    public List<Course> fetchByCourseStatus(String... values) {
        return fetch(CourseTable.COURSE.COURSE_STATUS, values);
    }

    /**
     * Fetch records that have <code>course_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfCourseType(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseTable.COURSE.COURSE_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>course_type IN (values)</code>
     */
    public List<Course> fetchByCourseType(String... values) {
        return fetch(CourseTable.COURSE.COURSE_TYPE, values);
    }

    /**
     * Fetch records that have <code>course_title BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfCourseTitle(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseTable.COURSE.COURSE_TITLE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>course_title IN (values)</code>
     */
    public List<Course> fetchByCourseTitle(String... values) {
        return fetch(CourseTable.COURSE.COURSE_TITLE, values);
    }

    /**
     * Fetch records that have <code>video_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfVideoUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseTable.COURSE.VIDEO_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>video_url IN (values)</code>
     */
    public List<Course> fetchByVideoUrl(String... values) {
        return fetch(CourseTable.COURSE.VIDEO_URL, values);
    }

    /**
     * Fetch records that have <code>course_subtitle BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfCourseSubtitle(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseTable.COURSE.COURSE_SUBTITLE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>course_subtitle IN (values)</code>
     */
    public List<Course> fetchByCourseSubtitle(String... values) {
        return fetch(CourseTable.COURSE.COURSE_SUBTITLE, values);
    }

    /**
     * Fetch records that have <code>real_price BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfRealPrice(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseTable.COURSE.REAL_PRICE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>real_price IN (values)</code>
     */
    public List<Course> fetchByRealPrice(Long... values) {
        return fetch(CourseTable.COURSE.REAL_PRICE, values);
    }

    /**
     * Fetch records that have <code>line_price BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfLinePrice(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseTable.COURSE.LINE_PRICE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>line_price IN (values)</code>
     */
    public List<Course> fetchByLinePrice(Long... values) {
        return fetch(CourseTable.COURSE.LINE_PRICE, values);
    }

    /**
     * Fetch records that have <code>duration BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<Course> fetchRangeOfDuration(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseTable.COURSE.DURATION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>duration IN (values)</code>
     */
    public List<Course> fetchByDuration(String... values) {
        return fetch(CourseTable.COURSE.DURATION, values);
    }
}
