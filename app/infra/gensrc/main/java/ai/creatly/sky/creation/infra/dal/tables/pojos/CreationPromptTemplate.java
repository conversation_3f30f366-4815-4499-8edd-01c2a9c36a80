/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.JSONB;


/**
 * 提示词模板
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationPromptTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String bizType;
    private String code;
    private String title;
    private String category;
    private JSONB prompt;
    private String description;

    public CreationPromptTemplate() {}

    public CreationPromptTemplate(CreationPromptTemplate value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.bizType = value.bizType;
        this.code = value.code;
        this.title = value.title;
        this.category = value.category;
        this.prompt = value.prompt;
        this.description = value.description;
    }

    public CreationPromptTemplate(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        String bizType,
        String code,
        String title,
        String category,
        JSONB prompt,
        String description
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.bizType = bizType;
        this.code = code;
        this.title = title;
        this.category = category;
        this.prompt = prompt;
        this.description = description;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_prompt_template.id</code>. 主键
     */
    public CreationPromptTemplate setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.created_at</code>.
     * 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.creation_prompt_template.created_at</code>.
     * 创建时间
     */
    public CreationPromptTemplate setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.updated_at</code>.
     * 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.creation_prompt_template.updated_at</code>.
     * 更新时间
     */
    public CreationPromptTemplate setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.biz_type</code>. 业务分类
     */
    public String getBizType() {
        return this.bizType;
    }

    /**
     * Setter for <code>creation.creation_prompt_template.biz_type</code>. 业务分类
     */
    public CreationPromptTemplate setBizType(String bizType) {
        this.bizType = bizType;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.code</code>. 模板编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * Setter for <code>creation.creation_prompt_template.code</code>. 模板编号
     */
    public CreationPromptTemplate setCode(String code) {
        this.code = code;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.title</code>. 标题
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * Setter for <code>creation.creation_prompt_template.title</code>. 标题
     */
    public CreationPromptTemplate setTitle(String title) {
        this.title = title;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.category</code>. 类目编号
     */
    public String getCategory() {
        return this.category;
    }

    /**
     * Setter for <code>creation.creation_prompt_template.category</code>. 类目编号
     */
    public CreationPromptTemplate setCategory(String category) {
        this.category = category;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.prompt</code>. 提示词
     */
    public JSONB getPrompt() {
        return this.prompt;
    }

    /**
     * Setter for <code>creation.creation_prompt_template.prompt</code>. 提示词
     */
    public CreationPromptTemplate setPrompt(JSONB prompt) {
        this.prompt = prompt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_prompt_template.description</code>. 描述
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>creation.creation_prompt_template.description</code>. 描述
     */
    public CreationPromptTemplate setDescription(String description) {
        this.description = description;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationPromptTemplate other = (CreationPromptTemplate) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.bizType == null) {
            if (other.bizType != null)
                return false;
        }
        else if (!this.bizType.equals(other.bizType))
            return false;
        if (this.code == null) {
            if (other.code != null)
                return false;
        }
        else if (!this.code.equals(other.code))
            return false;
        if (this.title == null) {
            if (other.title != null)
                return false;
        }
        else if (!this.title.equals(other.title))
            return false;
        if (this.category == null) {
            if (other.category != null)
                return false;
        }
        else if (!this.category.equals(other.category))
            return false;
        if (this.prompt == null) {
            if (other.prompt != null)
                return false;
        }
        else if (!this.prompt.equals(other.prompt))
            return false;
        if (this.description == null) {
            if (other.description != null)
                return false;
        }
        else if (!this.description.equals(other.description))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.bizType == null) ? 0 : this.bizType.hashCode());
        result = prime * result + ((this.code == null) ? 0 : this.code.hashCode());
        result = prime * result + ((this.title == null) ? 0 : this.title.hashCode());
        result = prime * result + ((this.category == null) ? 0 : this.category.hashCode());
        result = prime * result + ((this.prompt == null) ? 0 : this.prompt.hashCode());
        result = prime * result + ((this.description == null) ? 0 : this.description.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationPromptTemplate (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(bizType);
        sb.append(", ").append(code);
        sb.append(", ").append(title);
        sb.append(", ").append(category);
        sb.append(", ").append(prompt);
        sb.append(", ").append(description);

        sb.append(")");
        return sb.toString();
    }
}
