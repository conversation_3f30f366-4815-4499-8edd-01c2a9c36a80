/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.CourseUserCertificateTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CourseUserCertificate;
import ai.creatly.sky.creation.infra.dal.tables.records.CourseUserCertificateRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 用户证书
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class CourseUserCertificateDAO extends DAOImpl<CourseUserCertificateRecord, CourseUserCertificate, Long> {

    /**
     * Create a new CourseUserCertificateDAO without any configuration
     */
    public CourseUserCertificateDAO() {
        super(CourseUserCertificateTable.COURSE_USER_CERTIFICATE, CourseUserCertificate.class);
    }

    /**
     * Create a new CourseUserCertificateDAO with an attached configuration
     */
    @Autowired
    public CourseUserCertificateDAO(Configuration configuration) {
        super(CourseUserCertificateTable.COURSE_USER_CERTIFICATE, CourseUserCertificate.class, configuration);
    }

    @Override
    public Long getId(CourseUserCertificate object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseUserCertificate> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CourseUserCertificate> fetchById(Long... values) {
        return fetch(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CourseUserCertificate fetchOneById(Long value) {
        return fetchOne(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CourseUserCertificate> fetchOptionalById(Long value) {
        return fetchOptional(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.ID, value);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseUserCertificate> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CourseUserCertificate> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseUserCertificate> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CourseUserCertificate> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.UPDATED_AT, values);
    }

    /**
     * Fetch records that have <code>cover_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseUserCertificate> fetchRangeOfCoverUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.COVER_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cover_url IN (values)</code>
     */
    public List<CourseUserCertificate> fetchByCoverUrl(String... values) {
        return fetch(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.COVER_URL, values);
    }

    /**
     * Fetch records that have <code>cert_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseUserCertificate> fetchRangeOfCertId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.CERT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cert_id IN (values)</code>
     */
    public List<CourseUserCertificate> fetchByCertId(Long... values) {
        return fetch(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.CERT_ID, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseUserCertificate> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<CourseUserCertificate> fetchByName(String... values) {
        return fetch(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.NAME, values);
    }

    /**
     * Fetch records that have <code>score BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseUserCertificate> fetchRangeOfScore(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.SCORE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>score IN (values)</code>
     */
    public List<CourseUserCertificate> fetchByScore(Long... values) {
        return fetch(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.SCORE, values);
    }

    /**
     * Fetch records that have <code>uid BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseUserCertificate> fetchRangeOfUid(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.UID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>uid IN (values)</code>
     */
    public List<CourseUserCertificate> fetchByUid(Long... values) {
        return fetch(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.UID, values);
    }

    /**
     * Fetch records that have <code>org_code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseUserCertificate> fetchRangeOfOrgCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.ORG_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_code IN (values)</code>
     */
    public List<CourseUserCertificate> fetchByOrgCode(String... values) {
        return fetch(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.ORG_CODE, values);
    }

    /**
     * Fetch records that have <code>limit_exam BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CourseUserCertificate> fetchRangeOfLimitExam(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.LIMIT_EXAM, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>limit_exam IN (values)</code>
     */
    public List<CourseUserCertificate> fetchByLimitExam(Long... values) {
        return fetch(CourseUserCertificateTable.COURSE_USER_CERTIFICATE.LIMIT_EXAM, values);
    }
}
