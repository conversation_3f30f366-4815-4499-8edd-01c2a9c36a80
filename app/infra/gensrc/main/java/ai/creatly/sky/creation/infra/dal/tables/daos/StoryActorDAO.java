/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.daos;


import ai.creatly.sky.creation.infra.dal.tables.StoryActorTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationStoryActor;
import ai.creatly.sky.creation.infra.dal.tables.records.StoryActorRecord;

import jakarta.annotation.Nullable;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.processing.Generated;

import org.jooq.Configuration;
import org.jooq.Converter;
import org.jooq.JSONB;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 故事演员
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class StoryActorDAO extends DAOImpl<StoryActorRecord, CreationStoryActor, Long> {

    /**
     * Create a new StoryActorDAO without any configuration
     */
    public StoryActorDAO() {
        super(StoryActorTable.CREATION_STORY_ACTOR, CreationStoryActor.class);
    }

    /**
     * Create a new StoryActorDAO with an attached configuration
     */
    @Autowired
    public StoryActorDAO(Configuration configuration) {
        super(StoryActorTable.CREATION_STORY_ACTOR, CreationStoryActor.class, configuration);
    }

    @Override
    public Long getId(CreationStoryActor object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryActor> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(StoryActorTable.CREATION_STORY_ACTOR.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<CreationStoryActor> fetchById(Long... values) {
        return fetch(StoryActorTable.CREATION_STORY_ACTOR.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    @Nullable
    public CreationStoryActor fetchOneById(Long value) {
        return fetchOne(StoryActorTable.CREATION_STORY_ACTOR.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<CreationStoryActor> fetchOptionalById(Long value) {
        return fetchOptional(StoryActorTable.CREATION_STORY_ACTOR.ID, value);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryActor> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(StoryActorTable.CREATION_STORY_ACTOR.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<CreationStoryActor> fetchByName(String... values) {
        return fetch(StoryActorTable.CREATION_STORY_ACTOR.NAME, values);
    }

    /**
     * Fetch records that have <code>gender BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryActor> fetchRangeOfGender(String lowerInclusive, String upperInclusive) {
        return fetchRange(StoryActorTable.CREATION_STORY_ACTOR.GENDER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>gender IN (values)</code>
     */
    public List<CreationStoryActor> fetchByGender(String... values) {
        return fetch(StoryActorTable.CREATION_STORY_ACTOR.GENDER, values);
    }

    /**
     * Fetch records that have <code>age BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryActor> fetchRangeOfAge(String lowerInclusive, String upperInclusive) {
        return fetchRange(StoryActorTable.CREATION_STORY_ACTOR.AGE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>age IN (values)</code>
     */
    public List<CreationStoryActor> fetchByAge(String... values) {
        return fetch(StoryActorTable.CREATION_STORY_ACTOR.AGE, values);
    }

    /**
     * Fetch records that have <code>weight BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryActor> fetchRangeOfWeight(String lowerInclusive, String upperInclusive) {
        return fetchRange(StoryActorTable.CREATION_STORY_ACTOR.WEIGHT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>weight IN (values)</code>
     */
    public List<CreationStoryActor> fetchByWeight(String... values) {
        return fetch(StoryActorTable.CREATION_STORY_ACTOR.WEIGHT, values);
    }

    /**
     * Fetch records that have <code>portrait_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryActor> fetchRangeOfPortraitUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(StoryActorTable.CREATION_STORY_ACTOR.PORTRAIT_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>portrait_url IN (values)</code>
     */
    public List<CreationStoryActor> fetchByPortraitUrl(String... values) {
        return fetch(StoryActorTable.CREATION_STORY_ACTOR.PORTRAIT_URL, values);
    }

    /**
     * Fetch records that have <code>default_voice BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryActor> fetchRangeOfDefaultVoice(String lowerInclusive, String upperInclusive) {
        return fetchRange(StoryActorTable.CREATION_STORY_ACTOR.DEFAULT_VOICE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>default_voice IN (values)</code>
     */
    public List<CreationStoryActor> fetchByDefaultVoice(String... values) {
        return fetch(StoryActorTable.CREATION_STORY_ACTOR.DEFAULT_VOICE, values);
    }

    /**
     * Fetch records that have <code>ext BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryActor> fetchRangeOfExt(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(StoryActorTable.CREATION_STORY_ACTOR.EXT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ext IN (values)</code>
     */
    public List<CreationStoryActor> fetchByExt(JSONB... values) {
        return fetch(StoryActorTable.CREATION_STORY_ACTOR.EXT, values);
    }

    /**
     * Fetch records that have <code>creator BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryActor> fetchRangeOfCreator(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(StoryActorTable.CREATION_STORY_ACTOR.CREATOR, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creator IN (values)</code>
     */
    public List<CreationStoryActor> fetchByCreator(JSONB... values) {
        return fetch(StoryActorTable.CREATION_STORY_ACTOR.CREATOR, values);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryActor> fetchRangeOfCreatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(StoryActorTable.CREATION_STORY_ACTOR.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<CreationStoryActor> fetchByCreatedAt(ZonedDateTime... values) {
        return fetch(StoryActorTable.CREATION_STORY_ACTOR.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<CreationStoryActor> fetchRangeOfUpdatedAt(ZonedDateTime lowerInclusive, ZonedDateTime upperInclusive) {
        return fetchRange(StoryActorTable.CREATION_STORY_ACTOR.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<CreationStoryActor> fetchByUpdatedAt(ZonedDateTime... values) {
        return fetch(StoryActorTable.CREATION_STORY_ACTOR.UPDATED_AT, values);
    }
}
