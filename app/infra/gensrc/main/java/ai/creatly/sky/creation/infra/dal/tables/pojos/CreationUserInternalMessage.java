/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 用户站内消息
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CreationUserInternalMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String sourceId;
    private String sourceType;
    private String messageType;
    private String bizType;
    private String content;
    private String status;
    private ZonedDateTime readTime;

    public CreationUserInternalMessage() {}

    public CreationUserInternalMessage(CreationUserInternalMessage value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.sourceId = value.sourceId;
        this.sourceType = value.sourceType;
        this.messageType = value.messageType;
        this.bizType = value.bizType;
        this.content = value.content;
        this.status = value.status;
        this.readTime = value.readTime;
    }

    public CreationUserInternalMessage(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String sourceId,
        String sourceType,
        String messageType,
        String bizType,
        String content,
        String status,
        @Nullable ZonedDateTime readTime
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.sourceId = sourceId;
        this.sourceType = sourceType;
        this.messageType = messageType;
        this.bizType = bizType;
        this.content = content;
        this.status = status;
        this.readTime = readTime;
    }

    /**
     * Getter for <code>creation.creation_user_internal_message.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.creation_user_internal_message.id</code>. 主键
     */
    public CreationUserInternalMessage setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_internal_message.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for
     * <code>creation.creation_user_internal_message.created_at</code>. 创建时间
     */
    public CreationUserInternalMessage setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_internal_message.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for
     * <code>creation.creation_user_internal_message.updated_at</code>. 更新时间
     */
    public CreationUserInternalMessage setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_internal_message.uid</code>.
     * 消息接收方
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.creation_user_internal_message.uid</code>.
     * 消息接收方
     */
    public CreationUserInternalMessage setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_internal_message.source_id</code>. 消息来源方表示id
     */
    public String getSourceId() {
        return this.sourceId;
    }

    /**
     * Setter for
     * <code>creation.creation_user_internal_message.source_id</code>. 消息来源方表示id
     */
    public CreationUserInternalMessage setSourceId(String sourceId) {
        this.sourceId = sourceId;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_internal_message.source_type</code>. 消息来源方
     */
    public String getSourceType() {
        return this.sourceType;
    }

    /**
     * Setter for
     * <code>creation.creation_user_internal_message.source_type</code>. 消息来源方
     */
    public CreationUserInternalMessage setSourceType(String sourceType) {
        this.sourceType = sourceType;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_internal_message.message_type</code>. 消息类型
     */
    public String getMessageType() {
        return this.messageType;
    }

    /**
     * Setter for
     * <code>creation.creation_user_internal_message.message_type</code>. 消息类型
     */
    public CreationUserInternalMessage setMessageType(String messageType) {
        this.messageType = messageType;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_internal_message.biz_type</code>.
     * 业务类型
     */
    public String getBizType() {
        return this.bizType;
    }

    /**
     * Setter for <code>creation.creation_user_internal_message.biz_type</code>.
     * 业务类型
     */
    public CreationUserInternalMessage setBizType(String bizType) {
        this.bizType = bizType;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_internal_message.content</code>.
     * 消息内容
     */
    public String getContent() {
        return this.content;
    }

    /**
     * Setter for <code>creation.creation_user_internal_message.content</code>.
     * 消息内容
     */
    public CreationUserInternalMessage setContent(String content) {
        this.content = content;
        return this;
    }

    /**
     * Getter for <code>creation.creation_user_internal_message.status</code>.
     * 当前状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.creation_user_internal_message.status</code>.
     * 当前状态
     */
    public CreationUserInternalMessage setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for
     * <code>creation.creation_user_internal_message.read_time</code>. 确认时间
     */
    @Nullable
    public ZonedDateTime getReadTime() {
        return this.readTime;
    }

    /**
     * Setter for
     * <code>creation.creation_user_internal_message.read_time</code>. 确认时间
     */
    public CreationUserInternalMessage setReadTime(@Nullable ZonedDateTime readTime) {
        this.readTime = readTime;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CreationUserInternalMessage other = (CreationUserInternalMessage) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.sourceId == null) {
            if (other.sourceId != null)
                return false;
        }
        else if (!this.sourceId.equals(other.sourceId))
            return false;
        if (this.sourceType == null) {
            if (other.sourceType != null)
                return false;
        }
        else if (!this.sourceType.equals(other.sourceType))
            return false;
        if (this.messageType == null) {
            if (other.messageType != null)
                return false;
        }
        else if (!this.messageType.equals(other.messageType))
            return false;
        if (this.bizType == null) {
            if (other.bizType != null)
                return false;
        }
        else if (!this.bizType.equals(other.bizType))
            return false;
        if (this.content == null) {
            if (other.content != null)
                return false;
        }
        else if (!this.content.equals(other.content))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.readTime == null) {
            if (other.readTime != null)
                return false;
        }
        else if (!this.readTime.equals(other.readTime))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.sourceId == null) ? 0 : this.sourceId.hashCode());
        result = prime * result + ((this.sourceType == null) ? 0 : this.sourceType.hashCode());
        result = prime * result + ((this.messageType == null) ? 0 : this.messageType.hashCode());
        result = prime * result + ((this.bizType == null) ? 0 : this.bizType.hashCode());
        result = prime * result + ((this.content == null) ? 0 : this.content.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.readTime == null) ? 0 : this.readTime.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreationUserInternalMessage (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(sourceId);
        sb.append(", ").append(sourceType);
        sb.append(", ").append(messageType);
        sb.append(", ").append(bizType);
        sb.append(", ").append(content);
        sb.append(", ").append(status);
        sb.append(", ").append(readTime);

        sb.append(")");
        return sb.toString();
    }
}
