/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.AiToolTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.CreationAiTool;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * AI工具集
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiToolRecord extends UpdatableRecordImpl<AiToolRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.creation_ai_tool.id</code>. id
     */
    public AiToolRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.id</code>. id
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.created_at</code>. 创建时间
     */
    public AiToolRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.updated_at</code>. 更新时间
     */
    public AiToolRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.title</code>. 工具名称
     */
    public AiToolRecord setTitle(@Nullable String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.title</code>. 工具名称
     */
    @Nullable
    public String getTitle() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.cover_url</code>. 工具封面
     */
    public AiToolRecord setCoverUrl(@Nullable String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.cover_url</code>. 工具封面
     */
    @Nullable
    public String getCoverUrl() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.priority</code>. 推荐优先级
     */
    public AiToolRecord setPriority(@Nullable Long value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.priority</code>. 推荐优先级
     */
    @Nullable
    public Long getPriority() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.is_hot</code>. 是否推荐
     */
    public AiToolRecord setIsHot(@Nullable Short value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.is_hot</code>. 是否推荐
     */
    @Nullable
    public Short getIsHot() {
        return (Short) get(6);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.status</code>. 启用状态
     */
    public AiToolRecord setStatus(@Nullable Short value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.status</code>. 启用状态
     */
    @Nullable
    public Short getStatus() {
        return (Short) get(7);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.hidden</code>. 是否显示
     */
    public AiToolRecord setHidden(@Nullable Short value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.hidden</code>. 是否显示
     */
    @Nullable
    public Short getHidden() {
        return (Short) get(8);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.path</code>. 跳转路径
     */
    public AiToolRecord setPath(@Nullable String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.path</code>. 跳转路径
     */
    @Nullable
    public String getPath() {
        return (String) get(9);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.sub_title</code>. 副标题
     */
    public AiToolRecord setSubTitle(@Nullable String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.sub_title</code>. 副标题
     */
    @Nullable
    public String getSubTitle() {
        return (String) get(10);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.init_param</code>. 初始化参数
     */
    public AiToolRecord setInitParam(@Nullable String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.init_param</code>. 初始化参数
     */
    @Nullable
    public String getInitParam() {
        return (String) get(11);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.category</code>. 工具类目
     */
    public AiToolRecord setCategory(@Nullable String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.category</code>. 工具类目
     */
    @Nullable
    public String getCategory() {
        return (String) get(12);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.credits</code>. 消耗元气
     */
    public AiToolRecord setCredits(@Nullable Long value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.credits</code>. 消耗元气
     */
    @Nullable
    public Long getCredits() {
        return (Long) get(13);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.model</code>. 模型
     */
    public AiToolRecord setModel(@Nullable String value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.model</code>. 模型
     */
    @Nullable
    public String getModel() {
        return (String) get(14);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.biz_type</code>. 类型
     */
    public AiToolRecord setBizType(@Nullable String value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.biz_type</code>. 类型
     */
    @Nullable
    public String getBizType() {
        return (String) get(15);
    }

    /**
     * Setter for <code>creation.creation_ai_tool.function</code>. 功能
     */
    public AiToolRecord setFunction(@Nullable String value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>creation.creation_ai_tool.function</code>. 功能
     */
    @Nullable
    public String getFunction() {
        return (String) get(16);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AiToolRecord
     */
    public AiToolRecord() {
        super(AiToolTable.CREATION_AI_TOOL);
    }

    /**
     * Create a detached, initialised AiToolRecord
     */
    public AiToolRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, @Nullable String title, @Nullable String coverUrl, @Nullable Long priority, @Nullable Short isHot, @Nullable Short status, @Nullable Short hidden, @Nullable String path, @Nullable String subTitle, @Nullable String initParam, @Nullable String category, @Nullable Long credits, @Nullable String model, @Nullable String bizType, @Nullable String function) {
        super(AiToolTable.CREATION_AI_TOOL);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setTitle(title);
        setCoverUrl(coverUrl);
        setPriority(priority);
        setIsHot(isHot);
        setStatus(status);
        setHidden(hidden);
        setPath(path);
        setSubTitle(subTitle);
        setInitParam(initParam);
        setCategory(category);
        setCredits(credits);
        setModel(model);
        setBizType(bizType);
        setFunction(function);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised AiToolRecord
     */
    public AiToolRecord(CreationAiTool value) {
        super(AiToolTable.CREATION_AI_TOOL);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setTitle(value.getTitle());
            setCoverUrl(value.getCoverUrl());
            setPriority(value.getPriority());
            setIsHot(value.getIsHot());
            setStatus(value.getStatus());
            setHidden(value.getHidden());
            setPath(value.getPath());
            setSubTitle(value.getSubTitle());
            setInitParam(value.getInitParam());
            setCategory(value.getCategory());
            setCredits(value.getCredits());
            setModel(value.getModel());
            setBizType(value.getBizType());
            setFunction(value.getFunction());
            resetChangedOnNotNull();
        }
    }
}
