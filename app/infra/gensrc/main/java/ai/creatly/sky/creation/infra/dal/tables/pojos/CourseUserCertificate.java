/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 用户证书
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class CourseUserCertificate implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String coverUrl;
    private Long certId;
    private String name;
    private Long score;
    private Long uid;
    private String orgCode;
    private Long limitExam;

    public CourseUserCertificate() {}

    public CourseUserCertificate(CourseUserCertificate value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.coverUrl = value.coverUrl;
        this.certId = value.certId;
        this.name = value.name;
        this.score = value.score;
        this.uid = value.uid;
        this.orgCode = value.orgCode;
        this.limitExam = value.limitExam;
    }

    public CourseUserCertificate(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        @Nullable String coverUrl,
        @Nullable Long certId,
        @Nullable String name,
        @Nullable Long score,
        @Nullable Long uid,
        @Nullable String orgCode,
        @Nullable Long limitExam
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.coverUrl = coverUrl;
        this.certId = certId;
        this.name = name;
        this.score = score;
        this.uid = uid;
        this.orgCode = orgCode;
        this.limitExam = limitExam;
    }

    /**
     * Getter for <code>creation.course_user_certificate.id</code>. 用户证书id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.course_user_certificate.id</code>. 用户证书id
     */
    public CourseUserCertificate setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.course_user_certificate.created_at</code>. 创建时间
     */
    public CourseUserCertificate setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.course_user_certificate.updated_at</code>. 更新时间
     */
    public CourseUserCertificate setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.cover_url</code>. 证书封面
     */
    @Nullable
    public String getCoverUrl() {
        return this.coverUrl;
    }

    /**
     * Setter for <code>creation.course_user_certificate.cover_url</code>. 证书封面
     */
    public CourseUserCertificate setCoverUrl(@Nullable String coverUrl) {
        this.coverUrl = coverUrl;
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.cert_id</code>. 证书id
     */
    @Nullable
    public Long getCertId() {
        return this.certId;
    }

    /**
     * Setter for <code>creation.course_user_certificate.cert_id</code>. 证书id
     */
    public CourseUserCertificate setCertId(@Nullable Long certId) {
        this.certId = certId;
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.name</code>. 证书名称
     */
    @Nullable
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>creation.course_user_certificate.name</code>. 证书名称
     */
    public CourseUserCertificate setName(@Nullable String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.score</code>. 总分
     */
    @Nullable
    public Long getScore() {
        return this.score;
    }

    /**
     * Setter for <code>creation.course_user_certificate.score</code>. 总分
     */
    public CourseUserCertificate setScore(@Nullable Long score) {
        this.score = score;
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.uid</code>. 用户id
     */
    @Nullable
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.course_user_certificate.uid</code>. 用户id
     */
    public CourseUserCertificate setUid(@Nullable Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.org_code</code>. 组织机构
     */
    @Nullable
    public String getOrgCode() {
        return this.orgCode;
    }

    /**
     * Setter for <code>creation.course_user_certificate.org_code</code>. 组织机构
     */
    public CourseUserCertificate setOrgCode(@Nullable String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    /**
     * Getter for <code>creation.course_user_certificate.limit_exam</code>. 限制次数
     */
    @Nullable
    public Long getLimitExam() {
        return this.limitExam;
    }

    /**
     * Setter for <code>creation.course_user_certificate.limit_exam</code>. 限制次数
     */
    public CourseUserCertificate setLimitExam(@Nullable Long limitExam) {
        this.limitExam = limitExam;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final CourseUserCertificate other = (CourseUserCertificate) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.coverUrl == null) {
            if (other.coverUrl != null)
                return false;
        }
        else if (!this.coverUrl.equals(other.coverUrl))
            return false;
        if (this.certId == null) {
            if (other.certId != null)
                return false;
        }
        else if (!this.certId.equals(other.certId))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.score == null) {
            if (other.score != null)
                return false;
        }
        else if (!this.score.equals(other.score))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.orgCode == null) {
            if (other.orgCode != null)
                return false;
        }
        else if (!this.orgCode.equals(other.orgCode))
            return false;
        if (this.limitExam == null) {
            if (other.limitExam != null)
                return false;
        }
        else if (!this.limitExam.equals(other.limitExam))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.coverUrl == null) ? 0 : this.coverUrl.hashCode());
        result = prime * result + ((this.certId == null) ? 0 : this.certId.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.score == null) ? 0 : this.score.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.orgCode == null) ? 0 : this.orgCode.hashCode());
        result = prime * result + ((this.limitExam == null) ? 0 : this.limitExam.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseUserCertificate (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(coverUrl);
        sb.append(", ").append(certId);
        sb.append(", ").append(name);
        sb.append(", ").append(score);
        sb.append(", ").append(uid);
        sb.append(", ").append(orgCode);
        sb.append(", ").append(limitExam);

        sb.append(")");
        return sb.toString();
    }
}
