/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables;


import ai.creatly.sky.creation.infra.dal.CreationSchema;
import ai.creatly.sky.creation.infra.dal.Keys;
import ai.creatly.sky.creation.infra.dal.tables.records.ProductVideoRecord;

import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Collection;

import javax.annotation.processing.Generated;

import org.jooq.Condition;
import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;
import org.jooq.types.YearToSecond;


/**
 * 商品视频
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class ProductVideoTable extends TableImpl<ProductVideoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>creation.creation_product_video</code>
     */
    public static final ProductVideoTable CREATION_PRODUCT_VIDEO = new ProductVideoTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ProductVideoRecord> getRecordType() {
        return ProductVideoRecord.class;
    }

    /**
     * The column <code>creation.creation_product_video.id</code>. 主键ID
     */
    public final TableField<ProductVideoRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "主键ID");

    /**
     * The column <code>creation.creation_product_video.created_at</code>. 创建时间
     */
    public final TableField<ProductVideoRecord, ZonedDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "创建时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_product_video.updated_at</code>. 更新时间
     */
    public final TableField<ProductVideoRecord, ZonedDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(3).nullable(false).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.TIMESTAMPWITHTIMEZONE)), this, "更新时间", Converter.ofNullable(OffsetDateTime.class, ZonedDateTime.class, ZonedDateTime::from, ZonedDateTime::toOffsetDateTime));

    /**
     * The column <code>creation.creation_product_video.uid</code>. 所属用户ID（1为平台）
     */
    public final TableField<ProductVideoRecord, Long> UID = createField(DSL.name("uid"), SQLDataType.BIGINT.nullable(false), this, "所属用户ID（1为平台）");

    /**
     * The column <code>creation.creation_product_video.status</code>. 状态
     */
    public final TableField<ProductVideoRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(16).nullable(false), this, "状态");

    /**
     * The column <code>creation.creation_product_video.title</code>. 标题
     */
    public final TableField<ProductVideoRecord, String> TITLE = createField(DSL.name("title"), SQLDataType.VARCHAR(50).nullable(false), this, "标题");

    /**
     * The column <code>creation.creation_product_video.description</code>. 描述
     */
    public final TableField<ProductVideoRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(200).nullable(false), this, "描述");

    /**
     * The column <code>creation.creation_product_video.cover_file_id</code>.
     * 封面图文件ID
     */
    public final TableField<ProductVideoRecord, Long> COVER_FILE_ID = createField(DSL.name("cover_file_id"), SQLDataType.BIGINT, this, "封面图文件ID");

    /**
     * The column <code>creation.creation_product_video.cover_url</code>. 封面图地址
     */
    public final TableField<ProductVideoRecord, String> COVER_URL = createField(DSL.name("cover_url"), SQLDataType.VARCHAR(150), this, "封面图地址");

    /**
     * The column <code>creation.creation_product_video.video_file_id</code>.
     * 视频文件ID
     */
    public final TableField<ProductVideoRecord, Long> VIDEO_FILE_ID = createField(DSL.name("video_file_id"), SQLDataType.BIGINT.nullable(false), this, "视频文件ID");

    /**
     * The column <code>creation.creation_product_video.video_url</code>.
     * 视频地址（OSS格式）
     */
    public final TableField<ProductVideoRecord, String> VIDEO_URL = createField(DSL.name("video_url"), SQLDataType.VARCHAR(150).nullable(false), this, "视频地址（OSS格式）");

    /**
     * The column <code>creation.creation_product_video.video_duration</code>.
     * 视频时长
     */
    public final TableField<ProductVideoRecord, Duration> VIDEO_DURATION = createField(DSL.name("video_duration"), SQLDataType.INTERVAL.nullable(false), this, "视频时长", Converter.ofNullable(YearToSecond.class, Duration.class, YearToSecond::toDuration, YearToSecond::valueOf));

    /**
     * The column <code>creation.creation_product_video.video_format</code>.
     * 视频格式（小写）
     */
    public final TableField<ProductVideoRecord, String> VIDEO_FORMAT = createField(DSL.name("video_format"), SQLDataType.VARCHAR(16).nullable(false), this, "视频格式（小写）");

    /**
     * The column <code>creation.creation_product_video.video_resolution</code>.
     * 视频分辨率（宽度x高度）
     */
    public final TableField<ProductVideoRecord, String> VIDEO_RESOLUTION = createField(DSL.name("video_resolution"), SQLDataType.VARCHAR(32), this, "视频分辨率（宽度x高度）");

    /**
     * The column <code>creation.creation_product_video.audio_file_id</code>.
     * 音频文件ID
     */
    public final TableField<ProductVideoRecord, Long> AUDIO_FILE_ID = createField(DSL.name("audio_file_id"), SQLDataType.BIGINT, this, "音频文件ID");

    /**
     * The column <code>creation.creation_product_video.audio_url</code>.
     * 音频地址（OSS格式）
     */
    public final TableField<ProductVideoRecord, String> AUDIO_URL = createField(DSL.name("audio_url"), SQLDataType.VARCHAR(150), this, "音频地址（OSS格式）");

    /**
     * The column <code>creation.creation_product_video.audio_metadata</code>.
     * 音频元信息
     */
    public final TableField<ProductVideoRecord, JSONB> AUDIO_METADATA = createField(DSL.name("audio_metadata"), SQLDataType.JSONB, this, "音频元信息");

    /**
     * The column <code>creation.creation_product_video.creator</code>. 创建者
     */
    public final TableField<ProductVideoRecord, JSONB> CREATOR = createField(DSL.name("creator"), SQLDataType.JSONB.nullable(false), this, "创建者");

    private ProductVideoTable(Name alias, Table<ProductVideoRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private ProductVideoTable(Name alias, Table<ProductVideoRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment("商品视频"), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>creation.creation_product_video</code> table
     * reference
     */
    public ProductVideoTable(String alias) {
        this(DSL.name(alias), CREATION_PRODUCT_VIDEO);
    }

    /**
     * Create an aliased <code>creation.creation_product_video</code> table
     * reference
     */
    public ProductVideoTable(Name alias) {
        this(alias, CREATION_PRODUCT_VIDEO);
    }

    /**
     * Create a <code>creation.creation_product_video</code> table reference
     */
    public ProductVideoTable() {
        this(DSL.name("creation_product_video"), null);
    }

    @Override
    @Nullable
    public Schema getSchema() {
        return aliased() ? null : CreationSchema.CREATION;
    }

    @Override
    public UniqueKey<ProductVideoRecord> getPrimaryKey() {
        return Keys.CREATION_PRODUCT_VIDEO_PKEY;
    }

    @Override
    public ProductVideoTable as(String alias) {
        return new ProductVideoTable(DSL.name(alias), this);
    }

    @Override
    public ProductVideoTable as(Name alias) {
        return new ProductVideoTable(alias, this);
    }

    @Override
    public ProductVideoTable as(Table<?> alias) {
        return new ProductVideoTable(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ProductVideoTable rename(String name) {
        return new ProductVideoTable(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ProductVideoTable rename(Name name) {
        return new ProductVideoTable(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ProductVideoTable rename(Table<?> name) {
        return new ProductVideoTable(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ProductVideoTable where(Condition condition) {
        return new ProductVideoTable(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ProductVideoTable where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ProductVideoTable where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ProductVideoTable where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ProductVideoTable where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ProductVideoTable where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ProductVideoTable where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ProductVideoTable where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ProductVideoTable whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ProductVideoTable whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
