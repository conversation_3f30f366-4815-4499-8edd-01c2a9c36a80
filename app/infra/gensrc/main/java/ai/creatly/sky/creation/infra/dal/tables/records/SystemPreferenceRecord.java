/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.records;


import ai.creatly.sky.creation.infra.dal.tables.SystemPreferenceTable;
import ai.creatly.sky.creation.infra.dal.tables.pojos.SystemPreference;

import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 系统配置中心
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class SystemPreferenceRecord extends UpdatableRecordImpl<SystemPreferenceRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>creation.system_preference.id</code>. 主键
     */
    public SystemPreferenceRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>creation.system_preference.id</code>. 主键
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>creation.system_preference.created_at</code>. 创建时间
     */
    public SystemPreferenceRecord setCreatedAt(@Nullable ZonedDateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>creation.system_preference.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return (ZonedDateTime) get(1);
    }

    /**
     * Setter for <code>creation.system_preference.updated_at</code>. 更新时间
     */
    public SystemPreferenceRecord setUpdatedAt(@Nullable ZonedDateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>creation.system_preference.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return (ZonedDateTime) get(2);
    }

    /**
     * Setter for <code>creation.system_preference.config_key</code>. 配置项
     */
    public SystemPreferenceRecord setConfigKey(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>creation.system_preference.config_key</code>. 配置项
     */
    public String getConfigKey() {
        return (String) get(3);
    }

    /**
     * Setter for <code>creation.system_preference.config_value</code>. 配置值
     */
    public SystemPreferenceRecord setConfigValue(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>creation.system_preference.config_value</code>. 配置值
     */
    public String getConfigValue() {
        return (String) get(4);
    }

    /**
     * Setter for <code>creation.system_preference.operator</code>. 操作人
     */
    public SystemPreferenceRecord setOperator(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>creation.system_preference.operator</code>. 操作人
     */
    public String getOperator() {
        return (String) get(5);
    }

    /**
     * Setter for <code>creation.system_preference.status</code>. 状态
     * VALID/INVALID
     */
    public SystemPreferenceRecord setStatus(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>creation.system_preference.status</code>. 状态
     * VALID/INVALID
     */
    public String getStatus() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SystemPreferenceRecord
     */
    public SystemPreferenceRecord() {
        super(SystemPreferenceTable.SYSTEM_PREFERENCE);
    }

    /**
     * Create a detached, initialised SystemPreferenceRecord
     */
    public SystemPreferenceRecord(Long id, @Nullable ZonedDateTime createdAt, @Nullable ZonedDateTime updatedAt, String configKey, String configValue, String operator, String status) {
        super(SystemPreferenceTable.SYSTEM_PREFERENCE);

        setId(id);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        setConfigKey(configKey);
        setConfigValue(configValue);
        setOperator(operator);
        setStatus(status);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised SystemPreferenceRecord
     */
    public SystemPreferenceRecord(SystemPreference value) {
        super(SystemPreferenceTable.SYSTEM_PREFERENCE);

        if (value != null) {
            setId(value.getId());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
            setConfigKey(value.getConfigKey());
            setConfigValue(value.getConfigValue());
            setOperator(value.getOperator());
            setStatus(value.getStatus());
            resetChangedOnNotNull();
        }
    }
}
