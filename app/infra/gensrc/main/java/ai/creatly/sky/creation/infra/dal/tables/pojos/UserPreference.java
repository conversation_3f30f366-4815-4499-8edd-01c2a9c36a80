/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.infra.dal.tables.pojos;


import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.ZonedDateTime;

import javax.annotation.processing.Generated;


/**
 * 用户配置中心
 */
@Generated(
    value = {
        "https://www.jooq.org",
        "jOOQ version:3.19.11"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserPreference implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long uid;
    private String configKey;
    private String configValue;
    private String operator;
    private String status;

    public UserPreference() {}

    public UserPreference(UserPreference value) {
        this.id = value.id;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
        this.uid = value.uid;
        this.configKey = value.configKey;
        this.configValue = value.configValue;
        this.operator = value.operator;
        this.status = value.status;
    }

    public UserPreference(
        Long id,
        @Nullable ZonedDateTime createdAt,
        @Nullable ZonedDateTime updatedAt,
        Long uid,
        String configKey,
        String configValue,
        String operator,
        String status
    ) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.uid = uid;
        this.configKey = configKey;
        this.configValue = configValue;
        this.operator = operator;
        this.status = status;
    }

    /**
     * Getter for <code>creation.user_preference.id</code>. 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>creation.user_preference.id</code>. 主键
     */
    public UserPreference setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.created_at</code>. 创建时间
     */
    @Nullable
    public ZonedDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>creation.user_preference.created_at</code>. 创建时间
     */
    public UserPreference setCreatedAt(@Nullable ZonedDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.updated_at</code>. 更新时间
     */
    @Nullable
    public ZonedDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>creation.user_preference.updated_at</code>. 更新时间
     */
    public UserPreference setUpdatedAt(@Nullable ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.uid</code>. 用户id
     */
    public Long getUid() {
        return this.uid;
    }

    /**
     * Setter for <code>creation.user_preference.uid</code>. 用户id
     */
    public UserPreference setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.config_key</code>. 配置项
     */
    public String getConfigKey() {
        return this.configKey;
    }

    /**
     * Setter for <code>creation.user_preference.config_key</code>. 配置项
     */
    public UserPreference setConfigKey(String configKey) {
        this.configKey = configKey;
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.config_value</code>. 配置值
     */
    public String getConfigValue() {
        return this.configValue;
    }

    /**
     * Setter for <code>creation.user_preference.config_value</code>. 配置值
     */
    public UserPreference setConfigValue(String configValue) {
        this.configValue = configValue;
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.operator</code>. 操作人
     */
    public String getOperator() {
        return this.operator;
    }

    /**
     * Setter for <code>creation.user_preference.operator</code>. 操作人
     */
    public UserPreference setOperator(String operator) {
        this.operator = operator;
        return this;
    }

    /**
     * Getter for <code>creation.user_preference.status</code>. 状态 VALID/INVALID
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>creation.user_preference.status</code>. 状态 VALID/INVALID
     */
    public UserPreference setStatus(String status) {
        this.status = status;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final UserPreference other = (UserPreference) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.updatedAt == null) {
            if (other.updatedAt != null)
                return false;
        }
        else if (!this.updatedAt.equals(other.updatedAt))
            return false;
        if (this.uid == null) {
            if (other.uid != null)
                return false;
        }
        else if (!this.uid.equals(other.uid))
            return false;
        if (this.configKey == null) {
            if (other.configKey != null)
                return false;
        }
        else if (!this.configKey.equals(other.configKey))
            return false;
        if (this.configValue == null) {
            if (other.configValue != null)
                return false;
        }
        else if (!this.configValue.equals(other.configValue))
            return false;
        if (this.operator == null) {
            if (other.operator != null)
                return false;
        }
        else if (!this.operator.equals(other.operator))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.updatedAt == null) ? 0 : this.updatedAt.hashCode());
        result = prime * result + ((this.uid == null) ? 0 : this.uid.hashCode());
        result = prime * result + ((this.configKey == null) ? 0 : this.configKey.hashCode());
        result = prime * result + ((this.configValue == null) ? 0 : this.configValue.hashCode());
        result = prime * result + ((this.operator == null) ? 0 : this.operator.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("UserPreference (");

        sb.append(id);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);
        sb.append(", ").append(uid);
        sb.append(", ").append(configKey);
        sb.append(", ").append(configValue);
        sb.append(", ").append(operator);
        sb.append(", ").append(status);

        sb.append(")");
        return sb.toString();
    }
}
