/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.video.model.response;

import ai.creatly.sky.creation.domain.support.multimedia.model.AudioMetadata;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jspeeder.core.model.BizStatus;
import com.jspeeder.core.util.time.Dates;
import jakarta.annotation.Nullable;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * 商品视频
 *
 * <AUTHOR>
 * @version ProductVideoVM.java, v 0.1 2024-05-25 下午12:00 zhoudong
 */
@Data
public class ProductVideoVM {

    /**
     * 主键ID
     */
    private String        id;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = Dates.PATTERN_DATE_TIME)
    private ZonedDateTime createdAt;
    /**
     * 状态
     */
    private BizStatus     status;
    /**
     * 标题
     */
    private String        title;
    /**
     * 描述
     */
    private String        description;
    /**
     * 封面图地址（HTTP地址，3小时有效，使用时获取）
     */
    private String        coverUrl;
    /**
     * 视频地址（HTTP地址，3小时有效，使用时获取）
     */
    private String        videoUrl;
    /**
     * 视频时长
     */
    private Integer       videoMills;
    /**
     * 视频格式（小写）
     */
    private String        videoFormat;
    /**
     * 视频分辨率
     */
    @Nullable
    private String        videoResolution;
    /**
     * 音频地址（HTTP地址，3小时有效，使用时获取）
     */
    @Nullable
    private String        audioUrl;
    /**
     * 音频元信息
     */
    @Nullable
    private AudioMetadata audioMetadata;
}
