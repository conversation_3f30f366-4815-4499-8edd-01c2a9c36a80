/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.enums;

import ai.creatly.sky.creation.domain.support.trade.OrderBizType;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 定价计划类型
 *
 * <AUTHOR>
 * @version PlanType.java, v 0.1 2023-10-08 下午9:34 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum PlanType {

    SUBSCRIPTION("订阅", OrderBizType.SUBSCRIPTION),
    TOP_UP("充值", OrderBizType.TOP_UP),
    ;

    private final String desc;
    private final OrderBizType orderBizType;
}
