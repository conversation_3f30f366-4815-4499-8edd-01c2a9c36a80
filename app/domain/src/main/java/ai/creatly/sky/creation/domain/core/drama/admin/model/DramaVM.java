/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.admin.model;

import ai.creatly.sky.creation.domain.core.drama.model.DramaContent;
import ai.creatly.sky.creation.domain.core.drama.model.DramaStats;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaLocalSyncStatus;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaStatus;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaType;
import ai.creatly.sky.creation.domain.core.drama.model.response.DramaReviewVM;
import ai.creatly.sky.creation.domain.support.category.model.admin.model.CategoryPathVM;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version DramaVM.java, 2024-12-12 下午4:23 zhoudong
 */
@Data
public class DramaVM {

    private String               id;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime        createdAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime        updatedAt;
    /**
     * 用户ID
     */
    private String               uid;
    /**
     * 剧情名称
     */
    private String               name;
    /**
     * 剧情类型
     */
    private DramaType            type;
    /**
     * 剧情类型描述
     */
    private String               typeDesc;
    /**
     * 剧情状态
     */
    private DramaStatus          status;
    /**
     * 适用的产品类目
     */
    private List<CategoryPathVM> productCategories;
    /**
     * 适用的客群类目
     */
    private List<CategoryPathVM> crowdCategories;
    /**
     * 剧情相关视频
     */
    private List<DramaVideoVM>   videos;
    /**
     * 剧情总时长(秒)
     */
    private String               durationSeconds;
    /**
     * 剧情内容
     */
    private DramaContent         content;
    /**
     * 剧情封面图地址（HTTP协议）
     */
    @Nullable
    private String               coverUrl;
    /**
     * 剧情统计信息
     */
    private DramaStats           stats;
    /**
     * 剧情审核明细
     */
    private DramaReviewVM        review;
    /**
     * 本地剧情库同步状态
     */
    private DramaLocalSyncStatus localSyncStatus;
}
