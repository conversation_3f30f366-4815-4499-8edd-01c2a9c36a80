/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.category.model.admin.model;

import ai.creatly.sky.creation.domain.support.category.model.enums.CategoryDomain;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;

/**
 *
 * <AUTHOR>
 * @version CategoryVM.java, 2024-12-12 下午5:16 zhoudong
 */
@Data
public class CategoryVM {
    /**
     * 主键
     */
    private String           id;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime  createdAt;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime  updatedAt;
    /**
     * 类目域
     */
    private CategoryDomain domain;
    /**
     * 父类目编码
     */
    @Nullable
    private String         parentCode;
    /**
     * 类目编码
     */
    private String         code;
    /**
     * 类目名称
     */
    private String         name;
    /**
     * 是否为叶子节点
     */
    private Boolean        isLeaf;
    /**
     * 类目父路径（用 / 拼接类目编号）
     */
    @Nullable
    private String         parentPath;
    /**
     * 类目全路径（用 / 拼接类目编号）
     */
    private String         path;
    /**
     * 类目节点层级（从1开始，等于1则表示该类目为根节点）
     */
    private Integer        level;
}
