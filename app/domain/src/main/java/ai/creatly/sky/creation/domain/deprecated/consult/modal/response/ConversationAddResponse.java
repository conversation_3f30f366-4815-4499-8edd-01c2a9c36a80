/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.consult.modal.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @version ConversationAddResponse.java, v 0.1 2023-10-02 15:51 syoka
 */
@Data(staticConstructor = "of")
//@AllArgsConstructor
//@NoArgsConstructor
public class ConversationAddResponse {

    private String conversationId;

    private String scenario;

    private String scenarioNo;
}
