/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.repository;

import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanSource;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import ai.creatly.sky.creation.domain.support.bizconfig.repository.BizConfigRepository;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.stream.Collectors.toList;

/**
 * 付费计划持久化实现
 *
 * <AUTHOR>
 * @version PlanRepositoryImpl.java, v 0.1 2023-10-12 上午10:38 zhoudong
 */
@Repository
@RequiredArgsConstructor
public class PlanRepositoryImpl implements PlanRepository {

    private final BizConfigRepository bizConfigRepository;

    private final static String SCHEMA_CODE = "plan";

    @Override
    public Optional<Plan> queryOptionalById(String id) {
        Asserts.notBlank(id, "付费计划ID不能为空");
        return bizConfigRepository.queryOne(SCHEMA_CODE, id, Plan.class)
                .map(plan -> plan.setSource(PlanSource.PLATFORM));
    }

    @Override
    public List<Plan> queryByType(PlanType type) {
        Asserts.notNull(type, "付费计划类型不能为空");
        Map<String, String> queryParams = Map.of("type", type.toString());
        return bizConfigRepository.queryList(SCHEMA_CODE, queryParams, Plan.class)
                .stream()
                .map(plan -> plan.setSource(PlanSource.PLATFORM))
                .collect(toList());
    }
}
