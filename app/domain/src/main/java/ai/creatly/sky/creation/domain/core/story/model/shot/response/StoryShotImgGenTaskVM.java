/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.shot.response;

import ai.creatly.sky.creation.domain.core.userfile.model.response.BizFileVM;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version StoryShotImgGenTaskVM.java, v 0.1 2024-05-10 上午12:28 zhoudong
 */
@Data
@Accessors(chain = true)
public class StoryShotImgGenTaskVM {

    /**
     * 分镜图列表
     */
    private List<BizFileVM> files;
    /**
     * 任务ID
     */
    private String          taskId;
    /**
     * 业务状态（SUCCESS/FAILURE）
     */
    private String          taskStatus;
}
