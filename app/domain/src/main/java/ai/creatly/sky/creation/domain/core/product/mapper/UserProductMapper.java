/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.product.mapper;

import ai.creatly.sky.creation.domain.core.product.model.SavingVideoProduct;
import ai.creatly.sky.creation.domain.core.product.model.request.VideoProductCreateRequest;
import ai.creatly.sky.creation.domain.core.product.model.request.VideoProductUpdateRequest;
import ai.creatly.sky.creation.domain.core.videoproject.model.request.VideoProductDTO;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version UserProductMapper.java, v 0.1 2024-10-17 下午5:30 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface UserProductMapper {

    SavingVideoProduct toSavingVideoProduct(VideoProductCreateRequest request);

    SavingVideoProduct toSavingVideoProduct(VideoProductUpdateRequest request);

    @Mapping(target = "coverFileId", ignore = true)
    @Mapping(target = "userSelectedAssetIds", ignore = true)
    SavingVideoProduct toSavingVideoProduct(VideoProductDTO videoProductDTO);
}
