/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.approval.service;

import ai.creatly.sky.creation.domain.support.approval.model.ApprovalBizType;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalStatus;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalTask;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version ApprovalService.java, 2024-10-29 上午10:52 zhoudong
 */
public interface ApprovalService {

    /**
     * 分页查询
     *
     * @param status   状态
     * @param pageable 分页参数
     * @return 分页列表
     */
    Page<ApprovalTask> queryPage(ApprovalStatus status, Pageable pageable);

    /**
     * 查询审批任务
     *
     * @param taskId 任务ID
     * @return 审批任务详情
     */
    Optional<ApprovalTask> queryOptionalById(long taskId);

    /**
     * 查询审批任务
     *
     * @param bizType 业务类型
     * @param bizId   业务ID
     * @return 审批任务详情
     */
    ApprovalTask queryByBizId(ApprovalBizType bizType, long bizId);

    /**
     * 提交审批任务
     *
     * @param task 审批任务
     * @return 任务ID
     */
    long submit(ApprovalTask task);

    /**
     * 接受审批任务
     *
     * @param taskId 审批任务ID
     */
    void accept(long taskId);

    /**
     * 审批通过
     *
     * @param taskId 审批任务ID
     */
    void approve(long taskId);

    /**
     * 审批拒绝
     *
     * @param taskId 审批任务ID
     */
    void reject(long taskId);
}
