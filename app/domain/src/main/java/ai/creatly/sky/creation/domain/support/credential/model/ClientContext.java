/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.credential.model;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @version ClientContext.java, v 0.1 2023-08-05 23:37 joton
 */
@Data
@AllArgsConstructor(staticName = "of")
public class ClientContext {

    /**
     * API Key ID（全局唯一）
     */
    private String appKey;
    /**
     * API Key Name
     */
    private String name;
}
