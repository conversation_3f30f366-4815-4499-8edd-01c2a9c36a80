/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 声音内容片段类型
 *
 * <AUTHOR>
 * @version VoiceSegmentType.java, v 0.1 2023-12-07 下午10:09 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum VoiceSegmentType {

    TEXT("纯文本"),
    PROSODY("韵律调节"),
    SHORT_BREAK("停顿"),
    SHORT_SOUND("特效音"),
    BOOKMARK("书签"),
    ;
    private final String desc;

    public boolean isText() {
        return this == TEXT;
    }
}
