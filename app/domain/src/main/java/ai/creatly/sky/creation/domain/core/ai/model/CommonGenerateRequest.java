/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.model;

import ai.creatly.sky.creation.domain.core.ai.image.model.enums.ImageExpandDirection;
import ai.creatly.sky.creation.domain.core.ai.model.enums.AspectRatio;
import ai.creatly.sky.creation.domain.core.ai.model.enums.GenerateFunction;
import ai.creatly.sky.creation.domain.core.userfile.model.FileRef;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version CommonGenerateRequest.java, v0.1 2025-02-20 21:36
 */
@Data
public class CommonGenerateRequest {

    /**
     * 基础模型
     */
    private String           baseModel;

    /**
     * lora增强
     */
    private String           loraModel;

    /**
     * lora参考强度[-10,100]
     */
    private Float            loraFidelity = 1.0f;

    /**
     * 功能
     */
    private GenerateFunction function;

    /**
     * 风格模板ID
     */
    private String           templateUuid;

    private String           promptText;


    private AspectRatio      aspectRatio;

    private ImageExpandDirection direction;

    /**
     * 参考图
     */
    private FileRef          imageFileRefs;

    /**
     * 可选，参考模式，枚举值：subject（角色特征参考）, face（人物长相参考）
     */
    private String           imageReference;

    /**
     * 可选，参考强度[0,1]
     */
    private Float            imageFidelity;

    /**
     * 参考图2
     */
    private FileRef          imageFile2Refs;

    /**
     * 参考图2
     */
    private FileRef          imageFile3Refs;

    /**
     * 生成数量
     */
    private Integer          imgCount = 1;

    private Object           controlnet;

    /**
     * 负项提示词
     */
    private String           negativePrompt;


}
