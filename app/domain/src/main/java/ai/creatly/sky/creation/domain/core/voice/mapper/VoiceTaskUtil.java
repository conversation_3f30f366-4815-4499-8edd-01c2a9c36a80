/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.mapper;

import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 * @version VoiceTaskUtil.java, v 0.1 2023-12-13 下午5:38 zhoudong
 */
@UtilityClass
public class VoiceTaskUtil {

    public String toTaskBizNo(Voice voice) {
        return AppConstants.isSystemUser(voice.getUid()) ? voice.getCode() : voice.getCode() + ":" + voice.getUid();
    }

    public long parseVoiceUid(String taskBizNo) {
        String[] splits = taskBizNo.split(":", 2);
        if (splits.length ==2) {
            return Long.parseLong(splits[1]);
        }
        return AppConstants.SYSTEM_UID;
    }

    public String parseVoiceCode(String taskBizNo) {
        String[] splits = taskBizNo.split(":", 2);
        return splits[0];
    }
}
