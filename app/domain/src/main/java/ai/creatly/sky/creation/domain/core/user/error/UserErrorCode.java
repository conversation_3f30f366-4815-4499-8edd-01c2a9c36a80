/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 用户异常
 *
 * <AUTHOR>
 * @version UserErrorCode.java, v 0.1 2023-05-27 17:50 syoka
 */
@Getter
@RequiredArgsConstructor
public enum UserErrorCode implements ErrorCode {

    USER_NOT_LOGIN("用户未登录"),
    USER_TASK_ERROR("任务异常，请稍后再试"),
    USER_ILLEGAL_TOKEN("用户非法凭证"),

    USERNAME_ALREADY_EXISTS("用户名:{}已存在"),
    USER_EMAIL_ALREADY_EXIST("用户邮箱:{}已存在"),

    USER_VERIFY_CODE_NOT_MATCH("用户验证码错误"),
    USER_VERIFY_CODE_NOT_EXISTS("用户验证码缺失"),
    USER_EMAIL_IS_ILLEGAL("用户邮箱非法"),
    USER_SIGNUP_INVITE_CODE_NOT_VALID("无效的用户注册邀请码:{}，请输入合法的邀请码"),
    USER_SIGNUP_ILLEGAL("非法注册请求"),

    USER_NOT_EXISTS("用户不存在"),
    THE_PHONE_HAS_ALREADY_BIND_USER("此手机号已绑定了其他用户"),
    THE_INPUT_PHONE_IS_EMPTY("输入手机号不可为空"),
    THE_WECHAT_HAS_ALREADY_BIND_USER("此微信号已绑定了其他用户"),
    USER_OVER_MAX_LOGIN_RETRY_TIME("登陆错误次数过多，请稍后再试，用户名:{}"),
    USER_USERNAME_IS_ILLEGAL("用户名非法，用户名仅支持字符和数字且长度介于3～25位"),
    USERNAME_PWD_NOT_CORRECT("用户名或密码错误，当前输入的用户名:{}"),
    LOGIN_PWD_NOT_CORRECT("用户名或密码错误"),

    USER_SIGNUP_INVITE_CODE_MISSING("请填写合法的注册邀请码"),
    USER_SIGNUP_INVITE_CODE_OUT_OF_DATE("用户注册邀请码:{}，已过期"),

    USER_OPENAPI_CREDENTIALS_ALREADY_EXISTS("此用户已开启OpenAPI权限，禁止重复申请"),
    USER_OPENAPI_CREDENTIALS_INVALID("此用户未开通OpenAPI权限"),
    USER_OPENAPI_SIGN_INVALID("签名不正确"),
    USER_OPENAPI_SIGN_EXPIRED("签名已过期"),
    ENTERPRISE_MEMBER_EXPIRED("企业会员已过期，请续费"),

    NO_USER_CREDIT_ACCOUNT_AVAILABLE("未找到用户可用的支付账号"),
    USER_CREDIT_EXPENSE_LOG_NOT_EXISTS("用户消费记录不存在"),
    USER_CREDIT_NOT_ENOUGH("元气不足，无法完成创作任务，请充值后再试"),

    USER_WORKSPACE_NOT_EXISTS("用户工作空间{}不存在，请联系管理员"),
    USER_OPS_FREQUENCY_TOO_HIGH("操作过于频繁，请稍后再试，手机号：{}"),

    USER_INPUT_IS_NOT_CHINESE_PHONE_NUMBER("请输入正确的11位中国电话号码"),
    NO_AVAILABLE_VERIFY_CODE_SEND_CHANNEL("没有可用的验证码发送渠道"),
    THIRD_PARTY_ALIYUN_SEND_SMS_FAILURE("阿里云短信服务出现异常"),
    USER_SIGN_IN_SMS_CODE_INVALID("用户登录短信验证码错误"),
    USER_SIGN_IN_SCAN_CODE_TIMEOUT("用户扫码登录超时"),

    UNSUPPORTED_WORKSPACE_TEMPLATE("不识别的工作空间模版类型"),

    NO_IMPLEMENTATION_OF_BENEFIT_ACTION("缺失此权益实现:{}"),

    WECHAT_LOGIN_LOSE_CSRF_TOKEN("微信登陆失败，缺失安全参数"),
    WECHAT_GET_LOGIN_IN_PAGE_FAIL("微信获取登陆二维码失败"),

    WECHAT_LOGIN_FAIL("微信登录失败：{}"),
    WECHAT_LOGIN_UNAUTHORIZED("微信登录未授权"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
