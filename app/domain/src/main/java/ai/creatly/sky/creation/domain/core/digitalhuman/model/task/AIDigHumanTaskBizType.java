/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.model.task;

import com.jspeeder.core.data.enums.ICode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version AIDigHumanTaskBizType.java, v 0.1 2024-05-25 20:23 syoka
 */
@Getter
@RequiredArgsConstructor
public enum AIDigHumanTaskBizType implements ICode {

    DIG_HUMAN_GEN_AVATAR("数字人形象训练"),
    DIG_HUMAN_GEN_VIDEO("数字人视频生成"),
    ;

    private final String desc;

    @Override
    public String getCode() {
        return this.name();
    }
}
