/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.chat.model.request;

import ai.creatly.sky.creation.domain.core.ai.chat.model.MessageRole;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version ChatMessageRequest.java, v0.1 2025-02-19 20:36
 */
@Data
public class ChatMessageRequest {

    @NotNull
    private MessageRole role;
    @NotBlank
    private String      text;
}
