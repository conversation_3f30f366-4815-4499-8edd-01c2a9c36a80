/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.model.organization;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户所从属的组织,逻辑上可以属于多个组织，但仅有一个有效（在用户切换账号的时候，实际上开始切换组织状态）。
 * 用户组织结构内部会包含组织基础信息及主账号信息
 *
 * <AUTHOR>
 * @version UserOrganization.java, v 0.1 2024-01-19 20:56 syoka
 */
@Data
@Accessors(chain = true)
public class UserOrganization {

    /**
     * 组织id
     */
    private Long                   orgId;
    /**
     * 组织主账号id
     */
    private Long                   orgUid;
    /**
     * 用户主账号名
     */
    private String                 orgUserName;
    /**
     * 组织码-全剧唯一
     */
    private String                 orgCode;
    /**
     * 当前组织激活状态
     */
    private UserOrganizationStatus status;
    /**
     * 组织名字
     */
    private String orgName;

    private String logoUrl;
}
