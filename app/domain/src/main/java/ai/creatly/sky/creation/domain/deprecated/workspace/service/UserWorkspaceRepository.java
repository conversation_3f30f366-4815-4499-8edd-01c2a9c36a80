/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.deprecated.workspace.service;

import ai.creatly.sky.creation.domain.deprecated.workspace.model.UserWorkspace;
import ai.creatly.sky.creation.domain.deprecated.workspace.model.UserWorkspaceDigest;
import ai.creatly.sky.creation.domain.deprecated.workspace.model.WorkspaceSynchronize;
import ai.creatly.sky.creation.domain.deprecated.workspace.model.request.WorkspaceCompositeQuery;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version UserWorkspaceRepository.java, v 0.1 2023-09-23 22:11 syoka
 */
@Deprecated
public interface UserWorkspaceRepository {

    /**
     * 分页查询用户工作空间列表
     */
    Page<UserWorkspaceDigest> getUserWorkspaceDigest(Long uid, WorkspaceCompositeQuery query, Pageable pageable);

    /**
     * 查询用户指定工作空间
     *
     * @param workspaceId 工作空间id
     * @param uid         用户id
     */
    Optional<UserWorkspace> findWorkspaceById(Long workspaceId, Long uid);

    /**
     * 删除用户工作空间
     *
     * @param workspaceId 工作空间id
     * @param uid         用户id
     */
    void deleteUserWorkspace(Long workspaceId, Long uid);

    /**
     * 更新用户工作空间
     *
     * @param workspace -
     */
    void updateUserWorkspace(UserWorkspace workspace);

    /**
     * 创建用户工作空间
     *
     * @param userWorkspace -
     * @return -
     */
    String createUserWorkspace(UserWorkspace userWorkspace);

    /**
     * 创建用户工作空间
     *
     * @param workspaceId key ID
     * @param model       工作空间配置信息
     */
    void synchronizeWorkspace(String workspaceId, WorkspaceSynchronize model);

    String getSynchronizeWorkspaceProgress(String workspaceId);

    String queryOSSURIByOwnerIdAndWorkspaceId(long ownerId, String workspaceId);
}
