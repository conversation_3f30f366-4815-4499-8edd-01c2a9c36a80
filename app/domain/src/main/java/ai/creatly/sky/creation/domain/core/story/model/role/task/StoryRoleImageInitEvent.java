/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.task;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version StoryRoleImageInitEvent.java, v 0.1 2024-03-29 17:47 syoka
 */
@Data
@Accessors(chain = true)
public class StoryRoleImageInitEvent {

    /**
     * 生成任务ID
     */
    private Long taskId;
}
