/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.wechat.oauth2.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version WechatUserToken.java, v 0.1 2024-04-01 15:01 syoka
 */
@Data
@Accessors(chain = true)
public class WechatUserToken {

    private String accessToken;
    private String appId;
    private String openid;
    private String unionid;
}
