/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.task.repaint.model;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizInput;
import ai.creatly.sky.creation.domain.core.userfile.model.FileRef;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @version ImageRepaintTaskInput.java, v0.1 2025-02-24 15:53
 */
@Data
@Accessors(chain = true)
public class ImageRepaintTaskInput implements TaskBizInput {

    private FileRef imageFile;
    private FileRef maskImageFile;
    private String  promptText;
}
