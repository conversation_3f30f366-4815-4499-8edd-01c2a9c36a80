/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version VideoAspectRatio.java, 2024-10-31 下午12:41 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum VideoAspectRatio {

    _9_16("9:16", 1080, 1920),
    _16_9("16:9", 1920, 1080),
    _1_1("1:1", 1080, 1080),
    ;

    private final String desc;
    private final int    defaultWidth;
    private final int    defaultHeight;
}
