/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.wechat.mp;

import ai.creatly.sky.creation.domain.core.auth.model.WechatAppName;
import ai.creatly.sky.creation.domain.support.wechat.model.WechatUser;
import ai.creatly.sky.creation.domain.support.wechat.mp.model.account.WechatMpQrCode;
import ai.creatly.sky.creation.domain.support.wechat.mp.model.message.send.MpMessageOption;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;
import java.util.Map;

/**
 * 微信公众号客户端
 *
 * <AUTHOR>
 * @version WechatMpClient.java, v 0.1 2024-03-31 11:27 syoka
 */
public interface WechatMpClient {

    String getAppId(WechatAppName appName);

    String getToken(WechatAppName appName);

    byte[] getAesKey(WechatAppName appName);

    /**
     * 生成微信公众号二维码
     *
     * @param scene      自定义场景值
     * @param appName    微信应用名称
     * @param expireTime 二维码过期时间（不传则为永久二维码），最大不超过2592000（即30天）
     * @return -
     */
    WechatMpQrCode generateQrCode(String scene, WechatAppName appName, @Nullable Duration expireTime);

    WechatUser getUserInfo(String openId, WechatAppName appName);

    void sendMessage(String openId, String templateId, Map<String, String> params, MpMessageOption option, WechatAppName appName);
}
