package ai.creatly.sky.creation.domain.core.ai.video;

import ai.creatly.sky.creation.domain.core.ai.video.model.AiVideoTaskResult;
import ai.creatly.sky.creation.domain.core.ai.video.model.DigPersonVideoTaskInput;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;

/**
 * AI视频-数字人
 *
 * <AUTHOR>
 */
public interface AiDigPersonClient {

    AiVideoTaskResult generate(DigPersonVideoTaskInput input, UserContext userContext);

    CreditsExpense expense(AiTask aiTask);

    AiVideoTaskResult queryGeneration(String taskId, UserContext userContext);
}
