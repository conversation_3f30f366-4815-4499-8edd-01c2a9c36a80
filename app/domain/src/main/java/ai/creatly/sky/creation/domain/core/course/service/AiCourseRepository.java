/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.course.service;

import ai.creatly.sky.creation.domain.core.course.model.AiCourse;

import java.util.List;

/**
 * <AUTHOR>
 * @version AiToolRepository.java, v0.1 2025-02-19 21:17
 */
public interface AiCourseRepository {

    List<AiCourse> queryIndexHotCourse();

    List<AiCourse> queryImageHotCourse();

    List<AiCourse> queryVideoHotCourse();

    List<AiCourse> queryAudioHotCourse();

    List<AiCourse> queryCourseHotList();

    /**
     * 获取付费课程列表
     * @return
     */
    List<AiCourse> queryCourseList();

    /**
     * 获取所有课程
     * @return
     */
    List<AiCourse> queryAllCourseList();

    /**
     * 新增课程
     * @param aiCourse
     * @return
     */
    AiCourse addCourse(AiCourse aiCourse);

    /**
     * 编辑课程
     * @param aiCourse
     * @return
     */
    AiCourse editCourse(AiCourse aiCourse);

    /**
     * 获取课程
     * @param courseId
     * @return
     */
    AiCourse getById(Long courseId);
}
