/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userasset.model.enums;

import com.jspeeder.core.data.enums.ICode;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 素材类型
 *
 * <AUTHOR>
 * @version AssetType.java, 2024-10-21 下午5:22 zhoudong
 */
@Getter
@AllArgsConstructor
public enum AssetType implements ICode {

    image("静态图"),
    gif_image("动图"),
    short_video("短视频");

    private final String desc;

    @Override
    public String getCode() {
        return this.name();
    }
}
