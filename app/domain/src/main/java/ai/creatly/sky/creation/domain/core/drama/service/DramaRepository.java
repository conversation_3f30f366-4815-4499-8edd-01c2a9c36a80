/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.service;

import ai.creatly.sky.creation.domain.core.drama.error.DramaErrorCode;
import ai.creatly.sky.creation.domain.core.drama.model.Drama;
import ai.creatly.sky.creation.domain.core.drama.model.DramaReview;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaAction;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaStatus;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.exception.SysException;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @version DramaRepository.java, v 0.1 2024-10-18 下午8:30 zhoudong
 */
public interface DramaRepository {

    long create(Drama drama);

    void updateById(Drama drama);

    void action(long id, DramaAction action, @Nullable DramaReview review);

    default Drama queryById(long id) {
        return this.queryOptionalById(id).orElseThrow(() -> new SysException(DramaErrorCode.DRAMA_NOT_FOUND));
    }

    Optional<Drama> queryOptionalById(long id);

    Page<Drama> queryPageByStatus(DramaStatus status, Pageable pageable);

    Page<Drama> queryPageByUidAndStatus(long uid, List<DramaStatus> statuses, Pageable pageable);

    Optional<Drama> querySyncedByContentMd5(String contentMd5);

    default List<Drama> queryByIds(List<Long> dramaIds) {
        return this.queryByIds(Set.copyOf(dramaIds));
    }

    List<Drama> queryByIds(Set<Long> dramaIds);

    /**
     * 汇总剧情已支持的产品类目
     *
     * @return 类目树
     */
    List<String> summaryProductCategoryPaths();
}
