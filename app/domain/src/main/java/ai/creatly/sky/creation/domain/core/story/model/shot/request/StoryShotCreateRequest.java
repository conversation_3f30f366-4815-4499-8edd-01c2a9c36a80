/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.shot.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 新增故事分镜请求
 *
 * <AUTHOR>
 * @version StoryShotCreateRequest.java, v 0.1 2024-03-01 21:09 heb
 */
@Data
@Accessors(chain = true)
public class StoryShotCreateRequest {

    /**
     * 场景ID
     */
    @NotBlank
    private String sceneId;
    /**
     * 分镜名
     */
    @NotBlank(message = "分镜名不能为空")
    @Size(max = 20)
    private String shotName;
    /**
     * 分镜描述
     */
    private String description;
}
