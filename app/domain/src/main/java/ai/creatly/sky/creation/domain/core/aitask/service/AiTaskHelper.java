/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.service;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version AiTaskHelper.java, v 0.1 2023-12-09 下午4:14 zhoudong
 */
public interface AiTaskHelper {

    /**
     * 异步触发执行
     *
     * @param taskId 任务ID
     */
    void asyncExecute(long taskId);

    /**
     * 获取任务归属人的用户上下文
     *
     * @param aiTask 任务
     * @return 用户上下文
     */
    UserContext getOwnerUserContext(AiTask aiTask);

    void sendWechatMessageSilently(AiTask aiTask, String templateId, Map<String, String> params);
}
