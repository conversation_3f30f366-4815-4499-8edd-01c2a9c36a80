/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.wechat.mp.model.account;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Duration;

/**
 * 微信公众号二维码
 *
 * <AUTHOR>
 * @version WechatMpQrCode.java, v 0.1 2024-10-14 下午6:40 zhoudong
 */
@Data
@Accessors(chain = true)
public class WechatMpQrCode {
    private String   ticket;
    private Duration expireTime;
    private String   url;
}
