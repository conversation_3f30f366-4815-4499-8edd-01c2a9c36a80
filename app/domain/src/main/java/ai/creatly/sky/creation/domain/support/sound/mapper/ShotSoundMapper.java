/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.sound.mapper;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.story.model.shot.response.ShotSoundVM;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.support.sound.model.Sound;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version ShotSoundMapper.java, v 0.1 2024-04-17 下午12:09 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface ShotSoundMapper {

    default ShotSoundVM toShotSoundVM(Sound shotSound, UserFileHelper userFileHelper) {
        return new ShotSoundVM()
                .setSoundId(shotSound.getId().toString())
                .setSoundName(shotSound.getName())
                .setSoundUrl(userFileHelper.getHttpUrl(shotSound.getAudioUrl(), FileAcl.PRIVATE));
    }
}
