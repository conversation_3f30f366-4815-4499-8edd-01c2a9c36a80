/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.task.expand.model;

import ai.creatly.sky.creation.domain.core.ai.image.model.enums.ImageExpandDirection;
import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizInput;
import ai.creatly.sky.creation.domain.core.userfile.model.FileRef;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

/**
 *
 * <AUTHOR>
 * @version ImageExpandTaskInput.java, v0.1 2025-02-24 15:52
 */
@Data
public class ImageExpandTaskInput implements TaskBizInput {
    private FileRef              imageFile;
    private ImageExpandDirection direction;
    @Nullable
    private String               promptText;
}
