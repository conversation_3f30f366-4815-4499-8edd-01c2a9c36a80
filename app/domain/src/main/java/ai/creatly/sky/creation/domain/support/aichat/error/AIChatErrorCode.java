/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.aichat.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version AIGCErrorCode.java, v 0.1 2023-01-05 20:11 joton
 */
@Getter
@RequiredArgsConstructor
public enum AIChatErrorCode implements ErrorCode {

    UNSUPPORTED_SERVICE_PROVIDER("不支持的服务提供方:{}"),
    OPEN_AI_API_CALL_ERROR("OpenAI接口调用失败"),
    OPEN_AI_CONFIG_ABSENCE("OpenAI配置缺失"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
