/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.credit.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 用户余额账户流水
 *
 * <AUTHOR>
 * @version CreditLog.java, v 0.1 2023-12-27 下午11:35 zhoudong
 */
@Data
@Accessors(chain = true)
public class CreditLog {

    public static final String INCOME  = "INCOME";
    public static final String EXPENSE = "EXPENSE";

    /**
     * 主键ID
     */
    private Long              id;
    /**
     * 创建时间
     */
    private ZonedDateTime     createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime     updatedAt;
    /**
     * 用户ID
     */
    private Long              uid;
    /**
     * 业务标识（根据不同的业务类型，含义不一样）
     */
    private String            bizNo;
    /**
     * 关联业务类型
     */
    private CreditLogBizType  bizType;
    /**
     * INCOME/EXPENSE
     */
    private String            type;
    /**
     * 余额变动总量（正数为收入，负数为支出）
     */
    private Integer           amount;
    /**
     * 用户余额变动列表
     */
    private List<CreditDelta> creditDeltas;

    /*------------------- 关联属性 -------------------*/

    /**
     * 用户余额账户收支流水明细列表
     */
    private List<CreditHistory> histories;
}
