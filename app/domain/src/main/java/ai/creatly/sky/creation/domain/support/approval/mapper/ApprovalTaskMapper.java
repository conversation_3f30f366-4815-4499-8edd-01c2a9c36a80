/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.approval.mapper;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.TaskSharedUser;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalTask;
import ai.creatly.sky.creation.domain.support.approval.model.Reviewer;
import ai.creatly.sky.creation.domain.support.approval.task.ApprovalTaskResult;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version ApprovalMapper.java, 2024-10-29 下午2:55 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface ApprovalTaskMapper {

    default ApprovalTask toApprovalTask(AiTask task) {
        return this.toApprovalTask(task, task.parseBizResult(ApprovalTaskResult.class));
    }

    @Mapping(target = "reviewers", source = "task.sharedUsers")
    @Mapping(target = "applicantName", source = "task.ownerName")
    @Mapping(target = "applicantId", source = "task.ownerId")
    @Mapping(target = "status", source = "task.bizStatus")
    ApprovalTask toApprovalTask(AiTask task, ApprovalTaskResult result);

    Reviewer toReviewer(TaskSharedUser sharedUser);
}
