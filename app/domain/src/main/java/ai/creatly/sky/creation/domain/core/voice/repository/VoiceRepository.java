/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.repository;

import ai.creatly.sky.creation.domain.core.voice.error.VoiceErrorCode;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceLocale;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceQO;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceCategory;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.data.repository.SingleRepository;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 声音存储管理
 *
 * <AUTHOR>
 * @version VoiceRepository.java, v 0.1 2023-11-25 下午20:38 heb
 */
public interface VoiceRepository extends SingleRepository<Voice, VoiceQO> {

    /*--------------------------------- 写操作（删缓存）---------------------------------*/

    /**
     * 创建声音
     *
     * @param voice 声音
     * @return 主键ID
     */
    @Override
    long create(Voice voice);

    /**
     * 修改
     *
     * @param voice 声音
     */
    @Override
    void updateById(Voice voice);

    /**
     * 修改声音下指定的口音
     *
     * @param voiceLocale 声音下指定的口音
     */
    void updateVoiceLocaleById(VoiceLocale voiceLocale);

    /**
     * 上架
     *
     * @param id 声音ID
     * @return 是否成功
     */
    boolean online(long id);

    /**
     * 下架
     *
     * @param id 声音ID
     * @return 是否成功
     */
    boolean offline(long id);

    /**
     * 重新启用（已下架 -> 草稿）
     *
     * @param id 声音ID
     * @return 是否成功
     */
    boolean restart(long id);

    /**
     * 更新口音的预览音频
     *
     * @param voiceId         声音ID
     * @param localeCode      口音代号
     * @param previewAudioId  预览音频文件ID
     * @param previewAudioUrl 预览音频文件地址（OSS地址）
     */
    void updateLocalePreviewAudio(long voiceId, String localeCode, Long previewAudioId, String previewAudioUrl);

    /*--------------------------------- 读操作（无缓存）---------------------------------*/

    @Override
    default @NotNull Voice queryById(long id) {
        return this.queryOptionalById(id).orElseThrow(() -> new SysException(VoiceErrorCode.VOICE_NOT_FOUND));
    }

    Optional<Voice> queryOptionalById(long id);

    Map<Long, Voice> queryMapByIds(Set<Long> ids);

    default Voice queryByUidAndCode(long uid, String code) {
        return this.queryOptionalByUidAndCode(uid, code).orElseThrow(() -> new SysException(VoiceErrorCode.VOICE_NOT_FOUND));
    }

    Optional<Voice> queryOptionalByUidAndCode(long uid, String code);

    List<Voice> queryOnlineVoices(VoiceCategory category, long uid);

    List<Voice> queryVoices(VoiceCategory category);

    @Override
    List<Voice> query(VoiceQO qo, Pageable pageable);

    @Override
    int count(VoiceQO qo);
}
