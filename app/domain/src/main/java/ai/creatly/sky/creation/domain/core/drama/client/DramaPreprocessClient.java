/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.client;

import ai.creatly.sky.creation.domain.core.drama.client.model.DramaPreprocessResult;
import ai.creatly.sky.creation.domain.core.drama.model.Drama;

/**
 * 剧情预处理客户端
 *
 * <AUTHOR>
 * @version DramaPreprocessClient.java, 2024-10-21 下午5:40 zhoudong
 */
public interface DramaPreprocessClient {

    /**
     * 预处理剧情（剧情文件已经提前下载到本地）
     *
     * @param drama 剧情
     * @return 预处理结果
     * @see ai.creatly.sky.creation.domain.core.drama.task.DramaFileSyncProperties
     */
    DramaPreprocessResult preprocess(Drama drama);
}
