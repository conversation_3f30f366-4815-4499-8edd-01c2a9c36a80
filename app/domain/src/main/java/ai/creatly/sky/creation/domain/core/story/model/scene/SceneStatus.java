/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.scene;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum SceneStatus {

    /**
     * 活动的
     */
    ACTIVE("活动的"),
    /**
     * 已存档
     */
    ARCHIVED("已存档"),
    ;

    private final String desc;
}
