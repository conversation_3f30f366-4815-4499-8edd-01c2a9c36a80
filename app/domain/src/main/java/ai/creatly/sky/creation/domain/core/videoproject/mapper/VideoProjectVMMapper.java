/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.mapper;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.response.VideoSubtitleLayerVM;
import ai.creatly.sky.creation.domain.core.userasset.model.AssetFile;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.videoproject.model.*;
import ai.creatly.sky.creation.domain.core.videoproject.model.reponse.*;
import ai.creatly.sky.creation.domain.core.voice.model.PresetVoice;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import ai.creatly.sky.creation.domain.support.multimedia.model.response.TimedTextVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import lombok.Setter;
import org.jetbrains.annotations.Nullable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @version VideoProjectVMMapper.java, v 0.1 2024-09-25 下午2:16 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public abstract class VideoProjectVMMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    @Nullable
    private String getProjectCoverHttpUrl(VideoProject videoProject) {
        final String coverUrl;
        if (videoProject.getCoverUrl() != null) {
            coverUrl = userFileHelper.getHttpUrl(videoProject.getCoverUrl(), FileAcl.PRIVATE);
        } else if (!videoProject.getVideos().isEmpty()) {
            // 取首个成品视频的封面作为项目封面
            String videoUrl = videoProject.getVideos().getFirst().getUrl();
            coverUrl = userFileHelper.getMediaCoverHttpUrl(FileType.VIDEO, videoUrl, FileAcl.PRIVATE);
        } else if (videoProject.getBackgroundLayer() != null) {
            // 取背景图作为封面
            coverUrl = userFileHelper.getMediaCoverHttpUrl(FileType.IMAGE, videoProject.getBackgroundLayer().getUrl(), FileAcl.PRIVATE);
        } else if (!videoProject.getAssets().isEmpty()) {
            // 取首个素材的封面作为项目封面
            VideoAsset firstAsset = videoProject.getAssets().getFirst();
            coverUrl = userFileHelper.getMediaCoverHttpUrl(firstAsset.getFileType(), firstAsset.getUrl(), FileAcl.PRIVATE);
        } else {
            coverUrl = null;
        }
        return coverUrl;
    }

    private List<VideoComposeTaskVM> toVideoComposeTaskVMs(VideoProject videoProject) {
        return videoProject.getComposeTasks()
                .stream()
                .map(composeTask -> {
                    RenderedVideo video = videoProject.findRenderedVideoByComposeTaskId(composeTask.getTaskId()).orElse(null);
                    return this.toVideoComposeTaskVM(composeTask, video);
                }).toList();
    }

    public VideoProjectEntryVM toVideoProjectEntryVM(VideoProject videoProject, @Nullable Long renderEstimatedMills) {
        final String coverUrl = this.getProjectCoverHttpUrl(videoProject);
        final List<VideoComposeTaskVM> composeTasks = this.toVideoComposeTaskVMs(videoProject);
        return this.toVideoProjectEntryVM(videoProject, composeTasks, coverUrl, renderEstimatedMills);
    }

    @Mapping(target = "coverUrl", source = "coverUrl")
    @Mapping(target = "composeTasks", source = "composeTasks")
    protected abstract VideoProjectEntryVM toVideoProjectEntryVM(VideoProject videoProject, List<VideoComposeTaskVM> composeTasks,
                                                                 String coverUrl, @Nullable Long renderEstimatedMills);

    public VideoProjectVM toVideoProjectVM(VideoProject videoProject) {
        final String coverUrl = this.getProjectCoverHttpUrl(videoProject);
        final List<VideoComposeTaskVM> composeTasks = this.toVideoComposeTaskVMs(videoProject);
        return this.toVideoProjectVM(videoProject, composeTasks, coverUrl);
    }

    @Mapping(target = "coverUrl", source = "coverUrl")
    @Mapping(target = "composeTasks", source = "composeTasks")
    protected abstract VideoProjectVM toVideoProjectVM(VideoProject videoProject, List<VideoComposeTaskVM> composeTasks, String coverUrl);

    protected VideoComposeTaskVM toVideoComposeTaskVM(VideoComposeTask videoComposeTask, @Nullable RenderedVideo video) {
        final String coverUrl = userFileHelper.getHttpUrl(videoComposeTask.getCoverUrl(), FileAcl.PRIVATE);
        return this.toVideoComposeTaskVM(videoComposeTask, video, coverUrl);
    }

    @Mapping(target = "coverUrl", source = "coverUrl")
    @Mapping(target = "width", source = "videoComposeTask.width")
    @Mapping(target = "height", source = "videoComposeTask.height")
    protected abstract VideoComposeTaskVM toVideoComposeTaskVM(VideoComposeTask videoComposeTask, @Nullable RenderedVideo video,
                                                               String coverUrl);

    protected AudioTrackVM toAudioTrackVM(AudioTrack audioTrack) {
        final String url = userFileHelper.getHttpUrl(audioTrack.getUrl(), FileAcl.PRIVATE);
        return this.toAudioTrackVM(audioTrack, url);
    }

    @Mapping(target = "url", source = "url")
    protected abstract AudioTrackVM toAudioTrackVM(AudioTrack audioTrack, String url);

    protected VideoTrackVM toVideoTrackVM(VideoTrack videoTrack) {
        final String url = userFileHelper.getHttpUrl(videoTrack.getUrl(), FileAcl.PRIVATE);
        return this.toVideoTrackVM(videoTrack, url);
    }

    @Mapping(target = "url", source = "url")
    protected abstract VideoTrackVM toVideoTrackVM(VideoTrack videoTrack, String url);

    protected abstract VideoSubtitleLayerVM toVideoSubtitleLayerVM(VideoSubtitleLayer subtitleLayer);

    protected abstract TimedTextVM toTimedTextVM(VideoSubtitle subtitle);

    protected abstract VideoScriptVM toVideoScriptVM(VideoScript script);

    protected abstract VideoProductVM toVideoProductVM(VideoProduct product);

    protected VideoVM toVideoVM(RenderedVideo video) {
        String url = userFileHelper.getHttpUrl(video.getUrl(), FileAcl.PRIVATE);
        String coverUrl = userFileHelper.getMediaCoverHttpUrl(FileType.VIDEO, video.getUrl(), FileAcl.PRIVATE);
        return this.toVideoVM(video, url, coverUrl);
    }

    @Mapping(target = "url", source = "url")
    @Mapping(target = "coverUrl", source = "coverUrl")
    protected abstract VideoVM toVideoVM(RenderedVideo video, String url, String coverUrl);

    protected abstract List<VideoAssetVM> totoVideoAssetVMs(List<VideoAsset> videoAssets);

    public VideoAssetVM toVideoAssetVM(VideoAsset videoAsset) {
        String url = userFileHelper.getHttpUrl(videoAsset.getUrl(), FileAcl.PRIVATE);
        String coverUrl = userFileHelper.getMediaCoverHttpUrl(videoAsset.getFileType(), videoAsset.getUrl(), FileAcl.PRIVATE);
        return this.toVideoAssetVM(videoAsset, url, coverUrl);
    }

    @Mapping(target = "url", source = "url")
    @Mapping(target = "coverUrl", source = "coverUrl")
    protected abstract VideoAssetVM toVideoAssetVM(VideoAsset videoAsset, String url, String coverUrl);

    protected VideoAvatarLayerVM toVideoAvatarLayerVM(VideoAvatarLayer avatarLayer) {
        String avatarImageUrl = userFileHelper.getHttpUrl(avatarLayer.getAvatarImageUrl(), FileAcl.PRIVATE);
        return this.toVideoAvatarLayerVM(avatarLayer, avatarImageUrl);
    }

    @Mapping(target = "avatarImageUrl", source = "avatarImageUrl")
    protected abstract VideoAvatarLayerVM toVideoAvatarLayerVM(VideoAvatarLayer avatarLayer, String avatarImageUrl);

    protected VideoBackgroundLayerVM toVideoBackgroundLayerVM(VideoBackgroundLayer backgroundLayer) {
        String url = userFileHelper.getHttpUrl(backgroundLayer.getUrl(), FileAcl.PRIVATE);
        return this.toVideoBackgroundLayerVM(backgroundLayer, url);
    }

    @Mapping(target = "url", source = "url")
    protected abstract VideoBackgroundLayerVM toVideoBackgroundLayerVM(VideoBackgroundLayer backgroundLayer, String url);

    protected abstract VideoVoiceoverVM toVideoVoiceoverVM(PresetVoice voiceover);

    protected VideoShotVM toVideoShotVM(VideoShot videoShot) {
        String assetUrl = null;
        String coverUrl = null;
        if (videoShot.getAssetFile() != null) {
            AssetFile assetFile = videoShot.getAssetFile();
            assetUrl = userFileHelper.getHttpUrl(assetFile.getUrl(), FileAcl.PRIVATE);
            coverUrl = userFileHelper.getMediaCoverHttpUrl(assetFile.getType(), assetFile.getUrl(), FileAcl.PRIVATE);
        }
        String videoUrl = userFileHelper.getHttpUrl(videoShot.getVideoUrl(), FileAcl.PRIVATE);
        return this.toVideoShotVM(videoShot, assetUrl, coverUrl, videoUrl);
    }

    @Mapping(target = "assetFileType", source = "videoShot.assetFile.type")
    @Mapping(target = "assetUrl", source = "assetUrl")
    @Mapping(target = "coverUrl", source = "coverUrl")
    @Mapping(target = "videoUrl", source = "videoUrl")
    protected abstract VideoShotVM toVideoShotVM(VideoShot videoShot,
                                                 @Nullable String assetUrl,
                                                 @Nullable String coverUrl,
                                                 @Nullable String videoUrl);
}
