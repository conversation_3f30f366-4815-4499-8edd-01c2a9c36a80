/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.image.client.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 矩形区域
 *
 * <AUTHOR>
 * @version Rectangle.java, v 0.1 2023-11-18 下午10:05 zhoudong
 */
@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
public class Rectangle {

    /**
     * 涂抹标记矩形区域的x坐标（左上角）
     */
    private Integer x;
    /**
     * 涂抹标记矩形区域的y坐标（左上角）
     */
    private Integer y;
    /**
     * 涂抹标记矩形区域的宽度
     */
    private Integer width;
    /**
     * 涂抹标记矩形区域的高度
     */
    private Integer height;
}
