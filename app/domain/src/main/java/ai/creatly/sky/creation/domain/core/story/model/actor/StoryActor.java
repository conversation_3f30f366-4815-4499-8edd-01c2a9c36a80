/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.actor;

import ai.creatly.sky.creation.domain.core.story.model.role.StoryRoleGender;
import ai.creatly.sky.creation.domain.core.story.model.role.StoryRoleGeneration;
import com.jspeeder.core.model.OperatorRef;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

@Data
@Accessors(chain = true)
public class StoryActor {

    /**
     * 主键ID
     */
    private Long                id;
    /**
     * 创建时间
     */
    private ZonedDateTime       createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime       updatedAt;
    /**
     * 演员名称
     */
    private String              name;
    /**
     * 演员性别
     */
    private StoryRoleGender     gender;
    /**
     * 演员年龄
     */
    private StoryRoleGeneration generation;
    /**
     * 演员肖像文件ID
     */
    private Long                portraitId;
    /**
     * 演员肖像地址
     */
    private String              portraitUrl;
    /**
     * 演员声音
     */
    private String              voiceId;
    /**
     * 创建人
     */
    private OperatorRef         creator;
}
