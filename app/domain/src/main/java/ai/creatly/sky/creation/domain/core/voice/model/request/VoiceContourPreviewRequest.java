/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.request;

import ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment.prosody.IntonationContourPoint;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 声音语调预览请求
 *
 * <AUTHOR>
 * @version VoiceContourPreviewRequest.java, v 0.1 2023-12-11 下午11:36 zhoudong
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VoiceContourPreviewRequest extends VoiceSynthesizeRequest {

    /**
     * 局部语调轮廓（由多个轮廓点组成，不传则使用原始语调合成）
     */
    @Valid
    @Size(max = 5)
    private List<IntonationContourPoint> contour;
}
