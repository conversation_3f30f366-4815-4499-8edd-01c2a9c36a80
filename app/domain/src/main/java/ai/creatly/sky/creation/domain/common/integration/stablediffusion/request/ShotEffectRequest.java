/**
 * Copyright For creatly
 *
 * <AUTHOR>
 * @version SubmitImageineRequest.java, v 0.1 2023-08-19 23:23
 */
package ai.creatly.sky.creation.domain.common.integration.stablediffusion.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ShotEffectRequest {

    private String bucket;
    @JsonProperty("object_key")
    private String objectKey;
    @JsonProperty("object_result_key")
    private String objectResultKey;
}
