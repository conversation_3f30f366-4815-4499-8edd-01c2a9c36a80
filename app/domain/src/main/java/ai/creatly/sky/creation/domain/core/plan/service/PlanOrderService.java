/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.service;

import ai.creatly.sky.creation.domain.core.plan.model.PlanOrder;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import ai.creatly.sky.creation.domain.core.plan.service.pay.PlanPayService;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * 定价计划订单服务
 *
 * <AUTHOR>
 * @version PlanOrderService.java, v 0.1 2023-10-12 上午11:41 zhoudong
 */
public interface PlanOrderService extends PlanPayService {

    /**
     * 分页查询已支付的定价计划订单
     *
     * @param uid       用户ID
     * @param planTypes 定价计划类型
     * @param pageable  分页参数
     * @return 定价计划订单列表
     */
    Page<PlanOrder> queryPaidPage(long uid, List<PlanType> planTypes, Pageable pageable);

    /**
     * 查询定价计划订单详情
     *
     * @param orderId 订单ID
     * @return 定价计划订单详情
     */
    @Nullable
    PlanOrder queryPlanOrder(long orderId);
}
