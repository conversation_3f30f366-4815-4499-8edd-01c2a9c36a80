/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.prompt.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 提示词业务使用场景
 *
 * <AUTHOR>
 * @version PromptBizSource.java, v 0.1 2023-10-13 13:58 syoka
 */
@Getter
@AllArgsConstructor
public enum PromptBizSource {

    MAGICAL_BRUSH("马良"),
    INTELLIJ_WRITER("丹青妙笔"),
    SYSTEM("系统应用"),
    ;

    private final String desc;

    public static PromptBizSource of(String code) {
        for (PromptBizSource categoryEnum : values()) {
            if (StringUtils.equals(categoryEnum.name(), code)) {
                return categoryEnum;
            }
        }
        return null;
    }
}
