/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.service.builder;

import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.config.LocalFileProperties;
import ai.creatly.sky.creation.domain.core.userfile.error.FileErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.mapper.UserFileMapper;
import ai.creatly.sky.creation.domain.core.userfile.model.FileInput;
import ai.creatly.sky.creation.domain.core.userfile.model.FileMetadata;
import ai.creatly.sky.creation.domain.core.userfile.model.InputStreamFile;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import ai.creatly.sky.creation.domain.support.multimedia.model.ImageMetadata;
import ai.creatly.sky.creation.domain.support.multimedia.model.VideoMetadata;
import ai.creatly.sky.creation.domain.support.multimedia.resolver.MediaFileResolver;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version LocalFileService.java, v 0.1 2024-09-24 下午4:39 zhoudong
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class LocalFileBuilder {

    private final LocalFileProperties localFileProperties;
    private final UserFileHelper      userFileHelper;
    private final MediaFileResolver   mediaFileResolver;
    private final UserFileMapper      userFileMapper;

    /**
     * 构建用户文件
     *
     * @param filename    文件名（包含本地相对路径，不以 / 开头）
     * @param bizSource   业务来源
     * @param subPath     动态子路径（带文件名和后缀，相对于业务路径）
     * @param userContext 用户上下文
     * @return 用户文件
     */
    public InputStreamFile buildStreamFile(String filename, FileBizSource bizSource, @Nullable String subPath, UserContext userContext) {
        Asserts.isTrue(localFileProperties.getEnabled(), "本地文件存储功能未启用");
        File file = new File(localFileProperties.getBaseDir(), filename);
        FileType fileType = mediaFileResolver.detectFileType(file);

        final FileMetadata fileMetadata;
        final String extension;
        switch (fileType) {
            case IMAGE -> {
                ImageMetadata metadata = mediaFileResolver.resolveImageMetadata(file, bizSource);
                fileMetadata = new FileMetadata()
                        .setBytes(metadata.getSize().getSizeInBytes())
                        .setWidth(metadata.getWidth())
                        .setHeight(metadata.getHeight())
                        .setBizData(metadata.getMetadata());
                extension = metadata.getExtension();
            }
            case VIDEO -> {
                VideoMetadata metadata = mediaFileResolver.resolveVideoMetadata(file, bizSource);
                fileMetadata = new FileMetadata()
                        .setBytes(metadata.getSize().getSizeInBytes())
                        .setWidth(metadata.getWidth())
                        .setHeight(metadata.getHeight())
                        .setBizData(metadata.getMetadata());
                extension = metadata.getFormat();
            }
            default -> throw new UnsupportedOperationException("不支持的文件类型");
        }

        FileInput fileInput = new FileInput()
                .setType(fileType)
                // 使用业务来源中声明的ACL
                .setAcl(null)
                .setBizSource(bizSource)
                .setOriginalFilename(FilenameUtils.getBaseName(filename))
                .setSubPath(subPath)
                .setExtension(extension)
                .setFileMetadata(fileMetadata);
        UserFile userFile = userFileHelper.buildUserFile(userContext, IdHelper.getId(), fileInput);

        return userFileMapper.toInputStreamFile(userFile, () -> {
            try {
                return new BufferedInputStream(new FileInputStream(file));
            } catch (IOException e) {
                log.warn("上传{}文件失败", fileType.getDesc(), e);
                throw new BizException(FileErrorCode.FILE_UPLOAD_ERROR, fileType.getDesc(), e);
            }
        });
    }
}
