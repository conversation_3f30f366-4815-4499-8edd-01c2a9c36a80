/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.service;

import ai.creatly.sky.creation.domain.common.integration.azure.model.AudioQuality;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.FileOutput;
import ai.creatly.sky.creation.domain.core.userfile.model.FileUploadOption;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.voice.model.AudioData;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceTimeEstimation;
import ai.creatly.sky.creation.domain.core.voice.model.admin.request.VoiceCreateRequest;
import ai.creatly.sky.creation.domain.core.voice.model.admin.response.SelfVoiceCheckResult;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceSynthesizeRequest;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.VoiceCreationContent;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.VoiceDialogue;
import ai.creatly.sky.creation.domain.support.speech.model.request.SpeechSynthesisRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <AUTHOR>
 * @version VoiceService.java, v 0.1 2023-11-06 下午2:52 zhoudong
 */
@Validated
public interface VoiceService {

    /**
     * 校验自营声音模型
     *
     * @param voiceCode 自营声音编号
     * @return 校验结果
     */
    SelfVoiceCheckResult checkSelfVoiceModel(String voiceCode);

    /**
     * 创建草稿声音
     *
     * @param request 创建参数
     * @return 已创建的声音ID
     */
    long createDraftVoice(@Valid @NotNull VoiceCreateRequest request);

    /**
     * 预估语音合成相关时长（误差5s）
     *
     * @param request  语音合成请求
     * @param maxWords 最大字数
     * @return 语音合成时长预估
     */
    VoiceTimeEstimation estimateTime(VoiceSynthesizeRequest request, int maxWords);

    /**
     * 简单文本合成语音
     *
     * @param voiceDialogue 一段对话
     * @param userContext   用户上下文
     * @return 合成的音频（未持久化）(注意⚠️：如果输出的音频文件大小为0，则不会上传到OSS，这种情况视为合成失败，返回空)
     */
    @Nullable
    default UserFile synthesize(@Valid @NotNull VoiceDialogue voiceDialogue, @NotNull FileBizSource bizSource,
                                @NotNull UserContext userContext) {
        return this.synthesize(voiceDialogue, FileOutput.of(bizSource), userContext);
    }

    /**
     * 简单文本合成语音
     *
     * @param voiceDialogue 一段对话
     * @param userContext   用户上下文
     * @return 合成的音频（未持久化）(注意⚠️：如果输出的音频文件大小为0，则不会上传到OSS，这种情况视为合成失败，返回空)
     */
    @Nullable
    default UserFile synthesize(@Valid @NotNull VoiceDialogue voiceDialogue, @Valid @NotNull FileOutput fileOutput,
                                @NotNull UserContext userContext) {
        VoiceCreationContent content = new VoiceCreationContent().setDialogues(List.of(voiceDialogue));
        return this.synthesize(content, fileOutput, userContext);
    }

    /**
     * 用户合成有声内容，并上传音频到OSS TODO 把合成内容的校验部分内聚到一起（build content -> check content -> synthesize）
     *
     * @param content     有声内容
     * @param bizSource   业务来源
     * @param userContext 用户上下文
     * @return 合成的音频（未持久化）(注意⚠️：如果输出的音频文件大小为0，则不会上传到OSS，这种情况视为合成失败，返回空)
     */
    @Nullable
    default UserFile synthesize(@Valid @NotNull VoiceCreationContent content, @NotNull FileBizSource bizSource,
                                @NotNull UserContext userContext) {
        return this.synthesize(content, FileOutput.of(bizSource), userContext);
    }

    /**
     * 什么用户合成什么有声内容，并上传音频到什么位置
     *
     * @param content     有声内容
     * @param fileOutput  音频上传位置 TODO 指定上传的目标路径：是否区分环境，基于user还是system
     * @param userContext 用户上下文
     * @return 合成的音频（未持久化）(注意⚠️：如果输出的音频文件大小为0，则不会上传到OSS，这种情况视为合成失败，返回空)
     */
    @Nullable
    UserFile synthesize(@Valid @NotNull VoiceCreationContent content, @Valid @NotNull FileOutput fileOutput,
                        @NotNull UserContext userContext);

    /**
     * 系统合成语音，并上传到OSS  TODO 需要优化，将文件的环境和权限统一管理起来
     *
     * @param voice       声音
     * @param request     合成参数
     * @param bizSource   业务来源
     * @param audioOption 音频文件上传选项
     * @return 合成的音频（未持久化）(注意⚠️：如果输出的音频文件大小为0，则不会上传到OSS，这种情况视为合成失败，返回空)
     */
    @Nullable
    UserFile synthesizeBySystem(@NotNull Voice voice, @Valid @NotNull VoiceSynthesizeRequest request, @NotNull FileBizSource bizSource,
                                @NotNull FileUploadOption audioOption);

    /**
     * 合成基础语音，写入输出流（没有上传到OSS的动作）
     *
     * @param request 合成参数
     * @param quality 输出音质
     * @return 音频数据(注意⚠️：输出的音频文件大小可能为0，调用方根据实际场景自行校验)
     */
    AudioData synthesize(@Valid @NotNull SpeechSynthesisRequest request, @NotNull AudioQuality quality);
}
