/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.facefusion.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 视频换脸请求
 *
 * <AUTHOR>
 * @version VideoFaceSwapRequest.java, v 0.1 2024-06-21 下午4:41 zhoudong
 */
@Data
public class VideoFaceSwapRequest {
    /**
     * 文件OSS Bucket
     */
    private String bucket;
    /**
     * 输入：视频文件OSS Key
     */
    @JsonProperty("video_file_key")
    private String videoFileKey;
    /**
     * 输入：人脸图片文件OSS Key
     */
    @JsonProperty("face_image_file_key")
    private String faceImageFileKey;
    /**
     * 指定输出的视频文件OSS Key
     */
    @JsonProperty("output_video_file_key")
    private String outputVideoFileKey;
    /**
     * 进度回调地址
     * </p>
     * 1. POST请求
     * 2. 直接在url后面拼接上参数：&status=running/success/fail&failReason=xxx&progress=0~100，整数）
     */
    private String progressCallbackUrl;
}
