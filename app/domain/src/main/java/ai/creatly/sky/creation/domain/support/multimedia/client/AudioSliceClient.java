/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.multimedia.client;

import ai.creatly.sky.creation.domain.support.multimedia.client.model.AudioSliceRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @version AudioSliceClient.java, v 0.1 2024-09-19 下午1:38 zhoudong
 */
public interface AudioSliceClient {

    /**
     * 音频切割
     *
     * @param request 切割入参
     * @return 每个分片的文件大小（字节数）
     */
    List<Long> slice(AudioSliceRequest request);
}
