/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.task;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version StoryRoleImgGenEvent.java, v 0.1 2024-04-07 15:46 syoka
 */
@Data
@Accessors(chain = true)
public class StoryRoleImgGenEvent {

    private Long storyId;
    private Long roleId;
    private Long taskId;
}
