/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.workspace.model.request;

import jakarta.annotation.Nonnull;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version BuildVideoConfigCommand.java, v 0.1 2023-09-23 15:33 syoka
 */
@Data
public class BuildArtifactVideoConfigCommand {

    @Nonnull
    private Integer width;

    @Nonnull
    private Integer height;

    @Nonnull
    private Integer fps;

    @Nonnull
    private String audio;

    private List<BuildArtifactVideoPageConfigCommand> pages;

    @Data
    public static class BuildArtifactVideoPageConfigCommand {
        private String id;

        private ArtifactPageData data;

        private List<ArtifactPageElement> elements;
    }

    @Data
    public static class ArtifactPageData {

        private Integer duration;

        private String transDuration;

        private String trans;

        private String backgroundColor;
    }

    @Data
    public static class ArtifactPageElement {

        private String id;

        private String type;

        private Map<String, Object> config;
    }
}
