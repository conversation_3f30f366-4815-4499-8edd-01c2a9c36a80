/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.credit.cost.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum CreditCostRuleErrorCode implements ErrorCode {

    COST_RULE_NOT_FOUND("计费规则不存在"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return name();
    }
}
