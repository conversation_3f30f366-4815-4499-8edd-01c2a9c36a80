/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.service;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskExecInfo;

/**
 * <AUTHOR>
 * @version TaskAlertHelper.java, v 0.1 2023-11-30 下午4:26 zhoudong
 */
public interface TaskAlertHelper {

    void alertOnExecTooManyTimes(AiTask aiTask);

    void alertOnException(AiTask aiTask, Exception e);

    void alertOnCanceled(AiTask aiTask);

    void alertOnRollback(AiTask aiTask, AiTaskExecInfo execInfo);

    void alertOnRetry(AiTask aiTask, AiTaskExecInfo execInfo, String retryName);

    /**
     * 任务是否自愈恢复（存在错误次数 或 存在回滚次数）
     *
     * @param aiTask 任务实例
     * @return 是否自愈恢复
     */
    boolean isFinishedWithSelfRecovery(AiTask aiTask);

    /**
     * 任务自愈恢复告警（存在错误次数 或 存在回滚次数）
     *
     * @param aiTask 任务实例
     */
    void alertOnFinishedWithSelfRecovery(AiTask aiTask);
}
