/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.mapper;

import ai.creatly.sky.creation.domain.core.digitalhuman.model.request.video.VideoAvatarLayerRequest;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.task.input.VideoAvatarLayer;
import ai.creatly.sky.creation.domain.core.userfile.model.BizFile;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version DigHumanVideoRequestMapper.java, v 0.1 2024-08-01 上午12:50 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface DigHumanVideoRequestMapper {

    @Mapping(target = "assetUrl", source = "coverFile.fileUrl")
    @Mapping(target = "assetId", source = "coverFile.fileId")
    VideoAvatarLayer toVideoAvatarConfig(VideoAvatarLayerRequest request, BizFile coverFile);
}
