/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiimage_old.mapper;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.common.integration.oss.FileProcessOption;
import ai.creatly.sky.creation.domain.common.util.ProgressUtils;
import ai.creatly.sky.creation.domain.core.aiimage_old.model.AiImageMetadata;
import ai.creatly.sky.creation.domain.core.aiimage_old.model.response.AiImageVM;
import ai.creatly.sky.creation.domain.core.aiimage_old.model.response.AttachmentVM;
import ai.creatly.sky.creation.domain.core.aiimage_old.model.task.param.ImageImagineParam;
import ai.creatly.sky.creation.domain.core.aiimage_old.model.task.result.AIImageResult;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskConstant;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Mapper(config = BaseMapperConfig.class)
@Slf4j
public abstract class AiImageMapper {
    private static final int COMPLETE = 100;

    /**
     * 缩小为原来的20%
     */
    private static final int DEFAULT_RATIO = 20;

    @Setter(onMethod_ = @Autowired)
    private UserFileService userFileService;
    @Setter(onMethod_ = @Autowired)
    private UserFileHelper  userFileHelper;

    @Mapping(target = "prompts", ignore = true)
    @Mapping(target = "progress", ignore = true)
    @Mapping(target = "imageUrl", ignore = true)
    @Mapping(target = "finishedDurationSeconds", ignore = true)
    @Mapping(target = "estimatedDurationSeconds", ignore = true)
    @Mapping(target = "errorMsg", ignore = true)
    @Mapping(target = "bannedWord", ignore = true)
    @Mapping(target = "describePrompts", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    public abstract AiImageVM toAiImage(AiTask aiTask);


    @AfterMapping
    protected void toAiImageVMAfter(AiTask aiTask, @MappingTarget AiImageVM aiImageVM) {
        AIImageResult aiImageResult = aiTask.parseBizResult(AIImageResult.class);
        ImageImagineParam imageImagineParam = aiTask.parseBizParams(ImageImagineParam.class);

        aiImageVM.setPrompts(imageImagineParam.getPrompts());
        aiImageVM.setProgress(ProgressUtils.getProgress(aiImageResult.getProgress()));

        // 设置提示词和进度
        if (StringUtils.isNotEmpty(aiImageResult.getImageFileId()) && COMPLETE == aiImageVM.getProgress()) {
            try {
                // 快捷获取httpImageUrl
                if (Objects.nonNull(aiImageResult.getOssUri())) {
                    String httpUrl = userFileHelper.getHttpUrl(aiImageResult.getOssUri(), aiTask.getOwnerId());
                    aiImageVM.setImageUrl(httpUrl);
                } else {
                    String imageUrl = userFileService.queryHttpUrlById(aiTask.getOwnerId(), Long.parseLong(aiImageResult.getImageFileId()));
                    aiImageVM.setImageUrl(imageUrl);
                }
            } catch (Exception exception) {
                log.warn("[AiImageMapper] find userFile image failure,taskId:{}", aiTask.getId(), exception);
                aiImageVM.setErrorMsg("文件异常丢失，请联系管理员");
            }
        }

        if (StringUtils.isEmpty(aiImageVM.getErrorMsg())) {
            aiImageVM.setErrorMsg(getErrorMsgIfExists(aiTask, aiImageResult));
            aiImageVM.setBannedWord(getBannedWord(aiTask));
        }

        // 图片裁剪
        aiImageVM.setAttachments(getAttachment(aiImageResult.getFileMetadata(), aiImageResult.getOssUri()));

    }

    private String getErrorMsgIfExists(AiTask aiTask, AIImageResult aiImageResult) {
        if (StringUtils.isNotEmpty(aiImageResult.getErrorMsg())) {
            return aiImageResult.getErrorMsg();
        }
        JSONObject bizExecInfo = aiTask.getBizExecInfo();
        if (Objects.nonNull(bizExecInfo)) {
            if (bizExecInfo.has(AiTaskConstant.BIZ_ERROR_MSG)) {
                return bizExecInfo.getString(AiTaskConstant.BIZ_ERROR_MSG);
            }
        }
        return null;

    }

    private String getBannedWord(AiTask aiTask) {
        JSONObject bizExecInfo = aiTask.getBizExecInfo();
        if (Objects.nonNull(bizExecInfo) && bizExecInfo.has(AiTaskConstant.BANNED_WORD)) {
            return bizExecInfo.getString(AiTaskConstant.BANNED_WORD);
        }
        return null;
    }

    private List<AttachmentVM> getAttachment(AiImageMetadata fileMetadata, String ossUrl) {
        if (Objects.isNull(fileMetadata) || StringUtils.isEmpty(ossUrl)) {
            return Collections.emptyList();
        }

        int width = Objects.requireNonNull(fileMetadata.getWidth());
        int height = Objects.requireNonNull(fileMetadata.getHeight());

        int halfWidth = width >> 1;
        int halfHeight = height >> 1;


        // 图片1 左上
        String imagePos1 = "w_" + halfWidth + ",h_" + halfHeight + ",x_0,y_0";
        String image1 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE, FileProcessOption.of(getCropStyle(imagePos1)));
        String imageThumbnail1 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE,
                FileProcessOption.of(getStyles(Arrays.asList(getCropStyle(imagePos1), getThumbnail()))));

        // 图片2 右上
        String imagePos2 = "w_" + halfWidth + ",h_" + halfHeight + ",x_" + halfWidth + ",y_0";
        String image2 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE, FileProcessOption.of(getCropStyle(imagePos2)));
        String imageThumbnail2 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE,
                FileProcessOption.of(getStyles(Arrays.asList(getCropStyle(imagePos2), getThumbnail()))));

        // 图片3 左下
        String imagePos3 = "w_" + halfWidth + ",h_" + halfHeight + ",x_0,y_" + halfHeight;
        String image3 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE, FileProcessOption.of(getCropStyle(imagePos3)));
        String imageThumbnail3 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE,
                FileProcessOption.of(getStyles(Arrays.asList(getCropStyle(imagePos3), getThumbnail()))));

        // 图片4 右下
        String imagePos4 = "w_" + halfWidth + ",h_" + halfHeight + ",x_" + halfWidth + ",y_" + halfHeight;
        String image4 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE, FileProcessOption.of(getCropStyle(imagePos4)));
        String imageThumbnail4 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE,
                FileProcessOption.of(getStyles(Arrays.asList(getCropStyle(imagePos4), getThumbnail()))));

        return Arrays.asList(new AttachmentVM(1, image1, imageThumbnail1),
                new AttachmentVM(2, image2, imageThumbnail2),
                new AttachmentVM(3, image3, imageThumbnail3),
                new AttachmentVM(4, image4, imageThumbnail4));
    }


    private String getStyles(List<String> styles) {
        return String.join(",", styles);
    }

    private String getCropStyle(String position) {
        return String.join(",", "image/crop", position);
    }

    private String getThumbnail() {
        return String.join(",", "image/resize", "p_" + AiImageMapper.DEFAULT_RATIO);
    }
}
