/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.task.exec;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskConstant;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * 故事角色图（4合1原图）
 *
 * <AUTHOR>
 * @version StoryRoleSourceImage.java, v 0.1 2024-05-01 上午12:24 zhoudong
 */
@Data
@Accessors(chain = true)
public class StoryRoleSourceImage {

    /**
     * 角色ID
     */
    private String roleId;
    /**
     * 文生图提示词
     */
    private String prompt;
    /**
     * 4合1原图文件ID
     */
    @Nullable
    private Long   imageFileId;
    /**
     * 外部生图任务ID
     */
    @Nullable
    private String imageOutTaskId;
    /**
     * 外部生图任务状态
     */
    @Nullable
    private String imageOutTaskStatus;

    @JsonIgnore
    public boolean isFinishStatus() {
        return AiTaskConstant.BIZ_STATUS_SUCCESS.equals(this.imageOutTaskStatus)
                || AiTaskConstant.BIZ_STATUS_FAILURE.equals(this.imageOutTaskStatus);
    }

    @JsonIgnore
    public boolean isSuccessStatus() {
        return AiTaskConstant.BIZ_STATUS_SUCCESS.equals(this.imageOutTaskStatus);
    }

    @JsonIgnore
    public boolean isFailStatus() {
        return AiTaskConstant.BIZ_STATUS_FAILURE.equals(this.imageOutTaskStatus);
    }

    @JsonIgnore
    public boolean isNotStartStatus() {
        return "NOT_START".equals(this.imageOutTaskStatus);
    }
}
