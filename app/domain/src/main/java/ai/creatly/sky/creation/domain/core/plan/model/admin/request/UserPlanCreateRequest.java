/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.admin.request;

import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanPeriodType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.Duration;
import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @version UserPlanCreateRequest.java, v 0.1 2023-12-21 下午9:23 zhoudong
 */
@Data
public class UserPlanCreateRequest {

    /**
     * 用户ID
     */
    @NotBlank
    private String                       uid;
    /**
     * 平台统一定价计划ID
     */
    @NotBlank
    private String                       platformPlanId;
    /**
     * 描述
     */
    @NotBlank
    private String                       description;
    /**
     * 原价（分）
     */
    @NotNull
    private Integer                      originalFee;
    /**
     * 实价（分）
     */
    @NotNull
    private Integer                      realFee;
    /**
     * 计划周期类型
     */
    @NotNull
    private PlanPeriodType               periodType;
    /**
     * 权益有效期（以天为单位，如果有值，最多365天，为空则表示终身有效）
     */
    @NotNull
    private Duration                     benefitDuration;
    /**
     * 会员赠送余额
     */
    private Integer                      memberGiftCredits;
    /**
     * 会员赠送余额权益名称
     */
    private String                       memberCreditsBenefitName;
    /**
     * 会员充值赠送余额权益列表
     */
    @Valid
    private List<TopUpCreditsBenefitDTO> memberTopUpCreditsBenefits;

    public Optional<TopUpCreditsBenefitDTO> findMemberTopUpCreditsBenefit(String benefitCode) {
        return memberTopUpCreditsBenefits.stream()
                .filter(topUpCreditsBenefit -> benefitCode.equals(topUpCreditsBenefit.getBenefitCode()))
                .findFirst();
    }
}
