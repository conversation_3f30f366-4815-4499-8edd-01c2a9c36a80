/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.credit.model.response;

import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version CreditsExpenseVM.java, v 0.1 2024-06-26 下午4:36 zhoudong
 */
@Data
@Accessors(chain = true)
public class CreditsExpenseVM {
    private CreditLogBizType  bizType;
    private CreditAccountType type;
    private Integer           amount;
}
