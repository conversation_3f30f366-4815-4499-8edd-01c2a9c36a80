/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version DramaContent.java, 2024-10-28 下午8:39 zhoudong
 */
@Data
@Accessors(chain = true)
public class DramaContent {

    /**
     * 多个视频用 竖线 分隔
     */
    private String md5;
    /**
     * 算法内容打标数据
     */
    private String description;
}
