/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.response;

import ai.creatly.sky.creation.domain.core.story.model.role.StoryRoleGender;
import ai.creatly.sky.creation.domain.core.story.model.role.StoryRoleGeneration;
import ai.creatly.sky.creation.domain.core.story.model.role.StoryRoleSource;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * 故事角色
 *
 * <AUTHOR>
 * @version StoryRoleVM.java, v 0.1 2024-03-06 22:40 heb
 */
@Data
@Accessors(chain = true)
public class StoryRoleVM {

    /**
     * 角色ID
     */
    private String              id;
    /**
     * 角色名称
     */
    private String              roleName;
    /**
     * 角色来源
     */
    private StoryRoleSource     roleSource;
    /**
     * 角色性别
     */
    @Nullable
    private StoryRoleGender     roleGender;
    /**
     * 角色年龄段
     */
    @Nullable
    private StoryRoleGeneration roleGeneration;
    /**
     * 角色描述
     */
    private String              roleDesc;
    /**
     * 角色形象图文件ID
     */
    @Nullable
    private String              portraitId;
    /**
     * 角色形象图地址（HTTP地址）
     */
    @Nullable
    private String              portraitUrl;
    /**
     * resize过的图
     */
    @Nullable
    private String              portraitThumbnailUrl;
    /**
     * 角色声音ID
     */
    @Nullable
    private String              voiceId;
}
