/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aivideo.repository;

import ai.creatly.sky.creation.domain.core.aivideo.model.AiVideo;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;

/**
 * <AUTHOR>
 * @version AiVideoRepository.java, v 0.1 2024-05-06 下午2:37 zhoudong
 */
public interface AiVideoRepository {

    AiVideo create(AiVideo aiVideo);

    AiVideo update(AiVideo aiVideo);

    AiVideo getById(Long videoId);

    /**
     * 分页查询公开的AI视频列表
     *
     * @param pageable 分页参数
     * @return AI视频列表
     */
    Page<AiVideo> queryPublicPage(Pageable pageable);
}
