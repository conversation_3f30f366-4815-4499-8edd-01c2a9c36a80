/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.deprecated.aiproduct;

import ai.creatly.sky.creation.domain.deprecated.aiproduct.model.SceneImage;
import ai.creatly.sky.creation.domain.deprecated.aiproduct.model.SceneSnapshotImage;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;

import java.util.Optional;

/**
 * 商品场景图仓储服务
 *
 * <AUTHOR>
 * @version SceneImageRepository.java, v 0.1 2023-11-16 10:50 syoka
 */
public interface SceneImageRepository {

    /**
     * 通过id查询场景快照
     *
     * @param sceneSnapshotId 快照主键ID
     * @return 场景快照图
     */
    Optional<SceneSnapshotImage> querySnapshotOptionalById(Long sceneSnapshotId);

    /**
     * 查询当前场景图的最新快照版
     *
     * @param sceneId 场景图
     * @return
     */
    Optional<SceneSnapshotImage> queryLatestSnapshotOptionalBySceneId(Long sceneId, Integer index);

    /**
     * 查找场景图的第一个版本
     *
     * @param sceneId
     * @param index
     * @return
     */
    Optional<SceneSnapshotImage> queryFirstSnapshotVersion(Long sceneId, Integer index);

    /**
     * 检查场景图是否有存量快照
     *
     * @param sceneId 场景图
     * @return 是否存在
     */
    boolean existsSnapshotBySceneId(long sceneId, Integer index);

    /**
     * 通过id查询场景
     *
     * @param sceneImageId 主键ID
     * @return 场景图
     */
    Optional<SceneImage> queryOptionalById(long sceneImageId);

    /**
     * 查询商品场景图
     *
     * @param uid 用户id
     * @param pageable 分页参数
     * @return 场景图列表
     */
    Page<SceneImage> queryUserSceneImagePageable(Long uid, Pageable pageable);

    /**
     * 创建场景图片
     *
     * @param sceneImage 场景图
     * @return
     */
    Long createSceneImage(SceneImage sceneImage);

    /**
     * 创建一个快照副本
     *
     * @param sceneSnapshotImage 场景快照图
     * @return 快照主键ID
     */
    long createSnapshot(SceneSnapshotImage sceneSnapshotImage);

    /**
     * 清除制定场景图的所有快照数据
     *
     * @param sceneId
     */
    void deleteSceneSnapshot(Long sceneId, Integer index);

    void deleteSceneSnapshotVersion(Long snapshotId);
}
