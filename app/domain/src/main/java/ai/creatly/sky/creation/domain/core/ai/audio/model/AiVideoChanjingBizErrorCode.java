package ai.creatly.sky.creation.domain.core.ai.audio.model;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.json.JSONObject;

/**
 * <AUTHOR>
 */

@Getter
@RequiredArgsConstructor
public enum AiVideoChanjingBizErrorCode implements ErrorCode {

    ERROR_CODE_1000("1000","生成异常，请重新生成！", 1),
    ERROR_CODE_400("400", "传入参数格式错误",0),
    ERROR_CODE_10400("10400", "AccessToken验证失败/缺少信息",0),
    ERROR_CODE_40000("40000", "参数错误", 0),
    ERROR_CODE_40001("40001", "超出QPS限制",0),
    ERROR_CODE_40002("40001", "制作视频时长到达上限",1),
    ERROR_CODE_50000("50000", "系统内部错误/数字人错误",0),
    ERROR_CODE_50011("50011", "作品存在不合法规文字内容",1),
    ERROR_CODE_10("10","数字人配套音频上传失败", 0),
    ERROR_CODE_11("11", "数字人配套音频生成失败", 0),
    ERROR_CODE_12("12", "蝉豆不足扣费失败", 0);

    private final String code;
    private final String msg;
    private final Integer level;


    public static String getErrorMsg(String code) {
        for (AiVideoChanjingBizErrorCode errorCode : AiVideoChanjingBizErrorCode.values()) {
            if (errorCode.getCode().equals(code)) {
                JSONObject result = new JSONObject();
                result.put("errorCode", errorCode.getCode());
                result.put("errorLevel", errorCode.getLevel());
                result.put("errorMsg", errorCode.getMsg());
                return result.toString();
            }
        }
        JSONObject result = new JSONObject();
        result.put("errorCode", "1000");
        result.put("errorLevel", 1);
        result.put("errorMsg", "生成异常，请重新生成！");
        return result.toString();
    }


}
