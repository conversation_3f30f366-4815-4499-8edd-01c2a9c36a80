/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.tool;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 元气充值页面
 *
 * <AUTHOR>
 * @version CourseWxNativePrepayResult.java
 */
@Data
@Accessors(chain = true)
public class RechargeWxPrepayVM {
    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 收银台过期时间（要设计为小于微信的有效期，微信的有效期是2小时）
     */
    private ZonedDateTime expireAt;
    /**
     * 扫码支付：预支付跳转链接（注意：不是二维码图片地址，而是二维码里的内容）
     */
    private String codeUrl;

    /**
     * 支付结果
     * <p>
     * SUCCESS：支付成功
     * <p>
     * REFUND：转入退款
     * <p>
     * NOTPAY：未支付
     * <p>
     * CLOSED：已关闭
     */
    private String status;

}
