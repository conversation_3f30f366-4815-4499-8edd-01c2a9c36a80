package ai.creatly.sky.creation.domain.core.ai.tool;

import ai.creatly.kylin.trade.sdk.order.client.TradeOrderClient;
import ai.creatly.kylin.trade.sdk.order.enums.OrderItemType;
import ai.creatly.kylin.trade.sdk.order.error.OrderErrorCode;
import ai.creatly.kylin.trade.sdk.order.request.OrderCreateAndWxNativePrepayRequest;
import ai.creatly.kylin.trade.sdk.order.request.OrderCreateRequest;
import ai.creatly.kylin.trade.sdk.order.request.OrderItemDTO;
import ai.creatly.kylin.trade.sdk.order.response.OrderDetailVO;
import ai.creatly.kylin.trade.sdk.payment.wxpay.response.WxNativePrepayVO;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsIncome;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.support.trade.OrderBizSource;
import ai.creatly.sky.creation.domain.support.trade.OrderBizStatus;
import ai.creatly.sky.creation.domain.support.trade.OrderBizType;
import ai.creatly.sky.creation.domain.support.trade.TradeConstants;
import com.jspeeder.core.data.problem.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version AiToolCreditService.java, v0.1 2025-03-26 11:35
 */
@Service
@Slf4j
public class AiRechargeService {

    @Autowired
    private AiRechargeRepository aiRechargeRepository;

    @Autowired
    private TradeOrderClient tradeOrderClient;

    @Autowired
    private UserCreditDomainService userCreditDomainService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    public List<AiRecharge> getRechargeList(){
        return aiRechargeRepository.getRechargeList();
    }

    public RechargeWxPrepayVM getRechargeCode(Long credit, UserContext userContext,String clientIp){
        AiRecharge aiRecharge = aiRechargeRepository.getRecharge(credit, userContext);
        //充值二维码在有效期内
        if (aiRecharge != null) {
            if (aiRecharge.getExpireAt().isAfter(Instant.now().atZone(ZoneId.systemDefault()))) {
                RechargeWxPrepayVM rechargeWxPrepayVM = new RechargeWxPrepayVM();
                rechargeWxPrepayVM.setCodeUrl(aiRecharge.getCodeUrl());
                rechargeWxPrepayVM.setOrderId(aiRecharge.getOrderId().toString());
                rechargeWxPrepayVM.setExpireAt(aiRecharge.getExpireAt());
                return rechargeWxPrepayVM;
            } else {
                OrderDetailVO orderDetail = tradeOrderClient.queryOrderDetail(TradeConstants.TENANT_CODE, aiRecharge.getOrderId());
                if (orderDetail.getPaidTime() != null) {
                    ZonedDateTime payTime = Instant.ofEpochMilli(orderDetail.getPaidTime())
                            .atZone(ZoneId.systemDefault());
                    aiRecharge.setPaidTime(payTime);
                    aiRecharge.setPayStatus(OrderBizStatus.SUCCESS.name());
                } else {
                    aiRecharge.setPayStatus(OrderBizStatus.CLOSED.name());
                }
                aiRechargeRepository.update(aiRecharge);
            }
        }

        AiRecharge rechargeTemplate = aiRechargeRepository.getTemplateByCredit(credit);
        OrderCreateRequest order = new OrderCreateRequest();
        order.setTenantCode(TradeConstants.TENANT_CODE);
        order.setBizSn(userContext.getUid() + "-" + credit + "-" + Instant.now().getEpochSecond());
        order.setBizSource(OrderBizSource.WEB.name());
        order.setBizType(OrderBizType.RECHARGE.name());
        order.setBizSubType(credit.toString());
        order.setTitle("元气充值"+credit);
        order.setDescription("元气充值"+credit);
        order.setBuyerId(String.valueOf(userContext.getUid()));
        order.setBuyerName(userContext.getCurrentUsername());
        order.setSellerId(AppConstants.SYSTEM_UID.toString());
        order.setSellerName(AppConstants.SYSTEM_USERNAME);
        order.setValidMinutes(1);
        order.setItems(List.of(getOrderItemDTO(rechargeTemplate.getPrice())));

        // 下单并支付
        OrderCreateAndWxNativePrepayRequest request = new OrderCreateAndWxNativePrepayRequest();
        request.setOrder(order);
        request.setPayerClientIp(clientIp);
        WxNativePrepayVO wxNativePrepayVO = tradeOrderClient.createOrderAndPay(request);

        // 返回微信预支付结果
        ZonedDateTime expireAt = Instant.ofEpochMilli(wxNativePrepayVO.getOrderCreatedAt())
                .atZone(ZoneId.systemDefault())
                .plusMinutes(1);

        AiRecharge createRecharge = new AiRecharge();
        createRecharge.setOrderId(wxNativePrepayVO.getOrderId());
        createRecharge.setPayStatus(OrderBizStatus.NOTPAY.name());
        createRecharge.setPrice(rechargeTemplate.getPrice());
        createRecharge.setCredit(rechargeTemplate.getCredit());
        createRecharge.setStatus(rechargeTemplate.getStatus());
        createRecharge.setPriority(0L);
        createRecharge.setOwnerId(userContext.getUid());
        createRecharge.setOwnerName(userContext.getCurrentUsername());
        createRecharge.setExpireAt(expireAt);
        createRecharge.setCodeUrl(wxNativePrepayVO.getCodeUrl());
        aiRechargeRepository.createRecord(createRecharge);

        RechargeWxPrepayVM rechargeWxPrepayVM = new RechargeWxPrepayVM();
        rechargeWxPrepayVM.setCodeUrl(wxNativePrepayVO.getCodeUrl());
        rechargeWxPrepayVM.setOrderId(wxNativePrepayVO.getOrderId().toString());
        rechargeWxPrepayVM.setExpireAt(expireAt);
        return rechargeWxPrepayVM;

    }

    @NotNull
    private static OrderItemDTO getOrderItemDTO(Long price) {
        OrderItemDTO orderItemDTO = new OrderItemDTO();
        orderItemDTO.setType(OrderItemType.SERVICE);
        orderItemDTO.setItemId(String.valueOf(price));
        orderItemDTO.setItemName("元气充值"+price);
        orderItemDTO.setItemCategories(List.of(OrderBizType.COURSE.name()));
        orderItemDTO.setItemDuration(Duration.ofDays(30));
        orderItemDTO.setUnitPrice(price);
        orderItemDTO.setRealUnitPrice(price);
        orderItemDTO.setQuantity(1);
        return orderItemDTO;
    }

    public RechargeWxPrepayVM payResult(String orderId){
        try {
            AiRecharge record = aiRechargeRepository.getRechargeByOrderId(Long.parseLong(orderId));
            if (record == null) {
                log.info("recharge record not exist, orderId=" + orderId);
                return null;
            }
            //已更新状态增加元气不重复增加
            if (!OrderBizStatus.SUCCESS.name().equals(record.getPayStatus())) {
                log.info("recharge orderId=" + orderId);
                OrderDetailVO orderDetail = tradeOrderClient.queryOrderDetail(TradeConstants.TENANT_CODE, record.getOrderId());
                transactionTemplate.setTimeout(10);
                transactionTemplate.executeWithoutResult(status -> {
                    if (orderDetail.getPaidTime() != null) {
                        ZonedDateTime payTime = Instant.ofEpochMilli(orderDetail.getPaidTime())
                                .atZone(ZoneId.systemDefault());
                        record.setPaidTime(payTime);
                        record.setPayStatus(OrderBizStatus.SUCCESS.name());
                        aiRechargeRepository.update(record);

                        //账户新增元气,orderId作为新增元气id避免并发时重复新增元气
                        CreditsIncome income = CreditsIncome.builder()
                                .uid(record.getOwnerId())
                                .bizType(CreditLogBizType.TOP_UP_PLAN_ORDER)
                                .bizNo(orderId)
                                .type(CreditAccountType.GENERAL)
                                .amount(record.getCredit().intValue())
                                .build();
                        userCreditDomainService.addCreditAccount(income);
                    }
                });
            }
            RechargeWxPrepayVM rechargeWxPrepayVM = new RechargeWxPrepayVM();
            rechargeWxPrepayVM.setOrderId(record.getOrderId().toString());
            rechargeWxPrepayVM.setStatus(record.getPayStatus());
            return rechargeWxPrepayVM;
        } catch (BizException e) {
            if (Objects.equals(e.getErrorCode().getCode(), OrderErrorCode.ORDER_NOT_EXISTS.getCode())) {
                // 订单不存在
                return null;
            }
            throw e;
        }
    }
}
