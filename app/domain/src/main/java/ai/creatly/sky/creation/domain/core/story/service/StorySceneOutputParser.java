/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.service;

import ai.creatly.sky.creation.domain.core.story.error.StoryErrorCode;
import ai.creatly.sky.creation.domain.core.story.model.scene.LLMGenStoryScene;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.json.JSON;
import dev.langchain4j.model.output.OutputParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 故事剧本分镜解析
 *
 * <AUTHOR>
 * @version StorySceneOutputParser.java, v 0.1 2024-03-09 14:49 heb
 */
@Component
@Slf4j
public class StorySceneOutputParser implements OutputParser<LLMGenStoryScene> {
    @Override
    public LLMGenStoryScene parse(String text) {
        String escapeStr = escapeControlChars(text);
        try {
            return JSON.parseObject(escapeStr, LLMGenStoryScene.class);
        } catch (Exception e) {
            log.info("[StorySceneOutputParser] parse failure,text:{}", text);
            throw new BizException(StoryErrorCode.STORY_GEN_UNKNOWN_EXCEPTION);
        }
    }

    @Override
    public String formatInstructions() {
        return "parse string to story draft model";
    }

    public String escapeControlChars(String text) {
        StringBuilder sb = new StringBuilder();
        for (char c : text.toCharArray()) {
            if (!Character.isISOControl(c)) {
                sb.append(c);
            }
        }
        return sb.toString();
    }
}
