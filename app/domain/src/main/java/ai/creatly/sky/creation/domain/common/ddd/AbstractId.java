/*
 * Copyright (C), 2021-2023, haixuejie All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.common.ddd;

/**
 * 实体/聚合根id
 *
 * <AUTHOR>
 * @version : AbstractId.java, v 1.0 2023年08月08日 23时19分 syoka Exp$
 */
public class AbstractId {

    protected final Long id;

    public AbstractId(Long id) {
        this.id = id;
    }

    public AbstractId(String id) {
        this.id = Long.valueOf(id);
    }

    public String stringValue() {
        return this.id.toString();
    }

    public Long longValue() {
        return this.id;
    }
}
