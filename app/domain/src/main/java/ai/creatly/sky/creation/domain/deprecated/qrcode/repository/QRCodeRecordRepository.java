/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.qrcode.repository;

import ai.creatly.sky.creation.domain.deprecated.qrcode.QrcodeRecordInfo;
import ai.creatly.sky.creation.domain.deprecated.qrcode.model.QRCodeScene;
import ai.creatly.sky.creation.domain.deprecated.qrcode.model.QrCodeScanRecordInfo;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * QR生成历史仓库
 *
 * <AUTHOR>
 * @version : QRCodeHistoryRepository.java, v 1.0 2023年07月21日 16时21分 syoka Exp$
 */
public interface QRCodeRecordRepository {

    /**
     * 通过用户id+场景 查询用户的生成历史记录
     *
     * @param issuer 码签发
     * @param scene  场景
     * @return 码签发历史
     */
    QrcodeRecordInfo getQRCodeRecordByIssuerAndScene(Long issuer, QRCodeScene scene);

    /**
     * 通过code获取发码记录信息
     */
    @Nullable
    QrcodeRecordInfo getQRCodeRecordByCode(String code);

    /**
     * 创建二维码生成记录
     *
     * @param history 二维码记录
     */
    void create(QrcodeRecordInfo history);

    /**
     * 获取码的扫描次数
     */
    long getValidScanTimes(String code);

    /**
     * 增加QRCode的用户扫码次数
     */
    void createQRCodeScanTime(Long issuerUserId, Long scanUserId, String code);

    /**
     * 获取qr
     */
    List<QrCodeScanRecordInfo> getQRCodeScanRecordByCode(Long uid, String code);

    /**
     * 通过用户的uid和scene查找对应的二维码
     *
     * @param uid   -
     * @param scene -
     * @return -
     */
    String getQRCodeRecordByUidAndScene(Long uid, QRCodeScene scene);
}
