/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.deprecated.menu.repository;

import ai.creatly.sky.creation.domain.deprecated.menu.model.MenuItem;

import java.util.List;

/**
 * <AUTHOR>
 * @version MenuItemRepository.java, v 0.1 2023-06-13 23:16 joton
 */
public interface MenuItemRepository {

    List<MenuItem> queryByMenuId(long menuId);
}
