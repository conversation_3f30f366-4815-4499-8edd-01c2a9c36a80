/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.course.service;

import ai.creatly.sky.creation.domain.core.course.model.AiCourseOrderRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @version AiToolRepository.java, v0.1 2025-02-19 21:17
 */
public interface AiCourseOrderRecordRepository {
    AiCourseOrderRecord getById(Long uid, Long courseId);

    AiCourseOrderRecord getByOrderId(Long uid, Long orderId);

    AiCourseOrderRecord getByOrderId(Long orderId);

    List<AiCourseOrderRecord> getList(Long uid, Long courseId);

    void createCourseOrderRecord(AiCourseOrderRecord aiCourseOrderRecord);

    boolean updateRecord(AiCourseOrderRecord aiCourseOrderRecord);

    AiCourseOrderRecord getCourseSucced(Long uid,Long courseId);
}
