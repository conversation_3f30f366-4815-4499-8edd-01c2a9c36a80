/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.service;

import ai.creatly.sky.creation.domain.core.ai.image.mapper.ImageExpansionMapper;
import ai.creatly.sky.creation.domain.core.ai.image.model.ImageExpansion;
import ai.creatly.sky.creation.domain.core.ai.image.task.AiImageTaskBizType;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskQO;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version ImageExpansionService.java, v0.1 2025-02-19 21:39
 */
@Service
@RequiredArgsConstructor
public class ImageExpansionService {

    private final ImageExpansionMapper imageExpansionMapper;
    private final AiTaskRepository     aiTaskRepository;

    public Page<ImageExpansion> queryGenerationPage(long uid, Pageable pageable) {
        AiTaskQO qo = new AiTaskQO()
                .setTaskType(AiTaskType.AI_IMAGE)
                .setBizType(AiImageTaskBizType.image_expand)
                .setOwnerId(uid);
        Page<AiTask> page = aiTaskRepository.queryPage(qo, pageable);
        return page.map(imageExpansionMapper::toImageExpansion);
    }
}
