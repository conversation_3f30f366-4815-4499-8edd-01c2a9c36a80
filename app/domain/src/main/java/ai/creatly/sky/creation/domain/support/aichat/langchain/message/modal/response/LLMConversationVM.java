/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal.response;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class LLMConversationVM {


    /**
     * 用户id
     */
    private String uid;

    /**
     * 会话id
     */
    private String conversationId;

    /**
     * 场景标识
     */
    private String scenarioId;

    /**
     * 会话中的场景关联id
     */
    private String scenarioCtxNo;

    /**
     * 最后一次消息
     */
    private String title;

    /**
     * 最后一次会话时间
     */
    private LocalDateTime createTime;


}
