/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.request;

import ai.creatly.sky.creation.domain.common.validation.EachNotBlank;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 视频带货产品信息
 *
 * <AUTHOR>
 * @version VideoProductDTO.java, v 0.1 2024-09-24 下午3:46 zhoudong
 */
@Data
public class VideoProductDTO {
    /**
     * 产品标题
     */
    @NotBlank
    @Size(min = 2, max = 100)
    private String       title;
    /**
     * 带货产品分类（全路径）
     */
    @NotEmpty
    @Size(min = 1, max = 4)
    @EachNotBlank
    private List<String> categoryCodes;
    /**
     * 产品人群分类（全路径）
     */
    @NotEmpty
    @Size(min = 1, max = 4)
    @EachNotBlank
    private List<String> crowdCategoryCodes;
    /**
     * 产品特色
     */
    @NotEmpty
    @Size(max = 10)
    @EachNotBlank
    private List<String> features;
    /**
     * 产品详细描述
     */
    @Size(min = 2, max = 200)
    private String       description;
}
