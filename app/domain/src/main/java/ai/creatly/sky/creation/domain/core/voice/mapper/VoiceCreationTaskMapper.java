/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.mapper;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.response.BaseUserAiTaskVM;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceTimeEstimation;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceCreationRequest;
import ai.creatly.sky.creation.domain.core.voice.model.response.task.VoiceCreationTaskVM;
import ai.creatly.sky.creation.domain.core.voice.model.task.VoiceTaskBizType;
import ai.creatly.sky.creation.domain.core.voice.model.task.param.VoiceCreationParams;
import ai.creatly.sky.creation.domain.core.voice.model.task.param.VoiceSynthesisParams;
import ai.creatly.sky.creation.domain.core.voice.model.task.result.VoiceTaskResult;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.problem.exception.SysException;
import jodd.util.StringUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

/**
 *
 * <AUTHOR>
 * @version VoiceCreationTaskMapper.java, v 0.1 2023-12-13 上午10:50 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface VoiceCreationTaskMapper extends VoiceTaskBaseMapper {

    default AiTask toVoiceCreationTask(Voice voice, VoiceCreationRequest request, UserContext userContext,
                                       VoiceTimeEstimation timeEstimation) {
        String bizType = voice.getCategory().getCreationTaskBizType().getCode();
        VoiceCreationParams params = this.toCreationParams(request, timeEstimation);
        return this.toVoiceTask(voice, bizType, userContext, params);
    }

    VoiceCreationParams toCreationParams(VoiceCreationRequest request, VoiceTimeEstimation timeEstimation);

    VoiceCreationRequest toCreationRequest(VoiceCreationParams params);

    default VoiceCreationTaskVM toVoiceCreationTaskVM(AiTask task, UserFileHelper userFileHelper) {
        final String audioName;
        VoiceTaskBizType bizType = VoiceTaskBizType.valueOf(task.getBizType());
        if (bizType.isSynthesis()) {
            var params = task.parseBizParams(VoiceSynthesisParams.class);
            // 取输入文本的前30个字符作为文件名，不满则为全部输入文本
            audioName = StringUtil.substring(params.getInputText(), 0, 30);
        } else if (bizType.isCreation()) {
            var params = task.parseBizParams(VoiceCreationParams.class);
            String text = VoiceCreationMapper.getRawText(params.getDialogues());
            if (text.length() > 21) {
                audioName = StringUtil.substring(text, 0, 20) + "......";
            } else {
                audioName = StringUtil.substring(text, 0, 20);
            }
        } else {
            throw new SysException("will not happen");
        }

        VoiceCreationTaskVM voiceCreationTaskVM = new VoiceCreationTaskVM();
        this.fillBaseUserAiTaskVM(task, voiceCreationTaskVM);

        VoiceTaskResult result = task.parseBizResult(VoiceTaskResult.class);
        return voiceCreationTaskVM
                .setAudioName(audioName)
                .setAudioUrl(userFileHelper.getHttpUrl(result.getAudioUrl(), task.getOwnerId()));
    }

    @Mapping(target = "finishedDurationSeconds", source = "sysResult.finishedDuration.seconds")
    @Mapping(target = "estimatedDurationSeconds", source = "estimatedDuration.seconds")
    void fillBaseUserAiTaskVM(AiTask task, @MappingTarget BaseUserAiTaskVM baseUserAiTaskVM);
}
