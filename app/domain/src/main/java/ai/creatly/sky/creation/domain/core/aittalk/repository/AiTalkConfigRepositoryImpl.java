package ai.creatly.sky.creation.domain.core.aittalk.repository;

import ai.creatly.sky.creation.domain.core.aittalk.model.enums.AiTalkProvider;
import ai.creatly.sky.creation.domain.support.bizconfig.repository.BizConfigRepository;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;
import lombok.RequiredArgsConstructor;
import org.json.JSONObject;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version AiTalkConfigRepositoryImpl.java, v 0.1 2023-07-29 10:40 joton
 */
@Repository
@RequiredArgsConstructor
class AiTalkConfigRepositoryImpl implements AiTalkConfigRepository {

    private final BizConfigRepository bizConfigRepository;

    @Override
    public AiTalkProvider getAiTalkProvider() {
        String schemaCode = "common_kv";
        String configKey = "ai_talk_provider";
        JSONObject jsonObject = bizConfigRepository.queryOne(schemaCode, List.of(configKey), JSONObject.class)
                .orElseThrow(() -> new SysException(CommonErrorCode.UNSPECIFIED));
        return jsonObject.getEnum(AiTalkProvider.class, "value");
    }
}
