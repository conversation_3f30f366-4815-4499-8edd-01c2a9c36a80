/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.video.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version ProductVideoErrorCode.java, v 0.1 2024-05-25 上午11:45 zhoudong
 */
@AllArgsConstructor
@Getter
public enum ProductVideoErrorCode implements ErrorCode {

    PRODUCT_VIDEO_NOT_FOUND("商品视频不存在"),
    PRODUCT_VIDEO_RE_TALK_DOWNLOAD_INPUT_FILE_TIMEOUT("商品视频重配音，下载文件超时"),
    PRODUCT_VIDEO_RE_TALK_TASK_NOT_FOUND("商品视频重配音任务不存在"),
    PRODUCT_VIDEO_RE_TALK_PROGRESS_INVALID("无效进度"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
