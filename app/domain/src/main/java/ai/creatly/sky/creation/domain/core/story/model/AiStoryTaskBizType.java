/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model;

import com.jspeeder.core.data.enums.ICode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version AiStoryTaskBizType.java, v 0.1 2024-04-24 下午11:55 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum AiStoryTaskBizType implements ICode {

    STORY_ROLE_PORTRAIT_INIT("故事角色图初始化（多角色批量生图）"),
    STORY_ROLE_PORTRAIT_GENERATE("故事角色图生成（单角色生图）"),
    STORY_SHOT_IMAGE_GENERATE("故事分镜图生成（单分镜生图）"),
    STORY_SHOT_CAMERA_MOVEMENT("故事运镜（单分镜图生运镜）"),
    STORY_SHOT_EFFECT("故事动效（单分镜图生动效）"),
    ;

    private final String desc;

    @Override
    public String getCode() {
        return this.name();
    }
}
