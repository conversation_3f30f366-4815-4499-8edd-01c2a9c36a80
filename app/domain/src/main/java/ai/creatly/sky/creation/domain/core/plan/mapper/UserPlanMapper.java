/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.mapper;

import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.PlanBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.UserPlan;
import ai.creatly.sky.creation.domain.core.plan.model.UserPlanBenefit;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 *
 * <AUTHOR>
 * @version UserPlanMapper.java, v 0.1 2023-12-21 下午4:01 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface UserPlanMapper {

    default Plan toPlan(UserPlan userPlan) {
        Plan plan = new Plan();
        this.updatePlan(userPlan, plan);
        plan.setBenefits(this.toPlanBenefits(userPlan.getId(), userPlan.getBenefits()));
        return plan;
    }

    @Mapping(target = "source", constant = "USER")
    @Mapping(target = "buyMethod", constant = "ONLINE_PAY")
    @Mapping(target = "benefits", ignore = true)
    void updatePlan(UserPlan userPlan, @MappingTarget Plan plan);

    default List<PlanBenefit> toPlanBenefits(long planId, List<UserPlanBenefit> benefits) {
        if (benefits == null) {
            return null;
        }
        return benefits.stream()
                .map(userPlanBenefit -> this.toPlanBenefit(planId, userPlanBenefit))
                .collect(toList());
    }

    PlanBenefit toPlanBenefit(long planId, UserPlanBenefit benefit);
}
