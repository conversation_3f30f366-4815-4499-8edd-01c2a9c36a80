/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.util;

import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 * @version : AppConstants.java, v 1.0 2023年06月24日 14时44分 syoka
 */
@UtilityClass
public class AppConstants {

    public static final String APP_NAME = "creation";

    public static final Long   SYSTEM_UID      = 1L;
    public static final String SYSTEM_USERNAME = "SYSTEM";

    public static final String DEFAULT_ORG = "creatly";

    /**
     * 判断是否为系统用户
     *
     * @param uid 用户ID
     * @return 是否为系统用户
     */
    public boolean isSystemUser(long uid) {
        return SYSTEM_UID.equals(uid);
    }

    public static final String MQ_TOPIC = "CREATION_TOPIC";

    public interface MQTags {
        String PLAN_ORDER_PAID              = "plan_order_paid";
        String VOICE_AUDIO_GENERATED        = "voice_audio_generated";
        String AI_PRODUCT_IMAGE_IN_PAINTING = "ai_product_image_in_painting";
        String USER_VOICE_ORDER_PAID        = "user_voice_order_paid";
        String PRODUCT_AI_IMAGE_IMAGINED    = "product_ai_image_imagined";
    }
}
