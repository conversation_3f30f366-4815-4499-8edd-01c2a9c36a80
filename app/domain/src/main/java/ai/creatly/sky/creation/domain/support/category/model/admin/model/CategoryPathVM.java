/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.category.model.admin.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version CategoryPathVM.java, 2024-12-17 下午3:57 zhoudong
 */
@Data
@Accessors(chain = true)
public class CategoryPathVM {

    private String       id;
    /**
     * 类目编号（全路径）
     */
    private List<String> codes;
    /**
     * 类目名称（全路径）
     */
    private List<String> names;
}
