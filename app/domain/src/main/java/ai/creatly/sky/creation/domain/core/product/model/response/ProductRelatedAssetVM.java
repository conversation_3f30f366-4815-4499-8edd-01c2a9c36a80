/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.product.model.response;

import ai.creatly.sky.creation.domain.core.userasset.model.response.UserAssetVM;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @version ProductRelatedAssetVM.java, v 0.1 2024-10-18 下午3:18 zhoudong
 */
@Data
public class ProductRelatedAssetVM extends UserAssetVM {

    /**
     * 该素材是否关联了指定产品（指定产品通过查询条件传入）
     */
    @Nullable
    private Boolean productRelated;
}
