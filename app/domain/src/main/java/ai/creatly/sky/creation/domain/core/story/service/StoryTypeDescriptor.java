/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.service;

import ai.creatly.sky.creation.domain.core.story.model.story.StoryType;

import java.util.List;

/**
 * 故事类型加载器
 *
 * <AUTHOR>
 * @version StoryTypeDescriptor.java, v 0.1 2024-03-07 16:23 syoka
 */
public interface StoryTypeDescriptor {

    StoryType getStoryType(String storyType);

    List<StoryType> getAllStoryType();

}
