/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.service;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsRefund;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsService;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version DigHumanVideoCreditsService.java, v 0.1 2024-05-29 14:36 syoka
 */
@Deprecated
public interface DigHumanVideoCreditsService extends CreditsService {

    /**
     * 计算费用
     *
     * @param duration 时长
     * @return 费用
     */
    int calcCredits(Duration duration);

    /**
     * 数字人视频 计费
     *
     * @return -
     */
    CreditsExpense calcExpense(AiTask aiTask, UserFile audioFile);

    /**
     * 数字人视频 退款
     *
     * @param aiTask  ai任务类型
     * @return -
     */
    CreditsRefund buildRefund(AiTask aiTask);
}
