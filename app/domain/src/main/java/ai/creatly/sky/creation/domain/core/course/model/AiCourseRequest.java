/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.domain.core.course.model;


import lombok.Data;
import lombok.experimental.Accessors;


/**
 * AI课程
 */
@Data
@Accessors(chain = true)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiCourseRequest {

    /**
     * 课程ID
     */
    private Long          courseId;
    /**
     * 课件ID
     */
    private Long          contentId;
    /**
     * 播放到的秒数
     */
    private Integer          seconds;
}
