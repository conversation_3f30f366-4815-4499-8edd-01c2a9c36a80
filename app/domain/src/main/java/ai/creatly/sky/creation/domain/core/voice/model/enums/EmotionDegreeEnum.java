/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 情绪表现程度
 *
 * <AUTHOR>
 * @version EmotionDegreeEnum.java, v 0.1 2023-06-19 23:04 joton
 */
@Getter
@RequiredArgsConstructor
public enum EmotionDegreeEnum {

    MEDIUM("1"),
    STRONG("2"),
    SOFT("1.5"),
    WEEK("0.5"),
    ;

    /**
     * 取值范围：0.01 ~ 2
     */
    private final String value;

    public static EmotionDegreeEnum getByValue(String value) {
        for (EmotionDegreeEnum emotionDegreeEnum : EmotionDegreeEnum.values()) {
            if (emotionDegreeEnum.value.equals(value)) {
                return emotionDegreeEnum;
            }
        }
        return null;
    }
}
