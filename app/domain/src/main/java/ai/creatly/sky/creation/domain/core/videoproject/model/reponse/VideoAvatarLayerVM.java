/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.reponse;

import lombok.Data;

/**
 * <AUTHOR>
 * @version VideoAvatarLayerVM.java, v 0.1 2024-09-21 下午7:24 zhoudong
 */
@Data
public class VideoAvatarLayerVM {

    /**
     * 数字人形象ID
     */
    private String  avatarId;
    /**
     * 数字人形象图片地址
     */
    private String  avatarImageUrl;
    private Integer width;
    private Integer height;
    private Integer left;
    private Integer top;
    private Double  rotation;
}
