/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.tool;

import ai.creatly.sky.creation.domain.core.auth.model.UserContext;

import java.util.List;

/**
 * <AUTHOR>
 * @version AiRechargeRepository.java, v0.1 2025-02-19 21:17
 */
public interface AiRechargeRepository {

    AiRecharge getTemplateByCredit(Long credit);

    AiRecharge getRecharge(Long credit, UserContext context);

    AiRecharge getRechargeByOrderId(Long orderId, UserContext context);

    AiRecharge getRechargeByOrderId(Long orderId);

    List<AiRecharge> getRechargeList();

    AiRecharge update(AiRecharge aiRecharge);

    AiRecharge createRecord(AiRecharge aiRecharge);
}
