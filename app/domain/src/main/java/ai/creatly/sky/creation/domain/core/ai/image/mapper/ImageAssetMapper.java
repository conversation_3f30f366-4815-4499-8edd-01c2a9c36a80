/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.mapper;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.ai.image.model.OrderedImageAsset;
import ai.creatly.sky.creation.domain.core.ai.image.model.response.OrderedImageAssetVM;
import ai.creatly.sky.creation.domain.core.asset.model.OrderedAssetRef;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import lombok.Setter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version ImageAssetMapper.java, v0.1 2025-02-24 16:23
 */
@Mapper(config = BaseMapperConfig.class)
abstract class ImageAssetMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    public OrderedImageAssetVM toImageAssetVM(OrderedImageAsset imageAsset) {
        String coverUrl = userFileHelper.getHttpUrl(imageAsset.getCoverUrl(), FileAcl.PRIVATE);
        String imageUrl = userFileHelper.getHttpUrl(imageAsset.getImageUrl(), FileAcl.PRIVATE);
        return this.toImageAssetVM(imageAsset, coverUrl, imageUrl);
    }

    @Mapping(target = "coverUrl", source = "coverUrl")
    @Mapping(target = "imageUrl", source = "imageUrl")
    protected abstract OrderedImageAssetVM toImageAssetVM(OrderedImageAsset imageAsset, String coverUrl, String imageUrl);

    @Mapping(target = "imageUrl", source = "file.url")
    public abstract OrderedImageAsset toImageAsset(OrderedAssetRef assetRef);

    public abstract List<OrderedImageAsset> toImageAssets(List<OrderedAssetRef> assetRefs);
}
