/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.service.pay;

import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.PlanOrder;
import ai.creatly.sky.creation.domain.core.plan.model.specific.payment.PlanWxNativePrepayResult;

/**
 * 定价计划支付服务
 *
 * <AUTHOR>
 * @version PlanPayService.java, v 0.1 2023-10-19 下午4:34 zhoudong
 */
public interface PlanPayService {

    /**
     * 微信扫码支付
     *
     * @param plan          付费计划
     * @param payerClientIp 付款人终端设备IP
     * @param userContext   用户登录上下文
     * @return 微信扫码预支付结果
     */
    PlanWxNativePrepayResult doWxNativePay(Plan plan, String payerClientIp, UserContext userContext);

    /**
     * 付款成功后发货
     *
     * @param planOrder 已付费的订单
     */
    void shipOnPaid(PlanOrder planOrder);
}
