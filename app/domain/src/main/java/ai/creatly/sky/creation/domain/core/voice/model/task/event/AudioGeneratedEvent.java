/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.task.event;

import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * 音频生成事件
 *
 * <AUTHOR>
 * @version AudioGeneratedEvent.java, v 0.1 2023-11-06 下午2:54 zhoudong
 */
@Data
@Accessors(chain = true)
public class AudioGeneratedEvent {

    /**
     * 是否成功
     */
    private Boolean success;
    /**
     * 生成任务ID
     */
    private Long    taskId;
    /**
     * 取消原因（失败才有）
     */
    @Nullable
    private String  cancelReason;
    /**
     * 生成的音频文件ID（成功才有）
     */
    @Nullable
    private Long    audioId;
}
