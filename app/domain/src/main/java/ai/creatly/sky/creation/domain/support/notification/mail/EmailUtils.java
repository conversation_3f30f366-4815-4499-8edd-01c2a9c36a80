/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.notification.mail;

import lombok.experimental.UtilityClass;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version EmailUtils.java, v 0.1 2023-11-25 19:39 syoka
 */
@UtilityClass
public class EmailUtils {

    private static final String  regex   = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
    private              Pattern pattern = Pattern.compile(regex);

    public static boolean isEmail(String email) {
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }
}
