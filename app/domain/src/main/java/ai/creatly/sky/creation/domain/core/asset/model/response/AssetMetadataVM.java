/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.model.response;

import ai.creatly.sky.creation.domain.core.userfile.model.response.FileRefVM;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version AssetMetadataVM.java, v0.1 2025-03-03 18:40
 */
@Data
public class AssetMetadataVM {

    /**
     * 生成耗时
     */
    @Nullable
    private GenerationTimeVM generationTime;
    /**
     * 任务ID
     */
    @Nullable
    private String           taskId;
    /**
     * 视频项目ID
     */
    @Nullable
    private String           videoProjectId;
    /**
     * 原始提示词
     */
    @Nullable
    private String           inputPrompt;
    /**
     * 引用文件
     */
    @Nullable
    private List<FileRefVM>  attachments;
    /**
     * 最终用于生成的提示词
     */
    @Nullable
    private String           finalPrompt;
}
