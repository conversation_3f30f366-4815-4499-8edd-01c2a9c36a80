/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.model;

import ai.creatly.sky.creation.domain.core.user.model.organization.UserOrganization;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * creatly 用户模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class UserInfo {

    /**
     * 用户ID
     */
    private String              id;
    /**
     * 创建时间
     */
    private ZonedDateTime     createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime     updatedAt;
    /**
     * 用户状态
     */
    private UserStatus        status;
    /**
     * 用户名（全局唯一）
     */
    private String            username;
    /**
     * 用户别名
     */
    private String            nickname;
    /**
     * 手机（全局唯一）
     */
    private String            phone;
    /**
     * 密码
     */
    private String            password;
    /**
     * 用户邮箱（全局唯一）
     */
    @Nullable
    private String            email;
    /**
     * 头像地址（HTTP地址）
     */
    private String            avatar;
    /**
     * 当前组织信息
     */
    @Nullable
    private UserOrganization  activeOrganization;

    /**
     * 微信统一用户ID（微信用户在上海知行元主体的微信开放平台下的唯一ID，可以跨多个应用端之间统一）
     */
    @Nullable
    @Builder.Default
    private String            wechatUnionId = "";
    /**
     * 微信用户在不同应用端下的唯一标识
     */
    private Set<WechatOpenId> wechatOpenIds;
    /**
     * 邀请码
     */
    private String            inviteCode;
    /**
     * 用户角色列表
     */
    private List<UserRole>    roles;

    /**
     * 最后一次登录的组织
     */
    private String            lastOrgCode;

    private String            fullName;

    private String            idCard;


    public UserInfo addWechatOpenId(WechatOpenId wechatOpenId) {
        if (wechatOpenId == null) {
            return this;
        }
        if (this.wechatOpenIds == null) {
            this.wechatOpenIds = new HashSet<>();
        }
        this.wechatOpenIds.add(wechatOpenId);
        return this;
    }

    public boolean existsRole(UserRoleType roleType) {
        if (roleType == null || this.roles == null) {
            return false;
        }
        return this.roles.stream().anyMatch(role -> role.getType() == roleType);
    }
}
