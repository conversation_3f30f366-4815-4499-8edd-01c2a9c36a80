/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.auth.model.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 微信H5网页登录所需参数
 *
 * <AUTHOR>
 * @version WechatSignInPageParam.java, v 0.1 2024-04-02 14:21 syoka
 */
@Data
@Accessors(chain = true)
public class WechatSignInH5Param {

    /**
     * 自定义属性state
     */
    private String state;
    /**
     * 自定义标示会话id
     */
    private String sid;
    /**
     * 重定向路径(以/开头)
     */
    private String redirectPath;
}
