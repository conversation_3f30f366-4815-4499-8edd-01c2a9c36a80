/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.mapper;

import ai.creatly.sky.creation.domain.core.ai.image.model.AiImageTask;
import ai.creatly.sky.creation.domain.core.ai.image.model.response.AiImageTaskVM;
import ai.creatly.sky.creation.domain.core.ai.image.task.common.model.AiImageTaskResult;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 *
 * <AUTHOR>
 * @version AiImageTaskMapper.java, v0.1 2025-03-03 21:02
 */
@Mapper(config = BaseMapperConfig.class, uses = ImageAssetMapper.class)
public interface AiImageTaskMapper {

    default AiImageTask toAiImageTask(AiTask aiTask) {
        var result = aiTask.parseBizResult(AiImageTaskResult.class);
        return this.toAiImageTask(aiTask, result);
    }

    @Mapping(target = "taskId", source = "aiTask.id")
    AiImageTask toAiImageTask(AiTask aiTask, AiImageTaskResult result);

    AiImageTaskVM toAiImageTaskVM(AiImageTask aiImageTask);
}
