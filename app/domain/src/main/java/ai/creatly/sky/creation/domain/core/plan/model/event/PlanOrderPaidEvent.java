/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.event;

import ai.creatly.sky.creation.domain.core.plan.model.PlanOrder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @version PlanOrderPaidEvent.java, v 0.1 2023-11-07 下午8:49 zhoudong
 */
@Data
@Accessors(chain = true)
public class PlanOrderPaidEvent {
    /**
     * 定价计划订单
     */
    private PlanOrder order;
}
