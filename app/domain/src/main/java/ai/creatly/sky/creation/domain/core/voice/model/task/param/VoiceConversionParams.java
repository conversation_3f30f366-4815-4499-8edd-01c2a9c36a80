/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.task.param;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizParams;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version VoiceConvertParams.java, v 0.1 2023-12-02 下午9:45 zhoudong
 */
@Data
@Accessors(chain = true)
public class VoiceConversionParams implements TaskBizParams {

    /**
     * 输入音频文件ID
     */
    private Long     inputAudioId;
    /**
     * 输入音频文件地址（OSS地址）
     */
    private String   inputAudioUrl;
    /**
     * 输入音频时长
     */
    private Duration inputAudioDuration;
    /**
     * 变音耗时预估
     */
    private Duration estimatedConversionCost;
}
