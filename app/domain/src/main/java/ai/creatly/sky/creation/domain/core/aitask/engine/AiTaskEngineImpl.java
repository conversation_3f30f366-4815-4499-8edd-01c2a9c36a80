/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.engine;

import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.factory.AiTaskHandlerFactory;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskCancelAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskRollbackAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskStillAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreCancelAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.transaction.AiTaskSynchronization;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.transaction.AiTaskTransactionManager;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskConstant;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskExecInfo;
import ai.creatly.sky.creation.domain.core.aitask.model.UpdatableAiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.aitask.service.TaskAlertHelper;
import ai.creatly.sky.creation.domain.core.aitask.service.TaskNoticeHelper;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.net.Inets;
import jodd.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 任务引擎实现
 *
 * <AUTHOR>
 * @version AiTaskEngineImpl.java, v 0.1 2023-08-06 01:36 joton
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AiTaskEngineImpl implements AiTaskEngine {

    /**
     * 任务执行锁（单机锁） TODO 尽快加上redis分布式锁，不然本地调试和服务器调度可能会并发执行同一个任务
     */
    private static final ConcurrentHashMap<Long, Long> EXECUTING_TASK_LOCK = new ConcurrentHashMap<>();

    private final AiTaskRepository     aiTaskRepository;
    private final AiTaskHandlerFactory aiTaskHandlerFactory;
    private final TaskAlertHelper      taskAlertHelper;
    private final TaskNoticeHelper     taskNoticeHelper;
    private final TransactionTemplate  transactionTemplate;

    /**
     * 加任务执行锁
     *
     * @param taskId 任务ID
     * @return 加锁是否成功
     */
    private boolean lock(long taskId) {
        Long previousValue = EXECUTING_TASK_LOCK.putIfAbsent(taskId, System.currentTimeMillis());
        // 为空说明是第一次put，则表明加锁成功
        return previousValue == null;
    }

    /**
     * 释放任务执行锁
     *
     * @param taskId 任务ID
     */
    private void releaseLock(long taskId) {
        EXECUTING_TASK_LOCK.remove(taskId);
    }

    @Override
    public void execute(@NotNull AiTask aiTask) {
        long taskId = aiTask.getId();
        AiTaskType taskType = aiTask.getTaskType();
        String bizType = aiTask.getBizType();
        log.info("[execute]taskId={},taskType={},bizType={}", taskId, taskType, bizType);
        AiTaskHandler taskHandler = aiTaskHandlerFactory.getHandler(taskType, bizType);
        if (taskHandler == null) {
            log.warn("[execute][no_handler]taskId={},taskType={},bizType={}", taskId, taskType, bizType);
            return;
        }
        AiTaskConfig taskConfig = aiTaskHandlerFactory.getTaskConfig(taskType, bizType);

        // 加执行锁
        boolean lockSuccess = this.lock(taskId);
        if (!lockSuccess) {
            log.warn("[execute][lock_fail]taskId={},taskType={},bizType={}", taskId, taskType, bizType);
            return;
        }

        // 加锁成功后，再次查询任务，确保最新。在加锁期间内，任务不能被任何其他线程修改
        aiTask = aiTaskRepository.queryById(taskId);
        AiTaskExecInfo execInfo = aiTask.getExecInfo()
                .setExecTime(LocalDateTime.now(ZoneId.systemDefault()))
                .setExecHost(Inets.fetchLocalHostName())
                .incExecCount();

        // 执行次数告警
        Integer execCountAlertThreshold = taskConfig.getExecCountAlertThreshold();
        if (Objects.nonNull(execInfo.getExecCount())) {
            int overCount = execInfo.getExecCount() - execCountAlertThreshold;
            if (overCount >= 0 && overCount % execCountAlertThreshold == 0) {
                taskAlertHelper.alertOnExecTooManyTimes(aiTask);
            }
        }

        try {
            if (this.checkSysTimeout(aiTask)) {
                // 任务超时，直接取消
                UpdatableAiTask updatableAiTask = new UpdatableAiTask().setBizStatus(AiTaskConstant.BIZ_STATUS_TIMEOUT);
                aiTaskRepository.cancelById(taskId, "TIMEOUT", updatableAiTask, execInfo);
                // 查询最新的
                aiTask = aiTaskRepository.queryById(taskId);
            } else if (execInfo.getExecCount() > taskConfig.getExecTryLimit()) {
                // 任务超过最大允许的执行次数，直接取消
                UpdatableAiTask updatableAiTask = new UpdatableAiTask().setBizStatus(AiTaskConstant.BIZ_STATUS_TRYOUT);
                aiTaskRepository.cancelById(taskId, "TRYOUT", updatableAiTask, execInfo);
            }

            // 处理 CREATED 任务
            if (aiTask.getStatus() == AiTaskStatus.CREATED) {
                // 预处理
                try {
                    if (taskConfig.getQueueMode() == TaskQueueMode.POP_UNTIL_COMPLETED) {
                        // 提升优先级（这样可以确保已经开始执行的任务，在没有完结之前，永远被优先捞取起来，从而控制对下游调用的频次）
                        if (aiTask.getPriority() >= AiTask.DEFAULT_PRIORITY) {
                            aiTaskRepository.updatePriority(taskId, AiTask.DEFAULT_PRIORITY - 10);
                        }
                    }

                    // 初始化任务更新同步函数
                    AiTaskTransactionManager.initSynchronization();

                    TaskPreAction taskPreAction = taskHandler.preHandle(aiTask);
                    if (taskPreAction == null) {
                        taskPreAction = TaskPreAction.KEEP_STILL;
                    }
                    @Nullable UpdatableAiTask updatableAiTask = taskPreAction.getUpdatableAiTask();
                    if (taskPreAction.isKeepStill() && updatableAiTask == null && AiTaskTransactionManager.isSynchronizationEmpty()) {
                        // 没有更新task内容且没有同步函数，无效执行，减少数据库IO
                        log.warn("[execute][CREATED->KEEP_STILL]no changes,taskId={},taskType={},bizType={}", taskId, taskType, bizType);
                        return;
                    }
                    if (updatableAiTask == null) {
                        updatableAiTask = new UpdatableAiTask();
                    }
                    if (updatableAiTask.getStartedAt() == null) {
                        updatableAiTask.setStartedAt(ZonedDateTime.now());
                    }
                    switch (taskPreAction.getType()) {
                        // 状态不变
                        case KEEP_STILL -> this.dryRun(taskId, updatableAiTask, execInfo);
                        // 更新到 RUNNING
                        case FORWARD_RUNNING -> this.start(taskId, updatableAiTask, execInfo);
                        // 更新到 FINISHED
                        case FORWARD_FINISHED -> {
                            if (updatableAiTask.getFinishedAt() == null) {
                                updatableAiTask.setFinishedAt(ZonedDateTime.now());
                            }
                            this.finish(aiTask, updatableAiTask, execInfo);
                        }
                        // 更新到 CANCELED
                        case FORWARD_CANCELED -> {
                            TaskPreCancelAction taskPreCancelAction = (TaskPreCancelAction) taskPreAction;
                            this.cancel(taskId, taskPreCancelAction.getReason(), updatableAiTask, execInfo);
                        }
                        // 兜底
                        default -> throw new SysException("no such pre action: " + taskPreAction.getType());
                    }
                    if (updatableAiTask.getAutoExec() != null && !updatableAiTask.getAutoExec()) {
                        // 本轮主动关闭了自动调度执行，则不再继续执行后续阶段
                        return;
                    }
                } finally {
                    AiTaskTransactionManager.clearSynchronization();
                }
                // 查询最新的
                aiTask = aiTaskRepository.queryById(taskId);
            }

            // 处理 RUNNING 任务
            if (aiTask.getStatus() == AiTaskStatus.RUNNING) {
                try {
                    // 初始化任务更新同步函数
                    AiTaskTransactionManager.initSynchronization();

                    // 核心处理
                    TaskAction taskAction = taskHandler.handle(aiTask);
                    if (taskAction == null) {
                        taskAction = TaskAction.KEEP_STILL;
                    }
                    @Nullable UpdatableAiTask updatableAiTask = taskAction.getUpdatableAiTask();
                    switch (taskAction.getType()) {
                        // 状态不变
                        case KEEP_STILL -> {
                            TaskStillAction taskStillAction = (TaskStillAction) taskAction;
                            String retryName = taskStillAction.getRetryName();
                            if (updatableAiTask == null && AiTaskTransactionManager.getSynchronizations().isEmpty() && retryName == null) {
                                // 没有更新task内容且没有同步函数且无主动重试，则视为无效执行，减少数据库IO
                                log.warn("[execute][RUNNING->KEEP_STILL]no changes,taskId={},taskType={},bizType={}",
                                        taskId, taskType, bizType);
                                return;
                            }
                            if (retryName != null) {
                                // 更新重试统计数据，并发送告警通知
                                execInfo.incRetryCount(retryName, taskStillAction.getRetryReason());
                                taskAlertHelper.alertOnRetry(aiTask, execInfo, retryName);
                            }
                            this.dryRun(taskId, updatableAiTask, execInfo);
                            return;
                        }
                        // 更新到 FINISHED
                        case FORWARD_FINISHED -> {
                            if (updatableAiTask == null) {
                                updatableAiTask = new UpdatableAiTask();
                            }
                            if (updatableAiTask.getFinishedAt() == null) {
                                updatableAiTask.setFinishedAt(ZonedDateTime.now());
                            }
                            this.finish(aiTask, updatableAiTask, execInfo);
                        }
                        // 更新到 CANCELED
                        case FORWARD_CANCELED -> {
                            TaskCancelAction taskCancelAction = (TaskCancelAction) taskAction;
                            this.cancel(taskId, taskCancelAction.getReason(), updatableAiTask, execInfo);
                        }
                        // 回滚到最初状态
                        case ROLLBACK -> {
                            TaskRollbackAction taskRollbackAction = (TaskRollbackAction) taskAction;
                            if (execInfo.exceedMaxRollbackCount(taskConfig.getMaxRollbackCount())) {
                                // 超过最大回滚次数后，自动取消任务
                                if (updatableAiTask == null) {
                                    updatableAiTask = new UpdatableAiTask();
                                }
                                if (StringUtils.isBlank(taskRollbackAction.getReason())) {
                                    this.cancel(taskId, "exceed max rollback count", updatableAiTask, execInfo);
                                } else {
                                    this.cancel(taskId, taskRollbackAction.getReason(), updatableAiTask, execInfo);
                                }
                            } else {
                                execInfo.incRollbackCount()
                                        .setRollbackTime(LocalDateTime.now())
                                        .setRollbackReason(taskRollbackAction.getReason());
                                this.rollbackRunningToCreated(taskId, updatableAiTask, execInfo);
                                // 任务回滚告警
                                taskAlertHelper.alertOnRollback(aiTask, execInfo);
                                // 回滚后直接退出本次执行
                                return;
                            }
                        }
                        // 兜底
                        default -> throw new SysException("no such action: " + taskAction.getType());
                    }
                    if (updatableAiTask != null && updatableAiTask.getAutoExec() != null && !updatableAiTask.getAutoExec()) {
                        // 本轮主动关闭了自动调度执行，则不再继续执行后续阶段
                        return;
                    }
                } finally {
                    AiTaskTransactionManager.clearSynchronization();
                }
                // 查询最新的
                aiTask = aiTaskRepository.queryById(taskId);
            }

            // 处理 FINISHED/CANCELED 任务
            if (aiTask.getStatus().isFinal()) {
                try {
                    // 初始化任务更新同步函数
                    AiTaskTransactionManager.initSynchronization();

                    // 容错处理：如果任务状态已经是完成了，但没有完成时间，则先设置完成时间
                    if (aiTask.getStatus() == AiTaskStatus.FINISHED && aiTask.getFinishedAt() == null) {
                        this.finish(aiTask, null, execInfo);
                        // 查询最新的
                        aiTask = aiTaskRepository.queryById(taskId);
                    }

                    // 后置处理
                    TaskPostAction taskPostAction = taskHandler.postHandle(aiTask);
                    if (taskPostAction == null) {
                        taskPostAction = TaskPostAction.COMPLETE;
                    }
                    @Nullable UpdatableAiTask updatableAiTask = taskPostAction.getUpdatableAiTask();
                    switch (taskPostAction.getType()) {
                        // 状态不变
                        case KEEP_STILL -> {
                            if (updatableAiTask == null && AiTaskTransactionManager.getSynchronizations().isEmpty()) {
                                // 没有更新task内容且没有同步函数，无效执行，减少数据库IO
                                log.warn("[execute][FINAL->KEEP_STILL]no changes,taskId={},taskType={},bizType={}",
                                        taskId, taskType, bizType);
                                return;
                            }
                            this.dryRun(taskId, updatableAiTask, execInfo);
                        }
                        // 完结任务
                        case COMPLETE -> {
                            this.complete(aiTask, updatableAiTask, execInfo, taskConfig);
                            // 任务完结后，针对某些情况，进行告警
                            if (aiTask.getStatus() == AiTaskStatus.CANCELED) {
                                // 任务取消告警
                                if (updatableAiTask != null) {
                                    // 查询最新的
                                    aiTask = aiTaskRepository.queryById(taskId);
                                }
                                taskAlertHelper.alertOnCanceled(aiTask);
                            }
                            if (taskAlertHelper.isFinishedWithSelfRecovery(aiTask)) {
                                // 任务自愈恢复告警（存在错误次数 或 存在回滚次数）
                                if (updatableAiTask != null) {
                                    // 查询最新的
                                    aiTask = aiTaskRepository.queryById(taskId);
                                }
                                taskAlertHelper.alertOnFinishedWithSelfRecovery(aiTask);
                            }
                        }
                        // 兜底
                        default -> throw new SysException("no such post action: " + taskPostAction.getType());
                    }
                } finally {
                    AiTaskTransactionManager.clearSynchronization();
                }
            }
        } catch (Exception e) {
            log.error("[execute][error]taskId={},taskType={},bizType={}", taskId, taskType, bizType, e);
            // 截取错误信息
            execInfo.incErrorCount()
                    .setErrorTime(LocalDateTime.now())
                    .setErrorMsg(ExceptionUtil.exceptionStackTraceToString(e));
            aiTaskRepository.dryRunById(taskId, null, execInfo);

            // 告警
            taskAlertHelper.alertOnException(aiTask, e);
        } finally {
            // 释放执行锁
            this.releaseLock(taskId);
            log.info("[execute][lock_released]taskId={},taskType={},bizType={}", taskId, taskType, bizType);
        }
    }

    /**
     * 检查任务是否超时
     *
     * @param aiTask 任务
     * @return 是否超时
     */
    private boolean checkSysTimeout(AiTask aiTask) {
        ZonedDateTime now = ZonedDateTime.now();
        {
            Duration timeout = aiTask.getSysParams().getTimeoutFromCreatedAt();
            if (timeout != null && Duration.between(aiTask.getCreatedAt(), now).compareTo(timeout) > 0) {
                return true;
            }
        }
        {
            Duration timeout = aiTask.getSysParams().getTimeoutFromStartedAt();
            if (timeout != null && aiTask.getStartedAt() != null && Duration.between(aiTask.getStartedAt(), now).compareTo(timeout) > 0) {
                return true;
            }
        }
        // 全局兜底：3天
        Duration timeout = Duration.ofDays(3);
        return Duration.between(aiTask.getCreatedAt(), now).compareTo(timeout) > 0;
    }

    /**
     * 任务空跑（不推进主状态）
     *
     * @param taskId          任务ID
     * @param updatableAiTask 任务更新数据
     * @param execInfo        任务执行数据
     */
    private void dryRun(long taskId, @Nullable UpdatableAiTask updatableAiTask, AiTaskExecInfo execInfo) {
        List<AiTaskSynchronization> synchronizations = AiTaskTransactionManager.getSynchronizations();
        if (synchronizations.isEmpty()) {
            aiTaskRepository.dryRunById(taskId, updatableAiTask, execInfo);
        } else {
            transactionTemplate.executeWithoutResult(status -> {
                // 执行业务的同步回调函数
                AiTaskTransactionManager.getSynchronizations().forEach(AiTaskSynchronization::inCommit);
                aiTaskRepository.dryRunById(taskId, updatableAiTask, execInfo);
            });
        }
    }

    /**
     * 开始任务
     *
     * @param taskId          任务ID
     * @param updatableAiTask 任务更新数据
     * @param execInfo        任务执行数据
     */
    private void start(long taskId, @Nullable UpdatableAiTask updatableAiTask, AiTaskExecInfo execInfo) {
        List<AiTaskSynchronization> synchronizations = AiTaskTransactionManager.getSynchronizations();
        if (synchronizations.isEmpty()) {
            aiTaskRepository.startById(taskId, updatableAiTask, execInfo);
        } else {
            transactionTemplate.executeWithoutResult(status -> {
                // 执行业务的同步回调函数
                AiTaskTransactionManager.getSynchronizations().forEach(AiTaskSynchronization::inCommit);
                aiTaskRepository.startById(taskId, updatableAiTask, execInfo);
            });
        }
    }

    /**
     * 回滚任务，从 RUNNING 到 CREATED
     *
     * @param taskId          任务ID
     * @param updatableAiTask 任务更新数据
     * @param execInfo        任务执行数据
     */
    private void rollbackRunningToCreated(long taskId, @Nullable UpdatableAiTask updatableAiTask, AiTaskExecInfo execInfo) {
        List<AiTaskSynchronization> synchronizations = AiTaskTransactionManager.getSynchronizations();
        if (synchronizations.isEmpty()) {
            aiTaskRepository.rollbackRunningToCreatedById(taskId, updatableAiTask, execInfo);
        } else {
            transactionTemplate.executeWithoutResult(status -> {
                // 执行业务的同步回调函数
                AiTaskTransactionManager.getSynchronizations().forEach(AiTaskSynchronization::inCommit);
                aiTaskRepository.rollbackRunningToCreatedById(taskId, updatableAiTask, execInfo);
            });
        }
    }

    /**
     * 完成任务
     *
     * @param aiTask          任务
     * @param updatableAiTask 任务更新数据
     * @param execInfo        任务执行数据
     */
    private void finish(AiTask aiTask, @Nullable UpdatableAiTask updatableAiTask, AiTaskExecInfo execInfo) {
        if (execInfo.getProgress() != null && execInfo.getProgress() < 100) {
            execInfo.setProgress(100);
        }

        List<AiTaskSynchronization> synchronizations = AiTaskTransactionManager.getSynchronizations();
        if (synchronizations.isEmpty()) {
            aiTaskRepository.finishById(aiTask.getId(), aiTask.getCreatedAt(), updatableAiTask, execInfo);
        } else {
            transactionTemplate.executeWithoutResult(status -> {
                // 执行业务的同步回调函数
                AiTaskTransactionManager.getSynchronizations().forEach(AiTaskSynchronization::inCommit);
                aiTaskRepository.finishById(aiTask.getId(), aiTask.getCreatedAt(), updatableAiTask, execInfo);
            });
        }
    }

    /**
     * 取消任务
     *
     * @param taskId          任务ID
     * @param reason          取消原因
     * @param updatableAiTask 任务更新数据
     * @param execInfo        任务执行数据
     */
    private void cancel(long taskId, String reason, @Nullable UpdatableAiTask updatableAiTask, AiTaskExecInfo execInfo) {
        String finalReason = StringUtils.defaultIfBlank(reason, "unknown");
        List<AiTaskSynchronization> synchronizations = AiTaskTransactionManager.getSynchronizations();
        if (synchronizations.isEmpty()) {
            aiTaskRepository.cancelById(taskId, finalReason, updatableAiTask, execInfo);
        } else {
            transactionTemplate.executeWithoutResult(status -> {
                // 执行业务的同步回调函数
                AiTaskTransactionManager.getSynchronizations().forEach(AiTaskSynchronization::inCommit);
                aiTaskRepository.cancelById(taskId, finalReason, updatableAiTask, execInfo);
            });
        }
    }

    /**
     * 完结任务调度
     *
     * @param aiTask          任务实例
     * @param updatableAiTask 任务更新数据
     * @param execInfo        任务执行数据
     */
    private void complete(AiTask aiTask, @Nullable UpdatableAiTask updatableAiTask, AiTaskExecInfo execInfo, AiTaskConfig taskConfig) {
        List<AiTaskSynchronization> synchronizations = AiTaskTransactionManager.getSynchronizations();
        transactionTemplate.executeWithoutResult(status -> {
            if (!synchronizations.isEmpty()) {
                // 执行业务的同步回调函数
                AiTaskTransactionManager.getSynchronizations().forEach(AiTaskSynchronization::inCommit);
            }
            aiTaskRepository.completeById(aiTask.getId(), updatableAiTask, execInfo);
            // 任务完成通知
            if (taskConfig.getNotifyUserOnCompleted()) {
                taskNoticeHelper.noticeOnComplete(aiTask);
            }
        });
    }
}
