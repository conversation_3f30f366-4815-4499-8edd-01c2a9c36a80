/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.feedback.repository;

import ai.creatly.sky.creation.domain.core.feedback.model.Feedback;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.exception.SysException;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version : FeedbackRepository.java, v 1.0 2023年12月09日 20时55分 heb
 */
public interface FeedbackRepository {

    /**
     * 创建用户反馈
     *
     * @param feedback 创建用户反馈
     * @return -
     */
    long create(Feedback feedback);

    default Feedback queryById(long id) {
        return this.queryOptionalById(id).orElseThrow(() -> new SysException("user feedback not found!"));
    }

    Optional<Feedback> queryOptionalById(long id);

    /**
     * 分页获取反馈信息列表
     *
     * @param keyword -
     * @param pageable -
     * @return -
     */
    Page<Feedback> queryPage(String keyword, Pageable pageable);
}
