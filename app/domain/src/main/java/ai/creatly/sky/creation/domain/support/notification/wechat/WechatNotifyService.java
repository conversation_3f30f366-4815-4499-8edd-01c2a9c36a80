/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.notification.wechat;

import ai.creatly.sky.creation.domain.core.auth.model.WechatAppName;
import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import ai.creatly.sky.creation.domain.core.user.model.WechatOpenId;
import ai.creatly.sky.creation.domain.core.user.repository.UserRepository;
import ai.creatly.sky.creation.domain.support.wechat.mp.WechatMpClient;
import ai.creatly.sky.creation.domain.support.wechat.mp.model.message.send.MpMessageOption;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version WechatNotifyService.java, 2024-11-02 下午9:57 zhoudong
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WechatNotifyService {

    private final WechatMpClient         wechatMpClient;
    private final UserRepository         userRepository;
    private final WechatNotifyProperties wechatNotifyProperties;

    public void sendMessage(long uid, String templateId, Map<String, String> params, MpMessageOption messageOption) {
        WechatAppName appName = wechatNotifyProperties.getAppName();
        UserInfo userInfo = userRepository.queryById(uid);
        String openId = userInfo.getWechatOpenIds()
                .stream()
                .filter(wechatOpenId -> wechatOpenId.getAppName().equals(appName.name()))
                .findFirst()
                .map(WechatOpenId::getOpenId)
                .orElse(null);
        if (openId == null) {
            log.warn("用户 {} 未关注 {}，无法发送公众号消息", uid, appName.getDesc());
            return;
        }
        wechatMpClient.sendMessage(openId, templateId, params, messageOption, appName);
    }
}
