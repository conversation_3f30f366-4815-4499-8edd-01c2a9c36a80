/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.admin.model;

import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskExecStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @version TaskUpdateRequest.java, v 0.1 2023-10-19 16:56 syoka
 */
@Data
public class TaskUpdateRequest {

    private ZonedDateTime    startedAt;
    private ZonedDateTime    delayedAt;
    private AiTaskStatus     status;
    private String           bizStatus;
    private String           bizParams;
    private String           bizExecInfo;
    private String           bizResult;
    private AiTaskExecStatus execStatus;
    private Boolean          autoExec;
    private Integer          priority;
}
