/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.reponse;

import ai.creatly.sky.creation.domain.core.voice.model.enums.EmotionDegreeEnum;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechPitchType;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechRateType;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechVolumeType;
import lombok.Data;

/**
 * <AUTHOR>
 * @version VideoVoiceoverVM.java, v 0.1 2024-09-21 下午7:26 zhoudong
 */
@Data
public class VideoVoiceoverVM {
    /**
     * 声音ID
     */
    private Long              voiceId;
    /**
     * 口音编号
     */
    private String            localeCode;
    /**
     * 声音情绪（通过声音列表API获取）
     */
    private String            emotionCode;
    /**
     * 情绪表现程度（默认中）
     */
    private EmotionDegreeEnum emotionDegree;
    /**
     * 调节音调
     */
    private SpeechPitchType   pitch;
    /**
     * 调节语速
     */
    private SpeechRateType    rate;
    /**
     * 调节音量
     */
    private SpeechVolumeType  volume;
}
