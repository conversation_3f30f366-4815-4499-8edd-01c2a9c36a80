/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 微信扫码支付定价计划返回结果
 *
 * <AUTHOR>
 * @version PlanWxNativePayResultVM.java, v 0.1 2023-10-08 下午10:10 zhoudong
 */
@Data
@Accessors(chain = true)
public class PlanWxNativePayResultVM {

    /**
     * 订单ID（用于获取支付结果）
     */
    private String        orderId;
    /**
     * 预支付跳转链接（注意：不是二维码图片地址，而是二维码里的内容）
     */
    private String        codeUrl;
    /**
     * 收银台过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime expireAt;
}
