/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.support.prompt.repository;

import ai.creatly.sky.creation.domain.support.prompt.model.PromptTag;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @version PromptTagRepository.java, v 0.1 2023-10-28 16:54 syoka
 */
public interface PromptTagRepository {


    /**
     * 通过code列表查询提示词tag
     *
     * @param code
     * @return
     */
    List<PromptTag> findByCodes(List<String> code);

    /**
     * 基于提示词tag的分页查询
     *
     * @param pageable 分页参数
     * @param code tag码
     * @param name tag名
     * @return
     */
    Page<PromptTag> findPageablePromptTag(Pageable pageable, String code, String name);

    /**
     * 创建提示词标签
     *
     * @param code
     * @param name
     * @param enName
     * @param color
     * @return
     */
    String createPromptTag(String code, String name, String enName, String color);

    /**
     * 删除提示词标签
     *
     * @param code
     */
    void deletePromptTag(String code);
}
