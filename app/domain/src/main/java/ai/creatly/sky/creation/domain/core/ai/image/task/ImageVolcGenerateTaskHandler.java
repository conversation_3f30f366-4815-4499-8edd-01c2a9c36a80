/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.task;

import ai.creatly.sky.creation.domain.core.ai.image.AiImageGenerateClient;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskBizType;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskCommonInput;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskCommonResult;
import ai.creatly.sky.creation.domain.core.ai.model.OrderedAsset;
import ai.creatly.sky.creation.domain.core.ai.video.model.AiVideoTaskVars;
import ai.creatly.sky.creation.domain.core.ai.video.model.VideoGenerateTaskInput;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.transaction.AiTaskTransactionManager;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskBizExecStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskHelper;
import ai.creatly.sky.creation.domain.core.asset.model.Asset;
import ai.creatly.sky.creation.domain.core.asset.model.AssetFile;
import ai.creatly.sky.creation.domain.core.asset.model.AssetMetadata;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetBizType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetSourceType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetStatus;
import ai.creatly.sky.creation.domain.core.asset.service.AssetRepository;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsRefund;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.userasset.model.AssetContent;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.ImageFileService;
import com.jspeeder.core.data.id.IdHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version ImageGenerateTaskHandler.java, v0.1 2025-02-28 13:41
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ImageVolcGenerateTaskHandler implements AiTaskHandler {

    private final AiImageGenerateClient imageGenerateClient;

    @Autowired
    protected ImageFileService        imageFileService;
    @Autowired
    protected AiTaskHelper            aiTaskHelper;
    @Autowired
    protected UserFileRepository      userFileRepository;
    @Autowired
    protected UserCreditDomainService userCreditDomainService;
    @Autowired
    private   AssetRepository         assetRepository;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.AI_IMAGE)
                .setBizType(AiTaskBizType.image_volc_generate)
                .setLoadSize(9)
                .setQueueMode(TaskQueueMode.POP_PER_EXECUTED)
                .setExecCountAlertThreshold(Integer.MAX_VALUE)
                .setNotifyOnSubmit(false)
                .setNotifyUserOnCompleted(false);
    }

//    private CreditsExpense expense(AiTask aiTask) {
//        Integer cost = imageGenerateClient.expense(aiTask);
//        return CreditsExpense.builder()
//                .uid(aiTask.getOwnerId())
//                .type(CreditAccountType.GENERAL)
//                .amount(cost)
//                // 完成一条创作任务，扣除一次费用
//                .bizType(CreditLogBizType.GEN_IMAGE)
//                .bizNo(aiTask.getId().toString())
//                .build();
//    }

    @Override
    public TaskPreAction preHandle(AiTask aiTask) {
        //执行扣费，扣费下放到不同的实现层
        var input = aiTask.parseBizInput(AiTaskCommonInput.class);
        UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);

//        CreditsExpense expense = this.expense(aiTask);
//        // 余额不足，直接取消任务
//        if (!userCreditDomainService.consumeCredits(expense)) {
//            log.info("用户元气不足，uid={},taskId={}", userContext.getUid(), aiTask.getId());
//            return TaskPreAction.FORWARD_CANCELED.withReason("元气不足");
//        }

        AiTaskCommonResult bizResult= imageGenerateClient.generate(input, userContext);
        var taskVars = new AiVideoTaskVars().setData(bizResult.getData()).setTaskId(bizResult.getTaskId()).setErrorMsg(bizResult.getErrorMsg());
        if (bizResult.getBizExecStatus() == AiTaskBizExecStatus.FAILED) {
            return TaskPreAction.FORWARD_CANCELED.updateBizVars(taskVars);
        }
        return TaskPreAction.FORWARD_RUNNING.updateBizVars(taskVars);
    }

    @Override
    public TaskAction handle(AiTask aiTask) {
        try {
            // 1、查询生成状态
            UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);
            var input = aiTask.parseBizInput(VideoGenerateTaskInput.class);
            AiTaskCommonResult  result = imageGenerateClient.queryGeneration(aiTask, userContext);

            // 2、生成中/失败直接结束
            if (result.getBizExecStatus() == AiTaskBizExecStatus.PROCESSING) {
                return TaskAction.KEEP_STILL.updateBizResult(result);
            } else if(result.getBizExecStatus() == AiTaskBizExecStatus.FAILED){
                return TaskAction.FORWARD_CANCELED.updateBizResult(result);
            }

            // 3、生成成功，下载转存生成的视频资源，保存为用户资产
            List<OrderedAsset> assetList = result.getAssets();
            List<UserFile> userFiles = assetList.stream().map(x -> {
                FileBizSource bizSource = FileBizSource.AI_IMAGE;
                UserFile userFile;
                if(x.getUserFile()!=null){
                    userFile = x.getUserFile();
                }else {
                    userFile = imageFileService.upload(x.getUrl(), IdHelper.getId(), bizSource, userContext);
                }
                x.setUrl(userFile.getOssUrl());
                x.setCoverUrl(imageFileService.generateThumbnailUrl(userFile,50));
                return userFile;
            }).toList();

            List<Asset> assets = userFiles.stream().map(x -> this.buildAsset(input, x)).toList();

            AiTaskTransactionManager.registerSynchronization(() -> {
                userFileRepository.batchCreate(userFiles);
                assetRepository.batchCreate(assets);
            });
            return TaskAction.FORWARD_FINISHED.updateBizResult(result);
        }catch (Exception e){
            log.error("图片生成异常",e);
            return TaskAction.FORWARD_CANCELED;
        }
    }

    @Override
    public TaskPostAction postHandle(AiTask aiTask) {
        //任务取消则消耗元气恢复
        if (aiTask.getStatus() == AiTaskStatus.CANCELED) {
            CreditsRefund creditsRefund = CreditsRefund.builder()
                    .uid(aiTask.getOwnerId())
                    .bizType(CreditLogBizType.GEN_IMAGE)
                    .bizNo(aiTask.getId().toString())
                    .allowExpenseAbsent(false)
                    .build();
            userCreditDomainService.refundCredits(creditsRefund);
        }
        return TaskPostAction.COMPLETE;
    }

    private Asset buildAsset(VideoGenerateTaskInput taskInput, UserFile userFile) {
        var metadata = new AssetMetadata()
                .setInputPrompt(taskInput.getRequest().getPromptText());

        return new Asset()
                .setId(IdHelper.getId())
                .setUid(userFile.getUid())
                .setStatus(AssetStatus.VALID)
                .setName(userFile.getOriginalFilename())
                .setSourceType(AssetSourceType.generation)
                .setBizType(AssetBizType.image_generation)
                .setCoverUrl(userFile.getOssUrl())
                .setFile(AssetFile.fromUserFile(userFile))
                .setContent(new AssetContent().setMd5(userFile.getMd5()))
                .setMetadata(metadata)
                .setTags(new ArrayList<>());
    }
}
