/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.product.model;

import ai.creatly.sky.creation.domain.core.product.model.enums.ProductAssetType;
import ai.creatly.sky.creation.domain.core.userasset.model.UserAsset;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version ProductAsset.java, v 0.1 2024-10-16 下午3:36 zhoudong
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(of = {"assetId", "type"})
public class ProductAsset {

    private Long             assetId;
    private ProductAssetType type;
    /*-------------关联属性---------------*/
    @JsonIgnore
    private UserAsset        asset;
}
