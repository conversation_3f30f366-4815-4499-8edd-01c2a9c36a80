/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.domain.core.course.model;


import ai.creatly.sky.creation.domain.core.ai.exam.model.LongToStringSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;


/**
 * AI课程
 */
@Data
@Accessors(chain = true)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiCourse{
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long          id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String        category;
    private String        coverUrl;
    private String        coverHttpUrl;
    private String        courseIntro;
    private String        courseStatus;
    private String        courseType;
    private String        courseTitle;
    private String        videoUrl;
    private String        videoHttpUrl;
    private String        courseSubtitle;
    private Long          realPrice;
    private Long          linePrice;
    private String        duration;
}
