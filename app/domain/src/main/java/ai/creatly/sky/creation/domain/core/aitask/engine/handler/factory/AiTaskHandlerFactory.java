/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.engine.handler.factory;

import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import org.jetbrains.annotations.Nullable;

import java.util.Set;

/**
 * <AUTHOR>
 * @version AiTaskHandlerFactory.java, v 0.1 2023-07-29 00:38 joton
 */
public interface AiTaskHandlerFactory {

    Set<AiTaskConfig> getAllTaskConfigs();

    AiTaskConfig getTaskConfig(AiTaskType taskType, String bizType);

    @Nullable
    AiTaskHandler getHandler(AiTaskType taskType, String bizType);
}
