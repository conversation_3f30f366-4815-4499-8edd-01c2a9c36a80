/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.request.product;

import ai.creatly.sky.creation.domain.core.videoproject.model.enums.VideoAspectRatio;
import ai.creatly.sky.creation.domain.core.videoproject.model.request.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 商品视频项目全量保存请求
 *
 * <AUTHOR>
 * @version VideoProjectSaveRequest.java, v 0.1 2024-09-21 下午6:42 zhoudong
 */
@Data
public class ProductVideoProjectSaveRequest {

    /**
     * 项目名称
     */
    @Size(min = 1, max = 100)
    private String                  name;
    /**
     * 视频宽高比
     */
    private VideoAspectRatio        aspectRatio;
    /**
     * 带货产品ID（如果是从商品库里选择的，则必须传进来）
     */
    @Digits(integer = 20, fraction = 0)
    private String                  productId;
    /**
     * 带货产品信息（如果无产品ID，则会自动创建该产品；如果有产品ID，则会更新该产品）
     */
    @Valid
    private VideoProductDTO         product;
    /**
     * 视频脚本
     */
    @Valid
    private VideoScriptDTO          script;
    /**
     * 数字人视频层（传入为空，则会更新为空）
     */
    @Valid
    private VideoAvatarLayerDTO     avatarLayer;
    /**
     * 背景层（传入为空，则会更新为空）
     */
    @Valid
    private VideoBackgroundLayerDTO backgroundLayer;
    /**
     * 配音（传入为空，则会更新为空）
     */
    @Valid
    private VideoVoiceoverDTO       voiceover;
    /**
     * 字幕层（传入为空，则会更新为空）
     */
    @Valid
    private VideoSubtitleLayerDTO   subtitleLayer;

    @Deprecated
    public void setDimensionType(String dimensionType) {
        if (dimensionType != null) {
            switch (dimensionType) {
                case "vertical" -> this.aspectRatio = VideoAspectRatio._9_16;
                case "horizontal" -> this.aspectRatio = VideoAspectRatio._16_9;
            }
        }
    }
}
