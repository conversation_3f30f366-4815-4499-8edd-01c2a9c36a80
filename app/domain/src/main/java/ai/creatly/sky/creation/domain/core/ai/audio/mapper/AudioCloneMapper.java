/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.audio.mapper;

import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioTtsVM;
import ai.creatly.sky.creation.domain.core.ai.video.model.AiVideoTaskResult;
import ai.creatly.sky.creation.domain.core.ai.video.model.VideoGenerateTaskInput;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.userfile.model.response.FileRefVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version VideoGenerationMapper.java, v0.1 2025-02-19 21:38
 */
@Mapper(config = BaseMapperConfig.class)
public interface AudioCloneMapper {

    default AudioTtsVM toAudioCloneVM(AiTask aiTask) {
        var taskInput = aiTask.parseBizInput(VideoGenerateTaskInput.class);
        var taskResult = aiTask.parseBizResult(AiVideoTaskResult.class);

        AudioTtsVM audioTtsVM = new AudioTtsVM();

        FileRefVM photo = new FileRefVM();
        photo.setUrl("https://creatly-online.oss-accelerate.aliyuncs.com…reation/system/tts_avatar/%E6%99%93%E8%BE%B0.jpeg");
        audioTtsVM.setPhotoFile(photo);
        FileRefVM audio = new FileRefVM();
        audio.setUrl("https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/talking_photo/synthesis_voice/%E6%99%93%E6%B6%B5.mpga");
        audioTtsVM.setAudioFile(audio);
        audioTtsVM.setStatus(AiTaskStatus.FINISHED);
        audioTtsVM.setTaskId(aiTask.getId().toString());
        return audioTtsVM;
    }
}
