/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.label.mapper;

import ai.creatly.sky.creation.domain.support.label.model.Label;
import ai.creatly.sky.creation.domain.support.label.model.LabelRef;
import ai.creatly.sky.creation.domain.support.label.model.request.LabelCreateRequest;
import ai.creatly.sky.creation.domain.support.label.model.request.LabelRefDTO;
import ai.creatly.sky.creation.domain.support.label.model.response.LabelVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 标签模型转换
 *
 * <AUTHOR>
 * @version LabelMapper.java, v 0.1 2023-04-02 23:16 joton
 */
@Mapper(config = BaseMapperConfig.class)
public interface LabelMapper {

    @Mapping(target = "id", ignore = true)
    LabelRef toLabelRef(LabelRefDTO labelRefDTO);

    LabelVM toLabelVM(Label label);

    @Mapping(target = "usedCount", constant = "0")
    @Mapping(target = "status", constant = "VALID")
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "id", ignore = true)
    Label toCreateModel(long uid, LabelCreateRequest request);
}
