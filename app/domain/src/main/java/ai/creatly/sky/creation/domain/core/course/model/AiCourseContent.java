/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.domain.core.course.model;


import ai.creatly.sky.creation.domain.core.ai.exam.model.LongToStringSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.jspeeder.core.data.id.AutoIncrement;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;


/**
 * AI课程
 */
@Data
@Accessors(chain = true)
@SuppressWarnings({"all", "unchecked", "rawtypes", "this-escape"})
public class AiCourseContent {

    /**
     * 主键ID
     */
    @AutoIncrement
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long          id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long          courseId;
    private String        category;
    private String        title;
    private String        videoUrl;
    private String        videoHttpUrl;
    private Long          priority;
    private String        duration;
    private String        content;
    private Boolean       isFree;
}
