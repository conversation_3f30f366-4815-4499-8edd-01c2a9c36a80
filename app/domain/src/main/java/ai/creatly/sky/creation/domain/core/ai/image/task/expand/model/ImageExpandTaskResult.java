/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.task.expand.model;

import ai.creatly.sky.creation.domain.core.ai.image.task.common.model.AiImageTaskResult;
import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizResult;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version ImageExpandTaskResult.java, v0.1 2025-02-24 15:52
 */
@Data
public class ImageExpandTaskResult extends AiImageTaskResult implements TaskBizResult {

}
