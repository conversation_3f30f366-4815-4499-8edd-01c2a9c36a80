/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.workspace.model.request;

import ai.creatly.sky.creation.domain.deprecated.workspace.model.WorkspaceType;
import lombok.Data;

/**
 * 创建用户工作空间请求
 *
 * <AUTHOR>
 * @version WorkspaceCreateRequest.java, v 0.1 2023-09-23 15:19 syoka
 */
@Data
public class WorkspaceCreateRequest {

    /**
     * 创作模版
     */
    private String templateId;

    /**
     * 是否是空模版
     */
    private Boolean empty;

    /**
     * 工作空间类型
     */
    private WorkspaceType workspaceType;

}
