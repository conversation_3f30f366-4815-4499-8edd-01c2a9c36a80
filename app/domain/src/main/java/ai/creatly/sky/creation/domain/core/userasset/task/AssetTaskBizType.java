/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userasset.task;

import com.jspeeder.core.data.enums.ICode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version AssetTaskBizType.java, 2024-11-02 下午6:34 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum AssetTaskBizType implements ICode {

    preprocess("预处理"),
    video_slice_preprocess("视频切片预处理"),
    ;

    private final String desc;

    @Override
    public String getCode() {
        return this.name();
    }
}
