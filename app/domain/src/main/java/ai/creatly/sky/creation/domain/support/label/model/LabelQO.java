/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.label.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 标签查询参数
 *
 * <AUTHOR>
 * @version LabelQO.java, v 0.1 2023-04-02 23:30 joton
 */
@Data
@Accessors(chain = true)
public class LabelQO {

    /**
     * 标签名称
     */
    private String name;
}
