/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.oss;

import com.jspeeder.core.util.Asserts;
import org.jetbrains.annotations.Nullable;
import org.json.JSONObject;

import javax.annotation.WillClose;
import javax.annotation.WillNotClose;
import java.io.InputStream;
import java.util.Map;

/**
 * OSS常用操作
 *
 * <AUTHOR>
 * @version OssTemplate.java, v 0.1 2023-05-07 15:46 joton
 */
public interface OssTemplate {

    /**
     * 获取存储空间所在地域
     *
     * @param bucket 存储空间
     * @return 地域
     */
    String getBucketRegion(String bucket);

    /**
     * 列举目录
     *
     * @param bucket      存储空间
     * @param prefix      公共前缀（以/结尾）
     * @param previousDir 本地列举目录的起点（不包含）
     * @param limit       列举目录的最大个数
     * @return 目录列表结果
     */
    DirListResult listDirs(String bucket, String prefix, String previousDir, int limit);

    /**
     * 上传文件
     *
     * @param bucket      存储空间
     * @param key         文件对象key
     * @param acl         文件访问控制
     * @param inputStream 文件输入流
     * @return OSS协议的文件地址
     */
    default String upload(String bucket, String key, FileAcl acl, @WillClose InputStream inputStream) {
        return this.upload(bucket, key, acl, inputStream, null);
    }

    /**
     * 上传文件
     * </p>
     * 注意：⚠️⚠️用户自定义元数据的key只能包含数字、英文字母（大小写）和中划线。该限制的官方出处来自OSS网页控制台：文件详情 -> 设置文件元数据 -> 添加自定义元数据
     *
     * @param bucket       存储空间
     * @param key          文件对象key
     * @param acl          文件访问控制
     * @param inputStream  文件输入流
     * @param userMetadata 自定义元数据
     * @return OSS协议的文件地址
     */
    String upload(String bucket, String key, FileAcl acl, @WillClose InputStream inputStream, @Nullable Map<String, String> userMetadata);

    /**
     * 下载文件
     *
     * @param ossUrl OSS文件地址（oss://bucket/key）
     * @return 文件流（注意使用的地方要关流⚠️）
     */
    @WillNotClose
    InputStream download(String ossUrl);

    /**
     * 下载文件
     *
     * @param bucket 存储空间
     * @param key    文件对象key
     * @return 文件流（注意使用的地方要关流⚠️）
     */
    @WillNotClose
    InputStream download(String bucket, String key);

    /**
     * 下载文件
     *
     * @param bucket  存储空间
     * @param key     文件对象key
     * @param process 对象加工参数
     * @return 文件流（注意使用的地方要关流⚠️）
     */
    @WillNotClose
    InputStream download(String bucket, String key, @Nullable String process);

    /**
     * 复制相同Key的文件
     *
     * @param sourceBucket 原存储空间
     * @param targetBucket 目标存储空间
     * @param key          文件对象key
     */
    void copyWithSameKey(String sourceBucket, String targetBucket, String key, FileAcl targetAcl);

    /**
     * 复制相同Bucket的文件
     *
     * @param bucket    文件存储空间
     * @param sourceKey 源文件key
     * @param targetKey 目标文件key
     */
    void copyWithSameBucket(String bucket, String sourceKey, String targetKey, FileAcl targetAcl);

    /**
     * 将原路径的sourceKey进行process处理，并将结果存放至targetKey
     * ps:targetKey的访问权限和sourceKey自动保持一致
     *
     * @param bucket    存储空间（处理前和处理后的文件保存在一个存储空间下面）
     * @param sourceKey 源文件key
     * @param targetKey 处理结果文件key
     * @param targetAcl 处理结果文件访问权限
     * @param process   处理样式
     */
    void processObject(String bucket, String sourceKey, String targetKey, FileAcl targetAcl, String process);

    /**
     * 更新文件的访问控制权限
     *
     * @param bucket 存储空间
     * @param key    文件对象key
     * @param acl    访问控制权限
     */
    void updateFileAcl(String bucket, String key, FileAcl acl);

    /**
     * 获取文件HTTP URL（纯本地计算，无网络IO开销）
     * <p/>
     * <ul>
     *     <li>私有读文件，返回URL有签名和有效期，格式为：https://<Bucket>.<外网Endpoint>/<Object>?签名信息&ak<li/>
     *     <li>公共读文件，返回URL无签名和有效期，格式为：https://<Bucket>.<外网Endpoint>/<Object><li/>
     * </ul>
     *
     * @param bucket       存储空间
     * @param key          文件对象key
     * @param accessOption 访问选项
     * @return 文件访问链接
     */
    default String getHttpUrl(String bucket, String key, FileAccessOption accessOption) {
        return this.getHttpUrl(bucket, key, accessOption, null);
    }

    /**
     * 获取文件HTTP URL（纯本地计算，无网络IO开销）
     *
     * @param bucket        存储空间
     * @param key           文件对象key
     * @param accessOption  访问选项
     * @param processOption 文件处理选项
     * @return 文件访问链接
     */
    String getHttpUrl(String bucket, String key, FileAccessOption accessOption, @Nullable FileProcessOption processOption);

    /**
     * 获取文件HTTP URL（纯本地计算，无网络IO开销）
     *
     * @param ossUrl       OSS文件地址（oss://bucket/key）
     * @param accessOption 访问选项
     * @return 文件访问链接
     */
    default String getHttpUrl(String ossUrl, FileAccessOption accessOption) {
        return this.getHttpUrl(ossUrl, accessOption, null);
    }

    /**
     * 获取文件HTTP URL（纯本地计算，无网络IO开销）
     *
     * @param ossUrl        OSS文件地址（oss://bucket/key）
     * @param accessOption  访问选项
     * @param processOption 文件处理选项
     * @return 文件访问链接
     */
    String getHttpUrl(String ossUrl, FileAccessOption accessOption, @Nullable FileProcessOption processOption);

    /**
     * 获取指定oss对象的元信息
     *
     * @param bucket 文件存储空间
     * @param key    文件对象key
     * @return 用户自定义文件元数据
     */
    JSONObject getUserMetadata(String bucket, String key);

    /**
     * 物理删除文件
     *
     * @param bucket 存储空间
     * @param key    文件对象key
     */
    void delete(String bucket, String key);

    /**
     * 检查指定文件是否存在
     *
     * @param ossUrl OSS文件地址（oss://bucket/key）
     * @return 是否存在
     */
    boolean isFilePresent(String ossUrl);

    /**
     * 检查指定文件是否存在
     *
     * @param bucket 存储空间
     * @param key    文件对象key
     * @return 是否存在
     */
    boolean isFilePresent(String bucket, String key);

    default void assureStoragePresent(String bucket, String key) {
        Asserts.isTrue(this.isFilePresent(bucket, key), "OSS文件不存在");
    }

    /**
     * 查询存储空间访问控制权限
     *
     * @param bucket 存储空间
     * @return 访问控制权限
     */
    FileAcl getBucketAcl(String bucket);

    /**
     * 查询文件的访问控制权限
     *
     * @param bucket 存储空间
     * @param key    文件对象key
     * @return 访问控制权限
     */
    FileAcl getFileAcl(String bucket, String key);

    /**
     * 获取文件内容元数据
     *
     * @param bucket 存储空间
     * @param key    文件对象key
     * @return 文件内容元数据
     */
    FileContentMetadata getContentMetadata(String bucket, String key);
}
