/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.deprecated.qrcode.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version : QRCodeErrorCode.java, v 1.0 2023年07月22日 09时24分 syoka Exp$
 */
@Getter
@AllArgsConstructor
public enum QRCodeErrorCode implements ErrorCode {

    QR_CODE_NOT_VALID("二维码不正确"),
    QR_CODE_OVER_MAX_USE_TIME("此二维码超过最大使用次数，请更换邀请二维码"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
