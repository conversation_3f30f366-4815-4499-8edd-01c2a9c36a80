/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.prompt;

import ai.creatly.sky.creation.domain.common.util.TextUtil;
import com.jspeeder.core.util.text.FormatUtil;
import jodd.util.StringPool;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@UtilityClass
public class PromptUtils {

    public static boolean containsChineseCharacters(String input) {
        return TextUtil.containsChineseCharacter(input);
    }

    /**
     * 拼装两个prompt
     * 如果提示词包含参数，会先截取参数以获取非参数内容，再将它们拼接起来
     *
     * @param prompt 提示词
     * @param addPrompt 本次追加的提示词
     * @return -
     */
    public static String concatPrompts(String prompt, String addPrompt) {
        int firstParamIdx = StringUtils.indexOf(prompt, "--");
        int additionalFirstParamIdx = StringUtils.indexOf(addPrompt, "--");

        String originPrompt = StringUtils.substring(prompt, 0, firstParamIdx);
        String originPromptParam = StringPool.EMPTY;
        if (firstParamIdx != -1) {
            originPromptParam = StringUtils.substring(prompt, firstParamIdx);
        }

        String additionPrompt = StringUtils.substring(addPrompt, 0, additionalFirstParamIdx);
        String additionPromptParam = StringPool.EMPTY;
        if (additionalFirstParamIdx != -1) {
            additionPromptParam = StringUtils.substring(addPrompt, additionalFirstParamIdx);
        }
        return FormatUtil.format("{} {}, {} {}", originPrompt, additionPrompt, originPromptParam, additionPromptParam);
    }


    /**
     * 用于获取图片描述提示词用
     * mj返回的是带markdown语法的结果，我们需要对结果集进行\n\n切割
     */
    public static List<String> splitAndRemoveMarkdownLink(String markdownPrompt) {
        List<String> paragraphs = new ArrayList<>();

        String[] splitParagraphs = markdownPrompt.split("\n\n");

        for (String paragraph : splitParagraphs) {
            // 移除链接的语法，但保留链接中的文本
            String cleanedParagraph = paragraph.replaceAll("\\[([^]]+)]\\([^)]+\\)", "$1");
            String paragraphData = cleanedParagraph.replaceAll("[0-9️⃣]+", "");
            paragraphs.add(paragraphData.trim());
        }
        return paragraphs;
    }

    public String appendPromptParam(String prompt, Map<String, String> params) {
        String paramsStr = params.entrySet().stream()
                .map(entry -> " --" + entry.getKey() + " " + entry.getValue())
                .collect(Collectors.joining(" "));
        return String.join(prompt, " ", paramsStr);
    }
}
