/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 用户LLM 会话
 */
@Data
@Accessors(chain = true)
public class LLMConversation {

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 场景标识
     */
    private Long scenarioId;

    /**
     * 会话中的场景关联id
     */
    private Long scenarioCtxNo;

    /**
     * 会话标题
     */
    private String title;

    /**
     * 会话创建时间
     */
    private LocalDateTime createTime;

}
