/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.core.aiimage_old.model.task.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version AIImageZoomInParam.java, v 0.1 2023-09-23 16:05 syoka
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImageOutPaintParam extends ImageOpsBaseParam {

    /**
     * 扩展画面倍数50,75
     */
    private Integer outPaintSize;
}
