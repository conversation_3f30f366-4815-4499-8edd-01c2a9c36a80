/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.member.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 *
 * <AUTHOR>
 * @version MemberErrorCode.java, v 0.1 2023-10-28 上午11:34 zhoudong
 */
@RequiredArgsConstructor
@Getter
public enum MemberErrorCode implements ErrorCode {

    MEMBER_DOWNGRADE_UNSUPPORTED("会员暂不支持降级"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
