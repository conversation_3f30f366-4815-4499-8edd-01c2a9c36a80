/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.video.model.request;

import ai.creatly.sky.creation.domain.core.userfile.model.InputStreamFile;
import jakarta.annotation.Nullable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version ProductVideoReTalkRequest.java, v 0.1 2024-05-25 下午5:17 zhoudong
 */
@Data
@Accessors(chain = true)
public class ProductVideoReTalkRequest {

    /**
     * 原视频文件
     */
    private InputStreamFile videoFile;
    /**
     * 配音文件
     */
    private InputStreamFile audioFile;
    /**
     * 换脸图片
     */
    @Nullable
    private InputStreamFile faceImage;
    @Nullable
    private String          title;
    @Nullable
    private String          description;
}
