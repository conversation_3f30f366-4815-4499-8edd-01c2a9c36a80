/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.deprecated.material.model;

import ai.creatly.sky.creation.domain.common.ddd.Entity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 素材主体
 */
@Entity
@Data
@Accessors(chain = true)
public class MaterialBody {

    /**
     * 文件标识
     */
    private Long   fileId;
    /**
     * 文件地址（OSS 地址）
     */
    private String fileUrl;
    /**
     * 文本（当素材类型为文案时有值）
     */
    private String text;
}
