/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.wechat.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version WechatUser.java, v 0.1 2024-10-12 下午7:27 zhoudong
 */
@Data
@Accessors(chain = true)
public class WechatUser {

    private WechatAppType appType;
    private String        appName;
    private String        appId;
    private String        openId;
    private String        unionId;
    private String        nickname;
    private String        avatar;
}
