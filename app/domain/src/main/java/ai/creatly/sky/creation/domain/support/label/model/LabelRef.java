/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.label.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 关联的标签
 *
 * <AUTHOR>
 * @version LabelRef.java, v 0.1 2023-03-21 22:07 joton
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(of = "name")
public class LabelRef {

    /**
     * 标签ID
     */
    private Long   id;
    /**
     * 标签编码
     */
    private String code;
    /**
     * 显示名称
     */
    private String name;
}
