/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model;

import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanPeriodType;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version UserPlanQO.java, v 0.1 2023-12-21 下午3:57 zhoudong
 */
@Data
public class UserPlanQO {

    /**
     * 用户ID
     */
    private Long           uid;
    /**
     * 计划周期类型
     */
    private PlanPeriodType periodType;
}
