/*
 *
 *  * Creatly Inc.
 *  * Copyright (c) 2023-2023 All Rights Reserved.
 *
 */
package ai.creatly.sky.creation.domain.support.notification.mail.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EmailContext {

    /**
     * 发送端
     */
    private String from;
    /**
     * 目标端
     */
    private String to;
    /**
     * 主题
     */
    private String subject;
    /**
     * 邮件内容
     */
    private String text;
}
