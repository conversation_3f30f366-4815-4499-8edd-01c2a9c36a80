/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.mapper;

import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.admin.response.PlatformPlanVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

/**
 *
 * <AUTHOR>
 * @version PlatformPlanAdminMapper.java, v 0.1 2023-12-22 上午1:12 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface PlatformPlanAdminMapper {

    PlatformPlanVM toPlatformPlanVM(Plan plan);
}
