/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jetbrains.annotations.Nullable;

/**
 * 系统头像
 */
@AllArgsConstructor
@Getter
public enum SystemAvatar {

    youngBlackMan(1L, "youngBlackMan", "https://creatly-online.oss-accelerate.aliyuncs.com/creation/system/avatar/youngBlackMan.png"),
    oldWhiteMan(2L, "oldWhiteMan", "https://creatly-online.oss-accelerate.aliyuncs.com/creation/system/avatar/oldWhiteMan.png"),

    young<PERSON><PERSON>Man(3L, "youngBlueMan", "https://creatly-online.oss-accelerate.aliyuncs.com/creation/system/avatar/youngBlueMan.png"),
    youngBlack<PERSON>oman(4L, "youngBlackWoman", "https://creatly-online.oss-accelerate.aliyuncs.com/creation/system/avatar/youngBlackWoman.png"),
    young<PERSON>rangeWoman(5L, "youngOrangeWoman", "https://creatly-online.oss-accelerate.aliyuncs.com/creation/system/avatar/youngOrangeWoman.png"),
    youngPurpleMan(6L, "youngPurpleMan", "https://creatly-online.oss-accelerate.aliyuncs.com/creation/system/avatar/youngPurpleMan.png"),
    ;

    private final Long   id;
    private final String name;
    private final String httpUrl;

    @Nullable
    public static SystemAvatar ofId(Long id) {
        for (SystemAvatar avatar : values()) {
            if (avatar.getId().equals(id)) {
                return avatar;
            }
        }
        return null;
    }
}
