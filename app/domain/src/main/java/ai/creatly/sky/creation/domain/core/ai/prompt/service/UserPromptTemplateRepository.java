/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.prompt.service;

import ai.creatly.sky.creation.domain.core.ai.prompt.error.PromptTemplateErrorCode;
import ai.creatly.sky.creation.domain.core.ai.prompt.model.PromptTemplateBizType;
import ai.creatly.sky.creation.domain.core.ai.prompt.model.UserPromptTemplate;
import com.jspeeder.core.data.problem.exception.SysException;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version UserPromptTemplateRepository.java, v0.1 2025-02-19 19:19
 */
public interface UserPromptTemplateRepository {

    long create(UserPromptTemplate promptTemplate);

    void updateById(UserPromptTemplate promptTemplate);

    default UserPromptTemplate queryByCode(String code) {
        return this.queryOptionalByCode(code).orElseThrow(() -> new SysException(PromptTemplateErrorCode.PROMPT_TEMPLATE_NOT_FOUND));
    }

    Optional<UserPromptTemplate> queryOptionalByCode(String code);

    List<UserPromptTemplate> queryByBizType(PromptTemplateBizType bizType);
}
