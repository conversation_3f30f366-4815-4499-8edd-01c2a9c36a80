/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model;

import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * 声音扮演的角色
 *
 * <AUTHOR>
 * @version VoiceRole.java, v 0.1 2023-11-04 下午7:48 zhoudong
 */
@Data
@Accessors(chain = true)
public class VoiceRole {

    public static final String DEFAULT_CODE = "default";

    /**
     * 角色编号
     */
    private String code;
    /**
     * 角色描述
     */
    private String desc;
    /**
     * 预览音频文件ID
     */
    @Nullable
    private Long previewAudioId;
    /**
     * 预览音频地址（OSS地址）
     */
    @Nullable
    private String previewAudioUrl;
}
