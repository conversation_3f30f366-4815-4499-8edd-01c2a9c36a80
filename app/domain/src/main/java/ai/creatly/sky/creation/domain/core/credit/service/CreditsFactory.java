/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.credit.service;

import ai.creatly.sky.creation.domain.core.aiproduct.image.service.ProductImageCreditsService;
import ai.creatly.sky.creation.domain.core.aiproduct.video.service.ProductVideoCreditsService;
import ai.creatly.sky.creation.domain.core.aittalk.service.SelfTalkAvatarCreditsService;
import ai.creatly.sky.creation.domain.core.digitalhuman.service.DigHumanAvatarCreditsService;
import ai.creatly.sky.creation.domain.core.digitalhuman.service.DigHumanVideoCreditsService;
import ai.creatly.sky.creation.domain.core.story.service.StoryCreditsService;
import ai.creatly.sky.creation.domain.core.videoproject.service.VideoProjectCreditsService;
import ai.creatly.sky.creation.domain.core.voice.service.VoiceCreditsService;
import com.jspeeder.core.data.problem.exception.SysException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 计费服务工厂（集中维护每个业务模块的计费服务入口）
 *
 * <AUTHOR>
 * @version CreditsFactory.java, v 0.1 2024-01-16 下午5:36 zhoudong
 * <ul>
 *     <li>收入请求：{@link ai.creatly.sky.creation.domain.core.credit.model.CreditsIncome}</li>
 *     <li>支出请求：{@link ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense}</li>
 *     <li>退款请求：{@link ai.creatly.sky.creation.domain.core.credit.model.CreditsRefund}</li>
 * </ul>
 */
@Component
@RequiredArgsConstructor
public class CreditsFactory {

    private final List<CreditsService> creditsServices;

    /**
     * @return 图生数字人计费服务
     */
    public SelfTalkAvatarCreditsService selfTalkAvatar() {
        return creditsServices.stream()
                .map(creditsService -> creditsService instanceof SelfTalkAvatarCreditsService service ? service : null)
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new SysException("未找到图生数字人计费服务"));
    }

    /**
     * @return 语音创作计费服务
     */
    public VoiceCreditsService voice() {
        return creditsServices.stream()
                .map(creditsService -> creditsService instanceof VoiceCreditsService service ? service : null)
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new SysException("未找到语音创作计费服务"));
    }

    /**
     * @return 商品图片计费服务
     */
    public ProductImageCreditsService productImage() {
        return creditsServices.stream()
                .map(creditsService -> creditsService instanceof ProductImageCreditsService service ? service : null)
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new SysException("未找到商品图片计费服务"));
    }

    /**
     * @return 商品视频计费服务
     */
    public ProductVideoCreditsService productVideo() {
        return creditsServices.stream()
                .map(creditsService -> creditsService instanceof ProductVideoCreditsService service ? service : null)
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new SysException("未找到商品视频计费服务"));
    }

    /**
     * @return 故事计费服务
     */
    public StoryCreditsService story() {
        return creditsServices.stream()
                .map(creditsService -> creditsService instanceof StoryCreditsService service ? service : null)
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new SysException("未找到故事计费服务"));
    }

    /**
     * @return 真人数字人计费服务
     */
    public DigHumanAvatarCreditsService digHumanAvatar() {
        return creditsServices.stream()
                .map(creditsService -> creditsService instanceof DigHumanAvatarCreditsService service ? service : null)
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new SysException("未找到真人数字人计费服务"));
    }

    /**
     * @return 真人数字人视频计费服务
     */
    @Deprecated
    public DigHumanVideoCreditsService digHumanVideo() {
        return creditsServices.stream()
                .map(creditsService -> creditsService instanceof DigHumanVideoCreditsService service ? service : null)
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new SysException("未找到真人数字人视频计费服务"));
    }

    /**
     * @return 视频项目计费服务
     */
    public VideoProjectCreditsService videoProject() {
        return creditsServices.stream()
                .map(creditsService -> creditsService instanceof VideoProjectCreditsService service ? service : null)
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new SysException("未找到视频项目计费服务"));
    }
}
