/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.multimedia.service;

import ai.creatly.sky.creation.domain.support.multimedia.model.AudioSubtitle;
import ai.creatly.sky.creation.domain.support.multimedia.model.SynthesisMark;
import ai.creatly.sky.creation.domain.support.multimedia.model.TimedText;
import ai.creatly.sky.creation.domain.support.multimedia.model.TtsSubtitleText;
import ai.creatly.sky.creation.domain.support.multimedia.util.SubtitleUtil;
import com.hankcs.hanlp.restful.HanLPClient;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.ClassUtil;
import jodd.util.StringPool;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.IOException;
import java.time.Duration;
import java.util.*;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version SubtitleServiceImpl.java, v 0.1 2024-08-15 下午4:10 zhoudong
 */
@Service
@Slf4j
public class SubtitleServiceImpl implements SubtitleService {

    /**
     * 用于断句的标点符号集合
     */
    private final static Set<String> BREAK_PUNCTUATIONS = Set.of(
            // 中文标点符号
            "。", // 句号
            "？", // 问号
            "！", // 感叹号
            "，", // 逗号
            "；", // 分号
            "：", // 冒号
            "“", // 左双引号
            "”", // 右双引号
            "‘", // 左单引号
            "’", // 右单引号
            "——", // 破折号
            "、", // 顿号

            // 英文标点符号
            ".", // Period (句号)
            "?", // Question Mark (问号)
            "!", // Exclamation Mark (感叹号)
            ",", // Comma (逗号)
            ";", // Semicolon (分号)
            ":", // Colon (冒号)
            "\"", // Quotation Mark (双引号)
            "'", // Apostrophe (单引号)
            "—",  // Em Dash (破折号)

            // 西班牙语
            "¡", // Inverted Exclamation Mark (倒感叹号)
            "¿",  // Inverted Question Mark (倒问号)

            // 日语
            "・" // Katakana Middle Dot (片假名中点)
    );

    private static boolean isAllBreakPunctuations(String text) {
        return text.chars().allMatch(c -> BREAK_PUNCTUATIONS.contains(String.valueOf((char) c)));
    }

    @Override
    public List<TtsSubtitleText> genSubtitleTexts(int dialogueIndex, String text, AudioSubtitle subtitle, List<String> keywords) {
        Asserts.isTrue(text != null && !text.isBlank() && subtitle != null, "文本或字幕配置为空，无法生成字幕");
        try {
            List<List<String>> plainSentences = tokenize(text);
            Asserts.notEmpty(plainSentences, "will not happen");

            // 重点词合并，确保重点词不会被断开
            if (keywords != null && !keywords.isEmpty()) {
                plainSentences = plainSentences.stream()
                        .map(sentence -> SubtitleUtil.forceWords(sentence, keywords))
                        .collect(toList());
            }

            List<List<TextToken>> sentences = splitOriginalText(plainSentences, text, subtitle.getBreakEndIndexes());
            Asserts.notEmpty(sentences, "will not happen");

            List<TtsSubtitleText> subtitleTexts = toSubtitleTexts(dialogueIndex, sentences, subtitle);
            Asserts.notEmpty(subtitleTexts, "will not happen");

            return improveTtsSubtitles(subtitleTexts, subtitle);
        } catch (IOException e) {
            if (Boolean.TRUE.equals(subtitle.getOptional())) {
                // 此处会返回空集合，上层需要自行判断
                return new ArrayList<>();
            }
            throw new SysException(CommonErrorCode.UNSPECIFIED, "生成字幕失败", e);
        }
    }

    private static List<TtsSubtitleText> improveTtsSubtitles(List<TtsSubtitleText> subtitleTexts, AudioSubtitle subtitle) {
        // 如果某条字幕少于3个字或宽度小于200px，则合并到上一条字幕中去，如果不属于同一个分段编号，则不合并
        List<TtsSubtitleText> newSubtitleTexts = new ArrayList<>();
        for (int i = 0; i < subtitleTexts.size(); i++) {
            TtsSubtitleText current = subtitleTexts.get(i);
            if (i == 0) {
                // 第一条字幕，不合并
                newSubtitleTexts.add(current);
                continue;
            }
            String currentDisplayText = current.getDisplayText();
            if (currentDisplayText.length() <= 2 || textWidth(currentDisplayText, subtitle.getFontSize()) < 200) {
                TtsSubtitleText previous = newSubtitleTexts.getLast();
                if (previous.getDialogueIndex().equals(current.getDialogueIndex())
                        && Objects.equals(previous.getSegmentNo(), current.getSegmentNo())) {
                    // 合并到上一条字幕中去
                    previous.setEndIndex(current.getEndIndex());
                    previous.setOriginalText(previous.getOriginalText() + current.getOriginalText());
                    previous.setDisplayText(previous.getDisplayText() + currentDisplayText);
                } else {
                    // 不属于同一个对话或分段编号，则不合并
                    newSubtitleTexts.add(current);
                }
            } else {
                newSubtitleTexts.add(current);
            }
        }
        return newSubtitleTexts;
    }

    private static List<List<String>> tokenize(String text) throws IOException {
        // TODO 替换为本地部署，即可支持自定义词典
        String auth = "************************************************";
        HanLPClient client = new HanLPClient("https://www.hanlp.com/api", auth, "zh", 20);
        // 粗分标准
        List<?> list = client.parse(text, new String[]{"tok/coarse"}, new String[]{"tok/fine"}).get("tok/coarse");
        return ClassUtil.cast(list);
    }

    @Data
    @Accessors(chain = true)
    private static class TextToken {
        /**
         * 处理后的文本（带标点符号、无连续空白字符）
         */
        private String  text;
        /**
         * 自定义分段编号（从1开始，可以实现字幕分组能力，即连续多条字幕可以划分到同一个分段里去）
         */
        @Nullable
        private Integer segmentNo;
        /**
         * 原始文本
         */
        private String  originalText;
        private Integer originalEndIndex;
    }

    private static List<List<TextToken>> splitOriginalText(List<List<String>> plainSentences, String text,
                                                           @Nullable List<Integer> breakIndexes) {
        if (CollectionUtils.isNotEmpty(breakIndexes)) {
            // 确保升序且不重复
            breakIndexes = breakIndexes.stream()
                    .sorted(Integer::compareTo)
                    .distinct()
                    .collect(toList());
            Assert.isTrue(breakIndexes.getFirst() > 0, "自定义断句位置不合法");
            int lastBreakIndex = breakIndexes.getLast();
            Asserts.isTrue(lastBreakIndex < text.length() - 1, "自定义断句位置不合法");
        }

        List<List<TextToken>> sentences = new ArrayList<>();
        int i = 0;
        int segmentNo = 1;
        for (List<String> plainSentence : plainSentences) {
            List<TextToken> sentence = new ArrayList<>();
            for (String token : plainSentence) {
                int tokenBeginIndex = 0;
                StringBuilder originalText = new StringBuilder();
                for (int j = 0; j < token.length(); j++) {
                    char tokenChar = token.charAt(j);
                    if (Character.isWhitespace(tokenChar)) {
                        // 理论上，token不可能以空白字符开头，万一有，则跳过空白字符
                        tokenBeginIndex++;
                        continue;
                    }
                    while (i < text.length()) {
                        char originalChar = text.charAt(i++);
                        originalText.append(originalChar);
                        if (originalChar == tokenChar) {
                            break;
                        }
                    }
                }
                // 压缩连续空格，如果有的话
                boolean hasExtraWhitespace = false;
                while (i < text.length()) {
                    char originalChar = text.charAt(i);
                    if (!Character.isWhitespace(originalChar)) {
                        break;
                    }
                    originalText.append(originalChar);
                    hasExtraWhitespace = true;
                    i++;
                }
                if (hasExtraWhitespace && !isAllBreakPunctuations(token)) {
                    // 如果有连续多个空格，且非断句标点，则会被压缩成一个空格
                    token = token + StringPool.SPACE;
                }
                TextToken textToken = new TextToken()
                        .setText(token.substring(tokenBeginIndex))
                        .setOriginalText(originalText.toString())
                        .setOriginalEndIndex(i - 1);
                if (CollectionUtils.isEmpty(breakIndexes)) {
                    sentence.add(textToken);
                } else {
                    if (segmentNo > breakIndexes.size()) {
                        // 已经到达最后一个分段号
                        textToken.setSegmentNo(segmentNo);
                        sentence.add(textToken);
                    } else {
                        int originalEndIndex = textToken.getOriginalEndIndex();
                        int breakIndex = breakIndexes.get(segmentNo - 1);
                        if (originalEndIndex < breakIndex) {
                            // 还未到达自定义断句位置
                            textToken.setSegmentNo(segmentNo);
                            sentence.add(textToken);
                        } else if (originalEndIndex == breakIndex) {
                            // 刚好到达自定义断句位置
                            textToken.setSegmentNo(segmentNo++);
                            sentence.add(textToken);
                        } else {
                            // 如果超过自定义断句位置，则切分为两段文本（如果自定义断句没有破坏自然语义，则理论上不会进入到这个分支）
                            int tokenBreakIndex = token.length() - 1 - (originalEndIndex - breakIndex);
                            if (tokenBeginIndex > tokenBreakIndex) {
                                // 当前token直接归属到下一个分段
                                textToken.setSegmentNo(++segmentNo);
                                sentence.add(textToken);
                            } else {
                                // 拆成前后两段，分别属于不同的自定义分段
                                TextToken textToken1 = new TextToken()
                                        .setText(token.substring(tokenBeginIndex, tokenBreakIndex + 1))
                                        .setOriginalText(originalText.substring(0, tokenBreakIndex + 1))
                                        .setOriginalEndIndex(breakIndex)
                                        .setSegmentNo(segmentNo++);
                                TextToken textToken2 = new TextToken()
                                        .setText(token.substring(tokenBreakIndex + 1))
                                        .setOriginalText(originalText.substring(tokenBreakIndex + 1))
                                        .setOriginalEndIndex(originalEndIndex)
                                        .setSegmentNo(segmentNo);
                                sentence.add(textToken1);
                                sentence.add(textToken2);
                            }
                        }
                    }
                }
            }
            sentences.add(sentence);
        }
        if (i < text.length()) {
            String remainingText = text.substring(i);
            List<TextToken> latestSentence = sentences.getLast();
            TextToken latestToken = latestSentence.getLast();
            latestToken.setOriginalText(latestToken.getOriginalText() + remainingText)
                    .setOriginalEndIndex(text.length() - 1);
        }
        return sentences;
    }

    private static List<TtsSubtitleText> toSubtitleTexts(int dialogueIndex, List<List<TextToken>> sentences, AudioSubtitle subtitle) {
        List<TtsSubtitleText> subtitleTexts = new ArrayList<>();
        StringBuilder displayTextBuilder = new StringBuilder();
        int displayTextWidth = 0;
        for (List<TextToken> sentence : sentences) {
            List<TextToken> tokens = new ArrayList<>();
            Integer currentSegmentNo = null;
            for (TextToken token : sentence) {
                if (isAllBreakPunctuations(token.text)) {
                    // 遇到标点符号，进行字幕断句 TODO 如果遇到前后两个token都是标点符号呢？
                    tokens.add(token);
                    int endIndex = token.getOriginalEndIndex();
                    TtsSubtitleText subtitleText = toSubtitleText(dialogueIndex, tokens, endIndex, displayTextBuilder.toString());
                    subtitleTexts.add(subtitleText);
                    displayTextBuilder = new StringBuilder();
                    displayTextWidth = 0;
                    tokens.clear();
                    continue;
                }

                if (currentSegmentNo == null) {
                    currentSegmentNo = token.getSegmentNo();
                } else if (!currentSegmentNo.equals(token.getSegmentNo())) {
                    // 遇到自定义分段号变化，进行字幕断句
                    if (!tokens.isEmpty()) {
                        // 生成上一条字幕
                        int endIndex = token.getOriginalEndIndex();
                        TtsSubtitleText subtitleText = toSubtitleText(dialogueIndex, tokens, endIndex, displayTextBuilder.toString());
                        subtitleTexts.add(subtitleText);
                    }

                    // 将当前 token 加入到下一轮循环
                    tokens.clear();
                    tokens.add(token);
                    displayTextBuilder = new StringBuilder().append(token.text);
                    displayTextWidth = textWidth(token.text, subtitle.getFontSize());
                    currentSegmentNo = token.getSegmentNo();
                    continue;
                }

                int tokenWidth = textWidth(token.text, subtitle.getFontSize());
                if (displayTextWidth + tokenWidth > subtitle.getWidth()) {
                    // 达到字幕宽度，进行字幕断句
                    TextToken previousToken = tokens.getLast();
                    int endIndex = previousToken.getOriginalEndIndex();
                    subtitleTexts.add(toSubtitleText(dialogueIndex, tokens, endIndex, displayTextBuilder.toString()));
                    displayTextBuilder = new StringBuilder();
                    displayTextWidth = 0;
                    tokens.clear();
                }

                tokens.add(token);
                displayTextBuilder.append(token.text);
                displayTextWidth += tokenWidth;
            }

            if (!displayTextBuilder.isEmpty()) {
                TextToken previousToken = tokens.getLast();
                int endIndex = previousToken.getOriginalEndIndex();
                TtsSubtitleText subtitleText = toSubtitleText(dialogueIndex, tokens, endIndex, displayTextBuilder.toString());
                subtitleTexts.add(subtitleText);
                displayTextBuilder = new StringBuilder();
                displayTextWidth = 0;
            }
        }
        return subtitleTexts;
    }

    private static TtsSubtitleText toSubtitleText(int dialogueIndex, List<TextToken> tokens, int endIndex, String displayText) {
        Asserts.notEmpty(tokens, "will not happen");
        return new TtsSubtitleText()
                .setId(IdHelper.getStrId())
                .setDialogueIndex(dialogueIndex)
                .setDisplayText(displayText)
                .setEndIndex(endIndex)
                .setSegmentNo(tokens.getFirst().getSegmentNo())
                .setOriginalText(tokens.stream().map(TextToken::getOriginalText).reduce("", String::concat));
    }

    private static int textWidth(String text, int fontSize) {
        return text.chars().map(c -> charWidth((char) c, fontSize)).sum();
    }

    /**
     * 非等宽字体，计算字符的显示宽度
     *
     * @param c        字符
     * @param fontSize 字体大小（px）
     * @return 字符显示宽度（px）
     */
    private static int charWidth(char c, int fontSize) {
        // 取字宽的1/4：空白字符，字母：i、l、I、r、t、f、j
        if (Character.isWhitespace(c) || c == 'i' || c == 'l' || c == 'I' || c == 'r' || c == 't' || c == 'f' || c == 'j') {
            return fontSize / 4;
        }
        // 取字宽的3/5：ASCII字符和拉丁字符
        if (isLatinCharacter(c)) {
            return fontSize * 3 / 5;
        }
        return fontSize;
    }

    private static boolean isLatinCharacter(char c) {
        // ASCII可见字符 和 拉丁字符
        if (c > 32 && c <= 126) {
            return true;
        }
        Character.UnicodeBlock unicodeBlock = Character.UnicodeBlock.of(c);
        return unicodeBlock == Character.UnicodeBlock.BASIC_LATIN ||
                unicodeBlock == Character.UnicodeBlock.LATIN_1_SUPPLEMENT ||
                unicodeBlock == Character.UnicodeBlock.LATIN_EXTENDED_A ||
                unicodeBlock == Character.UnicodeBlock.LATIN_EXTENDED_B ||
                unicodeBlock == Character.UnicodeBlock.LATIN_EXTENDED_ADDITIONAL;
    }

    @Override
    public List<TimedText> toSubtitles(List<TtsSubtitleText> subtitleTexts, List<SynthesisMark> synthesisMarks, Duration duration) {
        if (CollectionUtils.isEmpty(subtitleTexts) || CollectionUtils.isEmpty(synthesisMarks)) {
            log.warn("字幕文本或字幕时间标记为空，无法构建最终字幕");
            return Collections.emptyList();
        }

        Map<String, List<SynthesisMark>> markMap = synthesisMarks.stream().collect(groupingBy(SynthesisMark::getName));

        List<TimedText> subtitles = new ArrayList<>();
        subtitleTexts.stream().collect(groupingBy(TtsSubtitleText::getDialogueIndex)).values().forEach(texts -> {
            // 在一个对话中，按照时间线排序
            texts.sort(Comparator.comparing(TtsSubtitleText::getEndIndex));
            for (int i = 0; i < texts.size(); i++) {
                TtsSubtitleText subtitleText = texts.get(i);
                String text = subtitleText.getDisplayText();

                // 开始时间和开始下标位置
                final Duration start;
                final int startIndex;
                if (i > 0) {
                    TimedText previousSubtitle = subtitles.get(i - 1);
                    start = previousSubtitle.getEnd();
                    startIndex = previousSubtitle.getOriginalEndIndex() + 1;
                } else {
                    start = Duration.ZERO;
                    startIndex = 0;
                }

                // 结束时间
                final Duration end;
                List<SynthesisMark> marks = markMap.get(subtitleText.getId());
                if (marks == null && i == texts.size() - 1) {
                    // 如果是最后一条字幕，且标记为空，则结束时间直接取音频总时长
                    end = duration;
                } else {
                    Asserts.notEmpty(marks, "无法找到字幕合成标记，markId=" + subtitleText.getId());
                    Asserts.isTrue(marks.size() == 1, "字幕合成标记出现多个重复的，markId={}", subtitleText.getId());
                    end = Duration.ofMillis(marks.getFirst().getOffsetMills());
                }

                // 结束下标位置
                final int endIndex = subtitleText.getEndIndex();

                Integer[] originalIndexRange = new Integer[]{startIndex, endIndex};
                subtitles.add(new TimedText(subtitleText.getId(), text, start, end)
                        .setDialogueIndex(subtitleText.getDialogueIndex())
                        .setOriginalIndexRange(originalIndexRange)
                        .setSegmentNo(subtitleText.getSegmentNo())
                );
            }
        });

        // 过滤时间线不合理的字幕
        return subtitles.stream()
                // 结束时间大于开始时间
                .filter(subtitle -> subtitle.getEnd().compareTo(subtitle.getStart()) > 0)
                // 按照开始时间升序
                .sorted(Comparator.comparing(TimedText::getStart))
                .collect(toList());
    }
}
