/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.service;

import ai.creatly.sky.creation.domain.common.util.crypto.DesensitizationUtil;
import ai.creatly.sky.creation.domain.common.util.crypto.EncryptUtil;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.invitation.model.UserInvitation;
import ai.creatly.sky.creation.domain.core.invitation.service.UserInvitationRepository;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.core.user.model.*;
import ai.creatly.sky.creation.domain.core.user.model.organization.UserOrganization;
import ai.creatly.sky.creation.domain.core.user.model.organization.UserOrganizationStatus;
import ai.creatly.sky.creation.domain.core.user.model.request.VerifyCodeScenario;
import ai.creatly.sky.creation.domain.core.user.repository.UserRepository;
import ai.creatly.sky.creation.domain.support.notification.app.AppNotifyHelper;
import ai.creatly.sky.creation.domain.support.wechat.model.WechatUser;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserService {

    private final UserRepository           userRepository;
    private final UserCreditDomainService  userCreditDomainService;
    private final SmsVerifyCodeManager     smsVerifyCodeManager;
    private final TransactionTemplate      transactionTemplate;
    private final AppNotifyHelper          appNotifyHelper;
    private final UserInvitationRepository userInvitationRepository;

    @NotNull
    public UserInfo getUserByPhoneIfNotExistThenCreate(String phone, @Nullable String inviteCode) {
        Validates.notBlank(phone, UserErrorCode.THE_INPUT_PHONE_IS_EMPTY);
        UserInfo userInfo = userRepository.queryOptionalByPhone(phone).orElse(null);
        if (Objects.nonNull(userInfo)) {
            userInfo.setPhone(DesensitizationUtil.desensitize(userInfo.getPhone()));
            userInfo.setPassword("******");
            return userInfo;
        }

        // 新增用户
        userInfo = this.createUserInfoByPhoneAndWxUnionId(phone, null, inviteCode);
        log.info("register new user, phone:{}", phone);
        appNotifyHelper.trace("register new user by phone, phone:{}", phone);

        // 返回用户信息
        userInfo.setPhone(DesensitizationUtil.desensitize(userInfo.getPhone()));
        userInfo.setPassword("******");
        return userInfo;
    }

    @NotNull
    public boolean isExistUserByPhone(String phone) {
        Validates.notBlank(phone, UserErrorCode.THE_INPUT_PHONE_IS_EMPTY);
        return userRepository.queryOptionalByPhone(phone).orElse(null)!=null;
    }
    /**
     * 微信绑定手机（幂等）
     *
     * @param phone      手机号
     * @param wechatUser 微信用户信息
     * @param inviteCode 邀请码
     * @return -
     */
    public UserInfo registerThenBindWechatPhone(String phone, WechatUser wechatUser, @Nullable String inviteCode) {
        String unionId = wechatUser.getUnionId();

        // 幂等逻辑
        UserInfo userByWxUnionId = userRepository.queryOptionalByWechatUnionId(unionId).orElse(null);
        if (Objects.nonNull(userByWxUnionId)) {
            // 已经绑定了该手机号，则幂等返回
            if (userByWxUnionId.getPhone().equals(phone)) {
                log.info("[createUserThenBindWechatPhone]user has already bind this phone, phone:{},unionId:{}", phone, unionId);
                return this.deserializeUserInfo(userByWxUnionId);
            }
            log.warn("[createUserThenBindWechatPhone]user has already bind other phone, phone:{},unionId:{}", phone, unionId);
            throw new BizException(UserErrorCode.THE_WECHAT_HAS_ALREADY_BIND_USER);
        }
        if (StringUtils.isEmpty(phone)) {
            log.warn("[createUserThenBindWechatPhone]phone is empty, phone:{},unionId:{}", phone, unionId);
            throw new BizException(UserErrorCode.THE_INPUT_PHONE_IS_EMPTY);
        }
        UserInfo userByPhone = userRepository.queryOptionalByPhone(phone).orElse(null);
        if (Objects.nonNull(userByPhone)) {
            // 已经绑定了该微信号，则幂等返回
            if (unionId.equals(userByPhone.getWechatUnionId())) {
                log.info("[createUserThenBindWechatPhone]user has already bind this wechat, phone:{},unionId:{}", phone, unionId);
                return this.deserializeUserInfo(userByPhone);
            }

            // 未绑定微信号，则进行绑定
            if (Objects.isNull(userByPhone.getWechatUnionId())) {
                log.info("[createUserThenBindWechatPhone]user bind this wechat, phone:{},unionId:{}", phone, unionId);
                userByPhone.setWechatUnionId(unionId).addWechatOpenId(WechatOpenId.from(wechatUser));
                userRepository.updateById(new UserInfo()
                        .setId(userByPhone.getId())
                        .setWechatUnionId(userByPhone.getWechatUnionId())
                        .setWechatOpenIds(userByPhone.getWechatOpenIds())
                );

                return this.deserializeUserInfo(userByPhone);
            }

            // 已经绑定了其他微信号
            log.warn("[createUserThenBindWechatPhone]user has already bind other wechat, phone:{},unionId:{}", phone, unionId);
            throw new BizException(UserErrorCode.THE_PHONE_HAS_ALREADY_BIND_USER);
        }

        // 新注册用户
        UserInfo userInfo = this.createUserInfoByPhoneAndWxUnionId(phone, wechatUser, inviteCode);
        log.info("微信用户注册成功，userId:{}, phone:{}, unionId:{}", userInfo.getId(), phone, unionId);
        appNotifyHelper.trace("微信用户注册成功，userId:{}, phone:{}, unionId:{}", userInfo.getId(), phone, unionId);

        return this.deserializeUserInfo(userInfo);
    }

    private UserOrganization getDefaultUserOrganization(){
        return new UserOrganization().setOrgCode("creatly").setOrgName("creatly").setStatus(UserOrganizationStatus.ACTIVE).setOrgId(10661670000000001L).setOrgUid(Long.valueOf("227824315849019392"));
    }

    private UserInfo createUserInfoByPhoneAndWxUnionId(String phone, @Nullable WechatUser wechatUser, @Nullable String inviteCode) {
        UserInfo userInfo = UserInfo.builder()
                .username(wechatUser == null ? phone : "wx_" + phone)
                // todo: 如果是微信用户，可以获取微信昵称和头像
                .nickname(phone)
                .avatar(SystemAvatar.youngBlackMan.getHttpUrl())
                .phone(phone)
                .password(phone.substring(phone.length() - 6))
                .email(null)
                .activeOrganization(this.getDefaultUserOrganization())
                .lastOrgCode(this.getDefaultUserOrganization().getOrgCode())
                .wechatUnionId(wechatUser == null ? null : wechatUser.getUnionId())
                .build()
                .addWechatOpenId(wechatUser == null ? null : WechatOpenId.from(wechatUser));
        log.info("创建用户:{}",userInfo);
        transactionTemplate.setTimeout(5);
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            long uid = userRepository.create(userInfo);
            // 初始化用户账户（赠送初始额度）
            userCreditDomainService.initUserGeneralCreditAccount(uid);
            // 通过邀请码注册
            if (StringUtils.isNotBlank(inviteCode)) {
                // 邀请者
                UserInfo inviter = userRepository.queryOptionalByInviteCode(inviteCode).orElse(null);
                if (inviter == null) {
                    log.warn("邀请码无效，inviteCode:{}", inviteCode);
                    return;
                }
                // 邀请者奖励
                int credits = userCreditDomainService.inviteUserReward(Long.parseLong(inviter.getId()), uid);

                // 新增邀请记录
                UserInvitation userInvitation = new UserInvitation()
                        .setInviterId(Long.parseLong(inviter.getId()))
                        .setInviterName(inviter.getNickname())
                        .setInviteeId(uid)
                        .setInviteeName(userInfo.getNickname())
                        .setInviteCode(inviteCode)
                        .setAwardedCredits(credits);
                userInvitationRepository.create(userInvitation);
            }
            userInfo.setId(String.valueOf(uid));
        });
        return userInfo;
    }

    private UserInfo deserializeUserInfo(UserInfo userInfo) {
        userInfo.setPhone(DesensitizationUtil.desensitize(userInfo.getPhone()));
        userInfo.setPassword("******");
        return userInfo;
    }

    public void updateUserInfo(long uid, Long avatarId, String nickName, String phone, String verifyCode) {
        String avatar = null;
        if (Objects.nonNull(avatarId)) {
            SystemAvatar systemAvatar = SystemAvatar.ofId(avatarId);
            if (Objects.nonNull(systemAvatar)) {
                avatar = systemAvatar.getHttpUrl();
            }
        }

        // 修改手机号需要验证码
        if (Objects.nonNull(phone)) {
            Validates.notBlank(verifyCode, UserErrorCode.USER_VERIFY_CODE_NOT_EXISTS);
            boolean validateResult = smsVerifyCodeManager.validateUserVerifyCode(VerifyCodeScenario.CHANGE_PHONE, phone, verifyCode);
            Validates.isTrue(validateResult, UserErrorCode.USER_VERIFY_CODE_NOT_MATCH);
        }

        UserInfo updatableUserInfo = UserInfo.builder()
                .id(String.valueOf(uid))
                .phone(phone)
                .nickname(nickName)
                .avatar(avatar)
                .build();
        userRepository.updateById(updatableUserInfo);
    }

    public void switchOrganization(long uid, String orgCode) {
        List<UserOrganization> userOrganizations = userRepository.getUserOrganizations(uid);
        if (CollectionUtils.isEmpty(userOrganizations)) {
            log.info("no organization found, uid:{},orgId:{}", uid, orgCode);
            return;
        }
        // 只能有一个active的账号
        List<UserOrganization> organizations = userOrganizations
                .stream()
                .peek(organization -> {
                    if (StringUtils.equals(String.valueOf(orgCode), organization.getOrgCode())) {
                        organization.setStatus(UserOrganizationStatus.ACTIVE);
                    } else {
                        organization.setStatus(UserOrganizationStatus.INACTIVE);
                    }
                }).toList();
        userRepository.updateUserOrganizations(uid, organizations, orgCode);
    }

    /**
     * 申请成为IP创作者（幂等）
     *
     * @param uid -
     */
    public void applyForIpCreator(long uid) {
        transactionTemplate.executeWithoutResult(status -> {
            UserInfo userInfo = userRepository.lockById(uid);
            Asserts.notNull(userInfo, "will not happen");
            List<UserRole> roles = new ArrayList<>(userInfo.getRoles());
            if (roles.stream().anyMatch(role -> role.getType() == UserRoleType.ip_creator)) {
                return;
            }
            roles.add(new UserRole().setType(UserRoleType.ip_creator));
            userRepository.updateById(new UserInfo().setId(String.valueOf(uid)).setRoles(roles));
        });
    }

    public UserInfo getUserByPassword(String userName, String password) {
        return userRepository.getUserByPassword(userName, password).orElse(null);
    }

    public boolean replacePassword(long uid, String newPassword) {
        UserInfo userInfo = userRepository.getUserById(uid).orElse(null);
        if (userInfo == null) {
            return false;
        }
        userInfo.setPassword(EncryptUtil.md5HexBase64(newPassword));
        userRepository.updateById(userInfo);
        return true;
    }
}
