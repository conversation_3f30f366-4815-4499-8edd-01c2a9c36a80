/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.model.response;

import ai.creatly.sky.creation.domain.core.credit.model.response.UserCreditAccountVM;
import ai.creatly.sky.creation.domain.core.member.model.MemberType;
import ai.creatly.sky.creation.domain.core.member.model.response.MemberShowStyleVM;
import ai.creatly.sky.creation.domain.core.user.model.UserRole;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version UserProfileVM.java, v 0.1 2023-08-21 01:33 joton
 */
@Data
@Accessors(chain = true)
public class UserProfileVM {

    private String                    uid;
    /**
     * 用户名（唯一）
     */
    private String                    nickName;
    /**
     * 用户名（唯一）
     */
    private String                    username;
    /**
     * 手机
     */
    private String                    phone;
    /**
     * 密码 非明文
     */
    private String                    password;
    /**
     * 用户邮箱
     */
    private String                    email;
    /**
     * 头像地址
     */
    private String                    avatar;
    /**
     * 有效账户列表（最多100个）
     */
    private List<UserCreditAccountVM> creditAccounts;
    /**
     * 用户所属组织
     */
    private List<UserOrganizationVM>  organizations;
    /**
     * 专属邀请码
     */
    private String                    inviteCode;
    /**
     * 有效的credits数量
     */
    private Integer                   totalActiveCredits;
    /**
     * 会员类型
     */
    private MemberType                memberType;
    /**
     * 会员到期日期，包含（yyyy-MM-dd）
     */
    @Nullable
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate                 memberExpireAt;
    /**
     * 会员展示样式
     */
    private MemberShowStyleVM         memberShowStyle;
    /**
     * 会员的订阅计划ID
     */
    private String                    memberPlanId;
    /**
     * 会员的订阅计划等级
     */
    private Integer                   memberPlanLevel;
    /**
     * 用户角色列表
     */
    private List<UserRole>            roles;

    private UserOrganizationVM        lastOrganization;

    /**
     * 姓名
     */
    private String                    fullName;

    /**
     * 身份证
     */
    private String                    idCard;

}
