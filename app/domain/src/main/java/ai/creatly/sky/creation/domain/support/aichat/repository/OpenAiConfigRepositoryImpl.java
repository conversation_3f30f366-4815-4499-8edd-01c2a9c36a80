package ai.creatly.sky.creation.domain.support.aichat.repository;

import ai.creatly.sky.creation.domain.support.aichat.error.AIChatErrorCode;
import ai.creatly.sky.creation.domain.support.aichat.model.OpenAiChatCompletionConfig;
import ai.creatly.sky.creation.domain.support.bizconfig.repository.BizConfigRepository;
import com.jspeeder.core.data.problem.exception.SysException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version OpenAiConfigRepositoryImpl.java, v 0.1 2023-05-08 16:26 joton
 */
@Repository
@RequiredArgsConstructor
public non-sealed class OpenAiConfigRepositoryImpl implements OpenAiConfigRepository {

    private final BizConfigRepository bizConfigRepository;

    @Override
    public OpenAiChatCompletionConfig chatCompletionConfig() {
        String schemaCode = "1";
        String api = "chat_completion";
        return bizConfigRepository.queryOne(schemaCode, List.of(api), OpenAiChatCompletionConfig.class)
                .orElseThrow(() -> new SysException(AIChatErrorCode.OPEN_AI_CONFIG_ABSENCE));
    }
}
