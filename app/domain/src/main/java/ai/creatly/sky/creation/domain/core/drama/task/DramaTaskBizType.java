/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.task;

import com.jspeeder.core.data.enums.ICode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version DramaTaskBizType.java, 2024-10-29 下午3:42 zhoudong
 */
@RequiredArgsConstructor
@Getter
public enum DramaTaskBizType implements ICode {

    preprocess("预处理"),
    ;

    private final String desc;

    @Override
    public String getCode() {
        return this.name();
    }
}
