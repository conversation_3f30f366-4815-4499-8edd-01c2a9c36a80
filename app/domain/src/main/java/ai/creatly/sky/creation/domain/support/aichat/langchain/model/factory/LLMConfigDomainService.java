/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.aichat.langchain.model.factory;

import ai.creatly.sky.creation.domain.common.ddd.DomainService;
import ai.creatly.sky.creation.domain.common.env.RuntimeEnv;
import ai.creatly.sky.creation.domain.support.aichat.langchain.error.CopilotErrorCode;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.LLMConfig;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.LLMName;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.baichuan.BaichuanCloud56BModel;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.baichuan.BaichuanCloud56BStreamChatModel;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.kimi.KimiChatLanguageModel;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.kimi.KimiStreamChatLanguageModel;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.openai.ProxyOpenAiModerationModel;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.openai.ProxyOpenAiStreamingChatModel;
import ai.creatly.sky.creation.domain.support.aichat.langchain.repository.LLMConfigRepository;
import ai.creatly.sky.creation.domain.support.config.service.PreferenceDomainService;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import com.jspeeder.core.data.problem.exception.BizException;
import dev.ai4j.openai4j.chat.ResponseFormatType;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;

import java.net.Proxy;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.util.function.Function.identity;

@DomainService
@RequiredArgsConstructor
public class LLMConfigDomainService implements InitializingBean {

    /**
     * 当前可用的NLP配置
     */
    private static final String ACTIVE_NLP_LLM_CONFIG = "ACTIVE_NLP_LLM_CONFIG";

    private final PreferenceDomainService preferenceDomainService;
    private final LLMConfigRepository llmConfigRepository;
    private final RuntimeEnv              runtimeEnv;
    private final AppAlertHelper          appAlertHelper;

    @Value("${application.baichuan-cloud.endpoint}")
    private String baichuanCloudEndpoint;
    @Value("${application.kimi-cloud.endpoint}")
    private String kimiCloudEndpoint;

    @Qualifier("usSocksProxy")
    private final Proxy usSocksProxy;

    public static final String JSON_OBJECT = "JSON_OBJECT";
    public static final String TEXT        = "TEXT";

    private final Map<String, Map<String, StreamingChatLanguageModel>> streamingModelMap = new HashMap<>();
    private final Map<String, Map<String, ChatLanguageModel>>          chatModelMap      = new HashMap<>();

    /**
     * 获取chat model
     *
     * @return -
     */
    public ChatLanguageModel getActiveChatLanguageModel() {
        return chatModelMap.get(getActiveNlpLLMConfig().getName()).get(JSON_OBJECT);
    }

    /**
     * 获取chat stream model
     *
     * @return -
     */
    public StreamingChatLanguageModel getActiveStreamChatLanguageModel() {
        return getActiveStreamChatLanguageModel(JSON_OBJECT);
    }

    /**
     * 获取chat stream model
     *
     * @param responseType 文本模式/JSON模式
     * @return -
     */
    public StreamingChatLanguageModel getActiveStreamChatLanguageModel(String responseType) {
        Map<String, StreamingChatLanguageModel> map = streamingModelMap.get(getActiveNlpLLMConfig().getName());
        StreamingChatLanguageModel streamingChatLanguageModel = map.get(responseType);
        if (Objects.isNull(streamingChatLanguageModel)) {
            throw new BizException(CopilotErrorCode.NO_LLM_SERVER_IMPLEMENT_FOUND);
        }
        return streamingChatLanguageModel;
    }


    /**
     * 获取当前有效的LLM NLP模型
     *
     * @return 获取当前
     */
    private LLMConfig getActiveNlpLLMConfig() {
        String modelName = preferenceDomainService.getSystemPreference(ACTIVE_NLP_LLM_CONFIG, LLMName.OPENAI_GPT4.name());
        return llmConfigRepository.getLLMConfig(LLMName.ofName(modelName));
    }

    @Override
    public void afterPropertiesSet() {
        List<LLMConfig> llmConfigs = llmConfigRepository.getLLMConfigs();
        Map<String, LLMConfig> llmConfigMap = llmConfigs.stream().collect(Collectors.toMap(LLMConfig::getName, identity()));

        String gpt4AppKey = llmConfigMap.get(LLMName.OPENAI_GPT4.name()).getAppKey();
        String baichuan56BAppKey = llmConfigMap.get(LLMName.BAICHUAN_56B.name()).getAppKey();
        String kimi8KAppKey = llmConfigMap.get(LLMName.KIMI.name()).getAppKey();

        // OpenAI stream
        ProxyOpenAiStreamingChatModel openAiJSONStreamChatModel = ProxyOpenAiStreamingChatModel.builder()
                .proxy(runtimeEnv.isOffline() ? null : usSocksProxy)
                .apiKey(gpt4AppKey)
                .responseType(ResponseFormatType.JSON_OBJECT)
                .build();
        ProxyOpenAiStreamingChatModel openAiTextStreamChatModel = ProxyOpenAiStreamingChatModel.builder()
                .proxy(runtimeEnv.isOffline() ? null : usSocksProxy)
                .apiKey(gpt4AppKey)
                .responseType(ResponseFormatType.TEXT)
                .build();

        BaichuanCloud56BStreamChatModel baichuanStreamChatModel = BaichuanCloud56BStreamChatModel.withUrl(baichuanCloudEndpoint, baichuan56BAppKey);
        KimiStreamChatLanguageModel kimiStreamChatModel = KimiStreamChatLanguageModel.withUrl(kimiCloudEndpoint, kimi8KAppKey);

        streamingModelMap.put(LLMName.OPENAI_GPT4.name(), Map.of(TEXT, openAiTextStreamChatModel, JSON_OBJECT, openAiJSONStreamChatModel));
        streamingModelMap.put(LLMName.BAICHUAN_56B.name(), Map.of(TEXT, baichuanStreamChatModel));
        streamingModelMap.put(LLMName.KIMI.name(), Map.of(TEXT, kimiStreamChatModel));

        // chat sync
        ProxyOpenAiModerationModel openAIJsonModel = ProxyOpenAiModerationModel.builder()
                .proxy(runtimeEnv.isOffline() ? null : usSocksProxy)
                .apiKey(gpt4AppKey)
                .responseType(ResponseFormatType.JSON_OBJECT)
                .build();

        ProxyOpenAiModerationModel openAiTextModel = ProxyOpenAiModerationModel.builder()
                .proxy(runtimeEnv.isOffline() ? null : usSocksProxy)
                .apiKey(gpt4AppKey)
                .responseType(ResponseFormatType.TEXT)
                .build();

        BaichuanCloud56BModel baichuanCloud56BModel = BaichuanCloud56BModel.withUrl(baichuanCloudEndpoint, baichuan56BAppKey);
        KimiChatLanguageModel kimiModel = KimiChatLanguageModel.withUrl(kimiCloudEndpoint, kimi8KAppKey);

        chatModelMap.put(LLMName.OPENAI_GPT4.name(), Map.of(TEXT, new DelegatingChatLanguageModel(openAiTextModel, appAlertHelper),
                JSON_OBJECT, new DelegatingChatLanguageModel(openAIJsonModel, appAlertHelper)));
        chatModelMap.put(LLMName.BAICHUAN_56B.name(), Map.of(TEXT, baichuanCloud56BModel));
        chatModelMap.put(LLMName.KIMI.name(), Map.of(TEXT, kimiModel));
    }
}
