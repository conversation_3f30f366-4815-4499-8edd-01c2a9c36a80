/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version StoryRoleGeneration.java, v 0.1 2024-03-28 15:25 syoka
 */
@Getter
@AllArgsConstructor
public enum StoryRoleGender {

    MALE("男性"),
    FEMALE("女性"),
    ;
    private final String desc;

    public static StoryRoleGender ofDesc(String name) {
        for (StoryRoleGender roleGender : values()) {
            if (StringUtils.equals(roleGender.desc, name)) {
                return roleGender;
            }
        }
        return StoryRoleGender.MALE;
    }
}
