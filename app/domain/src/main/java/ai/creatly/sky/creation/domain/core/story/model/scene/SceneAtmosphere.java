/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.scene;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version SceneAtmosphere.java, v 0.1 2024-04-07 18:08 syoka
 */
@Data
public class SceneAtmosphere {
    @JsonProperty("key_location")
    private String keyLocation;
    private String tone;
    @JsonProperty("env_crowd_situation")
    private String envCrowdSituation;
}
