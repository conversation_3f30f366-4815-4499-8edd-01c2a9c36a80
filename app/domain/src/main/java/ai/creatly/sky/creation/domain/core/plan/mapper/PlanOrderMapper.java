/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.mapper;

import ai.creatly.kylin.trade.sdk.order.enums.OrderItemType;
import ai.creatly.kylin.trade.sdk.order.event.OrderPaidEvent;
import ai.creatly.kylin.trade.sdk.order.request.OrderCreateRequest;
import ai.creatly.kylin.trade.sdk.order.request.OrderItemDTO;
import ai.creatly.kylin.trade.sdk.order.response.OrderItemVO;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.benefit.log.model.BenefitLog;
import ai.creatly.sky.creation.domain.core.plan.error.PlanErrorCode;
import ai.creatly.sky.creation.domain.core.plan.helper.PlanBenefitFinder;
import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.PlanBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.PlanOrder;
import ai.creatly.sky.creation.domain.core.plan.model.enums.BenefitType;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import ai.creatly.sky.creation.domain.core.plan.model.response.PlanOrderBenefitVM;
import ai.creatly.sky.creation.domain.core.plan.model.response.PlanOrderVM;
import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.CreditsBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.MemberBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.TopUpMoreCreditsBenefit;
import ai.creatly.sky.creation.domain.support.trade.OrderBizSource;
import ai.creatly.sky.creation.domain.support.trade.TradeConstants;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.currency.MoneyUtil;
import com.jspeeder.core.util.json.JSON;
import jodd.util.StringPool;
import org.jetbrains.annotations.Nullable;
import org.json.JSONObject;
import org.mapstruct.Mapper;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 定价计划订单模型转换（定价计划抽象为付费商品，权益抽象为免费商品）
 *
 * <AUTHOR>
 * @version PlanOrderMapper.java, v 0.1 2023-10-16 下午5:53 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface PlanOrderMapper {

    /**
     * 构建购买订阅计划的下单请求
     *
     * @param plan        订阅计划
     * @param userContext 用户登录上下文
     * @return 下单请求
     */
    default OrderCreateRequest buildSubscriptionOrderCreateRequest(Plan plan, UserContext userContext) {
        // 订单收费项列表
        List<OrderItemDTO> orderItems = this.toSubscriptionOrderItems(plan);
        // 下单请求
        return this.buildOrderCreateRequest(plan, userContext, orderItems);
    }

    /**
     * 构建购买充值计划的下单请求
     *
     * @param plan             充值计划
     * @param subscriptionPlan 已购的会员订阅计划
     * @param userContext      用户登录上下文
     * @return 下单请求
     */
    default OrderCreateRequest buildTopUpOrderCreateRequest(Plan plan, UserContext userContext, @Nullable Plan subscriptionPlan) {
        // 订单收费项
        OrderItemDTO orderItem = this.toTopUpOrderItem(plan, subscriptionPlan);
        // 下单请求
        return this.buildOrderCreateRequest(plan, userContext, List.of(orderItem));
    }

    private OrderCreateRequest buildOrderCreateRequest(Plan plan, UserContext userContext, List<OrderItemDTO> items) {
        OrderCreateRequest order = new OrderCreateRequest();
        order.setTenantCode(TradeConstants.TENANT_CODE);
        // 一个用户购买同一个付费计划，在并发情况下，同1s内幂等 TODO 可以放开到同1分钟内幂等
        order.setBizSn(userContext.getouid() + "-" + plan.getId() + "-" + Instant.now().getEpochSecond());
        order.setBizSource(OrderBizSource.WEB.name());
        order.setBizType(plan.getType().getOrderBizType().name());
        order.setBizSubType(plan.getId());
        order.setTitle(plan.getOrderTitle());
        order.setDescription(plan.getDescription());
        order.setBuyerId(String.valueOf(userContext.getouid()));
        order.setBuyerName(userContext.getCurrentUsername());
        order.setSellerId(AppConstants.SYSTEM_UID.toString());
        order.setSellerName(AppConstants.SYSTEM_USERNAME);
        // TODO: 2023/10/16 业务配置化
        order.setValidMinutes(60);
        order.setItems(items);
        return order;
    }

    /**
     * 订阅计划转换为收费项列表
     *
     * @param plan 订阅计划
     * @return 收费项列表
     */
    private List<OrderItemDTO> toSubscriptionOrderItems(Plan plan) {
        Asserts.isTrue(plan.getType() == PlanType.SUBSCRIPTION, "will not happen");
        List<OrderItemDTO> orderItems = new ArrayList<>();

        // 订阅计划收费项
        OrderItemDTO planOrderItem = this.toPlanOrderItemDTO(plan, OrderItemType.SUBSCRIPTION);
        // 记录是否自动续费
        Asserts.notNull(plan.getAutoRenewed(), "will not happen");
        Map<String, Object> planOrderItemExtInfo = Map.of(PlanOrder.ITEM_EXT_KEY_AUTO_RENEWED, plan.getAutoRenewed());
        planOrderItem.setExtInfo(JSON.toJSONString(planOrderItemExtInfo));
        orderItems.add(planOrderItem);

        // 将订阅计划携带的权益作为免费商品加入订单中，冗余存储权益快照
        // 1.会员权益 和 余额权益
        PlanBenefitFinder benefitFinder = plan.benefitFinder();
        benefitFinder.findByTypes(BenefitType.MEMBER, BenefitType.CREDITS).forEach(planBenefit -> {
            OrderItemDTO orderItem = new OrderItemDTO();
            orderItem.setType(OrderItemType.BENEFIT);
            orderItem.setItemId(planBenefit.getCode());
            orderItem.setItemName(planBenefit.getName());
            orderItem.setItemDuration(plan.getBenefitDuration());
            orderItem.setUnitPrice(0L);
            orderItem.setRealUnitPrice(0L);
            orderItem.setQuantity(1);
            if (planBenefit.getType() == BenefitType.MEMBER) {
                Asserts.notNull(planBenefit.getValue(), "will not happen");
                var memberBenefit = JSON.parseObject(planBenefit.getValue(), MemberBenefit.class);
                var itemExtInfo = Map.of(PlanOrder.ITEM_EXT_KEY_AUTO_MEMBER_TYPE, memberBenefit.getMemberType());
                orderItem.setExtInfo(JSON.toJSONString(itemExtInfo));
            } else {
                orderItem.setExtInfo(planBenefit.getValue());
            }
            orderItems.add(orderItem);
        });

        // 2.充值送余额权益 和 能力权益
        benefitFinder.groupByTypes(BenefitType.TOP_UP_MORE_CREDITS, BenefitType.ABILITY).forEach((type, list) -> {
            OrderItemDTO orderItem = new OrderItemDTO();
            orderItem.setType(OrderItemType.BENEFIT);
            orderItem.setItemId(type.name());
            orderItem.setItemName(type.getDesc());
            orderItem.setItemDuration(plan.getBenefitDuration());
            orderItem.setUnitPrice(0L);
            orderItem.setRealUnitPrice(0L);
            orderItem.setQuantity(1);
            var benefits = list.stream()
                    .map(PlanBenefit::getValue)
                    .filter(Objects::nonNull)
                    .map(JSON::parseJSONObject)
                    .toList();
            var itemExtInfo = Map.of(PlanOrder.ITEM_EXT_KEY_AUTO_BENEFITS, benefits);
            orderItem.setExtInfo(JSON.toJSONString(itemExtInfo));
            orderItems.add(orderItem);
        });

        return orderItems;
    }

    /**
     * 充值计划转换为收费项
     *
     * @param plan             付费计划
     * @param subscriptionPlan 已购的会员订阅计划
     * @return 收费项
     */
    private OrderItemDTO toTopUpOrderItem(Plan plan, @Nullable Plan subscriptionPlan) {
        Asserts.isTrue(plan.getType() == PlanType.TOP_UP, "will not happen");

        // 将购买的余额作为收费项（包含会员在当前充值档位下赠送的余额）
        OrderItemDTO planOrderItem = this.toPlanOrderItemDTO(plan, OrderItemType.CASH);

        // 将余额权益设置到订单项扩展信息里
        var creditsBenefit = plan.benefitFinder()
                .findSingleByType(BenefitType.CREDITS, CreditsBenefit.class)
                .orElse(null);
        Asserts.notNull(creditsBenefit, PlanErrorCode.TOP_UP_PLAN_CONFIG_ERROR, plan.getId());
        if (subscriptionPlan != null) {
            // 如果用户已经是会员了，则找到会员订阅计划里在当前充值档位的充值送余额权益，将其赠送的余额追加进去
            subscriptionPlan.benefitFinder()
                    .findByType(BenefitType.TOP_UP_MORE_CREDITS, TopUpMoreCreditsBenefit.class)
                    .stream()
                    .filter(topUpMoreCreditsBenefit -> plan.getId().equals(topUpMoreCreditsBenefit.getTopUpPlanId()))
                    .findFirst()
                    .ifPresent(topUpMoreCreditsBenefit -> {
                        // 追加会员赠送的余额
                        Integer giftCredits = creditsBenefit.getGiftCredits() + topUpMoreCreditsBenefit.getGiftCredits();
                        creditsBenefit.setGiftCredits(giftCredits);
                        creditsBenefit.setTotalCredits(creditsBenefit.getTopUpCredits() + giftCredits);
                    });
            planOrderItem.setExtInfo(JSON.toJSONString(creditsBenefit));
        } else {
            // 如果不是会员直接充值，则无法享受赠送余额
            planOrderItem.setExtInfo(JSON.toJSONString(creditsBenefit));
        }

        return planOrderItem;
    }

    private OrderItemDTO toPlanOrderItemDTO(Plan plan, OrderItemType orderItemType) {
        OrderItemDTO planOrderItem = new OrderItemDTO();
        planOrderItem.setType(orderItemType);
        planOrderItem.setItemId(plan.getId());
        planOrderItem.setItemName(plan.getType() + StringPool.DASH + plan.getName());
        planOrderItem.setItemCategories(List.of(plan.getType().name()));
        planOrderItem.setItemDuration(plan.getBenefitDuration());
        planOrderItem.setUnitPrice(plan.getOriginalFee());
        planOrderItem.setRealUnitPrice(plan.getRealFee());
        planOrderItem.setQuantity(1);
        return planOrderItem;
    }

    default PlanOrderVM toPlanOrderVM(PlanOrder planOrder, @Nullable BenefitLog benefitLog) {
        Plan paidPlan = planOrder.getPaidPlan();
        PlanOrderVM planOrderVM = new PlanOrderVM()
                .setBizSn(planOrder.getBizSn())
                .setTitle(planOrder.getTitle())
                .setUid(planOrder.getUid().toString())
                .setOriginalFee(MoneyUtil.centToYuan(planOrder.getOriginalFee()))
                .setRealFee(MoneyUtil.centToYuan(planOrder.getRealFee()))
                .setPaidFee(MoneyUtil.centToYuan(planOrder.getPaidFee()))
                .setStatus(planOrder.getStatus())
                .setPaymentChannel(planOrder.getPaymentChannel())
                .setPaidTime(planOrder.getPaidTime())
                .setPlanId(paidPlan.getId())
                .setPlanType(paidPlan.getType());

        // 权益列表
        List<PlanOrderBenefitVM> benefits = new ArrayList<>();
        PlanBenefitFinder benefitFinder = paidPlan.benefitFinder();
        if (paidPlan.getType() == PlanType.SUBSCRIPTION) {
            // 购买会员的订单：返回会员权益+余额权益
            final String memberBenefitName = benefitFinder.findSingleByType(BenefitType.MEMBER)
                    .map(PlanBenefit::getName)
                    .orElse(null);
            Asserts.notNull(memberBenefitName, "will not happen!");
            benefits.add(this.toPlanOrderBenefitVM(BenefitType.MEMBER, memberBenefitName));
            // 兼容老结构
            planOrderVM.setBenefitType(BenefitType.MEMBER).setBenefitName(memberBenefitName);
        }

        // 余额权益，权益名称里动态加上实际获得的总余额数量（包含赠送部分的）
        PlanBenefit creditsBenefit = benefitFinder.findSingleByType(BenefitType.CREDITS).orElse(null);
        Asserts.notNull(creditsBenefit, "will not happen!");
        Asserts.notNull(creditsBenefit.getValue(), "will not happen!");
        CreditsBenefit creditsBenefitValue = JSON.parseObject(creditsBenefit.getValue(), CreditsBenefit.class);
        final String creditsBenefitName = creditsBenefitValue.getTotalCredits() + creditsBenefit.getName();
        benefits.add(this.toPlanOrderBenefitVM(BenefitType.CREDITS, creditsBenefitName));
        planOrderVM.setBenefits(benefits);

        if (paidPlan.getType() == PlanType.TOP_UP) {
            // 兼容老结构
            planOrderVM.setBenefitType(BenefitType.CREDITS).setBenefitName(creditsBenefitName);
        }

        if (benefitLog != null) {
            // 权益的生效和到期时间，以实际的权益发放记录为准
            planOrderVM.setBenefitEffectAt(benefitLog.getEffectAt());
            planOrderVM.setBenefitExpireAt(benefitLog.getExpireAt());
        }
        return planOrderVM;
    }

    private PlanOrderBenefitVM toPlanOrderBenefitVM(BenefitType benefitType, String benefitName) {
        return new PlanOrderBenefitVM().setBenefitType(benefitType).setBenefitName(benefitName);
    }

    default PlanOrder toPlanOrder(OrderPaidEvent event, Plan currentPlan) {
        Plan paidPlan = this.toPaidPlan(currentPlan, event.getItems());
        return new PlanOrder()
                .setId(event.getId())
                .setBizSn(event.getBizSn())
                .setTitle(event.getTitle())
                .setUid(Long.valueOf(event.getBuyerId()))
                .setOriginalFee(event.getOriginalFee())
                .setRealFee(event.getRealFee())
                .setPaidFee(event.getPaidFee())
                .setStatus(event.getStatus())
                .setPaymentChannel(event.getPaymentChannel())
                .setPaidTime(event.getPaidTime())
                .setPaidPlan(paidPlan);
    }

    /**
     * 从订单收费项还原回付费计划（价格和发放的余额可能会有更新，因为它们在下单的那一刻已经确定，即使plan更新了，也以下单那一刻为准）
     *
     * @param currentPlan 当前最新的付费计划
     * @param orderItems  订单收费项列表
     * @return 付费计划
     */
    default Plan toPaidPlan(Plan currentPlan, List<OrderItemVO> orderItems) {
        Plan paidPlan = this.copy(currentPlan);

        PlanBenefit creditsPlanBenefit = paidPlan.benefitFinder()
                .findSingleByType(BenefitType.CREDITS)
                .orElse(null);
        // 不管是充值还是订阅，都会有余额权益
        Asserts.notNull(creditsPlanBenefit, "will not happen");

        // 找到针对付费计划的主收费项，还原购买时的费用、权益有效期、获得的余额
        OrderItemVO planOrderItemVO = orderItems.stream()
                .filter(orderItemVO -> Objects.nonNull(orderItemVO.getItemId()))
                .filter(orderItemVO -> orderItemVO.getItemId().equals(paidPlan.getId()))
                .findFirst()
                .orElse(null);
        Asserts.notNull(planOrderItemVO, "will not happen");
        paidPlan.setBenefitDuration(planOrderItemVO.getItemDuration());
        paidPlan.setOriginalFee(planOrderItemVO.getUnitPrice());
        paidPlan.setRealFee(planOrderItemVO.getRealUnitPrice());
        switch (paidPlan.getType()) {
            case SUBSCRIPTION -> {
                Asserts.isTrue(planOrderItemVO.getType() == OrderItemType.SUBSCRIPTION, "will not happen");
                // 针对订阅，还原购买时是否自动续费
                JSONObject itemExtInto = JSON.parseJSONObject(planOrderItemVO.getExtInfo());
                boolean autoRenewed = itemExtInto.optBoolean(PlanOrder.ITEM_EXT_KEY_AUTO_RENEWED);
                paidPlan.setAutoRenewed(autoRenewed);
                // 针对订阅，找到下单时赠送的余额权益，还原回去
                orderItems.stream()
                        .filter(orderItemDTO -> orderItemDTO.getType() == OrderItemType.BENEFIT)
                        .filter(orderItemVO -> Objects.nonNull(orderItemVO.getItemId()))
                        .filter(orderItemDTO -> orderItemDTO.getItemId().equals(creditsPlanBenefit.getCode()))
                        .findFirst()
                        .ifPresent(orderItemDTO -> creditsPlanBenefit.setValue(orderItemDTO.getExtInfo()));
            }
            case TOP_UP -> {
                Asserts.isTrue(planOrderItemVO.getType() == OrderItemType.CASH, "will not happen");
                // 针对充值，还原购买时发放的余额
                creditsPlanBenefit.setValue(planOrderItemVO.getExtInfo());
            }
            default -> throw new SysException(CommonErrorCode.UNSPECIFIED);
        }

        return paidPlan;
    }

    Plan copy(Plan plan);

    List<PlanBenefit> copyPlanBenefits(List<PlanBenefit> planBenefits);

    PlanBenefit copy(PlanBenefit planBenefit);
}
