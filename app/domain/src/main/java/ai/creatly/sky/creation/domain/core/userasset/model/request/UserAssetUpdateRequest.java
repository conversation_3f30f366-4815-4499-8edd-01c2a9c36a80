/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userasset.model.request;

import ai.creatly.sky.creation.domain.support.label.model.request.LabelRefDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version UserAssetUpdateRequest.java, v 0.1 2024-09-29 下午2:31 zhoudong
 */
@Data
public class UserAssetUpdateRequest {
    @Size(min = 1, max = 100)
    private String            name;
    @Size(max = 20)
    @Valid
    private List<LabelRefDTO> labels;
}
