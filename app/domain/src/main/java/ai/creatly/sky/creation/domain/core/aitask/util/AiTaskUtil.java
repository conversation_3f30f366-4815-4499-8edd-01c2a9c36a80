/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.util;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskResult;
import lombok.experimental.UtilityClass;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version AiTaskUtil.java, 2024-11-26 上午10:44 zhoudong
 */
@UtilityClass
public class AiTaskUtil {

    public String findCancelReason(List<AiTask> tasks, String defaultReason) {
        return tasks.stream()
                .filter(AiTask::isCanceled)
                .map(AiTask::getSysResult)
                .map(AiTaskResult::getCancelReason)
                .filter(Objects::nonNull)
                .findAny()
                .orElse(defaultReason);
    }
}
