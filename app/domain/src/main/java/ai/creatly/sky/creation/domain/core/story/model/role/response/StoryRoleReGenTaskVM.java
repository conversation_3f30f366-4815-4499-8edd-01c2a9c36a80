/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.response;

import ai.creatly.sky.creation.domain.core.userfile.model.response.BizFileVM;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version StoryRoleReGenTaskVM.java, v 0.1 2024-03-30 17:25 syoka
 */
@Data
@Accessors(chain = true)
public class StoryRoleReGenTaskVM {

    private String          status;
    private List<BizFileVM> files;
}
