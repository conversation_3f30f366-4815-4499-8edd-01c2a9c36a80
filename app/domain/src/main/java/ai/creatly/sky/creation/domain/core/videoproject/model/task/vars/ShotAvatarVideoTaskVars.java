/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.task.vars;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizVars;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @version ShotAvatarVideoTaskVars.java, v 0.1 2024-09-26 下午5:37 zhoudong
 */
@Data
@Accessors(chain = true)
public class ShotAvatarVideoTaskVars implements TaskBizVars {

    /**
     * 三方视频ID
     */
    private String outVideoId;
    /**
     * 绿幕视频文件ID
     */
    @Nullable
    private Long   greenVideoFileId;
    /**
     * 绿幕视频文件地址
     */
    @Nullable
    private String greenVideoUrl;
    /**
     * 遮罩视频文件ID
     */
    @Nullable
    private Long   maskVideoFileId;
    /**
     * 遮罩视频地址
     */
    @Nullable
    private String maskVideoUrl;
}
