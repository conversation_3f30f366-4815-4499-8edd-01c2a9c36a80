/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aittalk.mapper;

import ai.creatly.sky.creation.domain.core.aittalk.model.AiTalkEmotion;
import ai.creatly.sky.creation.domain.core.aittalk.model.response.AiTalkEmotionVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version AiTalkEmotionMapper.java, v 0.1 2023-06-24 01:25 joton
 */
@Mapper(config = BaseMapperConfig.class)
public interface AiTalkEmotionMapper {

    AiTalkEmotionVM toAiTalkEmotionVM(AiTalkEmotion aiTalkEmotion);
}
