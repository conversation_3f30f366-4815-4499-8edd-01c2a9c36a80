/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.model.response;

import ai.creatly.sky.creation.domain.core.userasset.model.response.UserAssetVM;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version UploadUserFileVM.java, 2024-10-31 下午3:44 zhoudong
 */
@Data
@Accessors(chain = true)
public class UploadUserFileVM extends UserAssetVM {
    /**
     * 上传的素材是否已存在（文件MD5值相同）
     */
    private Boolean exists;
}
