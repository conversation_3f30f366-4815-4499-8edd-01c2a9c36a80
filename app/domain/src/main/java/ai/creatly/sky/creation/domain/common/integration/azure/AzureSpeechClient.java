/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.azure;

import ai.creatly.sky.creation.domain.common.integration.azure.model.AudioQuality;
import ai.creatly.sky.creation.domain.support.multimedia.model.AudioMetadata;

import java.io.OutputStream;

/**
 * <AUTHOR>
 * @version AzureSpeechClient.java, v 0.1 2023-06-16 15:05 joton
 */
public interface AzureSpeechClient {

    /**
     * 开始语音合成
     *
     * @param ssml           语音合成标记语言
     * @param estimatedMills 预估合成耗时（根据预估的耗时动态设置调用TTS的耗时）
     * @param quality        音频输出质量
     * @param output         输出流（实时写入）
     * @return 音频元数据
     */
    AudioMetadata textToSpeech(String ssml, long estimatedMills, AudioQuality quality, OutputStream output);
}
