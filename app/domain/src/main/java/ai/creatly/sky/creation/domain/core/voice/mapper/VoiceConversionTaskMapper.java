/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.mapper;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.response.BaseUserAiTaskVM;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.response.task.VoiceConversionTaskVM;
import ai.creatly.sky.creation.domain.core.voice.model.task.param.VoiceConversionParams;
import ai.creatly.sky.creation.domain.core.voice.model.task.result.VoiceTaskResult;
import ai.creatly.sky.creation.domain.core.voice.util.VoiceUtil;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.time.Duration;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version VoiceConversionTaskMapper.java, v 0.1 2023-12-03 上午12:09 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface VoiceConversionTaskMapper extends VoiceTaskBaseMapper {

    default AiTask toVoiceConversionTask(Voice voice, String bizType, UserFile inputAudioFile, UserContext userContext) {
        Duration inputAudioDuration = Objects.requireNonNull(inputAudioFile.getMetadata().getDuration());
        VoiceConversionParams params = new VoiceConversionParams()
                .setInputAudioId(inputAudioFile.getId())
                .setInputAudioUrl(UserFileHelper.toOssUrl(inputAudioFile))
                .setInputAudioDuration(inputAudioDuration)
                .setEstimatedConversionCost(VoiceUtil.estimateConversionCost(inputAudioDuration));
        return this.toVoiceTask(voice, bizType, userContext, params);
    }

    default VoiceConversionTaskVM toVoiceConversionTaskVM(AiTask task, Voice voice, UserFileHelper userFileHelper) {
        VoiceConversionParams params = task.parseBizParams(VoiceConversionParams.class);
        VoiceTaskResult result = task.parseBizResult(VoiceTaskResult.class);

        VoiceConversionTaskVM voiceConversionTask = new VoiceConversionTaskVM();
        this.fillBaseUserAiTaskVM(task, voiceConversionTask);
        return voiceConversionTask.setInputAudioUrl(userFileHelper.getHttpUrl(params.getInputAudioUrl(), task.getOwnerId()))
                .setVoiceId(voice.getId().toString())
                .setVoiceCode(voice.getCode())
                .setAudioName(result.getAudioName())
                .setAudioExtension(result.getAudioExtension())
                .setAudioUrl(userFileHelper.getHttpUrl(result.getAudioUrl(), task.getOwnerId()));
    }

    @Mapping(target = "finishedDurationSeconds", source = "sysResult.finishedDuration.seconds")
    @Mapping(target = "estimatedDurationSeconds", source = "estimatedDuration.seconds")
    void fillBaseUserAiTaskVM(AiTask task, @MappingTarget BaseUserAiTaskVM baseUserAiTaskVM);
}
