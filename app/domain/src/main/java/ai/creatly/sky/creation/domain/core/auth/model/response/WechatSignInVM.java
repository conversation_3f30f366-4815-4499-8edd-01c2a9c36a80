/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.auth.model.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version WechatSignInVM.java, v 0.1 2024-04-16 17:30 syoka
 */
@Data
@Accessors(chain = true)
public class WechatSignInVM {

    /**
     * 用户使用微信扫码认证后，可能有 3 种情况：
     * </p>
     * <ol>
     *  <li>是一个已注册的微信用户，且已绑定手机号（isNew = false）</li>
     *  <li>是一个已注册的微信用户，但未绑定手机号（isNew = true）</li>
     *  <li>是一个新用户（isNew = true）</li>
     * </ol>
     */
    private Boolean isNew;
    /**
     * 如果是已注册微信用户，则返回真实登录令牌；如果是新微信用户，则返回临时的匿名令牌，用于下一步验证手机号进行注册或绑定
     */
    private String  token;
}
