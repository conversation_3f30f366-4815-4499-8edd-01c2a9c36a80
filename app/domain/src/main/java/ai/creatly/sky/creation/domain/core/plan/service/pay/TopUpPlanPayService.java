/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.service.pay;

import ai.creatly.kylin.trade.sdk.order.client.TradeOrderClient;
import ai.creatly.kylin.trade.sdk.order.request.OrderCreateRequest;
import ai.creatly.kylin.trade.sdk.order.response.OrderDetailVO;
import ai.creatly.kylin.trade.sdk.order.response.OrderItemVO;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.benefit.log.model.BenefitSourceType;
import ai.creatly.sky.creation.domain.core.benefit.log.repository.BenefitLogRepository;
import ai.creatly.sky.creation.domain.core.benefit.model.CreditBenefitContent;
import ai.creatly.sky.creation.domain.core.benefit.service.BenefitService;
import ai.creatly.sky.creation.domain.core.member.model.Member;
import ai.creatly.sky.creation.domain.core.member.respository.MemberRepository;
import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.PlanBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.PlanOrder;
import ai.creatly.sky.creation.domain.core.plan.model.enums.BenefitType;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.CreditsBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.specific.payment.PlanWxNativePrepayResult;
import ai.creatly.sky.creation.domain.core.plan.service.PlanService;
import ai.creatly.sky.creation.domain.support.trade.TradeConstants;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * 充值计划支付服务
 *
 * <AUTHOR>
 * @version TopUpPlanPayService.java, v 0.1 2023-10-19 下午3:53 zhoudong
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TopUpPlanPayService extends AbstractPlanPayService implements PlanPayService {

    private final MemberRepository     memberRepository;
    private final BenefitLogRepository benefitLogRepository;
    private final BenefitService       benefitService;
    private final PlanService          planService;
    private final TradeOrderClient     tradeOrderClient;

    @Override
    public PlanWxNativePrepayResult doWxNativePay(Plan plan, String payerClientIp, UserContext userContext) {
        Asserts.isTrue(plan.getType() == PlanType.TOP_UP, "will not happen!");

        // 会员校验
        long uid = userContext.getouid();
        Member member = memberRepository.queryActiveByUid(uid).orElse(null);

        // 订单幂等校验
        // 当存在待付款的本次充值订单的情况下，如果未超时（5分钟），则幂等返回；否则关单，再重新下单并支付
        var orderQueryParam = this.buildIdempotentCheckOrderQueryParam(uid, PlanType.TOP_UP.name(), plan.getId());
        // 理论上一个用户对于同一个充值档位只会有一笔待支付的充值订单
        // 极端并发情况可能会出现多笔待支付订单。再进一步极端情况，这多笔订单可能都会支付成功，这种情况没关系，就视为多次充值行为就可以了。
        PlanWxNativePrepayResult planWxPrePayResult = this.checkOrderAndPayIdempotent(orderQueryParam, plan);
        if (planWxPrePayResult != null) {
            return planWxPrePayResult;
        }

        // 获取当前会员已购的订阅计划
        Plan subscriptionPlan = null;
        if (member != null) {
            subscriptionPlan = planService.queryByIdWithBenefits(member.getPlanId()).orElse(null);
            Asserts.notNull(subscriptionPlan, "会员的订阅计划不存在，memberId:{}", member.getId());
        }

        // 下单并支付
        OrderCreateRequest order = planOrderMapper.buildTopUpOrderCreateRequest(plan, userContext, subscriptionPlan);
        return this.doWxNativePayAfterCheck(order, payerClientIp);
    }

    @Override
    public void shipOnPaid(PlanOrder planOrder) {
        BenefitSourceType sourceType = BenefitSourceType.TOP_UP_PLAN_ORDER;
        String sourceId = String.valueOf(planOrder.getId());
        // 幂等判断
        if (benefitLogRepository.queryOptionalBySource(sourceType, sourceId).isPresent()) {
            log.warn("[shipOnPaid][idempotent]sourceType={},sourceId={}", sourceType, sourceId);
            // 将订单推进到发货
            this.shipOrder(planOrder.getId());
            return;
        }

        // 发放余额权益
        Plan paidPlan = planOrder.getPaidPlan();
        PlanBenefit planBenefit = paidPlan.benefitFinder().findSingleByType(BenefitType.CREDITS).orElse(null);
        Asserts.notNull(planBenefit, "余额充值一定有余额权益");
        Asserts.notNull(planBenefit.getValue(), "余额充值一定有余额权益");
        var creditsBenefit = JSON.parseObject(planBenefit.getValue(), CreditsBenefit.class);
        if (creditsBenefit.getTopUpCredits() == null) {
            // 兼容脏数据
            OrderDetailVO orderDetail = tradeOrderClient.queryOrderDetail(TradeConstants.TENANT_CODE, planOrder.getId());
            OrderItemVO planOrderItemVO = orderDetail.getItems()
                    .stream()
                    .filter(orderItemVO -> Objects.nonNull(orderItemVO.getItemId()))
                    .filter(orderItemVO -> orderItemVO.getItemId().equals(paidPlan.getId()))
                    .findFirst()
                    .orElse(null);
            Asserts.notNull(planOrderItemVO, "will not happen");

            // 针对充值，还原购买时发放的余额
            planBenefit.setValue(planOrderItemVO.getExtInfo());

            creditsBenefit = JSON.parseObject(planBenefit.getValue(), CreditsBenefit.class);
            Asserts.notNull(creditsBenefit.getTopUpCredits(), "will not happen");
        }

        // 权益获得时间（不等于支付时间）
        ZonedDateTime receiveAt = ZonedDateTime.now();

        // 计算到期时间
        Duration benefitDuration = paidPlan.getBenefitDuration();
        Asserts.notNull(benefitDuration, "目前不支持充值终身有效的余额");
        ZonedDateTime expireAt = receiveAt.plus(benefitDuration);

        var benefitContent = new CreditBenefitContent()
                .setGrantCredits(creditsBenefit.getTotalCredits())
                .setSourceType(sourceType)
                .setSourceId(sourceId)
                .setBenefitCode(planBenefit.getCode())
                .setBenefitName(planBenefit.getName())
                .setBenefitValue(JSON.parseJSONObject(planBenefit.getValue()))
                .setPlanId(paidPlan.getId())
                .setPaidFee(planOrder.getPaidFee())
                .setReceiveAt(receiveAt)
                // 收货即生效
                .setEffectAt(receiveAt)
                .setExpireAt(expireAt);

        benefitService.grantBenefit(planOrder.getUid(), BenefitType.CREDITS, benefitContent);

        // 将订单推进到发货
        this.shipOrder(planOrder.getId());
    }
}
