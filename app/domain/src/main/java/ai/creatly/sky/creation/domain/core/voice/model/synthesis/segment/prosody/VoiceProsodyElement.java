/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment.prosody;

import ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment.VoiceBookmark;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version VoiceProsodyElement.java, v 0.1 2024-08-16 下午3:17 zhoudong
 */
@Data
@Accessors(chain = true)
public class VoiceProsodyElement {

    /**
     * 局部韵律下的内容片段类型（根据不同的类型，以下字段取对应的一个即可）
     */
    @NotNull
    private VoiceProsodyElementType type;
    /**
     * 纯文本（前后会去除空白字符和换行）
     */
    @Size(max = 30, message = "韵律调节的局部文本过长，不能超过{max}个字符")
    private String                  text;
    /**
     * 书签标记
     */
    @Valid
    private VoiceBookmark           bookmark;
}
