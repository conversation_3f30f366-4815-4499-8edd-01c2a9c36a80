/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.aichat.langchain.model;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 大模型枚举
 */
@Getter
public enum LLMName {

    OPENAI_GPT4,
    WENXINYIYAN,
    BAICHUAN_13B,
    BAICHUAN_56B,
    KIMI;

    public static LLMName ofName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (LLMName llmName : values()) {
            if (StringUtils.equalsIgnoreCase(llmName.name(), name)) {
                return llmName;
            }
        }
        return null;
    }
}
