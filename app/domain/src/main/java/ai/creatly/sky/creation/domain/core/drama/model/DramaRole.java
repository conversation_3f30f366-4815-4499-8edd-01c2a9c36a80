/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.model;

import ai.creatly.sky.creation.domain.core.userfile.model.FileRef;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 剧情角色
 *
 * <AUTHOR>
 * @version DramaRole.java, 2024-10-21 下午12:01 zhoudong
 */
@Data
@Accessors(chain = true)
public class DramaRole {

    /**
     * 角色名称（默认为数组下标）
     */
    private String               name;
    /**
     * 角色关联的音频列表
     */
    private List<DramaRoleAudio> audios;
    /**
     * 角色关联的声音模型文件
     */
    private FileRef              voiceModel;
    /**
     * 是否作为产品代言人
     */
    private Boolean              isSpokesperson;
}
