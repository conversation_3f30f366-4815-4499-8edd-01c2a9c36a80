/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model;

import ai.creatly.sky.creation.domain.core.plan.helper.PlanBenefitFinder;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanBuyMethod;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanPeriodType;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanSource;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;
import java.util.List;

/**
 * 定价计划
 *
 * <AUTHOR>
 * @version Plan.java, v 0.1 2023-10-10 下午11:11 zhoudong
 */
@Data
@Accessors(chain = true)
public class Plan {

    /**
     * 计划ID（全局唯一）
     */
    private String         id;
    /**
     * 计划类型
     */
    private PlanType       type;
    /**
     * 计划来源
     */
    private PlanSource     source;
    /**
     * 关联的用户ID（当计划来源为用户定制时才有值）
     */
    @Nullable
    private Long           uid;
    /**
     * 计划名称
     */
    private String         name;
    /**
     * 计划描述
     */
    private String         description;
    /**
     * 购买方式
     */
    private PlanBuyMethod  buyMethod;
    /**
     * 原价（分）
     */
    @Nullable
    private Long           originalFee;
    /**
     * 实价（分）
     */
    @Nullable
    private Long           realFee;
    /**
     * 订单标题（下单时使用）
     */
    @Nullable
    private String         orderTitle;
    /**
     * 计划周期类型
     */
    private PlanPeriodType periodType;
    /**
     * 到期是否自动续费
     */
    @Nullable
    private Boolean        autoRenewed;
    /**
     * 计划阶梯档位（用于排序、有序展示）
     */
    private Integer        level;
    /**
     * 权益有效期（为空则表示权益终身有效）
     */
    @Nullable
    private Duration       benefitDuration;

    /*----------------------关联数据----------------------*/
    /**
     * 关联的权益列表
     */
    private List<PlanBenefit> benefits;

    /**
     * 构建权益查找器
     *
     * @return 权益查找器
     */
    public PlanBenefitFinder benefitFinder() {
        return PlanBenefitFinder.of(this.benefits);
    }
}
