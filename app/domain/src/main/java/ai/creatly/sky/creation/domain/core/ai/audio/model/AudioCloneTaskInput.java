/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.audio.model;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizInput;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version AudioCloneTaskInput.java, v0.1 2025-02-24 14:41
 */
@Data
@Accessors(chain = true)
public class AudioCloneTaskInput implements TaskBizInput {

    private String          url;
    private AudioCloneRequest request;
}
