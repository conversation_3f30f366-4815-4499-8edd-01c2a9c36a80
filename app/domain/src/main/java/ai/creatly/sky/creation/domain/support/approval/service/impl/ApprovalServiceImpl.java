/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.approval.service.impl;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskQO;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskHelper;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskService;
import ai.creatly.sky.creation.domain.support.approval.mapper.ApprovalTaskMapper;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalBizType;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalStatus;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalTask;
import ai.creatly.sky.creation.domain.support.approval.service.ApprovalService;
import ai.creatly.sky.creation.domain.support.approval.task.ApprovalTaskInput;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version ApprovalServiceImpl.java, 2024-10-29 上午11:29 zhoudong
 */
@RequiredArgsConstructor
@Service
public class ApprovalServiceImpl implements ApprovalService {

    private final AiTaskService       aiTaskService;
    private final AiTaskRepository    aiTaskRepository;
    private final AiTaskHelper        aiTaskHelper;
    private final ApprovalTaskMapper  approvalTaskMapper;
    private final TransactionTemplate transactionTemplate;

    @Override
    public Page<ApprovalTask> queryPage(ApprovalStatus status, Pageable pageable) {
        AiTaskQO qo = new AiTaskQO()
                .setTaskType(AiTaskType.APPROVAL)
                .setBizStatus(status.name());
        return aiTaskRepository.queryPage(qo, pageable).map(approvalTaskMapper::toApprovalTask);
    }

    @Override
    public Optional<ApprovalTask> queryOptionalById(long taskId) {
        return aiTaskRepository.queryOptionalById(taskId).map(approvalTaskMapper::toApprovalTask);
    }

    @Override
    public ApprovalTask queryByBizId(ApprovalBizType bizType, long bizId) {
        AiTask task = aiTaskRepository.queryByUnique(AiTaskType.APPROVAL, bizType.name(), String.valueOf(bizId)).orElse(null);
        Validates.notNull(task, "审批任务不存在");
        return approvalTaskMapper.toApprovalTask(task);
    }

    @Override
    public long submit(ApprovalTask task) {
        // 发起一个审批任务
        ApprovalTaskInput taskInput = new ApprovalTaskInput();
        AiTask aiTask = AiTask.buildNew()
                .owner(task.getApplicantId(), task.getApplicantName())
                .creator(task.getCreator())
                .taskType(AiTaskType.APPROVAL)
                .bizType(task.getBizType())
                .bizNo(task.getBizNo())
                .subBizNo(task.getSubBizNo())
                .bizInput(taskInput)
                .bizStatus(ApprovalStatus.pending.name())
                .notAutoExec()
                .build();
        return aiTaskService.submit(aiTask);
    }

    @Override
    public void accept(long taskId) {
        aiTaskRepository.updateBizStatus(taskId, ApprovalStatus.pending.name(), ApprovalStatus.ongoing.name());
    }

    @Override
    public void approve(long taskId) {
        transactionTemplate.executeWithoutResult(status -> {
            aiTaskRepository.updateBizStatus(taskId, ApprovalStatus.ongoing.name(), ApprovalStatus.approved.name());
            aiTaskRepository.updateAutoExec(taskId, true);
        });

        // 异步触发任务执行
        aiTaskHelper.asyncExecute(taskId);
    }

    @Override
    public void reject(long taskId) {
        transactionTemplate.executeWithoutResult(status -> {
            aiTaskRepository.updateBizStatus(taskId, ApprovalStatus.ongoing.name(), ApprovalStatus.rejected.name());
            aiTaskRepository.updateAutoExec(taskId, true);
        });

        // 异步触发任务执行
        aiTaskHelper.asyncExecute(taskId);
    }
}
