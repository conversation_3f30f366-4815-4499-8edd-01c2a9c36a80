/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.model.task.process;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Duration;

/**
 * <AUTHOR>
 * @version VideoSubtitleContent.java, v 0.1 2024-07-15 15:01 syoka
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class VideoSubtitleContent {
    private String   text;
    private Duration start;
    private Duration end;

    public VideoSubtitleContent setStartSeconds(String startSeconds) {
        this.start = Duration.ofMillis(new BigDecimal(startSeconds).multiply(new BigDecimal(1000)).longValue());
        return this;
    }

    public VideoSubtitleContent setEndSeconds(String endSeconds) {
        this.end = Duration.ofMillis(new BigDecimal(endSeconds).multiply(new BigDecimal(1000)).longValue());
        return this;
    }

    /*--------------------- 这两个废弃的方法仅用于兼容历史数据从数据库反序列化出来 ---------------------*/

    @Deprecated
    public void setStartTime(String startTime) {
        this.setStartSeconds(startTime);
    }

    @Deprecated
    public void setEndTime(String endTime) {
        this.setEndSeconds(endTime);
    }
}
