/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.azure.model.ssml;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version Speak.java, v 0.1 2023-06-06 22:33 joton
 */
@Data
@JacksonXmlRootElement(localName = "speak")
public class Speak {
    @JacksonXmlProperty(isAttribute = true)
    private final String version = "1.0";

    @JacksonXmlProperty(isAttribute = true)
    private final String xmlns = "http://www.w3.org/2001/10/synthesis";

    @JacksonXmlProperty(isAttribute = true, localName = "xml:lang")
    private final String lang;

    @JacksonXmlProperty(isAttribute = true, localName = "xmlns:mstts")
    private String mstts;

    @JacksonXmlProperty(localName = "mstts:backgroundaudio")
    private SpeakBackgroundAudio backgroundAudio;

    @JacksonXmlProperty(localName = "voice")
    @JacksonXmlElementWrapper(useWrapping = false)
    private final List<SpeakVoice> voices = new ArrayList<>();

    /**
     * 添加一段声音
     *
     * @param speakVoice 一段声音
     * @return this
     */
    public Speak addVoice(SpeakVoice speakVoice) {
        if (speakVoice.getExpression() != null) {
            this.mstts = "https://www.w3.org/2001/mstts";
        }
        voices.add(speakVoice);
        return this;
    }
}
