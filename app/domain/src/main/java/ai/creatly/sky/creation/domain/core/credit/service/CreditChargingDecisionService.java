/**
 * Copyright For creatly
 *
 * <AUTHOR>
 * @version CreditChargingDecisionService.java, v 0.1 2023-08-19 23:03
 */
package ai.creatly.sky.creation.domain.core.credit.service;

import ai.creatly.sky.creation.domain.core.credit.model.UserCreditAccount;
import org.springframework.stereotype.Service;

/**
 * 计费决策模型
 * 默认计费模型 按月》按量
 */
@Service
public class CreditChargingDecisionService {

    /**
     * 决策余额支付模型
     */
    UserCreditAccount decideCreditChannel(String uid) {
        return null;
    }

}
