/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.auth.model.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 爱影登录请求（如果检测为新用户，自动完成注册）
 *
 * <AUTHOR>
 * @version StudioSignInRequest.java, v 0.1 2024-04-17 09:55 syoka
 */
@Data
public class StudioSignInRequest {

    /**
     * 手机号（根据手机号查询用户，不存在则走注册流程，存在则走登录流程）
     */
    @NotBlank
    private String phone;
    /**
     * 手机验证码
     */
    @NotBlank
    private String smsCode;
    /**
     * 邀请注册码（只对新注册用户生效）
     */
    private String inviteCode;
}
