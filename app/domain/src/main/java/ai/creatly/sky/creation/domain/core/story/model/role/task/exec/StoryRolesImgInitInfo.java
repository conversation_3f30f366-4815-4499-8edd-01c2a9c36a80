/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.task.exec;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizExecInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version StoryRolesImgInitInfo.java, v 0.1 2024-05-01 上午12:28 zhoudong
 */
@Data
@Accessors(chain = true)
public class StoryRolesImgInitInfo implements TaskBizExecInfo {

    /**
     * 已初始化的角色图列表（4合1原图）
     */
    private List<StoryRoleSourceImage> sourceImages;
}
