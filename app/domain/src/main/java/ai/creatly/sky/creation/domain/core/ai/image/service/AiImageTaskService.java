/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.service;

import ai.creatly.sky.creation.domain.core.ai.image.mapper.AiImageTaskMapper;
import ai.creatly.sky.creation.domain.core.ai.image.model.AiImageTask;
import ai.creatly.sky.creation.domain.core.aitask.error.AiTaskErrorCode;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @version AiImageTaskService.java, v0.1 2025-02-24 16:50
 */
@Service
@RequiredArgsConstructor
public class AiImageTaskService {

    private final AiTaskRepository  aiTaskRepository;
    private final AiImageTaskMapper aiImageTaskMapper;

    public AiImageTask queryGenerationStatus(long uid, long taskId) {
        AiTask aiTask = aiTaskRepository.queryOptionalById(taskId).orElse(null);
        Validates.notNull(aiTask, AiTaskErrorCode.TASK_NOT_EXISTS);
        Validates.isTrue(aiTask.belongsTo(uid), AiTaskErrorCode.TASK_NOT_EXISTS);
        return aiImageTaskMapper.toAiImageTask(aiTask);
    }
}
