/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.caching;

import ai.creatly.sky.creation.domain.common.util.AppConstants;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.experimental.UtilityClass;

import java.util.Arrays;
import java.util.Objects;

import static java.util.stream.Collectors.joining;

/**
 * <AUTHOR>
 * @version CacheUtil.java, v 0.1 2024-01-23 下午7:28 zhoudong
 */
@UtilityClass
public class CacheUtil {

    public String cacheKey(String keyPrefix, Object... args) {
        if (args == null || args.length == 0) {
            return AppConstants.APP_NAME + ":" + keyPrefix;
        }
        String argsKey = Arrays.stream(args).map(Objects::toString).collect(joining(CacheConstants.ARGS_DELIMITER));
        return FormatUtil.format("{}:{}{}", AppConstants.APP_NAME, keyPrefix, argsKey);
    }
}
