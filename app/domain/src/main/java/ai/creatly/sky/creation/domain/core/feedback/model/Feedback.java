/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.feedback.model;

import com.jspeeder.core.model.BizStatus;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class Feedback {

    /**
     * 反馈id
     */
    private Long                     id;
    /**
     * 创建时间
     */
    private ZonedDateTime            createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime            updatedAt;
    /**
     * 反馈用户ID
     */
    private Long                     uid;
    /**
     * 反馈用户名
     */
    private String                   username;
    /**
     * 用户手机号
     */
    private String                   phone;
    /**
     * 用户邮箱
     */
    @Nullable
    private String                   email;
    /**
     * 反馈标题
     */
    private String                   title;
    /**
     * 详细内容
     */
    private String                   detail;
    /**
     * 状态信息
     */
    private BizStatus                status;
    /**
     * 附件列表（如图、视频等）
     */
    private List<FeedbackAttachment> attachments;
}
