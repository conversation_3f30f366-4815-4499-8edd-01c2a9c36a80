/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userasset.model;

import ai.creatly.sky.creation.domain.core.userasset.model.enums.AssetType;
import ai.creatly.sky.creation.domain.support.label.model.LabelRef;
import com.jspeeder.core.model.BizStatus;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 用户素材
 *
 * <AUTHOR>
 * @version UserAsset.java, v 0.1 2024-09-21 下午3:22 zhoudong
 */
@Data
@Accessors(chain = true)
public class UserAsset {

    /**
     * 主键ID
     */
    private Long                  id;
    /**
     * 创建时间
     */
    private ZonedDateTime         createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime         updatedAt;
    /**
     * 用户ID
     */
    private Long                  uid;
    /**
     * 素材状态（无效即为软删除）
     */
    private BizStatus             status;
    /**
     * 素材名称（默认取文件名，可修改）
     */
    private String                name;
    /**
     * 素材类型
     */
    private AssetType             type;
    /**
     * 素材文件
     */
    private AssetFile             file;
    /**
     * 素材内容
     */
    private AssetContent          content;
    /**
     * 素材封面图文件ID
     */
    @Nullable
    private Long                  coverFileId;
    /**
     * 素材封面图地址
     */
    @Nullable
    private String                coverUrl;
    /**
     * 素材详细描述
     */
    private String                description;
    /**
     * 素材元数据
     */
    private UserAssetMetadata     metadata;
    /**
     * 素材关键词列表
     */
    private List<String>          keywords;
    /**
     * 素材标签列表
     */
    private List<LabelRef>        labels;
    /**
     * 视频素材切片列表（仅针对视频素材）
     */
    private List<AssetVideoSlice> videoSlices;

    /**
     * 是否属于某用户
     *
     * @param uid 用户ID
     * @return 是否属于
     */
    public boolean belongsTo(long uid) {
        return Objects.equals(this.getUid(), uid);
    }
}
