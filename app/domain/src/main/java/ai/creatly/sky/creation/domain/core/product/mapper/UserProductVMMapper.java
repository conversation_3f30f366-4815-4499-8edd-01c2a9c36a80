/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.product.mapper;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.product.model.ProductAsset;
import ai.creatly.sky.creation.domain.core.product.model.UserProduct;
import ai.creatly.sky.creation.domain.core.product.model.enums.ProductAssetType;
import ai.creatly.sky.creation.domain.core.product.model.response.ProductAssetVM;
import ai.creatly.sky.creation.domain.core.product.model.response.UserProductEntryVM;
import ai.creatly.sky.creation.domain.core.product.model.response.UserProductVM;
import ai.creatly.sky.creation.domain.core.userasset.mapper.UserAssetVMMapper;
import ai.creatly.sky.creation.domain.core.userasset.model.response.UserAssetVM;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.support.label.model.LabelRef;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import lombok.Setter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version UserProductVMMapper.java, v 0.1 2024-10-09 下午6:24 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public abstract class UserProductVMMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper    userFileHelper;
    @Setter(onMethod_ = @Autowired)
    private UserAssetVMMapper userAssetVMMapper;

    public UserProductEntryVM toUserProductEntryVM(UserProduct product) {
        String coverUrl = userFileHelper.getHttpUrl(product.getCoverUrl(), FileAcl.PRIVATE);
        return this.toUserProductEntryVM(product, coverUrl);
    }

    @Mapping(target = "crowdCategory", source = "product.firstCrowdCategory")
    @Mapping(target = "coverUrl", source = "coverUrl")
    protected abstract UserProductEntryVM toUserProductEntryVM(UserProduct product, String coverUrl);

    public UserProductVM toUserProductVM(UserProduct product) {
        String coverUrl = userFileHelper.getHttpUrl(product.getCoverUrl(), FileAcl.PRIVATE);
        return this.toUserProductVM(product, coverUrl);
    }

    @Mapping(target = "crowdCategory", source = "product.firstCrowdCategory")
    @Mapping(target = "coverUrl", source = "coverUrl")
    protected abstract UserProductVM toUserProductVM(UserProduct product, String coverUrl);

    protected String toLabelName(LabelRef label) {
        return label.getName();
    }

    protected ProductAssetVM toProductAssetVM(ProductAsset productAsset) {
        if (productAsset == null) {
            return null;
        }
        UserAssetVM userAssetVM = userAssetVMMapper.toUserAssetVM(productAsset.getAsset());
        return this.toProductAssetVM(userAssetVM, productAsset.getType());
    }

    protected abstract ProductAssetVM toProductAssetVM(UserAssetVM assetVM, ProductAssetType type);
}
