/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.model.enums;

/**
 * <AUTHOR>
 * @version ImageResizeMode.java, v 0.1 2024-09-27 下午4:18 zhoudong
 */
public enum ImageResizeMode {

    /**
     * 等比缩放，缩放图限制为指定w与h的矩形内的最大图片。
     */
    lfit,
    /**
     * 将原图等比缩放为延伸出指定w与h的矩形框外的最小图片，然后将超出的部分进行居中裁剪。
     */
    fill,
    /**
     * 等比缩放，缩放图为延伸出指定w与h的矩形框外的最小图片。
     */
    mfit,
    /**
     * 将原图缩放为指定w与h的矩形内的最大图片，然后使用指定颜色居中填充空白部分。
     */
    pad,
    /**
     * 固定宽高，强制缩放。
     */
    fixed
}
