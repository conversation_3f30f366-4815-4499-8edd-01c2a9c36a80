/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.multimedia.util;

import lombok.experimental.UtilityClass;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version SubtitleUtil.java, 2024-10-25 下午11:44 zhoudong
 */
@UtilityClass
public class SubtitleUtil {

    public static List<String> forceWords(List<String> texts, List<String> words) {
        for (String keyword : words) {
            texts = forceWord(texts, keyword);
        }
        return texts;
    }

    public static List<String> forceWord(List<String> texts, String word) {
        String text = String.join("", texts);
        List<Integer> startIndices = findSubstringIndices(text, word);
        if (startIndices.isEmpty()) {
            return texts;
        }
        for (int startIndex : startIndices) {
            int endIndex = startIndex + word.length();
            texts = mergeKeyword(texts, word, startIndex, endIndex);
        }
        return texts;
    }

    private static List<String> mergeKeyword(List<String> texts, String word, int startIndex, int endIndex) {
        List<String> newTexts = new ArrayList<>();
        int tokenStartIndex = 0;
        for (String token : texts) {
            int tokenEndIndex = tokenStartIndex + token.length();
            // 左交集
            if (tokenStartIndex < startIndex && tokenEndIndex >= startIndex) {
                // previous
                newTexts.add(token.substring(0, startIndex - tokenStartIndex));
                // remaining part
                String remaining = token.substring(startIndex - tokenStartIndex);
                if (remaining.length() <= word.length()) {
                    newTexts.add(remaining);
                } else {
                    newTexts.add(remaining.substring(0, word.length()));
                    newTexts.add(remaining.substring(word.length()));
                }
                tokenStartIndex += token.length();
                continue;
            }
            // 内包含
            if (tokenStartIndex >= startIndex && tokenEndIndex <= endIndex) {
                if (newTexts.isEmpty()) {
                    newTexts.add(token);
                } else {
                    // 追加到上一个token中
                    newTexts.set(newTexts.size() - 1, newTexts.getLast() + token);
                }
                tokenStartIndex += token.length();
                continue;
            }
            // 右交集
            if (tokenStartIndex <= endIndex && tokenEndIndex > endIndex) {
                // keyword part
                String keywordPart = token.substring(0, endIndex - tokenStartIndex);
                newTexts.set(newTexts.size() - 1, newTexts.getLast() + keywordPart);
                // remaining part
                newTexts.add(token.substring(endIndex - tokenStartIndex));
                tokenStartIndex += token.length();
                continue;
            }
            // 无交集
            newTexts.add(token);
            tokenStartIndex += token.length();
        }
        return newTexts;
    }

    private static List<Integer> findSubstringIndices(String fullText, String substring) {
        List<Integer> indices = new ArrayList<>();
        // 获取第一个出现的位置
        int index = fullText.indexOf(substring);
        while (index >= 0) {
            indices.add(index);
            // 获取下一个出现的位置
            index = fullText.indexOf(substring, index + substring.length());
        }
        return indices;
    }
}
