/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.azure.model.ssml;

import com.fasterxml.jackson.annotation.JsonRawValue;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText;
import com.jspeeder.core.util.xml.XML;
import lombok.Data;

/**
 * 声音表达
 *
 * <AUTHOR>
 * @version SpeakVoiceExpression.java, v 0.1 2023-06-06 23:24 joton
 */
@Data
@JacksonXmlRootElement(localName = SpeakVoiceExpression.LOCAL_NAME)
public class SpeakVoiceExpression {

    public static final String LOCAL_NAME = "mstts:express-as";

    @JacksonXmlProperty(isAttribute = true)
    private String style;
    /**
     * 情绪表现程序（取值：0.01 ~ 2，默认1）
     */
    @JacksonXmlProperty(isAttribute = true, localName = "styledegree")
    private String styleDegree;

    @JacksonXmlProperty(isAttribute = true)
    private String role;

    /**
     * {@link JacksonXmlText} 是为了把 content 作为 xml 的文本节点，而不是属性节点
     * {@link JsonRawValue} 是为了不对 content 进行 xml 转义
     * </p>
     *
     * @see SpeakVoiceBreak
     * @see SpeakVoiceAudio
     * @see SpeakVoiceProsody
     * @see SpeakVoiceBookmark
     */
    @JacksonXmlText
    @JsonRawValue
    private String content;

    public void setContent(String content) {
        if (content == null) {
            return;
        }
        // 确保反序列化时，在 prosody 节点下的所有纯文本内容，全部追加到 content 中
        if (this.content == null) {
            this.content = content;
        } else {
            this.content += content;
        }
    }

    /*-------------------------- 以下setter方法仅用于反序列化时，将子节点追加到 content 中 --------------------------*/

    void setBreak(SpeakVoiceBreak breakNode) {
        if (breakNode == null) {
            return;
        }
        // 确保反序列化时，在 prosody 节点下的所有 break 节点，全部追加到 content 中
        String xml = XML.toXMLString(breakNode);
        if (this.content == null) {
            this.content = xml;
        } else {
            this.content += xml;
        }
    }

    void setAudio(SpeakVoiceAudio audio) {
        if (audio == null) {
            return;
        }
        // 确保反序列化时，在 prosody 节点下的所有 audio 节点，全部追加到 content 中
        String xml = XML.toXMLString(audio);
        if (this.content == null) {
            this.content = xml;
        } else {
            this.content += xml;
        }
    }

    void setProsody(SpeakVoiceProsody prosody) {
        if (prosody == null) {
            return;
        }
        // 确保反序列化时，在 prosody 节点下的所有 prosody 节点，全部追加到 content 中
        String xml = XML.toXMLString(prosody);
        if (this.content == null) {
            this.content = xml;
        } else {
            this.content += xml;
        }
    }

    void setBookmark(SpeakVoiceBookmark bookmark) {
        if (bookmark == null) {
            return;
        }
        // 确保反序列化时，在 prosody 节点下的所有 bookmark 节点，全部追加到 content 中
        String xml = XML.toXMLString(bookmark);
        if (this.content == null) {
            this.content = xml;
        } else {
            this.content += xml;
        }
    }
}
