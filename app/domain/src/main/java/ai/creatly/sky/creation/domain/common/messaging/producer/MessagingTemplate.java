/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.messaging.producer;

import ai.creatly.sky.creation.domain.common.messaging.Message;
import ai.creatly.sky.creation.domain.common.messaging.TransactionMessage;
import ai.creatly.sky.creation.domain.common.messaging.support.MessageBuilder;
import org.jetbrains.annotations.Nullable;
import org.springframework.messaging.MessageHeaders;

/**
 * 消息发送模板
 *
 * <AUTHOR>
 * @version MessagingTemplate.java, v 0.1 2023-10-20 上午11:01 zhoudong
 * @see Destination
 */
public interface MessagingTemplate {

    /**
     * 转换消息体并发送
     *
     * @param destination 消息发送目的地
     * @param messageKey  消息内容在业务层面的唯一标识码（尽可能保证全局唯一，但即使重复也不会影响功能，只是给消息打个标方便事后查询）
     * @param payload     消息体
     * @return 消息全局唯一ID（即使同一条消息被重复发送，这个消息ID也会不一样）
     */
    default String send(String destination, String messageKey, Object payload) {
        return this.send(destination, messageKey, payload, null);
    }

    /**
     * 转换消息体并发送
     *
     * @param destination 消息发送目的地
     * @param messageKey  消息内容在业务层面的唯一标识码（尽可能保证全局唯一，但即使重复也不会影响功能，只是给消息打个标方便事后查询）
     * @param payload     消息体
     * @param headers     消息头
     * @return 消息全局唯一ID（即使同一条消息被重复发送，这个消息ID也会不一样）
     */
    default String send(String destination, String messageKey, Object payload, @Nullable MessageHeaders headers) {
        var message = MessageBuilder.payload(payload).messageKey(messageKey).build();
        return this.send(destination, message, headers);
    }

    /**
     * 发送普通消息
     *
     * @param destination 消息发送目的地
     * @param message     消息内容
     * @return 消息全局唯一ID（即使同一条消息被重复发送，这个消息ID也会不一样）
     */
    <T> String send(String destination, Message<T> message, @Nullable MessageHeaders headers);

    /**
     * 发送事务消息
     *
     * @param destination 消息发送目的地
     * @param message     消息内容
     * @return 消息全局唯一ID（即使同一条消息被重复发送，这个消息ID也会不一样）
     */
    default <T> String send(String destination, TransactionMessage<T> message) {
        return this.send(destination, message, null);
    }

    /**
     * 发送事务消息
     *
     * @param destination 消息发送目的地
     * @param message     消息内容
     * @return 消息全局唯一ID（即使同一条消息被重复发送，这个消息ID也会不一样）
     */
    <T> String send(String destination, TransactionMessage<T> message, @Nullable MessageHeaders headers);
}
