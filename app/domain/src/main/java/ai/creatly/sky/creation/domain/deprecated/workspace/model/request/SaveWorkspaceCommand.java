/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.workspace.model.request;


import ai.creatly.sky.creation.domain.deprecated.workspace.model.WorkspaceContent;
import lombok.Data;

/**
 * <AUTHOR>
 * @version SaveWorkspaceCommand.java, v 0.1 2023-09-23 15:24 syoka
 */
@Data
public class SaveWorkspaceCommand {

    /**
     * 工作空间id
     */
    private String           id;
    /**
     * 工作空间名称
     */
    private String           name;
    /**
     * 工作空间类型
     */
    private String           workspaceType;
    /**
     * 封面图片id
     */
    private String           coverFileId;
    /**
     * 封面图片 data
     */
    private String           coverFileData;
    /**
     * 工作空间内容
     */
    private WorkspaceContent workspaceContent;
}
