/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;

/**
 * 音量调节档位
 * <p/>
 * 调节范围：The rate changes should be within 0.5 to 2 times the original audio.
 *
 * <AUTHOR>
 * @version SpeechVolumeType.java, v 0.1 2023-06-26 02:35 joton
 */
@Getter
@RequiredArgsConstructor
public enum SpeechVolumeType {

    medium("medium"),
    silent("silent"),
    x_soft("x-soft"),
    soft("soft"),
    loud("loud"),
    x_loud("x-loud"),
    ;

    private final String value;

    @Nullable
    public static SpeechVolumeType getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (SpeechVolumeType volumeType : SpeechVolumeType.values()) {
            if (volumeType.value.equals(value)) {
                return volumeType;
            }
        }
        return null;
    }
}
