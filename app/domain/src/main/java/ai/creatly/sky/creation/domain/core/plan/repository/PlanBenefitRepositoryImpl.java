/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.repository;

import ai.creatly.sky.creation.domain.core.plan.model.PlanBenefit;
import ai.creatly.sky.creation.domain.support.bizconfig.repository.BizConfigRepository;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 付费计划权益持久化实现
 *
 * <AUTHOR>
 * @version PlanBenefitRepositoryImpl.java, v 0.1 2023-10-12 上午11:32 zhoudong
 */
@Repository
@RequiredArgsConstructor
public class PlanBenefitRepositoryImpl implements PlanBenefitRepository {

    private final BizConfigRepository bizConfigRepository;

    private final static String SCHEMA_CODE = "plan_benefit";

    @Override
    public List<PlanBenefit> queryByPlanId(String planId) {
        Asserts.notBlank(planId, "付费计划ID不能为空");
        Map<String, String> queryParams = Map.of("planId", planId);
        return bizConfigRepository.queryList(SCHEMA_CODE, queryParams, PlanBenefit.class);
    }
}
