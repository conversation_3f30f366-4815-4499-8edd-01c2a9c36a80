/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.prompt.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 *
 * <AUTHOR>
 * @version PromptTemplateErrorCode.java, v0.1 2025-02-22 14:16
 */
@Getter
@RequiredArgsConstructor
public enum PromptTemplateErrorCode implements ErrorCode {

    PROMPT_TEMPLATE_NOT_FOUND("提示词模板未找到"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
