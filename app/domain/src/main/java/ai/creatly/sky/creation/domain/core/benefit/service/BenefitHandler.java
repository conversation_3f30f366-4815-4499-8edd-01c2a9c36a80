/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.benefit.service;

import ai.creatly.sky.creation.domain.core.benefit.model.BenefitAction;
import ai.creatly.sky.creation.domain.core.plan.model.enums.BenefitType;

/**
 * <AUTHOR>
 * @version BenefitHandler.java, v 0.1 2023-10-14 19:09 syoka
 */
public interface BenefitHandler {

    /**
     * 获取当前权益类型
     */
    BenefitType getBenefitType();

    /**
     * 获取当前权益动作
     */
    BenefitAction getBenefitAction();


    void handle(long uid, Object content);

    @Override
    String toString();
}
