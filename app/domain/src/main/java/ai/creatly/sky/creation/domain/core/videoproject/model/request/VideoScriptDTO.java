/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 视频脚本
 *
 * <AUTHOR>
 * @version VideoScriptDTO.java, v 0.1 2024-09-21 下午6:57 zhoudong
 */
@Data
public class VideoScriptDTO {
    /**
     * 视频期望时长（带货视频默认是30s）
     */
    @Max(90)
    private Integer expectedSeconds;
    /**
     * 提示词
     */
    @Size(min = 2, max = 500)
    private String  prompt;
    /**
     * 完整文案
     */
    @Size(min = 5, max = 750)
    private String  text;
}
