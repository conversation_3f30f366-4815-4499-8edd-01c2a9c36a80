/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.caching.cacheable;

import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version CacheablePageTemplate.java, v 0.1 2024-02-09 上午2:13 zhoudong
 */
public interface CacheablePageTemplate<E> {

    /**
     * 缓存当前key到一个列表中
     *
     * @param listKey key列表的key
     * @return this
     */
    CacheablePageTemplate<E> pushKeyToList(String listKey);

    Page<E> get(Pageable pageable, Supplier<Page<E>> pageSupplier);
}
