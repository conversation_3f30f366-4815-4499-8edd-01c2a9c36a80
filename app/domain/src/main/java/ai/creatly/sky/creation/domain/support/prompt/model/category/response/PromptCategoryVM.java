/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.support.prompt.model.category.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @version ScenarioVM.java, v 0.1 2023-10-15 15:27 syoka
 */
@Data
public class PromptCategoryVM {


    private String bizSource;

    private String code;

    private String name;

    private String enName;
}
