/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.azure.model.ssml;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version SpeakVoiceBookmark.java, v 0.1 2024-08-15 下午5:15 zhoudong
 */
@Data
@Accessors(chain = true)
@JacksonXmlRootElement(localName = "bookmark")
public class SpeakVoiceBookmark {

    @JacksonXmlProperty(isAttribute = true)
    private String mark;
}
