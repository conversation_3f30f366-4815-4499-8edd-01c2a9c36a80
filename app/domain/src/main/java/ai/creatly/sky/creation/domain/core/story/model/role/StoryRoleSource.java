/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 故事角色来源
 */
@Getter
@RequiredArgsConstructor
public enum StoryRoleSource {

    NARRATOR("旁白"),
    AUTOMATIC("AI自动提取"),
    MANUAL("手动添加"),
    ;

    private final String desc;
}
