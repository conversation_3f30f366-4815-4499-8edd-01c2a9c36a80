/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiimage_old.model.task.result;

import ai.creatly.sky.creation.domain.core.aiimage_old.model.AiImageMetadata;
import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizResult;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class AIImageResult implements TaskBizResult {

    /**
     * midjourney 任务id
     */
    private String mjTaskId;
    /**
     * 状态
     */
    private String status;
    /**
     * 进度
     */
    private String progress;
    /**
     * 图片id
     */
    private String imageFileId;
    /**
     * oss地址
     */
    private String ossUri;
    /**
     * 业务异常信息
     */
    private String errorMsg;
    /**
     * 图片描述提示词
     */
    private String describePrompts;

    /**
     * 变动时间
     */
    private ZonedDateTime changeTime;

    /**
     * 图片元信息
     */
    private AiImageMetadata fileMetadata;

    /**
     * 账号信息
     */
    private String accountKey;
}
