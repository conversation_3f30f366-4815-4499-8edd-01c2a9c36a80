/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.azure.model;

import ai.creatly.sky.creation.domain.core.voice.model.enums.LangFamily;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 口音
 *
 * <AUTHOR>
 * @version LocaleOption.java, v 0.1 2023-11-17 下午9:31 zhoudong
 */
@Data
@AllArgsConstructor(staticName = "of")
public class LocaleOption {

    /**
     * 口音代号
     */
    private final String     code;
    /**
     * 口音描述（中文展示）
     */
    private final String     desc;
    /**
     * 该口音下的声音名称
     */
    private final String     voiceName;
    /**
     * 语言代号（使用 <a href="https://en.wikipedia.org/wiki/ISO_639">ISO_639</a> 标准 ）
     */
    private final String     langCode;
    /**
     * 语言名称（中文展示）
     */
    private final String     langName;
    /**
     * 国家代号（使用 <a href="https://en.wikipedia.org/wiki/List_of_ISO_3166_country_codes">ISO_3166</a> 标准 ）
     */
    private final String     countryCode;
    /**
     * 国家名称（中文展示）
     */
    private final String     countryName;
    /**
     * 语系
     */
    private final LangFamily langFamily;
}
