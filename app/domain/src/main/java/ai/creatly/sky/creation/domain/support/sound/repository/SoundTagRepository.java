/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.sound.repository;

import java.util.Set;

/**
 * 音效标签库
 *
 * <AUTHOR>
 * @version SoundTagRepository.java, v 0.1 2024-04-17 下午3:55 zhoudong
 */
public interface SoundTagRepository {

    /**
     * 查询所有标签
     *
     * @return 标签列表
     */
    Set<String> queryAllTags();

    /**
     * 设置所有标签
     *
     * @param tags 待合入的标签列表
     */
    void setTags(Set<String> tags);

    /**
     * 将标签列表合并到标签库里（做去重处理）
     *
     * @param tags 待合入的标签列表
     */
    void mergeTags(Set<String> tags);
}
