/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.content.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 基于平台纬度的热点
 */
@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class HotspotPlatformVM implements Serializable {

    /**
     * <k,v>k是平台名称
     */
    private String source;

    /**
     * 该平台的热度
     */
    private List<HotspotVM> hotspots;
}
