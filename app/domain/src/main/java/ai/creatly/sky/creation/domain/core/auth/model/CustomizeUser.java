/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.auth.model;

import ai.creatly.sky.creation.domain.core.user.model.organization.UserOrganization;
import com.jspeeder.core.model.OperatorSource;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;

/**
 * 自定义security user模型
 *
 * <AUTHOR>
 * @version : CustomizeUser.java, v 1.0 2023年07月31日 23时47分 syoka Exp$
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
public class CustomizeUser extends User {

    /**
     * 用户uid
     */
    private final Long             uid;
    /**
     * 客户端：appKey
     */
    private       String           appKey;
    /**
     * 客户端：appKeyName
     */
    private       String           appKeyName;
    /**
     * 用户手机号
     */
    private       String           phone;
    /**
     * 用户邮箱
     */
    private       String           email;
    /**
     * 请求来源
     */
    private       OperatorSource   source;
    /**
     * 组织信息
     */
    private       UserOrganization userOrganization;

    public CustomizeUser(Long uid, String username, String password, Collection<? extends GrantedAuthority> authorities) {
        super(username, password, authorities);
        this.uid = uid;
    }

    public CustomizeUser(Long uid, String username, String password, UserOrganization userOrganization,
                         Collection<? extends GrantedAuthority> authorities) {
        this(uid, username, password, authorities);
        this.userOrganization = userOrganization;
    }
}
