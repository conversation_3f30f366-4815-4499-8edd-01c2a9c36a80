/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.benefit.service;

import ai.creatly.sky.creation.domain.core.benefit.error.BenefitErrorCode;
import ai.creatly.sky.creation.domain.core.benefit.log.model.BenefitLog;
import ai.creatly.sky.creation.domain.core.benefit.log.model.BenefitSourceType;
import ai.creatly.sky.creation.domain.core.benefit.log.repository.BenefitLogRepository;
import ai.creatly.sky.creation.domain.core.benefit.model.BenefitAction;
import ai.creatly.sky.creation.domain.core.benefit.model.CreditBenefitContent;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsIncome;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.plan.model.enums.BenefitType;
import com.jspeeder.core.data.problem.exception.SysException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @version GrantCreditsBenefitHandler.java, v 0.1 2023-10-28 下午4:03 zhoudong
 */
@Component
@RequiredArgsConstructor
public class GrantCreditsBenefitHandler implements BenefitHandler {

    private final BenefitLogRepository    benefitLogRepository;
    private final TransactionTemplate     transactionTemplate;
    private final UserCreditDomainService userCreditDomainService;

    @Override
    public BenefitType getBenefitType() {
        return BenefitType.CREDITS;
    }

    @Override
    public BenefitAction getBenefitAction() {
        return BenefitAction.GRANT;
    }

    @Override
    public void handle(long uid, Object content) {
        CreditBenefitContent benefitContent = (CreditBenefitContent) content;

        ZonedDateTime effectAt = ObjectUtils.defaultIfNull(benefitContent.getEffectAt(), benefitContent.getReceiveAt());

        // 构建权益发放记录
        BenefitSourceType sourceType = benefitContent.getSourceType();
        BenefitLog benefitLog = new BenefitLog()
                .setUid(uid)
                .setSourceType(sourceType)
                .setSourceId(benefitContent.getSourceId())
                .setBenefitType(BenefitType.CREDITS)
                .setBenefitCode(benefitContent.getBenefitCode())
                .setBenefitName(benefitContent.getBenefitName())
                .setBenefitValue(benefitContent.getBenefitValue())
                .setPlanId(benefitContent.getPlanId())
                .setPaidFee(benefitContent.getPaidFee())
                .setReceiveAt(benefitContent.getReceiveAt())
                .setEffectAt(effectAt)
                .setExpireAt(benefitContent.getExpireAt());
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            // 新增余额账户
            final CreditLogBizType bizType = switch (sourceType) {
                // 会员赠送
                case MEMBER_GIFT -> CreditLogBizType.MEMBER_GIFT;
                // 充值购买
                case TOP_UP_PLAN_ORDER -> CreditLogBizType.TOP_UP_PLAN_ORDER;
                default -> throw new SysException(BenefitErrorCode.BENEFIT_SOURCE_NOT_SUPPORT_FOR_CREDITS);
            };
            CreditsIncome income = CreditsIncome.builder()
                    .uid(uid)
                    .bizType(bizType)
                    .bizNo(benefitContent.getSourceId())
                    .type(CreditAccountType.GENERAL)
                    .amount(benefitContent.getGrantCredits())
                    .effectAt(effectAt)
                    .expireAt(benefitContent.getExpireAt())
                    .build();
            long creditAccountId = userCreditDomainService.addCreditAccount(income);

            // 新增权益发放记录
            benefitLog.setBenefitId(String.valueOf(creditAccountId));
            benefitLogRepository.create(benefitLog);
        });
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }
}
