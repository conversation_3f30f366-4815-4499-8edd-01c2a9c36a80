/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.audio.task;

import ai.creatly.sky.creation.domain.core.ai.audio.AiAudioCloneClient;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiAudioTaskResult;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiAudioTaskVars;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioCloneTaskInput;
import ai.creatly.sky.creation.domain.core.ai.image.task.AiImageTaskBizType;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskBizExecStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskHelper;
import ai.creatly.sky.creation.domain.core.asset.model.Asset;
import ai.creatly.sky.creation.domain.core.asset.model.AssetFile;
import ai.creatly.sky.creation.domain.core.asset.model.AssetMetadata;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetBizType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetSourceType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetStatus;
import ai.creatly.sky.creation.domain.core.asset.service.AssetRepository;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsRefund;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.userasset.model.AssetContent;
import ai.creatly.sky.creation.domain.core.userfile.service.impl.UserFileServiceImpl;
import com.jspeeder.core.data.id.IdHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;

/**
 * <AUTHOR>
 * @version VideoGenerateTaskHandler.java, v0.1 2025-02-28 13:41
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AudioCloneTaskHandler implements AiTaskHandler {

    @Autowired
    private   AiAudioCloneClient      aiAudioCloneClient;
    @Autowired
    protected AiTaskHelper            aiTaskHelper;
    @Autowired
    protected UserCreditDomainService userCreditDomainService;
    @Autowired
    protected UserFileServiceImpl     userFileService;
    @Autowired
    private   AssetRepository         assetRepository;


    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.AI_AUDIO)
                .setBizType(AiImageTaskBizType.audio_clone)
                .setLoadSize(3)
                .setQueueMode(TaskQueueMode.POP_PER_EXECUTED)
                .setExecCountAlertThreshold(Integer.MAX_VALUE)
                .setNotifyOnSubmit(false);
    }

    @Override
    public TaskPreAction preHandle(AiTask aiTask) {
        return TaskPreAction.FORWARD_RUNNING.updateBizVars(new AiAudioTaskVars());
    }

    @Override
    public TaskAction handle(AiTask aiTask) {
        //1、查询生成状态
        var input = aiTask.parseBizInput(AudioCloneTaskInput.class);
        UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);

        AiAudioTaskResult result = aiAudioCloneClient.generate(input, userContext);

        // 2、生成中/失败直接结束
        if (result.getBizExecStatus() == AiTaskBizExecStatus.PROCESSING) {
            return TaskAction.KEEP_STILL.updateBizResult(result);
        } else if (result.getBizExecStatus() == AiTaskBizExecStatus.FAILED) {
            return TaskAction.FORWARD_CANCELED.updateBizResult(result);
        }

        Asset assets = this.buildAsset(input, result.getAssets().getFirst().getId(), userContext.getUid(), aiTask);
        assetRepository.create(assets);

        return TaskAction.FORWARD_FINISHED.updateBizResult(result);
    }

    @Override
    public TaskPostAction postHandle(AiTask aiTask) {
        //任务取消则消耗元气恢复
        if (aiTask.getStatus() == AiTaskStatus.CANCELED) {
            CreditsRefund creditsRefund = CreditsRefund.builder()
                    .uid(aiTask.getOwnerId())
                    .bizType(CreditLogBizType.AUDIO_CLONE)
                    .bizNo(aiTask.getId().toString())
                    .allowExpenseAbsent(false)
                    .build();
            userCreditDomainService.refundCredits(creditsRefund);
        }
        return TaskPostAction.COMPLETE;
    }

    private Asset buildAsset(AudioCloneTaskInput taskInput, String code, Long uid, AiTask aiTask) {

        var metadata = new AssetMetadata()
                .setAttachments(Collections.singletonList(taskInput.getRequest().getAudioFile()))
                .setTaskId(aiTask.getId())
                .setBizParams(aiTask.getBizParams());

        AssetFile assetFile = new AssetFile();
        assetFile.setUrl(taskInput.getRequest().getAudioFile().getUrl());
        return new Asset()
                .setId(IdHelper.getId())
                .setUid(uid)
                .setStatus(AssetStatus.VALID)
                .setName(taskInput.getRequest().getAudioName())
                .setSourceType(AssetSourceType.generation)
                .setBizType(AssetBizType.audio_clone)
                .setCoverUrl(taskInput.getRequest().getPhotoFile() == null ? "" : taskInput.getRequest().getPhotoFile().getUrl())
                .setFile(assetFile)
                .setContent(new AssetContent().setMd5(code))
                .setMetadata(metadata)
                .setTags(new ArrayList<>());
    }

}
