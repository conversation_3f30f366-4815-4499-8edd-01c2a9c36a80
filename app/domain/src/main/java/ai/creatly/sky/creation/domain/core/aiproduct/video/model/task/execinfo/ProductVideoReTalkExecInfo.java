/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.video.model.task.execinfo;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizExecInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version ProductVideoReTalkExecInfo.java, v 0.1 2024-05-27 下午9:35 zhoudong
 */
@Data
@Accessors(chain = true)
public class ProductVideoReTalkExecInfo implements TaskBizExecInfo {

    /**
     * 请求次数
     */
    private Integer requestCount;
    /**
     * 生成的视频文件ID
     */
    private Long    videoFileId;
    /**
     * 生成的视频地址（OSS格式）
     */
    private String  videoUrl;
    /**
     * 生成的视频文件key
     */
    private String  videoFileKey;
    /**
     * 生成失败原因
     */
    private String  failReason;

    public boolean neverRequest() {
        return this.requestCount == null || this.requestCount == 0;
    }

    @JsonIgnore
    public boolean isFirstRequest() {
        return this.requestCount == 1;
    }

    public void incrRequestCount() {
        if (requestCount == null) {
            requestCount = 0;
        }
        requestCount = requestCount + 1;
    }

    /**
     * 业务主动重试次数
     *
     * @return 重试次数
     */
    @JsonIgnore
    public int getRetryCount() {
        return this.getRequestCount() == null ? 0 : this.getRequestCount() - 1;
    }
}
