/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.admin.mapper;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.drama.admin.model.DramaVM;
import ai.creatly.sky.creation.domain.core.drama.admin.model.DramaVideoVM;
import ai.creatly.sky.creation.domain.core.drama.model.Drama;
import ai.creatly.sky.creation.domain.core.drama.model.DramaReview;
import ai.creatly.sky.creation.domain.core.drama.model.DramaVideo;
import ai.creatly.sky.creation.domain.core.drama.model.response.DramaReviewVM;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.support.category.model.Category;
import ai.creatly.sky.creation.domain.support.category.model.CategoryPath;
import ai.creatly.sky.creation.domain.support.category.model.admin.model.CategoryPathVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import lombok.Setter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version DramaVMAdminMapper.java, 2024-12-12 下午4:34 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public abstract class DramaVMAdminMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    public DramaVM toDramaVM(Drama drama, List<Category> categories) {
        String coverUrl = userFileHelper.getHttpUrl(drama.getCoverUrl(), FileAcl.PRIVATE);
        List<CategoryPathVM> productCategories = drama.getProductCategories().stream().map(categoryPath -> {
            Category category = categories.stream()
                    .filter(ctg -> ctg.getPathCodes().equals(categoryPath.getCodes()))
                    .findFirst()
                    .orElse(null);
            return this.toCategoryPathVM(categoryPath, category);
        }).collect(toList());
        List<CategoryPathVM> crowdCategories = drama.getCrowdCategories().stream().map(categoryPath -> {
            Category category = categories.stream()
                    .filter(ctg -> ctg.getPathCodes().equals(categoryPath.getCodes()))
                    .findFirst()
                    .orElse(null);
            return this.toCategoryPathVM(categoryPath, category);
        }).collect(toList());
        return toDramaVM(drama, coverUrl, productCategories, crowdCategories);
    }

    protected abstract CategoryPathVM toCategoryPathVM(CategoryPath path, Category category);

    @Mapping(target = "typeDesc", source = "drama.type.desc")
    @Mapping(target = "coverUrl", source = "coverUrl")
    @Mapping(target = "productCategories", source = "productCategories")
    @Mapping(target = "crowdCategories", source = "crowdCategories")
    protected abstract DramaVM toDramaVM(Drama drama, String coverUrl, List<CategoryPathVM> productCategories,
                                         List<CategoryPathVM> crowdCategories);

    protected DramaVideoVM toDramaVideoVM(DramaVideo dramaVideo) {
        return toDramaVideoVM(dramaVideo, userFileHelper.getHttpUrl(dramaVideo.getUrl(), FileAcl.PRIVATE));
    }

    @Mapping(target = "url", source = "url")
    protected abstract DramaVideoVM toDramaVideoVM(DramaVideo dramaVideo, String url);

    protected abstract DramaReviewVM toDramaReviewVM(DramaReview dramaReview);
}
