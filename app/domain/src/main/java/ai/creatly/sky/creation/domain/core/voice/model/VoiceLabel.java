/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model;

import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceLabelType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 声音标签
 *
 * <AUTHOR>
 * @version VoiceLabel.java, v 0.1 2023-12-02 下午4:01 zhoudong
 */
@Data
public class VoiceLabel {

    /**
     * 标签维度
     */
    @NotNull
    private VoiceLabelType type;
    /**
     * 标签码（英文/数字/下划线/中划线）
     */
    @NotBlank
    private String         code;
    /**
     * 标签名
     */
    @NotBlank
    private String         name;
}
