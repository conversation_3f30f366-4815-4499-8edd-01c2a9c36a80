/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.admin.model;

import ai.creatly.sky.creation.domain.core.user.model.UserRole;
import ai.creatly.sky.creation.domain.core.user.model.UserStatus;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version UserVM.java, 2024-12-17 下午7:01 zhoudong
 */
@Data
public class UserVM {

    /**
     * 用户ID
     */
    private String         id;
    /**
     * 创建时间
     */
    private ZonedDateTime  createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime  updatedAt;
    /**
     * 用户状态
     */
    private UserStatus     status;
    /**
     * 用户名（全局唯一）
     */
    private String         username;
    /**
     * 用户别名
     */
    private String         nickname;
    /**
     * 手机（全局唯一）
     */
    private String         phone;
    /**
     * 密码
     */
    private String         password;
    /**
     * 用户邮箱（全局唯一）
     */
    @Nullable
    private String         email;
    /**
     * 头像地址（HTTP地址）
     */
    private String         avatar;
    /**
     * 微信统一用户ID（微信用户在上海知行元主体的微信开放平台下的唯一ID，可以跨多个应用端之间统一）
     */
    @Nullable
    private String         wechatUnionId;
    /**
     * 邀请码
     */
    private String         inviteCode;
    /**
     * 用户角色列表
     */
    private List<UserRole> roles;
}
