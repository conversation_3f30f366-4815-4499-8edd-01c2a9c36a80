/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.organization.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version OrganizationError.java, v 0.1 2024-01-22 17:53 syoka
 */
@Getter
@RequiredArgsConstructor
public enum OrganizationErrorCode implements ErrorCode {

    ORGANIZATION_NOT_EXIST("组织不存在"),
    USER_ALREADY_IN_ORGANIZATION("用户已经在组织中"),
    ORGANIZATION_ALREADY_EXISTS("组织已经存在");

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
