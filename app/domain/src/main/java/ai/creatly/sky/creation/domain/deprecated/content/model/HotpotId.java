/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.content.model;

import ai.creatly.sky.creation.domain.common.ddd.AbstractId;

/**
 * <AUTHOR>
 * @version : HotpotId.java, v 1.0 2023年08月08日 23时28分 syoka Exp$
 */
public class HotpotId extends AbstractId {

    public HotpotId(Long id) {
        super(id);
    }

    public HotpotId(String id) {
        super(id);
    }
}
