/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.story.response;

import ai.creatly.sky.creation.domain.core.story.model.role.response.StoryRoleVM;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryStyle;
import ai.creatly.sky.creation.domain.core.story.model.story.enums.StorySourceType;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;
import java.util.List;

@Data
public class StoryDetailVM {
    /**
     * 主键ID
     */
    private String            id;
    /**
     * 故事内容
     */
    private String            name;
    /**
     * 创建时间
     */
    private ZonedDateTime     createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime     updatedAt;
    /**
     * 剧本内容（可按情况修改）
     */
    private String            script;
    /**
     * 剧本类型
     */
    private String            storyType;
    /**
     * 角色列表
     */
    private List<StoryRoleVM> roles;
    /**
     * 样式配置
     */
    private StoryStyle        styleConfig;
    /**
     * 故事灵感
     */
    private String            idea;
    /**
     * 来源类型
     */
    private StorySourceType   sourceType;
    /**
     * 是否已创建故事板（场景+分镜），查询详情才会返回
     */
    @Nullable
    private Boolean           isBoardCreated;
}
