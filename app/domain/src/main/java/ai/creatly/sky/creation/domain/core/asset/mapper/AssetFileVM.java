/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.mapper;

import ai.creatly.sky.creation.domain.support.file.model.FileType;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

/**
 *
 * <AUTHOR>
 * @version AssetFileVM.java, v0.1 2025-02-25 19:48
 */
@Data
public class AssetFileVM {

    private String   id;
    private FileType type;
    /**
     * 文件地址（HTTP）
     */
    private String   url;
    /**
     * 宽度（单位：像素，仅图片和视频才有）
     */
    @Nullable
    private Integer  width;
    /**
     * 高度（单位：像素，仅图片和视频才有）
     */
    @Nullable
    private Integer  height;
    /**
     * 时长（仅音频和视频才有）
     */
    private Integer  durationMills;
}
