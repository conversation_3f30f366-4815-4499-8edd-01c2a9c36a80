/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.chat.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 *
 * <AUTHOR>
 * @version ChatErrorCode.java, v0.1 2025-02-22 18:12
 */
@Getter
@RequiredArgsConstructor
public enum ChatErrorCode  implements ErrorCode {

    CHAT_CALL_LLM_ERROR("服务调用繁忙，请稍后重试"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
