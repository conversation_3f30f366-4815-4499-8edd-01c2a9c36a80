/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 声音相关任务提交结果
 *
 * <AUTHOR>
 * @version VoiceTaskSubmittedVM.java, v 0.1 2023-11-04 下午8:28 zhoudong
 */
@Data
@Accessors(chain = true)
public class VoiceTaskSubmittedVM {

    /**
     * 声音合成任务ID
     */
    private String taskId;
}
