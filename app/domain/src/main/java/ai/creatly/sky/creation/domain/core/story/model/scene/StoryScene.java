/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.scene;

import ai.creatly.sky.creation.domain.core.story.model.story.StoryStyle;
import com.jspeeder.core.model.OperatorRef;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 故事场景
 *
 * <AUTHOR>
 * @version StoryScene.java, v 0.1 2024-03-01 21:09 syoka
 */
@Data
@Accessors(chain = true)
public class StoryScene {
    /**
     * 场景ID
     */
    private Long            id;
    /**
     * 创建时间
     */
    private ZonedDateTime   createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime   updatedAt;
    /**
     * 故事ID
     */
    private Long            storyId;
    /**
     * 场景名称
     */
    private String          name;
    /**
     * 场景排序
     */
    private Integer         index;
    /**
     * 场景标题
     */
    private String          title;
    /**
     * 场景配置
     */
    private StoryStyle      config;
    /**
     * 创建者
     */
    private OperatorRef     creator;
    /**
     * 场景状态
     */
    private SceneStatus     status;
    /**
     * 场景氛围
     */
    private SceneAtmosphere atmosphere;
}
