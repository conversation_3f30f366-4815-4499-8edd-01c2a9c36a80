/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.chat.model;

import com.jspeeder.core.data.id.AutoIncrement;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @version ConversationMessage.java, v0.1 2025-02-19 19:24
 */
@Data
@Accessors(chain = true)
public class ConversationMessage {

    /**
     * 主键ID
     */
    @AutoIncrement
    private Long           id;
    /**
     * 创建时间
     */
    private ZonedDateTime  createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime  updatedAt;
    /**
     * 用户ID
     */
    private Long           uid;
    /**
     * 会话ID
     */
    private Long           conversationId;
    /**
     * 角色
     */
    private MessageRole    role;
    /**
     * 内容
     */
    private MessageContent content;
}
