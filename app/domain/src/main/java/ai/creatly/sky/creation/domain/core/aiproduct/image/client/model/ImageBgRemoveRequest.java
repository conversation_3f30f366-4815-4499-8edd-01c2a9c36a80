/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.image.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version ImageBgRemoveRequest.java, v 0.1 2024-02-28 下午4:24 zhoudong
 */
@Data
@Accessors(chain = true)
public class ImageBgRemoveRequest {

    private String         bucket;
    @JsonProperty("image_key")
    private String         imageKey;
    private ImageSelection selection;
    @JsonProperty("output_image_key")
    private String         outputImageKey;
}
