package ai.creatly.sky.creation.domain.core.ai.video.model;

import lombok.Data;
import lombok.experimental.Accessors;
import org.checkerframework.checker.nullness.qual.Nullable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DigPersonVideoRequest {

    //数字人参数
    String id;
//    Integer personX;
//    Integer personY;
    @Nullable
    String figureType;
    Integer personHeight;
    Integer personWidth;

    //背景颜色
    //String bgColour;

    //语音参数
    @Nullable
    String audioUrl;
    String language;

    @Nullable
    String text;
    @Nullable
    String voiceCode;
    @Nullable
    Float speechRate;
    @Nullable
    Float pitchRate;
    @Nullable
    Integer volume;

    //视频宽高
    Integer screenWidth;
    Integer screenHeight;

    //文本长度（字）与音频长度（秒）二选一
    @Nullable
    Integer wordCount;
    @Nullable
    Integer secondCount;



    //背景图片参数
//    @Nullable
//    String srcUrl;
//    @Nullable
//    Integer bgX;
//    @Nullable
//    Integer bgY;
//    @Nullable
//    Integer bgHeight;
//    @Nullable
//    Integer bgWidth;

}
