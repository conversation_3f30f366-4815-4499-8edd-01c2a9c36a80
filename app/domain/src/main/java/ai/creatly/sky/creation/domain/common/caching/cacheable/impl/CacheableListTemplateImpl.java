/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.caching.cacheable.impl;

import ai.creatly.sky.creation.domain.common.caching.CachingTemplate;
import ai.creatly.sky.creation.domain.common.caching.cacheable.CacheableListStep;
import ai.creatly.sky.creation.domain.common.caching.cacheable.CacheableListTemplate;
import com.jspeeder.core.util.Asserts;

import java.time.Duration;
import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version CacheableListTemplateImpl.java, v 0.1 2024-02-09 上午1:53 zhoudong
 */
public class CacheableListTemplateImpl<E> extends AbstractCacheableTemplate<List<E>>
        implements CacheableListStep<E>, CacheableListTemplate<E> {

    private final Class<E> elementType;

    public CacheableListTemplateImpl(CachingTemplate cachingTemplate, Class<E> elementType) {
        super(cachingTemplate);
        this.elementType = elementType;
    }

    @Override
    public CacheableListStep<E> key(String key) {
        Asserts.notBlank(key, "key must not be blank");
        this.cacheKey = key;
        return this;
    }

    @Override
    public CacheableListTemplate<E> timeout(Duration timeout) {
        Asserts.notBlank(this.cacheKey, "cacheKey must not be blank");
        Asserts.notNull(timeout, "timeout must not be null");
        this.timeout = timeout;
        return this;
    }

    @Override
    public CacheableListTemplate<E> pushKeyToList(String listKey) {
        Asserts.notBlank(listKey, "listKey must not be blank");
        this.listKey = listKey;
        return this;
    }

    @Override
    public List<E> get(Supplier<List<E>> valueSupplier) {
        Asserts.notBlank(this.cacheKey, "cacheKey must not be blank");
        Asserts.notNull(this.timeout, "timeout must not be null");
        // 先从缓存取值
        List<E> cacheValue = cachingTemplate.getList(this.cacheKey, this.elementType);
        // 返回已缓存的值
        return this.getCachedValue(cacheValue, valueSupplier);
    }
}
