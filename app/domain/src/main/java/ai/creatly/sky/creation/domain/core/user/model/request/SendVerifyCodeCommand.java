/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.model.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 发送验证码命令
 *
 * <AUTHOR>
 * @version SendVerifyCodeCommand.java, v 0.1 2023-09-24 21:35 syoka
 */
@Data
public class SendVerifyCodeCommand {

    @NotNull
    private VerifyCodeScenario scenario;
    @NotBlank
    private String             phone;
}
