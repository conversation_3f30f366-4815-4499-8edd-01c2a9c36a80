/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.engine.handler;

import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;

/**
 * <AUTHOR>
 * @version AiTaskHandler.java, v 0.1 2023-07-28 20:57 joton
 */
public interface AiTaskHandler extends AiTaskPreHandler, AiTaskPostHandler {

    AiTaskConfig config();

    @Override
    TaskPreAction preHandle(AiTask aiTask);

    TaskAction handle(AiTask aiTask);

    @Override
    TaskPostAction postHandle(AiTask aiTask);
}
