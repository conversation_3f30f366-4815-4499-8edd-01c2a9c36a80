/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.credit.cost.model;

import ai.creatly.sky.creation.domain.common.ddd.ValueObject;
import com.jspeeder.core.model.BizStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @version CreditCostRule.java, v 0.1 2023-12-01 23:26 heb
 */
@Data
@ValueObject
@Accessors(chain = true)
public class CreditCostRule {
    /**
     * 主键ID
     */
    private Long          id;
    /**
     * 创建时间
     */
    private ZonedDateTime createdAt;
    /**
     * 更改时间
     */
    private ZonedDateTime updatedAt;
    /**
     * 消耗值
     */
    private Long          cost;
    /**
     * 度量单位
     */
    private UnitsName     unitsName;
    /**
     * 度量数量
     */
    private Integer       unitsAmount;
    /**
     * 任务标识符
     */
    private String        taskType;
    /**
     * 任务类型
     */
    private String        taskBizType;
    /**
     * 规则状态
     */
    private BizStatus     status;
}
