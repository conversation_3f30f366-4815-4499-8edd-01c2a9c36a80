/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.sound.model;

import ai.creatly.sky.creation.domain.support.sound.model.enums.SoundSourceType;
import com.jspeeder.core.model.BizStatus;
import com.jspeeder.core.model.OperatorRef;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Set;

/**
 * 音效
 *
 * <AUTHOR>
 * @version Sound.java, v 0.1 2024-04-16 下午12:00 zhoudong
 */
@Data
@Accessors(chain = true)
public class Sound {

    private Long            id;
    private ZonedDateTime   createdAt;
    private ZonedDateTime   updatedAt;
    private Long            uid;
    private BizStatus       status;
    private SoundSourceType sourceType;
    private String          sourceNo;
    private String          name;
    private String          authorName;
    private Set<String>     tagNames;
    private Long            audioId;
    private String          audioUrl;
    private String          audioFormat;
    private Duration        duration;
    /**
     * 比特率（kps）
     */
    private Integer         bitrate;
    /**
     * 采样率（Hz）
     */
    private Integer         sampleRate;
    private OperatorRef     creator;
    private OperatorRef     updater;
}
