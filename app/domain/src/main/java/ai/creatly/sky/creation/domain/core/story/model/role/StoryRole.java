/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role;

import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * 故事角色
 *
 * <AUTHOR>
 * @version StoryRole.java, v 0.1 2024-03-06 22:40 heb
 */
@Data
@Accessors(chain = true)
public class StoryRole {

    /**
     * 角色ID
     */
    private String              id;
    /**
     * 角色名称
     */
    private String              roleName;
    /**
     * 角色来源
     */
    private StoryRoleSource     roleSource;
    /**
     * 角色性别
     */
    @Nullable
    private StoryRoleGender     roleGender;
    /**
     * 角色年龄段
     */
    @Nullable
    private StoryRoleGeneration roleGeneration;
    /**
     * 角色描述
     */
    private String              roleDesc;
    /**
     * 角色声音ID
     * </p>
     * 注意：老数据是基础声音的code，新数据是声音数据库的主键ID  TODO 修复完数据后，改成 Long 类型
     */
    private String              voiceId;
    /**
     * 角色形象图文件ID
     */
    @Nullable
    private Long                portraitId;
    /**
     * 角色形象图地址（OSS协议）
     */
    @Nullable
    private String              portraitUrl;
}
