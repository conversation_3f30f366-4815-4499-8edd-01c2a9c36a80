/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.label.model.request;

import ai.creatly.sky.creation.domain.support.label.model.enums.LabelSource;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version LabelCreateRequest.java, v 0.1 2023-04-16 22:43
 */
@Data
public class LabelCreateRequest {

    /**
     * 标签来源
     */
    @NotNull
    private LabelSource source;
    /**
     * 标签编码
     */
    @NotBlank(message = "标签编码不能为空")
    private String      code;
    /**
     * 中文名称
     */
    @NotBlank(message = "中文名称不能为空")
    private String      name;
}
