/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.service.pay;

import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @version PlanPayServiceFactory.java, v 0.1 2023-10-19 下午6:00 zhoudong
 */
@Service
@RequiredArgsConstructor
public class PlanPayServiceFactory {

    private final SubscriptionPlanPayService subscriptionPlanPayService;
    private final TopUpPlanPayService topUpPlanPayService;

    /**
     * 获取定价计划支付服务
     *
     * @param plan 定价计划
     * @return 定价计划支付服务
     */
    public PlanPayService getPlanPayService(Plan plan) {
        Asserts.notNull(plan.getType(), "will not happen!");
        switch (plan.getType()) {
            case SUBSCRIPTION -> {
                return subscriptionPlanPayService;
            }
            case TOP_UP -> {
                return topUpPlanPayService;
            }
            default -> throw new SysException(CommonErrorCode.UNSPECIFIED);
        }
    }
}
