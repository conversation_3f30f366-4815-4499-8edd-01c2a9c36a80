/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.repository;

import ai.creatly.sky.creation.domain.core.story.model.shot.ShotDynamic;
import ai.creatly.sky.creation.domain.core.story.model.shot.ShotStatic;
import ai.creatly.sky.creation.domain.core.story.model.shot.StoryShot;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version StoryShotRepository.java, v 0.1 2024-03-09 14:06 heb
 */
public interface StoryShotRepository {

    long create(StoryShot storyShot);

    /**
     * 保存故事分镜
     *
     * @param storyShot 故事分镜
     */
    void batchCreate(List<StoryShot> storyShot);

    /**
     * 增量更新，不要全量更新
     *
     * @param storyShot 故事分镜
     */
    void updateById(StoryShot storyShot);

    /**
     * 更新动态素材
     *
     * @param shotId      主键ID
     * @param shotDynamic 动态素材
     */
    void updateDynamicAssetById(long shotId, ShotDynamic shotDynamic);

    /**
     * 更新静态素材
     *
     * @param shotId     主键ID
     * @param shotStatic 静态素材
     */
    void updateStaticAssetById(long shotId, ShotStatic shotStatic);

    List<StoryShot> findStoryShotsByStoryIdSceneId(long storyId, long sceneId);

    List<StoryShot> findStoryShotsByStoryId(long storyId);

    Optional<StoryShot> queryStoryShotById(long storyId, long shotId);

    Map<Long, StoryShot> queryFirstStoryShotMapByStoryIds(List<Long> storyIds);
}
