/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.admin.response;

import ai.creatly.sky.creation.domain.core.plan.model.PlanBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanBuyMethod;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanPeriodType;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version PlatformPlanVM.java, v 0.1 2023-12-22 上午1:08 zhoudong
 */
@Data
public class PlatformPlanVM {

    /**
     * 计划ID（全局唯一）
     */
    private String            id;
    /**
     * 计划类型
     */
    private PlanType          type;
    /**
     * 计划名称
     */
    private String            name;
    /**
     * 计划描述
     */
    private String            description;
    /**
     * 购买方式
     */
    private PlanBuyMethod     buyMethod;
    /**
     * 原价（分）
     */
    @Nullable
    private String            originalFee;
    /**
     * 实价（分）
     */
    @Nullable
    private String            realFee;
    /**
     * 订单标题（下单时使用）
     */
    @Nullable
    private String            orderTitle;
    /**
     * 计划周期类型
     */
    private PlanPeriodType    periodType;
    /**
     * 到期是否自动续费
     */
    @Nullable
    private Boolean           autoRenewed;
    /**
     * 计划阶梯档位（用于排序、有序展示）
     */
    private Integer           level;
    /**
     * 权益有效期（为空则表示权益终身有效）
     */
    @Nullable
    private Duration          benefitDuration;
    /**
     * 关联的权益列表
     */
    private List<PlanBenefit> benefits;
}
