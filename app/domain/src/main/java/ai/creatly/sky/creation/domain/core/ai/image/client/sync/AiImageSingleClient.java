/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.client.sync;

import ai.creatly.sky.creation.domain.core.ai.image.task.expand.model.ImageExpandTaskInput;
import ai.creatly.sky.creation.domain.core.ai.image.task.generate.model.ImageGenerateTaskInput;
import ai.creatly.sky.creation.domain.core.ai.image.task.repaint.model.ImageRepaintTaskInput;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import org.jetbrains.annotations.Nullable;

/**
 * AI图像服务（同步生成单图）
 *
 * <AUTHOR>
 * @version AiImageSingleClient.java, v0.1 2025-03-04 13:26
 */
public interface AiImageSingleClient {

    @Nullable
    UserFile generateImage(ImageGenerateTaskInput input, UserContext userContext);

    @Nullable
    default UserFile expandImage(ImageExpandTaskInput input, UserContext userContext){
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Nullable
    default UserFile repaintImage(ImageRepaintTaskInput input, UserContext userContext){
        throw new UnsupportedOperationException("Not supported yet.");
    }
}
