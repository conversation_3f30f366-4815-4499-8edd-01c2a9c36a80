/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.messaging.support;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * MQ 相关错误码
 *
 * <AUTHOR>
 * @version MQErrorCode.java, v 0.1 2023-10-26 下午11:10 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum MQErrorCode implements ErrorCode {

    PRODUCER_INSTANCE_NOT_EXISTS("生产者实例不存在"),
    SEND_MESSAGE_FAIL("发送消息失败"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
