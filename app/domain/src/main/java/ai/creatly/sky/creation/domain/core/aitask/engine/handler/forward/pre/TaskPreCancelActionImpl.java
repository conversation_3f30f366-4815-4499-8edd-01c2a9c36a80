/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre;

import ai.creatly.sky.creation.domain.core.aitask.model.UpdatableAiTask;
import com.jspeeder.core.util.Asserts;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @version TaskPreCancelAction.java, v 0.1 2024-06-29 上午2:07 zhoudong
 */
@Getter
class TaskPreCancelActionImpl extends TaskPreActionImpl implements TaskPreCancelAction {

    private String reason;

    TaskPreCancelActionImpl(@Nullable UpdatableAiTask updatableAiTask) {
        super(ActionTypeEnum.FORWARD_CANCELED, updatableAiTask);
    }

    @Override
    public TaskPreCancelAction withAiTask(@NotNull UpdatableAiTask updatableAiTask) {
        super.checkNotNull(updatableAiTask);
        TaskPreCancelActionImpl taskPreCancelAction = new TaskPreCancelActionImpl(updatableAiTask);
        taskPreCancelAction.reason = reason;
        return taskPreCancelAction;
    }

    @Override
    public TaskPreCancelAction withReason(String reason, @Nullable UpdatableAiTask updatableAiTask) {
        super.checkNullable(updatableAiTask);
        Asserts.notBlank(reason, "cancel reason must not be blank");
        TaskPreCancelActionImpl taskPreCancelAction = new TaskPreCancelActionImpl(updatableAiTask);
        taskPreCancelAction.reason = reason;
        return taskPreCancelAction;
    }
}
