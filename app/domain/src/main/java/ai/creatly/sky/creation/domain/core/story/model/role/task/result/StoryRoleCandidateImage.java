/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.task.result;

import ai.creatly.sky.creation.domain.core.userfile.model.FileRef;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 角色候选图
 *
 * <AUTHOR>
 * @version StoryRoleCandidateImage.java, v 0.1 2024-05-05 下午10:27 zhoudong
 */
@Data
@Accessors(chain = true)
public class StoryRoleCandidateImage {

    /**
     * 角色ID
     */
    private String        roleId;
    /**
     * 角色候选图列表（4合1原图拆分后的4张图）
     */
    private List<FileRef> imageFiles;
}
