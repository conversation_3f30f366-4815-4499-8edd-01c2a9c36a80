/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.client;

import ai.creatly.sky.creation.domain.core.digitalhuman.client.model.ShortVideoPlatform;
import ai.creatly.sky.creation.domain.core.digitalhuman.error.DigHumanErrorCode;
import com.jspeeder.core.data.problem.exception.BizException;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version ShortVideoPlatformUtil.java, v 0.1 2024-07-03 15:49 syoka
 */
@UtilityClass
public class ShortVideoPlatformUtil {

    private static final String DOUYIN   = "douyin.com";
    private static final String KS       = "kuaishou.com";
    private static final String TIKTOK   = "tiktok.com";
    private static final String BILIBILI = "bilibili.com";
    private static final String XHS      = "xiaohongshu.com";

    public static ShortVideoPlatform getPlatform(String shortLink) {
        if (StringUtils.contains(shortLink, DOUYIN)) {
            return ShortVideoPlatform.DOUYIN;
        }
        if (StringUtils.contains(shortLink, KS)) {
            return ShortVideoPlatform.KS;
        }
        if (StringUtils.contains(shortLink, BILIBILI)) {
            return ShortVideoPlatform.BILIBILI;
        }
        if (StringUtils.contains(shortLink, TIKTOK)) {
            return ShortVideoPlatform.TIKTOK;
        }
        if (StringUtils.contains(shortLink, XHS)) {
            return ShortVideoPlatform.XHS;
        }
        throw new BizException(DigHumanErrorCode.DH_HOT_VIDEO_LINK_UNSUPPORTED);
    }

}
