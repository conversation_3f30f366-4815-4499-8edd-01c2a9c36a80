/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.task.param;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizParams;
import ai.creatly.sky.creation.domain.core.voice.model.enums.EmotionDegreeEnum;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechPitchType;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechRateType;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechVolumeType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * 声音合成参数
 *
 * <AUTHOR>
 * @version VoiceSynthesisParams.java, v 0.1 2023-11-07 上午1:47 zhoudong
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class VoiceSynthesisParams extends VoiceTaskParams implements TaskBizParams {

    /**
     * 输入文本
     */
    private String              inputText;
    /**
     * 口音编号
     */
    @Nullable
    private String              localeCode;
    /**
     * 扮演角色
     */
    @Nullable
    private String              roleCode;
    /**
     * 附加情绪
     */
    @Nullable
    private String            emotionCode;
    /**
     * 情绪表现程度（默认中）
     */
    @Nullable
    private EmotionDegreeEnum emotionDegree;
    /**
     * 调节音调
     */
    @Nullable
    private SpeechPitchType   pitch;
    /**
     * 调节语速
     */
    @Nullable
    private SpeechRateType      rate;
    /**
     * 调节音量
     */
    @Nullable
    private SpeechVolumeType    volume;

    public boolean hasProsody() {
        return pitch != null || rate != null || volume != null;
    }
}
