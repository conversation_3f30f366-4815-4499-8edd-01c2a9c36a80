/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.deprecated.material.model.response;

import ai.creatly.sky.creation.domain.deprecated.material.model.MaterialType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 素材列表项信息
 */
@Data
@Accessors(chain = true)
public class MaterialEntryVM {

    /**
     * 素材ID
     */
    private String id;
    /**
     * 素材名称
     */
    private String       name;
    /**
     * 素材类型
     */
    private MaterialType type;
    /**
     * 素材主体文案
     */
    private String       text;
    /**
     * 素材主体文件地址
     */
    private String fileUrl;
    /**
     * 素材ossurl
     */
    private String ossUrl;
    /**
     * 点赞数量
     */
    private Integer likes;
    /**
     * 查看数量
     */
    private Integer views;
    /**
     * 收藏数量
     */
    private Integer favorites;
}
