/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.helper;

import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanSource;
import lombok.experimental.UtilityClass;

import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 * @version PlanUtil.java, v 0.1 2023-12-21 下午3:42 zhoudong
 */
@UtilityClass
public class PlanUtil {

    private static final Pattern USER_PLAN_ID_PATTERN = Pattern.compile("^\\d+$");

    public PlanSource judgePlanSourceById(String planId) {
        if (USER_PLAN_ID_PATTERN.matcher(planId).matches()) {
            // 用户私人定价的计划ID是纯数字
            return PlanSource.USER;
        }
        return PlanSource.PLATFORM;
    }

}
