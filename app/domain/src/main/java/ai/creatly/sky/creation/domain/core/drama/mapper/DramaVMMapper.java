/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.mapper;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.drama.model.Drama;
import ai.creatly.sky.creation.domain.core.drama.model.DramaReview;
import ai.creatly.sky.creation.domain.core.drama.model.DramaVideo;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaVideoType;
import ai.creatly.sky.creation.domain.core.drama.model.response.DramaReviewVM;
import ai.creatly.sky.creation.domain.core.drama.model.response.DramaVM;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import lombok.Setter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version DramaVMMapper.java, v 0.1 2024-10-18 下午9:14 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public abstract class DramaVMMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    public DramaVM toDramaVM(Drama drama) {
        DramaVideo dramaVideo = drama.findVideos(DramaVideoType.original_split).getFirst();
        String videoUrl = userFileHelper.getHttpUrl(dramaVideo.getUrl(), FileAcl.PRIVATE);
        String coverUrl = userFileHelper.getHttpUrl(drama.getCoverUrl(), FileAcl.PRIVATE);
        return this.toDramaVM(drama, dramaVideo, videoUrl, coverUrl);
    }

    @Mapping(target = "endText", source = "video.endText")
    @Mapping(target = "type", source = "drama.type")
    @Mapping(target = "typeDesc", source = "drama.type.desc")
    @Mapping(target = "videoUrl", source = "videoUrl")
    @Mapping(target = "coverUrl", source = "coverUrl")
    protected abstract DramaVM toDramaVM(Drama drama, DramaVideo video, String videoUrl, String coverUrl);

    protected abstract DramaReviewVM toDramaReviewVM(DramaReview dramaReview);
}
