/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.enums;

import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.AbilityBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.CreditsBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.MemberBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.TopUpMoreCreditsBenefit;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 权益类型
 *
 * <AUTHOR>
 * @version BenefitType.java, v 0.1 2023-10-10 下午10:35 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum BenefitType {

    MEMBER("会员", MemberBenefit.class),
    CREDITS("余额", CreditsBenefit.class),
    TOP_UP_MORE_CREDITS("充值送余额", TopUpMoreCreditsBenefit.class),
    ABILITY("能力", AbilityBenefit.class),
    ;

    private final String desc;
    private final Class<?> benefitClass;
}
