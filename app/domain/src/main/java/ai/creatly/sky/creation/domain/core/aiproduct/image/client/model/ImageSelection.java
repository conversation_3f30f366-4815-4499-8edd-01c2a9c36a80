/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.image.client.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version ImageSelection.java, v 0.1 2024-02-28 下午4:24 zhoudong
 */
@Data
@Accessors(chain = true)
public class ImageSelection {

    private ImageSelectionMode mode;
    private Rectangle          rectangle;
}
