/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.model.response;

import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jspeeder.core.util.time.Dates;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;

/**
 * 当前登录用户的AI创作任务
 *
 * <AUTHOR>
 * @version BaseUserAiTaskVM.java, v 0.1 2023-06-24 00:58 joton
 * @see BaseTaskVM
 */
@Data
public abstract class BaseUserAiTaskVM {

    /**
     * 任务ID
     */
    private String        id;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = Dates.PATTERN_DATE_TIME)
    private ZonedDateTime createdAt;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = Dates.PATTERN_DATE_TIME)
    private ZonedDateTime updatedAt;
    /**
     * 任务状态
     */
    private AiTaskStatus  status;
    /**
     * 业务状态
     */
    private String        bizStatus;
    /**
     * 开始运行时间
     */
    @Nullable
    private ZonedDateTime startedAt;
    /**
     * 预估完成耗时
     */
    @Nullable
    private Long          estimatedDurationSeconds;
    /**
     * 完成时间（任务完成后才有值）
     */
    @Nullable
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime finishedAt;
    /**
     * 实际完成耗时（任务完成后才有值，从提交任务到最终完成）
     */
    @Nullable
    private Long          finishedDurationSeconds;
}
