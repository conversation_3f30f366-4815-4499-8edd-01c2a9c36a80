package ai.creatly.sky.creation.domain.core.ai.video.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 可灵声音驱动
 * <AUTHOR>
 * @version VideoLipSyncEnum.java, v0.1 2025-03-18 19:30
 */
@RequiredArgsConstructor
@Getter
public enum KlingVideoLipSyncEnum {
    genshin_vindi2("阳光少年", "genshin_vindi2", "zh", "https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/sample/audio/genshin_klee2.mp3"),
    zhinen_xuesheng("懂事小弟", "zhinen_xuesheng", "zh", "https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/sample/audio/zhinen_xuesheng.mp3"),
    tiyuxi_xuedi("运动少年", "tiyuxi_xuedi", "zh", "https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/sample/audio/tiyuxi_xuedi.mp3"),
    ai_shatang("青春少女","ai_shatang","zh","https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/sample/audio/ai_shatang.mp3"),
    genshin_klee2("温柔小妹","genshin_klee2","zh","https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/sample/audio/genshin_klee2.mp3"),
    genshin_kirara("元气少女","genshin_kirara","zh","https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/sample/audio/genshin_kirara.mp3"),

    en_genshin_vindi2("Sunny","genshin_vindi2","en","https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/sample/audio/Sunny%20genshin_vindi2.mp3"),
    en_zhinen_xuesheng("Sage","zhinen_xuesheng","en","https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/sample/audio/Sage%20zhinen_xuesheng.mp3"),
    en_AOT("Ace","AOT","en","https://creatly-online.oss-cn-hangzhou.aliyuncs.com/creation/system/sample/audio/Ace%20AOT.mp3");


    private final String name;
    private final String voiceId;
    private final String language;
    private final String url;

    public static List<JSONObject> getList(String language) {
        List<JSONObject> list = new ArrayList<>();
        KlingVideoLipSyncEnum[] klingVideoLipSyncEnums = KlingVideoLipSyncEnum.values();
        for (KlingVideoLipSyncEnum lipSyncEnum : klingVideoLipSyncEnums) {
            if (language.equals(lipSyncEnum.language)) {
                list.add(new JSONObject(lipSyncEnum));
            }
        }
        return list;
    }
}
