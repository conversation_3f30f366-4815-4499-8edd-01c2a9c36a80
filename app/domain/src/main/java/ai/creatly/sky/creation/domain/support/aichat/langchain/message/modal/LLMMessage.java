/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@Data
@Accessors(chain = true)
public class LLMMessage {

    /**
     * 消息id
     */
    private String messageId;

    /**
     * USER/SYSTEM/AI
     */
    private LLMMessageType messageType;

    /**
     * 消息内容
     */
    private String text;

    public enum LLMMessageType {
        USER,
        SYSTEM,
        AI,
        ;

        public static LLMMessageType of(String role) {
            for (LLMMessageType messageType : values()) {
                if (StringUtils.equalsAnyIgnoreCase(messageType.name(), role)) {
                    return messageType;
                }
            }
            return null;
        }
    }
}
