/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.exam.repository;

import ai.creatly.sky.creation.domain.core.ai.exam.model.AiUserExam;
import ai.creatly.sky.creation.domain.core.ai.exam.model.UserExamQuery;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;

import java.util.List;

/**
 * AI认证考试
 * <AUTHOR>
 * @version AiExamRecordRepository.java, v0.1 2025-02-19 21:17
 */
public interface AiExamRepository {

    List<AiUserExam> getAiExam(Long uid, Long certId);

    List<AiUserExam> getPassAiExam(Long uid);

    Page<AiUserExam> getPageList(Pageable pageable, UserExamQuery userExamQuery);

    AiUserExam getLastAiExam(Long uid, Long certId);

    AiUserExam getSubmitedAiExam(Long uid, Long certId);

    AiUserExam getAiExamById(Long examId);

    AiUserExam updateExam(AiUserExam aiExam);

    boolean createExam(AiUserExam aiExam);

    Integer getAiExamCount(Long uid, Long certId);
}
