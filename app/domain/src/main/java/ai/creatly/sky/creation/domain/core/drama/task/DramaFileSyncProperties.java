/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.task;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 剧情库同步配置
 *
 * <AUTHOR>
 * @version DramaSyncProperties.java, 2024-10-29 下午3:53 zhoudong
 */
@Component
@Data
@ConfigurationProperties(prefix = "application.drama.file-sync")
public class DramaFileSyncProperties {
    private String offlineForwardEndpoint;
    /**
     * 剧情库同步到本地的目录
     */
    private String localDir;
}
