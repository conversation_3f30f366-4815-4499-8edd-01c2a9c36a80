/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.model;

import ai.creatly.sky.creation.domain.support.wechat.model.WechatAppType;
import ai.creatly.sky.creation.domain.support.wechat.model.WechatUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version WechatOpenId.java, v 0.1 2024-10-12 下午7:41 zhoudong
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(of = "appName")
public class WechatOpenId {

    private WechatAppType appType;
    private String        appName;
    private String        openId;

    public static WechatOpenId from(WechatUser wechatUser) {
        return new WechatOpenId()
                .setAppType(wechatUser.getAppType())
                .setAppName(wechatUser.getAppName())
                .setOpenId(wechatUser.getOpenId());
    }
}
