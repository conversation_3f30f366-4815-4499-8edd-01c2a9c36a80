/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.story.enums;

import ai.creatly.sky.creation.domain.core.story.model.story.StoryStyleValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version HistoricalBackgroundType.java, v 0.1 2024-03-09 10:32 heb
 */
@Getter
@RequiredArgsConstructor
public enum HistoricalBackgroundType {

    /**
     * 现代欧洲
     */
    MODERN_EUROPE(
            "现代欧洲",
            "https://creatly.oss-cn-hangzhou.aliyuncs.com/creation/system/story/historical-style/%E7%8E%B0%E4%BB%A3%E6%AC%A7%E6%B4%B2.png"
    ),
    /**
     * 中世纪欧洲
     */
    MEDIEVAL_EUROPE(
            "中世纪欧洲",
            "https://creatly.oss-cn-hangzhou.aliyuncs.com/creation/system/story/historical-style/%E4%B8%AD%E4%B8%96%E7%BA%AA%E6%AC%A7%E6%B4%B2.jpg"
    ),
    /**
     * 古代中国
     */
    ANCIENT_CHINA(
            "古代中国",
            "https://creatly.oss-cn-hangzhou.aliyuncs.com/creation/system/story/historical-style/%E4%B8%AD%E5%9B%BD%E5%8F%A4%E4%BB%A3.jpg"
    ),
    /**
     * 现代中国
     */
    MODERN_CHINA(
            "现代中国",
            "https://creatly.oss-cn-hangzhou.aliyuncs.com/creation/system/story/historical-style/%E4%B8%AD%E5%9B%BD%E7%8E%B0%E4%BB%A3.jpg"
    ),
    /**
     * 战国
     */
    WARRING_STATES(
            "战国",
            "https://creatly.oss-cn-hangzhou.aliyuncs.com/creation/system/story/historical-style/%E6%AD%A6%E5%A3%AB.jpg"
    ),
    /**
     * 大航海时代
     */
    AGE_OF_DISCOVERY(
            "大航海时代",
            "https://creatly.oss-cn-hangzhou.aliyuncs.com/creation/system/story/historical-style/%E6%B5%B7%E7%9B%97.png"
    ),
    ;

    private final String desc;
    private final String cover;

    public static HistoricalBackgroundType ofCode(String code) {
        for (HistoricalBackgroundType backgroundEnum : values()) {
            if (StringUtils.equals(code, backgroundEnum.name())) {
                return backgroundEnum;
            }
        }
        return null;
    }

    public StoryStyleValue buildStyleValue() {
        StoryStyleValue storyStyleValue = new StoryStyleValue();
        storyStyleValue.setName(this.desc);
        storyStyleValue.setCode(this.name());
        storyStyleValue.setCover(this.cover);
        return storyStyleValue;
    }
}
