/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.mapper;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.DigitalHumanAvatar;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.response.AvatarVM;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.response.VideoAvatarLayerVM;
import ai.creatly.sky.creation.domain.core.userfile.model.BizFile;
import ai.creatly.sky.creation.domain.core.userfile.model.response.BizFileVM;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.videoproject.mapper.VideoSubtitleMapper;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoProjectConstants;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoSubtitleLayer;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import lombok.Setter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version DigHumanAvatarMapper.java, v 0.1 2024-05-25 17:33 syoka
 */
@Mapper(config = BaseMapperConfig.class, uses = VideoSubtitleMapper.class)
public abstract class DigitalHumanAvatarVMMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    @Mapping(target = "subtitleLayer", ignore = true)
    @Mapping(target = "avatarLayer", ignore = true)
    @Mapping(target = "coverFile", ignore = true)
    public AvatarVM toVM(DigitalHumanAvatar avatar) {
        BizFileVM bizFileVM = null;
        BizFile coverFile = avatar.getCoverFile();
        if (!Objects.isNull(coverFile)) {
            String httpUrl = userFileHelper.getHttpUrl(coverFile.getFileUrl(), FileAcl.PRIVATE);
            bizFileVM = new BizFileVM()
                    .setFileId(coverFile.getFileId().toString())
                    .setFileUrl(httpUrl);
        }

        int avatarWidth = avatar.getVideoWidth() * 17 / 20;
        int avatarHeight = avatar.getVideoHeight() * 17 / 20;
        VideoAvatarLayerVM avatarLayer = new VideoAvatarLayerVM()
                // 数字人尺寸（比画布小一点）
                .setWidth(avatarWidth)
                .setHeight(avatarHeight)
                // 横向居中
                .setLeft(avatar.getVideoWidth() / 2 - avatarWidth / 2)
                // 纵向置底
                .setTop(avatar.getVideoHeight() - avatarHeight)
                // 无旋转
                .setRotation(0);

        VideoSubtitleLayer videoSubtitleLayer = VideoProjectConstants.DEFAULT_SUBTITLE_LAYER.apply(avatar.getVideoHeight());

        return toVM(avatar, bizFileVM, avatarLayer, videoSubtitleLayer);
    }

    @Mapping(target = "id", source = "avatar.id")
    @Mapping(target = "coverFile", source = "coverFile")
    protected abstract AvatarVM toVM(DigitalHumanAvatar avatar, BizFileVM coverFile, VideoAvatarLayerVM avatarLayer,
                                     VideoSubtitleLayer subtitleLayer);
}
