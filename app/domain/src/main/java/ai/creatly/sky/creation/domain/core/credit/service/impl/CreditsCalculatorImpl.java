/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.credit.service.impl;

import ai.creatly.sky.creation.domain.core.credit.cost.CreditCostRuleService;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsCalculator;
import com.jspeeder.core.util.time.Times;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 额度计算器实现
 *
 * <AUTHOR>
 * @version CreditsCalculatorImpl.java, v 0.1 2023-12-27 下午3:32 zhoudong
 */
@Service
@RequiredArgsConstructor
public class CreditsCalculatorImpl implements CreditsCalculator {

    private final CreditCostRuleService creditCostRuleService;

    @Override
    public int calcCreditsByDuration(String ruleType, String ruleBizType, Duration duration) {
        int seconds = Times.toRoundedSeconds(duration);
        if (seconds <= 0) {
            return 0;
        }
        return creditCostRuleService.calcCredits(ruleType, ruleBizType, seconds);
    }

    @Override
    public int calcCreditsOnce(String ruleType, String ruleBizType) {
        return creditCostRuleService.calcCredits(ruleType, ruleBizType, 1);
    }

    @Override
    public int calcCreditsByWords(String ruleType, String ruleBizType, int words) {
        if (words <= 0) {
            return 0;
        }
        return creditCostRuleService.calcCredits(ruleType, ruleBizType, words);
    }
}
