/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.model;

import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.support.notification.app.AlertMessage;
import lombok.Builder;
import lombok.With;

/**
 *
 * <AUTHOR>
 * @version AlertTask.java, v 0.1 2023-11-30 下午4:29 zhoudong
 */
@Builder
@With
public record AlertTask(Long taskId,
                        AiTaskType taskType,
                        String bizType,
                        String bizNo,
                        String subBizNo,
                        String ownerName,
                        AiTaskStatus status,
                        String bizStatus,
                        String createdAt,
                        String startedAt,
                        String finishedDuration,
                        Integer execCount,
                        Integer errorCount,
                        String errorMsg,
                        String errorTime,
                        Integer rollbackCount,
                        String rollbackReason,
                        String rollbackTime,
                        String retryName,
                        Integer retryCount,
                        String retryReason,
                        String retryTime,
                        String cancelReason)
        implements AlertMessage {
}
