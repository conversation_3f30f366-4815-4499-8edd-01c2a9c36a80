/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.mapper;

import ai.creatly.sky.creation.domain.core.plan.helper.UserPlanBenefitFinder;
import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.PlanBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.UserPlan;
import ai.creatly.sky.creation.domain.core.plan.model.UserPlanBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.admin.request.TopUpCreditsBenefitDTO;
import ai.creatly.sky.creation.domain.core.plan.model.admin.request.UserPlanCreateRequest;
import ai.creatly.sky.creation.domain.core.plan.model.admin.response.UserPlanVM;
import ai.creatly.sky.creation.domain.core.plan.model.enums.BenefitType;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.CreditsBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.TopUpMoreCreditsBenefit;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.model.OperatorRef;
import com.jspeeder.core.util.Validates;
import com.jspeeder.core.util.json.JSON;
import org.mapstruct.Mapper;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version UserPlanAdminMapper.java, v 0.1 2023-12-21 下午9:41 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface UserPlanAdminMapper {

    default UserPlan toUserPlan(UserPlanCreateRequest request, Plan plan, OperatorRef createdBy) {
        UserPlan userPlan = new UserPlan()
                .setId(IdHelper.getId())
                .setCreatedBy(createdBy)
                .setUpdatedBy(createdBy)
                .setUid(Long.parseLong(request.getUid()))
                .setPlatformPlanId(request.getPlatformPlanId())
                .setType(plan.getType())
                .setName(plan.getName())
                .setDescription(request.getDescription())
                .setOriginalFee(Long.valueOf(request.getOriginalFee()))
                .setRealFee(Long.valueOf(request.getRealFee()))
                .setOrderTitle(plan.getOrderTitle())
                .setPeriodType(request.getPeriodType())
                .setAutoRenewed(plan.getAutoRenewed())
                .setLevel(plan.getLevel())
                .setBenefitDuration(request.getBenefitDuration())
                .setBenefits(this.toUserPlanBenefits(plan.getBenefits()));
        if (plan.getType() == PlanType.SUBSCRIPTION) {
            Validates.notNull(request.getMemberGiftCredits(), "会员赠送余额权益不能为空");
            Validates.notNull(request.getMemberCreditsBenefitName(), "会员赠送余额权益名称不能为空");
            Validates.notNull(request.getMemberTopUpCreditsBenefits(), "会员充值赠送余额权益不能为空");

            UserPlanBenefitFinder benefitFinder = userPlan.benefitFinder();
            UserPlanBenefit creditsBenefit = benefitFinder.findSingleByType(BenefitType.CREDITS).orElse(null);
            Validates.notNull(creditsBenefit, "会员赠送余额权益不能为空");
            creditsBenefit.setName(request.getMemberCreditsBenefitName())
                    .setValue(JSON.toJSONString(new CreditsBenefit()
                            .setGiftCredits(request.getMemberGiftCredits())
                            .setTopUpCredits(0)
                            .setTotalCredits(request.getMemberGiftCredits()))
                    );

            List<UserPlanBenefit> topUpCreditsBenefits = benefitFinder.findByType(BenefitType.TOP_UP_MORE_CREDITS);
            Validates.notEmpty(topUpCreditsBenefits, "会员充值赠送余额权益不能为空");
            topUpCreditsBenefits.forEach(topUpCreditsBenefit -> {
                TopUpCreditsBenefitDTO value = request.findMemberTopUpCreditsBenefit(topUpCreditsBenefit.getCode())
                        .orElse(null);
                Validates.notNull(value, "会员充值赠送余额权益[{}]不存在", topUpCreditsBenefit.getCode());
                topUpCreditsBenefit.setName(value.getBenefitName())
                        .setValue(JSON.toJSONString(this.toTopUpMoreCreditsBenefit(value)));
            });
        }
        return userPlan;
    }

    TopUpMoreCreditsBenefit toTopUpMoreCreditsBenefit(TopUpCreditsBenefitDTO topUpCreditsBenefit);

    List<UserPlanBenefit> toUserPlanBenefits(List<PlanBenefit> benefits);

    UserPlanBenefit toUserPlanBenefit(PlanBenefit benefit);

    UserPlanVM toUserPlanVM(UserPlan userPlan);
}
