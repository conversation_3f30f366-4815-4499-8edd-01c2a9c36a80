/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.client.model;

import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version ExtractedDramaRole.java, 2024-10-21 下午5:45 zhoudong
 */
@Data
@Accessors(chain = true)
public class ExtractedDramaRole {

    /**
     * 已处理的音频文件（去噪+拼接），已上传未持久化
     */
    private UserFile originalAudio;
    /**
     * 已克隆的样例声音
     */
    private UserFile exampleAudio;
    /**
     * 声音模型文件
     */
    private UserFile voiceModel;
}
