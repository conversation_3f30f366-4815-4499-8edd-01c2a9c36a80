/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.auth.service;

import ai.creatly.sky.creation.domain.common.caching.CacheUtil;
import ai.creatly.sky.creation.domain.common.caching.CachingTemplate;
import ai.creatly.sky.creation.domain.core.auth.model.CustomizeUser;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import ai.creatly.sky.creation.domain.core.user.repository.UserRepository;
import ai.creatly.sky.creation.domain.core.user.service.UserSignInWay;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import ai.creatly.sky.creation.domain.support.notification.mail.EmailUtils;
import ai.creatly.sky.creation.domain.support.notification.sms.SmsUtils;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @version AuthService.java, v 0.1 2024-04-01 16:39 syoka
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AuthService implements UserDetailsService {

    private final static String UNIVERSAL_LOGIN_CODE = "universal_login_code:{}";

    private final UserRepository  userRepository;
    private final CachingTemplate cachingTemplate;
    private final AppAlertHelper  appAlertHelper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        final UserSignInWay signInWay;
        if (SmsUtils.isChinesePhone(username)) {
            signInWay = UserSignInWay.PHONE;
        } else if (EmailUtils.isEmail(username)) {
            signInWay = UserSignInWay.EMAIL;
        } else {
            signInWay = UserSignInWay.USERNAME;
        }
        UserInfo userInfo = userRepository.queryOptionalByUsernameWithSensitive(username, signInWay)
                // Spring Security 专用异常，在 AuthFilter 中识别
                .orElseThrow(() -> {
                    log.error("[登录身份认证]用户{}不存在", username);
                    appAlertHelper.alertText(UserErrorCode.USERNAME_PWD_NOT_CORRECT.getMsg(), username);
                    return new UsernameNotFoundException(FormatUtil.format(UserErrorCode.USERNAME_PWD_NOT_CORRECT.getMsg(), username));
                });
        CustomizeUser user = new CustomizeUser(Long.parseLong(userInfo.getId()), userInfo.getUsername(), userInfo.getPassword(),
                userInfo.getActiveOrganization(), new ArrayList<>());
        user.setEmail(userInfo.getEmail());
        user.setPhone(userInfo.getPhone());
        return user;
    }

    public String generateUniversalLoginCode(String phone) {
        String code = IdHelper.get32UUID();
        cachingTemplate.set(CacheUtil.cacheKey(UNIVERSAL_LOGIN_CODE, phone), code, Duration.ofSeconds(30));
        return code;
    }

    public boolean checkUniversalLoginCode(String phone, String code) {
        String cacheCode = cachingTemplate.get(CacheUtil.cacheKey(UNIVERSAL_LOGIN_CODE, phone), String.class);
        if (cacheCode != null && cacheCode.equals(code)) {
            cachingTemplate.delete(CacheUtil.cacheKey(UNIVERSAL_LOGIN_CODE, phone));
            return true;
        }
        return false;
    }
}
