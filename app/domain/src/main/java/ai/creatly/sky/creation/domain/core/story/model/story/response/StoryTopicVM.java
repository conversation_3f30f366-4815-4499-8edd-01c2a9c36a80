/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.core.story.model.story.response;

import lombok.Data;

@Data
public class StoryTopicVM {
    /**
     * 故事类型中文名称
     */
    private String cnName;
    /**
     * 故事类型英文名称
     */
    private String enName;
    /**
     * 故事类型编码
     */
    private String code;
    /**
     * 故事图
     */
    private String img;
}
