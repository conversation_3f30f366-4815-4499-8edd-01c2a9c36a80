/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.mapper;

import ai.creatly.sky.creation.domain.core.member.model.Member;
import ai.creatly.sky.creation.domain.core.member.model.MemberType;
import ai.creatly.sky.creation.domain.core.plan.helper.PlanBenefitFinder;
import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.enums.BenefitType;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanBuyMethod;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import ai.creatly.sky.creation.domain.core.plan.model.response.BasePlanVM;
import ai.creatly.sky.creation.domain.core.plan.model.response.SubscriptionAbilityVM;
import ai.creatly.sky.creation.domain.core.plan.model.response.SubscriptionPlanVM;
import ai.creatly.sky.creation.domain.core.plan.model.response.TopUpPlanVM;
import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.CreditsBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.MemberBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.TopUpMoreCreditsBenefit;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.currency.MoneyUtil;
import org.jetbrains.annotations.Nullable;
import org.mapstruct.Mapper;

import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * 定价计划相关模型转换
 *
 * <AUTHOR>
 * @version PlanMapper.java, v 0.1 2023-10-12 上午11:34 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface PlanMapper {

    List<SubscriptionPlanVM> toPublicSubscriptionPlanVMs(List<Plan> plans);

    default SubscriptionPlanVM toPublicSubscriptionPlanVM(Plan plan) {
        Asserts.isTrue(plan.getType() == PlanType.SUBSCRIPTION, "will not happen");

        SubscriptionPlanVM subscriptionPlan = new SubscriptionPlanVM();
        this.fillBasePlanVM(plan, subscriptionPlan);
        subscriptionPlan.setBuyMethod(plan.getBuyMethod());
        if (plan.getBuyMethod() == PlanBuyMethod.ONLINE_PAY) {
            Asserts.notNull(plan.getOriginalFee(), "will not happen");
            subscriptionPlan.setOriginalFee(MoneyUtil.centToYuan(plan.getOriginalFee()));

            Asserts.notNull(plan.getRealFee(), "will not happen");
            subscriptionPlan.setRealFee(MoneyUtil.centToYuan(plan.getRealFee()));
        }
        subscriptionPlan.setPeriodType(plan.getPeriodType());
        subscriptionPlan.setAutoRenewed(plan.getAutoRenewed());

        PlanBenefitFinder benefitFinder = plan.benefitFinder();

        // 订阅计划下的会员权益只可能有一条
        var memberBenefit = benefitFinder.findSingleByType(BenefitType.MEMBER, MemberBenefit.class).orElse(null);
        Asserts.notNull(memberBenefit, "will not happen");
        subscriptionPlan.setMemberType(memberBenefit.getMemberType());

        // 订阅计划下的余额权益只可能有一条（未私人定制的企业版等于空）
        benefitFinder.findSingleByType(BenefitType.CREDITS, CreditsBenefit.class)
                .ifPresent(creditsBenefit -> subscriptionPlan.setPeriodCredits(creditsBenefit.getTotalCredits()));

        // 能力权益
        var abilities = benefitFinder.findByType(BenefitType.ABILITY).stream().map(planBenefit -> {
            SubscriptionAbilityVM subscriptionAbility = new SubscriptionAbilityVM();
            subscriptionAbility.setCode(planBenefit.getCode());
            subscriptionAbility.setName(planBenefit.getName());
            return subscriptionAbility;
        }).toList();
        subscriptionPlan.setAbilities(abilities);

        return subscriptionPlan;
    }

    default List<SubscriptionPlanVM> toSubscriptionPlanVMs(List<Plan> plans, @Nullable Member member) {
        return plans.stream().map(plan -> this.toSubscriptionPlanVM(plan, member)).collect(toList());
    }

    private SubscriptionPlanVM toSubscriptionPlanVM(Plan plan, @Nullable Member member) {
        SubscriptionPlanVM subscriptionPlan = this.toPublicSubscriptionPlanVM(plan);
        if (member != null && member.getType() == subscriptionPlan.getMemberType() && member.getType() != MemberType.ENTERPRISE) {
            // 如果用户已经是非企业版会员，则以当前会员的价格为准
            subscriptionPlan.setRealFee(MoneyUtil.centToYuan(member.getPlanRealFee()));
        }
        return subscriptionPlan;
    }

    default List<TopUpPlanVM> toTopUpPlanVMs(List<Plan> plans, @Nullable Plan subscriptionPlan) {
        return plans.stream().map(plan -> this.toTopUpPlanVM(plan, subscriptionPlan)).collect(toList());
    }

    private TopUpPlanVM toTopUpPlanVM(Plan plan, @Nullable Plan subscriptionPlan) {
        TopUpPlanVM topUpPlan = new TopUpPlanVM();
        this.fillBasePlanVM(plan, topUpPlan);
        Asserts.notNull(plan.getRealFee(), "will not happen");
        topUpPlan.setRealFee(MoneyUtil.centToYuan(plan.getRealFee()));

        // 充值计划下的余额权益只可能有一条
        var creditsBenefit = plan.benefitFinder()
                .findSingleByType(BenefitType.CREDITS, CreditsBenefit.class)
                .orElse(null);
        Asserts.notNull(creditsBenefit, "will not happen");
        topUpPlan.setCredits(creditsBenefit.getTopUpCredits());

        // 设置赠送余额
        if (subscriptionPlan == null) {
            topUpPlan.setGiftCredits(creditsBenefit.getGiftCredits());
        } else {
            // 找到会员订阅计划里在当前充值档位的充值送余额权益，获取其赠送的余额
            subscriptionPlan.benefitFinder()
                    .findByType(BenefitType.TOP_UP_MORE_CREDITS, TopUpMoreCreditsBenefit.class)
                    .stream()
                    .filter(benefit -> plan.getId().equals(benefit.getTopUpPlanId()))
                    .findFirst()
                    .ifPresent(benefit -> {
                        // 设置会员赠送的余额
                        Integer giftCredits = creditsBenefit.getGiftCredits() + benefit.getGiftCredits();
                        topUpPlan.setGiftCredits(giftCredits);
                    });
        }
        return topUpPlan;
    }

    private void fillBasePlanVM(Plan plan, BasePlanVM basePlan) {
        basePlan.setId(plan.getId());
        basePlan.setSource(plan.getSource());
        basePlan.setName(plan.getName());
        basePlan.setDescription(plan.getDescription());
        basePlan.setLevel(plan.getLevel());
        if (plan.getBenefitDuration() != null) {
            basePlan.setBenefitValidDays(Math.toIntExact(plan.getBenefitDuration().toDays()));
        }
    }
}
