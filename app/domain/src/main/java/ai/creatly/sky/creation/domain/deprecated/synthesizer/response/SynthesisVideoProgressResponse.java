/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.synthesizer.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @version SynthesisVideoProgressResponse.java, v 0.1 2023-10-11 18:13 syoka
 */
@Data
public class SynthesisVideoProgressResponse {

    /**
     * 视频生成的进度[0~100]
     */
    private Integer progress;

    /**
     * 当前执行到的阶段
     * 下载素材，合成视频，上传视频
     * DOWNLOAD_MATERIAL,MAKING_VIDEO,UPLOAD_VIDEO
     */
    private String subStage;

    /**
     * WAIT,PROCESSING,FINISH
     */
    private String status;
}
