/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.wechat.mp.cipher;

import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version WechatMpCipher.java, v 0.1 2024-10-15 下午8:25 zhoudong
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class WechatMpCipher {

    private final AppAlertHelper appAlertHelper;

    public String sign(String... params) {
        Arrays.sort(params);
        return DigestUtils.sha1Hex(String.join("", params));
    }

    public DecryptedResult decrypt(byte[] aesKey, String appId, String encrypt) {
        try {
            Cipher cipher = getCipher(aesKey, Cipher.DECRYPT_MODE);

            // 执行解密操作
            byte[] encryptedData = Base64.decodeBase64(encrypt);
            byte[] original = cipher.doFinal(encryptedData);

            // 去除补位字符
            byte[] bytes = PKCS7Encoder.decode(original);

            // 分离16位随机字符串,网络字节序和AppId
            byte[] networkOrder = Arrays.copyOfRange(bytes, 16, 20);
            int xmlLength = recoverNetworkBytesOrder(networkOrder);

            String xml = new String(Arrays.copyOfRange(bytes, 20, 20 + xmlLength), StandardCharsets.UTF_8);
            String fromAppId = new String(Arrays.copyOfRange(bytes, 20 + xmlLength, bytes.length), StandardCharsets.UTF_8);

            log.info("微信消息解密后明文:\n{}", xml);

            // appid不相同的情况
            if (!appId.equals(fromAppId)) {
                log.error("微信消息解密后appId不匹配");
                return new DecryptedResult().setSuccess(false);
            }
            return new DecryptedResult().setSuccess(true).setXml(xml).setFromAppId(fromAppId);
        } catch (Exception e) {
            log.error("微信消息解密失败", e);
            appAlertHelper.alertText("微信消息解密失败，appId={}, encrypt={}", appId, encrypt, e);
            return new DecryptedResult().setSuccess(false);
        }
    }

    public EncryptedResult encrypt(byte[] aesKey, String appId, String text) {
        try {
            String randomStr = "578048f401c1f2cb";
            ByteGroup byteCollector = new ByteGroup();
            byte[] randomStrBytes = randomStr.getBytes(StandardCharsets.UTF_8);
            byte[] textBytes = text.getBytes(StandardCharsets.UTF_8);
            byte[] networkBytesOrder = getNetworkBytesOrder(textBytes.length);
            byte[] appidBytes = appId.getBytes(StandardCharsets.UTF_8);

            // randomStr + networkBytesOrder + text + appid
            byteCollector.addBytes(randomStrBytes);
            byteCollector.addBytes(networkBytesOrder);
            byteCollector.addBytes(textBytes);
            byteCollector.addBytes(appidBytes);

            // ... + pad: 使用自定义的填充方式对明文进行补位填充
            byte[] padBytes = PKCS7Encoder.encode(byteCollector.size());
            byteCollector.addBytes(padBytes);

            // 获得最终的字节流, 未加密
            byte[] unencrypted = byteCollector.toBytes();

            // 设置加密模式为AES的CBC模式
            Cipher cipher = getCipher(aesKey, Cipher.ENCRYPT_MODE);

            // 加密
            byte[] encrypted = cipher.doFinal(unencrypted);

            // 使用BASE64对加密后的字符串进行编码
            String ciphertext = Base64.encodeBase64String(encrypted);
            return new EncryptedResult().setSuccess(true).setCiphertext(ciphertext);
        } catch (Exception e) {
            log.error("微信消息加密失败", e);
            appAlertHelper.alertText("微信消息加密失败，text={}, appId={}", text, appId, e);
            return new EncryptedResult().setSuccess(false);
        }
    }

    private static class ByteGroup {
        ArrayList<Byte> byteContainer = new ArrayList<>();

        public byte[] toBytes() {
            byte[] bytes = new byte[byteContainer.size()];
            for (int i = 0; i < byteContainer.size(); i++) {
                bytes[i] = byteContainer.get(i);
            }
            return bytes;
        }

        public void addBytes(byte[] bytes) {
            for (byte b : bytes) {
                byteContainer.add(b);
            }
        }

        public int size() {
            return byteContainer.size();
        }
    }

    // 生成4个字节的网络字节序
    private byte[] getNetworkBytesOrder(int sourceNumber) {
        byte[] orderBytes = new byte[4];
        orderBytes[3] = (byte) (sourceNumber & 0xFF);
        orderBytes[2] = (byte) (sourceNumber >> 8 & 0xFF);
        orderBytes[1] = (byte) (sourceNumber >> 16 & 0xFF);
        orderBytes[0] = (byte) (sourceNumber >> 24 & 0xFF);
        return orderBytes;
    }

    private Cipher getCipher(byte[] aesKey, int mode) throws GeneralSecurityException {
        SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
        IvParameterSpec iv = new IvParameterSpec(aesKey, 0, 16);
        Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
        cipher.init(mode, keySpec, iv);
        return cipher;
    }

    // 还原4个字节的网络字节序
    private int recoverNetworkBytesOrder(byte[] orderBytes) {
        int sourceNumber = 0;
        for (int i = 0; i < 4; i++) {
            sourceNumber <<= 8;
            sourceNumber |= orderBytes[i] & 0xff;
        }
        return sourceNumber;
    }
}
