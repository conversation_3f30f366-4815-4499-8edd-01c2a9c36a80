/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.workspace.model.request;

import ai.creatly.sky.creation.domain.deprecated.workspace.model.canvas.CanvasComponent;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version SaveCanvasPageCommand.java, v 0.1 2023-09-23 15:29 syoka
 */
@Data
public class SaveCanvasPageCommand {

    /**
     * 画布id
     */
    private String id;

    /**
     * 画布title
     */
    private String title;

    /**
     * 画布样式
     */
    private Map<String, String> style;

    /**
     * 页面内的组件列表
     */
    private List<CanvasComponent> components;
}
