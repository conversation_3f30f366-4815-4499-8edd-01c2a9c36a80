/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.mapper;

import ai.creatly.sky.creation.domain.core.digitalhuman.model.response.VideoSubtitleLayerVM;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoSubtitleLayer;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version VideoSubtitleMapper.java, 2024-10-31 下午5:07 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface VideoSubtitleMapper {

    VideoSubtitleLayerVM toVideoSubtitleLayerVM(VideoSubtitleLayer videoSubtitleLayer);
}
