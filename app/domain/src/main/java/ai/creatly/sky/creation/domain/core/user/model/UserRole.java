/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户的角色
 *
 * <AUTHOR>
 * @version UserRole.java, 2024-10-22 下午7:10 zhoudong
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(of = "type")
public class UserRole {

    private UserRoleType type;
}
