/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.task.common.model;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizResult;
import ai.creatly.sky.creation.domain.core.asset.model.OrderedAssetRef;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import static java.util.function.Function.identity;

/**
 *
 * <AUTHOR>
 * @version AiImageTaskResult.java, v0.1 2025-03-03 04:51
 */
@Data
@Accessors(chain = true)
public class AiImageTaskResult implements TaskBizResult {

    private List<OrderedAssetRef> assets;

    public void addAssetIfAbsent(OrderedAssetRef asset) {
        if (this.assets != null && this.exists(asset.getId())) {
            return;
        }
        if (this.assets == null) {
            this.assets = new ArrayList<>();
        }
        this.assets.add(asset);
        this.assets.sort(Comparator.comparing(identity()));
    }

    private boolean exists(long assetId) {
        return assets.stream().map(OrderedAssetRef::getId).anyMatch(id -> id.equals(assetId));
    }
}
