/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.auth.model;

import ai.creatly.sky.creation.domain.support.wechat.model.WechatAppType;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 微信应用名称
 *
 * <AUTHOR>
 * @version WechatAppName.java, v 0.1 2024-10-12 下午4:06 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum WechatAppName {

    mp_zxy("知行元公众号", WechatAppType.mp),
    web_creatly_ai("爱影工坊网站", WechatAppType.website),
    ;

    private final String        desc;
    private final WechatAppType appType;
}
