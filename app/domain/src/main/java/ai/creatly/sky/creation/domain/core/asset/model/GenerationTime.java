/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;

/**
 * 生成耗时
 *
 * <AUTHOR>
 * @version GenerationTime.java, v0.1 2025-03-03 15:50
 */
@Data
@Accessors(chain = true)
public class GenerationTime {

    /**
     * 预估值
     */
    private Duration estimated;
    /**
     * 实际值
     */
    @Nullable
    private Duration actual;

    @JsonIgnore
    public Long getEstimatedMills() {
        return estimated == null ? null : estimated.toMillis();
    }

    @JsonIgnore
    public Long getActualMills() {
        return actual == null ? null : actual.toMillis();
    }
}
