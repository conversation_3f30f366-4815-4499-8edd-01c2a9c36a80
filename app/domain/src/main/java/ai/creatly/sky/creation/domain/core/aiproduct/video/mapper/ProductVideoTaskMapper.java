/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.video.mapper;

import ai.creatly.sky.creation.domain.core.aiproduct.video.model.ProductVideo;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.ProductVideoReTalkTask;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.enums.ReTalkTaskStatus;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.request.ProductVideoReTalkRequest;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.response.ProductVideoReTalkTaskVM;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.task.ProductVideoTaskBizType;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.task.param.ProductVideoReTalkParams;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.BaseBizTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import jakarta.annotation.Nullable;
import jodd.util.StringPool;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.PolyNull;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version ProductVideoTaskMapper.java, v 0.1 2024-05-25 下午8:26 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public abstract class ProductVideoTaskMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    public AiTask buildTask(ProductVideoReTalkRequest request, UserContext userContext) {
        ProductVideoReTalkParams bizParams = new ProductVideoReTalkParams()
                .setVideoFileId(request.getVideoFile().getId())
                .setVideoUrl(UserFileHelper.toOssUrl(request.getVideoFile()))
                .setVideoDuration(request.getVideoFile().getDuration())
                .setVideoFilename(request.getVideoFile().getOriginalFilename())
                .setAudioFileId(request.getAudioFile().getId())
                .setAudioUrl(UserFileHelper.toOssUrl(request.getAudioFile()))
                .setAudioMetadata(Objects.requireNonNull(request.getAudioFile().audioMetadata()))
                .setTitle(StringUtils.defaultIfBlank(request.getTitle(), request.getVideoFile().getOriginalFilename()))
                .setDescription(Objects.toString(request.getDescription(), StringUtils.EMPTY));
        if (request.getFaceImage() != null) {
            bizParams.setFaceImageFileId(request.getFaceImage().getId())
                    .setFaceImageUrl(UserFileHelper.toOssUrl(request.getFaceImage()));
        }
        return AiTask.buildNew()
                .owner(userContext)
                .taskType(AiTaskType.AI_PRODUCT_VIDEO)
                .bizType(ProductVideoTaskBizType.re_talk.getCode())
                .bizNo(IdHelper.getId())
                .subBizNo(StringPool.EMPTY)
                .bizParams(bizParams)
                .build();
    }

    public ProductVideoReTalkTask toReTalkTask(AiTask aiTask, @Nullable ProductVideo productVideo) {
        ProductVideoReTalkParams bizParams = aiTask.parseBizParams(ProductVideoReTalkParams.class);
        ProductVideoReTalkTask reTalkTask = new ProductVideoReTalkTask()
                .setTitle(bizParams.getTitle())
                .setDescription(bizParams.getDescription());
        if (productVideo != null) {
            reTalkTask.setProductVideoId(productVideo.getId())
                    .setCoverUrl(productVideo.getCoverUrl())
                    .setVideoUrl(productVideo.getVideoUrl())
                    .setVideoMills(this.durationToMills(productVideo.getVideoDuration()))
                    .setVideoFormat(productVideo.getVideoFormat())
                    .setVideoResolution(productVideo.getVideoResolution())
                    .setAudioUrl(productVideo.getAudioUrl());
        }
        this.fillBaseTask(aiTask, reTalkTask);
        return reTalkTask;
    }

    @Mapping(target = "taskId", source = "id")
    @Mapping(target = "taskStatus", source = "status")
    @Mapping(target = "taskProgress", source = "execInfo.progress")
    @Mapping(target = "finishedMills", source = "sysResult.finishedDuration")
    @Mapping(target = "estimatedMills", source = "estimatedDuration")
    abstract void fillBaseTask(AiTask task, @MappingTarget BaseBizTask baseTask);

    @PolyNull
    protected Integer durationToMills(@PolyNull Duration duration) {
        return duration == null ? null : (int) duration.toMillis();
    }

    @PolyNull
    public ProductVideoReTalkTaskVM toReTalkTaskVM(@PolyNull ProductVideoReTalkTask task, long uid) {
        if (task == null) {
            return null;
        }
        String coverUrl = userFileHelper.getHttpUrl(task.getCoverUrl(), uid);
        String videoUrl = userFileHelper.getHttpUrl(task.getVideoUrl(), uid);
        String audioUrl = userFileHelper.getHttpUrl(task.getAudioUrl(), uid);
        return this.toReTalkTaskVM(task, coverUrl, videoUrl, audioUrl);
    }

    @Mapping(target = "coverUrl", source = "coverUrl")
    @Mapping(target = "videoUrl", source = "videoUrl")
    @Mapping(target = "audioUrl", source = "audioUrl")
    protected abstract ProductVideoReTalkTaskVM toReTalkTaskVM(ProductVideoReTalkTask task, String coverUrl, String videoUrl,
                                                               String audioUrl);

    public abstract List<AiTaskStatus> toAiTaskStatuses(List<ReTalkTaskStatus> statusList);

    @PolyNull
    protected AiTaskStatus toAiTaskStatus(@PolyNull ReTalkTaskStatus status) {
        return status == null ? null : status.getTaskStatus();
    }
}
