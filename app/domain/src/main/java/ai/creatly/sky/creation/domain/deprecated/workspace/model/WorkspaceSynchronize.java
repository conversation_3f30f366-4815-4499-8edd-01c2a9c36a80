/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.deprecated.workspace.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WorkspaceSynchronize {
    private String          workspaceId;
    private String          bucket;
    private String          ossKey;
    private WorkspaceConfig workspaceConfig;
}
