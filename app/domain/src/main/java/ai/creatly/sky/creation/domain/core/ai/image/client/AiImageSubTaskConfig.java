/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.client;

import ai.creatly.sky.creation.domain.core.ai.image.task.AiImageTaskBizType;

/**
 *
 * <AUTHOR>
 * @version AiImageSubTaskConfig.java, v0.1 2025-03-04 13:03
 */
public interface AiImageSubTaskConfig {

    AiImageTaskBizType getTaskBizType(AiImageAbility ability);

    boolean isSingle(AiImageAbility ability);
}
