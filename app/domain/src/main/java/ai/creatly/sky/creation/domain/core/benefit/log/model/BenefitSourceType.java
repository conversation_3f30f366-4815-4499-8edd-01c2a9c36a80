/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.benefit.log.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 权益来源类型
 *
 * <AUTHOR>
 * @version BenefitSourceType.java, v 0.1 2023-10-27 下午10:47 zhoudong
 */
@RequiredArgsConstructor
@Getter
public enum BenefitSourceType {

    SUBSCRIPTION_PLAN_ORDER("订阅订单", "唯一标识：交易中心订单ID"),
    TOP_UP_PLAN_ORDER("充值订单", "唯一标识：交易中心订单ID"),
    MEMBER_GIFT("会员赠送", "唯一标识：会员ID+赠送时间"),
    USER_SIGN_UP("新用户注册", "唯一标识：本用户ID"),
    INVITE_USER_TO_SIGN_UP("邀请新用户注册", "唯一标识：新用户ID"),
    ;

    private final String desc;
    private final String sourceIdDesc;
}
