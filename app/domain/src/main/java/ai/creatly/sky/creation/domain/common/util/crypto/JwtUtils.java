/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.util.crypto;

import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version : JwtUtils.java, v 1.0 2023年07月09日 17时24分 syoka Exp$
 */
@Slf4j
public class JwtUtils {

    public static final String CLAIM_CHANNEL        = "channel";
    public static final String CLAIM_CHANNEL_WECHAT = "wechat";
    public static final String CLAIM_USERNAME       = "username";
    public static final String SECRET_KEY           = "WYbja4JB8wVQpJj3tB6o8oNdZ4wBTB10cjhq7DtCzzzI";

    /**
     * Check if the token is valid and not expired
     */
    public static boolean validateToken(String token) {
        try {
            JwtParser parser = Jwts.parserBuilder().setSigningKey(SECRET_KEY).build();
            parser.parseClaimsJws(token);
            return true;
        } catch (MalformedJwtException ex) {
            log.warn("Invalid JWT token");
        } catch (ExpiredJwtException ex) {
            log.warn("Expired JWT token");
        } catch (UnsupportedJwtException ex) {
            log.warn("Unsupported JWT token");
        } catch (IllegalArgumentException ex) {
            log.warn("JWT claims string is empty");
        } catch (Exception e) {
            log.warn("there is an error with the signature of you token ");
        }
        return false;
    }

    /**
     * Extract the username from the JWT token
     *
     * @param token -
     * @return -
     */
    public static String getUsername(String token) {
        JwtParser parser = Jwts.parserBuilder().setSigningKey(SECRET_KEY).build();
        return parser.parseClaimsJws(token).getBody().getSubject();
    }

    /**
     * 检查是否来自微信登陆的token
     *
     * @param token -
     * @return -
     */
    public static boolean isWechatLoginToken(String token) {
        Jws<Claims> jwsClaims = Jwts.parserBuilder()
                .setSigningKey(SECRET_KEY)
                .build()
                .parseClaimsJws(token);
        String channel = jwsClaims.getBody().get(CLAIM_CHANNEL, String.class);
        return StringUtils.equals(CLAIM_CHANNEL_WECHAT, channel);
    }

    public static String resolveWechatUnionId(String token) {
        Jws<Claims> jwsClaims = Jwts.parserBuilder()
                .setSigningKey(SECRET_KEY)
                .build()
                .parseClaimsJws(token);

        return jwsClaims.getBody().get(CLAIM_USERNAME, String.class);
    }
}
