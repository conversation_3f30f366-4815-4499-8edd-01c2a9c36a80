/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.service;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;

/**
 * <AUTHOR>
 * @version VideoFileService.java, v 0.1 2024-09-27 下午4:06 zhoudong
 */
public interface VideoFileService {

    UserFile upload(String httpUrl, long fileId, FileBizSource bizSource, UserContext userContext);

    default String clipCoverImageUrl(UserFile videoFile, int seconds) {
        return this.clipCoverImageUrl(videoFile.getBucket(), videoFile.getKey(), videoFile.getAcl(), seconds);
    }

    /**
     * 裁剪视频封面
     *
     * @param bucket    OSS bucket
     * @param sourceKey 源文件key
     * @param targetAcl 目标文件权限
     * @param seconds   裁剪时间点（秒）
     * @return 封面文件地址（OSS格式）
     */
    String clipCoverImageUrl(String bucket, String sourceKey, FileAcl targetAcl, int seconds);
}
