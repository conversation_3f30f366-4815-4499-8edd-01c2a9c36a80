/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.multimedia.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version AudioSliceRequest.java, v 0.1 2024-09-19 下午2:51 zhoudong
 */
@Data
@Accessors(chain = true)
public class AudioSliceRequest {

    private String       bucket;
    @JsonProperty("input_audio_key")
    private String       inputVideoKey;
    /**
     * 输出的文件对象key（支持批量切片）
     */
    @JsonProperty("output_audio_key")
    private List<String> outputAudioKey;
    /**
     * 起止时间戳（支持批量切片）
     */
    private List<long[]> timestamp;
}
