/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.core.aitask.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * AI创作任务执行状态
 *
 * <AUTHOR>
 * @version AiTaskExecStatus.java, v 0.1 2023-06-24 21:08 joton
 */
@Getter
@RequiredArgsConstructor
public enum AiTaskBizExecStatus {

    /**
     * 已提交
     */
    SUBMITTED,
    /**
     * 处理中
     */
    PROCESSING,

    /**
     * 成功
     */
    SUCCEED,

    /**
     * 失败
     */
    FAILED
}
