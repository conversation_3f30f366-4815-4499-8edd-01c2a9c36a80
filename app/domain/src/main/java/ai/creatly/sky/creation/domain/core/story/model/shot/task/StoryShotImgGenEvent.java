/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.shot.task;

import ai.creatly.sky.creation.domain.core.userfile.model.BizFile;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version StoryShotImgGenEvent.java, v 0.1 2024-04-09 19:36 syoka
 */
@Data
@Accessors(chain = true)
public class StoryShotImgGenEvent {

    private Long          taskId;
    private Long          storyId;
    private Long          shotId;
    private List<BizFile> files;
}
