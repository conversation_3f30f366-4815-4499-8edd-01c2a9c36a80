/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 *
 * <AUTHOR>
 * @version ImageAspectRatio.java, v0.1 2025-02-20 21:39
 */
@RequiredArgsConstructor
@Getter
public enum ImageAspectRatio {

    _1_1(1024, 1024),
    _16_9(1344, 576),
    _21_9(1195, 512),
    _4_3(1152, 864),
    _3_2(1024, 682),
    _2_3(832, 1248),
    _3_4(682,1024),
    _9_16(720,1280);

    private final int width;
    private final int height;
}
