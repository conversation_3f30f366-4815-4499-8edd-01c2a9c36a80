/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.util;

import ai.creatly.sky.creation.domain.core.userfile.error.FileErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.model.BaseFileInput;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileStorageType;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.data.problem.exception.SysException;
import jodd.util.StringPool;
import jodd.util.StringUtil;
import lombok.experimental.UtilityClass;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

import java.net.URI;
import java.net.URISyntaxException;

/**
 * <AUTHOR>
 * @version UserFileUtil.java, v 0.1 2023-07-01 19:08 joton
 */
@UtilityClass
public final class UserFileUtil {

    /**
     * 文件基本输入校验
     *
     * @param fileInput 文件基本输入
     */
    public void doCheckInput(BaseFileInput fileInput) {
        if (!fileInput.checkType()) {
            throw new BizException(FileErrorCode.FILE_TYPE_UNSUPPORTED, fileInput.getBizSource(), fileInput.getType());
        }
        if (!fileInput.checkExtension()) {
            String extensions = fileInput.supportedExtensions();
            switch (fileInput.getType()) {
                case IMAGE -> throw new BizException(FileErrorCode.IMAGE_FORMAT_UNSUPPORTED, extensions);
                case AUDIO -> throw new BizException(FileErrorCode.AUDIO_FORMAT_UNSUPPORTED, extensions);
                case VIDEO -> throw new BizException(FileErrorCode.VIDEO_FORMAT_UNSUPPORTED, extensions);
                default -> throw new BizException(FileErrorCode.FILE_FORMAT_UNSUPPORTED, extensions);
            }
        }
    }

    /**
     * 相对于用户的文件路径
     *
     * @param fileId    文件ID
     * @param fileInput 文件基本输入
     * @return 文件路径
     */
    public String getRelativeFilepath(long fileId, BaseFileInput fileInput) {
        UserFileUtil.doCheckInput(fileInput);
        if (StringUtils.isNotBlank(fileInput.getSubPath())) {
            return getRelativeFilepath(fileInput.getBizSource(), fileInput.getSubPath());
        }
        return getRelativeFilepath(fileInput.getBizSource(), String.valueOf(fileId), fileInput.getExtension());
    }

    /**
     * 相对于用户的文件路径
     *
     * @param bizSource    业务来源
     * @param fileBaseName 文件基础名称（不带后缀）
     * @param extension    文件后缀
     * @return 文件路径
     */
    public String getRelativeFilepath(FileBizSource bizSource, String fileBaseName, @Nullable String extension) {
        final String filename;
        if (StringUtils.isBlank(extension)) {
            filename = fileBaseName;
        } else {
            filename = fileBaseName + StringPool.DOT + extension;
        }
        return getRelativeFilepath(bizSource, filename);
    }

    /**
     * 相对于用户的文件路径
     *
     * @param bizSource 业务来源
     * @param subPath   文件子路径（带后缀）
     * @return 文件路径
     */
    public String getRelativeFilepath(FileBizSource bizSource, String subPath) {
        if (subPath.startsWith(StringPool.SLASH)) {
            return bizSource.getDir() + subPath;
        }
        return bizSource.getDir() + "/" + subPath;
    }

    @Nullable
    public FileType resolveFileType(String fileName) {
        if (StringUtil.isNotEmpty(fileName)) {
            String extension = FilenameUtils.getExtension(fileName);
            return switch (extension) {
                case "mp3", "wav" -> FileType.AUDIO;
                case "jpeg", "jpg", "png", "gif" -> FileType.IMAGE;
                case "mp4", "mpeg" -> FileType.VIDEO;
                default -> null;
            };
        }
        return null;
    }

    public FileStorageType resolveStorageType(String ossUrl) {
        try {
            URI uri = new URI(ossUrl);
            String scheme = uri.getScheme();
            return FileStorageType.getByScheme(scheme);
        } catch (URISyntaxException e) {
            throw new SysException(CommonErrorCode.UNSPECIFIED, e);
        }
    }
}
