/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.repository;

import ai.creatly.sky.creation.domain.core.aitask.error.AiTaskErrorCode;
import ai.creatly.sky.creation.domain.core.aitask.model.*;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import com.jspeeder.core.data.enums.ICode;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.data.repository.SingleRepository;
import jodd.util.StringPool;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;
import java.util.*;

/**
 * AI创作任务持久化
 *
 * <AUTHOR>
 * @version AiTaskRepository.java, v 0.1 2023-06-24 01:01 joton
 */
public interface AiTaskRepository extends SingleRepository<AiTask, AiTaskQO> {

    @Override
    long create(AiTask aiTask);

    AiTask createIdempotent(AiTask aiTask);

    @Override
    void deleteById(long id);

    @Override
    void updateById(AiTask aiTask);

    @Override
    Optional<AiTask> queryOptionalById(long id);

    @Override
    @NotNull
    default AiTask queryById(long id) {
        return this.queryOptionalById(id).orElseThrow(() -> new SysException(AiTaskErrorCode.TASK_NOT_EXISTS));
    }

    default List<AiTask> queryByIds(List<Long> ids) {
        return this.queryByIds(new HashSet<>(ids));
    }

    List<AiTask> queryByIds(Set<Long> ids);

    /**
     * 统计某个用户的某种任务未完成的任务数量
     *
     * @param ownerId  任务owner
     * @param taskType 任务类型
     * @param bizType  关联的业务类型
     * @return 数量
     */
    long countInProgressWithUser(long ownerId, AiTaskType taskType, @Nullable String bizType);

    /**
     * 统计某个任务未完成的数量
     *
     * @param taskType 任务类型
     * @param bizType  关联的业务类型
     * @return 数量
     */
    long countInProgress(AiTaskType taskType, @Nullable String bizType);

    default Optional<AiTask> queryByUnique(AiTask aiTask) {
        return this.queryByUnique(aiTask.getTaskType(), aiTask.getBizType(), aiTask.getBizNo(), aiTask.getSubBizNo());
    }

    default Optional<AiTask> queryByUnique(AiTaskUniqueValue uniqueValue) {
        if (Objects.isNull(uniqueValue.getSubBizNo())) {
            return this.queryByUnique(uniqueValue.getTaskType(), uniqueValue.getBizType().getCode(), uniqueValue.getBizNo());
        }
        return this.queryByUnique(uniqueValue.getTaskType(), uniqueValue.getBizType().getCode(), uniqueValue.getBizNo(),
                uniqueValue.getSubBizNo());
    }

    default Optional<AiTask> queryByUnique(AiTaskType taskType, String bizNo) {
        return this.queryByUnique(taskType, StringPool.EMPTY, bizNo, StringPool.EMPTY);
    }

    default Optional<AiTask> queryByUnique(AiTaskType taskType, String bizType, String bizNo) {
        return this.queryByUnique(taskType, bizType, bizNo, StringPool.EMPTY);
    }

    Optional<AiTask> queryByUnique(AiTaskType taskType, String bizType, String bizNo, String subBizNo);

    Optional<AiTask> queryLatestByCondition(AiTaskQO qo);

    default List<AiTask> queryByTaskTypeAndBizNo(AiTaskType taskType, ICode bizType, String bizNo) {
        return this.queryByTaskTypeAndBizNo(taskType, bizType.getCode(), bizNo);
    }

    List<AiTask> queryByTaskTypeAndBizNo(AiTaskType taskType, String bizType, String bizNo);

    @Override
    default Page<AiTask> queryPage(AiTaskQO qo, Pageable pageable) {
        return SingleRepository.super.queryPage(qo, pageable);
    }

    Page<AiTask> queryPageByTaskType(AiTaskType taskType, Pageable pageable);

    Page<AiTask> queryPageByOwnerIdAndTaskType(long ownerId, AiTaskType taskType, Pageable pageable);

    Page<AiTask> queryPageByOwnerIdAndTaskTypeAndBizType(long ownerId, AiTaskType taskType, List<String> bizTypes, Pageable pageable);

    /**
     * 查询待执行的可自动执行任务（按照优先级正序，数字越小，优先级越高）
     *
     * @param taskType 任务类型
     * @param bizType  业务类型
     * @param limit    查询数量限制
     * @return 任务列表
     */
    List<AiTask> queryWaitingForAutoExec(AiTaskType taskType, String bizType, int limit);

    void startById(long id, @Nullable UpdatableAiTask aiTask, AiTaskExecInfo execInfo);

    void rollbackRunningToCreatedById(long id, @Nullable UpdatableAiTask aiTask, AiTaskExecInfo execInfo);

    void cancelById(long id, String reason, @Nullable UpdatableAiTask aiTask, AiTaskExecInfo execInfo);

    void finishById(long id, ZonedDateTime createdAt, @Nullable UpdatableAiTask aiTask, AiTaskExecInfo execInfo);

    default void dryRunById(long id, UpdatableAiTask aiTask) {
        this.dryRunById(id, aiTask, null);
    }

    void dryRunById(long id, @Nullable UpdatableAiTask aiTask, @Nullable AiTaskExecInfo execInfo);

    void completeById(long id, @Nullable UpdatableAiTask aiTask, AiTaskExecInfo execInfo);

    void forceCancelById(long id);

    void softDeleteById(long id);

    /**
     * 任务挂起
     *
     * @param id 任务ID
     * @return 是否成功
     */
    boolean suspendById(long id);

    /**
     * 任务恢复
     *
     * @param id 任务ID
     * @return 是否成功
     */
    boolean resumeById(long id);

    /**
     * 调整任务执行优先级
     *
     * @param id       任务ID
     * @param priority 优先级
     */
    void updatePriority(long id, int priority);

    /**
     * 开启或关闭任务的自动调度执行
     *
     * @param id       任务ID
     * @param autoExec 是否自动执行
     * @return 是否成功
     */
    boolean updateAutoExec(long id, boolean autoExec);

    boolean updateBizNo(long id, String bizNo);

    /**
     * 更新任务的业务状态（仅支持非自动调度执行的任务可更新）
     *
     * @param id            任务ID
     * @param bizStatusFrom 原业务状态
     * @param bizStatusTo   新业务状态
     */
    void updateBizStatus(long id, String bizStatusFrom, String bizStatusTo);
}
