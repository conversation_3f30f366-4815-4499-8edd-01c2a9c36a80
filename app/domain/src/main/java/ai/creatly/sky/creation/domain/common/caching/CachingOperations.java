/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.caching;

import org.jetbrains.annotations.Nullable;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version CachingOperations.java, v 0.1 2024-01-23 下午6:17 zhoudong
 */
public interface CachingOperations {

    /*------------------------------- 写操作 -------------------------------*/

    void set(String key, @Nullable String value, Duration timeout);

    void set(String key, byte[] value, Duration timeout);

    void set(String key, @Nullable Object value, Duration timeout);

    boolean setIfAbsent(String key, @Nullable String value, Duration timeout);

    boolean setIfAbsent(String key, byte[] value, Duration timeout);

    boolean setIfAbsent(String key, @Nullable Object value, Duration timeout);

    void delete(String key);

    @Nullable <T> T getAndDelete(String key, Class<T> valueType);

    void delete(@Nullable Collection<String> keys);

    long increment(String key);

    long increment(String key, long delta);

    long decrement(String key);

    long decrement(String key, long delta);

    void listLeftPush(String key, @Nullable String value, Duration timeout);

    void listLeftPush(String key, byte[] value, Duration timeout);

    void listLeftPush(String key, @Nullable Object value, Duration timeout);

    void listRightPush(String key, @Nullable String value, Duration timeout);

    void listRightPush(String key, byte[] value, Duration timeout);

    void listRightPush(String key, @Nullable Object value, Duration timeout);

    void listTrim(String key, long start, long end);

    void hashPut(String key, String hashKey, @Nullable String value, Duration timeout);

    void hashPut(String key, String hashKey, byte[] value, Duration timeout);

    void hashPut(String key, String hashKey, @Nullable Object value, Duration timeout);

    boolean hashPutIfAbsent(String key, String hashKey, @Nullable String value, Duration timeout);

    boolean hashPutIfAbsent(String key, String hashKey, byte[] value, Duration timeout);

    boolean hashPutIfAbsent(String key, String hashKey, @Nullable Object value, Duration timeout);

    void hashDelete(String key, String hashKey);

    /*------------------------------- 读操作 -------------------------------*/

    boolean hasKey(String key);

    Set<String> keys(String pattern);

    default Set<String> keysStartedWith(String prefix) {
        return this.keys(prefix + "*");
    }

    @Nullable
    default String getString(String key) {
        return this.get(key, String.class);
    }

    @Nullable
    default Integer getInt(String key) {
        return this.get(key, Integer.class);
    }

    @Nullable
    default Long getLong(String key) {
        return this.get(key, Long.class);
    }

    byte[] getBinary(String key);

    @Nullable <T> T get(String key, Class<T> valueType);

    @Nullable <T> List<T> getList(String key, Class<T> elementType);

    @Nullable
    default String listLeftPopString(String key) {
        return this.listLeftPop(key, String.class);
    }

    @Nullable
    default Integer listLeftPopInt(String key) {
        return this.listLeftPop(key, Integer.class);
    }

    @Nullable
    default Long listLeftPopLong(String key) {
        return this.listLeftPop(key, Long.class);
    }

    byte[] listLeftPopBinary(String key);

    @Nullable <T> T listLeftPop(String key, Class<T> valueType);

    @Nullable
    default String listRightPopString(String key) {
        return this.listRightPop(key, String.class);
    }

    @Nullable
    default Integer listRightPopInt(String key) {
        return this.listRightPop(key, Integer.class);
    }

    @Nullable
    default Long listRightPopLong(String key) {
        return this.listRightPop(key, Long.class);
    }

    byte[] listRightPopBinary(String key);

    @Nullable <T> T listRightPop(String key, Class<T> valueType);

    default List<@Nullable String> listRangeString(String key, long start, long end) {
        return this.listRange(key, start, end, String.class);
    }

    @Nullable <T> List<@Nullable T> listRange(String key, long start, long end, Class<T> elementType);

    default List<@Nullable String> listAllString(String key) {
        return this.listAll(key, String.class);
    }

    default @Nullable <T> List<@Nullable T> listAll(String key, Class<T> elementType) {
        return this.listRange(key, 0, -1, elementType);
    }

    boolean hashHasKey(String key, String hashKey);

    @Nullable
    default String hashGetString(String key, String hashKey) {
        return this.hashGet(key, hashKey, String.class);
    }

    @Nullable
    default Integer hashGetInt(String key, String hashKey) {
        return this.hashGet(key, hashKey, Integer.class);
    }

    @Nullable
    default Long hashGetLong(String key, String hashKey) {
        return this.hashGet(key, hashKey, Long.class);
    }

    byte[] hashGetBinary(String key, String hashKey);

    @Nullable <T> T hashGet(String key, String hashKey, Class<T> valueType);

    @Nullable <T> List<T> hashGetList(String key, String hashKey, Class<T> elementType);
}
