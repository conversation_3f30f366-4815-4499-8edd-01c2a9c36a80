/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.credit.repository;

import ai.creatly.sky.creation.domain.core.credit.model.*;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import org.jetbrains.annotations.Nullable;

/**
 * 用户余额收支流水持久化 // TODO: 2023/10/29 查询快照数据可以加缓存
 *
 * <AUTHOR>
 * @version CreditLogRepository.java, v 0.1 2023-12-28 下午4:31 zhoudong
 */
public interface CreditLogRepository {

    /**
     * 新增一条余额收支流水
     *
     * @param creditLog 余额收支流水
     */
    void create(CreditLog creditLog);

    /**
     * 根据用户ID查询余额流水列表
     *
     * @param uid      用户ID
     * @param pageable 分页参数
     * @return 余额收支流水列表
     */
    Page<CreditLog> queryPage(long uid, Pageable pageable);

    /**
     * 根据用户ID查询余额账户流水明细列表
     *
     * @param uid      用户ID
     * @param pageable 分页参数
     * @return 余额收支流水列表
     */
    Page<CreditHistory> queryCreditHistoryPage(long uid, Pageable pageable);

    /**
     * 根据业务号判断是否存在对应的支出记录
     *
     * @param expense 余额支出请求
     * @return 是否存在
     */
    default boolean existsExpenseCreditLog(CreditsExpense expense) {
        return this.existsExpenseLogByBizNo(expense.getUid(), expense.getBizType(), expense.getBizNo());
    }

    /**
     * 根据业务号判断是否存在对应的支出记录
     *
     * @param uid     用户ID
     * @param bizType 业务类型
     * @param bizNo   业务号
     * @return 是否存在
     */
    boolean existsExpenseLogByBizNo(long uid, CreditLogBizType bizType, String bizNo);

    /**
     * 根据业务号查询唯一的支出记录
     *
     * @param refund 余额退款请求
     * @return 余额收支流水
     */
    @Nullable
    default CreditLog queryExpennseCreditLog(CreditsRefund refund) {
        return this.queryExpenseLogByBizNo(refund.getUid(), refund.getBizType(), refund.getBizNo());
    }

    /**
     * 根据业务号查询唯一的支出记录
     *
     * @param uid     用户ID
     * @param bizType 业务类型
     * @param bizNo   业务号
     * @return 余额收支流水
     */
    @Nullable
    CreditLog queryExpenseLogByBizNo(long uid, CreditLogBizType bizType, String bizNo);

    /**
     * 根据业务号判断是否存在对应的收入记录
     *
     * @param refund 余额退款请求
     * @return 是否存在
     */
    default boolean existsIncomeCreditLog(CreditsRefund refund) {
        return this.existsIncomeLogByBizNo(refund.getUid(), refund.getBizType(), refund.getBizNo());
    }

    /**
     * 根据业务号判断是否存在对应的收入记录
     *
     * @param uid     -
     * @param bizType -
     * @param bizNo   -
     * @return 是否存在
     */
    boolean existsIncomeLogByBizNo(long uid, CreditLogBizType bizType, String bizNo);
}
