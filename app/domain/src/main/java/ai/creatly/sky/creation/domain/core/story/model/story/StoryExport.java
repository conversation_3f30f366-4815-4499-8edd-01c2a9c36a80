/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.story;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 记录下载故事
 *
 * <AUTHOR>
 * @version DownStory.java, v 0.1 2024-05-08 23:09 heb
 */
@Data
@Accessors(chain = true)
public class StoryExport {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 创建时间
     */
    private ZonedDateTime createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime updatedAt;
    /**
     * 用户ID
     */
    private Long uid;
    /**
     * 状态
     */
    private String status;
    /**
     * 下载文件ID
     */
    private Long downFileId;
    /**
     * 下载文件URL
     */
    private String downFileUrl;
    /**
     * 故事ID
     */
    private Long storyId;
    /**
     * 导出格式
     */
    private String exportFormat;
}
