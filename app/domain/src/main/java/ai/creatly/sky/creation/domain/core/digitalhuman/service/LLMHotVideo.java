/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.service;

import ai.creatly.sky.creation.domain.core.digitalhuman.model.NarrativeStyle;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.response.HotVideoRewriteResponse;
import ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal.ChatMessageUtil;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.factory.LLMConfigDomainService;
import ai.creatly.sky.creation.domain.support.aichat.langchain.service.CopilotScenarioRegistrar;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version LLMHotVideo.java, v 0.1 2024-07-02 11:06 syoka
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class LLMHotVideo {

    /**
     * 叙事风格改写
     */
    public static final  String                    STYLE_REWRITE               = "styleRewrite";
    /**
     * 观点内容改写
     */
    public static final  String                    VIEW_FUSION                 = "viewFusion";
    /**
     * 提示词相关
     */
    private final static String                    HOT_VIDEO_IMITATE           = "SYSTEM:INSTRUCTION_HOT_VIDEO_IMITATE";
    private final static String                    HOT_VIDEO_NARRATIVE_REWRITE = "SYSTEM:HOT_VIDEO_NARRATIVE_REWRITE";
    private final        CopilotScenarioRegistrar  copilotScenarioRegistrar;
    private final        LLMConfigDomainService    llmConfigDomainService;
    private final        HotVideoWriteOutputParser hotVideoWriteOutputParser;


    /**
     * 重写叙事风格
     *
     * @param originInput    视频原文内容
     * @param narrativeStyle 风格类型
     */
    public String rewriteNarrativeStyle(String originInput, NarrativeStyle narrativeStyle) {
        String promptArr = copilotScenarioRegistrar.getScenarioPromptsByCode(HOT_VIDEO_NARRATIVE_REWRITE);
        // TODO后面要基于example做持久化
        // 故事相关提示词要求包含参数:<storyType>
        Map<String, Object> params = Map.of("originInput", originInput, "narrativeStyle", narrativeStyle.getName());
        List<ChatMessage> chatMessages = ChatMessageUtil.parseToChatMessage(promptArr, Map.of("input", params));

        // 获取llm模型配置
        ChatLanguageModel chatLanguageModel = llmConfigDomainService.getActiveChatLanguageModel();

        Response<AiMessage> response = chatLanguageModel.generate(chatMessages);
        AiMessage aiMessage = response.content();
        HotVideoRewriteResponse videoImitateResponse = hotVideoWriteOutputParser.parse(aiMessage.text());
        return videoImitateResponse.getOutput();
    }

    /**
     * 基于用户观点改写原视频内容
     *
     * @param originInput 原视频内容
     * @param userInput   用户观点
     */
    public String rewriteViewPoint(String originInput, String userInput) {
        String promptArr = copilotScenarioRegistrar.getScenarioPromptsByCode(HOT_VIDEO_IMITATE);
        // 故事相关提示词要求包含参数:<storyType>
        Map<String, Object> params = Map.of("originInput", originInput, "userInput", userInput);
        List<ChatMessage> chatMessages = ChatMessageUtil.parseToChatMessage(promptArr, Map.of("input", params));

        // 获取llm模型配置
        ChatLanguageModel chatLanguageModel = llmConfigDomainService.getActiveChatLanguageModel();

        Response<AiMessage> response = chatLanguageModel.generate(chatMessages);
        AiMessage aiMessage = response.content();
        HotVideoRewriteResponse videoImitateResponse = hotVideoWriteOutputParser.parse(aiMessage.text());
        return videoImitateResponse.getOutput();
    }
}
