/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role;

import lombok.Data;

import java.util.List;

/**
 * 涉及角色提示词结构均放到这里
 *
 * <AUTHOR>
 * @version StoryRolePromptSchema.java, v 0.1 2024-03-28 16:49 syoka
 */
public class StoryRolePromptSchema {


    @Data
    public static class StoryRoleOutputSchema {
        private RoleGenerateStepOne step_one;
        private RoleGenerateStepTwo step_two;
    }

    @Data
    public static class RoleGenerateStepOne {
        /**
         * 人物个数
         */
        private Integer person_num;
        /**
         * 是否存在物品
         */
        private boolean if_key_items;
        /**
         * 物品数量个数
         */
        private Integer key_items_num;
    }

    @Data
    public static class RoleGenerateStepTwo {
        /**
         * 人物详细信息
         */
        private List<PersonInfo> person_info;
    }

    @Data
    public static class RoleDetailInfo {
        private List<PersonInfo> person_lastinfo;
    }

    @Data
    public static class PersonInfo {
        /**
         * 人物名
         */
        private String       person_name;
        /**
         * 人物简称
         */
        private List<String> person_pronoun;
        /**
         * 年龄（中年）
         */
        private String       person_generation;
        /**
         * 性别
         */
        private String       person_gender;
        /**
         * 性格
         */
        private String       person_character;
        /**
         * 外贸特征
         */
        private String       physical_features;
        /**
         * 衣服特征
         */
        private String       clothing_features;
        /**
         * 附件特征
         */
        private String       accessory_features;
        /**
         * 声音特征
         */
        private String       voice_type;
    }
}
