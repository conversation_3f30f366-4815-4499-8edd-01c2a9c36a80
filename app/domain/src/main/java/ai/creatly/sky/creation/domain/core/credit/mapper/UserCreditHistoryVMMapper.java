/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.credit.mapper;

import ai.creatly.sky.creation.domain.core.credit.model.CreditHistory;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.response.UserCreditHistoryVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version UserCreditHistoryVMMapper.java, v 0.1 2023-12-20 11:35 syoka
 */
@Mapper(config = BaseMapperConfig.class)
public interface UserCreditHistoryVMMapper {

    UserCreditHistoryVM userCreditHistoryVM(CreditHistory history);

    default String toBizType(CreditLogBizType bizType) {
        return bizType.getDesc();
    }
}
