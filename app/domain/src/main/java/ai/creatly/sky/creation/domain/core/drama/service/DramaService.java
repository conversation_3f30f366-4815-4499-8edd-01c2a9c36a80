/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.service;

import ai.creatly.sky.creation.domain.common.scheduler.SchedulerPools;
import ai.creatly.sky.creation.domain.support.category.model.CategoryNode;
import ai.creatly.sky.creation.domain.support.category.model.enums.CategoryDomain;
import ai.creatly.sky.creation.domain.support.category.service.CategoryRepository;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * @version DramaService.java, 2024-12-16 下午4:34 zhoudong
 */
@Service
@RequiredArgsConstructor
public class DramaService implements ApplicationListener<ContextRefreshedEvent> {

    private final DramaRepository    dramaRepository;
    private final CategoryRepository categoryRepository;
    @Qualifier(SchedulerPools.DRAMA_SERVICE_CACHE_REFRESH)
    private final TaskScheduler      taskScheduler;

    private final LoadingCache<String, List<CategoryNode>> SUMMARY_PRODUCT_CATEGORY_TREES_CACHE = Caffeine.newBuilder()
            .maximumSize(1000)
            .build(key -> this.summaryProductCategoryTreesFromDb());

    /**
     * 汇总剧情已支持的产品类目
     *
     * @return 类目树
     */
    public List<CategoryNode> summaryProductCategoryTrees() {
        return SUMMARY_PRODUCT_CATEGORY_TREES_CACHE.get("1");
    }

    private List<CategoryNode> summaryProductCategoryTreesFromDb() {
        List<String> categoryPaths = dramaRepository.summaryProductCategoryPaths();
        return categoryRepository.queryTreesByDomainAndPaths(CategoryDomain.video_product, categoryPaths);
    }

    @Override
    public void onApplicationEvent(@NotNull ContextRefreshedEvent event) {
        // Schedule cache refresh task
        taskScheduler.scheduleWithFixedDelay(() -> SUMMARY_PRODUCT_CATEGORY_TREES_CACHE.refresh("1"), Duration.ofMinutes(10));
    }
}
