/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aivideo.model;

import ai.creatly.sky.creation.domain.core.aivideo.model.enums.AiVideoAcl;
import ai.creatly.sky.creation.domain.core.aivideo.model.enums.AiVideoBizSource;
import ai.creatly.sky.creation.domain.support.multimedia.enums.VideoFormat;
import com.jspeeder.core.model.BizStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Set;

/**
 * AI视频
 *
 * <AUTHOR>
 * @version AiVideo.java, v 0.1 2024-05-06 下午3:04 zhoudong
 */
@Data
@Accessors(chain = true)
public class AiVideo {

    /**
     * 主键ID
     */
    private Long             id;
    /**
     * 创建时间
     */
    private ZonedDateTime    createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime    updatedAt;
    /**
     * 用户ID
     */
    private Long             uid;
    /**
     * 状态
     */
    private BizStatus        status;
    /**
     * 访问权限
     */
    private AiVideoAcl       acl;
    /**
     * 业务来源
     */
    private AiVideoBizSource bizSource;
    /**
     * 业务编号
     */
    private String           bizNo;
    /**
     * 视频标题
     */
    private String           title;

    /**
     * 视频文件ID
     */
    private Long             fileId;
    /**
     * 视频文件地址（OSS格式）
     */
    private String           fileUrl;

    private String           fileHttpUrl;
    /**
     * 视频时长
     */
    private Duration         duration;
    /**
     * 视频格式（小写）
     */
    private VideoFormat      videoFormat;
    /**
     * 视频分辨率（宽度x高度）
     */
    private String           resolution;
    /**
     * 封面图文件ID
     */
    private Long             coverFileId;
    /**
     * 封面图地址（<a href="https://help.aliyun.com/zh/oss/user-guide/video-snapshots">视频截帧文档</a>）
     */
    private String           coverUrl;

    private String           coverHttpUrl;
    /**
     * 作者的用户ID
     */
    private Long             authorUid;
    /**
     * 作者名
     */
    private String           authorName;
    /**
     * 作者头像地址（HTTP格式）
     */
    private String           authorAvatar;

    private String           avatarHttpUrl;
    /**
     * 标签列表
     */
    private Set<String>      tagNames;
    /**
     * 点赞数量
     */
    private Integer          likeCount;
}
