/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.bizconfig.model.instance;

import ai.creatly.sky.creation.domain.support.bizconfig.model.schema.PropDataType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 通用业务配置属性（实例）
 *
 * <AUTHOR>
 * @version BizConfigProp.java, v 0.1 2023-05-08 15:28 joton
 * @see BizConfig
 * @see ai.creatly.sky.creation.domain.support.bizconfig.model.schema.BizConfigPropSchema
 */
@Data
@Accessors(chain = true)
public class BizConfigProp {
    /**
     * 属性字段（在一个模式下唯一）
     */
    private String key;
    /**
     * 属性值数据类型
     */
    private PropDataType dataType;
    /**
     * 属性值（如果是对象或数组类型，则该值存储为JSON结构）
     */
    private String value;
}
