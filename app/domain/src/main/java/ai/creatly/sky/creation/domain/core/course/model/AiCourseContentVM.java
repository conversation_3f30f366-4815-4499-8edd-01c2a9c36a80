/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.domain.core.course.model;


import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * AI课程详情
 */
@Data
@SuppressWarnings({"all", "unchecked", "rawtypes", "this-escape"})
public class AiCourseContentVM {

    private Long                    courseId;
    private Long                    contentId;
    private String                  category;
    private String                  title;
    private String                  videoUrl;
    private Long                    priority;
    private String                  duration;
    private String                  content;
    private Integer                 status;
    private Long                    seconds;
    private boolean                 paid;
    private boolean                 isFree;
    private List<AiCourseContentVM> subList = new ArrayList<>();

}
