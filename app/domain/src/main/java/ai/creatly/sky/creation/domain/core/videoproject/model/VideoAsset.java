/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model;

import ai.creatly.sky.creation.domain.common.integration.oss.OssUtil;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;

/**
 * 视频素材
 *
 * <AUTHOR>
 * @version VideoAsset.java, v 0.1 2024-09-21 下午3:48 zhoudong
 */
@Data
@Accessors(chain = true)
public class VideoAsset {

    /**
     * 素材ID
     */
    private Long         id;
    /**
     * 素材名称（冗余）
     */
    private String       name;
    /**
     * 素材文件类型
     */
    private FileType     fileType;
    /**
     * 素材文件地址（OSS协议）
     */
    private String       url;
    /**
     * 素材封面图地址（OSS协议）
     */
    private String       coverUrl;
    /**
     * 素材时长（仅视频才有）
     */
    @Nullable
    private Duration     duration;

    @JsonIgnore
    public String getFileKey() {
        return OssUtil.resolveKey(this.url);
    }
}
