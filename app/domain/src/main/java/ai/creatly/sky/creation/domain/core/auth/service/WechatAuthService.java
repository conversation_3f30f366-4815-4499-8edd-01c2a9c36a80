/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.auth.service;

import ai.creatly.sky.creation.domain.common.caching.CacheUtil;
import ai.creatly.sky.creation.domain.common.caching.CachingTemplate;
import ai.creatly.sky.creation.domain.common.env.RuntimeEnv;
import ai.creatly.sky.creation.domain.common.env.StandardEnv;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.common.util.crypto.AuthTokenHelper;
import ai.creatly.sky.creation.domain.common.util.crypto.JwtUtils;
import ai.creatly.sky.creation.domain.core.auth.event.WechatSignInEvent;
import ai.creatly.sky.creation.domain.core.auth.model.WechatAppName;
import ai.creatly.sky.creation.domain.core.auth.model.response.WechatSignInH5Param;
import ai.creatly.sky.creation.domain.core.auth.model.response.WechatSignInWebParam;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import ai.creatly.sky.creation.domain.core.user.model.WechatOpenId;
import ai.creatly.sky.creation.domain.core.user.repository.UserRepository;
import ai.creatly.sky.creation.domain.support.wechat.model.WechatUser;
import ai.creatly.sky.creation.domain.support.wechat.mp.WechatMpClient;
import ai.creatly.sky.creation.domain.support.wechat.mp.model.account.WechatMpQrCode;
import ai.creatly.sky.creation.domain.support.wechat.oauth2.WechatOAuth2Client;
import ai.creatly.sky.creation.domain.support.wechat.oauth2.model.WechatUserToken;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.util.Validates;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version WechatAuthService.java, v 0.1 2024-10-12 下午3:36 zhoudong
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WechatAuthService {

    private final static String WECHAT_SID_LOGIN_TOKEN = "wechat_sid_login_token:{}";
    private final static String WECHAT_UNION_ID_OPENID = "wechat_union_id_openid:{}";

    private final WechatMpClient            wechatMpClient;
    private final WechatOAuth2Client        wechatOAuth2Client;
    private final CachingTemplate           cachingTemplate;
    private final CsrfService               csrfService;
    private final UserRepository            userRepository;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final RuntimeEnv                runtimeEnv;
    private final AuthTokenHelper           authTokenHelper;

    /**
     * 生成微信扫码登录所需参数
     *
     * @param appName      微信应用名称
     * @param cacheTimeout 缓存时长
     */
    public WechatSignInWebParam getWechatSignInWebScanParam(WechatAppName appName, Duration cacheTimeout) {
        String env = runtimeEnv.getEnv().name().toLowerCase();
        String sid = FormatUtil.format("{}:scan-sign-in:{}:{}", AppConstants.APP_NAME, env, IdHelper.getId());
        WechatMpQrCode qrCode = wechatMpClient.generateQrCode(sid, appName, Duration.ofMinutes(10));
        // 缓存sid
        this.cacheWechatSignInSid(sid, cacheTimeout);
        return new WechatSignInWebParam()
                .setSid(sid)
                .setTicket(qrCode.getTicket())
                .setQrcodeContent(qrCode.getUrl());
    }

    @Nullable
    public StandardEnv getSignInWebScanSidEnv(String sid) {
        if (sid.startsWith(AppConstants.APP_NAME + ":scan-sign-in:")) {
            String env = sid.split(":")[2];
            return StandardEnv.valueOf(env.toUpperCase());
        }
        return null;
    }

    /**
     * 生成微信H5网页登录所需参数
     *
     * @param cacheTimeout 缓存时长
     */
    public WechatSignInH5Param getWechatSignInH5Param(Duration cacheTimeout) {
        // 准备csrf token和匿名会话sid
        String csrf = IdHelper.getPrettyId();
        String sid = IdHelper.get32UUID();
        String state = this.buildState(csrf, sid);
        // 缓存token
        csrfService.saveCsrfToken(csrf, cacheTimeout);
        // 缓存sid
        this.cacheWechatSignInSid(sid, cacheTimeout);
        return new WechatSignInH5Param().setSid(sid).setState(state);
    }

    private void cacheWechatSignInSid(String sid, Duration cacheTimeout) {
        cachingTemplate.set(CacheUtil.cacheKey("wechat-sign-in:sid:", sid), 1, cacheTimeout);
    }

    public void checkWechatSignInSid(String sid) {
        Validates.isTrue(cachingTemplate.hasKey(CacheUtil.cacheKey("wechat-sign-in:sid:", sid)), "登录凭证非法");
    }

    /**
     * 通过微信授权码置换平台自己的token
     */
    public void handleWechatLoginCallback(String code, String state, WechatAppName appName) {
        log.info("[handleWechatLoginCallback]code:{},state:{}", code, state);
        Map<String, String> param = this.parseState(state);
        String csrf = param.get("csrf");
        String sid = param.get("sid");

        // 微信token置换jwtToken，缓存微信匿名token，在下一步手机号登录/注册时，用于微信绑定验证
        // 校验csrfToken
        csrfService.validateCsrfToken(csrf);
        // 网页授权code置换微信token
        WechatUserToken accessToken = wechatOAuth2Client.getUserAccessToken(code, appName);
        WechatUser wechatUser = new WechatUser()
                .setAppType(appName.getAppType())
                .setAppId(accessToken.getAppId())
                .setAppName(appName.name())
                .setOpenId(accessToken.getOpenid())
                .setUnionId(accessToken.getUnionid());
        this.handleWechatUserEvent(sid, wechatUser);
    }

    public void handleWechatUserEvent(String sid, WechatUser wechatUser) {
        String unionId = wechatUser.getUnionId();
        @Nullable UserInfo user = userRepository.queryOptionalByWechatUnionId(unionId).orElse(null);

        // 针对存量用户，追加微信openId
        WechatOpenId wechatOpenId = WechatOpenId.from(wechatUser);
        if (user != null && !user.getWechatOpenIds().contains(wechatOpenId)) {
            user.addWechatOpenId(wechatOpenId);
            userRepository.updateById(new UserInfo()
                    .setId(user.getId())
                    .setWechatOpenIds(user.getWechatOpenIds())
            );
        }

        // 缓存微信unionId对应的openid
        cachingTemplate.set(FormatUtil.format(WECHAT_UNION_ID_OPENID, unionId), wechatUser, Duration.ofMinutes(10));

        // 存量用户直接返回标准token，新用户返回一次性匿名token
        final String jwtToken;
        if (Objects.isNull(user)) {
            jwtToken = authTokenHelper.generateWechatAnonymousToken(unionId);
        } else {
            jwtToken = authTokenHelper.generateToken(user.getUsername());
        }

        if (StringUtils.isNotEmpty(jwtToken)) {
            // 临时缓存一下该结果，用于应对微信回调快于获取扫码结果的异步请求，导致事件发布后没有被异步请求消费到的情况
            cachingTemplate.set(FormatUtil.format(WECHAT_SID_LOGIN_TOKEN, sid), jwtToken, Duration.ofMinutes(1));

            WechatSignInEvent event = new WechatSignInEvent(sid, jwtToken);
            applicationEventPublisher.publishEvent(event);
        }
    }

    private String buildState(String csrfToken, String sid) {
        return URLEncoder.encode(FormatUtil.format("csrf={}_{}", csrfToken, sid), StandardCharsets.UTF_8);
    }

    private Map<String, String> parseState(String state) {
        String[] pairs = state.split("=");
        if (ArrayUtils.isNotEmpty(pairs) && ArrayUtils.getLength(pairs) == 2) {
            String pair = pairs[1];
            String[] values = pair.split("_");

            return Map.of("csrf", values[0], "sid", values[1]);
        }
        return Map.of();
    }

    public String getWechatLoginToken(String sid) {
        return cachingTemplate.getString(FormatUtil.format(WECHAT_SID_LOGIN_TOKEN, sid));
    }

    /**
     * 检查微信登录token是否正确
     */
    public WechatUser checkWechatLoginToken(String loginToken) {
        Validates.notBlank(loginToken, UserErrorCode.USER_NOT_LOGIN);
        String unionId = JwtUtils.resolveWechatUnionId(loginToken);
        WechatUser wechatUser = cachingTemplate.get(FormatUtil.format(WECHAT_UNION_ID_OPENID, unionId), WechatUser.class);
        Validates.notNull(wechatUser, UserErrorCode.USER_ILLEGAL_TOKEN);
        return wechatUser;
    }
}
