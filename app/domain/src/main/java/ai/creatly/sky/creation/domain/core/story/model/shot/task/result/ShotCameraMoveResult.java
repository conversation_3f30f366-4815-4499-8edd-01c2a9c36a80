/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.shot.task.result;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizResult;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ShotCameraMoveResult implements TaskBizResult {

    /**
     * 文件ID
     */
    private Long   fileId;
    /**
     * 文件地址（OSS协议）
     */
    private String fileUrl;
}
