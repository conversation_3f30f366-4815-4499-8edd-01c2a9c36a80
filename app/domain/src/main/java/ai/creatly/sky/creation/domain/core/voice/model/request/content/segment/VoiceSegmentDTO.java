/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.request.content.segment;

import ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment.VoiceSegmentType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 一段声音内容
 *
 * <AUTHOR>
 * @version VoiceSegmentDTO.java, v 0.1 2023-12-07 下午9:02 zhoudong
 */
@Data
public class VoiceSegmentDTO {

    /**
     * 声音内容片段类型（根据不同的类型，以下字段取对应的一个即可）
     */
    @NotNull
    private VoiceSegmentType type;
    /**
     * 纯文本
     */
    @Size(max = 7500, message = "输入文本过长，请缩减至750字以内")
    private String           text;
    /**
     * 局部韵律调节
     */
    @Valid
    private VoiceProsodyDTO  prosody;
    /**
     * 停顿
     */
    @Valid
    private VoiceBreakDTO    shortBreak;
    /**
     * 特效音
     */
    @Valid
    private VoiceSoundDTO    shortSound;
}
