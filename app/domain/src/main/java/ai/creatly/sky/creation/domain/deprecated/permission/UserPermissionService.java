/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.permission;

import com.jspeeder.core.data.problem.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 权限服务
 *
 * <AUTHOR>
 * @version : AclService.java, v 1.0 2023年07月21日 17时36分 syoka Exp$
 */
@Service
@RequiredArgsConstructor
public class UserPermissionService {

    private final UserPermissionRepository userPermissionRepository;

    /**
     * 检查用户权限
     *
     * @param uid
     * @param code
     * @return
     */
    public void checkUserPermission(Long uid, String code) {
        userPermissionRepository.checkUserHasPermission(uid, code);
    }

    /**
     * 权限申请
     */
    public void applyPermission(Long uid, String permissionCode, ApplyPermissionPayload payload) {
        boolean alreadyApply = userPermissionRepository.checkDuplicateApplyExists(uid, permissionCode);
        if (alreadyApply) {
            throw new BizException(PermissionErrorCode.DUPLICATE_PERMISSION_APPLY);
        }
        userPermissionRepository.applyUserPermission(uid, permissionCode, payload);
    }
}
