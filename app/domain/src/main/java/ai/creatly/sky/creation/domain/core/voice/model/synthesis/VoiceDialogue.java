/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.synthesis;

import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceLocale;
import ai.creatly.sky.creation.domain.core.voice.model.enums.*;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment.VoiceSegment;
import com.jspeeder.core.data.problem.exception.SysException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * 语音对话
 *
 * <AUTHOR>
 * @version VoiceDialogue.java, v 0.1 2023-12-11 上午10:59 zhoudong
 */
@Data
@Accessors(chain = true)
public class VoiceDialogue {

    /**
     * 使用的声音
     */
    @NotNull
    private Voice              voice;
    /**
     * 口音编号（通过声音列表API获取）(基础声音无需传，演员声音必须传)
     */
    @Size(max = 32, message = "该声音不支持该本地化口音")
    private String             localeCode;
    /**
     * 扮演角色（通过声音列表API获取），如果不传，则使用默认的
     * <p/>
     * 注意：如果传了，则必须和 emotion 一起使用，否则不起作用。
     */
    @Nullable
    @Size(max = 32, message = "该声音不支持扮演该角色")
    private String             roleCode;
    /**
     * 定制情绪（通过声音列表API获取）
     */
    @Nullable
    @Size(max = 32, message = "该声音不支持使用该情绪")
    private String            emotionCode;
    /**
     * 情绪表现程度（默认1）
     */
    @Nullable
    private EmotionDegreeEnum emotionDegree;
    /**
     * 本段对话的音高（如果作用于整段，则每一段内容都会被强制相等）
     */
    @Nullable
    private SpeechPitchType   pitch;
    /**
     * 本段对话的语速（如果作用于整段，则每一段内容都会被强制相等）
     */
    @Nullable
    private SpeechRateType     rate;
    /**
     * 本段对话的音量（如果作用于整段，则每一段内容都会被强制相等）
     */
    @Nullable
    private SpeechVolumeType   volume;
    /**
     * 声音内容段列表
     */
    @Valid
    @NotEmpty
    @Size(max = 50)
    private List<VoiceSegment> segments;

    @NotNull
    public VoiceLocale voiceLocale() {
        if (voice.getCategory() == VoiceCategory.basic) {
            return voice.getFirstLocale();
        }
        return voice.getLocales().stream()
                .filter(locale -> locale.getCode().equals(localeCode))
                .findFirst()
                .orElseThrow(() -> new SysException("该声音不支持该本地化口音"));
    }
}
