/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward;

import ai.creatly.sky.creation.domain.core.aitask.model.UpdatableAiTask;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @version TaskCancelAction.java, v 0.1 2024-06-29 上午1:32 zhoudong
 */
public interface TaskCancelAction extends TaskAction {

    default TaskCancelAction withReason(String reason) {
        return this.withReason(reason, null);
    }

    TaskCancelAction withReason(String reason, @Nullable UpdatableAiTask updatableAiTask);

    String getReason();
}
