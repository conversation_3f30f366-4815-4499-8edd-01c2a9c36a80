/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.content.repository;

import ai.creatly.sky.creation.domain.deprecated.content.model.Tag;

import java.util.List;

public interface ContentTagRepository {

    /**
     * 通过标签名
     *
     * @param tags tag标签名
     * @return
     */
    List<Tag> getTagByName(List<String> tags);

    /**
     * 获取有效的标签
     */
    List<Tag> getValidTags();
}
