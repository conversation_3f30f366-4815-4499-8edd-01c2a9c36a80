/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.approval.admin.mapper;

import ai.creatly.sky.creation.domain.support.approval.admin.ApprovalTaskVM;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalTask;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version ApprovalAdminMapper.java, 2024-10-29 下午2:30 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface ApprovalAdminMapper {

    ApprovalTaskVM toApprovalTaskVM(ApprovalTask task);
}
