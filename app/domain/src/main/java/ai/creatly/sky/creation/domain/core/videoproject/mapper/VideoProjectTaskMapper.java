/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.mapper;

import ai.creatly.sky.creation.domain.common.integration.videorender.model.VideoRenderRequest.VideoRenderData.Subtitle;
import ai.creatly.sky.creation.domain.core.videoproject.model.*;
import ai.creatly.sky.creation.domain.core.videoproject.model.enums.VideoTrackType;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.input.*;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.time.Times;
import org.jetbrains.annotations.Nullable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version VideoProjectTaskMapper.java, 2024-10-23 下午5:31 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface VideoProjectTaskMapper {

    DramaAdsComposeTaskInput toDramaAdsComposeTaskInput(DramaAdsBatchComposeTaskInput batchComposeTaskInput, Integer composeIdx);

    DramaAdsRenderTaskInput toDramaAdsRenderTaskInput(DramaAdsComposeTaskInput composeTaskInput, long composeTaskId);

    @Mapping(target = "composeTaskId", ignore = true)
    @Mapping(target = "composeIdx", ignore = true)
    DramaAdsRenderTaskInput toDramaAdsRenderTaskInput(DramaAdsBatchRenderTaskInput batchRenderTaskInput);

    VideoEditorRenderTaskInput toVideoEditorRenderTaskInput(VideoComposition videoComposition);

    default List<VideoTrack> toVideoTracks(List<VideoShot> shots) {
        return this.toVideoTracks(shots, false);
    }

    default List<VideoTrack> toVideoTracks(List<VideoShot> shots, boolean dramaWatermark) {
        List<VideoTrack> videoTracks = new ArrayList<>();
        for (int i = 0; i < shots.size(); i++) {
            VideoShot videoShot = shots.get(i);

            final VideoTrackType trackType;
            final boolean mute;
            final boolean watermark;
            switch (videoShot.getMediaType()) {
                case sys_asset, user_asset -> {
                    trackType = VideoTrackType.asset_video;
                    mute = true;
                    watermark = false;
                }
                case drama_video -> {
                    trackType = VideoTrackType.drama_video;
                    mute = false;
                    watermark = dramaWatermark;
                }
                case avatar -> {
                    trackType = VideoTrackType.avatar_video;
                    mute = true;
                    watermark = false;
                }
                default -> throw new NoSuchElementException("not such media type: " + videoShot.getMediaType());
            }

            VideoTrack videoTrack = new VideoTrack()
                    .setType(trackType)
                    .setShotIndex(i)
                    .setMute(mute)
                    .setWatermark(watermark)
                    .setStart(videoShot.getStart())
                    .setEnd(videoShot.getEnd())
                    .setFileId(videoShot.getVideoFileId())
                    .setUrl(videoShot.getVideoUrl());
            videoTracks.add(videoTrack);
        }

        return videoTracks;
    }

    @Nullable
    default Subtitle toRenderSubtitle(@Nullable VideoSubtitleLayer subtitleLayer, List<VideoSubtitle> subtitles) {
        if (subtitleLayer == null) {
            return null;
        }

        var font = new Subtitle.Style.Font()
                .setFamily(subtitleLayer.getFontFamily())
                .setSize(subtitleLayer.getFontSize());
        var position = new Subtitle.Style.Position()
                .setTop(subtitleLayer.getTop())
                .setAlign(subtitleLayer.getAlign());
        var stroke = new Subtitle.Style.Stroke()
                .setColor(subtitleLayer.getStrokeColor())
                .setWidth(subtitleLayer.getStrokeWidth());
        var style = new Subtitle.Style()
                .setFont(font)
                .setPosition(position)
                .setStroke(stroke)
                .setColor(subtitleLayer.getColor())
                .setEffect(Objects.isNull(subtitleLayer.getEffect()) ? "none" : subtitleLayer.getEffect());

        Asserts.notEmpty(subtitles, "will not happen");
        var content = subtitles.stream()
                .map(subtitle -> new Subtitle.SubtitleContent()
                        .setText(subtitle.getText())
                        .setStartTime(Times.toSeconds(subtitle.getStart()))
                        .setEndTime(Times.toSeconds(subtitle.getEnd()))
                )
                .collect(toList());

        return new Subtitle()
                .setStyle(style)
                .setContent(content);
    }
}
