/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.invitation.service;

import ai.creatly.sky.creation.domain.core.invitation.model.UserInvitation;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;

/**
 * <AUTHOR>
 * @version UserInvitationRepository.java, v 0.1 2024-09-06 下午5:12 zhoudong
 */
public interface UserInvitationRepository {

    long create(UserInvitation userInvitation);

    Page<UserInvitation> queryPage(long inviterId, Pageable pageable);
}
