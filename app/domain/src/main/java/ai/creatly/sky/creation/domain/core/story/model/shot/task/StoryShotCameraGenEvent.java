/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.shot.task;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version StoryShotCameraGenEvent.java, v 0.1 2024-04-09 22:46 heb
 */
@Data
@Accessors(chain = true)
public class StoryShotCameraGenEvent {

    private Long   taskId;
    private Long   storyId;
    private Long   shotId;
    private String movement;
}
