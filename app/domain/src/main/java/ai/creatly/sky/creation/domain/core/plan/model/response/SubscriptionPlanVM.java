/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.response;

import ai.creatly.sky.creation.domain.core.member.model.MemberType;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanBuyMethod;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanPeriodType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * 订阅计划
 *
 * <AUTHOR>
 * @version SubscriptionPlanVM.java, v 0.1 2023-10-08 下午9:41 zhoudong
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SubscriptionPlanVM extends BasePlanVM {

    /**
     * 购买方式
     */
    private PlanBuyMethod               buyMethod;
    /**
     * 原价（元）
     */
    private String                      originalFee;
    /**
     * 实价（元）
     */
    private String                      realFee;
    /**
     * 订阅周期类型
     */
    private PlanPeriodType              periodType;
    /**
     * 到期是否自动续费（为空则可以让用户自行勾选）
     */
    @Nullable
    private Boolean                     autoRenewed;
    /**
     * 会员类型
     */
    private MemberType                  memberType;
    /**
     * 订阅周期内获得的余额
     */
    private Integer                     periodCredits;
    /**
     * 订阅获得的能力列表
     */
    private List<SubscriptionAbilityVM> abilities;
}
