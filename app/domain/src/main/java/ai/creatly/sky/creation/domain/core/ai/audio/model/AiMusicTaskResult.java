/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.audio.model;

import ai.creatly.sky.creation.domain.core.ai.model.OrderedAsset;
import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizResult;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskBizExecStatus;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version AiVideoTaskResult.java, v0.1 2025-03-03 04:51
 */
@Data
@Accessors(chain = true)
public class AiMusicTaskResult implements TaskBizResult {

    private List<OrderedAsset> assets;

    private Object data;

    private String taskId;

    private UserFile userFile;

    private AiTaskBizExecStatus bizExecStatus;

    private String errorMsg;

}
