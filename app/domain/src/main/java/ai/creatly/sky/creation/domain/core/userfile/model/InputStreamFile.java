/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.io.InputStreamSource;

import java.io.IOException;
import java.io.InputStream;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version InputStreamFile.java, v 0.1 2024-05-27 下午5:46 zhoudong
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InputStreamFile extends UserFile implements InputStreamSource {

    private Supplier<InputStream> stream;

    @Override
    public @NotNull InputStream getInputStream() throws IOException {
        return stream.get();
    }
}
