/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.image.model;

import com.jspeeder.core.model.BizStatus;
import com.jspeeder.core.model.OperatorRef;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;
import org.json.JSONObject;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 用户上传的商品图
 *
 * <AUTHOR>
 * @version ProductImage.java, v 0.1 2023-11-15 18:31 syoka
 */
@Data
@Accessors(chain = true)
public class ProductImage {

    /**
     * 商品图ID
     */
    private Long          id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    /**
     * 用户ID
     */
    private Long          uid;
    /**
     * 商品图文件ID（原图）
     */
    private Long          fileId;
    /**
     * 商品图URL（OSS协议格式）
     */
    private String        fileUrl;
    /**
     * 无背景商品图文件ID（该图片的大小会适配到物品形状）
     */
    private Long          noBgFileId;
    /**
     * 无背景商品图URL（OSS协议格式）
     */
    private String        noBgUrl;
    /**
     * 无背景商品图位置（左上角顶点的x和y距离）
     */
    @Nullable
    private List<String>  noBgPosition;
    /**
     * 无背景商品图缩放比例（宽高基于原商品图的缩放比例）
     */
    @Nullable
    private List<String>  noBgZoomRatio;
    /**
     * 透明背景图文件ID（该图片的大小和原图大小保持一致，透明背景，可以反映出物品的实际位置和大小）
     */
    @Nullable
    private Long          maskFileId;
    /**
     * 透明背景图URL（OSS协议格式）
     */
    @Nullable
    private String        maskUrl;
    /**
     * 商品图状态
     */
    private BizStatus     status;
    /**
     * 扩展信息
     */
    @Nullable
    private JSONObject    extInfo;
    /**
     * 创建者
     */
    private OperatorRef   creator;

    public String getNoBgRotation() {
        if (this.extInfo != null) {
            return this.extInfo.optString("noBgRotation");
        }
        return null;
    }

    public ProductImage setNoBgRotation(String noBgRotation) {
        if (this.extInfo == null) {
            this.extInfo = new JSONObject();
        }
        if (noBgRotation == null) {
            this.extInfo.remove("noBgRotation");
        } else {
            this.extInfo.put("noBgRotation", noBgRotation);
        }
        return this;
    }
}
