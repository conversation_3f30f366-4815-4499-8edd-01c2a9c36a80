/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.synthesizer.response;

import lombok.Data;

/**
 * 合成构建视频的响应参数
 *
 * <AUTHOR>
 * @version SynthesisVideoResponse.java, v 0.1 2023-10-07 10:45 syoka
 */
@Data
public class SynthesisVideoBaseResponse<E> {

    /**
     * 是否成功
     */
    private final boolean success;
    /**
     * 错误码
     */
    private final String errorCode;
    /**
     * 错误信息
     */
    private final String errorMsg;
    /**
     * 实际对象
     */
    private final E data;
}
