/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.image.mapper;

import ai.creatly.sky.creation.domain.core.aiproduct.image.model.request.ProductAiImageImagineRequest;
import ai.creatly.sky.creation.domain.core.aiproduct.image.model.response.ProductAiImageVM;
import ai.creatly.sky.creation.domain.core.aiproduct.image.model.task.ProductImageTaskBizType;
import ai.creatly.sky.creation.domain.core.aiproduct.image.model.task.param.ProductAiImageImagineParam;
import ai.creatly.sky.creation.domain.core.aiproduct.image.model.task.result.ProductAiImage;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.model.response.BaseUserAiTaskVM;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.util.Validates;
import lombok.Setter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version ProductAiImageMapper.java, v 0.1 2024-02-28 下午7:25 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public abstract class ProductAiImageMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    public AiTask buildAiTask(ProductAiImageImagineRequest request, UserContext userContext) {
        ProductAiImageImagineParam param = new ProductAiImageImagineParam()
                .setProductImageId(Validates.requireLong(request.getProductImageId(), "productImageId"))
                .setProductSceneId(Validates.requireLong(request.getProductSceneId(), "productSceneId"))
                .setOutputImageRatio(request.getAiImageRatio().getFraction());
        return AiTask.buildNew()
                .owner(userContext)
                .taskType(AiTaskType.AI_PRODUCT_IMAGE_V2)
                .bizType(ProductImageTaskBizType.imagine.getCode())
                .bizNo(request.getProductSceneId())
                .subBizNo(IdHelper.getStrId())
                .bizParams(param)
                .build();
    }

    public ProductAiImageVM toProductAiImageVM(AiTask aiTask) {
        ProductAiImageVM productAiImageVM = new ProductAiImageVM();
        this.fillBaseUserAiTaskVM(aiTask, productAiImageVM);

        ProductAiImage productAiImage = aiTask.parseBizResult(ProductAiImage.class);
        List<String> imageUrls = Optional.ofNullable(productAiImage.getImageUrls())
                .stream()
                .flatMap(Collection::stream)
                .map(url -> userFileHelper.getHttpUrl(url, aiTask.getOwnerId()))
                .collect(toList());
        return productAiImageVM
                .setSceneName(productAiImage.getSceneName())
                .setImageUrls(imageUrls);
    }

    @Mapping(target = "finishedDurationSeconds", source = "sysResult.finishedDuration.seconds")
    @Mapping(target = "estimatedDurationSeconds", source = "estimatedDuration.seconds")
    abstract void fillBaseUserAiTaskVM(AiTask task, @MappingTarget BaseUserAiTaskVM baseUserAiTaskVM);
}
