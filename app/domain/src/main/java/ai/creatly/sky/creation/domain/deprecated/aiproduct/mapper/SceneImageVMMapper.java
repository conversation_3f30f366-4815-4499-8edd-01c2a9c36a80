/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.deprecated.aiproduct.mapper;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.common.integration.oss.FileProcessOption;
import ai.creatly.sky.creation.domain.common.util.ProgressUtils;
import ai.creatly.sky.creation.domain.core.aiimage_old.model.AiImageMetadata;
import ai.creatly.sky.creation.domain.core.aiimage_old.model.response.AttachmentVM;
import ai.creatly.sky.creation.domain.core.aiimage_old.model.task.result.AIImageResult;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskConstant;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.deprecated.aiproduct.model.ProductSceneImageVM;
import ai.creatly.sky.creation.domain.deprecated.aiproduct.task.AIProductSceneImageResult;
import ai.creatly.sky.creation.domain.deprecated.aiproduct.task.AiProductSceneGeneratedParam;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version SceneImageVMMapper.java, v 0.1 2023-11-16 15:15 syoka
 */
@Mapper(config = BaseMapperConfig.class)
@Slf4j
public abstract class SceneImageVMMapper {

    /**
     * 缩小为原来的20%
     */
    private static final int DEFAULT_RATIO = 20;

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper  userFileHelper;

    @AfterMapping
    public void afterSceneImageMapping(AiTask aiTask, @MappingTarget ProductSceneImageVM imageVM) {
        AiProductSceneGeneratedParam param = aiTask.parseBizParams(AiProductSceneGeneratedParam.class);
        AIProductSceneImageResult result = aiTask.parseBizResult(AIProductSceneImageResult.class);

        imageVM.setSceneId(result.getSceneImageId());
        imageVM.setProgress(ProgressUtils.getProgress(result.getProgress()));
        imageVM.setPrompt(param.getPrompt());
        imageVM.setImageFileId(result.getImageFileId());

        if (StringUtils.isEmpty(result.getErrorMsg())) {
            imageVM.setErrorMsg(getErrorMsgIfExists(aiTask, result));
            imageVM.setBannedWord(getBannedWord(aiTask));
        }
        // 图片裁剪
        imageVM.setAttachments(getAttachment(result.getFileMetadata(), result.getOssUri()));
    }

    private String getErrorMsgIfExists(AiTask aiTask, AIImageResult aiImageResult) {
        if (StringUtils.isNotEmpty(aiImageResult.getErrorMsg())) {
            return aiImageResult.getErrorMsg();
        }
        JSONObject bizExecInfo = aiTask.getBizExecInfo();
        if (Objects.nonNull(bizExecInfo)) {
            if (bizExecInfo.has("bizErrorMsg")) {
                return bizExecInfo.getString("bizErrorMsg");
            }
        }
        return null;

    }

    private List<AttachmentVM> getAttachment(AiImageMetadata fileMetadata, String ossUrl) {
        if (Objects.isNull(fileMetadata) || StringUtils.isEmpty(ossUrl)) {
            return Collections.emptyList();
        }

        int width = Objects.requireNonNull(fileMetadata.getWidth());
        int height = Objects.requireNonNull(fileMetadata.getHeight());

        int halfWidth = width >> 1;
        int halfHeight = height >> 1;


        // 图片1 左上
        String imagePos1 = "w_" + halfWidth + ",h_" + halfHeight + ",x_0,y_0";
        String image1 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE, FileProcessOption.of(getCropStyle(imagePos1)));
        String imageThumbnail1 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE,
                FileProcessOption.of(getStyles(Arrays.asList(getCropStyle(imagePos1), getThumbnail()))));

        // 图片2 右上
        String imagePos2 = "w_" + halfWidth + ",h_" + halfHeight + ",x_" + halfWidth + ",y_0";
        String image2 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE, FileProcessOption.of(getCropStyle(imagePos2)));
        String imageThumbnail2 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE,
                FileProcessOption.of(getStyles(Arrays.asList(getCropStyle(imagePos2), getThumbnail()))));

        // 图片3 左下
        String imagePos3 = "w_" + halfWidth + ",h_" + halfHeight + ",x_0,y_" + halfHeight;
        String image3 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE, FileProcessOption.of(getCropStyle(imagePos3)));
        String imageThumbnail3 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE,
                FileProcessOption.of(getStyles(Arrays.asList(getCropStyle(imagePos3), getThumbnail()))));

        // 图片4 右下
        String imagePos4 = "w_" + halfWidth + ",h_" + halfHeight + ",x_" + halfWidth + ",y_" + halfHeight;
        String image4 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE, FileProcessOption.of(getCropStyle(imagePos4)));
        String imageThumbnail4 = userFileHelper.getHttpUrl(ossUrl, FileAcl.PRIVATE,
                FileProcessOption.of(getStyles(Arrays.asList(getCropStyle(imagePos4), getThumbnail()))));

        return Arrays.asList(new AttachmentVM(1, image1, imageThumbnail1),
                new AttachmentVM(2, image2, imageThumbnail2),
                new AttachmentVM(3, image3, imageThumbnail3),
                new AttachmentVM(4, image4, imageThumbnail4));
    }


    private String getStyles(List<String> styles) {
        return String.join(",", styles);
    }

    private String getCropStyle(String position) {
        return String.join(",", "image/crop", position);
    }

    private String getThumbnail() {
        return String.join(",", "image/resize", "p_" + SceneImageVMMapper.DEFAULT_RATIO);
    }

    private String getBannedWord(AiTask aiTask) {
        JSONObject bizExecInfo = aiTask.getBizExecInfo();
        if (Objects.nonNull(bizExecInfo) && bizExecInfo.has(AiTaskConstant.BANNED_WORD)) {
            return bizExecInfo.getString(AiTaskConstant.BANNED_WORD);
        }
        return null;
    }

}
