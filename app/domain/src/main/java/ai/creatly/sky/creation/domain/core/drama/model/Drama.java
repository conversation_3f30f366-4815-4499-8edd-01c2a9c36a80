/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.model;

import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaLocalSyncStatus;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaStatus;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaType;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaVideoType;
import ai.creatly.sky.creation.domain.support.category.model.CategoryPath;
import com.jspeeder.core.util.time.Times;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version Drama.java, v 0.1 2024-10-18 下午8:30 zhoudong
 */
@Data
@Accessors(chain = true)
public class Drama {

    private Long                 id;
    private ZonedDateTime        createdAt;
    private ZonedDateTime        updatedAt;
    /**
     * 用户ID
     */
    private Long                 uid;
    /**
     * 剧情名称
     */
    private String               name;
    /**
     * 剧情类型
     */
    private DramaType            type;
    /**
     * 剧情状态
     */
    private DramaStatus          status;
    /**
     * 适用的产品类目
     */
    private List<CategoryPath>   productCategories;
    /**
     * 适用的客群类目
     */
    private List<CategoryPath>   crowdCategories;
    /**
     * 剧情相关视频
     */
    private List<DramaVideo>     videos;
    /**
     * 剧情总时长
     */
    private Duration             duration;
    /**
     * 剧情内容
     */
    private DramaContent         content;
    /**
     * 剧情角色列表
     */
    private List<DramaRole>      roles;
    /**
     * 剧情封面图地址（OSS协议）
     */
    @Nullable
    private String               coverUrl;
    /**
     * 剧情标注
     */
    private DramaAnnotation      annotation;
    /**
     * 剧情统计信息
     */
    private DramaStats           stats;
    /**
     * 剧情审核明细
     */
    private DramaReview          review;
    /**
     * 本地剧情库同步状态
     */
    private DramaLocalSyncStatus localSyncStatus;

    public List<DramaVideo> findVideos(DramaVideoType type) {
        return this.videos.stream().filter(video -> video.getType() == type).toList();
    }

    public Optional<DramaRole> findSpokesperson() {
        return this.roles.stream()
                .filter(DramaRole::getIsSpokesperson)
                .findFirst();
    }

    public String getDurationSeconds() {
        return Times.toFormattedSeconds(this.duration, 3);
    }
}
