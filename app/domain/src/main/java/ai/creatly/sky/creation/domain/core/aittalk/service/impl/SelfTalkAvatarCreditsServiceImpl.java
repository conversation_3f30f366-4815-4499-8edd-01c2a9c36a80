/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aittalk.service.impl;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aittalk.model.task.AiTalkTaskBizType;
import ai.creatly.sky.creation.domain.core.aittalk.model.task.param.AiTalkVideoParam;
import ai.creatly.sky.creation.domain.core.aittalk.model.task.param.CreateTalkScriptType;
import ai.creatly.sky.creation.domain.core.aittalk.service.SelfTalkAvatarCreditsService;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsRefund;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsCalculator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version SelfTalkAvatarCreditsServiceImpl.java, v 0.1 2024-01-16 下午5:43 zhoudong
 */
@Service
@RequiredArgsConstructor
public class SelfTalkAvatarCreditsServiceImpl implements SelfTalkAvatarCreditsService {

    private final CreditsCalculator creditsCalculator;

    @Override
    public CreditsExpense calcExpense(AiTask aiTask, Duration audioDuration) {
        String ruleType = AiTaskType.CREATE_AI_TALK_VIDEO.name();
        String ruleBizType = AiTalkTaskBizType.SELF_TALK.name();
        int credits = creditsCalculator.calcCreditsByDuration(ruleType, ruleBizType, audioDuration);
        var scriptType = aiTask.parseBizParams(AiTalkVideoParam.class).getScriptType();
        return CreditsExpense.builder()
                .uid(aiTask.getOwnerId())
                .type(CreditAccountType.GENERAL)
                .amount(credits)
                // 完成一条创作任务，扣除一次费用
                .bizType(CreditLogBizType.LIVE_PIC)
                .bizNo(aiTask.getId().toString())
                // 当输入为文本时，要先合成语音获得音频时长才能计算费用，所以只能后置扣费，允许最多1次扣到负数
                .allowNegative(scriptType == CreateTalkScriptType.text)
                .build();
    }

    @Override
    public CreditsRefund buildRefund(AiTask aiTask) {
        var scriptType = aiTask.parseBizParams(AiTalkVideoParam.class).getScriptType();
        return CreditsRefund.builder()
                .uid(aiTask.getOwnerId())
                .bizType(CreditLogBizType.LIVE_PIC)
                .bizNo(aiTask.getId().toString())
                // 当输入为文本时，由于是后置扣费，可能不存在扣费记录，所以允许不存在扣费记录
                .allowExpenseAbsent(scriptType == CreateTalkScriptType.text)
                .build();
    }
}
