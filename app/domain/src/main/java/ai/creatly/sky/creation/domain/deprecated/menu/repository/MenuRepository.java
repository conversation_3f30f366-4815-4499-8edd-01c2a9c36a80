/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.deprecated.menu.repository;

import ai.creatly.sky.creation.domain.deprecated.menu.model.Menu;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version MenuRepository.java, v 0.1 2023-06-13 23:16 joton
 */
public interface MenuRepository {

    Optional<Menu> queryValidByCode(String code);
}
