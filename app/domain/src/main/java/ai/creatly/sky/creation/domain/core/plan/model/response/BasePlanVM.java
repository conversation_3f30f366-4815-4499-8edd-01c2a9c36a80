/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.response;

import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanSource;
import lombok.Data;

/**
 * 付费计划
 *
 * <AUTHOR>
 * @version BasePlanVM.java, v 0.1 2023-10-08 下午9:28 zhoudong
 */
@Data
public abstract class BasePlanVM {

    /**
     * 计划ID
     */
    private String     id;
    /**
     * 计划来源
     */
    private PlanSource source;
    /**
     * 计划名称
     */
    private String     name;
    /**
     * 计划描述
     */
    private String     description;
    /**
     * 计划阶梯档位（可用于排序，有序展示）
     */
    private Integer    level;
    /**
     * 权益有效期
     */
    private Integer    benefitValidDays;
}
