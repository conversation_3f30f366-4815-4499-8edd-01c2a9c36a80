/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.label.model;

import ai.creatly.sky.creation.domain.support.label.model.enums.LabelSource;
import com.jspeeder.core.model.BizStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 标签元数据（uid + source + code 唯一）
 *
 * <AUTHOR>
 * @version Label.java, v0.1 2023-03-23 22:07 joton
 */
@Data
@Accessors(chain = true)
public class Label {

    /**
     * 主键ID
     */
    private Long          id;
    /**
     * 更新时间
     */
    private ZonedDateTime updatedAt;
    /**
     * 创建时间
     */
    private ZonedDateTime createdAt;
    /**
     * 用户ID（1表示平台）
     */
    private Long          uid;
    /**
     * 标签来源（标签打在哪种实体上，必须提前定义好，这样才能反向去查找该标签具体打在哪里了）
     */
    private LabelSource   source;
    /**
     * 编码（默认和name一致）
     */
    private String        code;
    /**
     * 名称
     */
    private String        name;
    /**
     * 状态
     */
    private BizStatus     status;
    /**
     * 使用次数
     */
    private Integer       usedCount;
}
