/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.video.model.response;

import ai.creatly.sky.creation.domain.core.aitask.model.response.BaseTaskVM;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jetbrains.annotations.Nullable;

/**
 * 商品视频重配音任务
 *
 * <AUTHOR>
 * @version ProductVideoReTalkTaskVM.java, v 0.1 2024-05-25 上午11:59 zhoudong
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductVideoReTalkTaskVM extends BaseTaskVM {

    /**
     * 视频标题
     */
    private String  title;
    /**
     * 视频描述
     */
    private String  description;
    /**
     * 已生成的商品视频ID
     */
    @Nullable
    private String  productVideoId;
    /**
     * 封面图地址（HTTP地址）
     */
    @Nullable
    private String  coverUrl;
    /**
     * 视频地址（HTTP地址）
     */
    @Nullable
    private String  videoUrl;
    /**
     * 视频时长
     */
    @Nullable
    private Integer videoMills;
    /**
     * 视频格式（小写）
     */
    @Nullable
    private String  videoFormat;
    /**
     * 视频分辨率
     */
    @Nullable
    private String  videoResolution;
    /**
     * 音频地址（HTTP地址）
     */
    @Nullable
    private String  audioUrl;
}
