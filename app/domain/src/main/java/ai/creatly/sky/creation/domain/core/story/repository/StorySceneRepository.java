/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.repository;

import ai.creatly.sky.creation.domain.core.story.model.scene.StoryScene;

import java.util.List;

/**
 * <AUTHOR>
 * @version StorySceneRepository.java, v 0.1 2024-03-01 21:06 syoka
 */
public interface StorySceneRepository {

    /**
     * 批量创建故事场景
     *
     * @param storyScenes 故事场景
     */
    void batchCreate(List<StoryScene> storyScenes);

    /**
     * 查询故事场景列表
     */
    List<StoryScene> findStoryScenesByStoryId(long storyId);

}
