/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.model.request;

import ai.creatly.sky.creation.domain.core.ai.image.model.enums.ImageExpandDirection;
import ai.creatly.sky.creation.domain.core.ai.model.CommonGenerateRequest;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

/**
 *
 * <AUTHOR>
 * @version ImageExpandRequest.java, v0.1 2025-02-20 21:36
 */
@Data
public class ImageExpandRequest extends CommonGenerateRequest {

    @Digits(integer = 20, fraction = 0)
    private String               fileId;
    @NotNull
    private ImageExpandDirection direction;
    @Nullable
    @Size(max = 100)
    private String               promptText;
}
