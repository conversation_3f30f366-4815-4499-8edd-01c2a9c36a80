/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.admin.model;

import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaVideoType;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 *
 * <AUTHOR>
 * @version DramaVideoVM.java, 2024-12-12 下午4:29 zhoudong
 */
@Data
@Accessors(chain = true)
public class DramaVideoVM {
    /**
     * 剧情视频类型
     */
    private DramaVideoType type;
    /**
     * 视频文件ID
     */
    private String         fileId;
    /**
     * 视频文件地址（HTTP协议）
     */
    private String         url;
    /**
     * 视频宽度
     */
    private Integer        width;
    /**
     * 视频高度
     */
    private Integer        height;
    /**
     * 该视频的最后一段文案
     */
    @Nullable
    private String         endText;
}
