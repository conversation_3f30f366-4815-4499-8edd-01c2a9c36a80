/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.mapper;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.ai.image.model.ImageExpansion;
import ai.creatly.sky.creation.domain.core.ai.image.model.request.ImageExpandRequest;
import ai.creatly.sky.creation.domain.core.ai.image.model.response.ImageExpansionVM;
import ai.creatly.sky.creation.domain.core.ai.image.task.expand.model.ImageExpandTaskInput;
import ai.creatly.sky.creation.domain.core.ai.image.task.expand.model.ImageExpandTaskResult;
import ai.creatly.sky.creation.domain.core.ai.image.task.generate.model.ImageGenerateTaskInput;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.asset.model.Asset;
import ai.creatly.sky.creation.domain.core.asset.model.AssetFile;
import ai.creatly.sky.creation.domain.core.asset.model.AssetMetadata;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetBizType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetSourceType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetStatus;
import ai.creatly.sky.creation.domain.core.userasset.model.AssetContent;
import ai.creatly.sky.creation.domain.core.userfile.model.FileRef;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import lombok.Setter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version ImageExpansionMapper.java, v0.1 2025-02-19 21:38
 */
@Mapper(config = BaseMapperConfig.class, uses = ImageAssetMapper.class)
public abstract class ImageExpansionMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    public abstract ImageExpandTaskInput toTaskInput(ImageExpandRequest request, FileRef imageFile);

    public Asset buildAsset(ImageExpandTaskInput taskInput, UserFile imageFile, String coverUrl) {
        var metadata = new AssetMetadata().setAttachments(List.of(taskInput.getImageFile()));
        return new Asset()
                .setId(IdHelper.getId())
                .setUid(imageFile.getUid())
                .setStatus(AssetStatus.VALID)
                .setName(imageFile.getOriginalFilename())
                .setSourceType(AssetSourceType.generation)
                .setBizType(AssetBizType.image_expansion)
                .setCoverUrl(coverUrl)
                .setFile(AssetFile.fromUserFile(imageFile))
                .setContent(new AssetContent().setMd5(imageFile.getMd5()))
                .setMetadata(metadata)
                .setTags(new ArrayList<>());
    }

    public ImageExpansion toImageExpansion(AiTask aiTask) {
        var taskInput = aiTask.parseBizInput(ImageExpandTaskInput.class);
        var taskResult = aiTask.parseBizResult(ImageExpandTaskResult.class);
        return this.toImageExpansion(aiTask, taskInput, taskResult);
    }

    @Mapping(target = "taskId", source = "aiTask.id")
    protected abstract ImageExpansion toImageExpansion(AiTask aiTask, ImageExpandTaskInput taskInput, ImageExpandTaskResult taskResult);

    public ImageExpansionVM toImageExpansionVM(ImageExpansion imageExpansion) {
        String imageUrl = userFileHelper.getHttpUrl(imageExpansion.getImageFile().getUrl(), FileAcl.PRIVATE);
        return this.toImageExpansionVM(imageExpansion, imageUrl);
    }

    @Mapping(target = "imageUrl", source = "imageUrl")
    public abstract ImageExpansionVM toImageExpansionVM(ImageExpansion imageExpansion, String imageUrl);
}
