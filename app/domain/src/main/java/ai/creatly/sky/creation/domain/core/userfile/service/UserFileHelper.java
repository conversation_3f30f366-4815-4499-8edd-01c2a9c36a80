/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.service;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.common.integration.oss.FileProcessOption;
import ai.creatly.sky.creation.domain.common.integration.oss.OssUtil;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.error.FileErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.model.FileInput;
import ai.creatly.sky.creation.domain.core.userfile.model.HttpFile;
import ai.creatly.sky.creation.domain.core.userfile.model.OssFile;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileScope;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileStorageType;
import ai.creatly.sky.creation.domain.core.userfile.util.UserFileUtil;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.file.HttpFileUtil;
import com.jspeeder.core.util.text.FormatUtil;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.PolyNull;
import org.jetbrains.annotations.Nullable;

import java.io.IOException;
import java.util.Base64;

/**
 * 用户文件辅助工具（以下方法皆为纯内存计算，不涉及网络IO）
 * <p/>
 * 这一层命名里涉及的OSS泛指一切文件对象存储服务（不仅仅局限于阿里云OSS），所以要保证唯一性必须有3要素：提供商，存储空间，文件key
 *
 * <AUTHOR>
 * @version UserFileHelper.java, v 0.1 2023-11-06 下午4:05 zhoudong
 */
public interface UserFileHelper {

    /**
     * 获取当前环境的存储空间
     *
     * @return 存储空间（区分环境）
     */
    String getCurrentBucket();

    /*-----------------------------------------构建用户文件记录-----------------------------------------*/

    /**
     * 构建一条系统用户文件记录
     *
     * @param fileId    文件ID
     * @param fileInput 文件输入参数
     * @return 用户文件记录（未持久化）
     */
    default UserFile buildSysFile(long fileId, FileInput fileInput) {
        return this.buildUserFile(UserContext.buildSystem(), fileId, fileInput);
    }

    /**
     * 构建一条用户文件记录
     *
     * @param userContext 用户上下文
     * @param fileId      文件ID
     * @param fileInput   文件输入参数
     * @return 文件记录（未持久化）
     */
    UserFile buildUserFile(UserContext userContext, long fileId, FileInput fileInput);

    /**
     * 构建文件存储路径（包含文件名和文件后缀）
     *
     * @param userContext 用户ID
     * @param fileId      文件ID
     * @param fileInput   文件输入参数
     * @return 文件存储路径
     */
    static String buildFileKey(UserContext userContext, long fileId, FileInput fileInput) {
        if (StringUtils.isNotBlank(fileInput.getFileKey())) {
            return fileInput.getFileKey();
        }
        final FileBizSource bizSource = fileInput.getBizSource();
        final String filepath = UserFileUtil.getRelativeFilepath(fileId, fileInput);
        return buildFileKey(userContext, bizSource, filepath);
    }

    /**
     * 构建文件存储路径（包含文件名和文件后缀）
     *
     * @param userContext  用户ID
     * @param fileBaseName 文件基础名称（不带后缀）
     * @param extension    文件后缀
     * @param bizSource    文件业务来源
     * @return 文件存储路径
     */
    static String buildFileKey(UserContext userContext, String fileBaseName, @Nullable String extension, FileBizSource bizSource) {
        final String filepath = UserFileUtil.getRelativeFilepath(bizSource, fileBaseName, extension);
        return buildFileKey(userContext, bizSource, filepath);
    }

    /**
     * 构建文件存储路径（包含文件名和文件后缀）
     *
     * @param userContext 用户ID
     * @param bizSource   文件业务来源
     * @param filepath    文件路径（包含文件名和文件后缀）TODO 重构为不带业务路径的子路径
     * @return 用户文件存储路径
     */
    static String buildFileKey(UserContext userContext, FileBizSource bizSource, String filepath) {
        final String fileKey;
        if (bizSource.getScope() == FileScope.user) {
            final long uid = userContext.getSessionUid();
            final String orgCode = userContext.hasActiveOrg() ? userContext.getUserOrganization().getOrgCode() : null;
            if (StringUtils.isEmpty(orgCode)) {
                fileKey = FormatUtil.format("{}/users/{}/{}", AppConstants.APP_NAME, uid, filepath);
            } else {
                fileKey = FormatUtil.format("{}/org/{}/users/{}/{}", AppConstants.APP_NAME, orgCode, uid, filepath);
            }
        } else if (bizSource.getScope() == FileScope.sys) {
            fileKey = FormatUtil.format("{}/system/{}", AppConstants.APP_NAME, filepath);
        } else if (bizSource.getScope() == FileScope.tmp) {
            fileKey = FormatUtil.format("{}/tmp/{}", AppConstants.APP_NAME, filepath);
        } else {
            fileKey = FormatUtil.format("{}/{}", AppConstants.APP_NAME, filepath);
        }
        return fileKey;
    }

    /**
     * 构建文件存储目录（ / 结尾，不含文件名）
     *
     * @param userContext 用户ID
     * @param bizSource   业务来源
     * @return 用户文件存储目录
     */
    static String buildFileDir(UserContext userContext, FileBizSource bizSource) {
        final String fileDir;
        final long uid = userContext.getSessionUid();
        final String orgCode = userContext.hasActiveOrg() ? userContext.getUserOrganization().getOrgCode() : null;
        final String dir = bizSource.getDir();
        if (bizSource.getScope() == FileScope.user) {
            if (StringUtils.isEmpty(orgCode)) {
                fileDir = FormatUtil.format("{}/users/{}/{}/", AppConstants.APP_NAME, uid, dir);
            } else {
                fileDir = FormatUtil.format("{}/org/{}/users/{}/{}/", AppConstants.APP_NAME, orgCode, uid, dir);
            }
        } else if (bizSource.getScope() == FileScope.sys) {
            fileDir = FormatUtil.format("{}/system/{}/", AppConstants.APP_NAME, dir);
        } else if (bizSource.getScope() == FileScope.tmp) {
            fileDir = FormatUtil.format("{}/tmp/{}/", AppConstants.APP_NAME, dir);
        } else {
            fileDir = FormatUtil.format("{}/{}/", AppConstants.APP_NAME, dir);
        }
        return fileDir;
    }

    /*-----------------------------------------获取HTTP协议地址-----------------------------------------*/

    /**
     * 获取文件的访问地址
     *
     * @param userFile 用户文件
     * @return 带访问地址的文件对象
     */
    HttpFile toHttpFile(UserFile userFile);

    /**
     * 获取文件的 HTTP 访问地址
     *
     * @param userFile 用户文件
     * @return 文件访问地址
     */
    String getHttpUrl(UserFile userFile);

    /**
     * 获取文件的 HTTP 访问地址
     *
     * @param ossUrl OSS文件地址（基于oss协议）
     * @param uid    文件所属的用户ID
     * @return 文件访问地址
     */
    String getHttpUrl(@Nullable String ossUrl, long uid);

    /**
     * 获取文件 HTTP 访问地址
     *
     * @param ossUrl  OSS文件地址（基于oss协议）
     * @param fileAcl 文件访问控制
     * @return 文件访问地址
     */
    default String getHttpUrl(@Nullable String ossUrl, FileAcl fileAcl) {
        return this.getHttpUrl(ossUrl, fileAcl, null);
    }

    /**
     * 获取文件 HTTP 访问地址
     *
     * @param ossUrl        OSS文件地址（基于oss协议）
     * @param fileAcl       文件访问控制
     * @param processOption 文件处理选项
     * @return 文件访问地址
     */
    @PolyNull
    String getHttpUrl(@Nullable String ossUrl, FileAcl fileAcl, @Nullable FileProcessOption processOption);

    default String getMediaCoverHttpUrl(FileType fileType, String ossUrl, FileAcl fileAcl) {
        FileProcessOption coverProcessOption = switch (fileType) {
            // 图片缩小到50%
            case IMAGE -> OssUtil.imageResize(50);
            // 视频截图，取第一秒
            case VIDEO -> OssUtil.videoSnapshot(1);
            default -> throw new SysException(FileErrorCode.FILE_TYPE_NOT_SUPPORT);
        };
        return this.getHttpUrl(ossUrl, fileAcl, coverProcessOption);
    }

    /*-----------------------------------------获取OSS协议地址-----------------------------------------*/

    /**
     * 转换为文件的 OSS 访问地址（建议上层业务优先考虑使用这个方法）
     *
     * @param userFile 用户文件
     * @return 文件OSS地址（带有OSS协议）
     */
    static String toOssUrl(UserFile userFile) {
        Asserts.isTrue(userFile.getStorageType() == FileStorageType.OSS, "文件服务暂时只支持阿里云OSS");
        return OssUtil.toOssUrl(userFile.getBucket(), userFile.getKey());
    }

    /**
     * 转base64
     *
     * @param url
     * @return
     * @throws IOException
     */
    static String toBase64(String url) {
        try {
            //对字节数组Base64编码
            return Base64.getEncoder().encodeToString(HttpFileUtil.downloadAsBytes(url));
        } catch (IOException ex) {
            throw new RuntimeException(ex);
        }
    }

    /*-----------------------------------------解析文件地址-----------------------------------------*/

    /**
     * 解析文件在存储空间下的唯一key
     *
     * @param ossUrl 文件OSS地址
     * @return 文件key
     */
    String resolveFileKey(String ossUrl);

    /**
     * 解析文件在存储空间下的bucket
     *
     * @param ossUrl 文件OSS地址
     * @return 文件key
     */
    String resolveFileBucket(String ossUrl);

    /**
     * 获取短连接
     *
     * @param fileId -
     * @return -
     */
    String getShortLink(long fileId);

    /**
     * 解析短连接中的ID部分
     *
     * @param shortLink [creatly://123967]
     * @param uid       -
     * @return ID部分
     */
    @Nullable
    Long parseShortLink(String shortLink, long uid);

    static OssFile toOssFile(UserFile userFile) {
        Asserts.isTrue(userFile.getStorageType() == FileStorageType.OSS, "文件服务暂时只支持阿里云OSS");
        return new OssFile(userFile.getBucket(), userFile.getKey());
    }

}
