/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.auth.model;

import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import ai.creatly.sky.creation.domain.core.user.model.organization.UserOrganization;
import ai.creatly.sky.creation.domain.core.user.model.organization.UserOrganizationStatus;
import ai.creatly.sky.creation.domain.support.credential.model.ClientContext;
import com.jspeeder.core.model.OperatorRef;
import com.jspeeder.core.model.OperatorType;
import lombok.*;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.util.Objects;

/**
 * 主站登录用户上下文
 *
 * <AUTHOR>
 * @version UserContext.java, v 0.1 2023-06-24 15:10 joton
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class UserContext {

    /**
     * 登录会话用户id
     */
    private Long             sessionUid;
    /**
     * 当前用户名字
     */
    private String           sessionUserName;
    /**
     * 当前用户手机号
     */
    private String           phone;
    /**
     * 当前用户邮箱
     */
    private String           email;
    /**
     * 当前用户从属组织
     */
    private UserOrganization userOrganization;
    /**
     * 客户端上下文
     */
    @Nullable
    private ClientContext    clientContext;

    /**
     * 实际操作者
     *
     * @return -
     */
    public OperatorRef toOperatorRef() {
        // WEB端
        if (clientContext == null) {
            return OperatorRef.of(OperatorType.USER, String.valueOf(sessionUid), sessionUserName);
        }
        // 客户端
        String appKey = String.valueOf(clientContext.getAppKey());
        String name = clientContext.getName();
        return OperatorRef.of(OperatorType.CLIENT, appKey, name);
    }

    public static UserContext buildFrom(UserInfo userInfo, OperatorRef operatorRef) {
        final UserContext userContext = new UserContext()
                .setSessionUid(Long.parseLong(userInfo.getId()))
                .setSessionUserName(userInfo.getUsername());

        // 设置用户从属组织
        UserOrganization organization = userInfo.getActiveOrganization();
        if (Objects.nonNull(organization)) {
            userContext.setUserOrganization(organization);
        }

        // 客户端
        if (operatorRef.getType() == OperatorType.CLIENT) {
            ClientContext clientContext = ClientContext.of(operatorRef.getId(), operatorRef.getName());
            userContext.setClientContext(clientContext);
        }
        return userContext;
    }

    public static UserContext buildSystem() {
        return new UserContext().setSessionUid(AppConstants.SYSTEM_UID).setSessionUserName(AppConstants.SYSTEM_USERNAME);
    }

    /**
     * 获取用户所属组织主账号，如果用户不属于任何组织则返回自身
     *
     * @return -
     */
    public long getouid() {
        // 如果用户有从属组织，则返回组织主账号id
//        if (Objects.nonNull(userOrganization) && UserOrganizationStatus.ACTIVE == userOrganization.getStatus()) {
//            // TODO: 2023-06-24 15:10 组织ID 和 组织的主账号ID 是不是搞混了？？？
//            return userOrganization.getOrgId();
//        }
        return this.sessionUid;
    }

    /**
     * 获取用户所属组织主账号，如果用户不属于任何组织则返回自身
     *
     * @return -
     */
    public long getUid() {
        return getouid();
    }

    public boolean hasActiveOrg() {
        return Objects.nonNull(userOrganization) && UserOrganizationStatus.ACTIVE == userOrganization.getStatus();
    }

    public String getCurrentUsername() {
        if (Objects.nonNull(userOrganization) && UserOrganizationStatus.ACTIVE == userOrganization.getStatus()) {
            // TODO: 2023-06-24 15:10 组织 和 组织的主账号 是不是搞混了？？？
            return userOrganization.getOrgName();
        }
        return this.sessionUserName;
    }
}
