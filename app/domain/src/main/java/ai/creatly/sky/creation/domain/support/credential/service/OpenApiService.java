/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.credential.service;

import ai.creatly.sky.creation.domain.support.credential.model.ClientCredential;
import ai.creatly.sky.creation.domain.support.credential.model.OpenApiToken;
import ai.creatly.sky.creation.domain.support.credential.model.RequestContent;
import org.jetbrains.annotations.Nullable;

/**
 * 开放接口服务
 *
 * <AUTHOR>
 * @version OpenApiService.java, v 0.1 2024-05-28 下午9:15 zhoudong
 */
public interface OpenApiService {

    /**
     * 客户端认证
     *
     * @param appKey    API Key ID
     * @param appSecret API Key Secret
     * @return 客户端凭证
     */
    @Nullable
    ClientCredential authenticate(String appKey, String appSecret);

    /**
     * 加签
     *
     * @param credential 客户端凭证
     * @param content    签名内容
     * @return 加签的令牌（返回null表示加签失败）
     */
    @Nullable
    String generateOpenApiToken(ClientCredential credential, RequestContent content);

    /**
     * 验签
     *
     * @param token   待验签的令牌
     * @param content 请求内容
     * @return 是否验签通过
     */
    boolean verifyOpenApiToken(OpenApiToken token, ClientCredential credential, RequestContent content);
}
