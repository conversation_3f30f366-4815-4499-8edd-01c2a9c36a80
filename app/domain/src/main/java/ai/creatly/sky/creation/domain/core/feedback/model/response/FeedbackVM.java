/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.feedback.model.response;

import com.jspeeder.core.model.BizStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class FeedbackVM {

    /**
     * 反馈id
     */
    private String                     id;
    /**
     * 创建时间
     */
    private ZonedDateTime              createdAt;
    /**
     * 反馈用户ID
     */
    private String                     uid;
    /**
     * 用户名称
     */
    private String                     username;
    /**
     * 用户手机号
     */
    private String                     phone;
    /**
     * 邮箱地址
     */
    private String                     email;
    /**
     * 反馈标题
     */
    private String                     title;
    /**
     * 反馈明细
     */
    private String                     detail;
    /**
     * 状态
     */
    private BizStatus                  status;
    /**
     * 附件列表（如图、视频等）
     */
    private List<FeedbackAttachmentVM> attachments;
}
