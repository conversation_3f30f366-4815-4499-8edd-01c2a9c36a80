/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.model.enums;

import com.jspeeder.core.data.enums.ICode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 *
 * <AUTHOR>
 * @version AssetStatus.java, v0.1 2025-03-03 09:23
 */
@Getter
@RequiredArgsConstructor
public enum AssetStatus implements ICode {

    QUEUING("排队中"),
    PROCESSING("处理中"),
    FAILED("处理失败"),
    VALID("有效的"),
    DELETED("已删除（不会透出）");

    private final String desc;

    @Override
    public String getCode() {
        return this.name();
    }
}
