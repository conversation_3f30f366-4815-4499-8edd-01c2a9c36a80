/**
 * Copyright For creatly
 *
 * <AUTHOR>
 * @version ImageSubmitChangeRequest.java, v 0.1 2023-08-19 23:16
 */
package ai.creatly.sky.creation.domain.common.integration.midjourney.response;

import lombok.Data;

@Data
public class AiStoryCreatRoleTaskResponse {

    /**
     * 状态码: 1(提交成功), 21(已存在), 22(排队中), other(错误)
     */
    private Integer code;

    /**
     * 描述
     */
    private String description;

    /**
     * 任务ID
     */
    private String result;

    /**
     * 账户标识
     */
    private String accountKey;

}
