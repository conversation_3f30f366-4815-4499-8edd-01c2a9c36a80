package ai.creatly.sky.creation.domain.core.ai.video.service;

import ai.creatly.sky.creation.domain.core.ai.video.model.CommonDpVM;
import ai.creatly.sky.creation.domain.core.ai.video.model.VideoGenerationVM;

import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;


import java.util.List;

/**
 * <AUTHOR>
 */
public interface DigitalPersonService {
    Page<CommonDpVM> queryCommonDpList(Pageable pageable);

    List<CommonDpVM> queryCommonDpList(Integer page, Integer size);

    Page<VideoGenerationVM> queryGenerationPage(long uid, Pageable pageable);

    VideoGenerationVM queryGenerationById(long uid, long taskId);
}
