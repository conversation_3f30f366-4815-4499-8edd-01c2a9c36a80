/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version DramaStats.java, 2024-10-23 下午8:51 zhoudong
 */
@Data
@Accessors(chain = true)
public class DramaStats {

    /**
     * 剧情被使用次数
     */
    private Integer usedCount;

    public void increaseUsedCount() {
        if (this.usedCount == null) {
            this.usedCount = 0;
        }
        this.usedCount++;
    }
}
