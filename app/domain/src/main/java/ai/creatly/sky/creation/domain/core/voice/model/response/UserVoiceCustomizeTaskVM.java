/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.response;

import ai.creatly.sky.creation.domain.core.aitask.model.response.BaseUserAiTaskVM;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 声音定制任务基础信息
 *
 * <AUTHOR>
 * @version UserVoiceCustomizeTaskVM.java, v 0.1 2024-01-21 下午22:28 heb
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UserVoiceCustomizeTaskVM extends BaseUserAiTaskVM {

    /**
     * 声音ID
     */
    private String voiceId;
    /**
     * 声音中文名称
     */
    private String voiceCnName;
    /**
     * 取消原因（不合格）
     */
    private String cancelReason;
}
