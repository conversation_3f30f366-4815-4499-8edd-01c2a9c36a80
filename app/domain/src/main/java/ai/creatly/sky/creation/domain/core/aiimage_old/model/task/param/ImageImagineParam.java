/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.core.aiimage_old.model.task.param;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizParams;
import ai.creatly.sky.creation.domain.core.userfile.model.ShortLinkFile;
import lombok.Data;

import java.util.Map;

/**
 * Ai图片参数
 */
@Data
public class ImageImagineParam implements TaskBizParams {

    /**
     * 提示词
     */
    protected String prompts;

    /**
     * 翻译后的提示词
     */
    protected String translatePrompts;

    /**
     * 最终提示词
     */
    protected String finalPrompt;

    /**
     * 比例
     */
    protected String scale;

    /**
     * 账户id
     */
    protected String accountKey;

    /**
     * Prompt中匹配的图片数据
     */
    protected Map<String, ShortLinkFile> attachments;
}
