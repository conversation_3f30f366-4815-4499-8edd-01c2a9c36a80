/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.repository;

import ai.creatly.sky.creation.domain.core.digitalhuman.model.DigitalHumanAvatar;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 数字人仓储服务
 *
 * <AUTHOR>
 * @version DigitalHumanRepository.java, v 0.1 2024-05-25 14:22 syoka
 */
public interface DigitalHumanRepository {

    /**
     * 保存数字人形象
     *
     * @return 返回数字人形象id
     */
    long saveAvatar(DigitalHumanAvatar avatar);

    /**
     * 更新数字人形象
     *
     * @param avatar 数字人形象
     */
    void updateAvatar(DigitalHumanAvatar avatar);

    /**
     * 根据id查询数字人形象
     *
     * @param id 数字人形象id
     * @return -
     */
    Optional<DigitalHumanAvatar> queryOptionalById(long id);

    /**
     * 根据id查询数字人形象
     *
     * @param uid 用户uid
     * @param id  数字人形象id
     * @return -
     */
    Optional<DigitalHumanAvatar> queryOptionalByUidAndId(long uid, long id);

    /**
     * @param uid      用户id
     * @param pageable 分页对象
     * @return -
     */
    Page<DigitalHumanAvatar> queryValidUserAndSystemPage(long uid, Pageable pageable);

    Page<DigitalHumanAvatar> queryUserPage(long uid, Pageable pageable);

    List<DigitalHumanAvatar> queryValidByUid(long uid);

    /**
     * 删除数字人形象（软删除）
     *
     * @param avatarId 虚拟人形象id
     */
    void deleteById(long avatarId);
}
