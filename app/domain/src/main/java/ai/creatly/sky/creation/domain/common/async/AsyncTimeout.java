/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.async;

import ai.creatly.sky.creation.domain.common.env.RuntimeEnv;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version AsyncTimeout.java, v 0.1 2024-04-23 上午1:27 zhoudong
 */
@Component
@RequiredArgsConstructor
public class AsyncTimeout {

    /**
     * 调试环境默认10分钟
     */
    private final static int DEBUG_SECONDS = 600;

    private final RuntimeEnv runtimeEnv;

    public Duration of(Duration duration) {
        if (runtimeEnv.isLocal()) {
            return Duration.ofSeconds(DEBUG_SECONDS);
        }
        return duration;
    }

    public Duration ofSeconds(long seconds) {
        return this.ofSeconds(seconds, DEBUG_SECONDS);
    }

    public Duration ofSeconds(long seconds, long debugSeconds) {
        if (runtimeEnv.isLocal()) {
            return Duration.ofSeconds(debugSeconds);
        }
        return Duration.ofSeconds(seconds);
    }
}
