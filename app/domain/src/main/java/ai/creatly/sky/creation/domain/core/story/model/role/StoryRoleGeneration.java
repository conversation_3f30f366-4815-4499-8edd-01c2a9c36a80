/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version StoryRoleGeneration.java, v 0.1 2024-03-28 15:25 syoka
 */
@Getter
@RequiredArgsConstructor
public enum StoryRoleGeneration {


    Children("儿童"),
    Teenager("青少年"),
    Adult("成年"),
    MiddleAged("中年"),
    @Deprecated
    Middle_Aged("中年"),
    Elderly("老年"),
    ;

    private final String desc;

    public static StoryRoleGeneration ofDesc(String name) {
        for (StoryRoleGeneration roleGender : values()) {
            if (StringUtils.equals(roleGender.desc, name)) {
                return roleGender;
            }
        }
        return StoryRoleGeneration.Teenager;
    }
}
