/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.enums;

/**
 * <AUTHOR>
 * @version VideoProjectStage.java, 2024-10-25 下午3:31 zhoudong
 */
public enum VideoProjectStage {

    /**
     * 编辑中
     */
    editing,
    /*----------编排阶段（只有导轨信息，无最终视频）----------*/
    /**
     * 编排排队中
     */
    compose_queuing,
    /**
     * 编排中
     */
    composing,
    /**
     * 已编排
     */
    composed,
    /**
     * 编排失败
     */
    compose_failed,
    /*----------渲染阶段（导出并下载最终视频）----------*/
    /**
     * 渲染排队中
     */
    render_queuing,
    /**
     * 渲染中
     */
    rendering,
    /**
     * 已渲染
     */
    rendered,
    /**
     * 渲染失败
     */
    render_failed,
}
