/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version PlanErrorCode.java, v 0.1 2023-10-12 下午5:31 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum PlanErrorCode implements ErrorCode {

    PLAN_NOT_EXISTS("定价计划不存在"),
    USER_PLAN_ALREADY_EXISTS("该用户的私人定价计划已存在"),
    PLAN_ORDER_NO_PLAN_RELATED("订单没有关联有效的付费计划"),
    PLAN_ONLINE_PAY_UNSUPPORTED("该定价计划不支持在线支付"),
    MEMBER_BENEFIT_NOT_EXISTS("会员权益不存在"),
    PLAN_ORDER_SHIP_FAIL("定价计划发货失败"),
    PLAN_ORDER_FAIL("下单失败，请重试"),
    PLAN_ORDER_PAY_POLL_TIMEOUT("订单支付结果拉取超时，请手动确认"),
    TOP_UP_PLAN_CONFIG_ERROR("充值档位{}配置错误"),
    PLAN_ORDER_NOT_FOUND("订单不存在"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
