/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.helper;

import ai.creatly.sky.creation.domain.core.plan.model.UserPlanBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.enums.BenefitType;
import lombok.RequiredArgsConstructor;
import org.jooq.lambda.Seq;

import java.util.List;
import java.util.Optional;

import static java.util.stream.Collectors.toList;

/**
 *
 * <AUTHOR>
 * @version UserPlanBenefitFinder.java, v 0.1 2023-12-21 下午11:45 zhoudong
 */
@RequiredArgsConstructor(staticName = "of")
public class UserPlanBenefitFinder {

    private final List<UserPlanBenefit> benefits;

    public List<UserPlanBenefit> findByType(BenefitType type) {
        return benefits.stream()
                .filter(planBenefit -> planBenefit.getType() == type)
                .collect(toList());
    }

    public Optional<UserPlanBenefit> findSingleByType(BenefitType type) {
        return Seq.seq(benefits)
                .filter(planBenefit -> planBenefit.getType() == type)
                .findSingle();
    }
}
