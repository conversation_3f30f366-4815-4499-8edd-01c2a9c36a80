/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.model.request;

import ai.creatly.sky.creation.domain.common.validation.EachDigits;
import ai.creatly.sky.creation.domain.core.ai.image.model.enums.ImageAspectRatio;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version ImageGenerateRequest.java, v0.1 2025-02-20 21:36
 */
@Data
public class ImageGenerateRequest {

    @NotBlank
    @Size(max = 500)
    private String           promptText;
    @NotNull
    private ImageAspectRatio aspectRatio;
    @EachDigits(integer = 20, fraction = 0)
    private List<String>     fileIds;
    private Integer          count;
}
