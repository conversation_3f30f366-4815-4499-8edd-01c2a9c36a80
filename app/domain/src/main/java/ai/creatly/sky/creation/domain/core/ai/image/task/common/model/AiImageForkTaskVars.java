/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.task.common.model;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizVars;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version AiImageForkTaskVars.java, v0.1 2025-02-28 14:31
 */
@Data
@Accessors(chain = true)
public class AiImageForkTaskVars implements TaskBizVars {
    /**
     * 单个图片生成的任务ID
     */
    private List<Long> taskIds;
}
