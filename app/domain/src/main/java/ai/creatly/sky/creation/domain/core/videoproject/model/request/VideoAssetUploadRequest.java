/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version VideoAssetUploadRequest.java, v 0.1 2024-09-21 下午5:39 zhoudong
 */
@Data
@Accessors(chain = true)
public class VideoAssetUploadRequest {

    private Long            projectId;
    private String          desc;
    private List<String>    keywords;
}
