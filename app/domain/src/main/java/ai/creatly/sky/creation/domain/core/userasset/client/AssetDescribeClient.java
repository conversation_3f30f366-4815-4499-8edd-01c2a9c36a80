/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userasset.client;

import ai.creatly.sky.creation.domain.core.userasset.model.UserAsset;

/**
 * 素材打标
 *
 * <AUTHOR>
 * @version AssetDescribeClient.java, 2024-10-22 下午9:04 zhoudong
 */
public interface AssetDescribeClient {

    /**
     * 对用户素材进行算法打标
     *
     * @param asset 用户素材
     * @return 打标文件地址（OSS协议）
     */
    String describe(UserAsset asset);

    /**
     * 对用户素材进行算法打标
     *
     * @param asset           用户素材
     * @param videoSliceIndex 视频片段索引（从0开始）
     * @return 打标文件地址（OSS协议）
     */
    String describe(UserAsset asset, int videoSliceIndex);
}
