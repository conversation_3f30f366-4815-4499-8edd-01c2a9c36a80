/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.admin.request;

import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceCategory;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceGender;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceStatus;
import lombok.Data;

import java.util.Set;

/**
 *
 * <AUTHOR>
 * @version VoiceQueryRequest.java, v 0.1 2023-11-16 下午5:40 zhoudong
 */
@Data
public class VoiceQueryRequest {

    /**
     * 声音ID集合
     */
    private Set<String>   ids;
    /**
     * 声音编号（全局唯一）
     */
    private String        code;
    /**
     * 声音分类
     */
    private VoiceCategory category;
    /**
     * 声音状态
     */
    private VoiceStatus   status;
    /**
     * 英文展示名
     */
    private String        enName;
    /**
     * 中文展示名
     */
    private String        cnName;
    /**
     * 性别
     */
    private VoiceGender   gender;
}
