/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.speech.mapper;

import ai.creatly.sky.creation.domain.common.integration.azure.model.SynthesisVoiceNameEnum;
import ai.creatly.sky.creation.domain.common.integration.azure.model.ssml.Speak;
import ai.creatly.sky.creation.domain.common.integration.azure.model.ssml.SpeakVoice;
import ai.creatly.sky.creation.domain.common.integration.azure.model.ssml.SpeakVoiceExpression;
import ai.creatly.sky.creation.domain.common.integration.azure.model.ssml.SpeakVoiceProsody;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceEmotion;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceRole;
import ai.creatly.sky.creation.domain.support.speech.model.request.SpeechSynthesisRequest;
import ai.creatly.sky.creation.domain.support.speech.model.response.SynthesisVoiceVM;
import ai.creatly.sky.creation.domain.support.speech.model.response.VoiceOption;
import com.jspeeder.core.data.enums.Option;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.util.xml.XML;
import org.apache.commons.text.StringEscapeUtils;
import org.mapstruct.Mapper;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 微软语音相关模型转换
 *
 * <AUTHOR>
 * @version AzureVoiceMapper.java, v 0.1 2023-05-21 16:01 joton
 */
@Mapper(config = BaseMapperConfig.class)
public abstract class AzureVoiceMapper {

    public String toSSML(SpeechSynthesisRequest request) {
        SpeakVoice speakVoice = this.toSpeakVoice(request);
        SynthesisVoiceNameEnum voice = Objects.requireNonNull(SynthesisVoiceNameEnum.getByName(request.getVoiceName()));
        Speak speak = new Speak(voice.getLocale().getCode());
        return XML.toXMLString(speak.addVoice(speakVoice));
    }

    public SpeakVoice toSpeakVoice(SpeechSynthesisRequest request) {
        SpeakVoiceExpression expression = null;
        if (StringUtils.hasText(request.getStyle())) {
            expression = new SpeakVoiceExpression();
            expression.setStyle(request.getStyle());

            String role = request.getRole();
            if (!StringUtils.startsWithIgnoreCase(role, VoiceRole.DEFAULT_CODE)) {
                expression.setRole(role);
            }
            if (request.getStyleDegree() != null) {
                expression.setStyleDegree(request.getStyleDegree().getValue());
            }
        }

        String content = this.toVoiceContent(request);

        SpeakVoice speakVoice = new SpeakVoice();
        speakVoice.setName(request.getVoiceName());
        if (expression == null) {
            speakVoice.setContent(content);
        } else {
            expression.setContent(content);
            speakVoice.setExpression(expression);
        }
        return speakVoice;
    }

    private String toVoiceContent(SpeechSynthesisRequest request) {
        if (request.hasProsody()) {
            SpeakVoiceProsody prosody = new SpeakVoiceProsody()
                    .setPitch(Objects.isNull(request.getPitch()) ? null : request.getPitch().getValue())
                    .setRate(Objects.isNull(request.getRate()) ? null : request.getRate().getAzureSpeechRate())
                    .setVolume(Objects.isNull(request.getVolume()) ? null : request.getVolume().getValue())
                    .setContent(StringEscapeUtils.escapeXml10(request.getText()));
            return XML.toXMLString(prosody);
        }
        return StringEscapeUtils.escapeXml10(request.getText());
    }

    @Deprecated
    public SynthesisVoiceVM toSynthesisVoice(SynthesisVoiceNameEnum voiceNameEnum) {
        SynthesisVoiceVM synthesisVoice = new SynthesisVoiceVM();
        synthesisVoice.setName(voiceNameEnum.getName());
        synthesisVoice.setShortName(voiceNameEnum.getShortName());
        synthesisVoice.setDisplayName(voiceNameEnum.getDisplayName());
        synthesisVoice.setGender(voiceNameEnum.getGender());
        synthesisVoice.setLocale(Option.of(voiceNameEnum.getLocale().getCode(), voiceNameEnum.getLocale().getDesc()));
        synthesisVoice.setAvatar(voiceNameEnum.getAvatar());

        List<VoiceOption> roles = voiceNameEnum.getRoles().stream().map(voice -> {
            VoiceOption voiceOption = new VoiceOption();
            voiceOption.setCode(voice.getCode());
            voiceOption.setDesc(voice.getDesc());
            voiceOption.setPreviewAudioUrl(voiceNameEnum.getPreviewAudioUrl());
            return voiceOption;
        }).collect(Collectors.toList());
        // 方便前端在下拉列表中默认显示这个选项
        VoiceOption role = new VoiceOption();
        role.setCode(VoiceRole.DEFAULT_CODE);
        role.setDesc(voiceNameEnum.getAgeLevel().getDesc());
        role.setPreviewAudioUrl(voiceNameEnum.getPreviewAudioUrl());
        roles.add(0, role);
        synthesisVoice.setRoles(roles);

        List<VoiceOption> styles = voiceNameEnum.getStyles().stream().map(voice -> {
            VoiceOption voiceOption = new VoiceOption();
            voiceOption.setCode(voice.getCode());
            voiceOption.setDesc(voice.getDesc());
            voiceOption.setPreviewAudioUrl(voiceNameEnum.getPreviewAudioUrl());
            return voiceOption;
        }).collect(Collectors.toList());
        // 方便前端在下拉列表中默认显示这个选项
        VoiceOption style = new VoiceOption();
        style.setCode(null);
        style.setDesc(VoiceEmotion.DEFAULT_DESC);
        style.setPreviewAudioUrl(voiceNameEnum.getPreviewAudioUrl());
        styles.add(0, style);
        synthesisVoice.setStyles(styles);
        return synthesisVoice;
    }
}
