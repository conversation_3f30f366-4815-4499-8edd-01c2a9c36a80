/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.story.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 剪映故事草稿下载
 *
 * <AUTHOR>
 * @version StoryJYDraftVM.java, v 0.1 2024-03-17 22:39 heb
 */

@Data
@Accessors(chain = true)
public class StoryJYDraftVM {
    /**
     * http下载地址
     */
    private String        downUrl;
}
