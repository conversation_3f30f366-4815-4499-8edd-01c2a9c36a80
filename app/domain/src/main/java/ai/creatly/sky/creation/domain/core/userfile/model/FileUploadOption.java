/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.model;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import lombok.Data;

/**
 * 文件上传选项
 *
 * <AUTHOR>
 * @version FileUploadOption.java, v 0.1 2023-11-18 下午3:15 zhoudong
 */
@Data(staticConstructor = "of")
public class FileUploadOption {
    /**
     * 文件的OSS存储空间
     */
    private final String  bucket;
    /**
     * 文件的OSS对象key
     */
    private final String  key;
    /**
     * 文件访问权限
     */
    private final FileAcl acl;
}
