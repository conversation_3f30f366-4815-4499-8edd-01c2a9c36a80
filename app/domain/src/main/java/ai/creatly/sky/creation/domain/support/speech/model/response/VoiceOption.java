/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.speech.model.response;

import com.jspeeder.core.data.enums.Option;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version VoiceOption.java, v 0.1 2023-06-06 22:10 joton
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VoiceOption extends Option {

    /**
     * 预览音频地址（wav格式，前端可以直接用于audio标签的src属性中）
     */
    private String previewAudioUrl;
}
