/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.video.repository;

import ai.creatly.sky.creation.domain.core.aiproduct.video.error.ProductVideoErrorCode;
import ai.creatly.sky.creation.domain.core.aiproduct.video.model.ProductVideo;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.exception.SysException;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @version ProductVideoRepository.java, v 0.1 2024-05-24 下午9:22 zhoudong
 */
public interface ProductVideoRepository {

    long create(ProductVideo productVideo);

    default ProductVideo queryById(long id) {
        return this.queryOptionalById(id).orElseThrow(() -> new SysException(ProductVideoErrorCode.PRODUCT_VIDEO_NOT_FOUND));
    }

    Optional<ProductVideo> queryOptionalById(long id);

    /**
     * 查询用户的商品视频（不含创作中的）
     *
     * @param uid      用户ID
     * @param pageable 分页参数
     * @return 商品视频列表
     */
    Page<ProductVideo> queryPage(long uid, Pageable pageable);

    Map<Long, ProductVideo> queryMapByIds(Set<Long> ids);
}
