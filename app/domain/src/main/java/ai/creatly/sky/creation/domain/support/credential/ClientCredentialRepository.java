/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.credential;

import ai.creatly.sky.creation.domain.support.credential.model.ClientCredential;

import java.time.ZonedDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version ClientCredentialRepository.java, v 0.1 2023-07-29 13:36 joton
 */
public interface ClientCredentialRepository {

    /**
     * 创建用户客户端访问凭证
     *
     * @param credentials -
     */
    void createAppCredentials(ClientCredential credentials);

    /**
     * 查询用户客户端访问凭证
     *
     * @param uid 用户ID
     * @return -
     */
    Optional<ClientCredential> getAppCredentialsByUid(String uid);

    /**
     * 查询用户客户端访问凭证
     *
     * @param appKey 应用分配的appkey
     * @return -
     */
    Optional<ClientCredential> getAppCredentialsByAppKey(String appKey);

    /**
     * 更新有效期
     *
     * @param uid      用户ID
     * @param expireAt 到期时间
     */
    void updateExpireAt(long uid, ZonedDateTime expireAt);
}
