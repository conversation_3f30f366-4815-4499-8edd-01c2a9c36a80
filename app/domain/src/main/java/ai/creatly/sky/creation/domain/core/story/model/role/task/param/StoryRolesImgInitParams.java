/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.task.param;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizParams;
import ai.creatly.sky.creation.domain.core.story.model.role.StoryRole;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryStyle;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class StoryRolesImgInitParams implements TaskBizParams {

    /**
     * 故事角色列表（待生成形象图的角色）
     */
    private List<StoryRole> roles;
    /**
     * 角色图片比例
     */
    private String          scale;
    /**
     * 画面风格
     */
    private StoryStyle      storyStyle;
}
