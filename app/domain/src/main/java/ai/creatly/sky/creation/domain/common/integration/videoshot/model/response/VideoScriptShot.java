/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.videoshot.model.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version VideoShotRequest.java, v 0.1 2024-09-19 下午7:21 zhoudong
 */
@Data
@Accessors(chain = true)
public class VideoScriptShot {
    private String             text;
    private VideoShotMediaType mediaType;
    private String             assetUrl;
}
