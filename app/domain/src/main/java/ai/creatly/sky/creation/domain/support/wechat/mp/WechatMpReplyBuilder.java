/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.wechat.mp;

import ai.creatly.sky.creation.domain.core.auth.model.WechatAppName;
import ai.creatly.sky.creation.domain.support.wechat.mp.cipher.EncryptedResult;
import ai.creatly.sky.creation.domain.support.wechat.mp.cipher.WechatMpCipher;
import com.jspeeder.core.data.problem.exception.SysException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version WechatMpReplyBuilder.java, v 0.1 2024-10-15 下午8:45 zhoudong
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class WechatMpReplyBuilder {

    private final WechatMpClient wechatMpClient;
    private final WechatMpCipher wechatMpCipher;

    public String buildTextMessage(String text, String nonce, String fromUser, String toUser, WechatAppName appName) {
        String appId = wechatMpClient.getAppId(appName);
        String token = wechatMpClient.getToken(appName);
        byte[] aesKey = wechatMpClient.getAesKey(appName);

        String createTime = String.valueOf(System.currentTimeMillis() / 1000);
        String xml = "<xml>\n" +
                "  <ToUserName><![CDATA[" + toUser + "]]></ToUserName>\n" +
                "  <FromUserName><![CDATA[" + fromUser + "]]></FromUserName>\n" +
                "  <CreateTime>" + createTime + "</CreateTime>\n" +
                "  <MsgType><![CDATA[text]]></MsgType>\n" +
                "  <Content><![CDATA[" + text + "]]></Content>\n" +
                "</xml>";
        log.info("xml=\n{}", xml);

        EncryptedResult encryptedResult = wechatMpCipher.encrypt(aesKey, appId, xml);
        if (!encryptedResult.isSuccess()) {
            throw new SysException(WechatMpErrorCode.MESSAGE_ENCRYPT_ERROR);
        }

        String ciphertext = encryptedResult.getCiphertext();
        String msgSignature = wechatMpCipher.sign(token, nonce, createTime, ciphertext);
        String encryptXml = "<xml>\n" +
                "  <Encrypt><![CDATA[" + ciphertext + "]]></Encrypt>\n" +
                "  <MsgSignature><![CDATA[" + msgSignature + "]]></MsgSignature>\n" +
                "  <TimeStamp>" + createTime + "</TimeStamp>\n" +
                "  <Nonce><![CDATA[" + nonce + "]]></Nonce>\n" +
                "</xml>";
        log.info("encryptXml=\n{}", encryptXml);
        return encryptXml;
    }
}
