/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.service;

import ai.creatly.kylin.trade.sdk.order.client.TradeOrderClient;
import ai.creatly.kylin.trade.sdk.order.enums.OrderItemType;
import ai.creatly.kylin.trade.sdk.order.error.OrderErrorCode;
import ai.creatly.kylin.trade.sdk.order.request.OrderQueryParam;
import ai.creatly.kylin.trade.sdk.order.response.OrderDetailVO;
import ai.creatly.kylin.trade.sdk.order.response.OrderEntryVO;
import ai.creatly.kylin.trade.sdk.order.response.OrderItemVO;
import ai.creatly.kylin.trade.sdk.order.response.OrderVO;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.plan.error.PlanErrorCode;
import ai.creatly.sky.creation.domain.core.plan.mapper.PlanOrderMapper;
import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.PlanOrder;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import ai.creatly.sky.creation.domain.core.plan.model.specific.payment.PlanWxNativePrepayResult;
import ai.creatly.sky.creation.domain.core.plan.service.pay.PlanPayService;
import ai.creatly.sky.creation.domain.core.plan.service.pay.PlanPayServiceFactory;
import ai.creatly.sky.creation.domain.support.trade.TradeConstants;
import com.jspeeder.core.data.mapper.TimeMapper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version PlanOrderServiceImpl.java, v 0.1 2023-10-12 上午11:42 zhoudong
 */
@Service
@Primary
@RequiredArgsConstructor
public class PlanOrderServiceImpl implements PlanOrderService, PlanPayService {

    private final TradeOrderClient      tradeOrderClient;
    private final PlanService           planService;
    private final PlanPayServiceFactory planPayServiceFactory;
    private final PlanOrderMapper       planOrderMapper;

    @Value("${spring.application.name}")
    private String appName;

    @Override
    public PlanWxNativePrepayResult doWxNativePay(Plan plan, String payerClientIp, UserContext userContext) {
        Asserts.notNull(plan.getAutoRenewed(), "will not happen");
        Validates.isTrue(!plan.getAutoRenewed(), "当前暂未支持自动续费");
        PlanPayService planPayService = planPayServiceFactory.getPlanPayService(plan);
        return planPayService.doWxNativePay(plan, payerClientIp, userContext);
    }

    @Override
    public void shipOnPaid(PlanOrder planOrder) {
        PlanPayService planPayService = planPayServiceFactory.getPlanPayService(planOrder.getPaidPlan());
        planPayService.shipOnPaid(planOrder);
    }

    @Override
    public Page<PlanOrder> queryPaidPage(long uid, List<PlanType> planTypes, Pageable pageable) {
        OrderQueryParam orderQueryParam = new OrderQueryParam();
        orderQueryParam.setTenantCode(this.appName);
        orderQueryParam.setBuyerId(String.valueOf(uid));
        orderQueryParam.setBizTypes(planTypes.stream().map(PlanType::name).collect(toList()));
        orderQueryParam.setPaid(true);
        Page<OrderEntryVO> orderPage = tradeOrderClient.queryOrderPage(orderQueryParam, pageable);

        return orderPage.map(order -> {
            Plan plan = this.toPlan(order.getItems());
            if (plan.getUid() != null) {
                Asserts.isTrue(plan.getUid().equals(uid), "will not happen");
            }
            Plan paidPlan = planOrderMapper.toPaidPlan(plan, order.getItems());
            return this.toPlanOrder(order, paidPlan);
        });
    }

    @Override
    public @Nullable PlanOrder queryPlanOrder(long orderId) {
        try {
            OrderDetailVO orderDetail = tradeOrderClient.queryOrderDetail(TradeConstants.TENANT_CODE, orderId);
            Plan plan = this.toPlan(orderDetail.getItems());
            Plan paidPlan = planOrderMapper.toPaidPlan(plan, orderDetail.getItems());
            return this.toPlanOrder(orderDetail, paidPlan);
        } catch (BizException e) {
            if (Objects.equals(e.getErrorCode().getCode(), OrderErrorCode.ORDER_NOT_EXISTS.getCode())) {
                // 订单不存在
                return null;
            }
            throw e;
        }
    }

    private PlanOrder toPlanOrder(OrderVO order, Plan paidPlan) {
        return new PlanOrder()
                .setId(order.getId())
                .setBizSn(order.getBizSn())
                .setTitle(order.getTitle())
                .setUid(Long.parseLong(order.getBuyerId()))
                .setOriginalFee(order.getOriginalFee())
                .setRealFee(order.getRealFee())
                .setPaidFee(order.getPaidFee())
                .setStatus(order.getStatus())
                .setPaymentChannel(order.getPaymentChannel())
                .setPaidTime(TimeMapper.INSTANCE.toZonedDateTime(order.getPaidTime()))
                .setPaidPlan(paidPlan);
    }

    private Plan toPlan(List<OrderItemVO> items) {
        return items.stream()
                .filter(item -> item.getType().in(OrderItemType.SUBSCRIPTION, OrderItemType.CASH))
                .findFirst()
                .map(OrderItemVO::getItemId)
                .map(planService::queryByIdWithBenefits)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .orElseThrow(() -> new BizException(PlanErrorCode.PLAN_ORDER_NO_PLAN_RELATED));
    }
}
