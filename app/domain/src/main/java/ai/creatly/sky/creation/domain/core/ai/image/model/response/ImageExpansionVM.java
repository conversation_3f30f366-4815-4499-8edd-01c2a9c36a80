/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.model.response;

import ai.creatly.sky.creation.domain.core.ai.image.model.enums.ImageExpandDirection;
import lombok.Data;

/**
 * <AUTHOR>
 * @version ImageExpansionVM.java, v0.1 2025-02-19 21:41
 */
@Data
public class ImageExpansionVM extends AiImageTaskVM {

    /**
     * 原图
     */
    private String               imageUrl;
    private ImageExpandDirection direction;
}
