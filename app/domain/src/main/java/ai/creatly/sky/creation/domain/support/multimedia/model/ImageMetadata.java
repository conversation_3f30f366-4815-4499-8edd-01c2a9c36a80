/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.multimedia.model;

import ai.creatly.sky.creation.domain.support.file.model.FileSize;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version ImageMetadata.java, v 0.1 2024-06-11 下午5:58 zhoudong
 */
@Data
@Accessors(chain = true)
public class ImageMetadata {

    /**
     * 图片大小
     */
    private FileSize            size;
    /**
     * 文件内容MD5
     */
    private String              contentMd5;
    /**
     * 图片文件扩展名（小写）
     */
    private String              extension;
    /**
     * 图片宽度（像素）
     */
    private Integer             width;
    /**
     * 图片高度（像素）
     */
    private Integer             height;
    /**
     * 生成式图片的提示词
     */
    private String              prompt;
    /**
     * 图片元数据
     */
    @Nullable
    private Map<String, String> metadata;

    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, Objects.toString(value));
    }
}
