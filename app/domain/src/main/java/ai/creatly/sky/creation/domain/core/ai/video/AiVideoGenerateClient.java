/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.video;

import ai.creatly.sky.creation.domain.core.ai.video.model.AiVideoTaskResult;
import ai.creatly.sky.creation.domain.core.ai.video.model.VideoGenerateRequest;
import ai.creatly.sky.creation.domain.core.ai.video.model.VideoGenerateTaskInput;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;

/**
 * AI通用生图
 *
 * <AUTHOR>
 * @version AiVideoGenerateClient.java, v0.1 2025-03-04 13:26
 */
public interface AiVideoGenerateClient {

    /**
     * 返回generateTaskId
     * @param input
     * @param userContext
     * @return
     */
    AiVideoTaskResult generateText2Video(VideoGenerateTaskInput input, UserContext userContext);

    /**
     * 文生视频消耗
     * @param aiTask
     * @param request
     * @return
     */
    CreditsExpense text2VideoExpense(AiTask aiTask, VideoGenerateRequest request);


    AiVideoTaskResult queryText2VideoGeneration(String taskId, UserContext userContext);

    /**
     * 返回generateTaskId
     * @param input
     * @param userContext
     * @return
     */
    AiVideoTaskResult generateImage2Video(VideoGenerateTaskInput input, UserContext userContext);

    /**
     * 图生视频消耗
     * @param aiTask
     * @param request
     * @return
     */
    CreditsExpense image2VideoExpense(AiTask aiTask,VideoGenerateRequest request);

    AiVideoTaskResult queryImage2VideoGeneration(String taskId, UserContext userContext);

}
