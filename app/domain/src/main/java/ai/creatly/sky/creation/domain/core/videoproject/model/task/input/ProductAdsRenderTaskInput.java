/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.task.input;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizInput;
import ai.creatly.sky.creation.domain.core.videoproject.model.*;
import ai.creatly.sky.creation.domain.core.voice.model.PresetVoice;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * <AUTHOR>
 * @version ProductAdsRenderTaskInput.java, v 0.1 2024-09-26 上午10:19 zhoudong
 */
@Data
@Accessors(chain = true)
public class ProductAdsRenderTaskInput implements TaskBizInput {

    /**
     * 视频脚本
     */
    private VideoScript          script;
    /**
     * 全文配音
     */
    private PresetVoice          voiceover;
    /**
     * 字幕层
     */
    @Nullable
    private VideoSubtitleLayer   subtitleLayer;
    /**
     * 数字人视频层
     */
    @Nullable
    private VideoAvatarLayer     avatarLayer;
    /**
     * 背景层
     */
    @Nullable
    private VideoBackgroundLayer backgroundLayer;
    /**
     * 音轨列表
     */
    private List<AudioTrack>     audioTracks;
    /**
     * 视频宽度（像素）
     */
    private Integer              width;
    /**
     * 视频高度（像素）
     */
    private Integer              height;
    /**
     * 视频分镜列表
     */
    private List<VideoShot>      shots;
}
