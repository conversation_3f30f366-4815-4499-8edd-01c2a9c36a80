/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.response.task;

import ai.creatly.sky.creation.domain.core.aitask.model.response.BaseUserAiTaskVM;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * 声音合成任务（列表项）
 *
 * <AUTHOR>
 * @version VoiceSynthesisTaskVM.java, v 0.1 2023-11-06 下午10:18 zhoudong
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class VoiceSynthesisTaskVM extends BaseUserAiTaskVM {

    /*-------------------- 合成输入 --------------------*/
    /**
     * 输入文本
     */
    private String inputText;
    /**
     * 合成使用的声音ID
     */
    private String voiceId;
    /**
     * 合成使用的声音编号（全局唯一）
     */
    private String voiceCode;
    /**
     * 合成使用的本地化口音编号
     */
    private String voiceLocaleCode;
    /**
     * 合成使用的本地化口音描述（中文展示）
     */
    private String voiceLocaleDesc;
    /**
     * 合成使用的角色编号
     */
    @Nullable
    private String voiceRoleCode;
    /**
     * 合成使用的角色描述
     */
    @Nullable
    private String voiceRoleDesc;
    /**
     * 合成使用的情绪编号
     */
    @Nullable
    private String voiceEmotionCode;
    /**
     * 合成使用的情绪描述
     */
    @Nullable
    private String voiceEmotionDesc;

    /*-------------------- 合成结果 --------------------*/
    /**
     * 合成的音频文件名（无扩展名，任务完成后才有值）
     */
    @Nullable
    private String audioName;
    /**
     * 合成的音频文件地址（任务完成后才有值）
     */
    @Nullable
    private String audioUrl;
}
