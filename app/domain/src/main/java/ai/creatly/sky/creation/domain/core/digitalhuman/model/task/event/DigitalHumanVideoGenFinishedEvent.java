/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.model.task.event;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @version StoryShotImgGenEvent.java, v 0.1 2024-04-09 19:36 syoka
 */
public class DigitalHumanVideoGenFinishedEvent extends ApplicationEvent {


    public DigitalHumanVideoGenFinishedEvent(Object source) {
        super(source);
    }

    @Accessors(chain = true)
    @Data
    public static class DigitalHumanVideoGenFinishedEventObj {
        private Long taskId;
        private Long videoId;
        private Long uid;
    }

}
