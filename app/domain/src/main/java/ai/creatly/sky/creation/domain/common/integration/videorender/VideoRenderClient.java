/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.videorender;

import ai.creatly.sky.creation.domain.common.integration.videorender.model.VideoRenderRequest;
import ai.creatly.sky.creation.domain.common.integration.videorender.model.VideoRenderResult;

/**
 * <AUTHOR>
 * @version VideoRenderClient.java, v 0.1 2024-09-26 下午7:35 zhoudong
 */
public interface VideoRenderClient {

    /**
     * 提交渲染视频任务（异步）
     */
    void renderVideo(VideoRenderRequest request);

    /**
     * 检查视频渲染进度
     */
    VideoRenderResult getRenderProgress(long taskId);
}
