/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.model.request;

import ai.creatly.sky.creation.domain.common.validation.EachDigits;
import ai.creatly.sky.creation.domain.common.validation.EachNotBlank;
import ai.creatly.sky.creation.domain.common.validation.EachNotNull;
import ai.creatly.sky.creation.domain.common.validation.NoDuplicates;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaType;
import ai.creatly.sky.creation.domain.support.category.model.request.CategoryPathDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version DramaCreateRequest.java, v 0.1 2024-10-18 下午9:17 zhoudong
 */
@Data
public class DramaCreateRequest {

    @Size(max = 100)
    private String                name;
    @NotNull
    private DramaType             type;
    @NotEmpty
    @EachNotNull
    @Valid
    private List<CategoryPathDTO> productCategories;
    @NotEmpty
    @EachNotNull
    @Valid
    private List<CategoryPathDTO> crowdCategories;
    @NotEmpty
    @Size(max = 2)
    @NoDuplicates
    @EachNotBlank
    @EachDigits(integer = 20, fraction = 0)
    private List<String>          videoFileIds;
    @NotBlank
    @Size(min = 2, max = 100)
    private String                endText;
}
