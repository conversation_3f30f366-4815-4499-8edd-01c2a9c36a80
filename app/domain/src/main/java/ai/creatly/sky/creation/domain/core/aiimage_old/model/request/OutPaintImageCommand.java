/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.core.aiimage_old.model.request;

import jakarta.annotation.Nonnull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 图片扩展描述命令
 *
 * <AUTHOR>
 * @version OutPaintImageCommand.java, v 0.1 2023-09-25 10:33 syoka
 */
@Data
public class OutPaintImageCommand {


    /**
     * zoomIn任务的taskId
     */
    private String taskId;

    /**
     * 50为2X扩绘
     * 75为1.5X扩绘
     */
    @Nonnull
    @Pattern(regexp = "50|75")
    private Integer outPaintSize;
}
