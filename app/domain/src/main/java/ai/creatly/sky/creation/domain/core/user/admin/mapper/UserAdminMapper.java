/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.admin.mapper;

import ai.creatly.sky.creation.domain.core.user.admin.model.UserVM;
import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version UserAdminMapper.java, 2024-12-17 下午7:02 zhoudong
 */
@Mapper(config = BaseMapperConfig.class)
public interface UserAdminMapper {

    UserVM toUserVM(UserInfo userInfo);
}
