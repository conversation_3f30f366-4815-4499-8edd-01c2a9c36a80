/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.support.aichat.langchain.message;

import ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal.LLMConversation;
import ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal.LLMMessage;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.LLMChainContext;

import java.util.List;

/**
 * 聊天服务
 */
public interface ChatMessageService {

    /**
     * 获取用户指定会话的聊天历史记录
     *
     * @param uid 用户id
     * @return 当前用户会话的聊天历史记录
     */
    List<LLMMessage> getUserChatMessagesHistories(Long uid, Long conversationId);

    /**
     * 获取用户指定会话的指定场景聊天历史记录
     *
     * @param uid 用户id
     * @return 当前用户会话的聊天历史记录
     */
    List<LLMMessage> getUserChatMessagesScenariosHistories(Long uid, Long conversationId, Long scenarioId, Long scenarioNo);

    /**
     * 用户会话添加记录
     *
     * @param context 用户上下文
     * @param llmMessage llm模型
     */
    void addUserChatMessage(LLMChainContext context, LLMMessage llmMessage);

    /**
     * 清除用户会话上下文
     *
     * @param uid 用户id
     * @param conversationId 用户会话id
     */
    void clearUserChatMessageHistory(Long uid, Long conversationId);

    /**
     * 创建一个用户会话
     *
     * @param uid 用户id
     * @param scenarioId
     * @return 会话id
     */
    Long newUserConversation(Long uid, Long scenarioId);

    /**
     * 删除用户会话记录
     *
     * @param uid 用户id
     * @param conversationId 会话id
     */
    void removeUserChatConversation(Long uid, Long conversationId);

    /**
     * 查询用户的会话个数
     *
     * @param uid 用户id
     * @return 会话个数
     */
    Integer findUserConversationNumber(Long uid);

    /**
     * 获取用户会话信息
     *
     * @param uid
     * @param conversationId
     * @return
     */
    LLMConversation findUserConversation(Long uid, Long conversationId);


    /**
     * 获取用户的会话
     *
     * @param uid 用户id
     * @return 会话摘要列表
     */
    List<LLMConversation> findUserConversations(Long uid);


    /**
     * 更新会话的标题
     *
     * @param uid 用户id
     * @param conversationId 会话id
     * @param title 标题
     */
    void updateConversation(Long uid, Long conversationId, String title);
}
