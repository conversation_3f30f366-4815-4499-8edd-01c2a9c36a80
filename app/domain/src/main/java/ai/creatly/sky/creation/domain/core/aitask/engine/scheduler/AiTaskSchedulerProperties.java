/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.engine.scheduler;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 * @version AiTaskSchedulerProperties.java, v 0.1 2023-07-28 21:17 joton
 */
@Data
@ConfigurationProperties(prefix = "application.ai-task.scheduler")
public class AiTaskSchedulerProperties {

    /**
     * 是否启用调度
     */
    private Boolean      enabled;
    /**
     * 调度的任务类型白名单（不设置则为全部，如果本地打开调度进行调试，则只能设置一个）
     */
    private List<String> enabledTaskTypes;
    /**
     * 调度的任务类型黑名单（先过白名单，再过黑名单）
     */
    private List<String> disabledTaskTypes;
}
