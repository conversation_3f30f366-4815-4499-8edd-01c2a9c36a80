/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.service;

import ai.creatly.sky.creation.domain.core.aiimage_old.error.AiImageErrorCode;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryType;
import ai.creatly.sky.creation.domain.support.config.repository.SystemPreferenceRepository;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version DefaultStoryTypeDescriptor.java, v 0.1 2024-03-07 16:28 syoka
 */
@Component
@RequiredArgsConstructor
public class DefaultStoryTypeDescriptor implements StoryTypeDescriptor {

    private final        SystemPreferenceRepository systemPreferenceRepository;
    private final static String                     STORY_TYPE_KEY = "STORY_TYPE_LIST";


    @Override
    public StoryType getStoryType(String type) {
        return findStoryType(type);
    }

    @Override
    public List<StoryType> getAllStoryType() {
        String storyTypeConfig = systemPreferenceRepository.getSystemPreferences(STORY_TYPE_KEY);
        return JSON.parseList(storyTypeConfig, StoryType.class);
    }

    public StoryType findStoryType(String storyType) {
        String storyTypeConfig = systemPreferenceRepository.getSystemPreferences(STORY_TYPE_KEY);
        List<StoryType> storyTypes = JSON.parseList(storyTypeConfig, StoryType.class);
        // 获取配型配置
        return storyTypes.stream()
                .filter(type -> type.getCode().equals(storyType))
                .findFirst()
                .orElseThrow(() -> new BizException(AiImageErrorCode.GENERATE_STORY_OCCURS_UNKNOWN_EXCEPTION));

    }

}
