/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.messaging.consumer;

/**
 * MQ消息监听处理器
 *
 * <AUTHOR>
 * @version RocketMQListener.java, v 0.1 2023-07-23 09:45 joton
 */
public interface MQListener<T> {

    /**
     * 消费者配置
     */
    ConsumerConfig consumerConfig();

    T doConvertMessage(String body);

    ConsumeAction onMessage(T event);
}
