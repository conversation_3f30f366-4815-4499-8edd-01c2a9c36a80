/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.aiproduct.model;

import ai.creatly.sky.creation.domain.core.aitask.model.response.BaseUserAiTaskVM;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version FinishedProductImageVM.java, v 0.1 2023-11-15 19:14 syoka
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FinishedProductImageVM extends BaseUserAiTaskVM {

    private String taskId;

    /**
     * 场景图
     */
    private String sceneFileUrl;

    /**
     * 商品图文件url
     */
    private String productImageFileUrl;

    /**
     * 成品文件图url
     */
    private String finishedProductImageUrl;


}
