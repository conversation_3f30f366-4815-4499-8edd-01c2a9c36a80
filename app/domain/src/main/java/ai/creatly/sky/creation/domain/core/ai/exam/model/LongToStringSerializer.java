package ai.creatly.sky.creation.domain.core.ai.exam.model;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version LongToStringSerializer.java, v0.1 2025-04-07 16:32
 */
public class LongToStringSerializer extends StdSerializer<Long> {

    protected LongToStringSerializer() {
        super(Long.class);
    }

    @Override
    public void serialize(Long value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeString(value.toString());
    }
}
