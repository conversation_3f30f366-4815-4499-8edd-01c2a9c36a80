/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.organization.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version Organization.java, v 0.1 2024-01-22 15:23 syoka
 */
@Data
@Accessors(chain = true)
public class Organization {

    private Long id;
    private String code;
    private String name;
    private String uid;
    private String userName;
    private String logoUrl;

    private List<QualificationFile> qualification;
}
