/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.wechat.mp;

import ai.creatly.sky.creation.domain.common.http.HttpRequest;
import ai.creatly.sky.creation.domain.support.wechat.mp.model.message.receive.WechatMpEvent;

/**
 * <AUTHOR>
 * @version WechatMpEventHandler.java, v 0.1 2024-10-15 下午7:52 zhoudong
 */
public interface WechatMpEventHandler {

    boolean support(WechatMpEvent event);

    String handle(HttpRequest originalRequest, WechatMpEvent event);
}
