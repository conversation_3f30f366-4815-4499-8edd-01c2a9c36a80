/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.helper;

import ai.creatly.sky.creation.domain.core.plan.model.PlanBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.enums.BenefitType;
import com.jspeeder.core.util.json.JSON;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.jooq.lambda.Seq;

import java.util.*;

import static java.util.stream.Collectors.toList;

/**
 * 权益查找器
 *
 * <AUTHOR>
 * @version PlanBenefitFinder.java, v 0.1 2023-10-12 下午3:01 zhoudong
 */
@RequiredArgsConstructor(staticName = "of")
public class PlanBenefitFinder {

    private final List<PlanBenefit> benefits;

    public List<PlanBenefit> findByType(BenefitType type) {
        return benefits.stream()
                .filter(planBenefit -> planBenefit.getType() == type)
                .collect(toList());
    }

    public Map<BenefitType, List<PlanBenefit>> groupByTypes(BenefitType... types) {
        Map<BenefitType, List<PlanBenefit>> map = new HashMap<>();
        for (BenefitType type : types) {
            map.put(type, this.findByType(type));
        }
        return map;
    }

    public List<PlanBenefit> findByTypes(BenefitType... types) {
        return benefits.stream()
                .filter(planBenefit -> ArrayUtils.contains(types, planBenefit.getType()))
                .collect(toList());
    }

    public Optional<PlanBenefit> findSingleByType(BenefitType type) {
        return Seq.seq(benefits)
                .filter(planBenefit -> planBenefit.getType() == type)
                .findSingle();
    }

    public <T> List<T> findByType(BenefitType type, Class<T> benefitValueType) {
        return benefits.stream()
                .filter(planBenefit -> planBenefit.getType() == type)
                .map(PlanBenefit::getValue)
                .filter(Objects::nonNull)
                .map(value -> JSON.parseObject(value, benefitValueType))
                .collect(toList());
    }

    public <T> Optional<T> findSingleByType(BenefitType type, Class<T> benefitValueType) {
        return Seq.seq(benefits)
                .filter(planBenefit -> planBenefit.getType() == type)
                .map(PlanBenefit::getValue)
                .filter(Objects::nonNull)
                .map(value -> JSON.parseObject(value, benefitValueType))
                .findSingle();
    }
}
