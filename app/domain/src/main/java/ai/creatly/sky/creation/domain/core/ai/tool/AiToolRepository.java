/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.tool;

import java.util.List;

/**
 * <AUTHOR>
 * @version AiToolRepository.java, v0.1 2025-02-19 21:17
 */
public interface AiToolRepository {

    List<AiTool> queryImageTools();

    List<AiTool> queryIndexTools();

    List<AiTool> queryAudioTools();

    List<AiTool> queryVideoTools(String phone);

    List<AiTool> queryLoraTools();

    String getParam(String modelId);

    List<AiTool> queryTools();
}
