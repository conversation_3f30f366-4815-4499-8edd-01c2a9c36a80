/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.model.response;

import lombok.Data;
import org.jetbrains.annotations.Nullable;

/**
 *
 * <AUTHOR>
 * @version GenerationTimeVM.java, v0.1 2025-03-03 18:40
 */
@Data
public class GenerationTimeVM {

    /**
     * 预估值
     */
    private Integer estimatedMills;
    /**
     * 实际值
     */
    @Nullable
    private Integer actualMills;

}
