/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.shot.response;

import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechRateType;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

/**
 * 分镜人物对话
 *
 * <AUTHOR>
 * @version ShotDialogueVM.java, v 0.1 2024-04-13 14:44 syoka
 */
@Data
public class ShotDialogueVM {
    /**
     * 角色名
     */
    private String         roleName;
    /**
     * 台词
     */
    private String         text;
    /**
     * 角色ID
     */
    @Nullable
    private String         roleId;
    /**
     * 角色图片地址（https://）
     */
    @Nullable
    private String         rolePortraitUrl;
    /**
     * 角色声音ID
     */
    @Nullable
    private String         voiceId;
    /**
     * 声音情绪（通过声音列表API获取）
     */
    @Nullable
    private String         emotionCode;
    /**
     * 对话语速
     */
    @Nullable
    private SpeechRateType rate;
    /**
     * 对话音频文件ID
     */
    @Nullable
    private String         audioFileId;
    /**
     * 对话音频文件地址（https://）
     */
    @Nullable
    private String         audioUrl;
}
