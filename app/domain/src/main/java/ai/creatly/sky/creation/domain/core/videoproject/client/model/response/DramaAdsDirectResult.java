/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.client.model.response;

import ai.creatly.sky.creation.domain.core.drama.model.Drama;
import ai.creatly.sky.creation.domain.core.drama.model.DramaRole;
import ai.creatly.sky.creation.domain.core.userfile.model.FileContent;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoShot;
import ai.creatly.sky.creation.domain.support.multimedia.model.AudioMetadata;
import ai.creatly.sky.creation.domain.support.multimedia.model.VideoMetadata;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Map;

/**
 * 剧情视频编导结果
 *
 * <AUTHOR>
 * @version DramaAdsDirectResult.java, 2024-10-22 上午10:41 zhoudong
 */
@Data
@Accessors(chain = true)
public class DramaAdsDirectResult {
    /**
     * 推荐的剧情（如果返回null，则说明没有匹配到合适的剧情）
     */
    @Nullable
    private Drama                    drama;
    /**
     * 视频分镜列表（完整的：剧情片头+多个商品广告分镜+剧情片尾）
     */
    private List<VideoShot>          shots;
    /**
     * 处理过的分镜视频元数据（输出文件ID，视频元数据）
     */
    private Map<Long, VideoMetadata> processedVideoMap;
    /**
     * 从剧情里匹配的广告代言人（含声音模型）(如果算法端黑盒处理掉了，这里就不需要感知了)
     */
    @Nullable
    private DramaRole                adsRole;
    /**
     * 广告音频元数据（大小、时长）
     */
    private AudioMetadata            adsAudioMetadata;
    /**
     * 广告音频内容（原始文案、逐字时间轴、字幕）
     */
    private FileContent              adsAudioContent;
}
