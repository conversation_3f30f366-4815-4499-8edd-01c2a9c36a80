/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.admin.model.request;

import ai.creatly.sky.creation.domain.common.validation.EachNotNull;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaType;
import ai.creatly.sky.creation.domain.support.category.model.request.CategoryPathDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version DramaUpdateRequest.java, 2024-12-12 下午5:14 zhoudong
 */
@Data
public class DramaUpdateRequest {

    /**
     * 剧情名称
     */
    private String                name;
    /**
     * 剧情类型
     */
    private DramaType             type;
    @NotEmpty
    @EachNotNull
    @Valid
    private List<CategoryPathDTO> productCategories;
    @NotEmpty
    @EachNotNull
    @Valid
    private List<CategoryPathDTO> crowdCategories;
}
