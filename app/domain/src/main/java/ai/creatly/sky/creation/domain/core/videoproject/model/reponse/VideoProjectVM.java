/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.reponse;

import ai.creatly.sky.creation.domain.core.videoproject.model.VideoSubtitleLayer;
import ai.creatly.sky.creation.domain.core.videoproject.model.enums.VideoAspectRatio;
import ai.creatly.sky.creation.domain.core.videoproject.model.enums.VideoProjectStage;
import ai.creatly.sky.creation.domain.core.videoproject.model.enums.VideoProjectStatus;
import ai.creatly.sky.creation.domain.core.videoproject.model.enums.VideoProjectType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version VideoProjectVM.java, v 0.1 2024-09-21 下午6:43 zhoudong
 */
@Data
public class VideoProjectVM {

    /**
     * 主键ID
     */
    private String                   id;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime            createdAt;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime            updatedAt;
    /**
     * 项目类型
     */
    private VideoProjectType         type;
    /**
     * 项目状态
     */
    private VideoProjectStatus       status;
    /**
     * 项目阶段
     */
    private VideoProjectStage        stage;
    /**
     * 当前项目阶段的失败原因
     */
    private String                   stageFailureReason;
    /**
     * 项目名
     */
    private String                   name;
    /**
     * 素材列表
     */
    private List<VideoAssetVM>       assets;
    /**
     * 数字人视频层
     */
    @Nullable
    private VideoAvatarLayerVM       avatarLayer;
    /**
     * 背景层
     */
    @Nullable
    private VideoBackgroundLayerVM   backgroundLayer;
    /**
     * 字幕层
     */
    @Nullable
    private VideoSubtitleLayer       subtitleLayer;
    /**
     * 视频脚本
     */
    @Nullable
    private VideoScriptVM            script;
    /**
     * 视频配音
     */
    @Nullable
    private VideoVoiceoverVM         voiceover;
    /**
     * 视频分镜列表
     */
    private List<VideoShotVM>        shots;
    /**
     * 视频编排时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime            composeAt;
    /**
     * 视频编排是否过期（原始输入信息更新后，需要提示用户重新编排视频）
     */
    private Boolean                  composeExpired;
    /**
     * 视频宽高比
     */
    private VideoAspectRatio         aspectRatio;
    /**
     * 宽度（像素）
     */
    private Integer                  width;
    /**
     * 高度（像素）
     */
    private Integer                  height;
    /**
     * 项目封面地址
     */
    @Nullable
    private String                   coverUrl;
    /**
     * 视频时长
     */
    @Nullable
    private Integer                  durationMills;
    /**
     * 最新一批编排任务（一个或多个）
     */
    @Nullable
    private List<VideoComposeTaskVM> composeTasks;
    /**
     * 导出的视频产物
     */
    private List<VideoVM>            videos;
}
