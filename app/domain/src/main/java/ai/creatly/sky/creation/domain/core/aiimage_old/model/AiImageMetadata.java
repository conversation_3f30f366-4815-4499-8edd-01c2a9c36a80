/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiimage_old.model;

import ai.creatly.sky.creation.domain.core.userfile.model.FileMetadata;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version AiImageMetadata.java, v 0.1 2023-10-09 19:25 syoka
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AiImageMetadata extends FileMetadata {

    private Integer width;
    private Integer height;
}
