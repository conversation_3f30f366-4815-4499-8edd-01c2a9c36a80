/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.multimedia.model;

import ai.creatly.sky.creation.domain.support.file.model.FileSize;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Duration;
import java.util.Map;

/**
 * 视频元数据
 *
 * <AUTHOR>
 * @version VideoMetadata.java, v 0.1 2024-05-24 下午9:41 zhoudong
 */
@Data
@Accessors(chain = true)
public class VideoMetadata {

    /**
     * 视频大小
     */
    private FileSize            size;
    /**
     * 文件内容MD5
     */
    private String              contentMd5;
    /**
     * 视频时长
     */
    private Duration            duration;
    /**
     * 视频格式（以小写字符串表示，如 "mp4", "avi" 等）
     */
    private String              format;
    /**
     * 视频宽度（以像素为单位）
     */
    private Integer             width;
    /**
     * 视频高度（以像素为单位）
     */
    private Integer             height;
    /**
     * 视频比特率（以 bps 为单位）
     */
    private Integer             bitrate;
    /**
     * 视频帧率（以 fps 为单位）
     */
    private Double              frameRate;
    /**
     * 视频元数据
     */
    private Map<String, String> metadata;
}
