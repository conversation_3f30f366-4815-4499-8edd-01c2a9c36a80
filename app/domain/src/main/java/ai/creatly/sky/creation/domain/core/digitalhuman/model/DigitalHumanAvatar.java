/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.model;

import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.userfile.model.BizFile;
import ai.creatly.sky.creation.domain.core.videoproject.model.enums.VideoAspectRatio;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 数字人形象
 *
 * <AUTHOR>
 * @version DigitalHumanAvatar.java, v 0.1 2024-05-24 19:48 syoka
 */
@Data
@Accessors(chain = true)
public class DigitalHumanAvatar {

    public static final String WAITING = "waiting";
    public static final String SUCCESS = "success";
    public static final String FAILURE = "failure";
    public static final String DELETE  = "delete";

    /**
     * 形象人id
     */
    private Long             id;
    /**
     * 创建时间
     */
    private ZonedDateTime    createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime    updatedAt;
    /**
     * 形象用户id
     */
    private Long             uid;
    /**
     * 形象外部id
     */
    private String           extOutId;
    /**
     * 形象用户名
     */
    private String           name;
    /**
     * 形象性别
     */
    private String           gender;
    /**
     * 形象训练文件file
     */
    private List<BizFile>    avatarFile;
    /**
     * 形象文件file
     */
    private BizFile          coverFile;
    /**
     * 形象状态
     */
    private String           status;
    /**
     * 形象宽高比
     */
    private VideoAspectRatio aspectRatio;
    /**
     * 数字人视频宽度（px）
     */
    private Integer          videoWidth;
    /**
     * 数字人视频高度（px）
     */
    private Integer          videoHeight;

    /**
     * 是否属于某用户
     *
     * @param uid 用户ID
     * @return 是否属于
     */
    public boolean belongsTo(long uid) {
        return Objects.equals(this.getUid(), uid);
    }

    @JsonIgnore
    public boolean isSystem() {
        return AppConstants.isSystemUser(this.uid);
    }

    @JsonIgnore
    public boolean isFinished() {
        return StringUtils.containsAny(this.status, SUCCESS, FAILURE);
    }
}


