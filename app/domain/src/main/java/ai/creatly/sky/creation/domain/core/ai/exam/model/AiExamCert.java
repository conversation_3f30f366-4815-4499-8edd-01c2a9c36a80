/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.exam.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 考试证书
 *
 * <AUTHOR>
 * @version AiExamCert.java
 */
@Data
@Accessors(chain = true)
public class AiExamCert {
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String coverUrl;
    private String showUrl;
    private Long courseId;
    private String name;
    private Long totalScore;
    private Long passScore;
    private Long limitExam;
    private String orgCode;
}
