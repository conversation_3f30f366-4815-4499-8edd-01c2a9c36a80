/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.service.impl;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsRefund;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsCalculator;
import ai.creatly.sky.creation.domain.core.story.model.role.StoryRole;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryConstants;
import ai.creatly.sky.creation.domain.core.story.service.StoryCreditsService;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceTimeEstimation;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.VoiceDialogue;
import ai.creatly.sky.creation.domain.core.voice.util.VoiceUtil;
import com.jspeeder.core.data.id.IdHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version StoryCreditsServiceImpl.java, v 0.1 2024-06-21 下午8:11 zhoudong
 */
@Service
@RequiredArgsConstructor
public class StoryCreditsServiceImpl implements StoryCreditsService {

    private final CreditsCalculator creditsCalculator;

    @Override
    public CreditsExpense estimateStoryScriptExpense(long uid) {
        String ruleType = AiTaskType.AI_STORY.name();
        String ruleBizType = CreditLogBizType.STORY_SCRIPT.name();
        int credits = creditsCalculator.calcCreditsByWords(ruleType, ruleBizType, StoryConstants.ESTIMATED_SCRIPT_WORDS);
        return CreditsExpense.builder()
                .uid(uid)
                .type(CreditAccountType.GENERAL)
                .bizType(CreditLogBizType.STORY_SCRIPT)
                .bizNo(String.valueOf(System.currentTimeMillis()))
                .amount(credits)
                .allowNegative(false)
                .build();
    }

    @Override
    public CreditsExpense calcStoryScriptExpense(String content, long uid) {
        String ruleType = AiTaskType.AI_STORY.name();
        String ruleBizType = CreditLogBizType.STORY_SCRIPT.name();
        int credits = creditsCalculator.calcTextCredits(ruleType, ruleBizType, content);
        return CreditsExpense.builder()
                .uid(uid)
                .type(CreditAccountType.GENERAL)
                .bizType(CreditLogBizType.STORY_SCRIPT)
                .bizNo(String.valueOf(System.currentTimeMillis()))
                .amount(credits)
                .allowNegative(false)
                .build();
    }

    @Override
    public CreditsExpense calcStoryRoleInfoInitExpense(long storyId, long uid) {
        String ruleType = AiTaskType.AI_STORY.name();
        String ruleBizType = CreditLogBizType.STORY_ROLE_INFO.name();
        int credits = creditsCalculator.calcCreditsOnce(ruleType, ruleBizType);
        return CreditsExpense.builder()
                .uid(uid)
                .type(CreditAccountType.GENERAL)
                .bizType(CreditLogBizType.STORY_ROLE_INFO)
                .bizNo(String.valueOf(storyId))
                .amount(credits)
                .allowNegative(false)
                .build();
    }

    /**
     * @see AiTaskType#AI_STORY
     * @see ai.creatly.sky.creation.domain.core.story.model.AiStoryTaskBizType#STORY_ROLE_PORTRAIT_INIT
     */
    @Override
    public List<CreditsExpense> calcStoryRoleImageInitExpenses(AiTask aiTask, List<StoryRole> roles) {
        String ruleType = aiTask.getTaskType().name();
        String ruleBizType = aiTask.getBizType();
        int credits = creditsCalculator.calcCreditsOnce(ruleType, ruleBizType);
        return roles.stream()
                .map(role -> CreditsExpense.builder()
                        .uid(aiTask.getOwnerId())
                        .type(CreditAccountType.GENERAL)
                        .bizType(CreditLogBizType.STORY_ROLE_IMAGES_INIT)
                        .bizNo(aiTask.getId() + "-" + role.getId())
                        .amount(credits)
                        .allowNegative(false)
                        .build()
                )
                .collect(toList());
    }

    @Override
    public List<CreditsRefund> buildStoryRoleImageInitRefunds(AiTask aiTask, List<StoryRole> failedRoles) {
        return failedRoles.stream()
                .map(role -> CreditsRefund.builder()
                        .uid(aiTask.getOwnerId())
                        .bizType(CreditLogBizType.STORY_ROLE_IMAGES_INIT)
                        .bizNo(aiTask.getId() + "-" + role.getId())
                        .allowExpenseAbsent(false)
                        .build()
                )
                .collect(toList());
    }

    /**
     * @see AiTaskType#AI_STORY
     * @see ai.creatly.sky.creation.domain.core.story.model.AiStoryTaskBizType#STORY_ROLE_PORTRAIT_GENERATE
     */
    @Override
    public CreditsExpense calcStoryRoleImageExpense(AiTask aiTask) {
        String ruleType = aiTask.getTaskType().name();
        String ruleBizType = aiTask.getBizType();
        int credits = creditsCalculator.calcCreditsOnce(ruleType, ruleBizType);
        return CreditsExpense.builder()
                .uid(aiTask.getOwnerId())
                .type(CreditAccountType.GENERAL)
                .bizType(CreditLogBizType.STORY_ROLE_IMAGE)
                .bizNo(aiTask.getId().toString())
                .amount(credits)
                .allowNegative(false)
                .build();
    }

    @Override
    public CreditsRefund buildStoryRoleImageRefund(AiTask aiTask) {
        return CreditsRefund.builder()
                .uid(aiTask.getOwnerId())
                .bizType(CreditLogBizType.STORY_ROLE_IMAGE)
                .bizNo(aiTask.getId().toString())
                .allowExpenseAbsent(false)
                .build();
    }

    @Override
    public CreditsExpense calcStoryScenesInitExpense(long storyId, long uid) {
        String ruleType = AiTaskType.AI_STORY.name();
        String ruleBizType = CreditLogBizType.STORY_SCENES_INIT.name();
        int credits = creditsCalculator.calcCreditsOnce(ruleType, ruleBizType);
        return CreditsExpense.builder()
                .uid(uid)
                .type(CreditAccountType.GENERAL)
                .bizType(CreditLogBizType.STORY_SCENES_INIT)
                .bizNo(String.valueOf(storyId))
                .amount(credits)
                .allowNegative(false)
                .build();
    }

    /**
     * @see AiTaskType#AI_STORY
     * @see ai.creatly.sky.creation.domain.core.story.model.AiStoryTaskBizType#STORY_SHOT_IMAGE_GENERATE
     */
    @Override
    public CreditsExpense calcStoryShotImageExpense(AiTask aiTask) {
        String ruleType = aiTask.getTaskType().name();
        String ruleBizType = aiTask.getBizType();
        int credits = creditsCalculator.calcCreditsOnce(ruleType, ruleBizType);
        return CreditsExpense.builder()
                .uid(aiTask.getOwnerId())
                .type(CreditAccountType.GENERAL)
                .bizType(CreditLogBizType.STORY_SHOT_IMAGE)
                .bizNo(aiTask.getId().toString())
                .amount(credits)
                .allowNegative(false)
                .build();
    }

    @Override
    public CreditsRefund buildStoryShotImageRefund(AiTask aiTask) {
        return CreditsRefund.builder()
                .uid(aiTask.getOwnerId())
                .bizType(CreditLogBizType.STORY_SHOT_IMAGE)
                .bizNo(aiTask.getId().toString())
                .allowExpenseAbsent(false)
                .build();
    }

    @Override
    public CreditsExpense calcShotSoundRecommendExpense(long storyId, long shotId, long uid) {
        String ruleType = AiTaskType.AI_STORY.name();
        String ruleBizType = CreditLogBizType.STORY_SHOT_SOUND_RECOMMEND.name();
        int credits = creditsCalculator.calcCreditsOnce(ruleType, ruleBizType);
        return CreditsExpense.builder()
                .uid(uid)
                .type(CreditAccountType.GENERAL)
                .bizType(CreditLogBizType.STORY_SHOT_SOUND_RECOMMEND)
                .bizNo(storyId + "-" + shotId + "-" + System.currentTimeMillis())
                .amount(credits)
                .allowNegative(false)
                .build();
    }

    @Override
    public CreditsExpense estimateShotDialogueAudioExpense(VoiceDialogue voiceDialogue, long uid) {
        String ruleType = AiTaskType.AI_STORY.name();
        String ruleBizType = CreditLogBizType.STORY_SHOT_DIALOGUE_AUDIO.name();
        VoiceTimeEstimation timeEstimation = VoiceUtil.estimateVoiceTime(voiceDialogue);
        int credits = creditsCalculator.calcCreditsByDuration(ruleType, ruleBizType, timeEstimation.getAudioDuration());
        return CreditsExpense.builder()
                .uid(uid)
                .type(CreditAccountType.GENERAL)
                .bizType(CreditLogBizType.STORY_SHOT_DIALOGUE_AUDIO)
                .bizNo(IdHelper.getStrId())
                .amount(credits)
                .allowNegative(false)
                .build();
    }

    @Override
    public CreditsExpense calcShotDialogueAudioExpense(UserFile audioFile) {
        String ruleType = AiTaskType.AI_STORY.name();
        String ruleBizType = CreditLogBizType.STORY_SHOT_DIALOGUE_AUDIO.name();
        int credits = creditsCalculator.calcCreditsByDuration(ruleType, ruleBizType, audioFile.getMetadata().getDuration());
        return CreditsExpense.builder()
                .uid(audioFile.getUid())
                .type(CreditAccountType.GENERAL)
                .bizType(CreditLogBizType.STORY_SHOT_DIALOGUE_AUDIO)
                .bizNo(audioFile.getId().toString())
                .amount(credits)
                .allowNegative(false)
                .build();
    }

    /**
     * @see AiTaskType#AI_STORY
     * @see ai.creatly.sky.creation.domain.core.story.model.AiStoryTaskBizType#STORY_SHOT_CAMERA_MOVEMENT
     */
    @Override
    public CreditsExpense calcShotCameraMoveVideoExpense(AiTask aiTask) {
        String ruleType = aiTask.getTaskType().name();
        String ruleBizType = aiTask.getBizType();
        int credits = creditsCalculator.calcCreditsOnce(ruleType, ruleBizType);
        return CreditsExpense.builder()
                .uid(aiTask.getOwnerId())
                .type(CreditAccountType.GENERAL)
                .bizType(CreditLogBizType.STORY_SHOT_CAMERA_MOVE_VIDEO)
                .bizNo(aiTask.getId().toString())
                .amount(credits)
                .allowNegative(false)
                .build();
    }

    @Override
    public CreditsRefund buildShotCameraMoveVideoRefund(AiTask aiTask) {
        return CreditsRefund.builder()
                .uid(aiTask.getOwnerId())
                .bizType(CreditLogBizType.STORY_SHOT_CAMERA_MOVE_VIDEO)
                .bizNo(aiTask.getId().toString())
                .allowExpenseAbsent(false)
                .build();
    }

    /**
     * @see AiTaskType#AI_STORY
     * @see ai.creatly.sky.creation.domain.core.story.model.AiStoryTaskBizType#STORY_SHOT_EFFECT
     */
    @Override
    public CreditsExpense calcShotEffectVideoExpense(AiTask aiTask) {
        String ruleType = aiTask.getTaskType().name();
        String ruleBizType = aiTask.getBizType();
        int credits = creditsCalculator.calcCreditsOnce(ruleType, ruleBizType);
        return CreditsExpense.builder()
                .uid(aiTask.getOwnerId())
                .type(CreditAccountType.GENERAL)
                .bizType(CreditLogBizType.STORY_SHOT_EFFECT_VIDEO)
                .bizNo(aiTask.getId().toString())
                .amount(credits)
                .allowNegative(false)
                .build();
    }

    @Override
    public CreditsRefund buildShotEffectVideoRefund(AiTask aiTask) {
        return CreditsRefund.builder()
                .uid(aiTask.getOwnerId())
                .bizType(CreditLogBizType.STORY_SHOT_EFFECT_VIDEO)
                .bizNo(aiTask.getId().toString())
                .allowExpenseAbsent(false)
                .build();
    }
}
