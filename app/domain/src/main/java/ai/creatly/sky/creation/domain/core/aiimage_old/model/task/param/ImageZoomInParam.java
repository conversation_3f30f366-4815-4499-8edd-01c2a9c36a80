/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.core.aiimage_old.model.task.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Ai图片变动
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImageZoomInParam extends ImageOpsBaseParam {

    /**
     * hd 2k 4k
     */
    private String quality;

}
