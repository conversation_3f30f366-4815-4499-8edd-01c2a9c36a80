/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.task.exec;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizExecInfo;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;

/**
 * 声音演员的语音合成中间产物（即基础语音合成的音频）
 *
 * <AUTHOR>
 * @version MarketVoiceSynthesisExecInfo.java, v 0.1 2023-11-08 下午9:11 zhoudong
 */
@Data
@Accessors(chain = true)
public class MarketVoiceSynthesisExecInfo implements TaskBizExecInfo {

    /**
     * 输入音频文件ID（如果合成失败，则为null）
     */
    @Nullable
    private Long     inputAudioId;
    /**
     * 输入音频文件地址（OSS地址）
     */
    private String   inputAudioUrl;
    /**
     * 输入音频时长
     */
    private Duration inputAudioDuration;
    /**
     * 输入音频合成实际耗时
     */
    private Duration inputAudioSynthesizedCost;
}
