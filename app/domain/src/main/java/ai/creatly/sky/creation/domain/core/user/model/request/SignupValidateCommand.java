/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.model.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

@Data
public class SignupValidateCommand implements Serializable {

    /**
     * 可校验的类型 username/email/phone/inviteCode
     */
    @NotBlank
    private String type;
    /**
     * 待校验的值
     */
    @NotBlank
    private String value;
}
