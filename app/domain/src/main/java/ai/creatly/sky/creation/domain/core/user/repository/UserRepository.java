/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.repository;

import ai.creatly.sky.creation.domain.core.user.model.UserInfo;
import ai.creatly.sky.creation.domain.core.user.model.organization.UserOrganization;
import ai.creatly.sky.creation.domain.core.user.service.UserSignInWay;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version UserRepository.java, v 0.1 2023-06-03 11:32
 */
public interface UserRepository {

    void saveOrUpdate(UserInfo userInfo);
    /**
     * 用户注册（密码需进行加密存储） TODO 用户的密码加密和验证的地方，应该内聚到一个地方，不要分散！
     *
     * @param userInfo -
     * @return 用户ID
     */
    long create(UserInfo userInfo);

    /**
     * 用户更新
     *
     * @param userInfo -
     */
    void updateById(UserInfo userInfo);

    default UserInfo queryById(long uid) {
        return queryOptionalById(uid).orElse(null);
    }

    Page<UserInfo> queryPage(Pageable pageable);

    @Nullable
    UserInfo lockById(long uid);

    /**
     * 通过用户id查询用户信息
     *
     * @param uid 用户id
     * @return 用户信息
     */
    Optional<UserInfo> queryOptionalById(long uid);

    /**
     * 通过多条件查询（用户名/电话/邮箱）查询用户信息
     *
     * @param key 用户名
     * @return -
     */
    Optional<UserInfo> queryOptionalByUsernameWithSensitive(String key, UserSignInWay signWay);

    /**
     * 通过手机号查询用户信息
     *
     * @param phone -
     * @return -
     */
    Optional<UserInfo> queryOptionalByPhone(String phone);

    /**
     * 通过微信的unionId查询用户
     *
     * @param unionId 微信开发平台下的唯一标识
     */
    Optional<UserInfo> queryOptionalByWechatUnionId(String unionId);

    /**
     * 更新用户所属组织
     *
     * @param uid           -
     * @param organizations -
     */
    void updateUserOrganizations(long uid, List<UserOrganization> organizations);

    void updateUserOrganizations(long uid, List<UserOrganization> organizations, String orgCode);

    void updateLastOrganization(long uid, String orgCode);
    /**
     * 查询用户所属组织
     *
     * @param uid -
     * @return -
     */
    List<UserOrganization> getUserOrganizations(long uid);

    Optional<UserInfo> queryOptionalByInviteCode(String inviteCode);

    Optional<UserInfo> getUserByPassword(String userName, String password);

    Optional<UserInfo> getUserById(long uid);
}

