/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.service;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizInput;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskService;
import ai.creatly.sky.creation.domain.core.drama.model.Drama;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaAction;
import ai.creatly.sky.creation.domain.core.drama.task.DramaTaskBizType;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalBizType;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalTask;
import ai.creatly.sky.creation.domain.support.approval.task.ApprovalListener;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * <AUTHOR>
 * @version DramaApprovalListener.java, 2024-10-29 下午3:09 zhoudong
 */
@Component
@RequiredArgsConstructor
public class DramaApprovalListener implements ApprovalListener {

    private final DramaRepository     dramaRepository;
    private final AiTaskService       aiTaskService;
    private final TransactionTemplate transactionTemplate;

    @Override
    public boolean support(ApprovalBizType bizType) {
        return ApprovalBizType.drama_review == bizType;
    }

    @Override
    public void onApproved(ApprovalTask task) {
        long dramaId = Long.parseLong(task.getBizNo());

        transactionTemplate.executeWithoutResult(status -> {
            dramaRepository.action(dramaId, DramaAction.approve, null);
            // 剧情预处理任务
            AiTask aiTask = AiTask.buildNew()
                    .owner(task.getApplicantId(), task.getApplicantName())
                    .creator(task.getCreator())
                    .taskType(AiTaskType.DRAMA)
                    .bizType(DramaTaskBizType.preprocess)
                    .bizNo(dramaId)
                    .subBizNo(StringUtils.EMPTY)
                    .bizInput(TaskBizInput.EMPTY)
                    .notAutoExec()
                    .build();
            aiTaskService.submit(aiTask);
        });
    }

    @Override
    public void onRejected(ApprovalTask task) {
        long dramaId = Long.parseLong(task.getBizNo());
        Drama drama = dramaRepository.queryById(dramaId);
        dramaRepository.action(dramaId, DramaAction.reject, drama.getReview().setRejectReason(task.getRejectReason()));
    }

    @Override
    public void onCompleted(ApprovalTask task) {
        // TODO 微信通知创作者
    }
}
