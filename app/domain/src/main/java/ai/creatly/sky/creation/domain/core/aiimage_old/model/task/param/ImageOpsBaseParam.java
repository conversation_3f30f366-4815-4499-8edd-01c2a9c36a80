/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.core.aiimage_old.model.task.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Ai图片变动
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImageOpsBaseParam extends ImageImagineParam {

    /**
     * 主任务 aiTaskId
     */
    protected String mainTaskId;

    /**
     * 主任务 midJourneyId
     */
    protected String mainMjTaskId;

    /**
     * 目标操作图下标[1~4]
     */
    private Integer index;
}
