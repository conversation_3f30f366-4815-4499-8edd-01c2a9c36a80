/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.task.expand;

import ai.creatly.sky.creation.domain.core.ai.image.client.sync.AiImageSingleClient;
import ai.creatly.sky.creation.domain.core.ai.image.mapper.ImageExpansionMapper;
import ai.creatly.sky.creation.domain.core.ai.image.task.AiImageTaskBizType;
import ai.creatly.sky.creation.domain.core.ai.image.task.common.AbstractAiImageSyncTaskHandler;
import ai.creatly.sky.creation.domain.core.ai.image.task.expand.model.ImageExpandTaskInput;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.asset.model.Asset;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;

/**
 *
 * <AUTHOR>
 * @version ImageExpandSingleTaskHandler.java, v0.1 2025-02-28 13:41
 */
//@Component
@RequiredArgsConstructor
public class ImageExpandSingleTaskHandler extends AbstractAiImageSyncTaskHandler implements AiTaskHandler {

    private final AiImageSingleClient  aiImageClient;
    private final ImageExpansionMapper imageExpansionMapper;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.AI_IMAGE)
                .setBizType(AiImageTaskBizType.single_image_expand)
                .setLoadSize(1)
                .setQueueMode(TaskQueueMode.POP_PER_EXECUTED)
                .setNotifyOnSubmit(false)
                .setNotifyUserOnCompleted(false);
    }

    @Nullable
    @Override
    protected UserFile generateImage(AiTask aiTask, UserContext userContext) {
        var input = aiTask.parseBizInput(ImageExpandTaskInput.class);
        return aiImageClient.expandImage(input, userContext);
    }

    @Override
    protected Asset convertToAsset(AiTask aiTask, UserFile imageFile) {
        var input = aiTask.parseBizInput(ImageExpandTaskInput.class);
        String coverUrl = imageFileService.generateThumbnailUrl(imageFile, 50);
        return imageExpansionMapper.buildAsset(input, imageFile, coverUrl);
    }
}
