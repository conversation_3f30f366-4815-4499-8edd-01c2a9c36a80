/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.multimedia.service;

import ai.creatly.sky.creation.domain.support.multimedia.model.AudioSubtitle;
import ai.creatly.sky.creation.domain.support.multimedia.model.SynthesisMark;
import ai.creatly.sky.creation.domain.support.multimedia.model.TimedText;
import ai.creatly.sky.creation.domain.support.multimedia.model.TtsSubtitleText;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * @version SubtitleService.java, v 0.1 2024-08-15 下午3:08 zhoudong
 */
@Validated
public interface SubtitleService {

    /**
     * 生成多条字幕文本（从原始文本切分而来）
     *
     * @param dialogueIndex 对话索引
     * @param text          原始文本（可包含标点和不发音的特殊字符）
     * @param subtitle      字幕配置
     * @return 字幕文本列表
     */
    default List<TtsSubtitleText> genSubtitleTexts(int dialogueIndex, String text, @Valid AudioSubtitle subtitle) {
        return this.genSubtitleTexts(dialogueIndex, text, subtitle, null);
    }

    /**
     * 生成多条字幕文本（从原始文本切分而来）
     *
     * @param dialogueIndex 对话索引
     * @param text          原始文本（可包含标点和不发音的特殊字符）
     * @param subtitle      字幕配置
     * @param keywords      关键词（不能被分词）
     * @return 字幕文本列表
     */
    List<TtsSubtitleText> genSubtitleTexts(int dialogueIndex, String text, @Valid AudioSubtitle subtitle, List<String> keywords);

    /**
     * 将分段文本转换为字幕列表
     *
     * @param subtitleTexts  字幕文本列表（无时间轴信息）
     * @param synthesisMarks 语音合成标记（字幕使用尾插标记法，比如 TextA | TextB | TextC |）
     * @param duration       总时长（注意⚠️：合成标记的最后一个标记可能会缺失，所以需要通过总时长进行容错处理）
     * @return 字幕列表
     */
    List<TimedText> toSubtitles(List<TtsSubtitleText> subtitleTexts, List<SynthesisMark> synthesisMarks, Duration duration);
}
