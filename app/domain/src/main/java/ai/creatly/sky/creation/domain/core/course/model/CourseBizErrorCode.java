/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.course.model;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 用户异常
 *
 * <AUTHOR>
 * @version UserErrorCode.java, v 0.1 2023-05-27 17:50 syoka
 */
@Getter
@RequiredArgsConstructor
public enum CourseBizErrorCode implements ErrorCode {

    ORG_COURSE_EXIST("机构已开通课程，不允许充分开课");

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
