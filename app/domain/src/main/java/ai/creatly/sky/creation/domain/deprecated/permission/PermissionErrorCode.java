/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.deprecated.permission;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version : PermissionErrorCode.java, v 1.0 2023年07月21日 17时42分 syoka Exp$
 */
@Getter
@AllArgsConstructor
public enum PermissionErrorCode implements ErrorCode {

    NO_PERMISSION("当前无权限操作"),
    DUPLICATE_PERMISSION_APPLY("重复的权限申请"),
    USER_HAS_NO_INVITE_QRCODE_PERMISSION("用户无权限操作"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
