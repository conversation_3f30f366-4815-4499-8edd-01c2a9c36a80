/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.video.service;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.ai.video.model.AiVideoTaskResult;
import ai.creatly.sky.creation.domain.core.ai.video.model.OrderedVideoAsset;
import ai.creatly.sky.creation.domain.core.ai.video.model.VideoGenerateTaskInput;
import ai.creatly.sky.creation.domain.core.ai.video.model.VideoGenerationVM;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTaskQO;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version ImageGenerationService.java, v0.1 2025-02-19 21:39
 */
@Service
@RequiredArgsConstructor
public class VideoGenerationService {

    private final AiTaskRepository aiTaskRepository;
    private final UserFileHelper   userFileHelper;

    public Page<VideoGenerationVM> queryGenerationPage(long uid, Pageable pageable) {
        AiTaskQO qo = new AiTaskQO()
                .setTaskType(AiTaskType.AI_VIDEO)
                .setOwnerId(uid);
        Page<AiTask> page = aiTaskRepository.queryPage(qo, pageable);

        return page.map(aiTask -> {
            var taskInput = aiTask.parseBizInput(VideoGenerateTaskInput.class);
            var taskResult = aiTask.parseBizResult(AiVideoTaskResult.class);
            VideoGenerationVM videoGenerationVM = new VideoGenerationVM();
            if (taskResult.getAssets() != null) {
                List<OrderedVideoAsset> assets = taskResult.getAssets();
                for (int i = 0; i < assets.size(); i++) {
                    assets.get(i).setVideoUrl(userFileHelper.getHttpUrl(assets.get(i).getVideoUrl(), FileAcl.PRIVATE));
                }
                videoGenerationVM.setAssets(taskResult.getAssets());
            }
            if (aiTask.getBizResult()!=null&&aiTask.getBizResult().has("errorMsg")) {
                videoGenerationVM.setErrorMsg(aiTask.getBizResult().getString("errorMsg"));
            }
            videoGenerationVM.setStatus(aiTask.getStatus());
            videoGenerationVM.setPromptText(taskInput.getRequest().getPromptText());
            videoGenerationVM.setTaskId(aiTask.getId().toString());
            return videoGenerationVM;
        });
    }

    public VideoGenerationVM queryGenerationById(long uid, long taskId) {

        AiTask aiTask = aiTaskRepository.queryById(taskId);
        var taskInput = aiTask.parseBizInput(VideoGenerateTaskInput.class);
        var taskResult = aiTask.parseBizResult(AiVideoTaskResult.class);
        VideoGenerationVM videoGenerationVM = new VideoGenerationVM();
        if (taskResult.getAssets() != null) {
            List<OrderedVideoAsset> assets = taskResult.getAssets();
            for (int i = 0; i < assets.size(); i++) {
                assets.get(i).setVideoUrl(userFileHelper.getHttpUrl(assets.get(i).getVideoUrl(), FileAcl.PRIVATE));
            }
            videoGenerationVM.setAssets(taskResult.getAssets());
        }
        if (aiTask.getBizResult()!=null&&aiTask.getBizResult().has("errorMsg")) {
            videoGenerationVM.setErrorMsg(aiTask.getBizResult().getString("errorMsg"));
        }
        videoGenerationVM.setStatus(aiTask.getStatus());
        videoGenerationVM.setPromptText(taskInput.getRequest().getPromptText());
        videoGenerationVM.setTaskId(aiTask.getId().toString());
        return videoGenerationVM;
    }


}
