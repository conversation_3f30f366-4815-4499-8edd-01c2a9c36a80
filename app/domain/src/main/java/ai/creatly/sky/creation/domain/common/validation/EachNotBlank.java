/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @version EachNotBlank.java, v 0.1 2024-10-18 上午12:54 zhoudong
 */
@Documented
@Constraint(validatedBy = EachNotBlankValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface EachNotBlank {

    String message() default "Each element must not be blank";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
