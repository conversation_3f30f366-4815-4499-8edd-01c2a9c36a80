/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.shot;

import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * 分镜文件素材
 *
 * <AUTHOR>
 * @version ShotAsset.java, v 0.1 2024-04-10 16:23 heb
 */
@Data
@Accessors(chain = true)
public class ShotAsset {
    /**
     * 分镜图片
     */
    @Nullable
    private ShotStatic  shotStatic;
    /**
     * 分镜视频
     */
    @Nullable
    private ShotDynamic shotDynamic;
}
