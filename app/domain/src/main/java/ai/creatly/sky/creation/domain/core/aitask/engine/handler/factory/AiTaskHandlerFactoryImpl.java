/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.engine.handler.factory;

import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskHandlerContainer;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.*;

import static java.util.stream.Collectors.toSet;

/**
 * <AUTHOR>
 * @version AiTaskHandlerFactoryImpl.java, v 0.1 2023-07-29 00:38 joton
 */
@Component
@RequiredArgsConstructor
public class AiTaskHandlerFactoryImpl implements AiTaskHandlerFactory, ApplicationListener<ApplicationReadyEvent> {

    private static final String CACHE_KEY_PREFIX = AppConstants.APP_NAME + ":ai_task:";

    private final Map<String, AiTaskHandlerContainer> taskHandlerContainerMap = new HashMap<>();

    @Override
    public void onApplicationEvent(@NotNull ApplicationReadyEvent event) {
        event.getApplicationContext().getBeansOfType(AiTaskHandler.class).values().forEach(taskHandler -> {
            AiTaskConfig taskConfig = taskHandler.config();
            this.check(taskConfig);
            AiTaskHandlerContainer container = new AiTaskHandlerContainer(taskConfig, taskHandler);
            taskHandlerContainerMap.put(this.toCacheKey(taskConfig), container);
        });
    }

    private void check(AiTaskConfig taskConfig) {
        Asserts.isTrue(taskConfig.getLoadSize() <= 200, "任务的loadSize暂时不支持大于200");
    }

    @Override
    public Set<AiTaskConfig> getAllTaskConfigs() {
        List<AiTaskHandlerContainer> containers = new ArrayList<>(taskHandlerContainerMap.values());
        return containers.stream().map(AiTaskHandlerContainer::taskConfig).collect(toSet());
    }

    @Override
    public AiTaskConfig getTaskConfig(AiTaskType taskType, String bizType) {
        AiTaskHandlerContainer container = this.getTaskHandlerContainer(taskType, bizType);
        return container.taskConfig();
    }

    @Nullable
    @Override
    public AiTaskHandler getHandler(AiTaskType taskType, String bizType) {
        AiTaskHandlerContainer container = this.findTaskHandlerContainer(taskType, bizType);
        return container == null ? null : container.taskHandler();
    }

    @Nullable
    private AiTaskHandlerContainer findTaskHandlerContainer(AiTaskType taskType, String bizType) {
        AiTaskHandlerContainer container = taskHandlerContainerMap.get(this.toCacheKey(taskType, bizType));
        if (Objects.isNull(container)) {
            container = taskHandlerContainerMap.get(this.toCacheKey(taskType, null));
        }
        return container;
    }

    private AiTaskHandlerContainer getTaskHandlerContainer(AiTaskType taskType, String bizType) {
        AiTaskHandlerContainer container = this.findTaskHandlerContainer(taskType, bizType);
        Asserts.notNull(container, "task handler not exist!");
        return container;
    }

    /**
     * 可缓存的key，全局唯一
     *
     * @return 可缓存的key
     */
    private String toCacheKey(AiTaskConfig taskConfig) {
        return this.toCacheKey(taskConfig.getTaskType(), taskConfig.getBizType());
    }

    /**
     * 可缓存的key，全局唯一
     *
     * @return 可缓存的key
     */
    private String toCacheKey(AiTaskType taskType, @Nullable String bizType) {
        if (bizType == null) {
            return CACHE_KEY_PREFIX + taskType;
        } else {
            return CACHE_KEY_PREFIX + taskType + ":" + bizType;
        }
    }
}
