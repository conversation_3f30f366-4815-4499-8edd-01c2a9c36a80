/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward;

import ai.creatly.sky.creation.domain.core.aitask.model.UpdatableAiTask;
import com.jspeeder.core.util.Asserts;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @version TaskStillActionImpl.java, v 0.1 2024-08-24 下午1:46 zhoudong
 */
@Getter
class TaskStillActionImpl extends TaskActionImpl implements TaskStillAction {

    private String retryName;
    private String retryReason;

    TaskStillActionImpl(@Nullable UpdatableAiTask updatableAiTask) {
        super(ActionTypeEnum.KEEP_STILL, updatableAiTask);
    }

    @Override
    public TaskAction withAiTask(@NotNull UpdatableAiTask updatableAiTask) {
        this.checkNotNull(updatableAiTask);
        TaskStillActionImpl taskStillAction = new TaskStillActionImpl(updatableAiTask);
        taskStillAction.retryName = retryName;
        taskStillAction.retryReason = retryReason;
        return taskStillAction;
    }

    @Override
    public TaskStillAction withRetry(String name, String reason, @Nullable UpdatableAiTask updatableAiTask) {
        this.checkNullable(updatableAiTask);
        Asserts.notBlank(name, "retry name must not be blank");
        Asserts.notBlank(reason, "retry reason must not be blank");
        TaskStillActionImpl taskAction = new TaskStillActionImpl(updatableAiTask);
        taskAction.retryName = name;
        taskAction.retryReason = reason;
        return taskAction;
    }
}
