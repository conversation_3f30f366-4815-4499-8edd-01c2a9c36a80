package ai.creatly.sky.creation.domain.support.aichat.model.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 回复文本（其中一段）
 *
 * <AUTHOR>
 * @version ReplyTextChunk.java, v 0.1 2023-05-07 15:35 joton
 */
@Data
@Accessors(chain = true)
public class ReplyTextChunk {

    /**
     * 回复标识（用于标识属于同一次回复里的文本）
     */
    private String id;
    /**
     * 是否第一条
     */
    private Boolean first;
    /**
     * 是否最后一条
     */
    private Boolean last;
    /**
     * 是否截断
     */
    private Boolean broken;
    /**
     * 文本
     */
    private String text;
}
