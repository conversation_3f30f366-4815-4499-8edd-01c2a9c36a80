/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.task;

import ai.creatly.sky.creation.domain.common.env.RuntimeEnv;
import ai.creatly.sky.creation.domain.common.http.RequestForwardClient;
import ai.creatly.sky.creation.domain.common.util.LocalFileUtil;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.drama.client.DramaPreprocessClient;
import ai.creatly.sky.creation.domain.core.drama.client.model.DramaPreprocessResult;
import ai.creatly.sky.creation.domain.core.drama.model.Drama;
import ai.creatly.sky.creation.domain.core.drama.model.DramaRole;
import ai.creatly.sky.creation.domain.core.drama.model.DramaVideo;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaLocalSyncStatus;
import ai.creatly.sky.creation.domain.core.drama.service.DramaRepository;
import ai.creatly.sky.creation.domain.core.userfile.error.FileErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.support.file.model.request.LocalFileSyncFromOssRequest;
import ai.creatly.sky.creation.domain.support.file.model.request.LocalFileSyncFromTextRequest;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.data.result.ApiVoidResult;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.json.JSON;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @version DramaPreprocessTaskHandler.java, 2024-10-29 下午3:47 zhoudong
 */
//@Component
@RequiredArgsConstructor
@Slf4j
public class DramaPreprocessTaskHandler implements AiTaskHandler {

    private final DramaRepository         dramaRepository;
    private final UserFileRepository      userFileRepository;
    private final UserFileService         userFileService;
    private final DramaFileSyncProperties dramaFileSyncProperties;
    private final DramaPreprocessClient   dramaPreprocessClient;
    private final RuntimeEnv              runtimeEnv;
    private final RequestForwardClient    requestForwardClient;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.DRAMA)
                .setBizType(DramaTaskBizType.preprocess)
                .setLoadSize(1)
                .setQueueMode(TaskQueueMode.POP_UNTIL_COMPLETED)
                .setExecCountAlertThreshold(60)
                .setNotifyOnSubmit(false)
                .setNotifyUserOnCompleted(false);
    }

    @Override
    public TaskPreAction preHandle(AiTask aiTask) {
        long dramaId = Long.parseLong(aiTask.getBizNo());
        Drama drama = dramaRepository.queryById(dramaId);
        if (drama.getLocalSyncStatus() == DramaLocalSyncStatus.synced) {
            // 直接结束任务，跳过预处理
            return TaskPreAction.FORWARD_FINISHED;
        }

        // 重复剧情校验，重复的剧情不需要再同步了
        Drama existingDrama = dramaRepository.querySyncedByContentMd5(drama.getContent().getMd5()).orElse(null);
        if (existingDrama != null && existingDrama.getId() != dramaId) {
            log.warn("相同的剧情文件已存在，不需要再写入本地文件");
            dramaRepository.updateById(new Drama()
                    .setId(dramaId)
                    .setRoles(existingDrama.getRoles())
                    .setContent(existingDrama.getContent())
                    .setLocalSyncStatus(DramaLocalSyncStatus.synced)
            );
            // 直接结束任务，跳过预处理
            return TaskPreAction.FORWARD_FINISHED;
        }

        // 同步到本地剧情库
        this.syncToLocal(drama);
        return TaskPreAction.FORWARD_RUNNING;
    }

    private void syncToLocal(Drama drama) {
        for (int i = 0; i < drama.getVideos().size(); i++) {
            DramaVideo video = drama.getVideos().get(i);
            UserFile dramaVideoFile = userFileRepository.queryById(video.getFileId());
            if (runtimeEnv.isProd()) {
                this.syncVideoFileToLocal(drama.getId(), i, dramaVideoFile);
            } else {
                this.downloadVideoFileToLocal(drama.getId(), i, dramaVideoFile);
            }
        }
    }

    private void syncVideoFileToLocal(long dramaId, int videoIndex, UserFile dramaVideoFile) {
        String endpoint = dramaFileSyncProperties.getOfflineForwardEndpoint();
        String path = "/dev/internal/api/file-sync";
        String query = FormatUtil.format("from=oss&to=local");
        String ossUrl = UserFileHelper.toOssUrl(dramaVideoFile);
        String filepath = this.getLocalFilepath(dramaId, videoIndex, dramaVideoFile);
        LocalFileSyncFromOssRequest request = new LocalFileSyncFromOssRequest()
                .setOssUrl(ossUrl)
                .setFilepath(filepath);
        log.info("线上环境同步剧情文件到本地剧情库，转发请求到内网，endpoint={},path={},query={},request={}", endpoint, path, query, request);
        String response = requestForwardClient.postJsonBody(endpoint, path, query, request);
        Asserts.notBlank(response, "剧情文件同步失败");
        ApiVoidResult result = JSON.parseObject(response, ApiVoidResult.class);
        Asserts.isTrue(result.isSuccess(), "剧情文件同步失败,result={}", result);
    }

    private void downloadVideoFileToLocal(long dramaId, int videoIndex, UserFile dramaVideoFile) {
        Path localFile = Paths.get(this.getLocalFilepath(dramaId, videoIndex, dramaVideoFile));
        if (Files.exists(localFile)) {
            return;
        }
        try (InputStream inputStream = userFileService.download(dramaVideoFile)) {
            LocalFileUtil.saveFile(inputStream, localFile);
        } catch (IOException e) {
            log.error("剧情视频文件下载失败", e);
            throw new SysException(FileErrorCode.FILE_DOWNLOAD_ERROR, "剧情视频", e);
        }
    }

    private String getLocalFilepath(long dramaId, int index, UserFile userFile) {
        String ext = userFile.getExtension();
        return FormatUtil.format("{}/{}/video_{}.{}", dramaFileSyncProperties.getLocalDir(), dramaId, index, ext);
    }

    @Override
    public TaskAction handle(AiTask aiTask) {
        long dramaId = Long.parseLong(aiTask.getBizNo());
        Drama drama = dramaRepository.queryById(dramaId);

        if (!StringUtils.hasText(drama.getContent().getDescription())) {
            DramaPreprocessResult result = dramaPreprocessClient.preprocess(drama);
            drama.getContent().setDescription(result.getContent());
            // TODO 暂时写死一个代言人角色
            drama.getRoles().add(new DramaRole().setName("1").setIsSpokesperson(true));
            dramaRepository.updateById(new Drama()
                    .setId(dramaId)
                    .setRoles(drama.getRoles())
                    .setContent(drama.getContent())
            );
        }

        // 同步写入剧情内容标签到本地剧情库
        this.writeDescriptionToLocalFile(drama);

        // 标记为已同步
        dramaRepository.updateById(new Drama().setId(dramaId).setLocalSyncStatus(DramaLocalSyncStatus.synced));
        return TaskAction.FORWARD_FINISHED;
    }

    private void writeDescriptionToLocalFile(Drama drama) {
        String description = drama.getContent().getDescription();
        String filepath = FormatUtil.format("{}/{}/description.json", dramaFileSyncProperties.getLocalDir(), drama.getId());
        if (runtimeEnv.isProd()) {
            String endpoint = dramaFileSyncProperties.getOfflineForwardEndpoint();
            String path = "/dev/internal/api/file-sync";
            String query = FormatUtil.format("from=text&to=local");
            LocalFileSyncFromTextRequest request = new LocalFileSyncFromTextRequest()
                    .setText(description)
                    .setFilepath(filepath);
            log.info("线上环境同步剧情打标文件到本地剧情库，转发请求到内网，endpoint={},path={},query={},request={}", endpoint, path, query, request);
            String response = requestForwardClient.postJsonBody(endpoint, path, query, request);
            Asserts.notBlank(response, "剧情文件同步失败");
            ApiVoidResult result = JSON.parseObject(response, ApiVoidResult.class);
            Asserts.isTrue(result.isSuccess(), "剧情打标文件同步失败,result={}", result);
        } else {
            Path path = Paths.get(filepath);
            if (!Files.exists(path)) {
                try (InputStream inputStream = IOUtils.toInputStream(description, StandardCharsets.UTF_8)) {
                    LocalFileUtil.saveFile(inputStream, path);
                } catch (IOException e) {
                    log.error("剧情打标文件写入失败", e);
                    throw new SysException(FileErrorCode.FILE_WRITE_ERROR, e);
                }
            }
        }
    }

    @Override
    public TaskPostAction postHandle(AiTask aiTask) {
        return TaskPostAction.COMPLETE;
    }
}
