/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.model;

import ai.creatly.sky.creation.domain.core.ai.image.model.enums.ImageAspectRatio;
import ai.creatly.sky.creation.domain.core.userfile.model.FileRef;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 提示词生图结果
 *
 * <AUTHOR>
 * @version ImageGeneration.java, v0.1 2025-02-19 19:59
 */
@Data
@Accessors(chain = true)
public class ImageGeneration extends AiImageTask {
    private String           promptText;
    private List<FileRef>    referImageFiles;
    private ImageAspectRatio aspectRatio;
}
