/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version FileErrorCode.java, v 0.1 2023-06-23 12:37 joton
 */
@Getter
@RequiredArgsConstructor
public enum FileErrorCode implements ErrorCode {

    FILE_UPLOAD_ERROR("{}文件上传失败"),
    FILE_DOWNLOAD_ERROR("{}文件下载失败"),
    FILE_READ_ERROR("文件解析失败"),
    FILE_NOT_EXISTS("文件不存在"),
    FILE_STORAGE_NOT_EXISTS("物理文件不存在"),
    FILE_TYPE_UNSUPPORTED("该业务来源：{}，不支持这种文件类型：{}"),
    FILE_FORMAT_UNSUPPORTED("文件格式不支持，仅支持：{}"),
    IMAGE_FORMAT_UNSUPPORTED("图片格式不支持，仅支持：{}"),
    AUDIO_FORMAT_UNSUPPORTED("音频格式不支持，仅支持：{}"),
    VIDEO_FORMAT_UNSUPPORTED("视频格式不支持，仅支持：{}"),
    FILE_TYPE_NOT_SUPPORT("文件类型不支持"),
    FILE_WRITE_ERROR("文件写入失败"),

    FILE_UPLOAD_BAD_IMAGE("上传失败，原图已损坏，请更换一张图片再次尝试"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
