/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;

/**
 * 视频脚本
 *
 * <AUTHOR>
 * @version VideoScript.java, v 0.1 2024-09-21 下午4:04 zhoudong
 */
@Data
@Accessors(chain = true)
public class VideoScript {

    /**
     * 视频带货产品
     * <br/>
     * 注意⚠️：只在项目关联产品的那一刻，才去校验原产品状态，一旦关联后，就是一份产品副本了。在项目后续操作中，原产品如果失效了，不会影响这份副本，但可在产品层做提示。
     */
    @Nullable
    private VideoProduct product;
    /**
     * 视频期望时长（默认30s）
     */
    private Duration     expectedDuration;
    /**
     * 视频文案提示词
     */
    @Nullable
    private String       prompt;
    /**
     * 视频文案
     */
    @Nullable
    private String       text;

    @JsonIgnore
    public Integer getExpectedSeconds() {
        if (expectedDuration == null) {
            return null;
        }
        return (int) expectedDuration.getSeconds();
    }
}
