/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.support.prompt.repository;

import ai.creatly.sky.creation.domain.support.prompt.model.admin.category.request.AdminPromptCategoryCreateRequest;
import ai.creatly.sky.creation.domain.support.prompt.model.category.PromptCategory;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;

/**
 * <AUTHOR>
 * @version PromptCategoryRepository.java, v 0.1 2023-10-28 11:34 syoka
 */
public interface PromptCategoryRepository {

    Page<PromptCategory> findPageablePromptCategory(Pageable pageable, String bizSource, String code, String name);

    void createPromptCategory(AdminPromptCategoryCreateRequest request);
}
