/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.videorender.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @version VideoRenderResult.java, v 0.1 2024-08-11 下午12:21 zhoudong
 */
@Getter
@With
@AllArgsConstructor
@RequiredArgsConstructor
public class VideoRenderResult {

    public static final VideoRenderResult FAILED   = new VideoRenderResult("FAILED", "未知异常");
    public static final VideoRenderResult FINISHED = new VideoRenderResult("FINISHED", "");
    public static final VideoRenderResult WAITING  = new VideoRenderResult("WAITING", "");

    private final String status;
    private final String message;
    /**
     * 视频文件大小
     */
    @Nullable
    private       Long   bytes;

    public boolean isFailed() {
        return "FAILED".equals(status);
    }

    public boolean isFinished() {
        return "FINISHED".equals(status);
    }

    public boolean isWaiting() {
        return "WAITING".equals(status);
    }
}
