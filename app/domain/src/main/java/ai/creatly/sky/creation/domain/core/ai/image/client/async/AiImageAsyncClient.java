/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.client.async;

import ai.creatly.sky.creation.domain.core.ai.image.task.expand.model.ImageExpandTaskInput;
import ai.creatly.sky.creation.domain.core.ai.image.task.generate.model.ImageGenerateTaskInput;
import ai.creatly.sky.creation.domain.core.ai.image.task.repaint.model.ImageRepaintTaskInput;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;

import java.util.List;

/**
 * AI图像服务（异步生成多图）
 *
 * <AUTHOR>
 * @version AiImageAsyncClient.java, v0.1 2025-02-28 11:46
 */
public interface AiImageAsyncClient {

    /**
     * 文生图/图生图
     *
     * @param input 输入
     * @param bizNo 唯一业务编号
     * @return 任务ID
     */
    String batchGenerate(ImageGenerateTaskInput input, String bizNo);

    List<UserFile> queryImageGenerations(String taskId, ImageGenerateTaskInput input, UserContext userContext);

    /**
     * 扩图
     *
     * @param input 输入
     * @param bizNo 唯一业务编号
     * @return 任务ID
     */
    default String batchExpand(ImageExpandTaskInput input, String bizNo){
        throw new UnsupportedOperationException("Not supported yet.");
    }

    default List<UserFile> queryExpandGenerations(String taskId, ImageExpandTaskInput input, UserContext userContext){
        throw new UnsupportedOperationException("Not supported yet.");
    }

    /**
     * 局部修改
     *
     * @param input 输入
     * @param bizNo 唯一业务编号
     * @return 任务ID
     */
    default String batchRepaint(ImageRepaintTaskInput input, String bizNo){
        throw new UnsupportedOperationException("Not supported yet.");
    }

    default List<UserFile> queryRepaintGenerations(String taskId, ImageRepaintTaskInput input, UserContext userContext){
        throw new UnsupportedOperationException("Not supported yet.");
    }
}
