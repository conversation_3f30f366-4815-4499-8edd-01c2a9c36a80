/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.image.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version ProductImageCategory.java, v 0.1 2024-02-28 下午4:31 zhoudong
 */
@Data
@Accessors(chain = true)
public class ProductImageCategory {

    /**
     * 商品分类
     */
    @JsonProperty("product_type")
    private String  productType;
    /**
     * 算法模型编号
     */
    private Integer number;
}
