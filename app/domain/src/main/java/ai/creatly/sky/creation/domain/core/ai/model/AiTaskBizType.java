/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.model;

import com.jspeeder.core.data.enums.ICode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 *
 * <AUTHOR>
 * @version AiTaskBizType.java, v0.1 2025-02-24 10:21
 */
@Getter
@RequiredArgsConstructor
public enum AiTaskBizType implements ICode {

    image_text_2_image("文生图"),
    image_common_generate("通用生图任务"),
    image_kling_generate("可灵生图任务"),
    image_liblib_generate("liblib生图任务"),
    image_volc_generate("火山生图任务"),
    image_cloth_try("AI试衣"),
    image_image_2_image("参考生图"),
    audio_clone("声音克隆"),
    audio_tts("文本转语音"),
    audio_music("AI音乐"),
    video_image_2_video("图生视频"),
    video_text_2_video("文生视频"),
    video_effects_video("创意特效"),
    video_lip_sync_video("口型驱动"),
    video_digital_person_video("数字人视频")
    ;

    private final String desc;

    @Override
    public String getCode() {
        return this.name();
    }
}
