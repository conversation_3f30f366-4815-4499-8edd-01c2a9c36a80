/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.task.exec;

import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import lombok.AccessLevel;
import lombok.Data;
import lombok.RequiredArgsConstructor;

/**
 * 基础语音合成结果
 *
 * <AUTHOR>
 * @version BasicVoiceSynthesisResult.java, v 0.1 2024-06-29 上午11:50 zhoudong
 */
@Data
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
public class BasicVoiceSynthesisResult {

    private final boolean  success;
    private final UserFile audioFile;
    private final String   cancelReason;

    public static BasicVoiceSynthesisResult success(UserFile audioFile) {
        return new BasicVoiceSynthesisResult(true, audioFile, null);
    }

    public static BasicVoiceSynthesisResult cancel(String cancelReason) {
        return new BasicVoiceSynthesisResult(false, null, cancelReason);
    }
}
