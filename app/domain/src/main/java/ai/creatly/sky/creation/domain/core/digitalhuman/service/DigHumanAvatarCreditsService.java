/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.service;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsRefund;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsService;

/**
 * <AUTHOR>
 * @version DigHumanCreditsService.java, v 0.1 2024-05-29 14:36 syoka
 */
public interface DigHumanAvatarCreditsService extends CreditsService {

    /**
     * 数字人形象训练 计费
     *
     * @return -
     */
    CreditsExpense calcBasicExpense(AiTask aiTask);

    /**
     * 数字人形象 退款
     *
     * @param aiTask  ai任务类型
     * @param bizType 业务类型
     * @return -
     */
    CreditsRefund buildRefund(AiTask aiTask, CreditLogBizType bizType);
}
