/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.response;

import ai.creatly.sky.creation.domain.core.plan.model.enums.BenefitType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 定价计划订单所获得的权益
 *
 * <AUTHOR>
 * @version PlanOrderBenefitVM.java, v 0.1 2023-12-22 上午2:00 zhoudong
 */
@Data
@Accessors(chain = true)
public class PlanOrderBenefitVM {

    /**
     * 权益类型
     */
    private BenefitType benefitType;
    /**
     * 权益名称
     */
    private String      benefitName;
}
