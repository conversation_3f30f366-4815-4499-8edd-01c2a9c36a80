/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.task.input;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizInput;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoAsset;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoProduct;
import ai.creatly.sky.creation.domain.core.videoproject.model.VideoSubtitleLayer;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * <AUTHOR>
 * @version DramaAdsComposeTaskInput.java, 2024-11-25 下午8:48 zhoudong
 */
@Data
@Accessors(chain = true)
public class DramaAdsComposeTaskInput implements TaskBizInput {

    /**
     * 编排索引（从0开始，对应 videoProject.composeTasks 集合里的下标）
     */
    private Integer            composeIdx;
    private VideoProduct       product;
    private List<VideoAsset>   assets;
    @Nullable
    private VideoSubtitleLayer subtitleLayer;
    /**
     * 视频宽度（像素）
     */
    private Integer            width;
    /**
     * 视频高度（像素）
     */
    private Integer            height;
}
