/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.credit.cost.repository;

import ai.creatly.sky.creation.domain.core.credit.cost.model.CreditCostRule;
import ai.creatly.sky.creation.domain.core.credit.cost.model.request.CreditCostRuleQueryParam;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;

/**
 * <AUTHOR>
 * @version : FeaturesCostRuleAdminRepository.java, v 1.0 2023年12月02日 10时29分 heb
 */
public interface CreditCostRuleAdminRepository {

    /**
     * 创建计费规则
     */
    long create(CreditCostRule creditCostRule);

    /**
     * 更新计费规则
     *
     * @param creditCostRule -
     */
    void updateById(CreditCostRule creditCostRule);


    /**
     * 删除计费规则
     *
     * @param id -
     */
    void deleteById(long id);

    /**
     * 假删计费规则
     *
     * @param id -
     */
    void softDeleteById(long id);

    /**
     * 分页查询
     */
    Page<CreditCostRule> queryCostRulePage(CreditCostRuleQueryParam queryParam, Pageable pageable);
}
