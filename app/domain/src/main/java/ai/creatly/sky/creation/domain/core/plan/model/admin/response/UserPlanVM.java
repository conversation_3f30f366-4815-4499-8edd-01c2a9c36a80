/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.admin.response;

import ai.creatly.sky.creation.domain.core.plan.model.UserPlanBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanPeriodType;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import com.jspeeder.core.model.OperatorRef;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version UserPlanVM.java, v 0.1 2023-12-21 下午9:25 zhoudong
 */
@Data
public class UserPlanVM {

    /**
     * 主键ID
     */
    private String                id;
    /**
     * 创建时间
     */
    private ZonedDateTime         createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime         updatedAt;
    /**
     * 创建者
     */
    private OperatorRef           createdBy;
    /**
     * 更新者
     */
    private OperatorRef           updatedBy;
    /**
     * 用户ID
     */
    private Long                  uid;
    /**
     * 平台统一定价计划ID
     */
    private String                platformPlanId;
    /**
     * 计划类型
     */
    private PlanType              type;
    /**
     * 计划名称
     */
    private String                name;
    /**
     * 计划描述
     */
    private String                description;
    /**
     * 原价（分）
     */
    private Long                  originalFee;
    /**
     * 实价（分）
     */
    private Long                  realFee;
    /**
     * 订单标题（下单时使用）
     */
    @Nullable
    private String                orderTitle;
    /**
     * 计划周期类型
     */
    private PlanPeriodType        periodType;
    /**
     * 到期是否自动续费（如果为空，则取决于用户选择）
     */
    @Nullable
    private Boolean               autoRenewed;
    /**
     * 计划阶梯档位（用于排序、有序展示）
     */
    private Integer               level;
    /**
     * 权益有效期（以天为单位，如果有值，最多365天，为空则表示终身有效）
     */
    @Nullable
    private Duration              benefitDuration;
    /**
     * 权益列表
     */
    private List<UserPlanBenefit> benefits;
}
