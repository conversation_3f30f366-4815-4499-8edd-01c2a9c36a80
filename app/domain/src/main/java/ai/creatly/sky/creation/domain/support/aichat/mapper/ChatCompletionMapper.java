/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.aichat.mapper;

import ai.creatly.sky.creation.domain.common.integration.openai.OpenAiTokenUtil;
import ai.creatly.sky.creation.domain.support.aichat.model.OpenAiChatCompletionConfig;
import ai.creatly.sky.creation.domain.support.aichat.model.response.ReplyTextChunk;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.util.Validates;
import com.theokanning.openai.completion.chat.*;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version ChatCompletionMapper.java, v 0.1 2023-05-13 19:32 joton
 */
@Mapper(config = BaseMapperConfig.class)
public interface ChatCompletionMapper {

    ChatMessage SYSTEM_MESSAGE = new ChatMessage(ChatMessageRole.SYSTEM.value(), "You are a helpful assistant.");

    default ChatCompletionRequest buildRequest(List<String> records, OpenAiChatCompletionConfig config) {
        final List<ChatMessage> messages = new ArrayList<>();
        messages.add(SYSTEM_MESSAGE);
        ChatMessageRole role = ChatMessageRole.USER;
        for (String record : records) {
            messages.add(new ChatMessage(role.value(), record));
            role = role == ChatMessageRole.USER ? ChatMessageRole.ASSISTANT : ChatMessageRole.USER;
        }

        // token数量校验（如果超过一个阈值，则从最早的历史会话开始丢弃，直到不超过该阈值）
        int inputTokens = OpenAiTokenUtil.countChatTokens(config.getModel(), messages);
        while (inputTokens > config.getMaxInputTokens()) {
            Validates.isTrue(messages.size() > 2, "当前一轮输入对话内容过长，请删减后再重试！");
            // 删除最早的一轮对话
            messages.remove(1);
            messages.remove(2);
            // 重新计算 token 数量
            inputTokens = OpenAiTokenUtil.countChatTokens(config.getModel(), messages);
        }

        return ChatCompletionRequest.builder()
                .model(config.getModel())
                .messages(messages)
                .temperature(config.getTemperature())
                // Defaults to 1
                .topP(1d)
                // Defaults to 1
                .n(1)
                .maxTokens(config.getMaxTokens())
                .user("creatly-" + System.currentTimeMillis())
                .build();
    }

    /**
     * 聊天回复文本段模型转换
     *
     * @param chunk 一段文本
     * @return 一段回复文本
     */
    default ReplyTextChunk toReplyTextChunk(ChatCompletionChunk chunk) {
        ReplyTextChunk replyTextChunk = new ReplyTextChunk();
        replyTextChunk.setId(chunk.getId());
        ChatCompletionChoice choice = chunk.getChoices().getFirst();
        if (choice.getMessage().getRole() != null) {
            // 第一条信息会携带角色
            replyTextChunk.setFirst(true);
        }
        if (choice.getFinishReason() != null) {
            if ("stop".equals(choice.getFinishReason())) {
                // 正常结束
                replyTextChunk.setLast(true);
            } else {
                // 异常中断
                replyTextChunk.setBroken(true);
            }
        }
        replyTextChunk.setText(Objects.toString(choice.getMessage().getContent(), ""));
        return replyTextChunk;
    }
}
