/*
 * This file is generated by jOOQ.
 */
package ai.creatly.sky.creation.domain.core.course.model;


import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;


/**
 * AI课程订单记录
 */
@Data
@Accessors(chain = true)
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class AiCourseLearnRecord {

    private Long          id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private Long          courseId;
    private Long          contentId;
    private Integer       status;
    private Long          ownerId;
    private String        ownerName;
    private Integer       initSeconds;
    private String        orgCode;
}
