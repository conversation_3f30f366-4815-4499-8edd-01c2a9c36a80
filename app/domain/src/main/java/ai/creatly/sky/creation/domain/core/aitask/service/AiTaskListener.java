/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.service;

import ai.creatly.sky.creation.domain.common.async.AsyncPools;
import ai.creatly.sky.creation.domain.common.async.AsyncTemplate;
import ai.creatly.sky.creation.domain.core.aitask.engine.AiTaskEngine;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.factory.AiTaskHandlerFactory;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskExecStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.event.AiTaskExecutionEvent;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 任务相关的事件监听
 *
 * <AUTHOR>
 * @version AiTaskListener.java, v 0.1 2023-11-23 上午1:52 zhoudong
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AiTaskListener {

    private final AiTaskRepository     aiTaskRepository;
    private final AiTaskEngine         aiTaskEngine;
    private final AppAlertHelper       appAlertHelper;
    private final AsyncTemplate        asyncTemplate;
    private final AiTaskHandlerFactory aiTaskHandlerFactory;

    /**
     * 内存事件异步触发任务执行
     */
    @EventListener(classes = AiTaskExecutionEvent.class)
    public void onExecutionEventAsync(AiTaskExecutionEvent event) {
        asyncTemplate.execute(AsyncPools.TASK_ASYNC_EXECUTE_POOL, () -> {
            AiTask aiTask = aiTaskRepository.queryOptionalById(event.taskId()).orElse(null);
            if (aiTask == null) {
                log.warn("[onExecutionEventAsync]task not existing,taskId={}", event.taskId());
                appAlertHelper.alertText("[onExecutionEventAsync]task not existing,taskId={}", event.taskId());
                return;
            }
            if (aiTask.getExecStatus() == AiTaskExecStatus.COMPLETED) {
                log.warn("[onExecutionEventAsync]task already completed,taskId={}", event.taskId());
                return;
            }
            // 如果执行中的该类型的任务已经达到上限，则不再执行，等自动调度执行
            AiTaskConfig taskConfig = aiTaskHandlerFactory.getTaskConfig(aiTask.getTaskType(), aiTask.getBizType());
            // 注意：此处统计已经包含了本任务
            long count = aiTaskRepository.countInProgress(aiTask.getTaskType(), aiTask.getBizType());
            if (count <= taskConfig.getLoadSize()) {
                aiTaskEngine.execute(aiTask);
            } else {
                log.warn("[onExecutionEventAsync]the task will not be executed until next schedule, taskId={}", event.taskId());
            }
        });
    }
}
