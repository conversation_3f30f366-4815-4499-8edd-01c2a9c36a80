/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.video.service;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsRefund;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsService;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;

/**
 * 商品视频统一计费服务
 *
 * <AUTHOR>
 * @version ProductVideoCreditsService.java, v 0.1 2024-05-28 上午12:09 zhoudong
 */
public interface ProductVideoCreditsService extends CreditsService {

    /**
     * 计算商品视频重配音扣费
     *
     * @param inputVideoFile 输入的视频文件
     * @return 余额支出请求
     */
    CreditsExpense calcReTalkExpense(AiTask aiTask, UserFile inputVideoFile);

    /**
     * 构建余额退还请求
     *
     * @param aiTask  商品视频相关任务
     * @param bizType 业务类型
     * @return 余额退还请求
     */
    CreditsRefund buildRefund(AiTask aiTask, CreditLogBizType bizType);
}
