/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.specific.benefit;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 权益内容-余额
 *
 * <AUTHOR>
 * @version CreditsBenefit.java, v 0.1 2023-10-10 下午10:24 zhoudong
 */
@Data
@Accessors(chain = true)
public class CreditsBenefit {

    /**
     * 总余额
     */
    private Integer totalCredits;
    /**
     * 充值余额
     */
    private Integer topUpCredits;
    /**
     * 赠送余额
     */
    private Integer giftCredits;
}
