package ai.creatly.sky.creation.domain.core.aittalk.model.request;

import jakarta.validation.constraints.Digits;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @version CreateTalkVideoAudioInput.java, v 0.1 2023-06-23 11:51 joton
 */
@Data
public class CreateTalkVideoAudioInput {

    /**
     * 音频文件ID
     */
    @Digits(integer = 20, fraction = 0, message = "输入音频文件ID长度不能超过{integer}")
    private String audioFileId;

    /**
     * 音频文件ID
     */
    @Digits(integer = 20, fraction = 0, message = "输入音频文件ID长度不能超过{integer}")
    private String audioId;

    public String getAudioFileId() {
        if (StringUtils.hasText(audioId)) {
            return audioId;
        }
        return audioFileId;
    }
}
