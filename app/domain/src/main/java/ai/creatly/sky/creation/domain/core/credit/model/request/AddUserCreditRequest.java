package ai.creatly.sky.creation.domain.core.credit.model.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 添加元气
 * <AUTHOR>
 */
@Data
public class AddUserCreditRequest {
    /**
     * 用户id
     */
    @NotNull
    private String userId;
    /**
     * credit数额
     */
    @NotNull
    @Min(1)
    @Max(1_000_000)
    private Integer credit;
}
