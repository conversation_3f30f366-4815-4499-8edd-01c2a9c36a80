/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.support.aichat.langchain.translator.exception;

import ai.creatly.sky.creation.domain.support.aichat.langchain.translator.model.TranslateResult;
import com.jspeeder.core.data.problem.error.ErrorCode;
import com.jspeeder.core.data.problem.exception.BizException;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @version TranslateBizException.java, v 0.1 2023-11-22 15:29 syoka
 */
@Getter
public class TranslatePromptException extends BizException {

    private final TranslateResult translateResult;

    public TranslatePromptException(@NotNull ErrorCode errorCode, TranslateResult translateResult) {
        super(errorCode);
        this.translateResult = translateResult;
    }
}
