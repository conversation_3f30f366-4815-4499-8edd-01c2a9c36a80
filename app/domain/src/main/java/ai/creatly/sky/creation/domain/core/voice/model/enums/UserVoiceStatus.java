/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.enums;

import com.jspeeder.core.data.enums.ICode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 用户定制声音状态
 *
 * <AUTHOR>
 * @version UserVoiceStatus.java, v 0.1 2024-01-20 下午15:40 heb
 */
@Getter
@RequiredArgsConstructor
public enum UserVoiceStatus implements ICode {

    DRAFT("草稿"),
    ONLINE("已上架"),
    OFFLINE("已下架"),
    UNPAID("待付款"),
    ;

    private final String desc;

    @Override
    public String getCode() {
        return this.name();
    }
}
