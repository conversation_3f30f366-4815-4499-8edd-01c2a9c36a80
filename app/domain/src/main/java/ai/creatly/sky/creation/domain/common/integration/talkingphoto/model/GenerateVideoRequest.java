/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.talkingphoto.model;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizParams;
import ai.creatly.sky.creation.domain.core.aittalk.model.enums.AiTalkPicCroppingType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version GenerateVideoRequest.java, v 0.1 2023-07-23 00:54 joton
 */
@Data
@Accessors(chain = true)
public class GenerateVideoRequest implements TaskBizParams {

    private String                requestId;
    private String                sourceImage;
    private String                drivenAudio;
    private String                uploadPath;
    /**
     * 头像模式True，非头像模式False
     */
    private Boolean               still;
    /**
     * crop 半身/full 全屏
     */
    private AiTalkPicCroppingType preprocess;
    private String                emotionType;
}
