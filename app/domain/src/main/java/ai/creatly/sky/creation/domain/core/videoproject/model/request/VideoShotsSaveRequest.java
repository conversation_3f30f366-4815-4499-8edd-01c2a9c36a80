/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version VideoShotsSaveRequest.java, v 0.1 2024-09-21 下午7:16 zhoudong
 */
@Data
public class VideoShotsSaveRequest {

    @NotEmpty
    @Size(max = 30)
    @Valid
    private List<VideoShotSaveDTO> shots;
}
