/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.cameramotion.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 图片运镜请求
 *
 * <AUTHOR>
 * @version ImageMoveRequest.java, v 0.1 2024-09-19 下午6:51 zhoudong
 */
@Data
@Accessors(chain = true)
public class ImageMoveRequest {
    private String bucket;
    @JsonProperty("input_object_key")
    private String inputImageKey;
    @JsonProperty("output_object_key")
    private String outputVideoKey;
    /**
     * 生成的视频时长（秒，可以小数）
     */
    private String duration;
    /**
     * 生成的视频格式（mp4、mov）
     */
    private String format;
}
