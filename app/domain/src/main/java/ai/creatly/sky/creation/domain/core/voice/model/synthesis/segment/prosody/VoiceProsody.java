/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment.prosody;

import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechPitchType;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechRateType;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechVolumeType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;

/**
 * 局部韵律调节
 *
 * <AUTHOR>
 * @version VoiceProsody.java, v 0.1 2023-12-07 下午10:50 zhoudong
 */
@Data
@Accessors(chain = true)
public class VoiceProsody {

    /**
     * 元素列表（局部文本和元素列表二选一）
     */
    @Valid
    @NotEmpty
    private List<VoiceProsodyElement>    elements;
    /**
     * 局部音高
     */
    @Nullable
    private SpeechPitchType              pitch;
    /**
     * 局部语速
     */
    @Nullable
    private SpeechRateType               rate;
    /**
     * 局部音量
     */
    @Nullable
    private SpeechVolumeType             volume;
    /**
     * 局部语调轮廓（由多个轮廓点组成）
     */
    @Nullable
    @Valid
    @Size(max = 5)
    private List<IntonationContourPoint> contour;

    public VoiceProsody addElement(VoiceProsodyElement element) {
        if (elements == null) {
            elements = new ArrayList<>();
        }
        elements.add(element);
        return this;
    }
}
