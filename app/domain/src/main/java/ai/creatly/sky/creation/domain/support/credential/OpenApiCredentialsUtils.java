/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.support.credential;

import lombok.experimental.UtilityClass;

import java.util.Random;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version : OpenApiCredentialsUtils.java, v 1.0 2023年07月29日 15时08分 syoka Exp$
 */
@UtilityClass
public class OpenApiCredentialsUtils {

    private static final String[] seed = {
            "1", "2", "3", "4", "5", "6", "7", "8", "9", "0",
            "a", "b", "c", "d", "e", "f", "g", "h", "i", "j",
            "k", "l", "m", "n", "o", "p", "q", "r", "s", "t",
            "u", "v", "w", "x", "y", "z", "A", "B", "C", "D",
            "E", "F", "G", "H", "I", "J", "K", "L", "M", "N",
            "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"
    };


    public static String generateAppKey() {
        return UUID.randomUUID().toString().replace("-", "");
    }


    public static String generateAppSecret() {
        Random random = new Random();
        StringBuilder secret = new StringBuilder();
        for (int i = 0; i < 30; i++) {
            secret.append(seed[random.nextInt(seed.length)]);
        }
        return secret.toString();
    }
}
