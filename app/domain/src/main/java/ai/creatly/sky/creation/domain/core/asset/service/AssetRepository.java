/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.service;

import ai.creatly.sky.creation.domain.core.asset.model.Asset;
import ai.creatly.sky.creation.domain.core.asset.model.AssetQO;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.error.CommonErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version AssetRepository.java, v0.1 2025-02-19 19:54
 */
public interface AssetRepository {

    long create(Asset asset);

    void batchCreate(List<Asset> assets);

    void updateById(Asset asset);

    default Asset queryById(long id) {
        return this.queryOptionalById(id).orElseThrow(() -> new SysException(CommonErrorCode.RECORD_NOT_FOUND));
    }

    Optional<Asset> queryOptionalById(long id);

    Page<Asset> queryPage(AssetQO assetQO, Pageable pageable);
}
