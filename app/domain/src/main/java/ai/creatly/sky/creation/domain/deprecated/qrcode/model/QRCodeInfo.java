/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.qrcode.model;

import lombok.Builder;
import lombok.Data;

/**
 * 二维码相关信息
 *
 * <AUTHOR>
 * @version : QRCodeInfo.java, v 1.0 2023年07月21日 15时28分 syoka Exp$
 */
@Data
@Builder
public class QRCodeInfo {

    /**
     * 生成码的主体
     */
    private String issuer;

    /**
     * 二维码场景码
     */
    private QRCodeScene scene;

    /**
     * 二维码地址
     */
    private String codeUrl;
}
