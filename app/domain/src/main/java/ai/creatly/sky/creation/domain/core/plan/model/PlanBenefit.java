/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model;

import ai.creatly.sky.creation.domain.core.plan.model.enums.BenefitType;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

/**
 * 付费计划内的权益
 *
 * <AUTHOR>
 * @version PlanBenefit.java, v 0.1 2023-10-10 下午10:46 zhoudong
 */
@Data
public class PlanBenefit {

    /**
     * 所属定价计划ID
     */
    private String planId;
    /**
     * 权益类型
     */
    private BenefitType type;
    /**
     * 权益编号（唯一键约束：planId + type + code）
     */
    private String code;
    /**
     * 权益名称
     */
    private String name;
    /**
     * 权益内容（JSON格式，根据不同权益类型区分不同模型，在消费时自行转换）
     *
     * @see ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.MemberBenefit
     * @see ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.CreditsBenefit
     * @see ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.TopUpMoreCreditsBenefit
     * @see ai.creatly.sky.creation.domain.core.plan.model.specific.benefit.AbilityBenefit
     */
    @Nullable
    private String value;
}
