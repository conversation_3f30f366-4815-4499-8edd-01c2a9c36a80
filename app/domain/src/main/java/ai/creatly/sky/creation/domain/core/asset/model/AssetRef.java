/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @version AssetRef.java, v0.1 2025-02-24 14:56
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(of = "id")
public class AssetRef {

    /**
     * 主键ID
     */
    private Long      id;
    /**
     * 封面图地址（OSS）
     */
    private String    coverUrl;
    /**
     * 文件
     */
    private AssetFile file;
}
