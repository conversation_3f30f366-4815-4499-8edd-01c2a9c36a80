/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.model.enums;

import ai.creatly.sky.creation.domain.core.digitalhuman.error.DigHumanErrorCode;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.NarrativeStyle;
import com.jspeeder.core.data.problem.exception.BizException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version ShortVideoNarrativeStyle.java, v 0.1 2024-07-10 10:05 syoka
 */
@RequiredArgsConstructor
@Getter
public enum ShortVideoNarrativeStyle implements NarrativeStyle {

    HUMOUR("humour", "幽默叙事", true),
    STORYTELLING("storytelling", "故事化叙事", false),
    STRAIGHTFORWARD("straightforward", "直接叙事", false),
    CASE_ANALYSIS("case-analysis", "案例分析", false),
    ;

    private final String  code;
    private final String  name;
    private final boolean enabled;

    public static ShortVideoNarrativeStyle of(String code) {
        for (ShortVideoNarrativeStyle value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new BizException(DigHumanErrorCode.DH_HOT_VIDEO_UN_RECOGNIZE_NARRATIVE_STYLE);
    }
}

