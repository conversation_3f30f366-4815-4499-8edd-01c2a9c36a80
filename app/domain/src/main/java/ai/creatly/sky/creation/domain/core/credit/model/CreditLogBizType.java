/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.credit.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 余额收支业务类型
 */
@AllArgsConstructor
@Getter
public enum CreditLogBizType {

    /*----------------收入----------------*/
    @Deprecated
    RECHARGE("系统充值", "兼容老数据"),
    ADMIN_RECHARGE("后台管理员充值", "唯一标识：系统生成唯一ID"),
    USER_SIGN_UP("新用户注册", "唯一标识：本用户ID"),
    INVITE_USER_TO_SIGN_UP("邀请新用户注册", "唯一标识：新用户ID"),
    TOP_UP_PLAN_ORDER("元气充值获得", "唯一标识：交易中心订单ID"),
    MEMBER_GIFT("会员赠送", "唯一标识：会员ID+赠送时间"),

    /*----------------支出----------------*/
    MA_LIANG("马良", "唯一标识：任务ID"),
    TXT_2_IMAGE("文生图消耗", "唯一标识：任务ID"),
    GEN_IMAGE("生图消耗", "唯一标识：任务ID"),
    INTELLIJ_WRITER("丹青", "唯一标识：会话消息ID"),
    LIVE_PIC("图像成片消耗", "唯一标识：任务ID"),
    AUDIO_CLONE("声音克隆消耗", "唯一标识：任务ID"),
    AUDIO_TTS("文本转语音消耗", "唯一标识：任务ID"),
    AUDIO_MUSIC("AI音乐消耗", "唯一标识：任务ID"),
    VIDEO_GENERATE("视频生成消耗", "唯一标识：任务ID"),
    VOICE_BASIC("声乐合成-基础语音-文本转语音", "唯一标识：音频文件ID"),
    VOICE_BASIC_CREATION("声乐合成-基础语音-语音创作", "唯一标识：任务ID"),
    VOICE_MARKET("声乐合成-声音演员-文本转语音", "唯一标识：任务ID"),
    VOICE_MARKET_CREATION("声乐合成-声音演员-语音创作", "唯一标识：任务ID"),
    VOICE_MARKET_CONVERSION("声乐合成-声音演员-以声出声", "唯一标识：任务ID"),
    COLLABORATION_CREATION("联合创作", "唯一标识：任务ID"),
    AI_PRODUCT_IMAGE("AI商品图", "唯一标识：任务ID"),
    PRODUCT_VIDEO_RE_TALK("商品视频重配音", "唯一标识：任务ID"),
    DIG_HUMAN_AVATAR_GEN("形象定制", "唯一标识：任务ID"),
    DIG_HUMAN_VIDEO_GEN("创意成片视频生成", "唯一标识：任务ID"),
    STORY_SCRIPT("故事短片-生成故事剧本", "唯一标识：时间戳"),
    STORY_ROLE_INFO("故事短片-剧本定稿（初始化角色信息）", "唯一标识：故事ID"),
    STORY_ROLE_IMAGES_INIT("故事短片-初始化角色图", "唯一标识：任务ID-角色ID"),
    STORY_ROLE_IMAGE("故事短片-生成角色图", "唯一标识：任务ID"),
    STORY_SCENES_INIT("故事短片-生成故事板", "唯一标识：故事ID"),
    STORY_SHOT_IMAGE("故事短片-生成分镜图", "唯一标识：任务ID"),
    STORY_SHOT_SOUND_RECOMMEND("故事短片-音效推荐", "唯一标识：故事ID-分镜ID-时间戳"),
    STORY_SHOT_DIALOGUE_AUDIO("故事短片-合成分镜对白语音", "唯一标识：音频文件ID"),
    STORY_SHOT_CAMERA_MOVE_VIDEO("故事短片-运镜视频生成", "唯一标识：任务ID"),
    STORY_SHOT_EFFECT_VIDEO("故事短片-动效视频生成", "唯一标识：任务ID"),
    VIDEO_PROJECT_VIDEO_RENDER("视频项目渲染视频", "唯一标识：任务ID"),
    ;

    private final String desc;
    private final String bizIdDesc;
}
