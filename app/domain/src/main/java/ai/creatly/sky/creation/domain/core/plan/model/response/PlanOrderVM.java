/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.response;

import ai.creatly.kylin.trade.sdk.order.enums.OrderStatus;
import ai.creatly.kylin.trade.sdk.payment.enums.PaymentChannel;
import ai.creatly.sky.creation.domain.core.plan.model.enums.BenefitType;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 定价计划订单（只返回支付成功的）
 *
 * <AUTHOR>
 * @version PlanOrderVM.java, v 0.1 2023-10-10 下午10:25 zhoudong
 */
@Data
@Accessors(chain = true)
public class PlanOrderVM {

    /**
     * 订单业务序号
     */
    private String                   bizSn;
    /**
     * 订单标题
     */
    private String                   title;
    /**
     * 用户ID
     */
    private String                   uid;
    /**
     * 订单原费用（元）
     */
    private String                   originalFee;
    /**
     * 订单实际费用（元）
     */
    private String                   realFee;
    /**
     * 支付费用（元）
     */
    private String                   paidFee;
    /**
     * 订单状态（已支付：表示权益还未到账，已发货：表示权益已经到账）
     */
    private OrderStatus              status;
    /**
     * 支付渠道
     */
    private PaymentChannel           paymentChannel;
    /**
     * 支付完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime            paidTime;
    /**
     * 付费计划ID
     */
    private String                   planId;
    /**
     * 付费计划类型
     */
    private PlanType                 planType;
    /**
     * 获得的权益列表
     */
    private List<PlanOrderBenefitVM> benefits;
    /**
     * 权益类型（待废弃⚠️）
     */
    private BenefitType              benefitType;
    /**
     * 权益名称（待废弃⚠️）
     */
    private String                   benefitName;
    /**
     * 权益生效时间（如果权益还未发放，则为空）
     */
    @Nullable
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime            benefitEffectAt;
    /**
     * 权益到期时间（如果权益还未发放，则为空）
     */
    @Nullable
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime            benefitExpireAt;
}
