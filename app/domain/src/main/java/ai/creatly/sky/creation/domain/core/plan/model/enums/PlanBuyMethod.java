/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 付费计划购买方式
 *
 * <AUTHOR>
 * @version PlanBuyMethod.java, v 0.1 2023-10-15 下午3:08 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum PlanBuyMethod {

    FREE_TRIAL("免费试用"),
    ONLINE_PAY("在线支付"),
    FACE_TO_FACE("线下面谈"),
    ;

    private final String desc;
}
