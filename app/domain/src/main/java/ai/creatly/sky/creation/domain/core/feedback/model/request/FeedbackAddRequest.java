/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.feedback.model.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 反馈请求
 *
 * <AUTHOR>
 * @version FeedbackAddRequest.java, v 0.1 2023-12-15 22:44 heb
 */

@Data
public class FeedbackAddRequest {

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String title;
    /**
     * 反馈明细
     */
    @NotBlank(message = "明细不能为空")
    private String detail;
}
