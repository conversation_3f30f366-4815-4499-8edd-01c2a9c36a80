/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.reactive;

import com.jspeeder.core.data.problem.error.ErrorCode;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Function;

/**
 *
 * <AUTHOR>
 * @version ErrorMapper.java, v0.1 2025-02-24 00:23
 */
@Slf4j
@UtilityClass
public class ErrorMapper {

    public Function<? super Throwable, ? extends Throwable> sysException(ErrorCode errorCode) {
        return t -> {
            log.error(errorCode.getMsg(), t);
            return new SysException(errorCode, t);
        };
    }

    public Function<? super Throwable, ? extends Throwable> sysException(ErrorCode errorCode, Object... args) {
        return t -> {
            log.error(errorCode.getMsg(), args, t);
            return new SysException(errorCode, FormatUtil.buildMessage(errorCode.getMsg(), args), t);
        };
    }
}
