/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.model;

import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import com.jspeeder.core.data.enums.ICode;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @version AiTaskUniqueValue.java, v 0.1 2023-08-06 00:24 joton
 */
@Data
@Builder
@AllArgsConstructor(access = AccessLevel.PACKAGE)
public class AiTaskUniqueValue {

    /**
     * 任务类型
     */
    private final AiTaskType taskType;
    /**
     * 关联外部业务类型（各业务领域独立维护自己的枚举，不做集中式维护）
     */
    private final ICode      bizType;
    /**
     * 关联外部业务单号
     */
    private final String     bizNo;
    /**
     * 关联外部业务子单号
     */
    @Nullable
    private final String     subBizNo;
}
