/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.response;

import ai.creatly.sky.creation.domain.core.voice.model.enums.LangFamily;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceAgeLevel;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceGender;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 基础声音
 *
 * <AUTHOR>
 * @version BasicVoiceVM.java, v 0.1 2023-11-21 下午7:38 zhoudong
 */
@Data
@Accessors(chain = true)
public class BasicVoiceVM {

    /**
     * 声音ID
     */
    private String               id;
    /**
     * 声音编号（全局唯一）
     */
    private String               code;
    /**
     * 英文展示名
     */
    private String               enName;
    /**
     * 中文展示名
     */
    private String               cnName;
    /**
     * 性别
     */
    private VoiceGender          gender;
    /**
     * 显示头像
     */
    private String               avatar;
    /**
     * 口音代号
     */
    private String               localeCode;
    /**
     * 口音描述（中文展示）
     */
    private String               localeDesc;
    /**
     * 语系类型
     */
    private LangFamily           langFamily;
    /**
     * 语系类型描述
     */
    private String               langFamilyDesc;
    /**
     * 年龄段
     */
    private VoiceAgeLevel        ageLevel;
    /**
     * 预览音频文件ID
     */
    private String               previewAudioId;
    /**
     * 预览音频文件ID
     */
    private String               previewAudioUrl;
    /**
     * 可扮演角色列表
     */
    private List<VoiceRoleVM>    roles;
    /**
     * 可附加情绪列表
     */
    private List<VoiceEmotionVM> emotions;
}
