/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.audio.task;

import ai.creatly.sky.creation.domain.core.ai.audio.AiAudioTtsClient;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiAudioTaskResult;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AudioTtsTaskInput;
import ai.creatly.sky.creation.domain.core.ai.model.AiTaskBizType;
import ai.creatly.sky.creation.domain.core.ai.model.OrderedAsset;
import ai.creatly.sky.creation.domain.core.ai.video.model.AiVideoTaskVars;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.transaction.AiTaskTransactionManager;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskBizExecStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskHelper;
import ai.creatly.sky.creation.domain.core.asset.model.Asset;
import ai.creatly.sky.creation.domain.core.asset.model.AssetFile;
import ai.creatly.sky.creation.domain.core.asset.model.AssetMetadata;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetBizType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetSourceType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetStatus;
import ai.creatly.sky.creation.domain.core.asset.service.AssetRepository;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsRefund;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.userasset.model.AssetContent;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.ImageFileService;
import ai.creatly.sky.creation.domain.core.userfile.service.impl.AudioFileServiceImpl;
import ai.creatly.sky.creation.domain.core.userfile.service.impl.UserFileServiceImpl;
import com.jspeeder.core.data.id.IdHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version VideoGenerateTaskHandler.java, v0.1 2025-02-28 13:41
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AudioTtsTaskHandler implements AiTaskHandler {

    @Autowired
    private   AiAudioTtsClient        aiAudioTtsClient;
    @Autowired
    protected ImageFileService        imageFileService;
    @Autowired
    protected AiTaskHelper            aiTaskHelper;
    @Autowired
    protected UserFileRepository      userFileRepository;
    @Autowired
    protected UserCreditDomainService userCreditDomainService;
    @Autowired
    protected UserFileServiceImpl     userFileService;
    @Autowired
    protected AudioFileServiceImpl    audioFileService;
    @Autowired
    private   AssetRepository         assetRepository;


    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.AI_AUDIO)
                .setBizType(AiTaskBizType.audio_tts)
                .setLoadSize(3)
                .setQueueMode(TaskQueueMode.POP_PER_EXECUTED)
                .setExecCountAlertThreshold(Integer.MAX_VALUE)
                .setNotifyOnSubmit(false);
    }

    @Override
    public TaskPreAction preHandle(AiTask aiTask) {
        log.info("文本转语音，preHandle开始执行request:{}", aiTask.getBizParams());
        var input = aiTask.parseBizInput(AudioTtsTaskInput.class);
        UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);
        AiAudioTaskResult bizResult = aiAudioTtsClient.generate(input, userContext);
        if (bizResult.getBizExecStatus() == AiTaskBizExecStatus.FAILED) {
            return TaskPreAction.FORWARD_CANCELED.updateBizVars(new AiVideoTaskVars());
        }
        return TaskPreAction.FORWARD_RUNNING.updateBizVars(new AiVideoTaskVars());
    }

    @Override
    public TaskAction handle(AiTask aiTask) {
        //1、查询生成状态
        var input = aiTask.parseBizInput(AudioTtsTaskInput.class);
        UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);
        AiAudioTaskResult result = aiAudioTtsClient.queryGeneration(aiTask, userContext);

        // 2、生成中/失败直接结束
        if (result.getBizExecStatus() == AiTaskBizExecStatus.PROCESSING) {
            return TaskAction.KEEP_STILL.updateBizResult(result);
        } else if(result.getBizExecStatus() == AiTaskBizExecStatus.FAILED){
            return TaskAction.FORWARD_CANCELED.updateBizResult(result);
        }

        // 3、生成成功，下载转存克隆的音频资源，保存为用户资产
        List<OrderedAsset> assetList = result.getAssets();

        List<UserFile> userFiles = assetList.stream().map(x -> {
            FileBizSource bizSource = FileBizSource.AI_AUDIO_TTS;
            // 下载文件并上传
            UserFile audioFile;
            if (x.getUserFile() != null) {
                audioFile = x.getUserFile();
            } else {
                audioFile = audioFileService.upload(x.getUrl(), IdHelper.getId(), bizSource, userContext);
            }
            x.setUrl(audioFile.getOssUrl());
            return audioFile;
        }).toList();

        List<Asset> assets = userFiles.stream().map(x -> this.buildAsset(input, x, aiTask)).toList();
        AiTaskTransactionManager.registerSynchronization(() -> {
            userFileRepository.batchCreate(userFiles);
            assetRepository.batchCreate(assets);
        });

        return TaskAction.FORWARD_FINISHED.updateBizResult(result);
    }

    @Override
    public TaskPostAction postHandle(AiTask aiTask) {
        //任务取消则消耗元气恢复
        if (aiTask.getStatus() == AiTaskStatus.CANCELED) {
            CreditsRefund creditsRefund = CreditsRefund.builder()
                    .uid(aiTask.getOwnerId())
                    .bizType(CreditLogBizType.AUDIO_TTS)
                    .bizNo(aiTask.getId().toString())
                    .allowExpenseAbsent(false)
                    .build();
            userCreditDomainService.refundCredits(creditsRefund);
        }
        return TaskPostAction.COMPLETE;
    }

    private Asset buildAsset(AudioTtsTaskInput taskInput, UserFile userFile, AiTask  aiTask) {
        var metadata = new AssetMetadata()
                .setAttachments(Collections.singletonList(taskInput.getRequest().getAudioFile()))
                .setTaskId(aiTask.getId())
                .setBizParams(aiTask.getBizParams());

        return new Asset()
                .setId(IdHelper.getId())
                .setUid(userFile.getUid())
                .setStatus(AssetStatus.VALID)
                .setName(userFile.getOriginalFilename())
                .setSourceType(AssetSourceType.generation)
                .setBizType(AssetBizType.audio_tts)
                .setCoverUrl(taskInput.getRequest().getPhotoFile() == null ? "" : taskInput.getRequest().getPhotoFile().getUrl())
                .setFile(AssetFile.fromUserFile(userFile))
                .setContent(new AssetContent().setMd5(userFile.getMd5()))
                .setMetadata(metadata)
                .setTags(new ArrayList<>());
    }

}
