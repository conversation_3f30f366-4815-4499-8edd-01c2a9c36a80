/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.image.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version AiImageImagineRequest.java, v 0.1 2024-02-28 下午4:30 zhoudong
 */
@Data
@Accessors(chain = true)
public class AiImageImagineRequest {

    private String               bucket;
    @JsonProperty("image_key")
    private String               imageKey;
    @JsonProperty("image_category")
    private ProductImageCategory imageCategory;
    @JsonProperty("output_image_count")
    private Integer              outputImageCount;
    @JsonProperty("output_image_ratio")
    private String               outputImageRatio;
    @JsonProperty("output_image_keys")
    private List<String>         outputImageKeys;
}
