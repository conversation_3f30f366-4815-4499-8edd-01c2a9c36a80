/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.messaging.consumer;

import lombok.Builder;
import lombok.Data;

/**
 * 消费者实例配置
 *
 * <AUTHOR>
 * @version ConsumerConfig.java, v 0.1 2023-11-13 下午3:29 zhoudong
 */
@Data
@Builder
public class ConsumerConfig {

    /**
     * 消费组（格式：^[%|a-zA-Z0-9_-]+$）
     */
    private final String consumerGroup;
    private final String topic;
    /**
     * 多个tag用||拼接
     */
    private final String tags;
    private final ConsumeMethod consumeMethod;
}
