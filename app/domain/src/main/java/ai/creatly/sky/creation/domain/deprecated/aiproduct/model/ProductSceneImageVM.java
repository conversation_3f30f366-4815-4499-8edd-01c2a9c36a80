/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.aiproduct.model;

import ai.creatly.sky.creation.domain.core.aiimage_old.model.response.AttachmentVM;
import ai.creatly.sky.creation.domain.core.aitask.model.response.BaseUserAiTaskVM;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jspeeder.core.util.time.Dates;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version ProductSceneVM.java, v 0.1 2023-11-15 18:21 syoka
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductSceneImageVM extends BaseUserAiTaskVM {

    /**
     * 任务id
     */
    private String taskId;

    private String sceneId;

    private String prompt;

    private Integer progress;

    private String errorMsg;

    @JsonFormat(pattern = Dates.PATTERN_DATE_TIME)
    private ZonedDateTime createdAt;

    /**
     * 错误敏感词
     */
    private String bannedWord;

    private String imageFileId;

    private List<AttachmentVM> attachments;
}
