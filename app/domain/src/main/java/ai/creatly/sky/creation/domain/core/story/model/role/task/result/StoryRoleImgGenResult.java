/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.task.result;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizResult;
import lombok.Data;

/**
 * 单个角色的图片生成结果
 *
 * <AUTHOR>
 * @version StoryRoleImgGenResult.java, v 0.1 2024-03-30 17:52 syoka
 */
@Data
public class StoryRoleImgGenResult implements TaskBizResult {

    /**
     * 角色候选图
     */
    private StoryRoleCandidateImage candidateImage;
}
