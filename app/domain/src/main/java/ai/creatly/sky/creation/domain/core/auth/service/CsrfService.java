/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.auth.service;

import ai.creatly.sky.creation.domain.common.caching.CachingTemplate;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.Validates;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version CsrfService.java, v 0.1 2024-04-17 10:36 syoka
 */
@Component
@RequiredArgsConstructor
public class CsrfService {
    private final static String CSRF_TOKEN_PREFIX = "csrf:{}";

    private final CachingTemplate cachingTemplate;

    public void saveCsrfToken(String csrfToken, Duration timeout) {
        cachingTemplate.set(FormatUtil.format(CSRF_TOKEN_PREFIX, csrfToken), csrfToken,timeout);
    }

    public void validateCsrfToken(String csrfToken) {
        Validates.notBlank(csrfToken, UserErrorCode.WECHAT_LOGIN_LOSE_CSRF_TOKEN);
        String redisCsrfToken = cachingTemplate.getAndDelete(FormatUtil.format(CSRF_TOKEN_PREFIX, csrfToken), String.class);
        if (!StringUtils.equals(redisCsrfToken, csrfToken)) {
            throw new BizException(UserErrorCode.WECHAT_LOGIN_LOSE_CSRF_TOKEN);
        }
    }

}
