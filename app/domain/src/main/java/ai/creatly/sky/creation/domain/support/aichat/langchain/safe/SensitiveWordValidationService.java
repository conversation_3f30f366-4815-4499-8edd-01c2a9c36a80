/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.support.aichat.langchain.safe;

import ai.creatly.sky.creation.domain.support.aichat.langchain.error.CopilotErrorCode;
import ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal.ChatMessageUtil;
import ai.creatly.sky.creation.domain.support.aichat.langchain.model.factory.LLMConfigDomainService;
import ai.creatly.sky.creation.domain.support.aichat.langchain.service.CopilotScenarioRegistrar;
import ai.creatly.sky.creation.domain.support.aichat.langchain.translator.exception.TranslateBizException;
import ai.creatly.sky.creation.domain.support.prompt.PromptUtils;
import com.jspeeder.core.util.json.JSON;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.UncheckedIOException;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version SensitiveWordValidationService.java, v 0.1 2023-11-22 15:15 syoka
 */
@Component
@RequiredArgsConstructor
public class SensitiveWordValidationService implements InitializingBean {

    private static final String                   SensitiveWordValidatorPrompt = "SYSTEM:sensitiveWordValidatorPrompt";
    private static final String                   BANNED_WORDS_FILE_PATH       = "classpath:banned-words.txt";
    private              List<String>             BANNED_WORDS;
    private final        ResourceLoader           resourceLoader;
    private final CopilotScenarioRegistrar copilotScenarioRegistrar;
    private final LLMConfigDomainService   llmConfigDomainService;
    public void validate(String originPrompt, String translatePrompt) {
        String finalPromptEn = translatePrompt.toLowerCase(Locale.ENGLISH);
        String bannedWord = null;
        int startIndex = 0;
        int endIndex = 0;
        String suspectedContent = null;
        for (String word : BANNED_WORDS) {
            Matcher matcher = Pattern.compile("\\b" + word + "\\b").matcher(finalPromptEn);
            if (matcher.find()) {
                startIndex = StringUtils.indexOfIgnoreCase(translatePrompt, word);
                endIndex = startIndex + word.length();
                bannedWord = translatePrompt.substring(startIndex, endIndex);
            }
        }
        if (StringUtils.isEmpty(bannedWord)) {
            return;
        }
        // 纯英文判断
        if (!PromptUtils.containsChineseCharacters(originPrompt)) {
            throw new TranslateBizException(CopilotErrorCode.TRANSLATE_PROMPT_FAIL, startIndex, endIndex, bannedWord, bannedWord);
        }

        // 存在敏感词
        String promptArray = copilotScenarioRegistrar.getScenarioPromptsByCode(SensitiveWordValidatorPrompt);

        List<ChatMessage> chatMessages = ChatMessageUtil.parseToChatMessage(promptArray, Map.of("input", originPrompt, "bannedWord", bannedWord));
        ChatLanguageModel chatLanguageModel = llmConfigDomainService.getActiveChatLanguageModel();
        Response<AiMessage> response = chatLanguageModel.generate(chatMessages);

        JSONObject jsonObject = JSON.parseJSONObject(response.content().text());
        if (jsonObject.has("content")) {
            suspectedContent = jsonObject.getString("content");
        }
        throw new TranslateBizException(CopilotErrorCode.TRANSLATE_PROMPT_FAIL, startIndex, endIndex, bannedWord, suspectedContent);
    }

    private String readContent() {
        Resource resource = resourceLoader.getResource(SensitiveWordValidationService.BANNED_WORDS_FILE_PATH);
        try (InputStream is = resource.getInputStream()) {
            return StreamUtils.copyToString(is, Charset.defaultCharset());
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }


    @Override
    public void afterPropertiesSet() {
        String bannedWords = readContent();
        BANNED_WORDS = Arrays.asList(StringUtils.split(bannedWords, "\n"));
    }
}
