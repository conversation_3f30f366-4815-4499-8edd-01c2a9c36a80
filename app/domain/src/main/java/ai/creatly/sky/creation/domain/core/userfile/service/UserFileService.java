/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.service;

import ai.creatly.sky.creation.domain.common.integration.oss.FileContentMetadata;
import ai.creatly.sky.creation.domain.core.aiimage_old.model.AiImageMetadata;
import ai.creatly.sky.creation.domain.core.userfile.error.FileErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.model.HttpFile;
import ai.creatly.sky.creation.domain.core.userfile.model.InputStreamFile;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileStorageType;
import com.jspeeder.core.util.Asserts;
import org.jetbrains.annotations.Nullable;
import org.json.JSONObject;

import java.io.InputStream;
import java.util.Map;
import java.util.Set;

/**
 * 用户文件服务（以下方法都涉及网络IO，请勿在循环中使用）TODO 需要优化，将文件的环境和权限统一管理起来
 * <p/>
 * 业务规则：系统文件区分公开和私有，用户文件皆为私有，除非用户主动公开发布（私有即意味着文件的HTTP访问地址带有签名和有效期）
 * <p/>
 * 存储空间：bucket
 * 文件对象：key (key = fileDir + filename)
 * 文件目录：fileDir
 * 文件名：filename（filename = fileId.extension）
 * 完整访问地址：ossUrl (ossUrl = 协议://bucket + key)
 *
 * <AUTHOR>
 * @version UserFileService.java, v 0.1 2023-06-24 12:26 joton
 */
public interface UserFileService {

    /*-----------------------------------------上传/下载-----------------------------------------*/

    /**
     * 上传文件（幂等）
     *
     * @param inputStreamFile 文件输入流
     * @return OSS协议的文件地址
     */
    String upload(InputStreamFile inputStreamFile);

    /**
     * 上传物理文件（幂等）
     *
     * @param data     文件字节数组
     * @param userFile 文件记录
     * @return OSS协议的文件地址
     */
    String upload(byte[] data, UserFile userFile);

    /**
     * 上传物理文件（幂等）
     *
     * @param stream   文件输入流
     * @param userFile 文件记录
     * @return OSS协议的文件地址
     */
    String upload(InputStream stream, UserFile userFile);

    /**
     * 下载物理文件
     *
     * @param userFile 用户文件记录
     * @return 文件流（注意使用的地方要关流⚠️）
     */
    default InputStream download(UserFile userFile) {
        return this.download(userFile, null);
    }

    /**
     * 下载物理文件
     *
     * @param userFile 用户文件记录
     * @param process  文件处理参数
     * @return 文件流（注意使用的地方要关流⚠️）
     */
    InputStream download(UserFile userFile, @Nullable String process);

    /*-----------------------------------------操作物理文件-----------------------------------------*/

    /**
     * 异步静默删除物理存储文件
     *
     * @param userFile 用户文件
     */
    void asyncDeleteStorageSilently(UserFile userFile);

    /**
     * 异步静默删除物理存储文件
     *
     * @param fileId 用户文件ID
     */
    void asyncDeleteStorageSilently(long fileId);

    /**
     * 静默删除物理存储文件
     *
     * @param userFile 用户文件
     */
    void deleteStorageSilently(UserFile userFile);

    /**
     * 静默删除物理存储文件
     *
     * @param fileUrl 文件地址（OSS协议）
     */
    void deleteStorageSilently(String fileUrl);

    /**
     * 检查物理存储文件是否存在
     *
     * @param userFile 用户文件记录
     */
    boolean isStoragePresent(UserFile userFile);

    default void assureStoragePresent(UserFile userFile) {
        Asserts.isTrue(this.isStoragePresent(userFile), FileErrorCode.FILE_STORAGE_NOT_EXISTS);
    }

    /**
     * 检查物理存储文件是否存在
     *
     * @param fileKey 文件key
     * @return 物理文件是否存在
     */
    default boolean isStoragePresent(String fileKey) {
        return this.isStoragePresent(FileStorageType.OSS, fileKey);
    }

    /**
     * 检查物理存储文件是否存在
     *
     * @param storageType 存储类型
     * @param fileKey     文件key
     * @return 物理文件是否存在
     */
    boolean isStoragePresent(FileStorageType storageType, String fileKey);

    /**
     * 使文件公开
     *
     * @param userFile 用户文件
     */
    void makePublic(UserFile userFile);

    /**
     * 使文件公开
     *
     * @param ossUrl OSS协议的文件地址
     */
    default void makePublic(String ossUrl) {
        this.makePublic(FileStorageType.OSS, ossUrl);
    }

    /**
     * 使文件公开
     *
     * @param storageType 存储类型
     * @param ossUrl      OSS协议的文件地址
     */
    void makePublic(FileStorageType storageType, String ossUrl);

    /**
     * 使文件私有
     *
     * @param userFile 用户文件
     */
    void makePrivate(UserFile userFile);

    /**
     * 使文件私有
     *
     * @param ossUrl OSS协议的文件地址
     */
    default void makePrivate(String ossUrl) {
        this.makePrivate(FileStorageType.OSS, ossUrl);
    }

    /**
     * 使文件私有
     *
     * @param storageType 存储类型
     * @param ossUrl      OSS协议的文件地址
     */
    void makePrivate(FileStorageType storageType, String ossUrl);

    /**
     * 获取文件自定义元数据
     *
     * @param userFile 用户文件
     * @return 用户自定义的文件元数据
     */
    JSONObject getUserMetadata(UserFile userFile);

    /**
     * 获取文件内容元数据
     *
     * @param userFile 用户文件
     * @return 文件内容元数据
     */
    FileContentMetadata getContentMetadata(UserFile userFile);

    /**
     * 获取图片文件元数据
     *
     * @param userFile 用户文件
     * @return 图片文件元数据
     */
    @Nullable
    AiImageMetadata getImgFileMetadata(UserFile userFile);

    /*-----------------------------------------操作数据库-----------------------------------------*/

    /**
     * 通过文件id查询文件访问地址
     *
     * @param uid    用户ID
     * @param fileId 文件id
     * @return 文件访问地址（HTTP地址）
     */
    String queryHttpUrlById(long uid, long fileId);

    /**
     * 通过文件id查询文件访问地址
     *
     * @param uid     用户ID
     * @param fileIds 文件id列表
     * @return 带有访问地址的文件对象
     */
    Map<Long, HttpFile> queryHttpFileMapByIds(long uid, Set<Long> fileIds);

    /**
     * 软删除用户文件记录  TODO 跑批处理任务计算文件是否被引用或超过1个月，如果是则彻底物理删除
     *
     * @param fileId 文件ID
     * @param uid    用户ID
     */
    void trash(long fileId, long uid);
}
