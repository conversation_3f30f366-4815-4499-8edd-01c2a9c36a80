/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.model;

import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.support.file.model.FileSize;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version AssetFile.java, v0.1 2025-02-19 19:37
 */
@Data
@Accessors(chain = true)
public class AssetFile {

    private Long     id;

    private FileType type;
    /**
     * 文件地址（OSS）
     */
    private String   url;
    /**
     * 文件大小
     */
    private FileSize size;
    /**
     * 宽度（单位：像素，仅图片和视频才有）
     */
    @Nullable
    private Integer  width;
    /**
     * 高度（单位：像素，仅图片和视频才有）
     */
    @Nullable
    private Integer  height;
    /**
     * 时长（仅音频和视频才有）
     */
    @Nullable
    private Duration duration;

    @JsonIgnore
    public Long getDurationMills() {
        return duration == null ? null : duration.toMillis();
    }

    public static AssetFile fromUserFile(UserFile file) {
        return new AssetFile()
                .setId(file.getId())
                .setType(file.getType())
                .setUrl(file.getOssUrl())
                .setSize(file.size())
                .setWidth(file.getMetadata().getWidth())
                .setHeight(file.getMetadata().getHeight())
                .setDuration(file.getMetadata().getDuration());
    }
}
