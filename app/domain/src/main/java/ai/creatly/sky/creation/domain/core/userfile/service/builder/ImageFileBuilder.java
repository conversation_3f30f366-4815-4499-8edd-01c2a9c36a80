/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.service.builder;

import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.FileInput;
import ai.creatly.sky.creation.domain.core.userfile.model.FileMetadata;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import ai.creatly.sky.creation.domain.support.multimedia.model.ImageMetadata;
import com.jspeeder.core.data.id.IdHelper;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version ImageFileBuilder.java, v 0.1 2023-12-27 下午6:35 zhoudong
 */
@Component
@RequiredArgsConstructor
public class ImageFileBuilder {

    private final UserFileHelper userFileHelper;

    public UserFile build(FileBizSource bizSource, ImageMetadata metadata, UserContext userContext) {
        return this.build(bizSource, metadata, userContext, null);
    }

    public UserFile build(FileBizSource bizSource, ImageMetadata metadata, UserContext userContext, @Nullable String originalFilename) {
        return this.build(IdHelper.getId(), bizSource, metadata, userContext, originalFilename);
    }

    public UserFile build(FileBizSource bizSource, ImageMetadata metadata, UserContext userContext, @Nullable String originalFilename,
                          @Nullable String subPath) {
        return this.build(IdHelper.getId(), bizSource, metadata, userContext, originalFilename, subPath);
    }

    public UserFile build(long fileId, FileBizSource bizSource, ImageMetadata metadata, UserContext userContext) {
        return this.build(fileId, bizSource, metadata, userContext, null);
    }

    public UserFile build(long fileId, FileBizSource bizSource, ImageMetadata metadata, UserContext userContext,
                          @Nullable String originalFilename) {
        return this.build(fileId, bizSource, metadata, userContext, originalFilename, null);
    }

    public UserFile build(long fileId, FileBizSource bizSource, ImageMetadata metadata, UserContext userContext,
                          @Nullable String originalFilename, @Nullable String subPath) {
        FileMetadata fileMetadata = new FileMetadata()
                .setBytes(metadata.getSize() == null ? null : metadata.getSize().getSizeInBytes())
                .setWidth(metadata.getWidth())
                .setHeight(metadata.getHeight())
                .setBizData(metadata.getMetadata())
                .setPrompt(metadata.getPrompt());
        FileInput fileInput = new FileInput()
                .setType(FileType.IMAGE)
                .setBizSource(bizSource)
                .setExtension(metadata.getExtension())
                .setOriginalFilename(originalFilename)
                .setSubPath(subPath)
                .setFileMetadata(fileMetadata)
                .setMd5(metadata.getContentMd5());
        return userFileHelper.buildUserFile(userContext, fileId, fileInput);
    }
}
