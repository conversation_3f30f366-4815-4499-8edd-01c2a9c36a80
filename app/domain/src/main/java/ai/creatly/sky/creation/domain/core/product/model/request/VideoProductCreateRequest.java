/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.product.model.request;

import ai.creatly.sky.creation.domain.common.validation.EachDigits;
import ai.creatly.sky.creation.domain.common.validation.EachNotBlank;
import ai.creatly.sky.creation.domain.common.validation.NoDuplicates;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 新增视频带货产品请求
 *
 * <AUTHOR>
 * @version VideoProductCreateRequest.java, v 0.1 2024-10-16 下午5:15 zhoudong
 */
@Data
public class VideoProductCreateRequest {
    /**
     * 产品标题
     */
    @NotBlank
    @Size(min = 2, max = 100)
    private String       title;
    /**
     * 带货产品分类（全路径）
     */
    @NotEmpty
    @Size(max = 4)
    @EachNotBlank
    private List<String> categoryCodes;
    /**
     * 产品人群分类（全路径）
     */
    @NotEmpty
    @Size(max = 4)
    @EachNotBlank
    private List<String> crowdCategoryCodes;
    /**
     * 产品特色
     */
    @NotEmpty
    @Size(max = 10)
    @NoDuplicates
    @EachNotBlank
    private List<String> features;
    /**
     * 产品详细描述
     */
    @Size(min = 2, max = 200)
    private String       description;
    /**
     * 产品封面图文件ID
     */
    @Digits(integer = 20, fraction = 0)
    private String       coverFileId;
    /**
     * 用户关联的素材
     */
    @Size(min = 1, max = 30)
    @NoDuplicates
    @EachDigits(integer = 20, fraction = 0)
    private List<String> userSelectedAssetIds;
}
