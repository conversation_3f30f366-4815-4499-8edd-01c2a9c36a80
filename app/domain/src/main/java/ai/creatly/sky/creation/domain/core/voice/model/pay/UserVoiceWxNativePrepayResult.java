/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.pay;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 微信扫码预支付结果
 *
 * <AUTHOR>
 * @version UserVoiceWxNativePrepayResult.java, v 0.1 2024-01-19 下午19:16 heb
 */
@Data
@Accessors(chain = true)
public class UserVoiceWxNativePrepayResult {

    /**
     * 订单ID
     */
    private Long          orderId;
    /**
     * 收银台过期时间（要设计为小于微信的有效期，微信的有效期是2小时）
     */
    private ZonedDateTime expireAt;
    /**
     * 扫码支付：预支付跳转链接（注意：不是二维码图片地址，而是二维码里的内容）
     */
    private String        codeUrl;
}
