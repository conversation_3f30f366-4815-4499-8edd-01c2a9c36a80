/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.videoshot.model.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version ShotsGenerateRequest.java, v 0.1 2024-09-19 下午7:19 zhoudong
 */
@Data
@Accessors(chain = true)
public class ShotsGenerateRequest {

    private String               text;
    private String               prompt;
    private String               productName;
    private String               productCategoryDesc;
    private String               crowdCategoryDesc;
    private List<VideoShotAsset> assets;
    /**
     * 是否插入数字人片段
     */
    private Boolean              avatarInsertable;
}
