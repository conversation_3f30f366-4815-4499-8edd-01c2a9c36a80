/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.caching.cacheable;

import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 缓存操作模板
 *
 * <AUTHOR>
 * @version CacheableTemplate.java, v 0.1 2024-01-23 下午8:30 zhoudong
 */
public interface CacheableTemplate<T> {

    /**
     * 缓存当前key到一个列表中
     *
     * @param listKey key列表的key
     * @return this
     */
    CacheableTemplate<T> pushKeyToList(String listKey);

    /**
     * 缓存当前key到一个列表中
     *
     * @param listKeyPrefix key列表的key前缀
     * @param argsGetter    key列表的key中的动态参数获取函数（根据返回结果动态获取）
     * @return this
     */
    CacheableTemplate<T> pushKeyToList(String listKeyPrefix, Function<T, Object> argsGetter);

    T get(Supplier<T> valueSupplier);

    Optional<T> optional(Supplier<Optional<T>> valueSupplier);
}
