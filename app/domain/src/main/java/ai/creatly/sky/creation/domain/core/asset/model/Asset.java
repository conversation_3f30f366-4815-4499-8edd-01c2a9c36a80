/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.model;

import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetBizType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetSourceType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetStatus;
import ai.creatly.sky.creation.domain.core.userasset.model.AssetContent;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 作品（主动上传 或 AI生成的）
 *
 * <AUTHOR>
 * @version Asset.java, v0.1 2025-02-19 19:29
 */
@Data
@Accessors(chain = true)
public class Asset {

    /**
     * 主键ID
     */
    private Long            id;
    /**
     * 创建时间
     */
    private ZonedDateTime   createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime   updatedAt;
    /**
     * 用户ID
     */
    private Long            uid;
    /**
     * 状态（无效即为软删除）
     */
    private AssetStatus     status;
    /**
     * 作品名称（默认取文件名，可修改）
     */
    private String          name;
    /**
     * 来源类型
     */
    private AssetSourceType sourceType;
    /**
     * 业务类型
     */
    private AssetBizType    bizType;
    /**
     * 封面图
     */
    private String          coverUrl;
    /**
     * 文件
     */
    private AssetFile       file;
    /**
     * 内容
     */
    private AssetContent    content;
    /**
     * 元数据
     */
    private AssetMetadata   metadata;
    /**
     * 标签列表
     */
    private List<String>    tags;

    private String          orgCode;

    /**
     * 是否属于某用户
     *
     * @param uid 用户ID
     * @return 是否属于
     */
    public boolean belongsTo(long uid) {
        return Objects.equals(this.getUid(), uid);
    }

    public AssetRef toRef() {
        return new AssetRef()
                .setId(id)
                .setFile(file)
                .setCoverUrl(coverUrl);
    }
}
