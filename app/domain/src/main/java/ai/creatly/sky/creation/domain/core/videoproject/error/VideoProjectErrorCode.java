/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version VideoProjectErrorCode.java, v 0.1 2024-09-21 下午3:36 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum VideoProjectErrorCode implements ErrorCode {

    VIDEO_PROJECT_NOT_FOUND("视频项目不存在"),
    AVATAR_NOT_FOUND("数字人形象不存在"),
    ASSET_FILE_TYPE_NOT_SUPPORT("资源文件类型不支持"),
    VIDEO_SYS_ASSET_PROCESS_FAIL("系统素材处理失败"),
    VIDEO_PROJECT_EDIT_NOT_ALLOWED("视频项目正在渲染中，不允许编辑"),
    VIDEO_PROJECT_DELETE_NOT_ALLOWED("视频项目正在渲染中，不允许删除"),

    VIDEO_ASSET_NOT_FOUND("视频素材不能为空"),
    VIDEO_ASSETS_SIZE_NOT_ENOUGH("素材数量不足，至少需要{}个"),
    VIDEO_ASSET_INVALID("素材已删除，请刷新页面再重试"),

    VIDEO_SCRIPT_NOT_EXISTS("视频脚本不存在"),
    VIDEO_SCRIPT_TEXT_OR_PROMPT_REQUIRED("视频脚本文本或提示词不能为空"),
    VIDEO_SCRIPT_PRODUCT_NOT_EXISTS("视频带货商品不能为空"),
    VIDEO_SCRIPT_PRODUCT_NAME_REQUIRED("视频脚本的产品名称不能为空"),
    VIDEO_SCRIPT_PRODUCT_CATEGORY_REQUIRED("视频脚本的产品类别不能为空"),
    VIDEO_SCRIPT_CROWD_CATEGORY_REQUIRED("视频脚本的人群类别不能为空"),

    VIDEO_SHOTS_UPDATE_EMPTY("视频分镜还未生成，无法更新"),
    VIDEO_SHOTS_UPDATE_SIZE_NOT_MATCH("视频分镜数量不能变更"),
    VIDEO_SHOTS_UPDATE_USER_ASSET_TO_SYS_ASSET_NOT_ALLOWED("更新分镜时，不允许将用户素材转换为系统素材"),
    VIDEO_SHOTS_NOT_EXISTS("视频分镜不存在"),
    VIDEO_SHOTS_TEXT_EMPTY("视频分镜文本不能为空"),
    VIDEO_SHOTS_DIRECT_SHOTS_MUST_BE_EMPTY("分镜已存在，不允许重复生成，请更新视频内容后再重试"),
    VIDEO_SHOTS_DIRECT_TIMEOUT("编导分镜超时"),
    VIDEO_SHOTS_DIRECT_NOT_ALLOWED("项目未处于编辑状态，不允许重新编导分镜"),
    VIDEO_SHOTS_ASSET_ID_REQUIRED("视频分镜素材ID不能为空"),

    VIDEO_VOICEOVER_NOT_EXISTS("视频配音不存在"),

    VIDEO_AVATAR_NOT_EXISTS("视频数字人不存在"),
    VIDEO_AVATAR_OUT_OF_CANVAS_BOUNDARY("视频数字人超出画布边界"),

    VIDEO_BACKGROUND_NOT_EXISTS("视频背景不存在"),

    VIDEO_PROJECT_COMPOSE_NOT_ALLOWED("视频正在生成中，不允许重复发起"),

    VIDEO_PROJECT_RENDER_NOT_ALLOWED("视频正在合成中，不允许重复发起"),

    DRAMA_ADS_VIDEO_DOES_NOT_SUPPORT_AVATAR("剧情广告片不支持数字人"),
    DRAMA_ADS_VIDEO_DOES_NOT_SUPPORT_SYS_ASSET("剧情广告片不支持系统素材"),
    DRAMA_ADS_VIDEO_DOES_NOT_SUPPORT_THE_SHOT_TYPE("剧情广告片不支持该分镜类型"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
