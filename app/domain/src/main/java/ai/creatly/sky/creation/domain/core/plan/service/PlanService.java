/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.service;

import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;

import java.util.List;
import java.util.Optional;

/**
 * 付费计划服务
 *
 * <AUTHOR>
 * @version PlanService.java, v 0.1 2023-10-15 下午11:36 zhoudong
 */
public interface PlanService {

    /**
     * 查询定价计划（含权益列表）
     *
     * @param planId 定价计划ID（平台的 or 私人定制的）
     * @return 定价计划（含权益列表）
     */
    Optional<Plan> queryByIdWithBenefits(String planId);

    /**
     * 查询平台定价计划列表（每个定价含权益列表）
     *
     * @param type 定价计划类型
     * @return 定价计划列表（每个定价含权益列表）
     */
    List<Plan> queryByTypeWithBenefits(PlanType type);

    /**
     * 查询平台+私人定价计划列表（每个定价含权益列表）
     *
     * @param type 付费计划类型
     * @param uid  用户ID
     * @return 付费计划列表（含权益列表）
     */
    List<Plan> queryByTypeWithBenefits(PlanType type, long uid);
}
