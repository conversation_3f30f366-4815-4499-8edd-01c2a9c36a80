/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.aichat.langchain.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version CopilotErrorCode.java, v 0.1 2023-10-08 11:38 syoka
 */
@Getter
@RequiredArgsConstructor
public enum CopilotErrorCode implements ErrorCode {

    NO_LLM_SERVER_IMPLEMENT_FOUND("未找到对应模型服务，请确认服务是否正常部署"),
    SCENARIO_NOT_EXISTS("提示词场景不存在"),
    TRANSLATE_PROMPT_FAIL("提示词翻译异常"),
    LANGUAGE_MODEL_INFER_ERROR("语言模型推理失败")
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
