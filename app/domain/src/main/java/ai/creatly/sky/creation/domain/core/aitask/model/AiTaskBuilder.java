/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.model;

import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import com.jspeeder.core.data.enums.ICode;
import com.jspeeder.core.model.OperatorRef;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.json.JSONObject;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version AiTaskBuilder.java, v 0.1 2024-03-01 上午12:41 zhoudong
 */
@RequiredArgsConstructor(access = AccessLevel.PACKAGE)
public class AiTaskBuilder {

    private final AiTask aiTask;

    public AiTaskStep1Builder owner(UserContext userContext) {
        aiTask.setOwnerId(userContext.getouid())
                .setOwnerName(userContext.getCurrentUsername())
                .setCreator(userContext.toOperatorRef());
        return new AiTaskStep1Builder(aiTask);
    }

    public AiTaskStep0Builder owner(Long ownerId, String ownerName) {
        aiTask.setOwnerId(ownerId)
                .setOwnerName(ownerName);
        return new AiTaskStep0Builder(aiTask);
    }

    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    public static class AiTaskStep0Builder {

        private final AiTask aiTask;

        public AiTaskStep1Builder creator(OperatorRef creator) {
            return new AiTaskStep1Builder(aiTask.setCreator(creator));
        }
    }

    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    public static class AiTaskStep1Builder {

        private final AiTask aiTask;

        public AiTaskStep2Builder taskType(AiTaskType taskType) {
            return new AiTaskStep2Builder(aiTask.setTaskType(taskType).setTaskName(taskType.getDesc()));
        }
    }

    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    public static class AiTaskStep2Builder {

        private final AiTask aiTask;

        public AiTaskStep3Builder bizType(ICode bizType) {
            String taskName = aiTask.getTaskName() + "-" + bizType.getDesc();
            return new AiTaskStep3Builder(aiTask.setBizType(bizType.getCode()).setTaskName(taskName));
        }

        public AiTaskStep3Builder bizType(String bizType) {
            return new AiTaskStep3Builder(aiTask.setBizType(bizType));
        }
    }

    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    public static class AiTaskStep3Builder {

        private final AiTask aiTask;

        public AiTaskStep4Builder bizNo(long bizNo) {
            return new AiTaskStep4Builder(aiTask.setBizNo(String.valueOf(bizNo)));
        }

        public AiTaskStep4Builder bizNo(String bizNo) {
            return new AiTaskStep4Builder(aiTask.setBizNo(bizNo));
        }
    }

    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    public static class AiTaskStep4Builder {

        private final AiTask aiTask;

        public AiTaskStep5Builder subBizNo(long subBizNo) {
            return new AiTaskStep5Builder(aiTask.setSubBizNo(String.valueOf(subBizNo)));
        }

        public AiTaskStep5Builder subBizNo(String subBizNo) {
            return new AiTaskStep5Builder(aiTask.setSubBizNo(subBizNo));
        }
    }

    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    public static class AiTaskStep5Builder {

        private final AiTask aiTask;

        public AiTaskStep6Builder bizInput(TaskBizInput bizInput) {
            return new AiTaskStep6Builder(aiTask.setBizInput(bizInput));
        }

        public AiTaskStep6Builder bizParams(TaskBizInput bizInput) {
            return new AiTaskStep6Builder(aiTask.setBizInput(bizInput));
        }

        public AiTaskStep6Builder bizParams(JSONObject bizParams) {
            return new AiTaskStep6Builder(aiTask.setBizParams(bizParams));
        }
    }

    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    public static class AiTaskStep6Builder {

        private final AiTask aiTask;

        public AiTaskStep6Builder bizStatus(@Nullable String bizStatus) {
            if (bizStatus != null) {
                aiTask.setBizStatus(bizStatus);
            }
            return this;
        }

        public AiTaskStep6Builder bizVars(@Nullable TaskBizVars bizVars) {
            if (bizVars != null) {
                aiTask.setBizExecInfo(bizVars);
            }
            return this;
        }

        public AiTaskStep6Builder notAutoExec() {
            aiTask.setAutoExec(false);
            return this;
        }

        public AiTaskStep6Builder creator(OperatorRef operator) {
            aiTask.setCreator(operator);
            return this;
        }

        public AiTaskStep6Builder estimatedDuration(Duration estimatedFinishedDuration) {
            aiTask.getSysParams().setEstimatedDuration(estimatedFinishedDuration);
            return this;
        }

        public AiTaskStep6Builder timeoutFromStartedAt(Duration timeout) {
            aiTask.getSysParams().setTimeoutFromStartedAt(timeout);
            return this;
        }

        public AiTaskStep6Builder timeoutFromCreatedAt(Duration timeout) {
            aiTask.getSysParams().setTimeoutFromCreatedAt(timeout);
            return this;
        }

        public AiTask build() {
            return aiTask;
        }
    }
}
