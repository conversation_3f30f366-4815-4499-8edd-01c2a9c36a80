/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.request;

import ai.creatly.sky.creation.domain.core.voice.model.enums.EmotionDegreeEnum;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechPitchType;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechRateType;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechVolumeType;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 声音文本合成请求
 *
 * <AUTHOR>
 * @version VoiceSynthesizeRequest.java, v 0.1 2023-11-04 下午8:00 zhoudong
 */
@Data
@Accessors(chain = true)
public class VoiceSynthesizeRequest {

    /**
     * 声音ID（通过查询声音列表API获取）
     */
    @NotBlank(message = "声音ID不能为空")
    @Size(max = 20, message = "声音ID格式错误")
    @Digits(integer = 20, fraction = 0, message = "声音ID格式错误")
    private String            voiceId;
    /**
     * 输入文本（在代码中去校验字数）
     *
     * @see ai.creatly.sky.creation.domain.common.util.TextUtil#WORD_PATTERN
     */
    @NotBlank(message = "输入文本不能为空")
    private String            text;
    /**
     * 口音编号（通过声音列表API获取），如果不传，则使用默认的
     */
    @Size(max = 32, message = "该声音不支持该本地化口音")
    private String            localeCode;
    /**
     * 扮演角色（通过声音列表API获取），如果不传，则使用默认的
     * <p/>
     * 注意：如果传了，则必须和 emotion 一起使用，否则不起作用。
     */
    @Size(max = 32, message = "该声音不支持扮演该角色")
    private String            roleCode;
    /**
     * 定制情绪（通过声音列表API获取）
     */
    @Size(max = 32, message = "该声音不支持使用该情绪")
    private String            emotionCode;
    /**
     * 情绪表现程度（默认中）
     */
    private EmotionDegreeEnum emotionDegree;
    /**
     * 调节音调
     */
    private SpeechPitchType   pitch;
    /**
     * 调节语速
     */
    private SpeechRateType    rate;
    /**
     * 调节音量
     */
    private SpeechVolumeType  volume;
}
