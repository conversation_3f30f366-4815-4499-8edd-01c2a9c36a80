/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户文件关联对象
 *
 * <AUTHOR>
 * @version FileRef.java, v 0.1 2024-05-05 下午10:17 zhoudong
 */
@Data
@Accessors(chain = true)
public class FileRef {

    /**
     * 文件ID
     */
    @JsonAlias("fileId")
    private String   id;
    /**
     * 文件地址（OSS协议）
     */
    @JsonAlias("fileUrl")
    private String url;
}
