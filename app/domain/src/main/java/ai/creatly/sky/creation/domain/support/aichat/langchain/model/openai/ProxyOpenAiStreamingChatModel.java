/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.aichat.langchain.model.openai;

import dev.ai4j.openai4j.OpenAiClient;
import dev.ai4j.openai4j.chat.*;
import dev.langchain4j.agent.tool.ToolSpecification;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.model.StreamingResponseHandler;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.openai.InternalOpenAiHelper;
import dev.langchain4j.model.openai.OpenAiStreamingResponseBuilder;
import dev.langchain4j.model.openai.OpenAiTokenizer;
import dev.langchain4j.model.output.Response;
import lombok.Builder;
import lombok.Data;

import java.net.Proxy;
import java.time.Duration;
import java.util.Collections;
import java.util.List;

@Data
public class ProxyOpenAiStreamingChatModel implements StreamingChatLanguageModel {

    static final String GPT_4_O = "gpt-4o";

    private OpenAiClient    client;
    private String          modelName;
    private Double          temperature;
    private Double          topP;
    private Integer         maxTokens;
    private Double          presencePenalty;
    private Double          frequencyPenalty;
    private OpenAiTokenizer tokenizer;
    private ResponseFormatType responseFormatType;


    @Builder
    public ProxyOpenAiStreamingChatModel(
            String apiKey,
            String modelName,
            Double temperature,
            Double topP,
            Integer maxTokens,
            Double presencePenalty,
            Double frequencyPenalty,
            Duration timeout,
            Boolean logRequests,
            Boolean logResponses,
            ResponseFormatType responseType,
            Proxy proxy) {

        modelName = modelName == null ? GPT_4_O : modelName;
        temperature = temperature == null ? 1.0 : temperature;
        timeout = timeout == null ? Duration.ofSeconds(5) : timeout;

        this.client = OpenAiClient.builder()
                .openAiApiKey(apiKey)
                .callTimeout(timeout)
                .connectTimeout(timeout)
                .readTimeout(timeout)
                .writeTimeout(timeout)
                .logRequests(logRequests)
                .logResponses(logResponses)
                .proxy(proxy)
                .build();
        this.modelName = modelName;
        this.temperature = temperature;
        this.topP = topP;
        this.maxTokens = maxTokens;
        this.presencePenalty = presencePenalty;
        this.frequencyPenalty = frequencyPenalty;
        this.responseFormatType = responseType;
        this.tokenizer = new OpenAiTokenizer(this.modelName);
    }

    @Override
    public void generate(List<ChatMessage> messages, StreamingResponseHandler<AiMessage> handler) {
        generate(messages, null, null, handler);
    }

    @Override
    public void generate(List<ChatMessage> messages, List<ToolSpecification> toolSpecifications, StreamingResponseHandler<AiMessage> handler) {
        generate(messages, toolSpecifications, null, handler);
    }

    @Override
    public void generate(List<ChatMessage> messages, ToolSpecification toolSpecification, StreamingResponseHandler<AiMessage> handler) {
        generate(messages, Collections.singletonList(toolSpecification), toolSpecification, handler);
    }

    private void generate(List<ChatMessage> messages,
                          List<ToolSpecification> toolSpecifications,
                          ToolSpecification toolThatMustBeExecuted,
                          StreamingResponseHandler<AiMessage> handler
    ) {
        ChatCompletionRequest.Builder requestBuilder = ChatCompletionRequest.builder()
                .stream(true)
                .model(modelName)
                .messages(InternalOpenAiHelper.toOpenAiMessages(messages))
                .temperature(temperature)
                .topP(topP)
                .responseFormat(this.responseFormatType)
                .maxTokens(maxTokens);

        if (toolSpecifications != null && !toolSpecifications.isEmpty()) {
            requestBuilder.tools(InternalOpenAiHelper.toTools(toolSpecifications));
        }
        if (toolThatMustBeExecuted != null) {
            requestBuilder.toolChoice(toolThatMustBeExecuted.name());
        }

        ChatCompletionRequest request = requestBuilder.build();

        OpenAiStreamingResponseBuilder responseBuilder = new OpenAiStreamingResponseBuilder(0);

        client.chatCompletion(request)
                .onPartialResponse(partialResponse -> {
                    responseBuilder.append(partialResponse);
                    handle(partialResponse, handler);
                })
                .onComplete(() -> {
                    Response<AiMessage> response = responseBuilder.build(null, false);
                    handler.onComplete(response);
                })
                .onError(handler::onError)
                .execute();
    }

    private static void handle(ChatCompletionResponse partialResponse, StreamingResponseHandler<AiMessage> handler) {
        List<ChatCompletionChoice> choices = partialResponse.choices();
        if (choices == null || choices.isEmpty()) {
            return;
        }
        Delta delta = choices.getFirst().delta();
        String content = delta.content();
        if (content != null) {
            handler.onNext(content);
        }
    }
}
