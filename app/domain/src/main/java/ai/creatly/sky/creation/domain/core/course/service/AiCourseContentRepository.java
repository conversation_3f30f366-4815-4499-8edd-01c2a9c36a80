/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.course.service;

import ai.creatly.sky.creation.domain.core.course.model.AiCourseContent;

import java.util.List;

/**
 * <AUTHOR>
 * @version AiToolRepository.java, v0.1 2025-02-19 21:17
 */
public interface AiCourseContentRepository {

    List<AiCourseContent> queryCourseContentList(Long courseId);

    AiCourseContent addCourseContent(AiCourseContent aiCourseContent);

    AiCourseContent updateCourseContent(AiCourseContent aiCourseContent);

    AiCourseContent queryById(Long courseContentId);

}
