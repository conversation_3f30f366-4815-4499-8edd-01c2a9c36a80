/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.credit.cost.model.request;

import ai.creatly.sky.creation.domain.core.credit.cost.model.UnitsName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version CreditCostRuleSaveRequest.java, v 0.1 2023-12-01 23:26 heb
 */
@Data
@Accessors(chain = true)
public class CreditCostRuleSaveRequest {
    /**
     * 消耗值
     */
    private Long      cost;
    /**
     * 度量单位
     */
    private UnitsName unitsName;
    /**
     * 度量数量
     */
    private Integer   unitsAmount;
    /**
     * 任务标识符
     */
    private String    taskType;
    /**
     * 任务类型
     */
    private String    taskBizType;
}
