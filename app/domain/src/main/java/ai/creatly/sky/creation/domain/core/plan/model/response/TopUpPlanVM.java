/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 充值计划
 *
 * <AUTHOR>
 * @version TopUpPlanVM.java, v 0.1 2023-10-08 下午9:42 zhoudong
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TopUpPlanVM extends BasePlanVM {

    /**
     * 充值金额（元）
     */
    private String realFee;
    /**
     * 充值获得余额
     */
    private Integer credits;
    /**
     * 赠送余额
     */
    private Integer giftCredits;
}
