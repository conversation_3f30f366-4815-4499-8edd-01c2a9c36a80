/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.reponse;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version VideoRenderConsumptionVM.java, v 0.1 2024-09-24 下午8:02 zhoudong
 */
@Data
@Accessors(chain = true)
public class VideoRenderConsumptionVM {

    /**
     * 预计消费余额
     */
    private Integer unpaidCredits;
    /**
     * 视频时长(带小数)
     */
    private String  videoSeconds;
}
