/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.mapper;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.asset.model.*;
import ai.creatly.sky.creation.domain.core.asset.model.response.AssetMetadataVM;
import ai.creatly.sky.creation.domain.core.asset.model.response.AssetVM;
import ai.creatly.sky.creation.domain.core.asset.model.response.GenerationTimeVM;
import ai.creatly.sky.creation.domain.core.userfile.mapper.UserFileMapper;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import lombok.Setter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version AssetMapper.java, v0.1 2025-02-19 21:03
 */
@Mapper(config = BaseMapperConfig.class, uses = UserFileMapper.class)
public abstract class AssetMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    public List<OrderedAssetRef> toOrderedAssetRefs(List<Asset> assets) {
        List<OrderedAssetRef> orderedAssets = new ArrayList<>(assets.size());
        for (int i = 0; i < assets.size(); i++) {
            OrderedAssetRef orderedAsset = this.toOrderedAssetRef(i + 1, assets.get(i));
            orderedAssets.add(orderedAsset);
        }
        return orderedAssets;
    }

    public OrderedAssetRef toOrderedAssetRef(int order, Asset asset) {
        return this.toOrderedAssetRef(order, asset.toRef());
    }

    public abstract OrderedAssetRef toOrderedAssetRef(int order, AssetRef asset);

    public AssetVM toAssetVM(Asset asset) {
        if (asset.getCoverUrl().startsWith("oss:")) {
            String coverUrl = userFileHelper.getHttpUrl(asset.getCoverUrl(), FileAcl.PRIVATE);
            return this.toAssetVM(asset, coverUrl);
        } else {
            return this.toAssetVM(asset, asset.getCoverUrl());
        }
    }

    @Mapping(target = "coverUrl", source = "coverUrl")
    protected abstract AssetVM toAssetVM(Asset asset, String coverUrl);

    public AssetFileVM toAssetFileVM(AssetFile assetFile) {
        String url = userFileHelper.getHttpUrl(assetFile.getUrl(), FileAcl.PRIVATE);
        return this.toAssetFileVM(assetFile, url);
    }

    @Mapping(target = "url", source = "url")
    protected abstract AssetFileVM toAssetFileVM(AssetFile assetFile, String url);

    protected abstract AssetMetadataVM toAssetMetadataVM(AssetMetadata metadata);

    protected abstract GenerationTimeVM toGenerationTimeVM(GenerationTime generationTime);
}
