/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.audio.task;

import ai.creatly.sky.creation.domain.core.ai.audio.AiAudioMusicClient;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiMusicTaskInput;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiMusicTaskResult;
import ai.creatly.sky.creation.domain.core.ai.audio.model.AiMusicTaskVars;
import ai.creatly.sky.creation.domain.core.ai.image.task.AiImageTaskBizType;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskBizExecStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskHelper;
import ai.creatly.sky.creation.domain.core.asset.model.Asset;
import ai.creatly.sky.creation.domain.core.asset.model.AssetFile;
import ai.creatly.sky.creation.domain.core.asset.model.AssetMetadata;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetBizType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetSourceType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetStatus;
import ai.creatly.sky.creation.domain.core.asset.service.AssetRepository;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsRefund;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsFactory;
import ai.creatly.sky.creation.domain.core.credit.service.UserCreditDomainService;
import ai.creatly.sky.creation.domain.core.userasset.model.AssetContent;
import ai.creatly.sky.creation.domain.core.userfile.model.FileRef;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.ImageFileService;
import ai.creatly.sky.creation.domain.core.userfile.service.impl.AudioFileServiceImpl;
import ai.creatly.sky.creation.domain.core.userfile.service.impl.UserFileServiceImpl;
import com.jspeeder.core.data.id.IdHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;

/**
 * <AUTHOR>
 * @version AudioMusicTaskHandler.java, v0.1 2025-02-28 13:41
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AudioMusicTaskHandler implements AiTaskHandler {

    @Autowired
    private AiAudioMusicClient        aiAudioMusicClient;

    @Autowired
    protected ImageFileService        imageFileService;
    @Autowired
    protected AiTaskHelper            aiTaskHelper;
    @Autowired
    protected UserFileRepository      userFileRepository;
    @Autowired
    protected CreditsFactory          creditsFactory;
    @Autowired
    protected UserCreditDomainService userCreditDomainService;
    @Autowired
    protected UserFileServiceImpl     userFileService;
    @Autowired
    protected AudioFileServiceImpl    audioFileService;
    @Autowired
    private   AssetRepository         assetRepository;


    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.AI_AUDIO)
                .setBizType(AiImageTaskBizType.audio_music)
                .setLoadSize(1)
                .setQueueMode(TaskQueueMode.POP_PER_EXECUTED)
                .setExecCountAlertThreshold(Integer.MAX_VALUE)
                .setNotifyOnSubmit(false);
    }

    @Override
    public TaskPreAction preHandle(AiTask aiTask) {
        log.info("AI音乐，preHandle开始执行request:{}", aiTask.getBizParams());
        var input = aiTask.parseBizInput(AiMusicTaskInput.class);
        UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);
        AiMusicTaskResult bizResult = aiAudioMusicClient.generate(input, userContext);
        var taskVars = new AiMusicTaskVars().setData(new JSONObject(bizResult.getData())).setTaskId(bizResult.getTaskId());
        if (bizResult.getBizExecStatus() == AiTaskBizExecStatus.FAILED) {
            return TaskPreAction.FORWARD_CANCELED.updateBizVars(taskVars);
        }
        return TaskPreAction.FORWARD_RUNNING.updateBizVars(taskVars);
    }

    @Override
    public TaskAction handle(AiTask aiTask) {
        //1、查询生成状态
        var input = aiTask.parseBizInput(AiMusicTaskInput.class);
        UserContext userContext = aiTaskHelper.getOwnerUserContext(aiTask);
        AiMusicTaskResult result = aiAudioMusicClient.queryGeneration(aiTask, userContext);

        // 2、生成中/失败直接结束
        if (result == null || result.getBizExecStatus() == AiTaskBizExecStatus.PROCESSING) {
            return TaskAction.KEEP_STILL.updateBizResult(result);
        } else if (result.getBizExecStatus() == AiTaskBizExecStatus.FAILED) {
            return TaskAction.FORWARD_CANCELED.updateBizResult(result);
        }
        return TaskAction.FORWARD_FINISHED.updateBizResult(result);
    }

    @Override
    public TaskPostAction postHandle(AiTask aiTask) {
        //任务取消则消耗元气恢复
        if (aiTask.getStatus() == AiTaskStatus.CANCELED) {
            CreditsRefund creditsRefund = CreditsRefund.builder()
                    .uid(aiTask.getOwnerId())
                    .bizType(CreditLogBizType.AUDIO_MUSIC)
                    .bizNo(aiTask.getId().toString())
                    .allowExpenseAbsent(false)
                    .build();
            userCreditDomainService.refundCredits(creditsRefund);
        }
        return TaskPostAction.COMPLETE;
    }

    private Asset buildAsset(AiMusicTaskInput taskInput, UserFile userFile,  AiTask  aiTask) {
        var metadata = new AssetMetadata()
                .setAttachments(Collections.singletonList(new FileRef()))
                .setTaskId(aiTask.getId())
                .setBizParams(aiTask.getBizParams());

        return new Asset()
                .setId(IdHelper.getId())
                .setUid(userFile.getUid())
                .setStatus(AssetStatus.VALID)
                .setName(userFile.getOriginalFilename())
                .setSourceType(AssetSourceType.generation)
                .setBizType(AssetBizType.audio_music)
                .setCoverUrl("")
                .setFile(AssetFile.fromUserFile(userFile))
                .setContent(new AssetContent().setMd5(userFile.getMd5()))
                .setMetadata(metadata)
                .setTags(new ArrayList<>());
    }
}
