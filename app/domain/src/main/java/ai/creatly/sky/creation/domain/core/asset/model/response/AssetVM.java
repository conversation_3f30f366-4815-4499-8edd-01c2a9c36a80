/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.model.response;

import ai.creatly.sky.creation.domain.core.asset.mapper.AssetFileVM;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetBizType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetSourceType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetStatus;
import lombok.Data;

import java.util.List;

/**
 * 作品
 *
 * <AUTHOR>
 * @version AssetVM.java, v0.1 2025-02-19 21:02
 */
@Data
public class AssetVM {

    /**
     * 主键ID
     */
    private String          id;
    /**
     * 状态（无效即为软删除）
     */
    private AssetStatus     status;
    /**
     * 作品名称（默认取文件名，可修改）
     */
    private String          name;
    /**
     * 来源类型
     */
    private AssetSourceType sourceType;
    /**
     * 业务类型
     */
    private AssetBizType    bizType;
    /**
     * 封面图
     */
    private String          coverUrl;
    /**
     * 文件
     */
    private AssetFileVM     file;
    /**
     * 元数据
     */
    private AssetMetadataVM metadata;
    /**
     * 标签列表
     */
    private List<String>    tags;
}
