/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.organization.mapper;

import ai.creatly.sky.creation.domain.core.organization.model.Organization;
import ai.creatly.sky.creation.domain.core.organization.model.response.OrganizationVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version OrganizationMapper.java, v 0.1 2024-01-22 16:06 syoka
 */
@Mapper(config = BaseMapperConfig.class)
public interface OrganizationVMMapper {

    @Mapping(target = "userInfo", ignore = true)
    OrganizationVM toOrganizationVM(Organization organization);

    List<OrganizationVM> toOrganizationVM(List<Organization> organizationList);
}
