/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.http;

import com.jspeeder.core.util.json.JSON;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 * 请求转发客户端
 *
 * <AUTHOR>
 * @version RequestForwardClient.java, v 0.1 2024-10-15 下午12:47 zhoudong
 */
public interface RequestForwardClient {

    @Nullable
    default String postJsonBody(String endpoint, String path, @Nullable String query, @Nullable Object body) {
        LinkedMultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("content-type", "application/json");
        String jsonBody = body == null ? null : JSON.toJSONString(body);
        return this.post(endpoint, path, query, headers, jsonBody);
    }

    @Nullable
    String post(String endpoint, String path, @Nullable String query, @Nullable MultiValueMap<String, String> headers,
                @Nullable String body);
}
