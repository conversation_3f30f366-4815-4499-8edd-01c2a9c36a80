/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.request;

import lombok.Data;

/**
 * 付费计划微信扫码支付请求
 *
 * <AUTHOR>
 * @version PlanWxNativePayRequest.java, v 0.1 2023-10-12 上午9:25 zhoudong
 */
@Data
public class PlanWxNativePayRequest {
    /**
     * 到期是否自动续费（当付款计划上没有，且用户自行勾选时需传）
     */
    private Boolean autoRenewed;
}
