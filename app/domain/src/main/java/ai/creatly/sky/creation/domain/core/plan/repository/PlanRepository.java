/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.repository;

import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;

import java.util.List;
import java.util.Optional;

/**
 * 付费计划持久化服务
 *
 * <AUTHOR>
 * @version PlanRepository.java, v 0.1 2023-10-10 下午10:07 zhoudong
 */
public interface PlanRepository {

    /**
     * 查询付费计划
     *
     * @param id 计划ID
     * @return 付费计划
     */
    Optional<Plan> queryOptionalById(String id);

    /**
     * 查询付费计划列表
     *
     * @param type 计划类型
     * @return 付费计划列表
     */
    List<Plan> queryByType(PlanType type);
}
