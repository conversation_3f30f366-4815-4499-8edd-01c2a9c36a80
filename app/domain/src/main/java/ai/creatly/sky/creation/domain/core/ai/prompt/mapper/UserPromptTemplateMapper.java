/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.prompt.mapper;

import ai.creatly.sky.creation.domain.core.ai.prompt.model.UserPromptTemplate;
import ai.creatly.sky.creation.domain.core.ai.prompt.model.response.UserPromptTemplateVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version PromptTemplateMapper.java, v0.1 2025-02-19 21:14
 */
@Mapper(config = BaseMapperConfig.class)
public interface UserPromptTemplateMapper {

    List<UserPromptTemplateVM> toPromptTemplateVMs(List<UserPromptTemplate> promptTemplates);

    UserPromptTemplateVM toPromptTemplateVM(UserPromptTemplate promptTemplate);
}
