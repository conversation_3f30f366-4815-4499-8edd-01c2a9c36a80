/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.product.service;

import ai.creatly.sky.creation.domain.core.product.error.UserProductErrorCode;
import ai.creatly.sky.creation.domain.core.product.model.SavingVideoProduct;
import ai.creatly.sky.creation.domain.core.product.model.UserProduct;
import com.jspeeder.core.data.problem.exception.SysException;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version UserProductService.java, v 0.1 2024-10-16 下午5:06 zhoudong
 */
public interface UserProductService {

    default UserProduct queryWithAssetsById(long id) {
        return queryOptionalWithAssetsById(id).orElseThrow(() -> new SysException(UserProductErrorCode.PRODUCT_NOT_FOUND));
    }

    Optional<UserProduct> queryOptionalWithAssetsById(long id);

    long createProduct(SavingVideoProduct videoProduct, long uid);

    void updateProduct(long id, SavingVideoProduct videoProduct, long uid);
}
