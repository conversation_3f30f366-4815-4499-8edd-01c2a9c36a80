/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.image.client;

import ai.creatly.sky.creation.domain.core.aiproduct.image.client.model.AiImageImagineRequest;
import ai.creatly.sky.creation.domain.core.aiproduct.image.client.model.ImageBgRemoveRequest;

/**
 * <AUTHOR>
 * @version AiProductImageClient.java, v 0.1 2024-02-27 下午10:31 zhoudong
 */
public interface AiProductImageClient {

    /**
     * 图片去除背景
     *
     * @param request -
     */
    boolean removeBg(ImageBgRemoveRequest request);

    /**
     * 图文生图（商品图片 + 场景提示词 => 生成图片）
     *
     * @param request 请求参数
     * @return 是否成功
     */
    boolean imagine(AiImageImagineRequest request);
}
