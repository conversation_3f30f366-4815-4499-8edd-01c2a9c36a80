/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.service.impl;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.common.integration.oss.FileProcessOption;
import ai.creatly.sky.creation.domain.common.integration.oss.OssTemplate;
import ai.creatly.sky.creation.domain.common.integration.oss.OssUtil;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.core.userfile.service.VideoFileService;
import ai.creatly.sky.creation.domain.core.userfile.service.builder.VideoFileBuilder;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.file.HttpFileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version VideoFileServiceImpl.java, v 0.1 2024-09-27 下午4:10 zhoudong
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VideoFileServiceImpl implements VideoFileService {

    private final VideoFileBuilder videoFileBuilder;
    private final UserFileService  userFileService;
    private final OssTemplate      ossTemplate;

    @Override
    public UserFile upload(String httpUrl, long fileId, FileBizSource bizSource, UserContext userContext) {
        log.info("[upload]开始下载视频,fileId:{},source={}", fileId, bizSource);
        long startTime = System.currentTimeMillis();
        byte[] videoData;
        try {
            videoData = HttpFileUtil.downloadAsBytes(httpUrl);
        } catch (IOException e) {
            log.error("[upload]下载视频失败,fileId:{},source={}", fileId, bizSource, e);
            throw new SysException("视频文件下载失败", e);
        }

        log.info("[upload]开始上传视频,fileId:{},source={}", fileId, bizSource);
        UserFile file = videoFileBuilder.build(fileId, videoData, bizSource, userContext);
        userFileService.upload(videoData, file);
        log.info("[upload]下载并上传视频成功,fileId:{},source={},耗时:{}ms", fileId, bizSource, System.currentTimeMillis() - startTime);
        return file;
    }

    @Override
    public String clipCoverImageUrl(String bucket, String sourceKey, FileAcl targetAcl, int seconds) {
        // 视频截图，取某一秒位置的图片
        String targetKey = OssUtil.renameKey(sourceKey, "_clip_t_" + seconds + "s", "jpg");
        if (ossTemplate.isFilePresent(bucket, targetKey)) {
            return OssUtil.toOssUrl(bucket, targetKey);
        }

        FileProcessOption processOption = OssUtil.videoSnapshot(seconds);
        ossTemplate.processObject(bucket, sourceKey, targetKey, targetAcl, processOption.getStyle());
        Asserts.isTrue(ossTemplate.isFilePresent(bucket, targetKey), "clip cover image failed");
        return OssUtil.toOssUrl(bucket, targetKey);
    }
}
