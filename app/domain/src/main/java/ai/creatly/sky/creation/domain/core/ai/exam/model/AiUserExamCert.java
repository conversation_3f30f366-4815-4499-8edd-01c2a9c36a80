/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.exam.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 考试证书
 *
 * <AUTHOR>
 * @version AiUserExamCert.java
 */
@Data
@Accessors(chain = true)
public class AiUserExamCert {
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;
    private String coverUrl;
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long certId;
    private String name;
    private Long score;
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long uid;
    private String orgCode;
    private Long limitExam;
}
