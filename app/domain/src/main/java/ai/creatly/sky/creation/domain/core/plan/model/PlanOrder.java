/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model;

import ai.creatly.kylin.trade.sdk.order.enums.OrderStatus;
import ai.creatly.kylin.trade.sdk.payment.enums.PaymentChannel;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;

/**
 * 付费计划订单
 *
 * <AUTHOR>
 * @version PlanOrder.java, v 0.1 2023-10-10 下午10:25 zhoudong
 */
@Data
@Accessors(chain = true)
public class PlanOrder {

    public final static String ITEM_EXT_KEY_AUTO_RENEWED     = "autoRenewed";
    public final static String ITEM_EXT_KEY_AUTO_MEMBER_TYPE = "memberType";
    public final static String ITEM_EXT_KEY_AUTO_BENEFITS    = "benefits";

    /**
     * 订单ID
     */
    private Long           id;
    /**
     * 订单业务号
     */
    private String         bizSn;
    /**
     * 订单标题
     */
    private String         title;
    /**
     * 用户ID
     */
    private Long           uid;
    /**
     * 订单原费用（分）
     */
    private Long           originalFee;
    /**
     * 订单实际费用（分）
     */
    private Long           realFee;
    /**
     * 已支付费用（分）
     */
    private Long           paidFee;
    /**
     * 订单状态
     */
    private OrderStatus    status;
    /**
     * 支付渠道
     */
    private PaymentChannel paymentChannel;
    /**
     * 支付时间
     */
    @Nullable
    private ZonedDateTime  paidTime;
    /**
     * 已支付的付费计划（可能会和最新的付费计划的数据有出入，比如价格、赠送的余额等）
     */
    private Plan           paidPlan;
}
