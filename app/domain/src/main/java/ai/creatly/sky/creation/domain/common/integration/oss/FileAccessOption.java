package ai.creatly.sky.creation.domain.common.integration.oss;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Instant;

/**
 * 文件访问选项
 *
 * <AUTHOR>
 * @version FileAccessOption.java, v 0.1 2023-07-01 16:38 joton
 */
@Data(staticConstructor = "of")
@Accessors(chain = true)
public class FileAccessOption {

    /**
     * 文件访问控制
     */
    private final FileAcl acl;
    /**
     * 当文件为私有读时，必须指定过期时间
     */
    private Instant expiredTime;
}
