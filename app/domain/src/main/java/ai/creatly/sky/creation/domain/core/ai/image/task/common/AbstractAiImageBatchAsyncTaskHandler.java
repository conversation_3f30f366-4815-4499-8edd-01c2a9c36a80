/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.task.common;

import ai.creatly.sky.creation.domain.core.ai.image.task.common.model.AiImageAsyncTaskVars;
import ai.creatly.sky.creation.domain.core.ai.image.task.common.model.AiImageTaskResult;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.post.TaskPostAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.pre.TaskPreAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.transaction.AiTaskTransactionManager;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.service.AiTaskHelper;
import ai.creatly.sky.creation.domain.core.asset.mapper.AssetMapper;
import ai.creatly.sky.creation.domain.core.asset.model.Asset;
import ai.creatly.sky.creation.domain.core.asset.service.AssetRepository;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.repository.UserFileRepository;
import ai.creatly.sky.creation.domain.core.userfile.service.ImageFileService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 异步生成多图
 *
 * <AUTHOR>
 * @version AbstractAiImageBatchAsyncTaskHandler.java, v0.1 2025-03-05 11:47
 */
public abstract class AbstractAiImageBatchAsyncTaskHandler implements AiTaskHandler {

    @Autowired
    protected ImageFileService   imageFileService;
    @Autowired
    protected AiTaskHelper       aiTaskHelper;
    @Autowired
    protected UserFileRepository userFileRepository;
    @Autowired
    protected AssetRepository    assetRepository;
    @Autowired
    protected AssetMapper        assetMapper;

    @Override
    public TaskPreAction preHandle(AiTask aiTask) {
        String remoteTaskId = this.submitGenerationTask(aiTask);
        var taskVars = new AiImageAsyncTaskVars().setRemoteTaskId(remoteTaskId);
        return TaskPreAction.FORWARD_RUNNING.updateBizVars(taskVars);
    }

    protected abstract String submitGenerationTask(AiTask aiTask);

    @Override
    public TaskAction handle(AiTask aiTask) {
        var userContext = aiTaskHelper.getOwnerUserContext(aiTask);

        List<UserFile> imageFiles = this.queryGeneratedImages(aiTask, userContext);
        if (imageFiles == null || imageFiles.isEmpty()) {
            return TaskAction.KEEP_STILL;
        }

        List<Asset> assets = this.convertToAssets(aiTask, imageFiles);
        AiTaskTransactionManager.registerSynchronization(() -> {
            userFileRepository.batchCreate(imageFiles);
            assetRepository.batchCreate(assets);
        });

        var result = new AiImageTaskResult().setAssets(assetMapper.toOrderedAssetRefs(assets));
        return TaskAction.FORWARD_FINISHED.updateBizResult(result);
    }

    protected abstract List<UserFile> queryGeneratedImages(AiTask aiTask, UserContext userContext);

    protected abstract List<Asset> convertToAssets(AiTask aiTask, List<UserFile> imageFiles);

    @Override
    public TaskPostAction postHandle(AiTask aiTask) {
        return TaskPostAction.COMPLETE;
    }
}
