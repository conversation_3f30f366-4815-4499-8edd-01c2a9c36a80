/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.service.impl;

import ai.creatly.sky.creation.domain.common.integration.azure.model.SynthesisVoiceNameEnum;
import ai.creatly.sky.creation.domain.common.util.TextUtil;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.error.FileErrorCode;
import ai.creatly.sky.creation.domain.core.userfile.model.HttpFile;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileService;
import ai.creatly.sky.creation.domain.core.voice.error.VoiceErrorCode;
import ai.creatly.sky.creation.domain.core.voice.mapper.VoiceCreationMapper;
import ai.creatly.sky.creation.domain.core.voice.mapper.VoiceSynthesisMapper;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceEmotion;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceLocale;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceRole;
import ai.creatly.sky.creation.domain.core.voice.model.enums.*;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceContourPreviewRequest;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceCreationRequest;
import ai.creatly.sky.creation.domain.core.voice.model.request.VoiceSynthesizeRequest;
import ai.creatly.sky.creation.domain.core.voice.model.request.content.segment.VoiceProsodyDTO;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.VoiceCreationContent;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.VoiceDialogue;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment.VoiceSegment;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment.VoiceSegmentType;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment.prosody.IntonationContourPoint;
import ai.creatly.sky.creation.domain.core.voice.repository.VoiceCacheableRepository;
import ai.creatly.sky.creation.domain.core.voice.service.VoiceChecker;
import ai.creatly.sky.creation.domain.core.voice.util.VoiceUtil;
import ai.creatly.sky.creation.domain.support.speech.model.request.SpeechSynthesisRequest;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version VoiceCheckerImpl.java, v 0.1 2024-08-19 下午3:10 zhoudong
 */
@Component
@RequiredArgsConstructor
public class VoiceCheckerImpl implements VoiceChecker {

    private final VoiceCacheableRepository voiceCacheableRepository;
    private final UserFileService          userFileService;
    private final VoiceSynthesisMapper     voiceSynthesisMapper;
    private final VoiceCreationMapper      voiceCreationMapper;

    @Override
    public VoiceDialogue checkSimpleSynthesis(SpeechSynthesisRequest request) {
        // 文本字数校验
        String text = request.getText();
        Validates.isTrue(TextUtil.countWords(text) > 0, "输入文本不存在有效内容");
        Validates.isTrue(TextUtil.countWords(text) <= 750, "输入文本不能超过750字");

        // 声音校验
        SynthesisVoiceNameEnum voiceName = SynthesisVoiceNameEnum.getByName(request.getVoiceName());
        Validates.notNull(voiceName, VoiceErrorCode.VOICE_NOT_FOUND);
        Voice voice = voiceName.buildVoice();
        VoiceLocale voiceLocale = this.synthesizeLocaleCheck(voice, request);

        // 文本内容校验
        this.checkTextLang(voiceLocale, text);

        return voiceSynthesisMapper.toVoiceDialogue(voice, request);
    }

    @Override
    public VoiceDialogue checkSimpleSynthesis(VoiceContourPreviewRequest request) {
        Voice voice = this.checkSimpleSynthesis(request, 50).getVoice();
        // 构造语音创作对话
        return voiceCreationMapper.toVoiceDialogue(request, voice);
    }

    @Override
    public VoiceDialogue checkSimpleSynthesis(VoiceSynthesizeRequest request, int maxWords) {
        // 参数矫正
        if (VoiceRole.DEFAULT_CODE.equals(request.getRoleCode())) {
            request.setRoleCode(null);
        }
        if (VoiceEmotion.DEFAULT_CODE.equals(request.getEmotionCode())) {
            request.setEmotionCode(null);
        }
        if (request.getEmotionCode() == null) {
            request.setEmotionDegree(null);
        }
        if (request.getPitch() == SpeechPitchType.medium) {
            request.setPitch(null);
        }
        if (request.getRate() == SpeechRateType.RATE_100) {
            request.setRate(null);
        }
        if (request.getVolume() == SpeechVolumeType.medium) {
            request.setVolume(null);
        }

        // 文本字数校验
        String text = request.getText();
        this.checkTextLength(text, maxWords);

        // 声音校验
        Voice voice = this.queryCheckedVoice(request.getVoiceId());
        VoiceLocale voiceLocale = this.synthesizeLocaleCheck(voice, request);

        // 文本内容校验
        this.checkTextLang(voiceLocale, text);

        return voiceSynthesisMapper.toVoiceDialogue(voice, request);
    }

    private void checkTextLength(String text, int maxWords) {
        Validates.isTrue(TextUtil.countWords(text) > 0, "输入文本不存在有效内容");
        Validates.isTrue(TextUtil.countWords(text) <= maxWords, "输入文本不能超过{}字", maxWords);
    }

    private Voice queryCheckedVoice(String voiceId) {
        Voice voice = voiceCacheableRepository.queryOptionalById(Validates.requireLong(voiceId, "voiceId")).orElse(null);
        Validates.notNull(voice, VoiceErrorCode.VOICE_NOT_FOUND);
        return voice;
    }

    @Override
    public VoiceDialogue checkSimpleSynthesis(long voiceId, String text, String emotionCode, EmotionDegreeEnum emotionDegree,
                                              SpeechRateType rate) {
        Voice voice = voiceCacheableRepository.queryOptionalById(voiceId).orElse(null);
        Validates.notNull(voice, VoiceErrorCode.VOICE_NOT_FOUND);

        this.checkTextLength(text, 100);
        VoiceLocale voiceLocale = this.synthesizeLocaleCheck(voice, null, null, emotionCode);

        this.checkTextLang(voiceLocale, text);

        // 构造语音合成内容
        return new VoiceDialogue()
                .setVoice(voice)
                .setEmotionCode(emotionCode)
                .setEmotionDegree(emotionDegree)
                .setRate(rate)
                .setSegments(List.of(new VoiceSegment().setType(VoiceSegmentType.TEXT).setText(text)));
    }

    @Override
    public VoiceCreationContent checkCreation(VoiceCreationRequest request, UserContext userContext) {
        // 参数矫正
        request.getDialogues().forEach(voiceDialogue -> {
            if (VoiceRole.DEFAULT_CODE.equals(voiceDialogue.getRoleCode())) {
                voiceDialogue.setRoleCode(null);
            }
            if (VoiceEmotion.DEFAULT_CODE.equals(voiceDialogue.getEmotionCode())) {
                voiceDialogue.setEmotionCode(null);
            }
            if (voiceDialogue.getEmotionCode() == null) {
                voiceDialogue.setEmotionDegree(null);
            }
            voiceDialogue.getSegments().removeIf(segment -> segment.getType().isText() && StringUtils.isBlank(segment.getText()));
        });
        request.getDialogues().removeIf(voiceDialogue -> voiceDialogue.getSegments().isEmpty());

        // 基本参数校验
        Validates.notEmpty(request.getDialogues(), "对话不能为空");
        request.getDialogues().forEach(voiceDialogue -> voiceDialogue.getSegments().forEach(voiceSegment -> {
            switch (voiceSegment.getType()) {
                case TEXT -> Validates.notBlank(voiceSegment.getText(), "text cannot be blank in text segment");
                case PROSODY -> {
                    VoiceProsodyDTO prosody = voiceSegment.getProsody();
                    Validates.notNull(prosody, "prosody cannot be null in prosody segment");
                    Validates.isTrue(ObjectUtils.anyNotNull(prosody.getPitch(), prosody.getRate(), prosody.getVolume(),
                            prosody.getContour()), "prosody must have one relevant property");
                    this.checkContour(prosody.getContour());
                }
                case SHORT_BREAK -> Validates.notNull(voiceSegment.getShortBreak(), "break cannot be null in break segment");
                case SHORT_SOUND -> Validates.notNull(voiceSegment.getShortSound(), "sound cannot be null in sound segment");
            }
        }));

        // 文本字数校验
        String text = VoiceCreationMapper.getRawText(request.getDialogues());
        Validates.isTrue(TextUtil.countWords(text) > 0, "输入文本不存在有效内容");
        Validates.isTrue(TextUtil.countWords(text) <= 750, "输入文本不能超过750字");

        // 声音校验
        Set<Long> voiceIds = VoiceCreationMapper.getAllVoiceIds(request.getDialogues());
        Map<Long, Voice> voiceMap = voiceCacheableRepository.queryMapByIds(voiceIds);
        Validates.isTrue(voiceIds.size() == voiceMap.keySet().size(), VoiceErrorCode.VOICE_NOT_FOUND);
        Validates.isTrue(voiceMap.values().stream().map(Voice::getCategory).distinct().count() == 1, VoiceErrorCode.VOICE_NOT_FOUND);
        request.getDialogues().forEach(dialogue -> {
            Voice voice = voiceMap.get(Long.parseLong(dialogue.getVoiceId()));
            // 口音校验
            VoiceLocale voiceLocale = this.synthesizeLocaleCheck(voice, dialogue);
            // 文本内容校验
            this.checkTextLang(voiceLocale, text);
        });

        // 文件存在性校验
        Set<Long> fileIds = VoiceCreationMapper.getAllFileIds(request);
        Map<Long, HttpFile> fileMap = userFileService.queryHttpFileMapByIds(userContext.getouid(), fileIds);
        Validates.isTrue(fileIds.size() == fileMap.keySet().size(), FileErrorCode.FILE_NOT_EXISTS);

        // 构造语音创作内容
        VoiceCreationContent content = voiceCreationMapper.toVoiceCreationContent(request, voiceMap, fileMap);

        // 停顿校验
        long breakMills = VoiceUtil.calcTotalBreakMills(content.getDialogues());
        Validates.isTrue(breakMills <= 30_000, "停顿总时长不能超过30s");

        // TODO 背景音乐和音效文件存在性校验

        return content;
    }

    @Override
    public void checkContour(@Nullable List<IntonationContourPoint> contour) {
        if (contour != null) {
            long size = contour.stream().map(IntonationContourPoint::getX).distinct().count();
            Validates.isTrue(size == contour.size(), "contour x must be unique");
            contour.sort(Comparator.comparing(IntonationContourPoint::getX));
        }
    }

    @Override
    public VoiceLocale synthesizeLocaleCheck(Voice voice, @Nullable String localeCode, @Nullable String roleCode,
                                             @Nullable String emotionCode) {
        final VoiceLocale voiceLocale;
        if (voice.getProviderType() == VoiceProviderType.self) {
            Validates.notBlank(localeCode, "口音不能为空");
            voiceLocale = voice.findLocaleByCode(localeCode).orElse(null);
            Validates.notNull(voiceLocale, "该口音不存在");
        } else {
            Asserts.isTrue(CollectionUtils.size(voice.getLocales()) == 1, "基础语音有且只有一个口音");
            voiceLocale = voice.getLocales().getFirst();
            if (localeCode != null) {
                Validates.isTrue(voiceLocale.getCode().equals(localeCode), "该口音不存在");
            }
        }

        if (roleCode != null) {
            VoiceRole voiceRole = voiceLocale.findRoleByCode(roleCode).orElse(null);
            Validates.notNull(voiceRole, "该角色不存在");
        }

        if (emotionCode != null) {
            VoiceEmotion voiceEmotion = voiceLocale.findEmotionByCode(emotionCode).orElse(null);
            Validates.notNull(voiceEmotion, "该情绪不存在");
        }

        Asserts.notNull(voiceLocale.getWordsPerMinute(), "语速未设置，voice={}, locale={}", voice.getCode(), voiceLocale.getCode());

        return voiceLocale;
    }

    @Override
    public void checkSynthesizedAudio(@Nullable UserFile audioFile) {
        Validates.notNull(audioFile, VoiceErrorCode.VOICE_SYNTHESIS_TEXT_INCORRECT);
        Validates.isTrue(userFileService.isStoragePresent(audioFile), VoiceErrorCode.AI_AUDIO_GENERATION_FAIL);
    }

    /**
     * 校验声音创作内容和口音是否匹配
     *
     * @param voiceLocale 口音
     * @param inputText   语音内容
     */
    @Override
    public void checkTextLang(VoiceLocale voiceLocale, String inputText) {
        if (voiceLocale.getLangCode().equalsIgnoreCase("en") && TextUtil.containsChineseCharacter(inputText)) {
            // 英文口音不能合成中文内容
            throw new BizException(VoiceErrorCode.VOICE_SYNTHESIS_LOCALE_CANNOT_MATCH_TEXT);
        }
        if (voiceLocale.getLangCode().equalsIgnoreCase("vi") && TextUtil.containsChineseCharacter(inputText)) {
            // 越南口音不能合成中文内容
            throw new BizException(VoiceErrorCode.VOICE_SYNTHESIS_LOCALE_CANNOT_MATCH_TEXT);
        }
        if (voiceLocale.getLangCode().equalsIgnoreCase("th") && TextUtil.containsChineseCharacter(inputText)) {
            // 泰国口音不能合成中文内容
            throw new BizException(VoiceErrorCode.VOICE_SYNTHESIS_LOCALE_CANNOT_MATCH_TEXT);
        }
    }
}
