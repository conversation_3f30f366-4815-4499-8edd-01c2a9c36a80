/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.task.vars;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizVars;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version VideoRenderTaskVars.java, 2024-10-24 下午3:36 zhoudong
 */
@Data
@Accessors(chain = true)
public class VideoRenderTaskVars implements TaskBizVars {

    /**
     * 输出的视频文件ID
     */
    private Long outputVideoFileId;
}
