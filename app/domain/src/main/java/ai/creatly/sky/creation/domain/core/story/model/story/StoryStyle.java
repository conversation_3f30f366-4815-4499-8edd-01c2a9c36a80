/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.story;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class StoryStyle {
    /**
     * 时代背景
     */
    @NotBlank
    private String historicalBackground;
    /**
     * 艺术风格
     */
    @NotBlank
    private String artisticStyle;


    public static StoryStyle empty() {
        return new StoryStyle();
    }
}
