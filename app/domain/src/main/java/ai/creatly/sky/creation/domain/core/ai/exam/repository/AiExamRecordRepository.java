/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.exam.repository;

import ai.creatly.sky.creation.domain.core.ai.exam.model.AiExamRecord;

import java.util.List;

/**
 * AI认证考试记录
 * <AUTHOR>
 * @version AiExamRecordRepository.java, v0.1 2025-02-19 21:17
 */
public interface AiExamRecordRepository {

    boolean saveBatch(List<AiExamRecord> list);

    List<AiExamRecord> getExamRecords(Long examId,Long uid);

    AiExamRecord getExamRecord(Long recordId);

    AiExamRecord saveExamRecord(AiExamRecord aiExamRecord);
}
