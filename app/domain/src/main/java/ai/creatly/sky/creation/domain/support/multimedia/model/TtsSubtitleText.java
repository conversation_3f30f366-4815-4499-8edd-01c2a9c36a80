/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.multimedia.model;

import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * TTS字幕文本信息（无时间轴信息，仅作为中间数据存在，不会持久化）
 *
 * <AUTHOR>
 * @version TtsSubtitleText.java, v 0.1 2024-08-15 下午6:13 zhoudong
 */
@Data
@Accessors(chain = true)
public class TtsSubtitleText {

    /**
     * 该分段的全局唯一标识
     */
    private String  id;
    /**
     * 在TTS原始字符串中的结束下标（从0开始）
     */
    private Integer endIndex;
    /**
     * TTS对话索引（从0开始）
     */
    private Integer dialogueIndex;
    /**
     * TTS自定义分段编号（从1开始，可以实现字幕分组能力，即连续多条字幕可以划分到同一个分段里去）
     */
    @Nullable
    private Integer segmentNo;
    /**
     * TTS原始文本（含标点符号）
     */
    private String  originalText;
    /**
     * 显示文本（不含断句标点符号）
     */
    private String  displayText;
}
