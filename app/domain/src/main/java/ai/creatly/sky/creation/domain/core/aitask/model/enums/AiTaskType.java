/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version AiTaskType.java, v 0.1 2023-06-24 02:08 joton
 */
@Getter
@RequiredArgsConstructor
public enum AiTaskType {

    /*-------------------业务型任务-------------------*/

    @Deprecated
    AI_ARTISTIC_FONT("AI生成艺术字"),
    @Deprecated
    COLLABORATION_CREATION("联合创作"),

    CREATE_AI_IMAGE("AI生成图片"),

    CREATE_AI_AUDIO("AI合成声音"),
    CREATE_USER_VOICE("用户声音定制"),

    CREATE_AI_TALK_VIDEO("AI合成说话者视频"),
    AI_TALK_EMOTION_PREPROCESS("AI预处理说话者表情"),

    AI_PRODUCT_IMAGE_V2("AI商品图2.0（预设场景一步生成）"),

    AI_PRODUCT_VIDEO("AI商品视频"),

    AI_STORY("AI短片故事"),
    AI_STORY_ROLE_IMAGE("故事角色形象生成"),

    AI_DIG_HUMAN("AI数字人"),

    VIDEO_PROJECT("视频项目"),

    DRAMA("剧情"),

    ASSET("素材"),

    AI_IMAGE("AI图像"),

    AI_VIDEO("AI视频"),

    AI_AUDIO("AI声音"),

    /*-------------------系统型任务-------------------*/

    APPROVAL("审批"),

    INIT_USER_GENERAL_CREDIT_ACCOUNT("初始化用户通用余额账户"),

    FIX_CREDITS_TIME("修复新注册用户余额的有效期（生效时间 和 过期时间）"),

    VOICE_MARKET_SYNC_VOICES("声音市场的声音同步任务"),

    VOICE_SYNTHESIS_ENUM_DB("基础合成声音枚举数据入DB"),

    TASK_DAILY_REPORT("任务统计日报"),

    SYNC_MARKET_VOICE_WORDS_PER_MINUTES("声音演员的声音语速数据同步"),

    SYNC_SOUND_LIB("音效库数据同步"),
    ;

    private final String desc;
}
