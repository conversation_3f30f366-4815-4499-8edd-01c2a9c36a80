/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.client;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.digitalhuman.client.model.DigitalHumanOutFile;
import ai.creatly.sky.creation.domain.core.digitalhuman.model.DigitalHumanVideo;

/**
 * <AUTHOR>
 * @version DigitalHumanClient.java, v 0.1 2024-05-25 17:50 syoka
 */
@Deprecated
public interface DigitalHumanClient {

    /**
     * 新增数字人视频
     *
     * @return 数字人视频id
     */
    String addAvatarVideo(DigitalHumanVideo video);

    /**
     * 查询数字人视频生成结果
     *
     * @param extOutId 外部视频id
     * @return 数字人视频生成状态
     */
    DigitalHumanOutFile getAvatarVideoResult(String extOutId);

    /**
     * 合并透明通道视频(同步等待)
     */
    void mergeAlphaVideo(DigitalHumanVideo video);

    /**
     * 视频渲染
     */
    void renderVideo(DigitalHumanVideo video, AiTask aiTask);

    /**
     * 清理渲染副作用
     */
    void clearRenderEffect(DigitalHumanVideo video);
}
