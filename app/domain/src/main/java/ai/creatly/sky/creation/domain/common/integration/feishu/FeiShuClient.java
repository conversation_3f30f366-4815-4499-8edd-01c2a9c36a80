/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.feishu;

import com.jspeeder.core.util.text.FormatUtil;

/**
 * 飞书消息发送client
 *
 * <AUTHOR>
 * @version FeiShuClient.java, v 0.1 2023-07-11 09:21
 */
public interface FeiShuClient {

    /**
     * 向飞书群 发消息
     * webhook机制
     *
     * @param webhookToken -
     * @param template -
     * @param args -
     */
    void sendMessageWithToken(String webhookToken, String template, Object... args);

    /**
     * 向飞书群 发消息
     * webhook机制
     *
     * @param template -
     * @param args -
     */
    default void sendMessage2Group(String template, Object... args) {
        this.sendMessage2Group(FormatUtil.format(template, args));
    }

    /**
     * 向飞书群 发消息
     * webhook机制
     *
     * @param content -
     */
    void sendMessage2Group(String content);
}
