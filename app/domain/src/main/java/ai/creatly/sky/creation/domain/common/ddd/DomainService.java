/*
 * Copyright (C), 2021-2023, ha<PERSON><PERSON><PERSON>e All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.common.ddd;

import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Indexed;

import java.lang.annotation.*;

/**
 * DDD Domain Service
 *
 * <AUTHOR>
 * @version : DomainService.java, v 1.0 2023年07月22日 12时38分 syoka Exp$
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Indexed
@Component
public @interface DomainService {

    @AliasFor(annotation = Component.class)
    String value() default "";
}
