/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version StoryErrorCode.java, v 0.1 2024-03-02 下午8:24 heb
 */
@RequiredArgsConstructor
@Getter
public enum StoryErrorCode implements ErrorCode {

    STORY_NOT_FOUND("故事不存在"),
    STORY_GEN_UNKNOWN_EXCEPTION("系统繁忙，请稍后重试"),

    STORY_DRAFT_DOWNLOAD_TIMEOUT("下载剪映草稿处理超时"),
    STORY_DRAFT_DOWNLOAD_ERROR("故事导出草稿剪映失败，请重试"),

    STORY_ROLE_GEN_UNKNOWN_EXCEPTION("系统繁忙，请稍后重试"),
    STORY_ROLE_NOT_FOUND("故事角色不存在"),
    STORY_ROLE_NOT_FOUND_NEED_TO_MANUALLY_ADD("未检测到故事角色，请手动添加角色"),
    STORY_ROLE_IMG_INIT_TIMEOUT("角色形象初始化超时，请刷新页面重试"),
    STORY_ROLE_IMG_INIT_FAILURE("角色形象初始化失败，请手动生成"),
    STORY_ROLE_PROMPT_INIT_FAILURE("角色形象提示词初始化失败"),
    STORY_ROLE_IMG_GEN_TIMEOUT("重新生成角色形象超时"),
    STORY_ROLE_NOT_SAVED("故事角色未保存，请先保存角色"),
    STORY_ROLE_IMG_LACK("角色形象缺失，请先生成所有角色的形象"),
    STORY_ROLE_VOICE_LACK("角色声音缺失，请先配置所有角色的声音"),
    STORY_ROLE_NAME_DUPLICATE("角色名字重复，请修改后重试"),
    STORY_ROLE_NOT_INITIALIZED("故事角色未初始化，请等待角色初始化完成"),

    STORY_BOARD_GEN_FAILURE("故事板生成失败，请重试"),

    STORY_SHOT_NOT_FOUND("故事分镜不存在"),
    STORY_SHOT_ID_LIST_INCOMPLETE("故事分镜列表不完整"),
    STORY_SHOT_STATIC_NOT_FOUND("故事分镜静态图不存在"),
    STORY_SHOT_INFO_INIT_TIMEOUT("故事分镜初始化超时"),
    STORY_SHOT_IMG_GEN_TIMEOUT("故事分镜画面生成超时"),
    STORY_SHOT_IMG_PROMPT_INIT_FAILURE("分镜图提示词初始化失败"),
    STORY_SHOT_CAMERA_GEN_TIMEOUT("运镜生成超时"),
    STORY_SHOT_CAMERA_GEN_FAIL("运镜生成失败，请重试"),
    STORY_SHOT_EFFECT_GEN_TIMEOUT("分镜动效生成超时"),
    STORY_SHOT_EFFECT_GEN_FAIL("分镜的动效生成失败，请重试"),
    STORY_SHOT_IMG_FILE_NOT_FOUND("分镜图文件不存在"),
    STORY_SHOT_DYNAMIC_FILE_NOT_FOUND("分镜视频文件不存在"),
    STORY_SHOT_DIALOGUE_FILE_NOT_FOUND("分镜的对话音频文件不存在"),
    STORY_SHOT_SOUND_RECOMMEND_TIMEOUT("分镜的音效推荐超时"),
    STORY_SHOT_DIALOGUE_SYNTHESIZE_TIMEOUT("分镜的对话合成超时"),
    STORY_SHOT_SOUND_NOT_FOUND("分镜的音效文件不存在"),
    STORY_SHOT_DURATION_INVALID("故事分镜时长不合法"),
    STORY_SHOT_DIALOGUE_NOT_FOUND("该分镜没有对话信息"),
    STORY_SHOT_DIALOGUE_ROLE_VOICE_NOT_SETUP("该对话中的角色：{}未设置声音，请先设置{}的声音"),
    STORY_SHOT_DIALOGUE_NO_ROLE_RELATED("该对话没有关联角色，请先关联角色"),

    STORY_ACTOR_NOT_FOUND("故事演员不存在"),
    ;
    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
