/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.azure.model.ssml;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @version SpeakVoiceAudio.java, v 0.1 2023-12-11 上午12:25 zhoudong
 */
@Data
@Accessors(chain = true)
@JacksonXmlRootElement(localName = "audio")
public class SpeakVoiceAudio {

    /**
     * 音频文件的 HTTP 地址
     */
    @JacksonXmlProperty(isAttribute = true)
    private String src;
}
