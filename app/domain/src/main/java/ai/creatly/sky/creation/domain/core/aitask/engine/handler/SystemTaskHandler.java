/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.engine.handler;

import ai.creatly.sky.creation.domain.common.env.RuntimeEnv;
import ai.creatly.sky.creation.domain.core.aitask.admin.mapper.AiTaskAdminMapper;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.forward.TaskAction;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.SysTask;
import ai.creatly.sky.creation.domain.core.aitask.repository.AiTaskRepository;
import jodd.util.Consumers;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version SystemTaskHandler.java, v 0.1 2023-08-22 01:35 joton
 */
public abstract class SystemTaskHandler implements SimpleTaskHandler, InitializingBean {

    @Autowired
    protected AiTaskRepository aiTaskRepository;
    @Autowired
    protected RuntimeEnv        runtimeEnv;
    @Autowired
    protected AiTaskAdminMapper aiTaskAdminMapper;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(this.buildSysTask().getTaskType())
                .setLoadSize(1)
                // TODO 系统任务也要区分永久性任务 还是 一次性任务，直接在 TaskType 层面区分
                .setExecCountAlertThreshold(Integer.MAX_VALUE);
    }

    @Override
    public void afterPropertiesSet() {
        this.initSysTask();
    }

    protected void initSysTask() {
        if (runtimeEnv.isLocal()) {
            // 本地环境不注册系统任务
            return;
        }
        // 先查询，查不到再注册任务
        AiTask task = aiTaskAdminMapper.toAiTask(this.buildSysTask());
        aiTaskRepository.queryByUnique(task.getTaskType(), task.getBizType(), task.getBizNo(), task.getSubBizNo())
                .ifPresentOrElse(Consumers.empty(), () -> aiTaskRepository.create(task));
    }

    protected abstract SysTask buildSysTask();

    @Override
    public abstract TaskAction handle(AiTask aiTask);
}
