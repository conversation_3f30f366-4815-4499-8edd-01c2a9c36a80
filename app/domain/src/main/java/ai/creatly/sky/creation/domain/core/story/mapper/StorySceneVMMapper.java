/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.mapper;

import ai.creatly.sky.creation.domain.core.story.model.scene.StoryScene;
import ai.creatly.sky.creation.domain.core.story.model.scene.response.StorySceneVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version StorySceneVMMapper.java, v 0.1 2024-04-15 22:13 syoka
 */
@Mapper(config = BaseMapperConfig.class)
public interface StorySceneVMMapper {

    StorySceneVM toStorySceneVM(StoryScene storyScene);
}
