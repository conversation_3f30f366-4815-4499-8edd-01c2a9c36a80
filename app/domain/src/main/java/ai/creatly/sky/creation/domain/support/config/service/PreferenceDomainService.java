/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.config.service;

import ai.creatly.sky.creation.domain.common.ddd.DomainService;
import ai.creatly.sky.creation.domain.support.config.repository.SystemPreferenceRepository;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version : SystemPreferenceService.java, v 1.0 2023年07月24日 22时26分 syoka Exp$
 */
@DomainService
@RequiredArgsConstructor
public class PreferenceDomainService {

    /**
     * 系统配置
     */
    @NonNull
    private final SystemPreferenceRepository systemPreferenceRepository;


    public String getSystemPreference(String configKey, String defaultValue) {
        if (StringUtils.isEmpty(configKey)) {
            return defaultValue;
        }

        String value = systemPreferenceRepository.getSystemPreferences(configKey);
        if (StringUtils.isEmpty(value)) {
            return defaultValue;
        }
        return value;
    }

    public void setSystemPreference(String configKey, String configValue, String operator) {
        String value = systemPreferenceRepository.getSystemPreferences(configKey);
        if (StringUtils.isEmpty(value)) {
            systemPreferenceRepository.insertSystemPreference(configKey, configValue, operator);
        } else {
            systemPreferenceRepository.setSystemPreference(configKey, configValue, operator);
        }
    }
}
