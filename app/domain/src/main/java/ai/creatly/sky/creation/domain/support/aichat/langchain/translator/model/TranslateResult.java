/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.support.aichat.langchain.translator.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version TranslateResult.java, v 0.1 2023-11-22 15:25 syoka
 */
@Data
@Accessors(chain = true)
public class TranslateResult {

    /**
     * 输入数据
     */
    private String prompt;

    /**
     * 翻译结果
     */
    private String translatePrompt;

    /**
     * 敏感词
     */
    private String bannedWord;

    /**
     * 敏感词关联的可疑的中文语句
     */
    private String suspectedContent;

    /**
     * 错误开始下标
     */
    private Integer errorStartIndex;

    /**
     * 错误结束下标
     */
    private Integer errorEndIndex;
}
