/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.mapper;

import ai.creatly.sky.creation.domain.core.story.model.role.StoryRole;
import ai.creatly.sky.creation.domain.core.story.model.shot.CameraMovement;
import ai.creatly.sky.creation.domain.core.story.model.shot.StoryShot;
import ai.creatly.sky.creation.domain.core.story.model.shot.response.StoryCameraMovementVM;
import ai.creatly.sky.creation.domain.core.story.model.story.Story;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryStyleValue;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryType;
import ai.creatly.sky.creation.domain.core.story.model.story.response.StoryDetailVM;
import ai.creatly.sky.creation.domain.core.story.model.story.response.StoryTopicVM;
import ai.creatly.sky.creation.domain.core.story.model.story.response.StoryVM;
import ai.creatly.sky.creation.domain.core.story.model.story.response.StyleValueVM;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import jakarta.annotation.Nullable;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version StoryVMMapper.java, v 0.1 2024-03-02 15:15 heb
 */
@Mapper(config = BaseMapperConfig.class, uses = StoryRoleVMMapper.class)
public abstract class StoryVMMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    public StoryVM toStoryVM(Story story, @Nullable StoryShot firstShot) {
        String defaultCoverUrl = "https://creatly.oss-cn-hangzhou.aliyuncs.com/creation/system/story/default-cover.jpeg";
        if (firstShot != null && firstShot.getAsset().getShotStatic() != null) {
            String coverUrl = userFileHelper.getHttpUrl(firstShot.getAsset().getShotStatic().getOssUrl(), story.getUid());
            return this.toStoryVM(story, coverUrl);
        }
        if (story.getRoles() != null) {
            String coverUrl = story.getRoles().stream()
                    .findFirst()
                    .map(StoryRole::getPortraitUrl)
                    .filter(StringUtils::isNotBlank)
                    .map(url -> userFileHelper.getHttpUrl(url, story.getUid()))
                    .orElse(defaultCoverUrl);
            return this.toStoryVM(story, coverUrl);
        }
        return this.toStoryVM(story, defaultCoverUrl);
    }

    public abstract StoryVM toStoryVM(Story story, String coverUrl);

    public abstract StoryDetailVM toStoryDetailVM(Story story, Boolean isBoardCreated);

    public String toStoryType(StoryType storyType) {
        return storyType.getCode();
    }

    public abstract StoryTopicVM toStoryTypeVM(StoryType storyType);

    public abstract StyleValueVM toStyleValueVM(StoryStyleValue storyStyleValue);

    public StoryCameraMovementVM toStoryCameraMovementVM(CameraMovement cameraMovement) {
        StoryCameraMovementVM movementVM = new StoryCameraMovementVM();
        movementVM.setCode(cameraMovement.name());
        movementVM.setName(cameraMovement.getDesc());
        movementVM.setSampleUrl(cameraMovement.getSampleUrl());
        return movementVM;
    }
}
