/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aittalk.model.task.result;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizExecInfo;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version TalkBizExecInfo.java, v 0.1 2023-08-04 23:28 joton
 */
public interface TalkBizExecInfo extends TaskBizExecInfo {

    @Nullable
    Integer getRequestCount();

    @Nullable
    String getRequestHost();

    @Nullable
    Duration getDuration();

    @Nullable
    String getProcessHost();

    @Nullable
    String getFailReason();

    /**
     * 业务主动重试次数
     *
     * @return 重试次数
     */
    default Integer getRetryCount() {
        return this.getRequestCount() == null ? 0 : this.getRequestCount() - 1;
    }
}
