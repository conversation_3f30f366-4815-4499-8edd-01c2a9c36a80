/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.specific.payment;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 定价计划的微信扫码预支付结果
 *
 * <AUTHOR>
 * @version PlanWxNativePrepayResult.java, v 0.1 2023-10-19 下午6:16 zhoudong
 */
@Data
@Accessors(chain = true)
public class PlanWxNativePrepayResult {

    /**
     * 订单ID
     */
    private Long          orderId;
    /**
     * 收银台过期时间（要设计为小于微信的有效期，微信的有效期是2小时）
     */
    private ZonedDateTime expireAt;
    /**
     * 扫码支付：预支付跳转链接（注意：不是二维码图片地址，而是二维码里的内容）
     */
    private String        codeUrl;
}
