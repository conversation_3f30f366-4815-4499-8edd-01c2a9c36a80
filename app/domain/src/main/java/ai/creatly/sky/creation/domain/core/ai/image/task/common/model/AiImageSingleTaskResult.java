/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.task.common.model;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizResult;
import ai.creatly.sky.creation.domain.core.asset.model.AssetRef;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @version AiImageSingleTaskResult.java, v0.1 2025-03-04 12:57
 */
@Data
@Accessors(chain = true)
public class AiImageSingleTaskResult implements TaskBizResult {
    private AssetRef asset;
}
