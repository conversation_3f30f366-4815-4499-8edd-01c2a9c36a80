/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.bizconfig.mapper;

import ai.creatly.sky.creation.domain.support.bizconfig.model.instance.BizConfig;
import ai.creatly.sky.creation.domain.support.bizconfig.model.instance.BizConfigProp;
import ai.creatly.sky.creation.domain.support.bizconfig.model.schema.PropDataType;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.util.ClassUtil;
import com.jspeeder.core.util.json.JSON;
import org.json.JSONObject;
import org.mapstruct.Mapper;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version BizConfigMapper.java, v 0.1 2023-05-08 16:12 joton
 */
@Mapper(config = BaseMapperConfig.class)
public interface BizConfigMapper {

    /**
     * 转换为业务对象
     *
     * @param type 业务对象类型
     * @param <T>  对象类型
     * @return 业务对象
     */
    default <T> T toBizObj(BizConfig config, Class<T> type) {
        StringBuilder sb = new StringBuilder("{");
        for (BizConfigProp prop : config.getProps()) {
            sb.append("\"").append(prop.getKey()).append("\":");
            if (Objects.requireNonNull(prop.getDataType()) == PropDataType.STRING) {
                sb.append(JSON.toJSONString(prop.getValue()));
            } else {
                sb.append(prop.getValue());
            }
            sb.append(",");
        }
        sb.deleteCharAt(sb.length() - 1).append("}");
        if (JSONObject.class.isAssignableFrom(type)) {
            return ClassUtil.cast(JSON.parseJSONObject(sb.toString()));
        }
        return JSON.parseObject(sb.toString(), type);
    }
}
