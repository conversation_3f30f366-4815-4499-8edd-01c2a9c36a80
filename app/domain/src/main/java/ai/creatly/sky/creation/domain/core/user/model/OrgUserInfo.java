/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * creatly 机构用户模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OrgUserInfo {

    /**
     * 用户状态
     */
    private UserStatus        status;
    /**
     * 用户名（全局唯一）
     */
    private String            username;
    /**
     * 手机（全局唯一）
     */
    private String            phone;

    private String            email;

    private String            fullName;

    private String            idCard;

    private String            orgCode;

    private String            className;

    private Long              courseId;

    private Long              uid;

}
