/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.repository;

import ai.creatly.sky.creation.domain.core.plan.model.PlanBenefit;

import java.util.List;

/**
 * 付费计划权益持久化
 *
 * <AUTHOR>
 * @version PlanBenefitRepository.java, v 0.1 2023-10-12 上午11:31 zhoudong
 */
public interface PlanBenefitRepository {

    /**
     * 查询付费计划列表
     *
     * @param planId 计划ID
     * @return 计划列表
     */
    List<PlanBenefit> queryByPlanId(String planId);
}
