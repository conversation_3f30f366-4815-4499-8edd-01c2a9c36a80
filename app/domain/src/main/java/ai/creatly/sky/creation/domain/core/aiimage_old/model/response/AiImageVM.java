/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.core.aiimage_old.model.response;

import ai.creatly.sky.creation.domain.core.aitask.model.response.BaseUserAiTaskVM;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 马良绘图结果（统一模型）
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AiImageVM extends BaseUserAiTaskVM {

    /**
     * 用户生成图时的提示词
     */
    private String prompts;

    /**
     * 任务类型返回
     */
    private String bizType;

    /**
     * 进度 [0,100]
     */
    private Integer progress;

    /**
     * 图片url(仅在进度百分之百时返回）
     */
    private String imageUrl;

    /**
     * 单图集合
     * ps:在画质增强接口情况下不返回
     */
    private List<AttachmentVM> attachments;

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 错误敏感词
     */
    private String bannedWord;

    /**
     * 图生文描述词
     */
    private List<String> describePrompts;

}
