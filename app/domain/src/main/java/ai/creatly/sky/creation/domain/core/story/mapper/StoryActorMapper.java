/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.mapper;

import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.story.model.actor.StoryActor;
import ai.creatly.sky.creation.domain.core.story.model.actor.response.StoryActorVM;
import ai.creatly.sky.creation.domain.core.story.model.role.StoryRole;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import lombok.Setter;
import org.mapstruct.Mapper;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(config = BaseMapperConfig.class)
public abstract class StoryActorMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    public StoryActor toStoryActor(StoryRole storyRole, UserContext userContext) {
        return new StoryActor()
                .setVoiceId(storyRole.getVoiceId())
                .setId(IdHelper.getId())
                .setName(storyRole.getRoleName())
                .setGeneration(storyRole.getRoleGeneration())
                .setGender(storyRole.getRoleGender())
                .setPortraitId(storyRole.getPortraitId())
                .setPortraitUrl(storyRole.getPortraitUrl())
                .setCreator(userContext.toOperatorRef());
    }

    public StoryActorVM toActorVM(StoryActor storyActor, long uid) {
        return new StoryActorVM()
                .setVoiceId(storyActor.getVoiceId())
                .setId(String.valueOf(storyActor.getId()))
                .setName(storyActor.getName())
                .setGeneration(storyActor.getGeneration())
                .setGender(storyActor.getGender())
                .setCreatedAt(storyActor.getCreatedAt())
                .setUpdatedAt(storyActor.getUpdatedAt())
                .setPortraitUrl(userFileHelper.getHttpUrl(storyActor.getPortraitUrl(), uid));
    }
}
