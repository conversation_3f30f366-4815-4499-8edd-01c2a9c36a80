/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.service;

import ai.creatly.sky.creation.domain.core.plan.helper.PlanUtil;
import ai.creatly.sky.creation.domain.core.plan.mapper.UserPlanMapper;
import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.PlanBenefit;
import ai.creatly.sky.creation.domain.core.plan.model.UserPlan;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanSource;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import ai.creatly.sky.creation.domain.core.plan.repository.PlanBenefitRepository;
import ai.creatly.sky.creation.domain.core.plan.repository.PlanRepository;
import ai.creatly.sky.creation.domain.core.plan.repository.UserPlanRepository;
import com.jspeeder.core.util.Asserts;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @version PlanServiceImpl.java, v 0.1 2023-10-15 下午11:36 zhoudong
 */
@Service
@RequiredArgsConstructor
public class PlanServiceImpl implements PlanService {

    private final PlanRepository        planRepository;
    private final PlanBenefitRepository planBenefitRepository;
    private final UserPlanRepository    userPlanRepository;
    private final UserPlanMapper        userPlanMapper;

    @Override
    public Optional<Plan> queryByIdWithBenefits(String planId) {
        Asserts.notBlank(planId, "planId must not be null");
        PlanSource source = PlanUtil.judgePlanSourceById(planId);
        return switch (source) {
            case PLATFORM -> planRepository.queryOptionalById(planId).map(plan -> {
                List<PlanBenefit> benefits = planBenefitRepository.queryByPlanId(plan.getId());
                plan.setBenefits(benefits);
                return plan;
            });
            case USER -> userPlanRepository.queryOptionalById(Long.parseLong(planId)).map(userPlanMapper::toPlan);
        };
    }

    @Override
    public List<Plan> queryByTypeWithBenefits(PlanType type) {
        Asserts.notNull(type, "plan type must not be null");
        List<Plan> plans = planRepository.queryByType(type);
        plans.forEach(plan -> {
            List<PlanBenefit> benefits = planBenefitRepository.queryByPlanId(plan.getId());
            plan.setBenefits(benefits);
        });
        return plans;
    }

    @Override
    public List<Plan> queryByTypeWithBenefits(PlanType type, long uid) {
        List<Plan> plans = this.queryByTypeWithBenefits(type);
        List<UserPlan> userPlans = userPlanRepository.queryByTypeAndUid(type, uid);
        userPlans.forEach(userPlan -> plans.stream()
                .filter(plan -> plan.getId().equals(userPlan.getPlatformPlanId()))
                .findFirst()
                .ifPresent(plan -> {
                    userPlanMapper.updatePlan(userPlan, plan);
                    plan.setBenefits(userPlanMapper.toPlanBenefits(userPlan.getId(), userPlan.getBenefits()));
                })
        );
        return plans;
    }
}
