/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.model;

import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskExecStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.model.OperatorRef;
import com.jspeeder.core.util.json.JSON;
import jakarta.annotation.Nullable;
import jodd.util.StringPool;
import lombok.Data;
import lombok.experimental.Accessors;
import org.json.JSONObject;
import org.springframework.data.annotation.Id;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * AI创作任务实例（涵盖各种AI创作工具）
 *
 * <AUTHOR>
 * @version AiTask.java, v 0.1 2023-06-22 23:02 joton
 */
@Data
@Accessors(chain = true)
public class AiTask {

    /**
     * 任务默认优先级
     */
    public final static int DEFAULT_PRIORITY = 100;
    /**
     * 任务默认分片号
     * todo 等上集群后再随机分片
     */
    public final static int DEFAULT_SHARDING = 0;

    /**
     * 唯一标识
     */
    @Id
    private Long                 id;
    /**
     * 创建时间
     */
    private ZonedDateTime        createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime        updatedAt;
    /**
     * 任务所有者ID（1表示平台）
     */
    private Long                 ownerId;
    /**
     * 任务所有者名称
     */
    private String               ownerName;
    /**
     * 任务名称
     */
    private String               taskName;
    /**
     * 任务类型
     */
    private AiTaskType           taskType;
    /**
     * 状态（TODO：考虑增加一个字段：标准子状态，取值有：成功，部分成功，失败）
     */
    private AiTaskStatus         status;
    /**
     * 关联外部业务状态（各业务领域独立维护自己的枚举，不做集中式维护）（如果没有，则存空字符串）
     */
    private String               bizStatus;
    /**
     * 关联外部业务类型（各业务领域独立维护自己的枚举，不做集中式维护）（如果没有，则存空字符串）
     */
    private String               bizType;
    /**
     * 关联外部业务单号
     */
    private String               bizNo;
    /**
     * 关联外部业务子单号（如果没有，则存空字符串）
     */
    private String               subBizNo;
    /**
     * 执行状态
     */
    private AiTaskExecStatus     execStatus;
    /**
     * 是否自动执行
     */
    private Boolean              autoExec;
    /**
     * 任务优先级（默认100）
     */
    private Integer              priority;
    /**
     * 任务随机分片号
     */
    private Integer              sharding;
    /**
     * 延迟到指定时间执行
     */
    @Nullable
    private ZonedDateTime        delayedAt;
    /**
     * 开始运行时间
     */
    @Nullable
    private ZonedDateTime        startedAt;
    /**
     * 运行完成时间
     */
    @Nullable
    private ZonedDateTime        finishedAt;
    /**
     * 协作用户列表
     */
    private List<TaskSharedUser> sharedUsers;
    /**
     * 业务输入参数
     */
    private JSONObject           bizParams;
    /**
     * 业务过程数据
     */
    private JSONObject           bizExecInfo;
    /**
     * 业务输出结果
     */
    private JSONObject           bizResult;
    /**
     * 系统标准输入参数
     */
    private AiTaskParams         sysParams;
    /**
     * 系统标准输出结果
     */
    private AiTaskResult         sysResult;
    /**
     * 系统标准过程数据
     */
    private AiTaskExecInfo       execInfo;
    /**
     * 创建人
     */
    private OperatorRef          creator;

    private String               orgCode;
    /**
     * 是否属于某用户
     *
     * @param uid 用户ID
     * @return 是否属于
     */
    public boolean belongsTo(long uid) {
        return Objects.equals(this.getOwnerId(), uid);
    }

    @JsonIgnore
    public boolean isStarted() {
        return this.getStatus() == AiTaskStatus.RUNNING;
    }

    @JsonIgnore
    public boolean isFinished() {
        return this.getStatus() == AiTaskStatus.FINISHED;
    }

    @JsonIgnore
    public boolean isCanceled() {
        return this.getStatus() == AiTaskStatus.CANCELED;
    }

    @JsonIgnore
    public boolean isCompleted() {
        return this.getExecStatus() == AiTaskExecStatus.COMPLETED;
    }

    @JsonIgnore
    public boolean isTimeoutFromCreated(Duration expectTime) {
        return Duration.between(this.createdAt, ZonedDateTime.now()).compareTo(expectTime) > 0;
    }

    @JsonIgnore
    public boolean isTimeoutFromStarted(Duration expectTime) {
        if (this.startedAt == null) {
            // 如果任务还未开始，则不算超时
            return false;
        }
        return Duration.between(this.startedAt, ZonedDateTime.now()).compareTo(expectTime) > 0;
    }

    public Duration durationFromStarted() {
        if (this.startedAt == null) {
            return Duration.ZERO;
        }
        return Duration.between(this.startedAt, ZonedDateTime.now());
    }

    /**
     * 获取任务预估完成耗时
     *
     * @return 预估完成耗时
     */
    @JsonIgnore
    @Nullable
    public Duration getEstimatedDuration() {
        return sysParams.getEstimatedDuration();
    }

    /**
     * 获取任务取消原因
     *
     * @return 取消原因
     */
    @JsonIgnore
    @Nullable
    public String getCancelReason() {
        return sysResult.getCancelReason();
    }

    public int getRetryCount(String retryName) {
        return this.execInfo.getRetryCount(retryName);
    }

    /*----------------------- 业务输入参数 -----------------------*/

    public AiTask setBizParams(JSONObject bizParams) {
        this.bizParams = bizParams;
        return this;
    }

    public AiTask setBizInput(TaskBizInput bizInput) {
        this.bizParams = JSON.toJSONObject(bizInput);
        return this;
    }

    public <T extends TaskBizParams> T parseBizParams(Class<T> type) {
        return JSON.parseObject(bizParams.toString(), type);
    }

    public <T extends TaskBizInput> T parseBizInput(Class<T> type) {
        return JSON.parseObject(bizParams.toString(), type);
    }

    /*----------------------- 业务过程数据 -----------------------*/

    public AiTask setBizExecInfo(JSONObject bizExecInfo) {
        this.bizExecInfo = bizExecInfo;
        return this;
    }

    public AiTask setBizExecInfo(TaskBizVars bizVars) {
        this.bizExecInfo = JSON.toJSONObject(bizVars);
        return this;
    }

    public <T extends TaskBizVars> T parseBizExecInfo(Class<T> type) {
        return this.parseBizVars(type);
    }

    public <T extends TaskBizVars> T parseBizVars(Class<T> type) {
        return JSON.parseObject(bizExecInfo.toString(), type);
    }

    /*----------------------- 业务输出结果 -----------------------*/

    public AiTask setBizResult(JSONObject bizResult) {
        this.bizResult = bizResult;
        return this;
    }

    public AiTask setBizResult(TaskBizResult bizResult) {
        this.bizResult = JSON.toJSONObject(bizResult);
        return this;
    }

    public <T extends TaskBizResult> T parseBizResult(Class<T> type) {
        return JSON.parseObject(bizResult.toString(), type);
    }

    /**
     * 初始化新任务
     *
     * @return 任务构建器
     */
    public static AiTaskBuilder buildNew() {
        AiTask aiTask = new AiTask()
                .setId(IdHelper.getId())
                .setStatus(AiTaskStatus.CREATED)
                .setBizStatus(StringPool.EMPTY)
                .setExecStatus(AiTaskExecStatus.WAITING)
                .setAutoExec(true)
                .setPriority(AiTask.DEFAULT_PRIORITY)
                .setSharding(AiTask.DEFAULT_SHARDING)
                .setSharedUsers(new ArrayList<>())
                .setBizResult(new JSONObject())
                .setBizExecInfo(new JSONObject())
                .setSysParams(new AiTaskParams())
                .setSysResult(new AiTaskResult())
                .setExecInfo(new AiTaskExecInfo().setExecCount(0).setProgress(0));
        return new AiTaskBuilder(aiTask);
    }
}
