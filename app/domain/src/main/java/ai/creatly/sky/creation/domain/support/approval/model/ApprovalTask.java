/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.approval.model;

import com.jspeeder.core.model.OperatorRef;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version ApprovalTask.java, 2024-10-29 上午11:10 zhoudong
 */
@Data
@Accessors(chain = true)
public class ApprovalTask {

    /**
     * 任务ID
     */
    private Long            id;
    /**
     * 审批业务类型
     */
    private ApprovalBizType bizType;
    /**
     * 业务编号
     */
    private String          bizNo;
    /**
     * 子业务编号
     */
    private String          subBizNo;
    /**
     * 审批状态
     */
    private ApprovalStatus  status;
    /**
     * 申请人ID
     */
    private Long            applicantId;
    /**
     * 申请人名称
     */
    private String          applicantName;
    /**
     * 审批人列表（可能多个）
     */
    private List<Reviewer>  reviewers;
    /**
     * 拒绝原因
     */
    private String          rejectReason;
    /**
     * 创建人
     */
    private OperatorRef     creator;
}
