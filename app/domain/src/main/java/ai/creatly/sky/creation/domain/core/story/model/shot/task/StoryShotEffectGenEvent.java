/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.shot.task;

import lombok.Data;

/**
 * <AUTHOR>
 * @version StoryShotEffectGenEvent.java, v 0.1 2024-04-09 22:46 heb
 */
@Data
public class StoryShotEffectGenEvent {

    private Long storyId;
    private Long shotId;
    private Long taskId;
}
