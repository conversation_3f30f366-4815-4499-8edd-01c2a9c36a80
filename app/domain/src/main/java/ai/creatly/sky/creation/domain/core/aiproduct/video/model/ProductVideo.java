/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.video.model;

import ai.creatly.sky.creation.domain.support.multimedia.model.AudioMetadata;
import com.jspeeder.core.model.BizStatus;
import com.jspeeder.core.model.OperatorRef;
import jakarta.annotation.Nullable;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * 商品视频（成品）
 *
 * <AUTHOR>
 * @version ProductVideo.java, v 0.1 2024-05-24 下午9:21 zhoudong
 */
@Data
@Accessors(chain = true)
public class ProductVideo {

    /**
     * 主键ID
     */
    private Long          id;
    /**
     * 创建时间
     */
    private ZonedDateTime createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime updatedAt;
    /**
     * 所属用户ID
     */
    private Long          uid;
    /**
     * 状态
     */
    private BizStatus     status;
    /**
     * 标题
     */
    private String        title;
    /**
     * 描述
     */
    private String        description;
    /**
     * 封面图文件ID
     */
    @Nullable
    private Long          coverFileId;
    /**
     * 封面图地址
     */
    @Nullable
    private String        coverUrl;
    /**
     * 视频文件ID
     */
    private Long          videoFileId;
    /**
     * 视频地址（OSS格式）
     */
    private String        videoUrl;
    /**
     * 视频时长
     */
    private Duration      videoDuration;
    /**
     * 视频格式（小写）
     */
    private String        videoFormat;
    /**
     * 视频分辨率（宽度x高度）
     */
    @Nullable
    private String        videoResolution;
    /**
     * 音频文件ID
     */
    @Nullable
    private Long          audioFileId;
    /**
     * 音频地址（OSS格式）
     */
    @Nullable
    private String        audioUrl;
    /**
     * 音频元信息
     */
    @Nullable
    private AudioMetadata audioMetadata;
    /**
     * 创建者
     */
    private OperatorRef   creator;

    /**
     * 是否属于某用户
     *
     * @param uid 用户ID
     * @return 是否属于
     */
    public boolean belongsTo(long uid) {
        return Objects.equals(this.uid, uid);
    }
}
