/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.model.response;

import ai.creatly.sky.creation.domain.core.voice.model.enums.EmotionDegreeEnum;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechRateType;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * 数字人配音
 *
 * <AUTHOR>
 * @version VideoAvatarVoiceVM.java, v 0.1 2024-08-27 下午4:58 zhoudong
 */
@Data
@Accessors(chain = true)
public class VideoAvatarVoiceVM {

    /**
     * 声音ID
     */
    private String          voiceId;
    /**
     * 语速
     */
    private SpeechRateType  rate;
    /**
     * 定制情绪（通过声音列表API获取）
     */
    @Nullable
    private String            emotionCode;
    /**
     * 情绪表现程度（默认1）
     */
    @Nullable
    private EmotionDegreeEnum emotionDegree;
}
