/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.model;

import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaVideoType;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version DramaVideo.java, 2024-10-21 下午7:42 zhoudong
 */
@Data
@Accessors(chain = true)
public class DramaVideo {

    /**
     * 剧情视频类型
     */
    private DramaVideoType type;
    /**
     * 视频文件ID
     */
    private Long           fileId;
    /**
     * 视频文件地址（OSS协议）
     */
    private String         url;
    /**
     * 视频宽度
     */
    private Integer        width;
    /**
     * 视频高度
     */
    private Integer        height;
    /**
     * 视频时长
     */
    private Duration       duration;
    /**
     * 该视频的最后一段文案
     */
    @Nullable
    private String         endText;
}
