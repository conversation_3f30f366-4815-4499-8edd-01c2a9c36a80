/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.model;

import ai.creatly.sky.creation.domain.support.file.model.FileSize;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import ai.creatly.sky.creation.domain.support.multimedia.enums.AudioSynthesisMode;
import ai.creatly.sky.creation.domain.support.multimedia.model.AudioMetadata;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.*;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * 用户文件元数据
 *
 * <AUTHOR>
 * @version FileMetadata.java, v 0.1 2023-07-01 18:08 joton
 */
@Data
@Accessors(chain = true)
@With
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class FileMetadata {

    /**
     * 存储地域
     */
    private String              region;
    /**
     * 文件大小（字节数） TODO 反序列化兼容存量数据（订正数据后删除 @JsonAlias）
     */
    @Nullable
    @JsonAlias("size")
    private Long                bytes;
    /**
     * 音频或视频文件的时长
     */
    @Nullable
    private Duration            duration;
    /**
     * 图片/视频宽度（以像素为单位）
     */
    @Nullable
    private Integer             width;
    /**
     * 图片/视频高度（以像素为单位）
     */
    @Nullable
    private Integer             height;
    /**
     * 视频帧率（fps）
     */
    @Nullable
    private Double              frameRate;
    /**
     * 生成式文件的提示词
     */
    @Nullable
    private String              prompt;
    /**
     * 业务自定义数据 TODO 反序列化兼容存量数据（订正数据后删除 @JsonAlias）
     */
    @Nullable
    @JsonAlias("userMetadata")
    private Map<String, String> bizData;

    public FileMetadata setDuration(Duration duration) {
        this.duration = duration;
        return this;
    }

    @JsonSetter
    public FileMetadata setDuration(@Nullable Object duration) {
        if (duration instanceof Integer i) {
            // 兼容存量数据（从数据库对象反序列化过来，单位是s）
            this.duration = Duration.ofSeconds(i);
        }
        if (duration instanceof Long l) {
            // 兼容存量数据（从数据库对象反序列化过来，单位是s）
            this.duration = Duration.ofSeconds(l);
        }
        if (duration instanceof String s) {
            // 新的数据，以格式化字符串形式存储
            this.duration = Duration.parse(s);
        }
        return this;
    }

    public FileMetadata addBizData(String key, String value) {
        if (bizData == null) {
            this.bizData = new HashMap<>();
        }
        this.bizData.put(key, value);
        return this;
    }

    public FileMetadata addBizData(String key, Number value) {
        return this.addBizData(key, Objects.toString(value, null));
    }

    public FileMetadata addBizData(String key, Duration value) {
        return this.addBizData(key, Objects.toString(value, null));
    }

    public FileMetadata addBizData(String key, Enum<?> value) {
        return this.addBizData(key, Objects.toString(value, null));
    }

    @Nullable
    public String optBizValue(String key) {
        return bizData == null ? null : bizData.get(key);
    }

    @Nullable
    public <T> T optBizValue(String key, Function<@NotNull String, T> mapper) {
        String value = this.optBizValue(key);
        return value == null ? null : mapper.apply(value);
    }

    @Nullable
    public <T> T optBizValue(String key, String alternativeKey, Function<@NotNull String, T> mapper) {
        String value = this.optBizValue(key);
        if (value == null) {
            value = this.optBizValue(alternativeKey);
        }
        return value == null ? null : mapper.apply(value);
    }

    public static FileMetadata fromAudioMetadata(AudioMetadata metadata) {
        return new FileMetadata()
                .setBytes(metadata.getSize().getSizeInBytes())
                .setDuration(metadata.getDuration())
                .addBizData("bitrate", metadata.getBitrate())
                .addBizData("sampleRate", metadata.getSampleRate())
                .addBizData("synthesisMode", metadata.getSynthesisMode())
                .addBizData("synthesisDuration", metadata.getSynthesisDuration());
    }

    @Nullable
    AudioMetadata toAudioMetadata(UserFile userFile) {
        if (userFile.getType() != FileType.AUDIO) {
            return null;
        }
        return new AudioMetadata()
                .setSize(FileSize.sizeInBytes(this.getBytes() == null ? -1 : this.getBytes()))
                .setExtension(userFile.getExtension())
                .setDuration(this.getDuration())
                .setBitrate(this.optBizValue("bitrate", Integer::parseInt))
                .setSampleRate(this.optBizValue("sampleRate", Integer::parseInt))
                .setSynthesisMode(this.optBizValue("synthesisMode", AudioSynthesisMode::valueOf))
                .setSynthesisDuration(this.optBizValue("synthesisDuration", "ttsCostTime", Duration::parse));
    }
}
