/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.model.admin.request;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class VoiceUpdatePreviewRequest {
    /**
     * 声音Id（通过查询声音列表API获取）
     */
    @NotBlank(message = "声音ID不能为空")
    @Size(max = 64, message = "声音ID格式错误")
    @Digits(integer = 20, fraction = 0, message = "声音ID格式错误")
    private String voiceId;
    /**
     * 声音Code（通过查询声音列表API获取）
     */
    @NotBlank(message = "声音Code不能为空")
    private String voiceCode;
    /**
     * 口音编号（通过声音列表API获取），如果不传，则使用默认的
     */
    @Size(max = 32, message = "该声音不支持该本地化口音")
    private String localeCode;
    /**
     * 扮演角色（通过声音列表API获取），如果不传，则使用默认的
     * <p/>
     * 注意：如果传了，则必须和 emotion 一起使用，否则不起作用。
     */
    @Size(max = 32, message = "该声音不支持扮演该角色")
    private String roleCode;
    /**
     * 定制情绪（通过声音列表API获取）
     */
    @Size(max = 32, message = "该声音不支持使用该情绪")
    private String emotionCode;
    /**
     * 预览音频地址（HTTP地址）
     */
    @NotBlank
    private String previewAudioUrl;
}
