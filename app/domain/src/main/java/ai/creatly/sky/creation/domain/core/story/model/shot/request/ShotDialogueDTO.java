/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.shot.request;

import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechRateType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 角色对白设置请求
 *
 * <AUTHOR>
 * @version ShotDialogueDTO.java, v 0.1 2024-05-18 下午4:09 zhoudong
 */
@Data
public class ShotDialogueDTO {

    /**
     * 对话关联的角色ID
     */
    @NotBlank(message = "角色ID不能为空")
    private String         roleId;
    /**
     * 台词
     */
    @Size(max = 100, message = "台词不能超过100字")
    private String         text;
    /**
     * 声音情绪（通过声音列表API获取）
     */
    @Size(max = 32, message = "该声音不支持使用该情绪")
    private String         emotionCode;
    /**
     * 对话语速
     */
    private SpeechRateType rate;
    /**
     * 对话音频文件ID
     */
    private String         audioFileId;
}
