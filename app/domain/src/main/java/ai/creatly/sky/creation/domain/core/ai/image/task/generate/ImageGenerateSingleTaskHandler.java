/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.task.generate;

import ai.creatly.sky.creation.domain.core.ai.image.client.sync.AiImageSingleClient;
import ai.creatly.sky.creation.domain.core.ai.image.mapper.ImageGenerationMapper;
import ai.creatly.sky.creation.domain.core.ai.image.task.AiImageTaskBizType;
import ai.creatly.sky.creation.domain.core.ai.image.task.common.AbstractAiImageSyncTaskHandler;
import ai.creatly.sky.creation.domain.core.ai.image.task.generate.model.ImageGenerateTaskInput;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.asset.model.Asset;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;

/**
 *
 * <AUTHOR>
 * @version ImageGenerateSingleTaskHandler.java, v0.1 2025-02-28 13:41
 */
//@Component
@RequiredArgsConstructor
public class ImageGenerateSingleTaskHandler extends AbstractAiImageSyncTaskHandler implements AiTaskHandler {

    private final AiImageSingleClient   aiImageSingleClient;
    private final ImageGenerationMapper imageGenerationMapper;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.AI_IMAGE)
                .setBizType(AiImageTaskBizType.single_image_generate)
                .setLoadSize(1)
                .setQueueMode(TaskQueueMode.POP_PER_EXECUTED)
                .setNotifyOnSubmit(false)
                .setNotifyUserOnCompleted(false);
    }

    @Nullable
    @Override
    public UserFile generateImage(AiTask aiTask, UserContext userContext) {
        var input = aiTask.parseBizInput(ImageGenerateTaskInput.class);
        return aiImageSingleClient.generateImage(input, userContext);
    }

    @Override
    public Asset convertToAsset(AiTask aiTask, UserFile imageFile) {
        var input = aiTask.parseBizInput(ImageGenerateTaskInput.class);
        String coverUrl = imageFileService.generateThumbnailUrl(imageFile, 50);
        return imageGenerationMapper.buildAsset(input, imageFile, coverUrl);
    }
}
