/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aittalk.mapper;

import ai.creatly.sky.creation.domain.common.integration.azure.model.AudioQuality;
import ai.creatly.sky.creation.domain.common.integration.azure.model.ssml.SpeakVoice;
import ai.creatly.sky.creation.domain.common.integration.azure.model.ssml.SpeakVoiceExpression;
import ai.creatly.sky.creation.domain.common.integration.azure.model.ssml.SpeakVoiceProsody;
import ai.creatly.sky.creation.domain.common.integration.did.model.CreateTalkRequest;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.aitask.model.response.BaseUserAiTaskVM;
import ai.creatly.sky.creation.domain.core.aittalk.model.AiTalkActor;
import ai.creatly.sky.creation.domain.core.aittalk.model.request.CreateTalkVideoTextInput;
import ai.creatly.sky.creation.domain.core.aittalk.model.response.AiTalkVideoItemVM;
import ai.creatly.sky.creation.domain.core.aittalk.model.response.AiTalkVideoVM;
import ai.creatly.sky.creation.domain.core.aittalk.model.task.AiTalkTaskBizType;
import ai.creatly.sky.creation.domain.core.aittalk.model.task.param.*;
import ai.creatly.sky.creation.domain.core.aittalk.model.task.result.AiTalkVideoResult;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.FileMetadata;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.voice.model.enums.EmotionDegreeEnum;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechPitchType;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechRateType;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechVolumeType;
import ai.creatly.sky.creation.domain.support.speech.mapper.AzureVoiceMapper;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.json.JSON;
import com.jspeeder.core.util.time.Times;
import com.jspeeder.core.util.xml.XML;
import jodd.util.StringPool;
import lombok.Setter;
import org.apache.commons.text.StringEscapeUtils;
import org.jetbrains.annotations.Nullable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version AiTalkVideoMapper.java, v 0.1 2023-06-24 01:25 joton
 */
@Mapper(config = BaseMapperConfig.class)
public abstract class AiTalkVideoMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper   userFileHelper;
    @Setter(onMethod_ = @Autowired)
    private AzureVoiceMapper azureVoiceMapper;

    /*----------------------------- 自建数字人任务构建 -----------------------------*/

    public CreateTalkScript toCreateTalkScript(CreateTalkVideoTextInput textInput) {
        CreateTalkScriptProvider voiceProvider = new CreateTalkScriptProvider()
                .setType(CreateTalkScriptProviderType.microsoft)
                .setVoiceId(textInput.getVoiceName());

        CreateTalkScript script = new CreateTalkScript()
                .setType(CreateTalkScriptType.text)
                .setProvider(voiceProvider)
                .setSubtitles(false);

        SpeakVoice speakVoice = azureVoiceMapper.toSpeakVoice(textInput);
        String content = speakVoice.getContent();
        if (StringUtils.hasText(content) && !"prosody".equals(XML.parseRootName(content))) {
            script.setSsml(false).setInput(content);
            CreateTalkVoiceConfig voiceConfig = new CreateTalkVoiceConfig()
                    .setStyle(textInput.getStyle())
                    .setRate(textInput.getRate())
                    .setPitch(textInput.getPitch());
            script.getProvider().setVoiceConfig(voiceConfig);
        }
        if (speakVoice.getExpression() != null) {
            script.setSsml(true).setInput(XML.toXMLString(speakVoice.getExpression()));
        }
        if (StringUtils.hasText(content) && "prosody".equals(XML.parseRootName(content))) {
            script.setSsml(true).setInput(content);
        }
        return script;
    }

    public CreateTalkScript toCreateTalkScript(UserFile audioFile) {
        return new CreateTalkScript()
                .setType(CreateTalkScriptType.audio)
                .setInput(UserFileHelper.toOssUrl(audioFile));
    }

    public AiTask toSelfTalkTask(UserContext userContext, AiTalkActor actor, CreateTalkScript script,
                                 @Nullable UserFile audioFile, Duration estimatedFinishedDuration) {
        String requestId = String.valueOf(IdHelper.getId());

        AiTalkVideoParam param = new AiTalkVideoParam()
                .setActorId(actor.getId())
                .setImageId(actor.getImageId())
                .setImageUrl(actor.getImageUrl())
                .setScriptType(script.getType())
                .setScript(script);
        if (audioFile != null) {
            param.setAudioId(audioFile.getId());
            // 音频长度
            if (audioFile.getMetadata().getDuration() != null) {
                param.setAudioDuration((long) Times.toRoundedSeconds(audioFile.getMetadata().getDuration()));
            }
            param.setAudioUrl(UserFileHelper.toOssUrl(audioFile));
        } else {
            // todo 音质应该和会员等级挂钩
            param.setOutputAudioQuality(AudioQuality.MP3_48Khz_192KBitRate);
        }

        return AiTask.buildNew()
                .owner(userContext)
                .taskType(AiTaskType.CREATE_AI_TALK_VIDEO)
                .bizType(AiTalkTaskBizType.SELF_TALK)
                .bizNo(requestId)
                .subBizNo(StringPool.EMPTY)
                .bizParams(param)
                .bizStatus(AiTaskStatus.CREATED.name())
                .estimatedDuration(estimatedFinishedDuration)
                .build();
    }

    /*----------------------------- DID数字人任务构建 -----------------------------*/

    public CreateTalkRequest toCreateDidTalkRequest(AiTalkActor actor, CreateTalkVideoTextInput textInput) {
        String sourceUrl = userFileHelper.getHttpUrl(actor.getImageUrl(), actor.getUid());

        CreateTalkRequest talkRequest = new CreateTalkRequest();
        talkRequest.setSourceUrl(sourceUrl);
        talkRequest.setScript(this.toCreateTalkScript(textInput));
        // 当开启时，生成结果会返回一个s3地址，解析比较麻烦。所以不在DID侧做持久化，当获取到生成结果后直接转存到我们自己的OSS。
        // 如果不持久化，DID生成的文件链接只有24小时有效期
        talkRequest.setPersist(false);
        return talkRequest;
    }

    public CreateTalkRequest toCreateDidTalkRequest(AiTalkActor actor, UserFile audioFile) {
        String sourceUrl = userFileHelper.getHttpUrl(actor.getImageUrl(), actor.getUid());

        CreateTalkScript script = new CreateTalkScript()
                .setType(CreateTalkScriptType.audio)
                .setInput(userFileHelper.getHttpUrl(audioFile));

        CreateTalkRequest talkRequest = new CreateTalkRequest();
        talkRequest.setSourceUrl(sourceUrl);
        talkRequest.setScript(script);
        // 当开启时，生成结果会返回一个s3地址，解析比较麻烦。所以不在DID侧做持久化，当获取到生成结果后直接转存到我们自己的OSS。
        talkRequest.setPersist(false);
        return talkRequest;
    }

    public AiTask toDidTalkTask(UserContext userContext, CreateTalkRequest talkRequest, @Nullable UserFile audioFile, AiTalkActor actor) {
        AiTalkVideoParam param = new AiTalkVideoParam()
                .setActorId(actor.getId())
                .setImageId(actor.getImageId())
                .setImageUrl(actor.getImageUrl())
                .setScriptType(talkRequest.getScript().getType())
                .setScript(talkRequest.getScript());
        if (audioFile != null) {
            param.setAudioId(audioFile.getId());
            param.setAudioUrl(UserFileHelper.toOssUrl(audioFile));
        }
        AiTask aiTask = AiTask.buildNew()
                .owner(userContext)
                .taskType(AiTaskType.CREATE_AI_TALK_VIDEO)
                .bizType(AiTalkTaskBizType.DID_TALK)
                .bizNo("fake_" + IdHelper.getPrettyId())
                .subBizNo(StringPool.EMPTY)
                .bizParams(JSON.toJSONObject(param))
                .build();

        // 将 DID 的请求参数原样存储起来
        JSON.toMap(talkRequest).forEach(aiTask.getBizParams()::put);
        return aiTask;
    }

    /*----------------------------- 任务解析 -----------------------------*/

    public CreateTalkVideoTextInput toTextInput(CreateTalkScript talkScript) {
        Asserts.isTrue(talkScript.getType().equals(CreateTalkScriptType.text), "必须是text输入");

        CreateTalkScriptProvider voiceProvider = talkScript.getProvider();
        Asserts.isTrue(voiceProvider.getType().equals(CreateTalkScriptProviderType.microsoft), "tts只支持微软tts");

        CreateTalkVideoTextInput textInput = new CreateTalkVideoTextInput();
        textInput.setVoiceName(voiceProvider.getVoiceId());

        // 逆向还原请求参数
        String input = talkScript.getInput();
        if (talkScript.getSsml()) {
            if (SpeakVoiceExpression.LOCAL_NAME.equals(XML.parseRootName(input))) {
                // 包含情绪
                SpeakVoiceExpression expression = XML.parseObject(input, SpeakVoiceExpression.class);
                textInput.setStyle(expression.getStyle());
                textInput.setStyleDegree(EmotionDegreeEnum.getByValue(expression.getStyleDegree()));
                textInput.setRole(expression.getRole());
                String content = expression.getContent();
                if ("prosody".equals(XML.parseRootName(content))) {
                    // 包含韵律
                    SpeakVoiceProsody prosody = XML.parseObject(content, SpeakVoiceProsody.class);
                    textInput.setText(StringEscapeUtils.unescapeXml(prosody.getContent()));
                    textInput.setRate(SpeechRateType.rateOf(prosody.getRate()));
                    textInput.setPitch(SpeechPitchType.getByValue(prosody.getPitch()));
                    textInput.setVolume(SpeechVolumeType.getByValue(prosody.getVolume()));
                } else {
                    // 不包含韵律
                    textInput.setText(content);
                }
            } else {
                // 包含韵律
                SpeakVoiceProsody prosody = XML.parseObject(input, SpeakVoiceProsody.class);
                Asserts.notNull(prosody, "will not happen!");
                textInput.setText(StringEscapeUtils.unescapeXml(prosody.getContent()));
                textInput.setRate(SpeechRateType.rateOf(prosody.getRate()));
                textInput.setPitch(SpeechPitchType.getByValue(prosody.getPitch()));
                textInput.setVolume(SpeechVolumeType.getByValue(prosody.getVolume()));
            }
        } else {
            // 简单纯文本，不使用 ssml 语法
            CreateTalkVoiceConfig voiceConfig = voiceProvider.getVoiceConfig();
            textInput.setText(input);
            textInput.setStyle(voiceConfig.getStyle());
            textInput.setRate(voiceConfig.getRate());
            textInput.setPitch(voiceConfig.getPitch());
        }
        return textInput;
    }

    /*----------------------------- 任务查询 -----------------------------*/

    public AiTalkVideoItemVM toAiTalkVideoItemVM(AiTask aiTask, @Nullable UserFile audioFile) {
        AiTalkVideoItemVM aiTalkVideoItem = new AiTalkVideoItemVM();
        this.fillUserAiTaskVM(aiTask, aiTalkVideoItem);
        this.fillAiTalkVideoItemVM(aiTask, audioFile, aiTalkVideoItem);
        return aiTalkVideoItem;
    }

    @Mapping(target = "finishedDurationSeconds", source = "sysResult.finishedDuration.seconds")
    @Mapping(target = "estimatedDurationSeconds", source = "estimatedDuration.seconds")
    protected abstract void fillUserAiTaskVM(AiTask aiTask, @MappingTarget BaseUserAiTaskVM aiTalkVideo);

    private void fillAiTalkVideoItemVM(AiTask aiTask, @Nullable UserFile audioFile, AiTalkVideoItemVM aiTalkVideoItem) {
        // 输入参数
        AiTalkVideoParam param = aiTask.parseBizParams(AiTalkVideoParam.class);
        aiTalkVideoItem.setActorId(param.getActorId().toString());

        String imageUrl = param.getImageUrl();
        if (StringUtils.hasText(imageUrl)) {
            imageUrl = userFileHelper.getHttpUrl(imageUrl, aiTask.getOwnerId());
            aiTalkVideoItem.setSourceImageUrl(imageUrl);
        }

        // 视频时长（等于音频时长）
        // 历史
        Optional.ofNullable(audioFile)
                .map(UserFile::getMetadata)
                .map(FileMetadata::getDuration)
                .ifPresent(duration -> aiTalkVideoItem.setVideoDuration((long) Times.toRoundedSeconds(duration)));
    }

    public AiTalkVideoVM toAiTalkVideoVM(AiTask aiTask, @Nullable UserFile audioFile) {
        AiTalkVideoVM aiTalkVideo = new AiTalkVideoVM();
        this.fillUserAiTaskVM(aiTask, aiTalkVideo);
        this.fillAiTalkVideoItemVM(aiTask, audioFile, aiTalkVideo);
        this.toAiTalkVideoVMAfter(aiTask, aiTalkVideo);
        return aiTalkVideo;
    }

    private void toAiTalkVideoVMAfter(AiTask aiTask, AiTalkVideoVM aiTalkVideo) {
        // 输入参数
        AiTalkVideoParam param = aiTask.parseBizParams(AiTalkVideoParam.class);
        var scriptType = param.getScriptType();
        String input = param.getScript().getInput();
        if (scriptType == CreateTalkScriptType.text) {
            aiTalkVideo.setText(input);
        } else {
            // 将 OSS 地址转换为 HTTP 地址
            aiTalkVideo.setAudioUrl(userFileHelper.getHttpUrl(input, aiTask.getOwnerId()));
        }

        // 输出结果
        AiTalkVideoResult result = aiTask.parseBizResult(AiTalkVideoResult.class);
        String resultUrl = result.getResultUrl();
        if (StringUtils.hasText(resultUrl)) {
            // 将 OSS 地址转换为 HTTP 地址
            aiTalkVideo.setResultUrl(userFileHelper.getHttpUrl(resultUrl, aiTask.getOwnerId()));
        }
    }
}
