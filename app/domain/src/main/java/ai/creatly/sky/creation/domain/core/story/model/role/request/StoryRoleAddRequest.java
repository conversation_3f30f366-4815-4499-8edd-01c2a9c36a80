/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.request;

import ai.creatly.sky.creation.domain.core.story.model.role.StoryRoleGender;
import ai.creatly.sky.creation.domain.core.story.model.role.StoryRoleGeneration;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 手动新增故事角色
 */
@Data
public class StoryRoleAddRequest {
    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 20, message = "角色名称不能超过20个字")
    private String              roleName;
    /**
     * 角色性别
     */
    private StoryRoleGender     roleGender;
    /**
     * 角色年龄段
     */
    private StoryRoleGeneration roleGeneration;
    /**
     * 角色描述
     */
    @NotBlank(message = "角色描述不能为空")
    @Size(max = 300, message = "角色描述不能超过300个字")
    private String              roleDesc;
}
