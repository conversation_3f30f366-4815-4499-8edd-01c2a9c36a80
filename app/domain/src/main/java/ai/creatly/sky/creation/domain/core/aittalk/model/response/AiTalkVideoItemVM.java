package ai.creatly.sky.creation.domain.core.aittalk.model.response;

import ai.creatly.sky.creation.domain.core.aitask.model.response.BaseUserAiTaskVM;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI语音视频的创作任务（用于列表项）
 *
 * <AUTHOR>
 * @version AiTalkVideoItemVM.java, v 0.1 2023-06-22 23:02 joton
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AiTalkVideoItemVM extends BaseUserAiTaskVM {

    /**
     * 输入的演员ID
     */
    private String actorId;
    /**
     * 输入的图片URL
     */
    private String sourceImageUrl;
    /**
     * 时长（秒）
     */
    private Long videoDuration;
}
