/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.task.param;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizParams;
import ai.creatly.sky.creation.domain.core.story.model.role.StoryRole;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryStyle;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @version StoryRoleImgGenParam.java, v 0.1 2024-03-30 17:49 syoka
 */
@Data
@Accessors(chain = true)
public class StoryRoleImgGenParam implements TaskBizParams {

    /**
     * 故事角色
     */
    private StoryRole  storyRole;
    /**
     * 角色图片比例
     */
    private String     scale;
    /**
     * 画面风格
     */
    private StoryStyle storyStyle;
    /**
     * 角色图提示词（如果为空，则需要在任务预处理逻辑中重新生成提示词）
     */
    @Nullable
    private String     rolePortraitPrompt;
}
