/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.video.model.task.param;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizParams;
import ai.creatly.sky.creation.domain.support.multimedia.model.AudioMetadata;
import jakarta.annotation.Nullable;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version ProductVideoReTalkParams.java, v 0.1 2024-05-25 下午8:27 zhoudong
 */
@Data
@Accessors(chain = true)
public class ProductVideoReTalkParams implements TaskBizParams {

    /**
     * 输入视频文件ID
     */
    private Long          videoFileId;
    /**
     * 输入视频地址（OSS格式）
     */
    private String        videoUrl;
    /**
     * 输入视频时长
     */
    private Duration      videoDuration;
    /**
     * 输入视频文件名
     */
    private String        videoFilename;
    /**
     * 输入音频文件ID
     */
    private Long          audioFileId;
    /**
     * 输入音频地址（OSS格式）
     */
    private String        audioUrl;
    /**
     * 输入音频元数据
     */
    private AudioMetadata audioMetadata;
    /**
     * 输入换脸图片文件ID
     */
    @Nullable
    private Long          faceImageFileId;
    /**
     * 输入换脸图片地址（OSS格式）
     */
    @Nullable
    private String        faceImageUrl;
    /**
     * 视频标题
     */
    private String        title;
    /**
     * 视频描述
     */
    private String        description;
}
