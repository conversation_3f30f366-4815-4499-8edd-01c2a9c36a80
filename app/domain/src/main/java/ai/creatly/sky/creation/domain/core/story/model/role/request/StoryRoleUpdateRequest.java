/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.request;

import ai.creatly.sky.creation.domain.core.story.model.role.StoryRoleGender;
import ai.creatly.sky.creation.domain.core.story.model.role.StoryRoleGeneration;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class StoryRoleUpdateRequest {
    /**
     * 角色名称
     */
    @Size(max = 20, message = "角色名称不能超过20个字")
    private String              roleName;
    /**
     * 角色性别
     */
    private StoryRoleGender     roleGender;
    /**
     * 角色年龄段
     */
    private StoryRoleGeneration roleGeneration;
    /**
     * 角色描述
     */
    @Size(max = 300, message = "角色描述不能超过300个字")
    private String              roleDesc;
    /**
     * 角色形象图文件ID
     */
    @Size(max = 20, message = "角色形象图文件ID格式错误")
    private String              rolePortraitId;
    /**
     * 角色声音ID
     */
    @Size(max = 20, message = "角色声音ID格式错误")
    private String              voiceId;
}
