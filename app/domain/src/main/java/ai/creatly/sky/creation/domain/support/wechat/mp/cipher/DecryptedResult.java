/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.wechat.mp.cipher;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version DecryptedResult.java, v 0.1 2024-10-15 下午8:24 zhoudong
 */
@Data
@Accessors(chain = true)
public class DecryptedResult {
    private boolean success;
    private String  xml;
    private String  fromAppId;
}
