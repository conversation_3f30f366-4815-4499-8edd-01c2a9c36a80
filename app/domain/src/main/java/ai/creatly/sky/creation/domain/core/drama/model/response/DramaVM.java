/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.model.response;

import ai.creatly.sky.creation.domain.core.drama.model.DramaStats;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaStatus;
import ai.creatly.sky.creation.domain.core.drama.model.enums.DramaType;
import ai.creatly.sky.creation.domain.support.category.model.CategoryPath;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version DramaVM.java, v 0.1 2024-10-18 下午9:02 zhoudong
 */
@Data
public class DramaVM {

    private String             id;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime      createdAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime      updatedAt;
    private String             name;
    private DramaType          type;
    private String             typeDesc;
    private DramaStatus        status;
    private List<CategoryPath> productCategories;
    private List<CategoryPath> crowdCategories;
    private String             videoUrl;
    @Nullable
    private String             endText;
    private String             coverUrl;
    private DramaStats         stats;
    /**
     * 剧情审核明细
     */
    private DramaReviewVM      review;
}
