/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.service;

import ai.creatly.sky.creation.domain.core.story.model.scene.SceneAtmosphere;
import com.jspeeder.core.util.json.JSON;
import dev.langchain4j.model.output.OutputParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version StorySceneAtmosphereParser.java, v 0.1 2024-04-07 18:03 syoka
 */
@Component
@Slf4j
public class StorySceneAtmosphereParser implements OutputParser<SceneAtmosphere> {
    @Override
    public SceneAtmosphere parse(String text) {
        try {
            return JSON.parseObject(text, SceneAtmosphere.class);
        } catch (Exception e) {
            log.info("[StorySceneAtmosphere] parse sceneAtmosphere failure,text:{}", text);
            return null;
        }
    }

    @Override
    public String formatInstructions() {
        return "fill up with scene atmosphere";
    }
}
