/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version DramaAction.java, 2024-10-28 下午8:20 zhoudong
 */
@RequiredArgsConstructor
@Getter
public enum DramaAction {

    /**
     * 开始审批
     */
    start_review(DramaStatus.pending_review, DramaStatus.in_review),
    /**
     * 拒绝
     */
    reject(DramaStatus.in_review, DramaStatus.rejected),
    /**
     * 通过（试运行）
     */
    approve(DramaStatus.in_review, DramaStatus.beta),
    /**
     * 发布（进入公有剧情库）
     */
    release(DramaStatus.beta, DramaStatus.released),
    ;

    private final DramaStatus from;
    private final DramaStatus to;
}
