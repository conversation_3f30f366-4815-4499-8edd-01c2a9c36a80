/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.exam.repository;

import ai.creatly.sky.creation.domain.core.ai.exam.model.AiExamCert;

/**
 * AI认证考试
 * <AUTHOR>
 * @version AiCertRepository.java, v0.1 2025-02-19 21:17
 */
public interface AiCertRepository {

    AiExamCert getCertById(Long certId);

    AiExamCert getCertByCourseId(Long courseId, String orgCode);

    AiExamCert saveOrUpdate(AiExamCert aiExamCert);

}
