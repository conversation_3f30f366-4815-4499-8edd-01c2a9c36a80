/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.video.model;

import ai.creatly.sky.creation.domain.core.userfile.model.response.FileRefVM;
import ai.creatly.sky.creation.domain.core.videoproject.model.enums.VideoAspectRatio;
import lombok.Data;
import org.springframework.lang.Nullable;

import java.util.List;

/**
 * <AUTHOR>
 * @version VideoGenerationVM.java, v0.1 2025-02-19 21:41
 */
@Data
public class VideoGenerationVM extends AiVideoTaskVM {
    private String           promptText;
    private List<FileRefVM>  referImageFiles;
    private VideoAspectRatio aspectRatio;
    @Nullable
    private String           errorMsg;
}
