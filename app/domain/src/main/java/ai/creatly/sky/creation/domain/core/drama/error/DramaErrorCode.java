/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.error;

import com.jspeeder.core.data.problem.error.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version DramaErrorCode.java, v 0.1 2024-10-18 下午8:31 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum DramaErrorCode implements ErrorCode {

    DRAMA_NOT_FOUND("剧情不存在"),
    DRAMA_INVALID("剧情无效，审核还未通过"),
    DRAMA_VIDEO_SPLIT_SIZE_NOT_SUPPORT("剧情暂不支持两段以上切片"),
    DRAMA_SPOKESPERSON_UNSET("剧情的广告代言人未设置"),
    DRAMA_PREPROCESS_TASK_NOT_FOUND("该剧情的预处理任务未找到"),
    ;

    private final String msg;

    @Override
    public String getCode() {
        return this.name();
    }
}
