/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.service;

import ai.creatly.sky.creation.domain.common.caching.CachingTemplate;
import ai.creatly.sky.creation.domain.core.user.error.UserErrorCode;
import ai.creatly.sky.creation.domain.core.user.model.request.UserVerifyType;
import ai.creatly.sky.creation.domain.core.user.model.request.VerifyCodeScenario;
import ai.creatly.sky.creation.domain.support.notification.sms.SmsUtils;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.text.FormatUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.rng.UniformRandomProvider;
import org.apache.commons.rng.simple.RandomSource;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version SmsVerifyCodeManager.java, v 0.1 2023-09-24 21:48 syoka
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SmsVerifyCodeManager {

    private static final String USER_SMS_VERIFY_CODE_PREFIX = "USER_{}_{}_VERIFY_CODE:{}";

    private final UniformRandomProvider rng = RandomSource.XO_RO_SHI_RO_128_PP.create();
    private final CachingTemplate       cachingTemplate;

    public String generateVerifyCode(VerifyCodeScenario scenario, String phone) {
        // 必须是中国的手机号
        if (!SmsUtils.isChinesePhone(phone)) {
            throw new BizException(UserErrorCode.USER_INPUT_IS_NOT_CHINESE_PHONE_NUMBER);
        }

        // 检查手机号存在有效的验证码
        String userSmsKey = FormatUtil.format(USER_SMS_VERIFY_CODE_PREFIX, scenario, UserVerifyType.PHONE, phone);
        String code = cachingTemplate.getString(userSmsKey);
        if (StringUtils.isEmpty(code)) {
            // 3分钟内有效（冗余3秒缓冲）
            String verifyCode = generateRandomCode();
            cachingTemplate.set(userSmsKey, verifyCode, Duration.ofMinutes(2).plusSeconds(3));
            log.info("Send code,userSmsKey:" + userSmsKey+",code:"+verifyCode);
            return verifyCode;
        }
        // 一个手机号最多只能有一条有效的验证码
        throw new BizException(UserErrorCode.USER_OPS_FREQUENCY_TOO_HIGH, phone);
    }

    public boolean validateUserVerifyCode(VerifyCodeScenario scenario, String phone, String smsCode) {
        if (StringUtils.isNotEmpty(phone) && StringUtils.isNotEmpty(smsCode)) {
            String userSmsKey = FormatUtil.format(USER_SMS_VERIFY_CODE_PREFIX, scenario, UserVerifyType.PHONE, phone);
            log.info("Verify code,userSmsKey:" + userSmsKey + ",code:" + smsCode);
            String mobileSmsCode = cachingTemplate.getString(userSmsKey);
            if (StringUtils.isNotEmpty(mobileSmsCode) && StringUtils.equals(smsCode, mobileSmsCode)) {
                cachingTemplate.delete(userSmsKey);
                return true;
            }
        }
        return false;
    }

    /**
     * 生成一次性码
     */
    private String generateRandomCode() {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(rng.nextInt(0, 10));
        }
        return code.toString();
    }
}
