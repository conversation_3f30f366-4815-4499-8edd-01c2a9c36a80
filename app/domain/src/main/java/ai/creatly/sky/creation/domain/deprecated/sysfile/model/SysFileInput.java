/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.deprecated.sysfile.model;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.userfile.model.BaseFileInput;
import ai.creatly.sky.creation.domain.core.userfile.model.FileMetadata;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import jodd.io.FileNameUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

/**
 * 系统文件输入（不区分环境，所有环境可共享同一份文件，但区分访问权限，写入后可根据实际诉求修改访问权限）
 *
 * <AUTHOR>
 * @version SysFileInput.java, v 0.1 2023-11-18 上午11:48 zhoudong
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SysFileInput extends BaseFileInput {

    /**
     * 直接指定文件的OSS存储空间
     */
    private String       bucket;
    /**
     * 文件访问控制
     */
    private FileAcl      acl;
    /**
     * 直接指定文件的OSS对象key
     */
    @Nullable
    private String       key;
    /**
     * 文件元数据
     */
    @Nullable
    private FileMetadata fileMetadata;

    public SysFileInput setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
        if (StringUtils.isNotBlank(originalFilename) && this.extension == null) {
            this.extension = FileNameUtil.getExtension(originalFilename);
        }
        return this;
    }

    public SysFileInput setType(FileType type) {
        this.type = type;
        return this;
    }

    public SysFileInput setBizSource(FileBizSource bizSource) {
        this.bizSource = bizSource;
        return this;
    }

    public SysFileInput setExtension(String extension) {
        this.extension = extension;
        return this;
    }
}
