/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.category.service;

import ai.creatly.sky.creation.domain.support.category.model.CategoryPath;
import ai.creatly.sky.creation.domain.support.category.model.enums.CategoryDomain;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version CategoryService.java, v 0.1 2024-10-17 下午5:58 zhoudong
 */
@Service
@RequiredArgsConstructor
public class CategoryService {

    private final CategoryRepository categoryRepository;

    public CategoryPath toCategoryPath(CategoryDomain domain, List<String> codes) {
        List<String> names = categoryRepository.queryNamesByDomainAndPathCodes(domain, codes);
        return new CategoryPath()
                .setCodes(codes)
                .setNames(names);
    }
}
