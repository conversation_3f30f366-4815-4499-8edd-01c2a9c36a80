/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.deprecated.menu.mapper;

import ai.creatly.sky.creation.domain.deprecated.menu.model.Menu;
import ai.creatly.sky.creation.domain.deprecated.menu.model.MenuItem;
import ai.creatly.sky.creation.domain.deprecated.menu.model.response.MenuItemVM;
import ai.creatly.sky.creation.domain.deprecated.menu.model.response.MenuVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.util.Asserts;
import com.jspeeder.core.util.ListUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version MenuMapper.java, v 0.1 2023-06-13 23:33 joton
 */
@Mapper(config = BaseMapperConfig.class)
public interface MenuMapper {

    MenuVM toMenuVM(Menu menu);

    default List<MenuItemVM> toMenuItemVMs(List<MenuItem> items) {
        return this.toMenuItemVMs(null, items);
    }

    default List<MenuItemVM> toMenuItemVMs(String parentCode, List<MenuItem> items) {
        return items.stream().filter(item -> Objects.equals(parentCode, item.getParentCode())).map(item -> {
            MenuItemVM itemVM = this.toMenuItemVM(item);
            // 断言目的是兜底脏数据导致的死循环
            Asserts.notBlank(item.getCode(), "菜单项标识不可能为空");
            itemVM.setSubItems(ListUtil.emptyToNull(this.toMenuItemVMs(item.getCode(), items)));
            return itemVM;
        }).toList();
    }

    @Mapping(target = "subItems", ignore = true)
    MenuItemVM toMenuItemVM(MenuItem item);
}
