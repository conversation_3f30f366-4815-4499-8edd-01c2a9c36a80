/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.speech.model.response;

import com.jspeeder.core.data.enums.Option;
import lombok.Data;

import java.util.List;

/**
 * 语音合成的声音
 *
 * <AUTHOR>
 * @version SynthesisVoiceVM.java, v 0.1 2023-05-21 15:59 joton
 */
@Data
public class SynthesisVoiceVM {

    /**
     * 完整的语音名称（用于语音合成API）
     */
    private String            name;
    /**
     * 英文名
     */
    private String            shortName;
    /**
     * 中文展示名
     */
    private String            displayName;
    /**
     * 性别（Female/Male）
     */
    private String            gender;
    /**
     * 语言
     */
    private Option            locale;
    /**
     * 头像
     */
    private String            avatar;
    /**
     * 支持的声音角色（如果为空，则仅支持默认的成人角色）
     */
    private List<VoiceOption> roles;
    /**
     * 支持的声音风格（如果为空，则仅支持默认的中性风格）
     */
    private List<VoiceOption> styles;
}
