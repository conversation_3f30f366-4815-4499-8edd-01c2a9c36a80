/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.user.mapper;


import ai.creatly.sky.creation.domain.core.user.model.organization.UserOrganization;
import ai.creatly.sky.creation.domain.core.user.model.response.UserOrganizationVM;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version UserOrganizationMapper.java, v 0.1 2024-01-22 14:18 syoka
 */
@Mapper(config = BaseMapperConfig.class)
public interface UserOrganizationMapper {

    UserOrganizationVM toUserOrganizationVM(UserOrganization userOrganization);

    default List<UserOrganizationVM> toUserOrganizationVM(List<UserOrganization> userOrganization) {
        return userOrganization.stream().map(this::toUserOrganizationVM).toList();
    }

}
