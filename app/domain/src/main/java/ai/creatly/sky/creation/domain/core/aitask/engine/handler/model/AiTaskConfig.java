/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.engine.handler.model;

import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import com.jspeeder.core.data.enums.ICode;
import jakarta.annotation.Nullable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version AiTaskConfig.java, v 0.1 2023-07-28 21:00 joton
 */
@Data
@RequiredArgsConstructor(staticName = "of")
@EqualsAndHashCode(of = {"taskType", "bizType"})
@Accessors(chain = true)
public class AiTaskConfig {

    /**
     * 任务类型
     */
    private final AiTaskType taskType;

    /**
     * 关联外部业务类型
     */
    @Nullable
    private String        bizType;
    /**
     * 每次调度任务捞取数量（默认10条）
     */
    private Integer       loadSize                = 10;

    /**
     * 默认任务执行尝试次数
     */
    private Integer       execTryLimit            = 360;

    /**
     * 有效执行次数告警阈值（默认20次，即任务有效执行次数达到20次后，每隔20次，会告警一次）
     */
    private Integer       execCountAlertThreshold = 20;
    /**
     * 执行锁失效时间（默认3s，在执行锁有效期间内，可以阻止同一条任务并发执行）todo 待实现
     */
    private Duration      execLockTimeout         = Duration.ofSeconds(3);
    /**
     * 任务排队模式
     */
    private TaskQueueMode queueMode               = TaskQueueMode.POP_PER_EXECUTED;
    /**
     * 执行发生连续异常时，到下次执行延迟的时间（呈现衰减趋势）todo 待实现
     * <p/>
     * 奇数位：延迟的分钟数
     * 偶数位：重复次数（-1表示一直重复）
     */
    private int[]         delayedMinutesOnError   = new int[]{1, 3, 5, 3, 10, 3, 30, 3, 60, -1};
    /**
     * 最大可容忍的回滚次数（达到最大回滚次数后，任务将自动取消）
     */
    private Integer       maxRollbackCount        = 3;
    /**
     * 任务提交时是否发送通知消息（默认通知）
     */
    private Boolean       notifyOnSubmit          = true;
    /**
     * 任务完结后是否发送站内通知（默认不通知）
     */
    private Boolean       notifyUserOnCompleted   = false;

    public AiTaskConfig setBizType(String bizType) {
        this.bizType = bizType;
        return this;
    }

    public AiTaskConfig setBizType(ICode bizType) {
        this.bizType = bizType.getCode();
        return this;
    }

    // TODO: 2023/11/17 自动挂起任务的最大容忍执行次数

    // TODO: 2023/11/17 自动挂起任务的最大容忍连续失败次数
}
