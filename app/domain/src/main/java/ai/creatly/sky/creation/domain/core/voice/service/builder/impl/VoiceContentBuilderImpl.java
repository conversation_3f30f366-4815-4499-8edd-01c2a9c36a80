/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.service.builder.impl;

import ai.creatly.sky.creation.domain.common.util.TextUtil;
import ai.creatly.sky.creation.domain.core.voice.error.VoiceErrorCode;
import ai.creatly.sky.creation.domain.core.voice.model.Voice;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceEmotion;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceLocale;
import ai.creatly.sky.creation.domain.core.voice.model.VoiceSynthesis;
import ai.creatly.sky.creation.domain.core.voice.model.admin.request.AudioSubtitleRequest;
import ai.creatly.sky.creation.domain.core.voice.model.enums.EmotionDegreeEnum;
import ai.creatly.sky.creation.domain.core.voice.model.enums.SpeechRateType;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.VoiceCreationContent;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.VoiceDialogue;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment.VoiceBreak;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment.VoiceSegment;
import ai.creatly.sky.creation.domain.core.voice.model.synthesis.segment.VoiceSegmentType;
import ai.creatly.sky.creation.domain.core.voice.repository.VoiceCacheableRepository;
import ai.creatly.sky.creation.domain.core.voice.service.VoiceChecker;
import ai.creatly.sky.creation.domain.core.voice.service.builder.VoiceContentBuilder;
import ai.creatly.sky.creation.domain.support.multimedia.model.AudioSubtitle;
import com.jspeeder.core.util.Validates;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version VoiceContentBuilderImpl.java, v 0.1 2024-09-27 下午1:23 zhoudong
 */
@Component
@RequiredArgsConstructor
public class VoiceContentBuilderImpl implements VoiceContentBuilder {

    private final VoiceCacheableRepository voiceCacheableRepository;
    private final VoiceChecker             voiceChecker;

    @Override
    public VoiceCreationContent buildContent(VoiceSynthesis synthesis, int maxWords, AudioSubtitleRequest subtitleRequest) {
        // 文本字数校验
        String totalText = String.join("", synthesis.getTextList());
        Validates.isTrue(TextUtil.countWords(totalText) > 0, "输入文本不存在有效内容");
        Validates.isTrue(TextUtil.countWords(totalText) <= maxWords, "输入文本不能超过{}字", maxWords);

        // 声音校验
        Voice voice = voiceCacheableRepository.queryOptionalById(synthesis.getVoiceId()).orElse(null);
        Validates.notNull(voice, VoiceErrorCode.VOICE_NOT_FOUND);
        String localeCode = synthesis.getLocaleCode();
        String emotionCode = VoiceEmotion.DEFAULT_CODE.equals(synthesis.getEmotionCode()) ? null : synthesis.getEmotionCode();
        VoiceLocale voiceLocale = voiceChecker.synthesizeLocaleCheck(voice, localeCode, null, emotionCode);

        // 文本内容校验
        voiceChecker.checkTextLang(voiceLocale, totalText);

        // 构造语音创作内容
        List<String> textList = synthesis.getTextList();
        List<VoiceSegment> segments = new ArrayList<>();
        List<Integer> breakEndIndexes = new ArrayList<>();
        int textLength = 0;
        for (int i = 0; i < textList.size(); i++) {
            String text = textList.get(i);
            segments.add(new VoiceSegment().setType(VoiceSegmentType.TEXT).setText(text));
            if (i < textList.size() - 1) {
                // 每段文本之间插入短暂停顿
                int breakMills = ObjectUtils.defaultIfNull(synthesis.getBreakMills(), 100);
                segments.add(new VoiceSegment().setType(VoiceSegmentType.SHORT_BREAK).setShortBreak(new VoiceBreak().setMills(breakMills)));
                textLength += text.length();
                breakEndIndexes.add(textLength - 1);
            }
        }
        AudioSubtitle subtitle = new AudioSubtitle()
                .setFontSize(subtitleRequest.getFontSize())
                .setWidth(subtitleRequest.getWidth())
                .setBreakEndIndexes(breakEndIndexes);

        EmotionDegreeEnum emotionDegree = emotionCode == null ? null : synthesis.getEmotionDegree();
        SpeechRateType rate = synthesis.getRate() == SpeechRateType.RATE_100 ? null : synthesis.getRate();
        VoiceDialogue voiceDialogue = new VoiceDialogue()
                .setVoice(voice)
                .setEmotionCode(emotionCode)
                .setEmotionDegree(emotionDegree)
                .setRate(rate)
                .setSegments(segments);

        return new VoiceCreationContent()
                .setDialogues(List.of(voiceDialogue))
                .setSubtitle(subtitle);
    }
}
