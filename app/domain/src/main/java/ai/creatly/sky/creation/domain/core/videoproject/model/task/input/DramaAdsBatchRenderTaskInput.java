/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model.task.input;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizInput;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.util.Set;

/**
 *
 * <AUTHOR>
 * @version DramaAdsBatchRenderTaskInput.java, 2024-11-29 下午4:40 zhoudong
 */
@Data
@Accessors(chain = true)
public class DramaAdsBatchRenderTaskInput extends DramaAdsBatchComposeTaskInput implements TaskBizInput {
    /**
     * 对指定的编排任务做视频渲染，如果不指定，则为全部编排任务
     */
    @Nullable
    private Set<Long> composeTaskIds;
}
