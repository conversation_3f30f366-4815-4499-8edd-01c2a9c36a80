/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.model;

import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileBizSource;
import ai.creatly.sky.creation.domain.core.userfile.model.enums.FileStorageType;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.support.file.model.FileSize;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import ai.creatly.sky.creation.domain.support.multimedia.model.AudioMetadata;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jspeeder.core.model.OperatorRef;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.annotation.Id;

import java.time.Duration;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * 用户文件
 *
 * <AUTHOR>
 * @version UserFile.java, v 0.1 2023-06-22 20:00 joton
 */
@Data
@Accessors(chain = true)
@ToString
public class UserFile {

    /**
     * 主键
     */
    @Id
    private Long            id;
    /**
     * 创建时间
     */
    private ZonedDateTime   createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime   updatedAt;
    /**
     * 所属用户（-1表示平台）
     */
    private Long            uid;
    /**
     * 业务来源
     */
    private FileBizSource   bizSource;
    /**
     * 原文件名（带扩展名）
     */
    private String          originalFilename;
    /**
     * 文件扩展名（不带.号）
     */
    private String          extension;
    /**
     * 文件类型
     */
    private FileType        type;
    /**
     * 文件存储类型
     */
    private FileStorageType storageType;
    /**
     * 文件存储空间
     */
    private String          bucket;
    /**
     * 文件唯一标识（在存储空间下，包含目录+文件名）
     */
    private String          key;
    /**
     * 文件访问控制
     */
    private FileAcl         acl;
    /**
     * 创建日期（yyyyMMdd）
     */
    private LocalDate       createdDate;
    /**
     * 创建月份（yyyyMM）
     */
    private YearMonth       createdMonth;
    /**
     * 创建者
     */
    private OperatorRef     creator;
    /**
     * 文件元数据
     */
    private FileMetadata    metadata;
    /**
     * 软删除标记（相当于回收站文件）
     */
    private Boolean         trashed;
    /**
     * 文件内容
     */
    private FileContent     content;
    /**
     * 文件内容MD5（在某些业务场景下，可用于校验文件内容是否相同）
     */
    @Nullable
    private String          md5;

    // TODO 增加 temp=true/false 字段，用于标记临时文件，后续定时清理（先上传，再持久化？）

    // TODO 增加资源列表字段（用于构造key：FileKeyBuilder#params(xxx, xxx)，users/{uid}/module/resources/{rid}[/sub_resources/{rid}]/file）

    /**
     * 是否属于某用户
     *
     * @param uid 用户ID
     * @return 是否属于
     */
    public boolean belongsTo(long uid) {
        return Objects.equals(this.getUid(), uid);
    }

    /**
     * 是否属于系统用户
     *
     * @return 是否属于系统用户
     */
    @JsonIgnore
    public boolean isSystem() {
        return Objects.equals(this.getUid(), AppConstants.SYSTEM_UID);
    }

    /**
     * 文件大小 TODO 抽成独立字段，不要放到大字段里
     *
     * @return 文件大小
     */
    @JsonIgnore
    public long getBytes() {
        if (metadata.getBytes() == null) {
            // unknown
            return -1;
        }
        return metadata.getBytes();
    }

    @JsonIgnore
    public String getOssUrl() {
        return UserFileHelper.toOssUrl(this);
    }

    @Nullable
    public FileSize size() {
        if (metadata.getBytes() == null) {
            // unknown
            return null;
        }
        return FileSize.sizeInBytes(metadata.getBytes());
    }

    /**
     * 音频/视频时长
     *
     * @return 文件大小
     */
    @JsonIgnore
    @Nullable
    public Duration getDuration() {
        return metadata.getDuration();
    }

    @JsonIgnore
    @Nullable
    public AudioMetadata audioMetadata() {
        return metadata.toAudioMetadata(this);
    }

    @JsonIgnore
    public FileRef toFileRef() {
        return new FileRef()
                .setId(String.valueOf(this.id))
                .setUrl(UserFileHelper.toOssUrl(this));
    }
}
