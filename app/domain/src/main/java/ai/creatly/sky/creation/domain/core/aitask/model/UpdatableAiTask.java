/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.model;

import com.jspeeder.core.util.json.JSON;
import jakarta.annotation.Nullable;
import lombok.Data;
import lombok.experimental.Accessors;
import org.json.JSONObject;

import java.time.Duration;
import java.time.ZonedDateTime;

/**
 * 可更新的AI创作任务
 *
 * <AUTHOR>
 * @version UpdatableAiTask.java, v 0.1 2023-08-01 03:32 joton
 */
@Data
@Accessors(chain = true)
public class UpdatableAiTask {

    /**
     * 延迟到指定时间执行 TODO 还未实现
     */
    @Nullable
    private ZonedDateTime delayedAt;
    /**
     * 开始运行时间
     */
    @Nullable
    private ZonedDateTime startedAt;
    /**
     * 运行完成时间
     */
    @Nullable
    private ZonedDateTime finishedAt;
    /**
     * 关联外部业务状态
     */
    private String        bizStatus;
    /**
     * 关联外部业务单号
     */
    private String        bizNo;
    /**
     * 关联外部业务子单号
     */
    private String        subBizNo;
    /**
     * 任务进度（在不同业务场景里可以自行定义进度范围，默认0-100）
     */
    private Integer       progress;
    /**
     * 业务输入参数
     */
    private JSONObject    bizParams;
    /**
     * 业务过程数据
     */
    private JSONObject    bizExecInfo;
    /**
     * 业务输出结果
     */
    private JSONObject    bizResult;
    /**
     * 是否自动执行
     */
    private Boolean       autoExec;

    public UpdatableAiTask setBizParams(JSONObject bizParams) {
        this.bizParams = bizParams;
        return this;
    }

    public UpdatableAiTask setBizParams(TaskBizParams bizParams) {
        this.bizParams = JSON.toJSONObject(bizParams);
        return this;
    }

    public UpdatableAiTask setBizExecInfo(JSONObject bizExecInfo) {
        this.bizExecInfo = bizExecInfo;
        return this;
    }

    public UpdatableAiTask setBizExecInfo(TaskBizVars bizVars) {
        this.bizExecInfo = JSON.toJSONObject(bizVars);
        return this;
    }

    public UpdatableAiTask setBizVars(TaskBizVars bizVars) {
        this.bizExecInfo = JSON.toJSONObject(bizVars);
        return this;
    }

    public UpdatableAiTask setBizResult(TaskBizResult bizResult) {
        this.bizResult = JSON.toJSONObject(bizResult);
        return this;
    }

    @Nullable
    public <T extends TaskBizResult> T parseBizResult(Class<T> type) {
        if (this.bizResult == null) {
            return null;
        }
        return JSON.parseObject(this.bizResult.toString(), type);
    }

    public UpdatableAiTask setDelay(@Nullable Duration delayTime) {
        if (delayTime != null) {
            this.delayedAt = ZonedDateTime.now().plus(delayTime);
        }
        return this;
    }
}
