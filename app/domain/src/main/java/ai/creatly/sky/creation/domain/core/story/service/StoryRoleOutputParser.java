/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.service;

import ai.creatly.sky.creation.domain.core.story.error.StoryErrorCode;
import ai.creatly.sky.creation.domain.core.story.model.role.*;
import com.jspeeder.core.data.id.IdHelper;
import com.jspeeder.core.data.problem.exception.BizException;
import com.jspeeder.core.util.json.JSON;
import com.jspeeder.core.util.text.FormatUtil;
import dev.langchain4j.model.output.OutputParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static java.util.stream.Collectors.toList;

/**
 * 故事剧本解析出故事角色，并补全角色信息
 *
 * <AUTHOR>
 * @version StorySceneOutputParser.java, v 0.1 2024-03-09 14:49 heb
 */
@Slf4j
@Component
public class StoryRoleOutputParser implements OutputParser<List<StoryRole>> {
    @Override
    public List<StoryRole> parse(String text) {
        String escapeStr = escapeControlChars(text);
        try {
            StoryRolePromptSchema.RoleDetailInfo roleDetailInfo = JSON.parseObject(escapeStr, StoryRolePromptSchema.RoleDetailInfo.class);
            List<StoryRolePromptSchema.PersonInfo> personInfoList = roleDetailInfo.getPerson_lastinfo();
            if (Objects.nonNull(personInfoList)) {
                return personInfoList.stream().map(e -> {
                    StoryRole storyRole = new StoryRole();
                    storyRole.setId(IdHelper.getStrId());
                    storyRole.setRoleSource(StoryRoleSource.AUTOMATIC);
                    storyRole.setRoleName(e.getPerson_name());
                    storyRole.setRoleGender(StoryRoleGender.ofDesc(e.getPerson_gender()));
                    storyRole.setRoleGeneration(StoryRoleGeneration.ofDesc(e.getPerson_generation()));
                    storyRole.setRoleDesc(FormatUtil.format("{},{},{}", e.getPhysical_features(), e.getClothing_features(), e.getAccessory_features()));
                    return storyRole;
                }).collect(toList());
            }
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("parse story role error, text:{}", text, e);
            throw new BizException(StoryErrorCode.STORY_ROLE_GEN_UNKNOWN_EXCEPTION, e);
        }
    }

    @Override
    public String formatInstructions() {
        return "parse string to story draft model";
    }

    public String escapeControlChars(String text) {
        StringBuilder sb = new StringBuilder();
        for (char c : text.toCharArray()) {
            if (!Character.isISOControl(c)) {
                sb.append(c);
            }
        }
        return sb.toString();
    }
}
