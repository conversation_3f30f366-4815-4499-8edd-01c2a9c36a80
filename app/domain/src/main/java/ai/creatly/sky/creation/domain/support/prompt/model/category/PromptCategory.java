/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.support.prompt.model.category;

import ai.creatly.sky.creation.domain.common.ddd.AggregateRoot;
import ai.creatly.sky.creation.domain.support.prompt.model.PromptBizSource;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

@AggregateRoot
@Data
@Accessors(chain = true)
public class PromptCategory {

    /**
     * 分类id
     */
    private Long id;

    /**
     * 编码
     */
    private String code;

    /**
     * 分类中文名
     */
    private String name;

    /**
     * 分类英文名
     */
    private String enName;

    /**
     * 业务来源
     */
    private PromptBizSource bizSource;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    private ZonedDateTime createdAt;

    /**
     * 更新时间
     */
    private ZonedDateTime updatedAt;
}
