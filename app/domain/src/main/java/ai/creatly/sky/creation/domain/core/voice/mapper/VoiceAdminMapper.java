/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.voice.mapper;

import ai.creatly.sky.creation.domain.common.integration.azure.model.SynthesisVoiceNameEnum;
import ai.creatly.sky.creation.domain.common.integration.oss.FileAcl;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import ai.creatly.sky.creation.domain.core.userfile.service.UserFileHelper;
import ai.creatly.sky.creation.domain.core.voice.error.VoiceErrorCode;
import ai.creatly.sky.creation.domain.core.voice.model.*;
import ai.creatly.sky.creation.domain.core.voice.model.admin.request.VoiceEmotionDTO;
import ai.creatly.sky.creation.domain.core.voice.model.admin.request.VoiceLocaleDTO;
import ai.creatly.sky.creation.domain.core.voice.model.admin.request.VoiceQueryRequest;
import ai.creatly.sky.creation.domain.core.voice.model.admin.request.VoiceRoleDTO;
import ai.creatly.sky.creation.domain.core.voice.model.admin.response.VoiceLocaleVM;
import ai.creatly.sky.creation.domain.core.voice.model.admin.response.VoiceVM;
import ai.creatly.sky.creation.domain.core.voice.model.enums.VoiceProviderType;
import com.jspeeder.core.data.mapper.BaseMapperConfig;
import com.jspeeder.core.util.Validates;
import com.jspeeder.core.util.json.JSON;
import lombok.Setter;
import org.jetbrains.annotations.Nullable;
import org.json.JSONObject;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version VoiceAdminMapper.java, v 0.1 2023-11-19 上午12:40 zhoudong
 */
@Mapper(config = BaseMapperConfig.class, uses = VoiceMapper.class)
public abstract class VoiceAdminMapper {

    @Setter(onMethod_ = @Autowired)
    private UserFileHelper userFileHelper;

    /*------------------------------------ 请求 ------------------------------------*/

    public Set<Long> getFileIds(List<VoiceLocaleDTO> locales) {
        if (locales == null) {
            return Collections.emptySet();
        }
        Set<Long> fileIds = new HashSet<>();
        for (VoiceLocaleDTO locale : locales) {
            if (locale.getPreviewAudioId() != null) {
                fileIds.add(Long.parseLong(locale.getPreviewAudioId()));
            }
            if (locale.getRoles() != null) {
                for (VoiceRoleDTO role : locale.getRoles()) {
                    if (role.getPreviewAudioId() != null) {
                        fileIds.add(Long.parseLong(role.getPreviewAudioId()));
                    }
                }
            }
            if (locale.getEmotions() != null) {
                for (VoiceEmotionDTO emotion : locale.getEmotions()) {
                    if (emotion.getPreviewAudioId() != null) {
                        fileIds.add(Long.parseLong(emotion.getPreviewAudioId()));
                    }
                }
            }
        }
        return fileIds;
    }

    public List<VoiceLocale> toVoiceLocales(Voice voice, List<VoiceLocaleDTO> locales, Map<Long, UserFile> fileMap) {
        if (locales == null) {
            return new ArrayList<>();
        }
        return IntStream.range(0, locales.size()).mapToObj(i -> {
            VoiceLocaleDTO locale = locales.get(i);
            return this.toVoiceLocale(voice, locale, fileMap, i);
        }).collect(toList());
    }

    private VoiceLocale toVoiceLocale(Voice voice, VoiceLocaleDTO locale, Map<Long, UserFile> fileMap, int ordinal) {
        String localePreviewAudioUrl = null;
        if (locale.getPreviewAudioId() != null) {
            UserFile localePreviewAudio = fileMap.get(Long.parseLong(locale.getPreviewAudioId()));
            Validates.notNull(localePreviewAudio, "本地化口音的预览音频文件不存在");
            localePreviewAudioUrl = UserFileHelper.toOssUrl(localePreviewAudio);
        }

        List<VoiceRole> roles = locale.getRoles().stream().map(role -> {
            String previewAudioUrl = null;
            if (role.getPreviewAudioId() != null) {
                UserFile previewAudio = fileMap.get(Long.parseLong(role.getPreviewAudioId()));
                Validates.notNull(previewAudio, "角色[{}]的预览音频文件不存在", role.getCode());
                previewAudioUrl = UserFileHelper.toOssUrl(previewAudio);
            }
            return this.toVoiceRole(role, previewAudioUrl);
        }).collect(toList());

        List<VoiceEmotion> emotions = locale.getEmotions().stream().map(emotion -> {
            String previewAudioUrl = null;
            if (emotion.getPreviewAudioId() != null) {
                UserFile previewAudio = fileMap.get(Long.parseLong(emotion.getPreviewAudioId()));
                Validates.notNull(previewAudio, "情绪[{}]的预览音频文件不存在", emotion.getCode());
                previewAudioUrl = UserFileHelper.toOssUrl(previewAudio);
            }
            return this.toVoiceEmotion(emotion, previewAudioUrl);
        }).collect(toList());

        final JSONObject extInfo;
        if (voice.getProviderType() == VoiceProviderType.self) {
            Validates.notBlank(locale.getMsVoiceCode(), "自营声音的口音必须关联基础声音");
            SelfVoiceLocalExtInfo selfVoiceLocalExtInfo = new SelfVoiceLocalExtInfo();
            selfVoiceLocalExtInfo.setMsVoiceCode(locale.getMsVoiceCode());
            // 基础声音
            SynthesisVoiceNameEnum voiceNameEnum = SynthesisVoiceNameEnum.getByName(locale.getMsVoiceCode());
            Validates.notNull(voiceNameEnum, VoiceErrorCode.VOICE_LOCALE_MS_VOICE_NOT_FOUND);
            selfVoiceLocalExtInfo.setMsVoiceCnName(voiceNameEnum.getDisplayName());
            extInfo = JSON.toJSONObject(selfVoiceLocalExtInfo);
        } else {
            extInfo = new JSONObject();
        }

        return this.toVoiceLocale(voice.getId(), voice.getCode(), voice.getUid(), locale, localePreviewAudioUrl, extInfo, ordinal)
                .setRoles(roles)
                .setEmotions(emotions);
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "emotions", ignore = true)
    abstract VoiceLocale toVoiceLocale(long voiceId, String voiceCode, long uid, VoiceLocaleDTO locale, @Nullable String previewAudioUrl,
                                       JSONObject extInfo, int ordinal);

    abstract VoiceRole toVoiceRole(VoiceRoleDTO voiceRoleDTO, @Nullable String previewAudioUrl);

    abstract VoiceEmotion toVoiceEmotion(VoiceEmotionDTO voiceEmotionDTO, @Nullable String previewAudioUrl);

    public abstract VoiceQO toVoiceQO(VoiceQueryRequest request);

    /*------------------------------------ 响应 ------------------------------------*/

    @Mapping(target = "locales", source = "voice")
    public abstract VoiceVM toVoiceVM(Voice voice);

    protected List<VoiceLocaleVM> toVoiceLocaleVMs(Voice voice) {
        return voice.getLocales()
                .stream()
                .map(voiceLocale -> this.toVoiceLocaleVM(voice.getProviderType(), voiceLocale))
                .collect(toList());
    }

    private VoiceLocaleVM toVoiceLocaleVM(VoiceProviderType providerType, VoiceLocale locale) {
        String previewAudioUrl = userFileHelper.getHttpUrl(locale.getPreviewAudioUrl(), FileAcl.PUBLIC);
        if (providerType == VoiceProviderType.self) {
            SelfVoiceLocalExtInfo selfVoiceLocalExtInfo = locale.parseExtInfo(SelfVoiceLocalExtInfo.class);
            return this.toVoiceLocaleVM(locale, previewAudioUrl, selfVoiceLocalExtInfo);
        }
        return this.toVoiceLocaleVM(locale, previewAudioUrl, null);
    }

    @Mapping(target = "previewAudioUrl", source = "previewAudioUrl")
    abstract VoiceLocaleVM toVoiceLocaleVM(VoiceLocale locale, String previewAudioUrl, SelfVoiceLocalExtInfo extInfo);
}
