/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 定价计划周期类型
 *
 * <AUTHOR>
 * @version PlanPeriodType.java, v 0.1 2023-10-11 下午9:01 zhoudong
 */
@Getter
@RequiredArgsConstructor
public enum PlanPeriodType {

    MONTHLY("月付"),
    QUARTER("季付"),
    SEMI_ANNUAL("半年付"),
    ANNUALLY("年付"),
    LIFETIME("终身买断"),
    PAY_AS_YOU_GO("按量付费"),
    ;

    private final String desc;
}
