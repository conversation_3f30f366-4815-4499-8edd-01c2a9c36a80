/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.task.generate;

import ai.creatly.sky.creation.domain.core.ai.image.client.async.AiImageAsyncClient;
import ai.creatly.sky.creation.domain.core.ai.image.mapper.ImageGenerationMapper;
import ai.creatly.sky.creation.domain.core.ai.image.task.AiImageTaskBizType;
import ai.creatly.sky.creation.domain.core.ai.image.task.common.AbstractAiImageBatchAsyncTaskHandler;
import ai.creatly.sky.creation.domain.core.ai.image.task.common.model.AiImageAsyncTaskVars;
import ai.creatly.sky.creation.domain.core.ai.image.task.generate.model.ImageGenerateTaskInput;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.AiTaskHandler;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.AiTaskConfig;
import ai.creatly.sky.creation.domain.core.aitask.engine.handler.model.TaskQueueMode;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.asset.model.Asset;
import ai.creatly.sky.creation.domain.core.auth.model.UserContext;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version ImageExpandBatchAsyncTaskHandler.java, v0.1 2025-02-28 13:41
 */
//@Component
@RequiredArgsConstructor
public class ImageGenerateBatchAsyncTaskHandler extends AbstractAiImageBatchAsyncTaskHandler implements AiTaskHandler {

    private final AiImageAsyncClient    aiImageAsyncClient;
    private final ImageGenerationMapper imageGenerationMapper;

    @Override
    public AiTaskConfig config() {
        return AiTaskConfig.of(AiTaskType.AI_IMAGE)
                .setBizType(AiImageTaskBizType.batch_image_generate_async)
                .setLoadSize(1)
                .setQueueMode(TaskQueueMode.POP_PER_EXECUTED)
                .setNotifyOnSubmit(false)
                .setNotifyUserOnCompleted(false);
    }

    @Override
    protected String submitGenerationTask(AiTask aiTask) {
        var input = aiTask.parseBizInput(ImageGenerateTaskInput.class);
        return aiImageAsyncClient.batchGenerate(input, aiTask.getBizNo());
    }

    @Override
    protected List<UserFile> queryGeneratedImages(AiTask aiTask, UserContext userContext) {
        var input = aiTask.parseBizInput(ImageGenerateTaskInput.class);
        var vars = aiTask.parseBizVars(AiImageAsyncTaskVars.class);
        return aiImageAsyncClient.queryImageGenerations(vars.getRemoteTaskId(), input, userContext);
    }

    @Override
    protected List<Asset> convertToAssets(AiTask aiTask, List<UserFile> imageFiles) {
        var input = aiTask.parseBizInput(ImageGenerateTaskInput.class);
        return imageFiles.stream().map(imageFile -> {
            String coverUrl = imageFileService.generateThumbnailUrl(imageFile, 50);
            return imageGenerationMapper.buildAsset(input, imageFile, coverUrl);
        }).toList();
    }
}
