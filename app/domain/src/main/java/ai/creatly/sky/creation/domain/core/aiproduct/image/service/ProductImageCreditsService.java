/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.image.service;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsRefund;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsService;

/**
 * AI商品图计费服务（收入、支出、退款）
 *
 * <AUTHOR>
 * @version AiProductCreditsService.java, v 0.1 2024-03-02 上午11:51 zhoudong
 */
public interface ProductImageCreditsService extends CreditsService {

    /**
     * 计算生成AI商品图的费用
     *
     * @param aiTask 生成任务
     * @return 余额支出请求
     */
    CreditsExpense calcImagineExpense(AiTask aiTask);

    /**
     * 构建生成AI商品图任务的余额退还请求
     *
     * @param aiTask 生成任务
     * @return 余额退还请求
     */
    CreditsRefund buildImagineRefund(AiTask aiTask);
}
