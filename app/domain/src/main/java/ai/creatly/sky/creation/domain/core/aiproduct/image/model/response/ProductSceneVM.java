/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.image.model.response;

import ai.creatly.sky.creation.domain.support.category.model.CategoryRef;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version ProductSceneVM.java, v 0.1 2024-02-27 下午8:38 zhoudong
 */
@Data
public class ProductSceneVM {

    /**
     * 所属商品类目（支持多级类目）
     */
    private List<CategoryRef> productCategories;
    /**
     * 商品场景id
     */
    private String            id;
    /**
     * 商品场景名称
     */
    private String            name;
    /**
     * 场景示例图URL（HTTP链接）
     */
    private String            sampleImageUrl;
}
