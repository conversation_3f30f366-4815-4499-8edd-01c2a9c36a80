/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.videoproject.model;

import ai.creatly.sky.creation.domain.core.videoproject.model.enums.AudioTrackType;
import ai.creatly.sky.creation.domain.core.videoproject.model.task.vars.DramaAdsComposeTaskVars;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @version VideoProjectConstants.java, v 0.1 2024-09-28 下午3:03 zhoudong
 */
public interface VideoProjectConstants {

    String VIDEO_COMPOSED = "video_composed";

    /**
     * 视频素材最小总时长
     */
    int VIDEO_ASSETS_MIN_TOTAL_SECONDS         = 8;
    /**
     * 视频生成耗时倍率（基于总时长呈线性增长）
     */
    int VIDEO_RENDER_TIME_RATE                 = 7;
    /**
     * 视频生成耗时倍率（基于总时长呈线性增长）
     */
    int GENERATION_TIME_RATE                   = 7;
    /**
     * 视频分镜语音合成停顿间隔
     */
    int VIDEO_SHOT_VOICE_SYNTHESIS_BREAK_MILLS = 10;
    /**
     * 不管横屏还是竖屏，字幕宽度固定为800px
     */
    int SUBTITLE_WIDTH                         = 800;

    /**
     * 视频字幕层默认配置（TODO 配置化）
     */
    Function<Integer, VideoSubtitleLayer> DEFAULT_SUBTITLE_LAYER = height -> new VideoSubtitleLayer()
            .setFontFamily("Douyin Sans")
            .setFontSize(80)
            .setColor("#e9ff4f")
            .setStrokeColor("#000000")
            .setStrokeWidth(6)
            .setTop(height * 3 / 4)
            .setAlign("center")
            .setEffect("word-by-word-coloring");

    Function<DramaAdsComposeTaskVars, AudioTrack> BACKGROUND_MUSIC = bizVars -> new AudioTrack()
            .setType(AudioTrackType.bg_music)
            .setUrl("oss://creatly-dev/creation/users/110757051658930176/video-project/drama-ads-audio/176733524427038720.mp3")
            .setStart(bizVars.getAdsAudioTrack().getStart())
            .setEnd(bizVars.getAdsAudioTrack().getEnd());
}
