/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aittalk.model.task.result;

import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizResult;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Duration;
import java.util.Map;

/**
 * <AUTHOR>
 * @version AiTalkVideoResult.java, v 0.1 2023-07-28 20:43 joton
 */
@Data
@Accessors(chain = true)
public class AiTalkVideoResult implements TalkBizExecInfo, TaskBizResult {

    /**
     * 请求次数
     */
    private Integer             requestCount;
    /**
     * 最新一次请求的目标主机
     */
    private String              requestHost;
    /**
     * 生成的视频文件ID
     */
    private Long                resultFileId;
    /**
     * 生成的视频文件地址（OSS格式）
     */
    private String              resultUrl;
    /**
     * 生成的音频文件ID
     */
    private Long                audioId;
    /**
     * 生成的音频文件地址（OSS格式）
     */
    private String              audioUrl;
    private Map<String, Object> metadata;
    private String              createdBy;
    /**
     * 合成视频的主机
     */
    private String              hostname;
    private String              failReason;
    /**
     * 合成耗时
     * <p/>
     * 当从 json 字符串反序列化时：
     * 1. 该字段如果是整数，会按照ms来解析；
     * 2. 如果是小数，整数部分按照s来解析，小数部分按照ns来解析；
     * 3. 如果是字符串，会按照 ISO-8601 标准来解析，不符合格式会解析失败。
     */
    private Duration            duration;

    public AiTalkVideoResult incrRequestCount() {
        if (requestCount == null) {
            requestCount = 0;
        }
        requestCount = requestCount + 1;
        return this;
    }

    @JsonIgnore
    @Override
    public String getProcessHost() {
        return hostname;
    }
}
