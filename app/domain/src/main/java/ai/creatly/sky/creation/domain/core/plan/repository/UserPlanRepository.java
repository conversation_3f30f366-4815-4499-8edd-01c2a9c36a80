/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.repository;

import ai.creatly.sky.creation.domain.core.plan.model.UserPlan;
import ai.creatly.sky.creation.domain.core.plan.model.UserPlanQO;
import ai.creatly.sky.creation.domain.core.plan.model.enums.PlanType;
import com.jspeeder.core.data.repository.SingleRepository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version UserPlanRepository.java, v 0.1 2023-12-21 下午3:50 zhoudong
 */
public interface UserPlanRepository extends SingleRepository<UserPlan, UserPlanQO> {

    /**
     * 创建私人定价计划
     *
     * @param userPlan 私人定价计划
     * @return 主键ID
     */
    @Override
    long create(UserPlan userPlan);

    /**
     * 创建私人定价计划
     *
     * @param userPlan 私人定价计划
     */
    @Override
    void updateById(UserPlan userPlan);

    /**
     * 查询私人定价计划
     *
     * @param id 计划ID
     * @return 私人定价计划
     */
    @Override
    Optional<UserPlan> queryOptionalById(long id);

    /**
     * 根据 UK 查询私人定价计划
     *
     * @param platformPlanId 平台定价计划ID
     * @param uid            用户ID
     * @return 私人定价计划列表
     */
    Optional<UserPlan> queryByPlatformPlanIdAndUid(String platformPlanId, long uid);

    /**
     * 查询私人定价计划列表
     *
     * @param type 计划类型
     * @return 私人定价计划列表
     */
    List<UserPlan> queryByTypeAndUid(PlanType type, long uid);
}
