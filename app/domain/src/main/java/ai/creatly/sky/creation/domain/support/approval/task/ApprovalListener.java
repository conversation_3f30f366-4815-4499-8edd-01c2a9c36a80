/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.approval.task;

import ai.creatly.sky.creation.domain.support.approval.model.ApprovalBizType;
import ai.creatly.sky.creation.domain.support.approval.model.ApprovalTask;

/**
 * <AUTHOR>
 * @version ApprovalListener.java, 2024-10-29 下午3:06 zhoudong
 */
public interface ApprovalListener {

    boolean support(ApprovalBizType bizType);

    void onApproved(ApprovalTask task);

    void onRejected(ApprovalTask task);

    void onCompleted(ApprovalTask task);
}
