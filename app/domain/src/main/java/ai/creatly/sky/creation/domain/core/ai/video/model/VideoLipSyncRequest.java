/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-${YEAR} All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.video.model;

import ai.creatly.sky.creation.domain.core.userfile.model.FileRef;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version VideoLipSyncRequest.java, v0.1 2025-02-20 21:36
 */
@Data
public class VideoLipSyncRequest {

    /**
     * 基础模型
     */
    private String model;

    /**
     * 参考视频
     */
    private FileRef videoFile;

    /**
     * 参考音频
     */
    private FileRef audioFile;

    /**
     * 生成视频时长
     */
    private Integer duration;

    private String audioType = "url";

    private Float voiceSpeed;

    private String voiceLanguage;

    private String voiceId;

    private String voiceText;

    /**
     * ● 枚举值：text2video，audio2video
     */
    private String voiceMode;

}
