/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.credit.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户余额变动
 *
 * <AUTHOR>
 * @version CreditDelta.java, v 0.1 2023-12-27 下午11:45 zhoudong
 */
@Data
@Accessors(chain = true)
public class CreditDelta {
    /**
     * 用户余额账户ID
     */
    private Long    creditId;
    /**
     * 余额变动数量（正数为收入，负数为支出）
     */
    private Integer amount;
    /**
     * 变动后的余额（在支出的情况下，可能为负数）
     */
    private Integer balance;
}
