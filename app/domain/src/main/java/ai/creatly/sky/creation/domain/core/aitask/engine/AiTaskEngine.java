/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aitask.engine;

import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import org.jetbrains.annotations.NotNull;

/**
 * 任务引擎
 *
 * <AUTHOR>
 * @version AiTaskEngine.java, v 0.1 2023-08-06 01:36 joton
 */
public interface AiTaskEngine {

    /**
     * AI创作任务执行
     *
     * @param aiTask AI创作任务
     */
    void execute(@NotNull AiTask aiTask);
}
