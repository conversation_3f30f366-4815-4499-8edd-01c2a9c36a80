/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.integration.capcut.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version DownStoryForJYRequest.java, v 0.1 2024-04-16 下午23:24 heb
 */
@Data
@Accessors(chain = true)
public class DownStoryForJYRequest {

    private String              storyId;
    private String              downOssUrl;
    private List<DownStoryShot> shotList;
}

