/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.model;

import ai.creatly.sky.creation.domain.core.userfile.model.BizFile;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;

/**
 * 数字人视频
 *
 * <AUTHOR>
 * @version DigitalHumanVideo.java, v 0.1 2024-05-25 10:26 syoka
 */
@Deprecated
@Data
@Accessors(chain = true)
public class DigitalHumanVideo {

    public static final String FULL_SIZE  = "full";
    public static final String SMALL_SIZE = "small";

    public static final String SUCCESS    = "success";
    public static final String FAILURE    = "failure";
    public static final String WAITING    = "waiting";
    public static final String GENERATING = "generating";
    public static final String DELETE     = "delete";

    /**
     * 视频id
     */
    private Long          id;
    /**
     * 创建时间
     */
    private ZonedDateTime createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime updatedAt;
    /**
     * 视频外部id(理论唯一)
     */
    @Nullable
    private String        extOutId;
    /**
     * 用户id
     */
    private Long          uid;
    /**
     * 数字人形象id（对内）
     */
    private String        avatarId;
    /**
     * 数字人形象id（对外｜冗余）
     */
    private String        extOutAvatarId;
    /**
     * 绿幕视频文件
     */
    @Nullable
    private BizFile       greenVideoFile;
    /**
     * 视频MASK文件,用于替换背景
     */
    @Nullable
    private BizFile       maskVideoFile;
    /**
     * 视频背景文件（可空）
     */
    @Nullable
    private BizFile       bgFile;
    /**
     * 视频音频文件
     */
    private BizFile       audioFile;
    /**
     * 视频状态
     */
    private String        status;
    /**
     * 视频名称
     */
    private String        name;
    /**
     * 视频大小 full｜small
     */
    private String        size;
    /**
     * 最终结果视频
     */
    @Nullable
    private BizFile       videoFile;
    /**
     * 透明通道视频（中间态数据不会持久化到UserFile和OSS）
     */
    @Nullable
    private BizFile       alphaVideoFile;

    @JsonIgnore
    public boolean isFinished() {
        return StringUtils.containsAny(this.status, SUCCESS, FAILURE);
    }
}
