/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jetbrains.annotations.NotNull;

/**
 *
 * <AUTHOR>
 * @version OrderedAssetRef.java, v0.1 2025-02-28 17:55
 */
@Data
@EqualsAndHashCode(callSuper = true, of = {})
public class OrderedAssetRef extends AssetRef implements Comparable<OrderedAssetRef> {

    private Integer order;

    @Override
    public int compareTo(@NotNull OrderedAssetRef o) {
        return this.order.compareTo(o.order);
    }
}
