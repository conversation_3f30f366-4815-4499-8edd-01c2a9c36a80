/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.ai.image.task.repaint.model;

import ai.creatly.sky.creation.domain.core.ai.image.task.common.model.AiImageTaskResult;
import ai.creatly.sky.creation.domain.core.aitask.model.TaskBizResult;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version ImageRepaintTaskResult.java, v0.1 2025-02-24 15:53
 */
@Data
public class ImageRepaintTaskResult extends AiImageTaskResult implements TaskBizResult {

}
