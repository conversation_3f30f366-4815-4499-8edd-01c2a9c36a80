/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.scene.response;

import ai.creatly.sky.creation.domain.core.story.model.scene.SceneStatus;
import ai.creatly.sky.creation.domain.core.story.model.story.StoryStyle;
import com.jspeeder.core.model.OperatorRef;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * 故事场景
 *
 * <AUTHOR>
 * @version StorySceneVM.java, v 0.1 2024-03-09 13:39 heb
 */

@Data
public class StorySceneVM {
    /**
     * 场景ID
     */
    private String        id;
    /**
     * 创建时间
     */
    private ZonedDateTime createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime updatedAt;
    /**
     * 故事ID
     */
    private String        storyId;
    /**
     * 场景名称
     */
    private String        name;
    /**
     * 场景排序
     */
    private Integer       index;
    /**
     * 场景标题
     */
    private String        title;
    /**
     * 场景配置
     */
    private StoryStyle    config;
    /**
     * 创建者
     */
    private OperatorRef   creator;
    /**
     * 场景状态
     */
    private SceneStatus   status;
}
