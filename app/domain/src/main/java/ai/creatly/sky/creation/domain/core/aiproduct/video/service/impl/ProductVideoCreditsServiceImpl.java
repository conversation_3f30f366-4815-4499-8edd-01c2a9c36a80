/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.aiproduct.video.service.impl;

import ai.creatly.sky.creation.domain.core.aiproduct.video.model.task.ProductVideoTaskBizType;
import ai.creatly.sky.creation.domain.core.aiproduct.video.service.ProductVideoCreditsService;
import ai.creatly.sky.creation.domain.core.aitask.model.AiTask;
import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditAccountType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditLogBizType;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsExpense;
import ai.creatly.sky.creation.domain.core.credit.model.CreditsRefund;
import ai.creatly.sky.creation.domain.core.credit.service.CreditsCalculator;
import ai.creatly.sky.creation.domain.core.userfile.model.UserFile;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version ProductVideoCreditsServiceImpl.java, v 0.1 2024-05-28 上午12:10 zhoudong
 */
@Service
@RequiredArgsConstructor
public class ProductVideoCreditsServiceImpl implements ProductVideoCreditsService {

    private final CreditsCalculator creditsCalculator;

    @Override
    public CreditsExpense calcReTalkExpense(AiTask aiTask, UserFile inputVideoFile) {
        String ruleType = AiTaskType.AI_PRODUCT_VIDEO.name();
        String ruleBizType = ProductVideoTaskBizType.re_talk.name();
        int credits = creditsCalculator.calcVideoCredits(ruleType, ruleBizType, inputVideoFile);
        return CreditsExpense.builder()
                .uid(aiTask.getOwnerId())
                .type(CreditAccountType.GENERAL)
                .amount(credits)
                // 生成一次，就扣除一次费用
                .bizType(CreditLogBizType.PRODUCT_VIDEO_RE_TALK)
                .bizNo(aiTask.getId().toString())
                .build();
    }

    @Override
    public CreditsRefund buildRefund(AiTask aiTask, CreditLogBizType bizType) {
        return CreditsRefund.builder()
                .uid(aiTask.getOwnerId())
                .bizType(bizType)
                .bizNo(aiTask.getId().toString())
                // 在退款时，扣费记录一定存在，所以不需要静默处理
                .allowExpenseAbsent(false)
                .build();
    }
}
