/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.creatly.sky.creation.domain.deprecated.aiproduct.model;

import ai.creatly.sky.creation.domain.core.aitask.model.enums.AiTaskStatus;
import ai.creatly.sky.creation.domain.core.userfile.model.BizFile;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 商品成品图
 *
 * <AUTHOR>
 * @version FinishedProductImage.java, v 0.1 2023-11-15 18:31 syoka
 */
@Data
@Accessors(chain = true)
public class FinishedProductImage {

    /**
     * 成品图任务id
     */
    private Long taskId;
    /**
     * 创建时间
     */
    private ZonedDateTime createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime updatedAt;

    /**
     * 任务状态
     */
    private AiTaskStatus status;
    /**
     * 业务状态
     */
    private String bizStatus;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 商品图
     */
    private BizFile productImage;

    /**
     * 场景图
     */
    private BizFile sceneImage;

    /**
     * 场景图下标
     */
    private Integer sceneImageIdx;

    /**
     * 场景初始快照
     */
    private BizFile primarySceneSnapshot;

    /**
     * 场景快照
     */
    private BizFile sceneSnapshot;

    /**
     * 成品图
     */
    private BizFile finishedProductImageFile;
}
