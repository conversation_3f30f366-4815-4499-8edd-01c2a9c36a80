/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.userfile.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 带有访问地址的文件对象
 *
 * <AUTHOR>
 * @version HttpFile.java, v 0.1 2023-12-26 下午5:34 zhoudong
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HttpFile extends UserFile {

    /**
     * OSS 链接地址
     */
    private String ossUrl;
    /**
     * HTTP 链接地址
     */
    private String httpUrl;
}
