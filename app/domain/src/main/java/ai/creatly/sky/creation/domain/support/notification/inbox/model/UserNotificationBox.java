/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.notification.inbox.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户站内消息盒
 *
 * <AUTHOR>
 * @version UserNotificationBox.java, v 0.1 2024-07-13 15:21 syoka
 */
@Data
@Accessors(chain = true)
public class UserNotificationBox {

    /**
     * 用户当前未读的消息通知
     */
    private Long unreadNotificationCount;

    /**
     * 用户是否有未读通知。
     */
    private boolean hasUnreadNotifications;
}
