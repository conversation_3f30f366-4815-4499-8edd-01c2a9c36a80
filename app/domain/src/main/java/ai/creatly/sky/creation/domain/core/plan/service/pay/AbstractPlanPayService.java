/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.plan.service.pay;

import ai.creatly.kylin.trade.sdk.order.client.TradeOrderClient;
import ai.creatly.kylin.trade.sdk.order.client.TradePaymentClient;
import ai.creatly.kylin.trade.sdk.order.enums.OrderStatus;
import ai.creatly.kylin.trade.sdk.order.request.OrderCreateAndWxNativePrepayRequest;
import ai.creatly.kylin.trade.sdk.order.request.OrderCreateRequest;
import ai.creatly.kylin.trade.sdk.order.request.OrderQueryParam;
import ai.creatly.kylin.trade.sdk.order.response.OrderEntryVO;
import ai.creatly.kylin.trade.sdk.payment.enums.PaymentStatus;
import ai.creatly.kylin.trade.sdk.payment.enums.PaymentType;
import ai.creatly.kylin.trade.sdk.payment.response.PaymentVO;
import ai.creatly.kylin.trade.sdk.payment.wxpay.response.PaymentWxPayVO;
import ai.creatly.kylin.trade.sdk.payment.wxpay.response.WxNativePrepayVO;
import ai.creatly.sky.creation.domain.common.util.AppConstants;
import ai.creatly.sky.creation.domain.core.plan.error.PlanErrorCode;
import ai.creatly.sky.creation.domain.core.plan.mapper.PlanOrderMapper;
import ai.creatly.sky.creation.domain.core.plan.model.Plan;
import ai.creatly.sky.creation.domain.core.plan.model.specific.payment.PlanWxNativePrepayResult;
import ai.creatly.sky.creation.domain.support.notification.app.AppAlertHelper;
import ai.creatly.sky.creation.domain.support.trade.TradeConstants;
import com.jspeeder.core.data.page.Page;
import com.jspeeder.core.data.page.PageBuilder;
import com.jspeeder.core.data.page.Pageable;
import com.jspeeder.core.data.problem.exception.SysException;
import com.jspeeder.core.util.Validates;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version AbstractPlanPayService.java, v 0.1 2023-10-28 下午10:28 zhoudong
 */
@Slf4j
public abstract class AbstractPlanPayService implements PlanPayService {

    /**
     * 收银台有效时间：5分钟
     */
    protected final static int PLAN_ORDER_VALID_MINUTES = 5;

    @Autowired
    protected TradeOrderClient   tradeOrderClient;
    @Autowired
    protected AppAlertHelper     appAlertHelper;
    @Autowired
    protected PlanOrderMapper    planOrderMapper;
    @Autowired
    protected TradePaymentClient tradePaymentClient;

    /**
     * 将订单推进到发货
     *
     * @param orderId 订单ID
     */
    protected void shipOrder(Long orderId) {
        boolean success = tradeOrderClient.shipOrder(TradeConstants.TENANT_CODE, orderId);
        if (!success) {
            log.error("[shipOnPaid][shipOrder fail]orderId={}", orderId);
            appAlertHelper.alertText("[shipOnPaid][shipOrder fail]orderId={}", orderId);
            throw new SysException(PlanErrorCode.PLAN_ORDER_SHIP_FAIL);
        }
    }

    protected OrderQueryParam buildIdempotentCheckOrderQueryParam(long uid, String bizType, @Nullable String bizSubType) {
        OrderQueryParam orderQueryParam = new OrderQueryParam();
        orderQueryParam.setTenantCode(TradeConstants.TENANT_CODE);
        orderQueryParam.setBizTypes(List.of(bizType));
        orderQueryParam.setStatuses(List.of(OrderStatus.CREATED));
        orderQueryParam.setBuyerId(String.valueOf(uid));
        orderQueryParam.setSellerId(AppConstants.SYSTEM_UID.toString());
        if (bizSubType != null) {
            orderQueryParam.setBizSubTypes(List.of(bizSubType));
        }
        return orderQueryParam;
    }

    /**
     * 下单并支付是否幂等
     *
     * @param orderQueryParam 待支付的订单的查询条件
     * @param plan 本次购买的付费计划
     *
     * @return 幂等返回的结果
     */
    @Nullable
    protected PlanWxNativePrepayResult checkOrderAndPayIdempotent(OrderQueryParam orderQueryParam, Plan plan) {
        Pageable pageable = PageBuilder.firstPage().size(1).build();
        Page<OrderEntryVO> page = tradeOrderClient.queryOrderPage(orderQueryParam, pageable);
        if (page.getTotal() <= 0) {
            return null;
        }
        OrderEntryVO orderEntryVO = page.getContent().getFirst();
        String planId = orderEntryVO.getBizSubType();
        if (StringUtils.isBlank(planId)) {
            // planId为空的数据是历史数据，直接忽略，等交易中心超时关单好了。本次就重新下单
            return null;
        }

        // 支付单
        PaymentVO paymentVO = orderEntryVO.getPayments()
                .stream()
                .filter(payment -> payment.getStatus() == PaymentStatus.PAYING)
                .filter(payment -> payment.getType() == PaymentType.WX_PAY_NATIVE)
                .findFirst()
                .orElse(null);
        if (paymentVO == null) {
            // 理论上不可能存在，如果有这种情况，那就是脏数据了，直接忽略，本次重新下单
            log.warn("[checkOrderAndPayIdempotent][WX_PAY_NATIVE]payment is null,orderId={}", orderEntryVO.getId());
            return null;
        }

        // 本单支付的收银台过期时间
        ZonedDateTime expireAt = Instant.ofEpochMilli(paymentVO.getCreatedAt())
                .atZone(ZoneId.systemDefault())
                .plusMinutes(PLAN_ORDER_VALID_MINUTES);
        String tenantCode = TradeConstants.TENANT_CODE;
        if (planId.equals(plan.getId()) && expireAt.isAfter(ZonedDateTime.now())) {
            // 是本订阅计划且支付未超时（5分钟），则幂等返回
            PaymentWxPayVO paymentWxPayVO = tradePaymentClient.fetchPaymentWxPay(tenantCode, paymentVO.getId());
            return new PlanWxNativePrepayResult()
                    .setOrderId(paymentWxPayVO.getOrderId())
                    .setExpireAt(expireAt)
                    .setCodeUrl(paymentWxPayVO.getCodeUrl());
        }

        // 否则关单，再重新下单并支付
        boolean success = tradeOrderClient.cancelOrder(tenantCode, orderEntryVO.getId());
        Validates.isTrue(success, PlanErrorCode.PLAN_ORDER_FAIL);
        return null;
    }

    protected PlanWxNativePrepayResult doWxNativePayAfterCheck(OrderCreateRequest order, String payerClientIp) {
        // 下单并支付
        OrderCreateAndWxNativePrepayRequest request = new OrderCreateAndWxNativePrepayRequest();
        request.setOrder(order);
        request.setPayerClientIp(payerClientIp);
        WxNativePrepayVO wxNativePrepayVO = tradeOrderClient.createOrderAndPay(request);

        // 返回微信预支付结果
        ZonedDateTime expireAt = Instant.ofEpochMilli(wxNativePrepayVO.getOrderCreatedAt())
                .atZone(ZoneId.systemDefault())
                .plusMinutes(PLAN_ORDER_VALID_MINUTES);
        return new PlanWxNativePrepayResult()
                .setOrderId(wxNativePrepayVO.getOrderId())
                .setExpireAt(expireAt)
                .setCodeUrl(wxNativePrepayVO.getCodeUrl());
    }
}
