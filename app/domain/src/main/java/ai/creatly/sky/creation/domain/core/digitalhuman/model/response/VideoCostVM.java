/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.digitalhuman.model.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 数字人视频扣费预计算
 *
 * <AUTHOR>
 * @version VideoCostVM.java, v 0.1 2024-05-25 19:14 syoka
 */
@Data
@Accessors(chain = true)
public class VideoCostVM {

    /**
     * 时长(单位: 秒，带小数)
     */
    private String  duration;
    /**
     * 预计消费元气
     */
    private Integer credits;
}
