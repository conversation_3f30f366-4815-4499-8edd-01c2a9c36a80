/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.story.model.role.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class StoryRoleInitTaskVM {
    /**
     * 角色列表
     */
    private List<StoryRoleVM> roles;
    /**
     * 任务ID
     */
    private String            taskId;
    /**
     * 是否已初始化过
     */
    private Boolean           initialized;
    /**
     * 任务状态
     * @see ai.creatly.sky.creation.domain.core.aitask.model.AiTaskConstant
     */
    private String            taskStatus;
}
