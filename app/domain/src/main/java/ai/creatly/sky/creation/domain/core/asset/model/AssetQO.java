/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.asset.model;

import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetBizType;
import ai.creatly.sky.creation.domain.core.asset.model.enums.AssetSourceType;
import ai.creatly.sky.creation.domain.support.file.model.FileType;
import com.jspeeder.core.model.BizStatus;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 作品查询
 *
 * <AUTHOR>
 * @version AssetQO.java, v0.1 2025-02-22 15:09
 */
@Data
@Accessors(chain = true)
public class AssetQO {

    /**
     * 用户ID
     */
    private Long            uid;
    /**
     * 工作空间
     */
    private String          orgCode;
    /**
     * 状态（无效即为软删除）
     */
    private BizStatus       status;
    /**
     * 来源类型
     */
    private AssetSourceType sourceType;
    /**
     * 业务类型
     */
    private AssetBizType    bizType;
    /**
     * 文件类型
     */
    private FileType        fileType;
}
