package ai.creatly.sky.creation.domain.core.aittalk.model;

import ai.creatly.sky.creation.domain.core.aittalk.model.enums.AiTalkEmotionStatus;
import com.jspeeder.core.model.OperatorRef;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Nullable;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @version AiTalkEmotion.java, v 0.1 2023-07-29 14:55 joton
 */
@Data
@Accessors(chain = true)
public class AiTalkEmotion {

    /**
     * 主键
     */
    private Long                id;
    /**
     * 创建时间
     */
    private ZonedDateTime       createdAt;
    /**
     * 更新时间
     */
    private ZonedDateTime       updatedAt;
    /**
     * 所属用户ID（1表示平台）
     */
    private Long                uid;
    /**
     * 编号
     */
    private String              code;
    /**
     * 名称
     */
    private String              name;
    /**
     * 表情视频文件ID
     */
    private Long                videoId;
    /**
     * 表情视频地址（冗余OSS地址）
     */
    private String              videoUrl;
    /**
     * 表情视频模板文件ID
     */
    @Nullable
    private Long                templateFileId;
    /**
     * 表情视频模板地址（冗余OSS地址）
     */
    @Nullable
    private String              templateUrl;
    /**
     * 是否为系统内置
     */
    private Boolean             embedded;
    /**
     * 状态
     */
    private AiTalkEmotionStatus status;
    /**
     * 创建者
     */
    private OperatorRef         creator;
}
