/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.drama.model.enums;

import com.jspeeder.core.data.enums.ICode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version DramaType.java, v 0.1 2024-10-18 下午8:55 zhoudong
 */
@RequiredArgsConstructor
@Getter
public enum DramaType implements ICode {

    emotional("情感"),
    social_reality("社会现实"),
    suspense("悬疑"),
    thriller("惊悚"),
    science_fiction("科幻"),
    fantasy("奇幻"),
    historical_costume("历史/古装"),
    crime("犯罪"),
    comedy("喜剧"),
    documentary("纪录片"),
    war("战争"),
    romance("爱情"),
    ;

    private final String desc;

    @Override
    public String getCode() {
        return this.name();
    }
}
