/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.common.messaging.support.impl;

import ai.creatly.sky.creation.domain.common.messaging.Message;
import com.jspeeder.core.util.Asserts;
import lombok.Getter;
import org.springframework.messaging.MessageHeaders;

import java.util.Map;

/**
 * MQ消息
 *
 * <AUTHOR>
 * @version MQMessage.java, v 0.1 2023-10-27 上午11:40 zhoudong
 */
@Getter
public class MQMessage<T> implements Message<T> {

    /**
     * 消息内容在业务层面的唯一标识码（尽可能保证全局唯一，但即使重复也不会影响功能，只是给消息打个标方便事后查询）
     */
    private final String messageKey;
    private final T payload;
    private final MessageHeaders headers;

    /**
     * Create a new message with the given payload.
     *
     * @param payload the message payload (never {@code null})
     */
    public MQMessage(String messageKey, T payload) {
        this(messageKey, payload, new MessageHeaders(null));
    }

    /**
     * Create a new message with the given payload and headers.
     * The content of the given header map is copied.
     *
     * @param payload the message payload (never {@code null})
     * @param headers message headers to use for initialization
     */
    public MQMessage(String messageKey, T payload, Map<String, Object> headers) {
        this(messageKey, payload, new MessageHeaders(headers));
    }

    /**
     * A constructor with the {@link MessageHeaders} instance to use.
     * <p><strong>Note:</strong> the given {@code MessageHeaders} instance is used
     * directly in the new message, i.e. it is not copied.
     *
     * @param payload the message payload (never {@code null})
     * @param headers message headers
     */
    public MQMessage(String messageKey, T payload, MessageHeaders headers) {
        Asserts.notNull(messageKey, "messageKey must not be null");
        Asserts.notNull(payload, "Payload must not be null");
        Asserts.notNull(headers, "MessageHeaders must not be null");
        this.messageKey = messageKey;
        this.payload = payload;
        this.headers = headers;
    }
}
