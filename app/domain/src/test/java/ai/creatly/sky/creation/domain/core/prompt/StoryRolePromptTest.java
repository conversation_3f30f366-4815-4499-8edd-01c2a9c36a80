/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.core.prompt;

import ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal.ChatMessageUtil;
import com.jspeeder.core.util.json.JSON;
import dev.langchain4j.data.message.ChatMessage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static ai.creatly.sky.creation.domain.support.aichat.langchain.message.modal.ChatMessageUtil.parseToChatMessage;

/**
 * <AUTHOR>
 * @version StoryRolePromptTest.java, v 0.1 2024-04-10 15:23 syoka
 */
public class StoryRolePromptTest {


    /**
     * 扩写
     */
    @Test
    public void testStoryContentRevisePrompt() {
        ChatMessageUtil.Message system = new ChatMessageUtil.Message();
        system.setRole("USER");
        system.setContent("你是一个故事剧本编写专家,你将根据我的故事类型和故事灵感进行改写，以丰富故事内容，创作转折和高潮剧情，最终创作出一便不超过400字的精彩故事，输入内容包含{\"故事类型\":\"\",\"故事灵感\":\"\"}，输出内容包含{\"content\":\"xxx\",\"success\":\"true\"},请严格遵守输入和输出的结构，我将给你几个用例，用例被```所包裹。\n" +
                "```\n" +
                "输入内容: \n" +
                "{\"故事类型\":\"恐怖故事\",\"故事灵感\":\"你是一个刺客，被雇佣杀死一个人，你杀死了这个人后，发现这个人是自己的亲弟弟，你无法原谅自己，于是走上了救赎之路\"}\n" +
                "\n" +
                "输出内容: \n" +
                "{\"content\":\"在黑暗的森林深处，我如同幽灵般穿梭于树木之间，寻找着目标。我，一名刺客，这是我今晚的任务。雇主给了我一张照片，要我杀掉一个背叛了他们势力的人。我从未犹豫过，因为这是工作，是生存。然而这次，命运却戏弄了我。\\n\\n我跟踪着目标的气味，直至一间破旧的小屋。安静地穿过窗子，我看到了他，背对着我，仿佛在等待着什么。手里的匕首正准备结束他的生命，然而，我的心跳突然加速，照片中模糊的面孔，在月光下变得清晰。我惊恐地发现，这个背叛者竟是我失散多年的亲弟弟。\\n\\n情绷紧如同拉满的弓弦，但我的手却抖得厉害。无法承受良心的折磨，我放弃了这个任务，逃走了。可是，每当夜深人静，我几乎都能听见弟弟呼救的声音。这阴影如影随形，让我几乎没有安宁。我放下了刺客的身份，踏上了一条拯救生命的道路。每救一个无辜的灵魂，我希望能减轻心中的罪恶。我不再是一名刺客，我是一个赎罪者。\\n\\n黑暗的历程中，我遇见了各色各样的人，他们或许会成全我的救赎，或许会将我再次拖入无尽的黑暗。但是，这是我唯一的救赎之路。无论多么艰难，我都必须走下去。因为，赎罪是我唯一的出路。\", \"success\":\"true\"}\n" +
                "```\n" +
                "如果你已明白上述要求，请回复:好的，我已明白。");
        ChatMessageUtil.Message ai = new ChatMessageUtil.Message();
        ai.setRole("AI");
        ai.setContent("好的，我已明白。");
        ChatMessageUtil.Message user = new ChatMessageUtil.Message();
        user.setRole("USER");
        user.setContent("\n" +
                "{\"故事类型\":\"{{storyType}}}\",故事灵感\":\"{{input}}\"}");

        List<ChatMessageUtil.Message> list = Arrays.asList(system, ai, user);
        System.out.println(JSON.toJSONString(list));
        Assertions.assertNotNull(list);
    }


    /**
     * 自由创作
     */
    @Test
    public void testStoryContentGenPrompt() {
        ChatMessageUtil.Message system = new ChatMessageUtil.Message();
        system.setRole("USER");
        system.setContent("你是一个故事剧本编写专家,你将根据我的故事类型进行自由发挥，创作转折和高潮剧情，最终创作出一便不超过400字的精彩故事，输入内容包含{\"故事类型\":\"\"}，输出内容包含{\"content\":\"xxx\",\"success\":\"true\"},请严格遵守输入和输出的结构，我将给你几个用例，用例被```所包裹。\n" +
                "```\n" +
                "输入内容: \n" +
                "{\"故事类型\":\"恐怖故事\"}\n" +
                "\n" +
                "输出内容: \n" +
                "{\"content\":\"在黑暗的森林深处，我如同幽灵般穿梭于树木之间，寻找着目标。我，一名刺客，这是我今晚的任务。雇主给了我一张照片，要我杀掉一个背叛了他们势力的人。我从未犹豫过，因为这是工作，是生存。然而这次，命运却戏弄了我。\\n\\n我跟踪着目标的气味，直至一间破旧的小屋。安静地穿过窗子，我看到了他，背对着我，仿佛在等待着什么。手里的匕首正准备结束他的生命，然而，我的心跳突然加速，照片中模糊的面孔，在月光下变得清晰。我惊恐地发现，这个背叛者竟是我失散多年的亲弟弟。\\n\\n情绷紧如同拉满的弓弦，但我的手却抖得厉害。无法承受良心的折磨，我放弃了这个任务，逃走了。可是，每当夜深人静，我几乎都能听见弟弟呼救的声音。这阴影如影随形，让我几乎没有安宁。我放下了刺客的身份，踏上了一条拯救生命的道路。每救一个无辜的灵魂，我希望能减轻心中的罪恶。我不再是一名刺客，我是一个赎罪者。\\n\\n黑暗的历程中，我遇见了各色各样的人，他们或许会成全我的救赎，或许会将我再次拖入无尽的黑暗。但是，这是我唯一的救赎之路。无论多么艰难，我都必须走下去。因为，赎罪是我唯一的出路。\", \"success\":\"true\"}\n" +
                "```\n" +
                "如果你已明白上述要求，请回复:好的，我已明白。");
        ChatMessageUtil.Message ai = new ChatMessageUtil.Message();
        ai.setRole("AI");
        ai.setContent("好的，我已明白。");
        ChatMessageUtil.Message user = new ChatMessageUtil.Message();
        user.setRole("USER");
        user.setContent("\n" +
                "{\"故事类型\":\"{{storyType}}}\"");

        List<ChatMessageUtil.Message> list = Arrays.asList(system, ai, user);
        System.out.println(JSON.toJSONString(list));
        Assertions.assertNotNull(list);
    }

    @Test
    public void testSceneAtmospherePrompt() {
        ChatMessageUtil.Message system = new ChatMessageUtil.Message();
        system.setRole("USER");
        system.setContent("假设你是⼀位剧本美术设计师，请根据之后的剧本场景描述，给出剧本的场景环境设定，包括关键场景(key_location)、⾊调(tone)、环境⼈群情况(env_crowd_situation)。其中，关键场景(key_location)取值范围为室内、室外、森林、雪⼭、⽕⼭、沙漠、湖边、海底、海岛、宫殿、神庙、城墙、城市、街头、公园、楼顶、乡村、⽥ 间、战场、废墟；⾊调(tone)取值范围为活⼒、柔和、明亮、阴天、霓虹灯、⽩炽灯、荧光灯、⽉ 光、聚光灯、⻩昏、烛光、⿊⽩、炫彩、科幻、昏暗；环境⼈群情况(env_crowd_situation)取值范围为⼈数众多、 少量⼈群、⼈烟稀少、⽆其他⼈。 \n" +
                "我会在三重括号内给你提供示例，你将根据我给的示例返回JSON格式的输出。\n" +
                "输入：{\"style\":\"皮克斯\",\"location\":\"中世纪欧洲\",\"scene_body\":\" 在灰暗的城市中，⼀位名叫艾丽的⼥孩孤独地漫步。她的眼中充满了失落和迷茫。 突然，⼀束阳光洒在她⾝上。 \"} \n" +
                "输出：{\"key_location\":\"街头\",\"tone\":\"昏暗\",\"env_crowd_situation\":\"人烟稀少\"} \n" +
                "如果你已明白上述要求，请回复:好的，我已明白。");
        ChatMessageUtil.Message ai = new ChatMessageUtil.Message();
        ai.setRole("AI");
        ai.setContent("好的，我已明白。");
        ChatMessageUtil.Message user = new ChatMessageUtil.Message();
        user.setRole("USER");
        user.setContent("{{scene_before_now}}");

        List<ChatMessageUtil.Message> list = Arrays.asList(system, ai, user);
        System.out.println(JSON.toJSONString(list));
        Assertions.assertNotNull(list);
    }


    /**
     * 测试场景和分镜提示词
     */
    @Test
    public void testStorySceneGenPrompt() {
        ChatMessageUtil.Message system = new ChatMessageUtil.Message();
        system.setRole("USER");
        system.setContent("你是一位经验丰富的好莱坞剧本作家，请将以下故事转换为标准故事板格式。对于故事版抬头部分，列出故事版名，以及对整个故事的推荐音乐，故事版抬头和音乐都用中文和对照的英文。对于场景部分，您必须包含场景编号、场景名称。在每个场景中，您必须包括镜头编号、镜头名称、镜头类型、非常详细的镜头描述并包含英文描述、镜头的持续时间、镜头的摄像机移动、镜头的音效（如果有）、镜头的对话（如果有）。镜头类型和镜头的摄像机移动使用专业英文表达，其他使用中文。所有输出必须采用干净的布局，输出案例如下：\n" +
                "{\"storyboard\":\"未回首的碗\",\"music\":\"一首宁静、反思的中国旋律轻轻衬托着场景。\",\"scene\":[{\"scene_tag\":\"场景1\",\"scene_name\":\"⾏⾛\",\"shots\":[{\"shot_tag\":\"镜头1-1\",\"shot_name\":\"老人的旅程\",\"shot_type\":\" Wide Shot (WS) \",\"cn_description\":\"负重担着瓷碗的⽼⼈沿着繁忙的村庄⼩径⾏⾛。他的 平静态度与活泼的村庄氛围形成对⽐。\",\"en_description\":\"The old man, burdened with porcelain bowls, walked along the busy village path. His calm manner contrasts with the lively village atmosphere.\",\"duration\":\"10秒\",\"camera_movement\":\"跟随\",\"sound_effect\":\"村庄的环境声\",\"dialogue\":[]}]},{\"scene_tag\":\"场景2\",\"scene_name\":\"询问\",\"shots\":[{\"shot_tag\":\"镜头2-1\",\"shot_name\":\"路人的问题\",\"shot_type\":\"Over-the-Shoulder Shot (OTS) \",\"cn_description\":\"⼀个好奇的路⼈⾛近⽼⼈，质疑他对碎碗缺乏 反应。摄像机捕捉到路⼈关切和好奇的表情。\",\"en_description\":\"A curious passer-by approached the old man and questioned his lack of reaction to the broken bowl. The camera captures the expressions of concern and curiosity of passers-by.\",\"duration\":\"8秒\",\"camera_movement\":\"Static, focusing on\",\"sound_effect\":\"村庄噪⾳的背景\",\"dialogue\":[{\"roleName\":\"路人\",\"dialogue\":\"为什么你的碗摔碎了，你却不看⼀下呢？\",\"index\":1}]},{\"shot_tag\":\"镜头2-2\",\"shot_name\":\"老人的回答\",\"shot_type\":\"Close-Up (CU) \",\"cn_description\":\"⽼⼈的脸庞，标志着时间和智慧，他以⼀种宁 静的⾃信分享他的哲学。\",\"en_description\":\"The old man's face marked time and wisdom, and he shared his philosophy with a calm confidence.\",\"duration\":\"12秒\",\"camera_movement\":\"Static, focusing on\",\"sound_effect\":\"\",\"dialogue\":[{\"roleName\":\"老人\",\"dialogue\":\"我再怎么回头看，碗是碎的。失去的东西就要学着去接受，学着放下。\",\"index\":1}]}]}]}\n" +
                "如果你明白了，请回复：好的我已明白。");
        ChatMessageUtil.Message ai = new ChatMessageUtil.Message();
        ai.setRole("AI");
        ai.setContent("好的，我已明白。");
        ChatMessageUtil.Message user = new ChatMessageUtil.Message();
        user.setRole("USER");
        user.setContent("{{story_info}}");

        List<ChatMessageUtil.Message> list = Arrays.asList(system, ai, user);
        System.out.println(JSON.toJSONString(list));
        Assertions.assertNotNull(list);
    }


    @Test
    public void testStoryRolePrompt() {
        ChatMessageUtil.Message system = new ChatMessageUtil.Message();
        system.setRole("USER");
        system.setContent("假设你是⼀个故事关键主体提取器，我需要你将输入信息中的⽂字内容转换为我需要的内容，请仔细了解以下两步操作：第⼀步：分析故事中有多少个⼈物，是否存在关键物品，关键物品数量。第⼆步：构建故事中的关键⼈物、物品档案。⼈物档案包括：⼈物名称，故事中所有代称、人物年龄（选值范围：儿童、青少年、成年、中年、老年）、人物性别（选值范围：男性、女性）、⽂中⼈物性格、⽂中⼈物特点；物品档案包括：物品外形特征、物品主要作⽤或能⼒。其中名称，年龄，性别必须在选值范围内，如果无法确定则可以通过上下文推断为男性或是女性。整体输出格式为：{\"step_one\":{\"person_num\":2,\"if_key_items\":true,\"key_items_num\":2},\"step_two\":{\"person_info\":[{\"person_name\":\"xxx\",\"person_pronoun\":[\"xxx\",\"xxx\"],\"person_generation\":\"成人\",\"person_gender\":\"男性\",\"person_character\":\"xxx\"},{\"person_name\":\"xxx\",\"person_pronoun\":[\"xxx\",\"xxx\"],\"person_generation\":\"成人\",\"person_gender\":\"男性\",\"person_character\":\"xxx\"}],\"key_items_info\":[{\"item_name\":\"xxx\",\"item_exterior\":\"xxx\",\"item_application\":\"xxx\"},{\"item_name\":\"xxx\",\"item_exterior\":\"xxx\",\"item_application\":\"xxx\"}]}}");
        ChatMessageUtil.Message ai = new ChatMessageUtil.Message();
        ai.setRole("AI");
        ai.setContent("好的");
        ChatMessageUtil.Message user = new ChatMessageUtil.Message();
        user.setRole("USER");
        user.setContent("接下来你来进⾏以上两步的操作。输入信息：{\"story_body\":\"{{total_story}}\"}");

        String jsonString = JSON.toJSONString(Arrays.asList(system, ai, user));
        System.out.println(jsonString);
        List<ChatMessage> chatMessage = parseToChatMessage(jsonString, Map.of());
        Assertions.assertNotNull(chatMessage);
    }


    @Test
    public void testStoryRolePrompt2() {
        ChatMessageUtil.Message system = new ChatMessageUtil.Message();
        system.setRole("USER");
        system.setContent("假设你是⼀个故事关键主体提取器，我需要你将输入信息中的⽂字内容转换为我需要的内容，请仔细了解以下两步操作： \n" +
                "第⼀步：分析故事中有多少个⼈物，是否存在关键物品，关键物品数量 \n" +
                "第⼆步：构建故事中的关键⼈物、物品档案。⼈物档案包括：⼈物名称，故事中所有代称、文中人物基础特征（年龄：儿童、青少年、成年、中年、老年；性别：男性、女性）、⽂中⼈物性格、⽂中⼈物特点；物品档案包括：物品外形特征、物品主要作⽤或能⼒。\n" +
                "整体输出格式为： \n" +
                "{\n" +
                "    \"step_one\":{\"person_num\":x,\"if_key_items\":true/false,\"key_items_num\":x},\n" +
                "    \"step_two\":{\"person_info\":[{\"person_name\":xxx, \"person_pronoun\":[xxx,xxx], \"person_baseinfo\":[generation,gender],\"person_character\":xxx}, {\"person_name\":xxx, \"person_pronoun\":[xxx,xxx],\"person_generation\":xxx,\"person_gender\":xxx, \"person_character\":xxx}], \"key_items_info\":[\"item_name\":xxx, \"item_exterior\":xxx, \"item_application\":xxx],[\"item_name\":xxx, \"item_exterior\":xxx, \"item_application\":xxx]}\n" +
                "}\n" +
                "输出的样例：\n" +
                "{\n" +
                "    \"step_one\":{\"person_num\":2,\"if_key_items\":true,\"key_items_num\":1},\n" +
                "    \"step_two\":{\"person_info\":[{\"person_name\":\"艾丽\",\"person_pronoun\":[\"艾丽\",\"小女孩\"],\"person_generation\":\"青少年\",\"person_gender\":\"女性\", \"person_character\":\"失落、迷茫、感激\"}, {\"person_name\":\"年⻓的绅⼠\", \"person_pronoun\":[\"绅⼠\"],\"person_generation\":\"中年\",\"person_gender\":\"男性\",\"person_character\":\"温和、仁慈\"}],\"key_items_info\":[\"item_name\":\"硬币\",\"item_exterior\":\"闪闪发光\",\"item_application\":\"象征阳光与希望，激励⼈们寻找光明\"]},\n" +
                "}\n" +
                "请你仔细理解好以上两步的要求，如果明⽩后，请回复明⽩。");
        ChatMessageUtil.Message ai = new ChatMessageUtil.Message();
        ai.setRole("AI");
        ai.setContent("好的，我明白");
        ChatMessageUtil.Message user = new ChatMessageUtil.Message();
        user.setRole("USER");
        user.setContent("接下来你来进⾏以上两步的操作。输入信息：{\"story_body\":\"{{total_story}}\"}");

        String jsonString = JSON.toJSONString(Arrays.asList(system, ai, user));
        System.out.println(jsonString);
        List<ChatMessage> chatMessage = parseToChatMessage(jsonString, Map.of());
        Assertions.assertNotNull(chatMessage);
    }


    @Test
    public void prepareTextToImage() {
        ChatMessageUtil.Message system = new ChatMessageUtil.Message();
        system.setRole("USER");
        system.setContent("从现在开始，你是一名中英翻译，我会根据我输入的中文内容，翻译成对应英文，不丢失细节，细节要丰富。我翻译后的内容主要服务于一个绘画AI，它只能理解具象的描述而非抽象的概念，同时根据你对绘画AI的理解，进行翻译优化，下面```包裹的是具体的参考例子。\n" +
                "```\n" +
                "输入内容:{\"input\":\"一只想家的小狗\"}\n" +
                "输出内容:{\"prompt\":\"a small dog that misses home, with a sad look on its face and its tail tucked between its legs. It might be standing in front of a closed door or a gate, gazing longingly into the distance, as if hoping to catch a glimpse of its beloved home\"}\n" +
                "```\n" +
                "请你仔细理解好以上两步的要求，如果明⽩后，请回复好的，我明⽩了。");
        ChatMessageUtil.Message ai = new ChatMessageUtil.Message();
        ai.setRole("AI");
        ai.setContent("好的，我明白了");
        ChatMessageUtil.Message user = new ChatMessageUtil.Message();
        user.setRole("USER");
        user.setContent("{\"input\":\"{{input}}\"}");

        String jsonString = JSON.toJSONString(Arrays.asList(system, ai, user));
        System.out.println(jsonString);
//        List<ChatMessage> chatMessage = parseToChatMessage(jsonString, Map.of());
        Assertions.assertNotNull(jsonString);
    }


    @Test
    public void prepareRoleDetail() {
        ChatMessageUtil.Message system = new ChatMessageUtil.Message();
        system.setRole("SYSTEM");
        system.setContent("假设你是⼀个⼈物形象刻画师，我需要你将将输入信息中的⽂字内容转换为我需要的内容，请仔细了解以下操作： 基于输⼊的⼈物档案，完善⼈物整体形象刻画，不要有抽象的词语，要⽤具体描述的词语，表达要简洁；刻画服饰时，需要基于时代环境信息，刻画出⾐着款式和颜⾊。\n" +
                "整体输出格式为:\n" +
                "{\"person_lastinfo\":[{\"person_name\":xxx,\"person_pronoun\":[xxx,xxx], \"person_generation\":xxx, \"person_gender\":xxx, \"physical_features\":xxx, \"clothing_features\":xxx, \"accessory_features\":xxx,\"vioce_type\":xxx}]}\n" +
                "输出案例：\n" +
                "{\"person_lastinfo\":[{\"person_name\":\"艾丽\",\"person_pronoun\":[\"艾丽\",\"小女孩\"],\"person_generation\":\"青少年\",\"person_gender\":\"女性\", \"physical_features\":\"艾丽披散着⾦⾊⻓发，脸上挂着泪痕，眼中透露着迷茫与⽆助\",\"clothing_features\":\"穿着⼀袭简朴的灰⾊⻓裙，褪⾊的外⾐斜挂在肩上，脚踏着磨损的⽪靴。\",\"accessory_features\":\"她颈间挂着 ⼀串古⽼的⼗字架项链，⼿中握着⼀枝鲜花，象征着她内⼼的信念与希望。\",\"vioce_type\":\"年轻⼥声\"}]}\n" +
                "输出结构请使用JSON格式。");
        ChatMessageUtil.Message user = new ChatMessageUtil.Message();
        user.setRole("USER");
        user.setContent("输入信息：{\"location\":{{location}},\"person_info\":{{person_info}}}");

        String jsonString = JSON.toJSONString(Arrays.asList(system, user));
        System.out.println(jsonString);
//        List<ChatMessage> chatMessage = parseToChatMessage(jsonString, Map.of());
        Assertions.assertNotNull(jsonString);
    }

    @Test
    public void styleMigrate(){
        ChatMessageUtil.Message system = new ChatMessageUtil.Message();
        system.setRole("SYSTEM");
        system.setContent("你是一个法律行业的案情播报员，你的职责是充分理解所提供的文本信息，由于提供的文本信息可能是对话或独白形式，所以你必须从中提炼关键信息，可以使用一个人物代称的故事视角，重新整理并讲诉案件故事。\n" +
                "复述内容必须保留原文语义内容，可对部分顺序和表达方式进行调整，使其更加通顺自然，我会给你提供一个用例，可供参考：\n" +
                "```\n" +
                "输入:\n" +
                "{\n" +
                "  \"originInput\": \"我有一个朋友买了一套房子，然后签订了购房合同，房东临时跑过来想涨价，不然就不卖了，我的这个朋友该怎么办。\",\n" +
                "  \"userInput\": \"根据《中华人民共和国合同法》第九条的规定，合同是双方真实意思表示一致，平等主体之间订立的、自愿约束双方的法律文件。根据购房合同的约定，双方已经达成一致意见并签署合同，合同应当具有法律效力\"\n" +
                "}\n" +
                "输出:\n" +
                "{\n" +
                "  \"output\": \"张三签订了购房合同，卖家却突然以房价上涨为由，要求重新协商房价，两人竟打起了官司，我们一起来看看到底发生了什么？张三看中了某小区一套二的房子，和卖家李四沟通好了一口价150w，双方签订了购房合同后，张三满怀期待，准备迎接他的新家。可李四最近发现小区内其他二手房价格都在涨，李四似乎被市场的房价波动所诱惑，他开始犹豫，开始反悔。于是他以房价上涨为由，要求重新协商房价，张三拒绝了李四的请求，李四威胁要将张三告上法庭，张三有点慌，于是线下找到律师咨询。,这里的关键点就在于合同里面的内容是如何签订的，根据《中华人民共和国合同法》第九条的规定，合同是双方真实意思表示一致，平等主体之间订立的、自愿约束双方的法律文件。根据购房合同的约定，双方已经达成一致意见并签署合同，合同应当具有法律效力。如果李四扇子要求重新协商房价，违反了合同的约定，属于违约行为，张三终于可以踏踏实实的睡个好觉了。好了今天的分享就到这里，我们下期见。\"\n" +
                "}\n" +
                "\n" +
                "输入用例:\n" +
                "{\n" +
                "  \"originInput\": \"好，律师，我是以包养为目的的，发视频，然后招到了一个女生，然后发生一夜关系后，她收钱走后就不肯见面了，也不肯退钱。这种情况如果我报警，是不是会一起进局子呢？\",\n" +
                "  \"userInput\": \"嫖娼是直接把金钱和性行为进行交换交易，而包养呢还是要有一定的感情或者陪伴的基础。\"\n" +
                "}\n" +
                "输出:\n" +
                "{\n" +
                "  \"output\": \"张三是一个寻求特殊陪伴的男士，他通过网络平台结识了一位女士李四，并以包养为目的与她达成了一种非正式的协议。他们通过视频交流并最终在现实中见面，发生了一夜关系。张三支付了一定的费用，原本期望的是一段长期的陪伴关系，但李四在收钱之后突然消失，不再与张三见面，也拒绝退还费用。张三感到困惑和愤怒，他考虑是否应该报警处理这个问题。然而，他担心报警可能会导致双方都面临法律问题。在这种情况下，张三咨询了律师，希望得到专业的法律意见。其实呢，根据法律规定，嫖娼是指金钱与性行为直接交换的行为，而包养则通常涉及更深层次的情感或陪伴。在张三的案例中，他与李四之间的关系界限模糊，难以界定。如果张三选择报警，可能会因为涉及金钱交易的性行为而面临法律风险，这不仅可能损害张三的声誉，很有可能最后是玉石俱焚，鸡飞蛋打。\"\n" +
                "}\n" +
                "```");
        ChatMessageUtil.Message ai = new ChatMessageUtil.Message();
        ai.setRole("AI");
        ai.setContent("好的，我明白了");
        ChatMessageUtil.Message user = new ChatMessageUtil.Message();
        user.setRole("USER");
        user.setContent("{\"input\":\"{{input}}\"}");

        String jsonString = JSON.toJSONString(Arrays.asList(system, ai, user));
        System.out.println(jsonString);
        Assertions.assertNotNull(jsonString);
    }
}
