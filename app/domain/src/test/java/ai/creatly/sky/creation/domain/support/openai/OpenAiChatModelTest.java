/*
 * Shanghai Zhixingyuan Network & Technology Co, Ltd.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */

package ai.creatly.sky.creation.domain.support.openai;

import dev.langchain4j.model.openai.OpenAiChatModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 *
 * <AUTHOR>
 * @version OpenAiChatModelTest.java, v 0.1 2024-04-22 下午3:02 zhoudong
 */
public class OpenAiChatModelTest {

    private final static String OPEN_AI_API_KEY = "***************************************************";

    @Test
    public void testHelloWorld() {
        OpenAiChatModel chatModel = OpenAiChatModel.withApiKey(OPEN_AI_API_KEY);
        String answer = chatModel.generate("Hello world!");
        Assertions.assertNotNull(answer);
    }

    @Test
    public void testJsonFormat() {
    }
}
